package cec.jiutian.fc.log.utils;

import cec.jiutian.fc.log.enums.ManagerTypeEnum;
import org.springframework.util.CollectionUtils;

import java.util.HashSet;
import java.util.List;

public class UserTypeUtil {


    public static boolean isThreeManager(List<String> roles) {
        if (CollectionUtils.isEmpty(roles)) {
            return false;
        }
        for (String role : roles) {
            if (ManagerTypeEnum.accountContains(role)) {
                return true;
            }
        }
        return false;
    }

    //系统管理员可查看日志
    public static final String NORMAL_USER = "1";
    //审计管理员可查看日志
    public static final String NON_AUDIT = "2";
    //安全管理员可查看日志
    public static final String AUDIT = "3";
    public static final String SUPER_USER = "superManager";
    public static String userTypeAcquire(List<String> roles) {
        if (CollectionUtils.isEmpty(roles)) {
            return NORMAL_USER;
        }
        HashSet<String> roleSet = new HashSet<>(roles);
        if (roleSet.contains(ManagerTypeEnum.auditManager.getCode())) {
            return AUDIT;
        } else if (roleSet.contains(ManagerTypeEnum.securityManager.getCode())
                 || roleSet.contains(ManagerTypeEnum.systemManager.getCode())) {
            return NON_AUDIT;
        }
        return NORMAL_USER;
    }

    public static String loginUserTypeAcquire(List<String> roles) {
        if (CollectionUtils.isEmpty(roles)) {
            return NORMAL_USER;
        }
        HashSet<String> roleSet = new HashSet<>(roles);
        if (roleSet.contains(ManagerTypeEnum.auditManager.getRoleName())) {
            return AUDIT;
        } else if (roleSet.contains(ManagerTypeEnum.securityManager.getRoleName())
                || roleSet.contains(ManagerTypeEnum.systemManager.getRoleName())) {
            return NON_AUDIT;
        }
        return NORMAL_USER;
    }

    public static boolean isSuperUser(List<String> roles) {
        if (CollectionUtils.isEmpty(roles)) {
            return false;
        }
        for (String role : roles) {
            if (SUPER_USER.equals(role)) {
                return true;
            }
        }
        return false;
    }


    public static boolean isSystemUser(List<String> roles) {
        if (CollectionUtils.isEmpty(roles)) {
            return false;
        }
        for (String role : roles) {
            if (ManagerTypeEnum.systemManager.getCode().equals(role)) {
                return true;
            }
        }
        return false;
    }
}
