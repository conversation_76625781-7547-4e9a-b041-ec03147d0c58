package cec.jiutian.fc.log.domain.loginLog.threeManager.event;


import cec.jiutian.common.context.UserContext;
import cec.jiutian.fc.log.domain.loginLog.threeManager.entity.ThreeManagerLoginLog;
import cec.jiutian.fc.log.enums.ManagerTypeEnum;
import cec.jiutian.core.frame.exception.FabosJsonWebApiRuntimeException;
import cec.jiutian.view.fun.DataProxy;
import cec.jiutian.view.query.Condition;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

public class ThreeManagerLoginLogDataProxy implements DataProxy<ThreeManagerLoginLog> {

    @Override
    public void beforeAdd(ThreeManagerLoginLog threeManagerLog) {
        throw new FabosJsonWebApiRuntimeException("登录日志不允许人为新增");
    }

    @Override
    public void beforeUpdate(ThreeManagerLoginLog threeManagerLog) {
        throw new FabosJsonWebApiRuntimeException("登录日志不允许变更");
    }

    @Override
    public void beforeDelete(ThreeManagerLoginLog threeManagerLog) {
        throw new FabosJsonWebApiRuntimeException("登录日志不允许删除");
    }

    @Override
    public String beforeFetch(List<Condition> conditions) {
        UserContext.CurrentUser currentUser = UserContext.get();
        List<String> roles = currentUser.getRoles();
        if (CollectionUtils.isEmpty(roles)) {
            throw new FabosJsonWebApiRuntimeException("权限不足！");
        }
        HashSet<String> roleSet = new HashSet<>(roles);
        if (roleSet.contains(ManagerTypeEnum.auditManager.getRoleName())) {
            //安全审计员 查看系统管理员、安全管理员日志
            return "ThreeManagerLoginLog.username in ('" + ManagerTypeEnum.systemManager.getRoleName()
                    + "', '" + ManagerTypeEnum.securityManager.getRoleName() + "')";
        }
        if (roleSet.contains(ManagerTypeEnum.securityManager.getRoleName())) {
            return "ThreeManagerLoginLog.username = '" + ManagerTypeEnum.auditManager.getRoleName() + "'";
        }
//        return  "ThreeManagerLog.userId = '" + currentUser.getUserId() + "'";
        throw new FabosJsonWebApiRuntimeException("权限不足！");
    }

    @Override
    public void afterFetch(Collection<Map<String, Object>> list) {
        list.forEach(map -> {
            Object opTime = map.get("opTime");
            if (opTime != null && opTime instanceof LocalDateTime) {
                LocalDateTime localDateTime = (LocalDateTime) opTime;
                String formattedTime = localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                map.put("opTime", formattedTime);
            }
        });
    }
}
