package cec.jiutian.fc.log.domain.businessLog.entity;

import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.FabosJsonI18n;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ReferenceTableType;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @description:
 */
@Entity
@Table(name = "fd_business_log_model_field")
@Getter
@Setter
@FabosJson(name = "业务日志模型",
        orderBy = "BusinessLogModelField.createTime desc"
        )
@FabosJsonI18n
public class BusinessLogModelField extends MetaModel {
    @FabosJsonField(
            views = @View(title = "字段"),
            edit = @Edit(title = "字段")
    )
    private String columnName;
    @FabosJsonField(
            views = @View(title = "字段名称"),
            edit = @Edit(title = "字段名称")
    )
    private String displayName;
    @FabosJsonField(
            views = @View(title = "字段id",show = false),
            edit = @Edit(title = "字段id",show = false)
    )
    private String fieldId;
    @FabosJsonField(
            views = {
                    @View(title = "模型", column = "modelName",show = false)
            },
            edit = @Edit(title = "模型", type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "modelName")
            )
    )
    @ManyToOne
    @JsonIgnoreProperties("businessLogModelFields")
    private BusinessLogModel businessLogModel;
}
