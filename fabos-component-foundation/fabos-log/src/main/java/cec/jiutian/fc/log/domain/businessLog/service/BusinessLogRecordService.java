package cec.jiutian.fc.log.domain.businessLog.service;

import cec.jiutian.common.util.StringUtils;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.core.frame.module.Page;
import cec.jiutian.fc.log.domain.businessLog.dto.BusinessLogRecordDataDTO;
import cec.jiutian.fc.log.domain.businessLog.dto.BusinessLogRecordQuery;
import cec.jiutian.fc.log.domain.businessLog.entity.BusinessLogBuild;
import cec.jiutian.log.businessLog.entity.BusinessLogRecord;
import cec.jiutian.log.businessLog.enums.ActionTypeEnum;
import jakarta.annotation.Resource;
import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description:
 */
@Service
@Slf4j
public class BusinessLogRecordService {
    @Resource
    private FabosJsonDao fabosJsonDao;
    @Resource
    private BusinessLogBuildService businessLogBuildService;

    /**
     * 查询记录日志
     * @param recordQuery
     * @return
     */
    public String buildJson(BusinessLogRecordQuery recordQuery){
        checkParams(recordQuery);
        BusinessLogBuild byModelNameAndVersion = businessLogBuildService.getByModelNameAndVersion(recordQuery.getModelName(), parseVersion(recordQuery.getVersion()));
        return byModelNameAndVersion==null?null:byModelNameAndVersion.getBuildJson();
    }

    private void checkParams(BusinessLogRecordQuery recordQuery){
        if (recordQuery == null) {
            throw new FabosJsonApiErrorTip("请求参数异常");
        }
        if (StringUtils.isBlank(recordQuery.getModelName())) {
            throw new FabosJsonApiErrorTip("modelName必填");
        }
        if (StringUtils.isBlank(recordQuery.getVersion())) {
            throw new FabosJsonApiErrorTip("version必填");
        }
    }
    /**
     * 分页查询数据
     * @param recordQuery
     * @return
     */
    public Page<BusinessLogRecordDataDTO> queryPage(BusinessLogRecordQuery recordQuery){
        checkParams(recordQuery);
        Page<BusinessLogRecordDataDTO> page = new Page<>();
        page.setPage(recordQuery.getPage());
        page.setPerPage(recordQuery.getPerPage());

        // 预处理日期
        String startDataSql = getDateSql(recordQuery.getStartDate(), " and r.recordTime >= :startDate");
        String endDataSql = getDateSql(recordQuery.getEndDate(), " and r.recordTime <= :endDate");

        if (recordQuery.getCondition() == null || recordQuery.getCondition().isEmpty()) {
            return getRecord(recordQuery, page, startDataSql, endDataSql);
        } else {
            return getRecordByField(recordQuery, page, startDataSql, endDataSql);
        }
    }

    /**
     * 获取日期条件的SQL部分
     * @param date 日期字符串
     * @param sqlPart SQL部分
     * @return
     */
    private String getDateSql(String date, String sqlPart) {
        return StringUtils.isNotBlank(date) ? sqlPart : "";
    }

    /**
     * 查询BusinessLogRecord 使用field 需要联表查询
     * @param recordQuery
     * @param page
     * @param startDataSql
     * @param endDataSql
     * @return
     */
    private Page<BusinessLogRecordDataDTO> getRecordByField(BusinessLogRecordQuery recordQuery, Page<BusinessLogRecordDataDTO> page, String startDataSql, String endDataSql) {
        Map<String, String> conditionMap = recordQuery.getCondition();
        List<String> conditions = new ArrayList<>();
        int index = 0;

        for (String key : conditionMap.keySet()) {
            conditions.add("(f.columnName = :columnName" + index + " and f.columnValue like :columnValue" + index + ")");
            index++;
        }

        String condition = String.join(" or ", conditions);
        EntityManager entityManager = fabosJsonDao.getEntityManager();

        String hql = "select r from BusinessLogRecord r where r.modelName = :modelName and r.buildVersion = :version " + endDataSql + startDataSql +
                " and r.id in (select f.businessLogRecord.id from BusinessLogRecordField as f where " + condition + ")";
        String countHql = "select count(*) from BusinessLogRecord r where r.modelName = :modelName and r.buildVersion = :version " + endDataSql + startDataSql +
                " and r.id in (select f.businessLogRecord.id from BusinessLogRecordField as f where " + condition + ")";

        Query query = entityManager.createQuery(hql);
        Query countQuery = entityManager.createQuery(countHql);

        setQueryParameters(query, countQuery, recordQuery,conditionMap);

        page.setTotal(Long.parseLong(countQuery.getSingleResult().toString()));

        if (page.getTotal() > 0) {
            List<BusinessLogRecord> list = query.setMaxResults(page.getPerPage()).setFirstResult((page.getPage() - 1) * page.getPerPage()).getResultList();
            page.setList(constructDTO(list));
        } else {
            page.setList(new ArrayList<>(0));
        }
        return page;
    }

    /**
     * 查询BusinessLogRecord 不使用field
     * @param recordQuery
     * @param page
     * @param startDataSql
     * @param endDataSql
     * @return
     */
    private Page<BusinessLogRecordDataDTO> getRecord(BusinessLogRecordQuery recordQuery, Page<BusinessLogRecordDataDTO> page, String startDataSql, String endDataSql) {
        EntityManager entityManager = fabosJsonDao.getEntityManager();
        String hql = "select r from BusinessLogRecord r where r.modelName = :modelName and r.buildVersion = :version " + endDataSql + startDataSql;
        String countHql = "select count(*) from BusinessLogRecord r where r.modelName = :modelName and r.buildVersion = :version" + endDataSql + startDataSql;

        Query query = entityManager.createQuery(hql);
        Query countQuery = entityManager.createQuery(countHql);

        setQueryParameters(query, countQuery, recordQuery,null);

        page.setTotal(Long.parseLong(countQuery.getSingleResult().toString()));

        if (page.getTotal() > 0) {
            List<BusinessLogRecord> list = query.setMaxResults(page.getPerPage()).setFirstResult((page.getPage() - 1) * page.getPerPage()).getResultList();
            page.setList(constructDTO(list));
        } else {
            page.setList(new ArrayList<>(0));
        }
        return page;
    }

    /**
     * 设置查询参数
     * @param query
     * @param countQuery
     * @param recordQuery
     * @param conditionMap
     */
    private void setQueryParameters(Query query, Query countQuery, BusinessLogRecordQuery recordQuery, Map<String, String> conditionMap) {
        query.setParameter("modelName", recordQuery.getModelName());
        query.setParameter("version", parseVersion(recordQuery.getVersion()));
        countQuery.setParameter("modelName", recordQuery.getModelName());
        countQuery.setParameter("version", parseVersion(recordQuery.getVersion()));

        if (StringUtils.isNotBlank(recordQuery.getStartDate())) {
            LocalDateTime startDate = LocalDateTime.parse(recordQuery.getStartDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            countQuery.setParameter("startDate", startDate);
            query.setParameter("startDate", startDate);
        }

        if (StringUtils.isNotBlank(recordQuery.getEndDate())) {
            LocalDateTime endDate = LocalDateTime.parse(recordQuery.getEndDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            countQuery.setParameter("endDate", endDate);
            query.setParameter("endDate", endDate);
        }

        if (conditionMap != null) {
            int index = 0;
            for (String key : conditionMap.keySet()) {
                query.setParameter("columnName" + index, key);
                query.setParameter("columnValue" + index, "%"+conditionMap.get(key)+"%");
                countQuery.setParameter("columnName" + index, key);
                countQuery.setParameter("columnValue" + index, "%"+conditionMap.get(key)+"%");
                index++;
            }
        }
    }

    /**
     * 构造DTO对象列表
     * @param list
     * @return
     */
    private List<BusinessLogRecordDataDTO> constructDTO(List<BusinessLogRecord> list) {
        return list.stream().map(o -> {
            BusinessLogRecordDataDTO recordDTO = new BusinessLogRecordDataDTO();
            recordDTO.setModelName(o.getModelName());
            recordDTO.setRecordTime(o.getRecordTime());
            recordDTO.setData(o.getData());
            recordDTO.setActionType(o.getActionType());
            recordDTO.setDataId(o.getDataId());
            recordDTO.setActionTypeStr(ActionTypeEnum.getValue(o.getActionType()));
            return recordDTO;
        }).toList();
    }

    /**
     * 解析版本号
     * @param version
     * @return
     */
    private int parseVersion(String version) {
        if (version == null || !version.startsWith("V")) {
            throw new FabosJsonApiErrorTip("version异常");
        }
        try {
            return Integer.parseInt(version.substring(1));
        } catch (NumberFormatException e) {
            throw new FabosJsonApiErrorTip("version异常");
        }
    }

}
