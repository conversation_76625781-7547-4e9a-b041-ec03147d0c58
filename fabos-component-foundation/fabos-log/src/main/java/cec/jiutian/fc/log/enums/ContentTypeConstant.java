package cec.jiutian.fc.log.enums;

public interface ContentTypeConstant {

    String CONTENT_TYPE_JSON = "application/json";

    // XML 格式的数据
    String CONTENT_TYPE_XML = "application/xml";

    // URL 编码的数据，通常用于表单提交
    String CONTENT_TYPE_URLENCODED = "application/x-www-form-urlencoded";

    // 二进制数据
    String CONTENT_TYPE_OCTET_STREAM = "application/octet-stream";

    // HTML 文档
    String CONTENT_TYPE_HTML = "text/html";

    // CSS 样式表
    String CONTENT_TYPE_CSS = "text/css";

    // JavaScript 文件
    String CONTENT_TYPE_JAVASCRIPT = "application/javascript";

    // CSV 文件
    String CONTENT_TYPE_CSV = "text/csv";

    // PDF 文件
    String CONTENT_TYPE_PDF = "application/pdf";

    // 图片文件
    String CONTENT_TYPE_JPEG = "image/jpeg";
    String CONTENT_TYPE_PNG = "image/png";
    String CONTENT_TYPE_GIF = "image/gif";

    // 文本文件
    String CONTENT_TYPE_TEXT_PLAIN = "text/plain";

    // RDF 数据
    String CONTENT_TYPE_RDF = "application/rdf+xml";

    // SOAP 消息
    String CONTENT_TYPE_SOAP = "application/soap+xml";

    // Atom feed
    String CONTENT_TYPE_ATOM = "application/atom+xml";

    // RSS feed
    String CONTENT_TYPE_RSS = "application/rss+xml";

    // GraphQL 请求
    String CONTENT_TYPE_GRAPHQL = "application/graphql";

    // multipart/form-data 通常用于文件上传
    String CONTENT_TYPE_MULTIPART_FORM_DATA = "multipart/form-data";

    // multipart/mixed 通常用于混合媒体类型的邮件
    String CONTENT_TYPE_MULTIPART_MIXED = "multipart/mixed";

    // multipart/related 通常用于关联的文档，如 HTML 和内嵌图像
    String CONTENT_TYPE_MULTIPART_RELATED = "multipart/related";

    // multipart/alternative 通常用于替代版本的消息体
    String CONTENT_TYPE_MULTIPART_ALTERNATIVE = "multipart/alternative";

    // multipart/report 通常用于 MIME 报告
    String CONTENT_TYPE_MULTIPART_REPORT = "multipart/report";

    // multipart/signed 通常用于数字签名
    String CONTENT_TYPE_MULTIPART_SIGNED = "multipart/signed";

    // multipart/encrypted 通常用于加密
    String CONTENT_TYPE_MULTIPART_ENCRYPTED = "multipart/encrypted";
}
