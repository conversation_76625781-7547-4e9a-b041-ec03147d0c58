package cec.jiutian.fc.excel.controller;

import cec.jiutian.core.frame.constant.FabosJsonRestPath;
import cec.jiutian.core.frame.controller.FabosModifyController;
import cec.jiutian.core.frame.invoke.DataProxyInvoke;
import cec.jiutian.core.frame.module.Page;
import cec.jiutian.core.frame.module.QueryCondition;
import cec.jiutian.core.frame.module.R;
import cec.jiutian.core.frame.module.TableQuery;
import cec.jiutian.core.frame.service.FabosJsonCoreService;
import cec.jiutian.core.frame.service.FabosJsonService;
import cec.jiutian.core.frame.util.DataHandlerUtil;
import cec.jiutian.core.frame.util.FabosJsonPowerUtil;
import cec.jiutian.core.view.fabosJson.view.FabosJsonModel;
import cec.jiutian.fc.excel.service.FabosExcelService;
import cec.jiutian.fc.excel.util.ExcelUtil;
import cec.jiutian.view.fun.PowerObject;
import cec.jiutian.view.query.Condition;
import com.google.gson.JsonObject;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Optional;

/**
 * 对Excel数据的处理
 *
 */
@RestController
@RequestMapping(FabosJsonRestPath.FABOS_EXCEL)
@RequiredArgsConstructor
public class FabosExcelController {
    @Resource
    private final FabosExcelService dataFileService;
    @Resource
    private final FabosModifyController fabosModifyController;
    @Resource
    private final FabosJsonService fabosJsonService;

    //模板下载
    @GetMapping(value = "/template/{fabosJsonModel}")
    public void getExcelTemplate(@PathVariable("fabosJsonModel") String fabosJsonModelName, HttpServletRequest request,
                                 HttpServletResponse response) throws IOException {
        FabosJsonModel fabosJsonModel = FabosJsonCoreService.getFabosJson(fabosJsonModelName);
        FabosJsonPowerUtil.checkPowerLegal(fabosJsonModel, PowerObject::isImportable);
        try (Workbook wb = dataFileService.createExcelTemplate(fabosJsonModel)) {
            wb.write(ExcelUtil.downLoadFile(request, response, fabosJsonModel.getFabosJson().name() + "_template" + FabosExcelService.XLS_FORMAT));
        }
    }

    public static final int MAX_PAGE_SIZE = 50000;
    //导出
    @PostMapping("/export/{fabosJsonModel}")
    public void exportData(@PathVariable("fabosJsonModel") String fabosJsonModelName,
                           @RequestBody(required = false) QueryCondition queryCondition,
                           HttpServletRequest request, HttpServletResponse response) throws IOException {
        FabosJsonModel fabosJsonModel = FabosJsonCoreService.getFabosJson(fabosJsonModelName);
        FabosJsonPowerUtil.checkPowerLegal(fabosJsonModel, PowerObject::isExport);
        TableQuery tableQuery = new TableQuery();
        List<Condition> list = fabosJsonService.generatorCondition(queryCondition);
        tableQuery.setPage(1);
        tableQuery.setPerPage(MAX_PAGE_SIZE);
        tableQuery.setOrderBy(queryCondition.getOrderBy());
        tableQuery.setOrderDir(queryCondition.getOrderDir());
        tableQuery.setCondition(list);
//        if (tableQuery.getPerPage() > MAX_PAGE_SIZE) {
//            tableQuery.setPerPage(MAX_PAGE_SIZE);
//        }
        Page page = fabosJsonService.getTableData(fabosJsonModel, tableQuery, null);
        // 为导出的page处理枚举显示
        Optional.ofNullable(page.getList()).ifPresent(it -> DataHandlerUtil.convertDataToFabosJsonView(fabosJsonModel, it));
        try (Workbook wb = dataFileService.exportExcel(fabosJsonModel, page)) {
            DataProxyInvoke.invoke(fabosJsonModel, (dataProxy -> dataProxy.excelExport(wb)));
            wb.write(ExcelUtil.downLoadFile(request, response, fabosJsonModel.getFabosJson().name() + FabosExcelService.XLSX_FORMAT));
        }
    }

    //导入
    @PostMapping("/import/{fabosJsonModel}")
    @Transactional(rollbackOn = Exception.class)
    public R importExcel(@PathVariable("fabosJsonModel") String eruptName, @RequestParam("file") MultipartFile file) {
        FabosJsonModel fabosJsonModel = FabosJsonCoreService.getFabosJson(eruptName);
        // 既然是导出，这里的fabosJsonModel应该是还主模型的，所以这个权限校验还是能用
        FabosJsonPowerUtil.powerLegal(fabosJsonModel, PowerObject::isImportable, "Not import permission");
        if (file.isEmpty() || null == file.getOriginalFilename()) return R.error("上传失败，请选择文件");
        List<JsonObject> list;
        int i = 1;
        try {
            i++;
            Workbook wb;
            if (file.getOriginalFilename().endsWith(FabosExcelService.XLS_FORMAT)) {
                wb = new HSSFWorkbook(file.getInputStream());
            } else if (file.getOriginalFilename().endsWith(FabosExcelService.XLSX_FORMAT)) {
                wb = new XSSFWorkbook(file.getInputStream());
            } else {
                throw new RuntimeException("上传文件格式必须为Excel");
            }
            DataProxyInvoke.invoke(fabosJsonModel, (dataProxy -> dataProxy.excelImport(wb)));
            list = dataFileService.excelToEruptObject(fabosJsonModel, wb);
            wb.close();
        } catch (Exception e) {
            throw new RuntimeException("Excel解析异常，出错行数：" + i + "，原因：" + e.getMessage(), e);
        }
        try {
            fabosModifyController.batchAddFabosJsonData(fabosJsonModel, list);
        } catch (Exception e) {
            throw new RuntimeException("数据导入异常，原因：" + e.getMessage());
        }
        return R.ok();
    }

}
