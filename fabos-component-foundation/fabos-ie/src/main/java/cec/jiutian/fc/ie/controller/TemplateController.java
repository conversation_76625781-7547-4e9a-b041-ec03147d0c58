package cec.jiutian.fc.ie.controller;

import cec.jiutian.fc.ie.util.ExcelTemplateGenerator;
import cec.jiutian.fc.ie.service.TemplateConfigService;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description:
 */
@RestController
@RequestMapping("/excel")
public class TemplateController {
    @Autowired
    private ExcelTemplateGenerator excelTemplateGenerator;
    @Resource
    private TemplateConfigService templateConfigService;

    /**
     * 测试模版生成
     * @return
     * @throws IOException
     */
    @GetMapping("/download-template")
    public ResponseEntity<byte[]> downloadTemplate() throws IOException {
        List<Map<String, Object>> sheets = templateConfigService.getTemplateConfig();
        ByteArrayInputStream inputStream = ExcelTemplateGenerator.generateTemplate(sheets);

        // 设置响应头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentDispositionFormData("attachment", "template.xlsx");

        return ResponseEntity.ok()
                .headers(headers)
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .body(inputStream.readAllBytes());
    }
}
