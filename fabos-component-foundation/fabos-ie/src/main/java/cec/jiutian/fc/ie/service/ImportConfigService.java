package cec.jiutian.fc.ie.service;

import cec.jiutian.common.context.UserContext;
import cec.jiutian.common.util.JacksonUtil;
import cec.jiutian.common.util.StringUtils;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.core.frame.service.FabosJsonCoreService;
import cec.jiutian.core.view.fabosJson.view.FabosJsonFieldModel;
import cec.jiutian.core.view.fabosJson.view.FabosJsonModel;
import cec.jiutian.data.jpa.JpaCrud;
import cec.jiutian.fc.ie.domain.imp.ImportConfig;
import cec.jiutian.fc.ie.domain.imp.ImportLog;
import cec.jiutian.fc.ie.domain.imp.ImportLogErrDetail;
import cec.jiutian.fc.ie.domain.imp.ImportModelConfig;
import cec.jiutian.fc.ie.domain.imp.ImportModelFieldConfig;
import cec.jiutian.fc.ie.dto.ImportConfigDTO;
import cec.jiutian.fc.ie.dto.ImportModelDTO;
import cec.jiutian.fc.ie.dto.ImportModelFieldDTO;
import cec.jiutian.fc.ie.dto.InitModelDTO;
import cec.jiutian.fc.ie.enums.ErrorHandleEnum;
import cec.jiutian.fc.ie.util.ExcelImpUtil;
import cec.jiutian.fc.ie.util.ExcelTemplateGenerator;
import cec.jiutian.meta.model.MetadataModel;
import cec.jiutian.meta.model.MetadataModelField;
import cec.jiutian.view.field.Edit;
import com.fasterxml.jackson.core.JsonProcessingException;
import jakarta.annotation.Resource;
import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description:
 */
@Service
@Slf4j
public class ImportConfigService {
    @Resource
    private JpaCrud jpaCrud;
    @Resource
    private FabosJsonDao fabosJsonDao;
    @Resource
    private ImportDbService importDbService;

    public Map<String,List<ImportModelFieldDTO>> initModelFieldMap(InitModelDTO model){
        if(model==null){
            throw new FabosJsonApiErrorTip("参数异常");
        }
        if(CollectionUtils.isEmpty(model.getModels())){
            throw new FabosJsonApiErrorTip("参数异常");
        }
        Map<String,List<ImportModelFieldDTO>> modelFieldMap = new HashMap<>();
        for (String modelName : model.getModels()) {
            List<ImportModelFieldDTO> importModelFieldDTOS = initModelField(modelName);
            modelFieldMap.put(modelName,importModelFieldDTOS);
        }
        return modelFieldMap;
    }
    private List<ImportModelFieldDTO> initModelField(String model){
        List<ImportModelFieldDTO> importModelFieldDTOS = new ArrayList<>();

        Map<String, FabosJsonFieldModel> fabosJsonFieldMap = getFabosJsonFieldMap(model);
        MetadataModel metadataModelQuery = new MetadataModel();
        metadataModelQuery.setName(model);
        MetadataModel metadataModel = jpaCrud.selectOne(metadataModelQuery);
        if(CollectionUtils.isNotEmpty(metadataModel.getFields())){
            for (MetadataModelField metaField : metadataModel.getFields()){
                //多对多暂不支持  一对多不需要配置 一对多需要在子表配置引用而不是在主表  因此主表的一对多不需要配置
                if(StringUtils.isNotBlank(metaField.getFieldType())&&("ManyToMany".equals(metaField.getFieldType())||"OneToMany".equals(metaField.getFieldType()))){
                    continue;
                }
                ImportModelFieldDTO importModelFieldDTO = new ImportModelFieldDTO();
                importModelFieldDTO.setFieldCode(metaField.getName());
                importModelFieldDTO.setFieldName(StringUtils.isNotBlank(metaField.getDisplayName())?metaField.getDisplayName():metaField.getName());
                importModelFieldDTO.setFieldJavaType(metaField.getFieldJavaType());
                FabosJsonFieldModel fabosJsonFieldModel = fabosJsonFieldMap.get(metaField.getName());
                Edit edit = fabosJsonFieldModel.getFabosJsonField().edit();
                importModelFieldDTO.setIsNull(edit.notNull()?1:0);
                importModelFieldDTO.setIsPrimaryKey(metaField.getPrimaryKey()?1:0);
                importModelFieldDTO.setIsReference(0);
                if(StringUtils.isNotBlank(metaField.getFieldType())){
                    importModelFieldDTO.setIsReference(1);
                    String javaType = metaField.getFieldJavaType();
                    importModelFieldDTO.setReferenceObject(javaType.substring(javaType.lastIndexOf(".") + 1));
                }
                importModelFieldDTO.setIsImport(1);


                importModelFieldDTOS.add(importModelFieldDTO);
            }

        }
        return importModelFieldDTOS;
    }

    private Map<String, FabosJsonFieldModel> getFabosJsonFieldMap(String model){
        FabosJsonModel fabosJsonModel = FabosJsonCoreService.getFabosJson(model);
        //fabosJsonModel.getFabosJsonFieldMap()里面字段不够详细
        return fabosJsonModel.getFabosJsonFieldModels().stream().collect(Collectors.toMap(FabosJsonFieldModel::getFieldName, Function.identity(), (key1, key2) -> key2));
    }

    public void addImportConfig(ImportConfigDTO dto){
        if(dto==null){
            throw new FabosJsonApiErrorTip("参数异常");
        }
        if(CollectionUtils.isEmpty(dto.getImportModelAddDTOS())){
            throw new FabosJsonApiErrorTip("模型不能为空");
        }
        if(StringUtils.isBlank(dto.getConfigCode())){
            throw new FabosJsonApiErrorTip("配置编码必填");
        }
        if(StringUtils.isBlank(dto.getConfigName())){
            throw new FabosJsonApiErrorTip("配置名称必填");
        }
        ImportConfig importConfig = buildImportConfig(dto);
        jpaCrud.insert(importConfig);
    }

    /**
     * 构建参数  新增编辑都在调用
     * @param dto
     * @return
     */
    //通过ImportConfigAddDTO构建ImportConfig对象
    private ImportConfig buildImportConfig(ImportConfigDTO dto){
        ImportConfig importConfig = new ImportConfig();
        importConfig.setConfigCode(dto.getConfigCode());
        importConfig.setConfigName(dto.getConfigName());
        importConfig.setMainModelCode(dto.getMainModelCode());
        importConfig.setMainModelName(dto.getMainModelName());
        importConfig.setMainModelId(dto.getMainModelId());
        importConfig.setErrorHandle(dto.getErrorHandle());
        importConfig.setCreateBy(UserContext.getUserName());
        importConfig.setCreateTime(LocalDateTime.now());
        List<ImportModelConfig> importModelConfigs = new ArrayList<>();
        dto.getImportModelAddDTOS().forEach(model->{
            ImportModelConfig modelConfig = new ImportModelConfig();

            MetadataModel metadataModel = jpaCrud.getById(MetadataModel.class, model.getId());
            modelConfig.setModelClass(metadataModel.getCode());
            modelConfig.setModel(metadataModel.getName());
            modelConfig.setModelName(metadataModel.getDisplayName());
            modelConfig.setTableName(metadataModel.getTableName());
            modelConfig.setSort(model.getSort());
            modelConfig.setIsMainModel(model.getIsMainModel());
            modelConfig.setModelId(model.getId());
            List<ImportModelFieldConfig> fieldConfigs = new ArrayList<>();
            Map<String, List<MetadataModelField>> filedMap = metadataModel.getFields().stream().collect(Collectors.groupingBy(MetadataModelField::getName));
            model.getImportModelFieldAddDTOS().forEach(field->{
                ImportModelFieldConfig fieldConfig = new ImportModelFieldConfig();
                fieldConfig.setFieldCode(field.getFieldCode());
                fieldConfig.setFieldName(field.getFieldName());
                fieldConfig.setIsImport(field.getIsImport());
                fieldConfig.setIsPrimaryKey(field.getIsPrimaryKey());
                fieldConfig.setIsReference(field.getIsReference());
                //如果是引用 那么必须要存在以下三个字段
                if(field.getIsReference()==1){
                    if(StringUtils.isBlank(field.getReferenceObject())){
                        throw new FabosJsonApiErrorTip("引用对象配置错误");
                    }
                    if(StringUtils.isBlank(field.getReferenceInputField())){
                        throw new FabosJsonApiErrorTip("引用存储字段配置错误");
                    }
                    if(StringUtils.isBlank(field.getReferenceField())){
                        throw new FabosJsonApiErrorTip("引用录入字段配置错误");
                    }
                }
                fieldConfig.setReferenceField(field.getReferenceField());
                fieldConfig.setReferenceInputField(field.getReferenceInputField());
                fieldConfig.setReferenceObject(field.getReferenceObject());
                fieldConfig.setIsNull(field.getIsNull());
                fieldConfig.setFieldJavaType(field.getFieldJavaType());
                List<MetadataModelField> metadataModelFields = filedMap.get(fieldConfig.getFieldCode());
                if(CollectionUtils.isNotEmpty(metadataModelFields)){
                    fieldConfig.setColumnName(metadataModelFields.get(0).getColumnName());
                }else if("id".equals(fieldConfig.getFieldCode())){
                    fieldConfig.setColumnName("id");
                }
                fieldConfigs.add(fieldConfig);
            });
            modelConfig.setFieldConfigs(fieldConfigs);
            importModelConfigs.add(modelConfig);
        });
        importConfig.setImportModelConfigs(importModelConfigs);
        return importConfig;
    }

    public ImportConfigDTO getById(String id){
        ImportConfig importConfig = jpaCrud.getById(ImportConfig.class,id);
        if(importConfig==null){
            throw new FabosJsonApiErrorTip("配置不存在");
        }
        ImportConfigDTO importConfigDTO = new ImportConfigDTO();
        importConfigDTO.setId(importConfig.getId());
        importConfigDTO.setConfigCode(importConfig.getConfigCode());
        importConfigDTO.setConfigName(importConfig.getConfigName());
        importConfigDTO.setMainModelCode(importConfig.getMainModelCode());
        importConfigDTO.setMainModelName(importConfig.getMainModelName());
        importConfigDTO.setMainModelId(importConfig.getMainModelId());
        importConfigDTO.setErrorHandle(importConfig.getErrorHandle());
        List<ImportModelDTO> importModelConfigs = new ArrayList<>();
        importConfig.getImportModelConfigs().forEach(model->{
            ImportModelDTO modelConfig = new ImportModelDTO();
            modelConfig.setName(model.getModel());
            modelConfig.setId(model.getModelId());
            modelConfig.setDisplayName(model.getModelName());
            modelConfig.setSort(model.getSort());
            modelConfig.setIsMainModel(model.getIsMainModel());
            List<ImportModelFieldDTO> fieldConfigs = new ArrayList<>();
            model.getFieldConfigs().forEach(field->{
                ImportModelFieldDTO fieldConfig = new ImportModelFieldDTO();
                fieldConfig.setFieldCode(field.getFieldCode());
                fieldConfig.setFieldName(field.getFieldName());
                fieldConfig.setIsImport(field.getIsImport());
                fieldConfig.setIsPrimaryKey(field.getIsPrimaryKey());
                fieldConfig.setIsReference(field.getIsReference());
                fieldConfig.setReferenceField(field.getReferenceField());
                fieldConfig.setReferenceInputField(field.getReferenceInputField());
                fieldConfig.setReferenceObject(field.getReferenceObject());
                fieldConfig.setIsNull(field.getIsNull());
                fieldConfig.setFieldJavaType(field.getFieldJavaType());
                fieldConfigs.add(fieldConfig);
            });
            modelConfig.setImportModelFieldAddDTOS(fieldConfigs);
            importModelConfigs.add(modelConfig);
        });
        //排序返回importModelConfigs里面按照sort字段升序返回
        importModelConfigs.sort(Comparator.comparingInt(ImportModelDTO::getSort));
        importConfigDTO.setImportModelAddDTOS(importModelConfigs);
        return importConfigDTO;
    }
    @Transactional
    public void editImportConfig(ImportConfigDTO dto){
        if(dto==null){
            throw new FabosJsonApiErrorTip("参数异常");
        }
        if(StringUtils.isBlank(dto.getId())){
            throw new FabosJsonApiErrorTip("id不能为空");
        }
        if(CollectionUtils.isEmpty(dto.getImportModelAddDTOS())){
            throw new FabosJsonApiErrorTip("模型不能为空");
        }
        if(StringUtils.isBlank(dto.getConfigCode())){
            throw new FabosJsonApiErrorTip("配置编码必填");
        }
        if(StringUtils.isBlank(dto.getConfigName())){
            throw new FabosJsonApiErrorTip("配置名称必填");
        }
        ImportConfig importConfigDb = jpaCrud.getById(ImportConfig.class,dto.getId());
        if(importConfigDb==null){
            throw new FabosJsonApiErrorTip("配置不存在");
        }
        ImportModelConfig importModelConfigQuery = new ImportModelConfig();
        importModelConfigQuery.setImportConfig(importConfigDb);
        List<ImportModelConfig> select = jpaCrud.select(importModelConfigQuery);
        select.forEach(model->{
            ImportModelFieldConfig importModelFieldConfigQuery = new ImportModelFieldConfig();
            importModelFieldConfigQuery.setImportModelConfig(model);
            List<ImportModelFieldConfig> fieldConfigs = jpaCrud.select(importModelFieldConfigQuery);
            fieldConfigs.forEach(field->{
                jpaCrud.delete(field);
            });
            jpaCrud.delete(model);
        });


        ImportConfig importConfig = buildImportConfig(dto);
        importConfig.setId(dto.getId());
        fabosJsonDao.mergeAndFlush(importConfig);
    }

    public void delete(ImportConfigDTO dto){
        if(dto==null){
            throw new FabosJsonApiErrorTip("参数异常");
        }
        if(StringUtils.isBlank(dto.getId())){
            throw new FabosJsonApiErrorTip("id不能为空");
        }
        jpaCrud.deleteById(ImportConfig.class,dto.getId());
    }

    public ImportConfig getImportConfig(String id){
        return jpaCrud.getById(ImportConfig.class,id);
    }

    /**
     * List<Map<String, Object>> sheets = new ArrayList<>();
     *
     *         // Sheet1配置
     *         Map<String, Object> sheet1 = new HashMap<>();
     *         sheet1.put("name", "Sheet1");
     *         sheet1.put("fields", Arrays.asList("Name", "Gender"));
     *         sheet1.put("dropdowns", Collections.singletonList(Map.of(
     *                 "field", "Gender",
     *                 "values", Arrays.asList("男", "女")
     *         )));
     *         sheets.add(sheet1);
     *         // Sheet2配置
     *         Map<String, Object> sheet2 = new HashMap<>();
     *         sheet2.put("name", "Sheet2");
     *         sheet2.put("fields", Arrays.asList("Name", "Age"));
     *         sheet2.put("dependentField", "Name");
     *         sheet2.put("dependentSheet", "Sheet1");
     *         sheet2.put("dependentFieldInSource", "Name");
     *         sheets.add(sheet2);
     * @param importConfig
     * @return
     * @throws IOException
     */
    public ByteArrayInputStream generateTemplate(ImportConfig importConfig) throws IOException {
        List<Map<String, Object>> sheets = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(importConfig.getImportModelConfigs())){
            importConfig.getImportModelConfigs().forEach(modelConfig->{
                Map<String, Object> sheet = new HashMap<>();
                sheet.put("name", modelConfig.getModelName());
                List<String> fields = new ArrayList<>();
                Map<String, FabosJsonFieldModel> fabosJsonFieldMap = getFabosJsonFieldMap(modelConfig.getModel());
                List<Map<String,Object>> dropdowns = new ArrayList<>();
                for (ImportModelFieldConfig d:modelConfig.getFieldConfigs()){
                    if (d.getIsImport()==0){
                        //不允许导入的  就不需要生成模版
                        continue;
                    }
                    fields.add(d.getFieldName());
                    //目前有两种需要设置excel的下拉框  第一种是布尔类型的字段   第二种是枚举
                    if("java.lang.Boolean".equals(d.getFieldJavaType())){
                        Map<String,Object> map = new HashMap<>();
                        map.put("field", d.getFieldName());
                        map.put("values", Arrays.asList("true","false"));
                        dropdowns.add(map);
                    }else {
                        //是否配置了枚举 主要是@Edit里面的choiceType配置
                        if(fabosJsonFieldMap.containsKey(d.getFieldCode())){
                            FabosJsonFieldModel fabosJsonFieldModel = fabosJsonFieldMap.get(d.getFieldCode());
                            if(fabosJsonFieldModel.getFabosJsonField().edit().choiceType()!=null&&fabosJsonFieldModel.getComponentValue()!=null){
                                Map<String,Object> map = new HashMap<>();
                                map.put("field", d.getFieldName());
                                List<Map<String, Object>> list = (List<Map<String, Object>>) fabosJsonFieldModel.getComponentValue();
                                List<String> values = new ArrayList<>();
                                for (Map<String, Object> valueMap : list) {
                                    String value = (String) valueMap.get("value");
                                    String label = (String) valueMap.get("label");
                                    values.add(label+"("+value+")");
                                }
                                map.put("values", values);
                                dropdowns.add(map);
                            }
                        }
                    }
                }
                sheet.put("fields", fields);
                if(CollectionUtils.isNotEmpty(dropdowns)){
                    sheet.put("dropdowns", dropdowns);
                }
                sheets.add(sheet);
            });
        }
        return ExcelTemplateGenerator.generateTemplate(sheets);
    }

    /**
     * 处理跳过 外部无事务内部存在事务   嵌套事务使用try catch不去处理异常会出现rollback-only
     * @param excellAllDataMap
     * @param importConfig
     * @throws JsonProcessingException
     */
    public ImportLog handleSkip(Map<String,List<Map<String, Object>>> excellAllDataMap,ImportConfig importConfig) throws JsonProcessingException {
        return handleExcelDataMap(excellAllDataMap,importConfig);
    }

    /**
     * 处理中断 外部事务内部无
     * @param excellAllDataMap
     * @param importConfig
     * @throws JsonProcessingException
     */
    @Transactional
    public ImportLog handleInterrupt(Map<String,List<Map<String, Object>>> excellAllDataMap,ImportConfig importConfig) throws JsonProcessingException {
        return handleExcelDataMap(excellAllDataMap,importConfig);
    }



    /**
     * 将解析出来的所有excel数据进行转换，按照importConfig配置进行转换
     *  1.将每个sheet数据的key从filedName转filedCode 。filedName是给客户显示的中文名，filedCode是模型名称字段。需要转换
     *  2.如果字段配置的是不允许导入的，则不进行转换。需要封装
     *  3.如果字段是一个引用对象。需要封装引用的对象
     *  4.这里只需要封装主模型。其他的引用模型需要封装到主模型里面。如果引用模型不存在，还需要查询数据库用于封装
     * @param excellAllDataMap  key是sheet名。value是sheet数据
     * @param importConfig  配置
     * @return 返回需要保存的数据
     */
    public ImportLog handleExcelDataMap(Map<String,List<Map<String, Object>>> excellAllDataMap,ImportConfig importConfig) throws JsonProcessingException {
        //判断出错是否中断。如果需要中断，表示遇到错误直接抛出异常然后事务回滚。如果出现错误跳过那么就是使用try catch捕获异常
        boolean interrupt = ErrorHandleEnum.INTERRUPT.name().equals(importConfig.getErrorHandle());//遇到错误是否中断

        //需要按照配置模型的顺序 因为这里存在对象的引用前后关系。
        List<ImportModelConfig> importModelConfigs = importConfig.getImportModelConfigs();
        //排序  按照排序顺序执行db
        importModelConfigs.sort(Comparator.comparing(ImportModelConfig::getSort));
        //处理日志
        ImportLog importLog = new ImportLog();
        importLog.setConfigCode(importConfig.getConfigCode());
        importLog.setConfigName(importConfig.getConfigName());
        importLog.setMainModelCode(importConfig.getMainModelCode());
        importLog.setMainModelName(importConfig.getMainModelName());
        importLog.setExecutor(UserContext.getUserName());
        importLog.setExecuteTime(LocalDateTime.now());
        Integer sum = 0;
        Integer errNum = 0;
        List<ImportLogErrDetail> importLogErrDetails = new ArrayList<>();
        //新的数据，封装过后的excel数据
        for (ImportModelConfig importModelConfig : importModelConfigs) {
            //每个模型字段配置
            List<ImportModelFieldConfig> fieldConfigs = importModelConfig.getFieldConfigs();
            Map<String,ImportModelFieldConfig> fieldNameConfigMap = fieldConfigs.stream().collect(Collectors.toMap(ImportModelFieldConfig::getFieldName, Function.identity()));
            //该模型所有数据
            List<Map<String, Object>> sheetDataList = excellAllDataMap.get(importModelConfig.getModelName());
            Integer rowNum = 1;//跳过了表头，因此从1开始自增
            for (Map<String, Object> map : sheetDataList) {
                //处理每条数据
                rowNum++;
                sum++;
                //单条数据封装
                Map<String, Object> stringObjectMap = convertExcelOneData(map, fieldNameConfigMap);
                try {
                    if (interrupt){
                        importDbService.saveByInterrupt(stringObjectMap,importModelConfig);
                    }else {
                        importDbService.saveBySkip(stringObjectMap,importModelConfig);
                    }
                }catch (Exception e){
                    log.error("数据处理异常：" + e.getMessage());
                    errNum++;
                    ImportLogErrDetail importLogErrDetail = new ImportLogErrDetail();
                    importLogErrDetail.setFailData(JacksonUtil.toJson(stringObjectMap));
                    importLogErrDetail.setFailReason(e.getMessage());
                    importLogErrDetail.setSheetName(importModelConfig.getModelName());
                    importLogErrDetail.setRowNum(rowNum);
                    importLogErrDetails.add(importLogErrDetail);
                    if(interrupt){
                        throw new RuntimeException("sheet页："+importModelConfig.getModelName()+"第"+rowNum+"行遇到错误，中断");
                    }
                }
            }
        }
        importLog.setTotalRows(sum);
        importLog.setFailRows(errNum);
        importLog.setSuccessRows(sum-errNum);
        importLog.setImportLogErrDetails(importLogErrDetails);
        jpaCrud.insert(importLog);
        return importLog;
    }


    //封装excel单条数据结构
    private Map<String,Object> convertExcelOneData(Map<String, Object> dataMap,Map<String,ImportModelFieldConfig> fieldNameConfigMap){
        Map<String,Object> resultMap = new HashMap<>();
        //循环fieldNameConfigMap
        for (Map.Entry<String, ImportModelFieldConfig> entry : fieldNameConfigMap.entrySet()) {
            String filedName = entry.getKey();
            //获取该字段的配置
            ImportModelFieldConfig importModelFieldConfig = entry.getValue();
            //配置不允许为空并且value为空直接返回异常
            if(importModelFieldConfig.getIsNull()==1&&dataMap.get(filedName)==null){
                throw new FabosJsonApiErrorTip("字段:"+filedName+"不允许为空");
            }
            if(importModelFieldConfig.getIsImport()==0){
                //不导入。跳过
                continue;
            }
            if(importModelFieldConfig.getIsReference()==1){
                //引用类型。如果引用存储字段和引用录入字段不一致，则转换value至。如果是一样的就不处理
                Object newValue = getIsReferenceFieldValue(importModelFieldConfig,dataMap);
                log.info("引用类型转换前：{},转换后：{}",dataMap.get(filedName),newValue);
                resultMap.put(importModelFieldConfig.getFieldCode(),newValue);
            }else {
                //使用code
                if("id".equals(importModelFieldConfig.getFieldCode())&&dataMap.get(filedName)==null){
                    resultMap.put(importModelFieldConfig.getFieldCode(),UUID.randomUUID().toString());
                }else {
                    resultMap.put(importModelFieldConfig.getFieldCode(),dataMap.get(filedName));
                }
            }
            //布尔类型拿到的是字符串。需要转一下数据  判断逻辑是importModelFieldConfig.getFieldJavaType()字段是否包含 Boolean
            if (importModelFieldConfig.getFieldJavaType().contains("Boolean")){
                resultMap.put(importModelFieldConfig.getFieldCode(),Boolean.valueOf(dataMap.get(filedName).toString()));
            }
        }
        return resultMap;
    }

    /**
     * 引用类型 查询实体
     * @param importModelFieldConfig
     * @param dataMap
     * @return
     */
    private Object getIsReferenceFieldValue(ImportModelFieldConfig importModelFieldConfig,Map<String, Object> dataMap){
        Object value = dataMap.get(importModelFieldConfig.getFieldName());
        if(value==null){
            return null;
        }
        if(importModelFieldConfig.getReferenceField().equals(importModelFieldConfig.getReferenceInputField())){
            return value;
        }
        //如果不相等。需要转化。比如录入字段code，存储字段是id，那么需要通过code查询出具体的id。使用id保存关联数据
        String hql = "select "+importModelFieldConfig.getReferenceField()+" from "+importModelFieldConfig.getReferenceObject() +" where "+importModelFieldConfig.getReferenceInputField()+" = :parameter";
        log.info("导入引用类型字段，value转换查询hql：{},value:{}",hql,value);
        EntityManager entityManager = fabosJsonDao.getEntityManager();
        Query query = entityManager.createQuery(hql);
        query.setParameter("parameter", value);
        Object newValue = CollectionUtils.isNotEmpty(query.getResultList())?query.getResultList().get(0):null;
        //如果引用类型查询出来为空。需要报错提示引用异常
        if(newValue==null){
            throw new FabosJsonApiErrorTip("引用字段：【"+importModelFieldConfig.getFieldName()+"】通过:【"+value+"】查询结果为空");
        }
        return newValue;
    }
    /**
     * 校验模版 ： sheet页是否与配置一致   每个sheet页的字段是否与配置一致
     * @param importModelConfigs
     * @param workbook
     */
    public void checkImport(List<ImportModelConfig> importModelConfigs,Workbook workbook){
        if(importModelConfigs.size()!=workbook.getNumberOfSheets()){
            throw new FabosJsonApiErrorTip("Excel中Sheet页数量与配置的模型数量不一致，请重新下载模版");
        }
        for (ImportModelConfig importModelConfig : importModelConfigs){
            String sheetName = importModelConfig.getModelName();
            Sheet sheet = workbook.getSheet(sheetName);
            if(sheet==null){
                throw new FabosJsonApiErrorTip("Excel中Sheet名必须与配置的模型名一致，请重新下载模版");
            }
            List<ImportModelFieldConfig> fieldConfigs = importModelConfig.getFieldConfigs();
            if(CollectionUtils.isEmpty(fieldConfigs)){
                throw new FabosJsonApiErrorTip("模型："+sheetName+"配置的模型字段为空，请检查配置");
            }
            //过滤掉fieldConfigs中isImport字段为0的数据
            List<ImportModelFieldConfig> configs = fieldConfigs.stream().filter(fieldConfig -> fieldConfig.getIsImport() == 1).toList();
            List<String> headerRow = ExcelImpUtil.getHeaderRow(sheet);
            if(CollectionUtils.isEmpty(headerRow)){
                throw new FabosJsonApiErrorTip("sheet页："+sheetName+"表头信息为空，请重新下载模版");
            }
            if(configs.size()!=headerRow.size()){
                throw new FabosJsonApiErrorTip("sheet页："+sheetName+"表头字段数量与配置的模型字段数量不一致，请重新下载模版");
            }
            //sheet页配置的表头信息与模型字段名称不一致
            for (ImportModelFieldConfig fieldConfig : configs){
                String fieldName = fieldConfig.getFieldName();
                if(!headerRow.contains(fieldName)){
                    throw new FabosJsonApiErrorTip("sheet页："+sheetName+"表头配置字段："+fieldName+"不存在，请重新下载模版");
                }
            }
        }
    }
}
