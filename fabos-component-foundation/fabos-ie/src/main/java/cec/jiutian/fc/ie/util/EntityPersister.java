package cec.jiutian.fc.ie.util;

import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.core.frame.service.FabosJsonCoreService;
import cec.jiutian.core.view.fabosJson.view.FabosJsonModel;
import jakarta.persistence.EntityManager;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToOne;
import jakarta.persistence.metamodel.EntityType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.util.Map;

@Slf4j
@Service
public class EntityPersister {
    private final EntityManager entityManager;

    private final FabosJsonDao fabosJsonDao;

    public EntityPersister(EntityManager entityManager, FabosJsonDao fabosJsonDao) {
        this.entityManager = entityManager;
        this.fabosJsonDao = fabosJsonDao;
    }

    /**
     * 通过实体名称创建、赋值并保存实体
     *
     * @param entityName  实体名称（简单类名或全限定名）
     * @param fieldValues 字段名-值映射
     * @return 保存后的实体
     */
    public <T> T createAndSaveEntity(String entityName, Map<String, Object> fieldValues) {
        try {
            // 1. 获取实体类
            Class<?> entityClass = resolveEntityClass(entityName);

            // 2. 创建实体实例
            Object entity = entityClass.getDeclaredConstructor().newInstance();

            // 3. 安全填充属性
            populateEntityFields(entity, fieldValues);

            entityManager.merge(entity);

            return (T) entity;
        } catch (Exception e) {
            log.error("创建并保存实体失败: {} - {}", entityName, e.getMessage(), e);
            throw new EntityOperationException("实体操作失败: " + entityName, e);
        }
    }

    /**
     * 解析实体类
     */
    private Class<?> resolveEntityClass(String entityName) {
        // 尝试通过简单类名查找
        for (EntityType<?> entityType : entityManager.getMetamodel().getEntities()) {
            Class<?> javaType = entityType.getJavaType();
            if (javaType.getSimpleName().equals(entityName) ||
                    javaType.getName().equals(entityName)) {
                return javaType;
            }
        }
        throw new IllegalArgumentException("未找到实体: " + entityName);
    }

    /**
     * 安全填充实体字段
     */
    private void populateEntityFields(Object entity, Map<String, Object> fieldValues)
            throws IllegalAccessException {
        Class<?> clazz = entity.getClass();

        for (Map.Entry<String, Object> entry : fieldValues.entrySet()) {
            String fieldName = entry.getKey();
            Object value = entry.getValue();

            try {
                // 获取字段（包括父类字段）
                Field field = findField(clazz, fieldName);
                if (field == null) {
                    log.error("未找到字段: {}", fieldName);
                    throw new NoSuchFieldException("字段不存在: " + fieldName);
                }

                // 如果字段为ManyToOne或OneToOne等关联字段，查询出对象
                FabosJsonModel fabosJsonModel = FabosJsonCoreService.getFabosJson(entity.getClass().getSimpleName());
                if (value != null && (fabosJsonModel.getFabosJsonFieldMap().get(entry.getKey()).getField().getAnnotation(ManyToOne.class) != null ||
                        fabosJsonModel.getFabosJsonFieldMap().get(entry.getKey()).getField().getAnnotation(OneToOne.class) != null)) {
                    // 如果是对象字段，则需要查询出对象
                    value = fabosJsonDao.getById(fabosJsonModel.getFabosJsonFieldMap().get(entry.getKey()).getField().getType(), entry.getValue());
                }

                // 检查value是否为uuid类型，是则转换为String
                if (value != null && value.getClass().getName().equals("java.util.UUID")) {
                    value = value.toString();
                }

                // 设置字段值
                if (value != null) {
                    field.setAccessible(true);
                    field.set(entity, value);
                }
            } catch (NoSuchFieldException e) {
                log.error("实体字段错误: {}", e);
                throw new EntityOperationException("实体字段错误", e);
            } catch (Exception e) {
                log.error("设置字段值失败: {}.{} = {}", clazz.getSimpleName(), fieldName, value, e);
                throw new EntityOperationException("设置字段值失败: " + fieldName, e);
            }
        }
    }

    /**
     * 查找字段（包括父类）
     */
    private Field findField(Class<?> clazz, String fieldName) {
        for (Class<?> c = clazz; c != null; c = c.getSuperclass()) {
            try {
                return c.getDeclaredField(fieldName);
            } catch (NoSuchFieldException e) {
                // 继续在父类中查找
            }
        }
        return null;
    }

}

// 自定义异常
class EntityOperationException extends RuntimeException {
    public EntityOperationException(String message, Throwable cause) {
        super(message, cause);
    }
}
