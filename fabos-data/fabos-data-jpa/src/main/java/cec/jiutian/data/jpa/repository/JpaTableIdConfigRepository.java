package cec.jiutian.data.jpa.repository;

import cec.jiutian.core.data.po.TableIdConfigPO;
import jakarta.persistence.LockModeType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface JpaTableIdConfigRepository extends JpaRepository<TableIdConfigPO, Long> {


     @Query(value = "from TableIdConfigPO t where t.tableName = ?1")
     TableIdConfigPO getTableIdConfigByName(String tableName);

    @Query("from TableIdConfigPO")
    List<TableIdConfigPO> getAllTableIdConfigList();

    @Lock(LockModeType.PESSIMISTIC_WRITE)
    @Query("from TableIdConfigPO t where t.identifier = ?1")
    TableIdConfigPO getTableIdConfigById(Long id);
}
