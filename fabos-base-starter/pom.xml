<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cec.jiutian</groupId>
        <artifactId>fabos-parent</artifactId>
        <version>3.2.2-SNAPSHOT</version>
    </parent>

    <artifactId>fabos-base-starter</artifactId>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <koupleless.runtime.version>2.1.1</koupleless.runtime.version>
        <sofa.ark.version>3.1.3</sofa.ark.version>
        <dubbo.version>3.3.0</dubbo.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>cec.jiutian</groupId>
            <artifactId>fabos-component-manage</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>repackage</id>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                        <configuration>
                            <!--	fix https://github.com/koupleless/koupleless/issues/161		-->
                            <loaderImplementation>CLASSIC</loaderImplementation>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
<!--            为了让三方依赖和 koupleless 模式适配，需要引入以下构建插件-->
            <plugin>
                <groupId>com.alipay.sofa.koupleless</groupId>
                <artifactId>koupleless-base-build-plugin</artifactId>
                <version>2.1.1</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>add-patch</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
