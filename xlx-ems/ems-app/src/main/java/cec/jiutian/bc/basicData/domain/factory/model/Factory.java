package cec.jiutian.bc.basicData.domain.factory.model;

import cec.jiutian.bc.basicData.domain.factory.proxy.FactoryDataProxy;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.ReferenceTreeType;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.config.Comment;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.Tree;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/* 依赖的视图创建语句
create or replace view factory_info_view as
select
	cast(mfa.id as text),
 	cast(mfa.pid as text) as parent_factory_id,
	cast(mfa.fctry_ara_cd as text) as code,
	cast(mfa.fctry_ara_nm as text) as name,
	cast(mfa.fctry_ara_shrt_nm as text) as description,
	mfa.level + 1 as factory_level,
	cast(mfa.fctry_path_name as text) as path_name,
	mfa.sort
from
	mo_fctry_ara mfa
order by
	mfa.level + 1 asc;
 */
@Entity
@Table(name = "factory_info_view")
@FabosJson(
        name = "位置信息视图",
        dataProxy = FactoryDataProxy.class,
        power = @Power(add = false, edit = false, delete = false, importable = false, export = false),
        orderBy = "Factory.sort",
        tree = @Tree(pid = "parentFactory.id", expandLevel = 4)
)
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TemplateType(type = "treeForm")
public class Factory {

    @Id
    @FabosJsonField(
            views = @View(title = "id", show = false),
            edit = @Edit(title = "id", show = false)
    )
    private String id;

    @FabosJsonField(
            views = @View(title = "编码"),
            edit = @Edit(title = "编码", readonly = @Readonly(add = false, edit = true), notNull = true)
    )
    private String code;

    @FabosJsonField(
            views = @View(title = "名称"),
            edit = @Edit(title = "名称", notNull = true, readonly = @Readonly(add = false, edit = true))
    )
    private String name;

    @FabosJsonField(
            views = @View(title = "显示顺序"),
            edit = @Edit(title = "显示顺序", numberType = @NumberType(min = 1))
    )
    private Integer sort;

    @ManyToOne
    @FabosJsonField(
            edit = @Edit(
                    title = "上级代码",
                    type = EditType.REFERENCE_TREE,
                    referenceTreeType = @ReferenceTreeType(pid = "parentFactory.id", expandLevel = 4)
            )
    )
    private Factory parentFactory;

    @FabosJsonField(
            edit = @Edit(title = "级别", show = false),
            views = @View(title = "级别", show = true)
    )
    @Comment("逻辑字段，生成的当前级别")
    private Integer factoryLevel;

    @FabosJsonField(
            edit = @Edit(title = "描述", notNull = false,
                    type = EditType.TEXTAREA, inputType = @InputType(length = 400))
    )
    private String description;

    @FabosJsonField(
            edit = @Edit(title = "层级信息", show = false),
            views = @View(title = "层级信息", show = true)
    )
    private String pathName;
}
