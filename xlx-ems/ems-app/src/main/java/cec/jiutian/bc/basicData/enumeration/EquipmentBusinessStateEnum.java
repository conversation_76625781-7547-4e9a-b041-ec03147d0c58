package cec.jiutian.bc.basicData.enumeration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/26
 * @description TODO
 */
public class EquipmentBusinessStateEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum data : Enum.values()) {
            list.add(new VLModel(data.name(), data.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        Standby("待机"),
        Working("运行中"),
        FaultWarning("故障预警"),
        Unused("闲置"),
        Repairing("维修中"),
        ;

        private final String value;

    }
}
