package cec.jiutian.bc.basicData.domain.energyCompareAnalysis.model;

import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.View;
import cec.jiutian.view.type.Power;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025年04月16日 9:09
 */
@FabosJson(
        name = "能源对比分析表",
        power = @Power(add = false, edit = false, delete = false, importable = false, export = false)
)
@Table(name = "ems_energy_compare_consumption"
)
@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EnergyCompareAnalysis extends MetaModel {

    @FabosJsonField(
            views = @View(title = "统计时间管理Id"),
            edit = @Edit(title = "统计时间管理Id")
    )
    private String statisticManagementId;

    @FabosJsonField(
            views = @View(title = "抄表或者采集时间"),
            edit = @Edit(title = "抄表或者采集时间")
    )
    private Date readingTime;

    @FabosJsonField(
            views = @View(title = "位置信息Id"),
            edit = @Edit(title = "位置信息Id")
    )
    private String locationId;

    @FabosJsonField(
            views = @View(title = "位置信息名称"),
            edit = @Edit(title = "位置信息名称")
    )
    private String locationName;

    @FabosJsonField(
            views = @View(title = "设备Id"),
            edit = @Edit(title = "设备Id")
    )
    private String equipmentInfoId;

    @FabosJsonField(
            views = @View(title = "设备标识"),
            edit = @Edit(title = "设备标识")
    )
    private String equipmentSign;

    @FabosJsonField(
            views = @View(title = "设备名称"),
            edit = @Edit(title = "设备名称")
    )
    private String equipmentName;

    @FabosJsonField(
            views = @View(title = "数值类型"),
            edit = @Edit(title = "数值类型")
    )
    private String dataType;

    @FabosJsonField(
            views = @View(title = "能源Id"),
            edit = @Edit(title = "能源Id")
    )
    private String energyId;

    @FabosJsonField(
            views = @View(title = "能源名称"),
            edit = @Edit(title = "能源名称")
    )
    private String energyName;

    @FabosJsonField(
            views = @View(title = "能源单位Id"),
            edit = @Edit(title = "能源单位Id")
    )
    private String energyUnitId;

    @FabosJsonField(
            views = @View(title = "能源单位中文名称"),
            edit = @Edit(title = "能源单位中文名称")
    )
    private String unitChnName;

    @FabosJsonField(
            views = @View(title = "能源单位英文名称"),
            edit = @Edit(title = "能源单位英文名称")
    )
    private String unitEngName;

    @FabosJsonField(
            views = @View(title = "能源类型名称"),
            edit = @Edit(title = "能源类型名称")
    )
    private String energyTypeName;

    @FabosJsonField(
            views = @View(title = "抄表数值或采集的数值"),
            edit = @Edit(title = "抄表数值或采集的数值")
    )
    private BigDecimal meterReadingValue;

    @FabosJsonField(
            views = @View(title = "计算后的能源消耗数值"),
            edit = @Edit(title = "计算后的能源消耗数值")
    )
    private BigDecimal usedValue;
}
