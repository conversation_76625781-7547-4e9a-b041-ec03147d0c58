package cec.jiutian.bc.statistics.repository;

import cec.jiutian.bc.basicData.domain.locationManagement.model.LocationManagement;
import cec.jiutian.bc.statistics.dto.DayEnergyConsumptionDTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface OrganizationEnergyConsumptionRepository extends JpaRepository<LocationManagement,String> {
    @Query(value = "SELECT " +
            "  v.name as organization, " +
            "  T.energy_name as energyName, " +
            "  to_char(r.data_ts, 'YYYY-MM-DD') as dateTime, " +
            "  coalesce(sum(r.used_value),0) " +
            "FROM " +
            "  ems_location_management " +
            "  M INNER JOIN factory_info_view v ON M.first_class_id = v. " +
            "  ID LEFT JOIN ems_used_record r ON r.param_code = M.parameter_flag " +
            "  LEFT JOIN ems_energy_type T ON r.energy_id = T.ID" +
            " where to_char(r.data_ts, 'YYYY-MM-DD') >= ?1 and to_char(r.data_ts, 'YYYY-MM-DD') <= ?2 " +
            " and( ?3 is null or v.name like %?3% )" +
            "GROUP BY v.NAME,T.energy_name,to_char(r.data_ts, 'YYYY-MM-DD')",nativeQuery = true)
    List<Object[]> getFirstDayEnergyConsumptionList(String startTime, String endTime,String organization);

    @Query(value = "SELECT " +
            "  v.name as organization, " +
            "  T.energy_name as energyName, " +
            "  to_char(r.data_ts, 'YYYY-MM-DD') as dateTime, " +
            "  coalesce(sum(r.used_value),0) " +
            "FROM " +
            "  ems_location_management " +
            "  M INNER JOIN factory_info_view v ON M.second_class_id = v. " +
            "  ID LEFT JOIN ems_used_record r ON r.param_code = M.parameter_flag " +
            "  LEFT JOIN ems_energy_type T ON r.energy_id = T.ID" +
            " where to_char(r.data_ts, 'YYYY-MM-DD') >= ?1 and to_char(r.data_ts, 'YYYY-MM-DD') <= ?2 " +
            " and( ?3 is null or v.name like %?3% )" +
            "GROUP BY v.NAME,T.energy_name,to_char(r.data_ts, 'YYYY-MM-DD')",nativeQuery = true)
    List<Object[]> getSecondDayEnergyConsumptionList(String startTime, String endTime,String organization);


    @Query(value = "SELECT " +
            "  v.name as organization, " +
            "  T.energy_name as energyName, " +
            "  to_char(r.data_ts, 'YYYY-MM-DD') as dateTime, " +
            "  coalesce(sum(r.used_value),0) " +
            "FROM " +
            "  ems_location_management " +
            "  M INNER JOIN factory_info_view v ON M.third_class_id = v. " +
            "  ID LEFT JOIN ems_used_record r ON r.param_code = M.parameter_flag " +
            "  LEFT JOIN ems_energy_type T ON r.energy_id = T.ID" +
            " where to_char(r.data_ts, 'YYYY-MM-DD') >= ?1 and to_char(r.data_ts, 'YYYY-MM-DD') <= ?2 " +
            " and( ?3 is null or v.name like %?3% )" +
            "GROUP BY v.NAME,T.energy_name,to_char(r.data_ts, 'YYYY-MM-DD')",nativeQuery = true)
    List<Object[]> getThirdDayEnergyConsumptionList(String startTime, String endTime,String organization);



    @Query(value = "SELECT " +
            "  v.name as organization, " +
            "  T.energy_name as energyName, " +
            "  to_char(r.data_ts, 'YYYY-MM') as dateTime, " +
            "  coalesce(sum(r.used_value),0) " +
            "FROM " +
            "  ems_location_management " +
            "  M INNER JOIN factory_info_view v ON M.first_class_id = v. " +
            "  ID LEFT JOIN ems_used_record r ON r.param_code = M.parameter_flag " +
            "  LEFT JOIN ems_energy_type T ON r.energy_id = T.ID" +
            " where to_char(r.data_ts, 'YYYY-MM') >= ?1 and to_char(r.data_ts, 'YYYY-MM') <= ?2 " +
            " and( ?3 is null or v.name like %?3% )" +
            "GROUP BY v.NAME,T.energy_name,to_char(r.data_ts, 'YYYY-MM')",nativeQuery = true)
    List<Object[]> getFirstMonthEnergyConsumptionList(String startTime, String endTime,String organization);

    @Query(value = "SELECT " +
            "  v.name as organization, " +
            "  T.energy_name as energyName, " +
            "  to_char(r.data_ts, 'YYYY-MM') as dateTime, " +
            "  coalesce(sum(r.used_value),0) " +
            "FROM " +
            "  ems_location_management " +
            "  M INNER JOIN factory_info_view v ON M.second_class_id = v. " +
            "  ID LEFT JOIN ems_used_record r ON r.param_code = M.parameter_flag " +
            "  LEFT JOIN ems_energy_type T ON r.energy_id = T.ID" +
            " where to_char(r.data_ts, 'YYYY-MM') >= ?1 and to_char(r.data_ts, 'YYYY-MM') <= ?2 " +
            " and( ?3 is null or v.name like %?3% )" +
            "GROUP BY v.NAME,T.energy_name,to_char(r.data_ts, 'YYYY-MM')",nativeQuery = true)
    List<Object[]> getSecondMonthEnergyConsumptionList(String startTime, String endTime,String organization);


    @Query(value = "SELECT " +
            "  v.name as organization, " +
            "  T.energy_name as energyName, " +
            "  to_char(r.data_ts, 'YYYY-MM') as dateTime, " +
            "  coalesce(sum(r.used_value),0) " +
            "FROM " +
            "  ems_location_management " +
            "  M INNER JOIN factory_info_view v ON M.third_class_id = v. " +
            "  ID LEFT JOIN ems_used_record r ON r.param_code = M.parameter_flag " +
            "  LEFT JOIN ems_energy_type T ON r.energy_id = T.ID" +
            " where to_char(r.data_ts, 'YYYY-MM') >= ?1 and to_char(r.data_ts, 'YYYY-MM') <= ?2 " +
            " and( ?3 is null or v.name like %?3% )" +
            "GROUP BY v.NAME,T.energy_name,to_char(r.data_ts, 'YYYY-MM')",nativeQuery = true)
    List<Object[]> getThirdMonthEnergyConsumptionList(String startTime, String endTime,String organization);

    @Query(value = "SELECT " +
            "  v.name as organization, " +
            "  T.energy_name as energyName, " +
            "  to_char(r.data_ts, 'YYYY') as dateTime, " +
            "  coalesce(sum(r.used_value),0) " +
            "FROM " +
            "  ems_location_management " +
            "  M INNER JOIN factory_info_view v ON M.first_class_id = v. " +
            "  ID LEFT JOIN ems_used_record r ON r.param_code = M.parameter_flag " +
            "  LEFT JOIN ems_energy_type T ON r.energy_id = T.ID" +
            " where to_char(r.data_ts, 'YYYY') >= ?1 and to_char(r.data_ts, 'YYYY') <= ?2 " +
            " and( ?3 is null or v.name like %?3% )" +
            "GROUP BY v.NAME,T.energy_name,to_char(r.data_ts, 'YYYY')",nativeQuery = true)
    List<Object[]> getFirstYearEnergyConsumptionList(String startTime, String endTime,String organization);

    @Query(value = "SELECT " +
            "  v.name as organization, " +
            "  T.energy_name as energyName, " +
            "  to_char(r.data_ts, 'YYYY') as dateTime, " +
            "  coalesce(sum(r.used_value),0) " +
            "FROM " +
            "  ems_location_management " +
            "  M INNER JOIN factory_info_view v ON M.second_class_id = v. " +
            "  ID LEFT JOIN ems_used_record r ON r.param_code = M.parameter_flag " +
            "  LEFT JOIN ems_energy_type T ON r.energy_id = T.ID" +
            " where to_char(r.data_ts, 'YYYY') >= ?1 and to_char(r.data_ts, 'YYYY') <= ?2 " +
            " and( ?3 is null or v.name like %?3% )" +
            "GROUP BY v.NAME,T.energy_name,to_char(r.data_ts, 'YYYY')",nativeQuery = true)
    List<Object[]> getSecondYearEnergyConsumptionList(String startTime, String endTime,String organization);


    @Query(value = "SELECT " +
            "  v.name as organization, " +
            "  T.energy_name as energyName, " +
            "  to_char(r.data_ts, 'YYYY') as dateTime, " +
            "  coalesce(sum(r.used_value),0) " +
            "FROM " +
            "  ems_location_management " +
            "  M INNER JOIN factory_info_view v ON M.third_class_id = v. " +
            "  ID LEFT JOIN ems_used_record r ON r.param_code = M.parameter_flag " +
            "  LEFT JOIN ems_energy_type T ON r.energy_id = T.ID" +
            " where to_char(r.data_ts, 'YYYY') >= ?1 and to_char(r.data_ts, 'YYYY') <= ?2 " +
            " and( ?3 is null or v.name like %?3% )" +
            "GROUP BY v.NAME,T.energy_name,to_char(r.data_ts, 'YYYY')",nativeQuery = true)
    List<Object[]> getThirdYearEnergyConsumptionList(String startTime, String endTime,String organization);
}
