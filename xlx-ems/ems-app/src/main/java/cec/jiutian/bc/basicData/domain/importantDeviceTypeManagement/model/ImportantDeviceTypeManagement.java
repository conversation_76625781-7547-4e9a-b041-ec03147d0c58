package cec.jiutian.bc.basicData.domain.importantDeviceTypeManagement.model;

import cec.jiutian.bc.basicData.domain.importantDeviceTypeManagement.handler.ImportantDeviceTypeManagementMultiDeleteHandler;
import cec.jiutian.bc.basicData.domain.importantDeviceTypeManagement.proxy.ImportantDeviceTypeManagementProxy;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowOperation;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025年03月18日 9:12
 */
@Table(name = "ems_important_device_type",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"id"}),
                @UniqueConstraint(columnNames = {"typeName"})
        }
)
@FabosJson(
        name = "重点设备分类管理",
        orderBy = "ImportantDeviceTypeManagement.createTime desc",
        dataProxy = ImportantDeviceTypeManagementProxy.class,
        power = @Power(add = true, importable = false, export = false, print = false, examine = false, examineDetails = false),
        rowOperation = {
                @RowOperation(
                        title = "批量删除",
                        code = "ImportantDeviceTypeManagement@MULTIDELETE",
                        mode = RowOperation.Mode.MULTI,
                        operationHandler = ImportantDeviceTypeManagementMultiDeleteHandler.class,
                        callHint = "是否确定操作？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "ImportantDeviceTypeManagement@MULTIDELETE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                )
        }
)
@Entity
@TemplateType(type = "multiTable")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ImportantDeviceTypeManagement extends MetaModel {

    @FabosJsonField(
            views = @View(title = "分类名称"),
            edit = @Edit(title = "分类名称", search = @Search(vague = true),
                    readonly = @Readonly(add = false, edit = false), notNull = true,
                    inputType = @InputType(length = 60))
    )
    private String typeName;

    @FabosJsonField(
            views = @View(title = "备注"),
            edit = @Edit(title = "备注", type = EditType.TEXTAREA)
    )
    private String remark;
}
