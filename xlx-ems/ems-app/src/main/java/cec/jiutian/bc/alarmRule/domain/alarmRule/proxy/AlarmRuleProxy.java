package cec.jiutian.bc.alarmRule.domain.alarmRule.proxy;

import cec.jiutian.bc.alarmRule.domain.alarmRule.model.AlarmRule;
import cec.jiutian.bc.alarmRule.enumeration.AlarmCountMethodEnum;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.view.fun.DataProxy;
import org.springframework.stereotype.Component;

@Component
public class AlarmRuleProxy implements DataProxy<AlarmRule> {
    @Override
    public void beforeAdd(AlarmRule alarmRule) {
        alarmRule.setEnableFlag("N");
        if (alarmRule.getCalculationMethod().equals(AlarmCountMethodEnum.Enum.OverHighLimit.name())) {
            alarmRule.setConfigLowValue(null);
        } else if (alarmRule.getCalculationMethod().equals(AlarmCountMethodEnum.Enum.OverLowLimit.name())) {
            alarmRule.setConfigHighValue(null);
        }
        else if (alarmRule.getCalculationMethod().equals(AlarmCountMethodEnum.Enum.IntervalCalculation.name())) {
            if (alarmRule.getConfigLowValue().compareTo(alarmRule.getConfigHighValue()) >= 0) {
                throw new FabosJsonApiErrorTip("上限需大于下限");
            }
        }
    }

    @Override
    public void beforeUpdate(AlarmRule alarmRule) {
        alarmRule.setEnableFlag("N");
        if (alarmRule.getCalculationMethod().equals(AlarmCountMethodEnum.Enum.OverHighLimit.name())) {
            alarmRule.setConfigLowValue(null);
        } else if (alarmRule.getCalculationMethod().equals(AlarmCountMethodEnum.Enum.OverLowLimit.name())) {
            alarmRule.setConfigHighValue(null);
        }
        else if (alarmRule.getCalculationMethod().equals(AlarmCountMethodEnum.Enum.IntervalCalculation.name())) {
            if (alarmRule.getConfigLowValue().compareTo(alarmRule.getConfigHighValue()) >= 0) {
                throw new FabosJsonApiErrorTip("上限需大于下限");
            }
        }
    }
}
