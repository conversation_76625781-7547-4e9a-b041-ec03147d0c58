package cec.jiutian.bc.basicData.domain.manualMeterReadingUnitManagement.handler;

import cec.jiutian.bc.basicData.domain.manualMeterReadingUnitManagement.model.ManualMeterReadingUnitManagement;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import java.util.HashMap;
import java.util.Map;

@Component
public class ManualMeterReadingUnitManagementDeviceInfoSelectHandler implements DependFiled.DynamicHandler<ManualMeterReadingUnitManagement> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public Map<String, Object> handle(ManualMeterReadingUnitManagement manualMeterReadingUnitManagement) {
        Map<String, Object> map = new HashMap<>();
        if(manualMeterReadingUnitManagement.getDeviceInfo() == null) {
            throw new FabosJsonApiErrorTip("请选择设备信息!");
        }
        map.put("deviceName", manualMeterReadingUnitManagement.getDeviceInfo().getName());
        return map;
    }
}