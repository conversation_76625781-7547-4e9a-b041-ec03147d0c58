package cec.jiutian.bc.basicData.domain.manualMeterReadingManagement.proxy;

import cec.jiutian.bc.basicData.domain.energyTypeManagement.model.EnergyTypeManagement;
import cec.jiutian.bc.basicData.domain.manualMeterReadingManagement.model.ManualMeterReadingManagement;
import cec.jiutian.bc.basicData.domain.manualMeterReadingUnitManagement.model.ManualMeterReadingUnitManagement;
import cec.jiutian.bc.basicData.enumeration.DataTypeEnum;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import jakarta.persistence.TypedQuery;
import jakarta.transaction.Transactional;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import java.math.BigDecimal;
import java.util.Calendar;
import java.util.List;

/*
 *
 * <AUTHOR>
 * @date 2025/3/20 10:18
 */
@Component
@Transactional
public class ManualMeterReadingManagementProxy implements DataProxy<ManualMeterReadingManagement> {

    @Resource
    FabosJsonDao fabosJsonDao;

    @Override
    public void beforeAdd(ManualMeterReadingManagement manualMeterReadingManagement) {
        if(manualMeterReadingManagement != null) {
            if(DataTypeEnum.Enum.AccumulationValue.name().equals(manualMeterReadingManagement.getMeterReadingUnitName().getDataType())) {
                String hql = "SELECT l FROM ManualMeterReadingManagement l " +
                        "WHERE l.meterReadingUnitName = :givenMeterReadingUnitName " +
                        " AND l.energyTypeManagement = :givenEnergyTypeManagement " +
                        " order by l.meterReadingDate desc limit 1";
                TypedQuery<ManualMeterReadingManagement> query = fabosJsonDao.getEntityManager().createQuery(hql, ManualMeterReadingManagement.class);
                query.setParameter("givenMeterReadingUnitName", manualMeterReadingManagement.getMeterReadingUnitName());
                query.setParameter("givenEnergyTypeManagement", manualMeterReadingManagement.getEnergyTypeManagement());
                List<ManualMeterReadingManagement> manualMeterReadingManagementList = query.getResultList();
                if(CollectionUtils.isNotEmpty(manualMeterReadingManagementList)) {
                    ManualMeterReadingManagement tmp = manualMeterReadingManagementList.get(0);
                    if(tmp.getMeterReadingDate() != null && manualMeterReadingManagement.getMeterReadingDate().before(tmp.getMeterReadingDate())) {
                        throw new FabosJsonApiErrorTip("抄表时间不能在最近一次抄表时间之前, 请检查!");
                    }
                    if(tmp.getMeterReadingValue() != null && tmp.getMeterReadingValue().doubleValue() > manualMeterReadingManagement.getMeterReadingValue().doubleValue()) {
                        throw new FabosJsonApiErrorTip("抄表数值不能小于最近一次的抄表数值, 请检查!");
                    }
                    manualMeterReadingManagement.setUsedValue(manualMeterReadingManagement.getMeterReadingValue().subtract(tmp.getMeterReadingValue()));
                } else {
                    manualMeterReadingManagement.setUsedValue(manualMeterReadingManagement.getMeterReadingValue());
                }
            } else {
                manualMeterReadingManagement.setUsedValue(BigDecimal.ZERO);
            }
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(manualMeterReadingManagement.getMeterReadingDate());

            manualMeterReadingManagement.setYear(calendar.get(Calendar.YEAR));
            manualMeterReadingManagement.setMonth(calendar.get(Calendar.MONTH) + 1);
            manualMeterReadingManagement.setDay(calendar.get(Calendar.DAY_OF_MONTH));
            manualMeterReadingManagement.setHour(calendar.get(Calendar.HOUR_OF_DAY));
            manualMeterReadingManagement.setWorkYear(calendar.get(Calendar.YEAR));
            manualMeterReadingManagement.setWorkMonth(calendar.get(Calendar.MONTH));
            if(manualMeterReadingManagement.getEnergyTypeManagement() != null) {
                EnergyTypeManagement energyTypeManagement = fabosJsonDao.findById(EnergyTypeManagement.class, manualMeterReadingManagement.getEnergyTypeManagement().getId());
                manualMeterReadingManagement.setEnergyUnit(energyTypeManagement == null ? null : energyTypeManagement.getEnergyUnit());
            } else {
                manualMeterReadingManagement.setEnergyUnit(null);
            }
            if(manualMeterReadingManagement.getMeterReadingUnitName() != null && StringUtils.isNotBlank(manualMeterReadingManagement.getMeterReadingUnitName().getId())) {
                ManualMeterReadingUnitManagement manualMeterReadingUnitManagement = fabosJsonDao.findById(ManualMeterReadingUnitManagement.class, manualMeterReadingManagement.getMeterReadingUnitName().getId());
                if(manualMeterReadingUnitManagement != null) {
                    manualMeterReadingManagement.setLocationName(manualMeterReadingUnitManagement.getLocationName());
                }
            }
        }
    }

    @Override
    public void beforeUpdate(ManualMeterReadingManagement manualMeterReadingManagement) {
        if(manualMeterReadingManagement != null) {
            ManualMeterReadingUnitManagement condition = new ManualMeterReadingUnitManagement();
            condition.setId(manualMeterReadingManagement.getMeterReadingUnitName().getId());
            ManualMeterReadingUnitManagement manualMeterReadingUnitManagement = fabosJsonDao.selectOne(condition);
            if(manualMeterReadingUnitManagement != null && DataTypeEnum.Enum.AccumulationValue.name().equals(manualMeterReadingUnitManagement.getDataType())) {
                checkManualMeterReadingManagement(manualMeterReadingManagement, true);
                String hql = "SELECT l FROM ManualMeterReadingManagement l " +
                        "WHERE l.meterReadingUnitName = :givenMeterReadingUnitName " +
                        " AND l.energyTypeManagement = :givenEnergyTypeManagement " +
                        " AND l.id != :givenId" +
                        " order by l.meterReadingDate desc limit 1";
                TypedQuery<ManualMeterReadingManagement> query = fabosJsonDao.getEntityManager().createQuery(hql, ManualMeterReadingManagement.class);
                query.setParameter("givenMeterReadingUnitName", manualMeterReadingManagement.getMeterReadingUnitName());
                query.setParameter("givenEnergyTypeManagement", manualMeterReadingManagement.getEnergyTypeManagement());
                query.setParameter("givenId", manualMeterReadingManagement.getId());
                List<ManualMeterReadingManagement> manualMeterReadingManagementList = query.getResultList();
                if(CollectionUtils.isNotEmpty(manualMeterReadingManagementList)) {
                    ManualMeterReadingManagement tmp = manualMeterReadingManagementList.get(0);
                    if(tmp.getMeterReadingDate() != null && manualMeterReadingManagement.getMeterReadingDate().before(tmp.getMeterReadingDate())) {
                        throw new FabosJsonApiErrorTip("抄表时间不能在最近一次抄表时间之前, 请检查!");
                    }
                    if(tmp.getMeterReadingValue() != null && tmp.getMeterReadingValue().doubleValue() > manualMeterReadingManagement.getMeterReadingValue().doubleValue()) {
                        throw new FabosJsonApiErrorTip("抄表数值不能小于最近一次的抄表数值, 请检查!");
                    }
                    manualMeterReadingManagement.setUsedValue(manualMeterReadingManagement.getMeterReadingValue().subtract(tmp.getMeterReadingValue()));
                } else {
                    manualMeterReadingManagement.setUsedValue(manualMeterReadingManagement.getMeterReadingValue());
                }
            } else {
                manualMeterReadingManagement.setUsedValue(BigDecimal.ZERO);
            }
            if(manualMeterReadingManagement.getEnergyTypeManagement() != null) {
                EnergyTypeManagement energyTypeManagement = fabosJsonDao.findById(EnergyTypeManagement.class, manualMeterReadingManagement.getEnergyTypeManagement().getId());
                manualMeterReadingManagement.setEnergyUnit(energyTypeManagement == null ? null : energyTypeManagement.getEnergyUnit());
            } else {
                manualMeterReadingManagement.setEnergyUnit(null);
            }
            if(manualMeterReadingUnitManagement != null) {
                manualMeterReadingManagement.setLocationName(manualMeterReadingUnitManagement.getLocationName());
            }
        }
    }

    @Override
    public void beforeDelete(ManualMeterReadingManagement manualMeterReadingManagement) {
        checkManualMeterReadingManagement(manualMeterReadingManagement, false);
    }

    public void checkManualMeterReadingManagement(ManualMeterReadingManagement manualMeterReadingManagement, boolean edit) {
        if(manualMeterReadingManagement != null) {
            ManualMeterReadingUnitManagement condition = new ManualMeterReadingUnitManagement();
            condition.setId(manualMeterReadingManagement.getMeterReadingUnitName().getId());
            ManualMeterReadingUnitManagement manualMeterReadingUnitManagement = fabosJsonDao.selectOne(condition);
            if(manualMeterReadingUnitManagement != null && DataTypeEnum.Enum.AccumulationValue.name().equals(manualMeterReadingUnitManagement.getDataType())) {
                String hql = "SELECT l FROM ManualMeterReadingManagement l " +
                        "WHERE l.meterReadingUnitName = :givenMeterReadingUnitName " +
                        " AND l.energyTypeManagement = :givenEnergyTypeManagement " +
                        " AND l.meterReadingDate >= :giveMeterReadingDate" +
                        " AND l.id != :givenId " +
                        " order by l.meterReadingDate desc";
                TypedQuery<ManualMeterReadingManagement> query = fabosJsonDao.getEntityManager().createQuery(hql, ManualMeterReadingManagement.class);
                query.setParameter("givenMeterReadingUnitName", manualMeterReadingManagement.getMeterReadingUnitName());
                query.setParameter("givenEnergyTypeManagement", manualMeterReadingManagement.getEnergyTypeManagement());
                query.setParameter("giveMeterReadingDate", manualMeterReadingManagement.getMeterReadingDate());
                query.setParameter("givenId", manualMeterReadingManagement.getId());
                List<ManualMeterReadingManagement> manualMeterReadingManagementList = query.getResultList();
                if(CollectionUtils.isNotEmpty(manualMeterReadingManagementList)) {
                    if(edit) {
                        throw new FabosJsonApiErrorTip("不能编辑非最新的抄表记录, 请检查!");
                    }
                    throw new FabosJsonApiErrorTip("不能删除非最新的抄表记录, 请检查!");
                }
            }
        }
    }
}
