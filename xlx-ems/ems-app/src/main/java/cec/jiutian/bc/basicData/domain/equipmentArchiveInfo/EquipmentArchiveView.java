package cec.jiutian.bc.basicData.domain.equipmentArchiveInfo;

import cec.jiutian.bc.basicData.enumeration.EquipmentBusinessStateEnum;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;


/* 依赖的视图创建语句
CREATE
	OR REPLACE VIEW equipment_archive_info_view AS
select
	eeea.id,
	eeea.general_code,
	eeea.equipment_sign,
	eeea.specification,
	eeea.serial_code,
	eeea.asset_code,
	eeea.business_state,
	eeei.code,
	eeei.name
from
	eam_ea_equipment_archive eeea
left join eam_ea_equipment_info eeei on
	eeei.id = eeea.equipment_id;
 */
@FabosJson(
        name = "设备台账信息模型视图",
        power = @Power(add = false, edit = false, delete = false, importable = false, export = false)
)
@Table(name = "equipment_archive_info_view"
)
@Entity
@Getter
@Setter
public class EquipmentArchiveView {

    @Id
    @FabosJsonField(
            views = @View(title = "id", show = false),
            edit = @Edit(title = "id", show = false)
    )
    private String id;

    @FabosJsonField(
            views = @View(title = "设备台账编号"),
            edit = @Edit(title = "设备台账编号")
    )
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "设备标识"),
            edit = @Edit(title = "设备标识")
    )
    private String equipmentSign;

    @FabosJsonField(
            views = @View(title = "设备型号"),
            edit = @Edit(title = "设备型号")
    )
    private String specification;

    @FabosJsonField(
            views = @View(title = "出厂编号"),
            edit = @Edit(title = "出厂编号")
    )
    private String serialCode;

    @FabosJsonField(
            views = @View(title = "设备资产编号"),
            edit = @Edit(title = "设备资产编号")
    )
    private String assetCode;

    @FabosJsonField(
            views = @View(title = "设备状态"),
            edit = @Edit(title = "设备状态", show = false, type = EditType.CHOICE,search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = EquipmentBusinessStateEnum.class))
    )
    private String businessState;

    @FabosJsonField(
            views = @View(title = "设备编码"),
            edit = @Edit(title = "物资编码",
                    search = @Search(vague = true)
            )
    )
    private String code;

    @FabosJsonField(
            views = @View(title = "设备名称"),
            edit = @Edit(title = "设备名称",
                    search = @Search(vague = true)
            )
    )
    private String name;
}
