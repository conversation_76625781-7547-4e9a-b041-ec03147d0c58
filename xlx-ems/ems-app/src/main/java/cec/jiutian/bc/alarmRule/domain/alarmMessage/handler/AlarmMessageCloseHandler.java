package cec.jiutian.bc.alarmRule.domain.alarmMessage.handler;

import cec.jiutian.bc.alarmRule.domain.alarmMessage.model.AlarmMessage;
import cec.jiutian.bc.alarmRule.enumeration.AlarmMessageStateEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class AlarmMessageCloseHandler implements OperationHandler<AlarmMessage,Void> {
    private final FabosJsonDao fabosJsonDao;

    public AlarmMessageCloseHandler(FabosJsonDao fabosJsonDao) {
        this.fabosJsonDao = fabosJsonDao;
    }

    @Override
    public String exec(List<AlarmMessage> alarmMessageList, Void modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(alarmMessageList)) {
            AlarmMessage alarmMessage = alarmMessageList.get(0);
            alarmMessage.setAlarmStatus(AlarmMessageStateEnum.Enum.CLOSE.name());
            fabosJsonDao.update(alarmMessage);
        }
        return "msg.success('操作成功')";
    }
}
