package cec.jiutian.bc.basicData.domain.manualMeterReadingUnitManagement.handler;

import cec.jiutian.bc.basicData.domain.factory.model.Factory;
import cec.jiutian.bc.basicData.domain.manualMeterReadingUnitManagement.model.ManualMeterReadingUnitManagement;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import java.util.HashMap;
import java.util.Map;

@Component
public class ManualMeterReadingUnitManagementFactorySelectHandler implements DependFiled.DynamicHandler<ManualMeterReadingUnitManagement> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public Map<String, Object> handle(ManualMeterReadingUnitManagement manualMeterReadingUnitManagement) {
        Map<String, Object> map = new HashMap<>();
        if(manualMeterReadingUnitManagement.getFactory() == null || StringUtils.isBlank(manualMeterReadingUnitManagement.getFactory().getId())) {
            Factory factory = new Factory();
            map.put("firstClass", factory);
            map.put("secondClass", factory);
            map.put("thirdClass", factory);
            map.put("forthClass", factory);
            return map;
        }
        Factory factory = fabosJsonDao.findById(Factory.class, manualMeterReadingUnitManagement.getFactory().getId());
        if(factory == null) {
            throw new FabosJsonApiErrorTip("选择的位置信息不存在, 请检查!");
        }
        manualMeterReadingUnitManagement.setFactory(factory);
        if(factory.getFactoryLevel() > 4) {
            throw new FabosJsonApiErrorTip("选择的位置信息层级不能超过四级, 请检查!");
        }
        setFactoryClass(map, manualMeterReadingUnitManagement.getFactory());
        return map;
    }

    private void setFactoryClass(Map<String, Object> map, Factory factory) {
        if(factory == null) {
            return;
        }
        if(factory.getFactoryLevel() == 4) {
            map.put("forthClass", factory);
            setFactoryClass(map, fabosJsonDao.findById(Factory.class, factory.getParentFactory().getId()));
        }
        if(factory.getFactoryLevel() == 3) {
            map.put("thirdClass", factory);
            setFactoryClass(map, fabosJsonDao.findById(Factory.class, factory.getParentFactory().getId()));
        }
        if(factory.getFactoryLevel() == 2) {
            map.put("secondClass", factory);
            setFactoryClass(map, fabosJsonDao.findById(Factory.class, factory.getParentFactory().getId()));
        }
        if(factory.getFactoryLevel() == 1) {
            map.put("firstClass", factory);
        }
    }
}