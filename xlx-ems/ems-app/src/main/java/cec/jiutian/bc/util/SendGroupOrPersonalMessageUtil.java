package cec.jiutian.bc.util;

import cec.jiutian.bc.ecs.dto.SendMsgGroupDTO;
import cec.jiutian.bc.ecs.dto.SendMsgToPersonDTO;
import cec.jiutian.bc.ecs.enums.MessageWayEnum;
import cec.jiutian.bc.ecs.provider.EcsMessageProvider;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025年06月03日 11:02
 */
@Component
@Slf4j
public class SendGroupOrPersonalMessageUtil {

    @Resource
    private EcsMessageProvider ecsMessageProvider;

    /**
     * 发送预警消息
     * @param messageGroupCode 消息组编码：根据已创建的消息组配置
     * @param content 消息内容
     * @param params   消息参数：当该消息包含需处理的链接时，传入消息参数，
     *                 消息参数为字符串类型的json键值对格式，ecs会将参数拼接到表单跳转URL中
     */
    public void sendGroupMessage(String messageGroupCode, String content, String params) {
        if(StringUtils.isBlank(messageGroupCode) || StringUtils.isBlank(content)) {
            return;
        }
        SendMsgGroupDTO sendMsgGroupDTO = new SendMsgGroupDTO();
        sendMsgGroupDTO.setMessageGroupCode(messageGroupCode);
        sendMsgGroupDTO.setContent(content);
        sendMsgGroupDTO.setParameters(params);
        log.info(String.format("发送预警消息, 消息组编码: %s, 消息内容: %s, 消息参数: %s", messageGroupCode, content, params));
        ecsMessageProvider.sendGroupMessage(sendMsgGroupDTO);
    }

    /**
     * 发送个人预警消息
     * @param title 标题
     * @param content 消息内容
     * @param sendBy 发送人
     * @param way 发送方式： Email 邮件; WECHAT 微信; App 站内信
     * @param receivers 接收人集合 电话号码 中间用逗号隔开
     */
    public void sendPersonMessage(String title, String content, String sendBy, MessageWayEnum way,
                                  Set<String> receivers) {
        if(CollectionUtils.isEmpty(receivers) || StringUtils.isBlank(content) || way == null) {
            return;
        }
        SendMsgToPersonDTO sendMsgToPersonDTO = new SendMsgToPersonDTO();
        sendMsgToPersonDTO.setTitle(title);
        sendMsgToPersonDTO.setContent(content);
        sendMsgToPersonDTO.setSendBy(sendBy);
        sendMsgToPersonDTO.setWay(way);
        sendMsgToPersonDTO.setReceivers(receivers);
        log.info(String.format("发送个人预警消息, 标题: %s, 消息内容: %s, 发送人: %s, 发送方式: %s, 接收人集合: %s", title, content, sendBy, way == null ? "" : way.name(), CollectionUtils.isEmpty(receivers) ? "" : String.join(",", receivers)));
        ecsMessageProvider.sendPersonMessage(sendMsgToPersonDTO);
    }

    /**
     * 发送个人预警消息
     * @param title 标题
     * @param content 消息内容
     * @param sendBy 发送人
     * @param way 发送方式： Email 邮件; WECHAT 微信; App 站内信
     * @param receiver 接收人
     */
    public void sendPersonMessage(String title, String content, String sendBy, MessageWayEnum way,
                                  String receiver) {
        if(StringUtils.isBlank(receiver) || StringUtils.isBlank(content) || way == null) {
            return;
        }
        SendMsgToPersonDTO sendMsgToPersonDTO = new SendMsgToPersonDTO();
        Set<String> user = new HashSet<>();
        user.add(receiver);
        sendMsgToPersonDTO.setTitle(title);
        sendMsgToPersonDTO.setContent(content);
        sendMsgToPersonDTO.setSendBy(sendBy);
        sendMsgToPersonDTO.setWay(way);
        sendMsgToPersonDTO.setReceivers(user);
        log.info(String.format("发送个人预警消息, 标题: %s, 消息内容: %s, 发送人: %s, 发送方式: %s, 接收人集合: %s", title, content, sendBy, way == null ? "" : way.name(), receiver));
        ecsMessageProvider.sendPersonMessage(sendMsgToPersonDTO);
    }
}
