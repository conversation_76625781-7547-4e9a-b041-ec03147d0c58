package cec.jiutian.functionexecutor.executors.remote;

import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.common.util.HttpServletRequestUtil;
import cec.jiutian.common.util.JacksonUtil;
import cec.jiutian.functionexecutor.dto.InvokeDTO;
import cec.jiutian.functionexecutor.executors.abstractExecutor.InvokeHandler;
import cec.jiutian.functionexecutor.rpcProcess.RpcExecutor;
import com.caucho.services.server.GenericService;
import jakarta.servlet.http.HttpServletRequest;
//import org.apache.dubbo.config.ReferenceConfig;
//import org.apache.dubbo.config.RegistryConfig;
//import org.apache.dubbo.rpc.RpcContext;
//import org.apache.dubbo.rpc.service.GenericException;
//import org.apache.dubbo.rpc.service.GenericService;

import java.io.IOException;
import java.util.Map;

public class RemoteExecutor extends InvokeHandler {

    @Override
    public Object doInvoke(InvokeDTO invokeDTO) {
//        Object invoke = null;
//        try {
//            HttpServletRequest request = HttpServletRequestUtil.getCurrentRequest();
//            RpcContext.getContext().setAttachment("token", request.getHeader("token"));
//            RpcContext.getContext().setAttachment("headers", HttpServletRequestUtil.convertHeadersToJson(request));
//            GenericService genericService = createGenericService(invokeDTO.getGroup(), false, 30000);
//            invoke = doInvoke(invokeDTO, genericService);
//        } catch (GenericException e) {
//            String message = e.getExceptionMessage();
//            throw new ServiceException(message);
//        }catch (Exception e){
//            throw new ServiceException(e);
//        }
        return null;
    }

    private static Object doInvoke(InvokeDTO invokeDTO, GenericService genericService) {
//        String classFullName = InvokeDTO.class.getName();
//        String[] argsType = new String[]{classFullName};
//        Object[] args = new Object[]{invokeDTO};
//        Object result = genericService.$invoke("doInvoke", argsType, args);
        return null;
    }

//    private GenericService createGenericService(String group, boolean async, Integer timeout) {
//        ReferenceConfig<GenericService> referenceConfig = new ReferenceConfig<>();
//        RegistryConfig registryConfig = this.context.getBean(RegistryConfig.class);
//        referenceConfig.setRegistry(registryConfig);
//        referenceConfig.setInterface(RpcExecutor.class);
////        referenceConfig.setVersion();
//        referenceConfig.setGroup(group);
//        referenceConfig.setAsync(async);
//        referenceConfig.setTimeout(timeout);
//        referenceConfig.setGeneric("true");
//
//        // 初始化 ReferenceConfig
//        GenericService genericService = referenceConfig.get();
//
//        return genericService;
//    }

    @Override
    public Object doNext(InvokeDTO invokeDTO) {
        return null;
    }
}
