<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>cec.jiutian</groupId>
        <artifactId>fabos-framework</artifactId>
        <version>3.2.2-SNAPSHOT</version>
    </parent>

    <artifactId>fabos-core</artifactId>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <dependencies>
<!--        <dependency>-->
<!--            <groupId>cec.jiutian</groupId>-->
<!--            <artifactId>fabos-annotation</artifactId>-->
<!--            <version>${project.version}</version>-->
<!--&lt;!&ndash;            <scope>compile</scope>&ndash;&gt;-->
<!--        </dependency>-->
        <dependency>
            <groupId>cec.jiutian</groupId>
            <artifactId>fabos-meta</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>${gson.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-ui</artifactId>
            <version>${springdoc-openapi-ui.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alipay.sofa.koupleless</groupId>
            <artifactId>koupleless-app-starter</artifactId>
            <version>${koupleless.runtime.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>io.minio</groupId>
            <artifactId>minio</artifactId>
            <version>${minio.version}</version>
        </dependency>
        <dependency>
            <groupId>com.lmax</groupId>
            <artifactId>disruptor</artifactId>
            <version>3.4.4</version>
        </dependency>
    </dependencies>

</project>