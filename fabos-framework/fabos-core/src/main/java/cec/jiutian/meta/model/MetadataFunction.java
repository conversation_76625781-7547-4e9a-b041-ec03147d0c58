package cec.jiutian.meta.model;

import cec.jiutian.meta.core.util.MD5Util;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @time 2024-09-24 17:11
 */


@Entity
@Table(name = "metadata_function")
@Getter
@Setter
public class MetadataFunction implements Serializable {

    @Id
    private String id;
    //归属组件（AK1）
//    private String component;
    //归属模型（AK1），特殊处理：如果model为空，则表示FabosJsonDataServiceDbImpl里面的方法
    @ManyToOne
//    @JoinColumn(referencedColumnName = "metadataId", name = "modelMetadataId")
    @JsonIgnoreProperties({"functions"})
    private MetadataModel referenceModel;
    //类中的方法名（AK1）
    private String methodName;
    //genericService.$invoke -> function
    //方法所在接口及完整路径编码(全限定名）
    private String methodCode;
    //方法所在接口及完整路径（包名）
    private String methodPackageName;
    //DubboReference.interfaceName
    //参数格式
    @Getter
    private String arguments;
    //genericService.$invoke ->{"java.lang.String","java.lang.Object"}
    //返回格式/类型
    private String returnType;
    //方法显示名
    private String displayName;
    //参数说明
    private String argumentsDescription;
    //返回值说明
    private String returnTypeDescription;
    //来源类型 系统生成/代码编写（区分是前端拖出来的还是后端的方法？
    private String sourceType;

    private String modelCode;

    private String description;

    public void generateAndSetMetadataId(){
        // it is unnecessary for setting code due to metadata id from reference model is already contains
        setId(MD5Util.digestAndFormat(referenceModel.getId() + methodName));
    }

    public void setArguments(Class<?>[] argumentsList) {
        if (argumentsList != null) {
            StringBuilder sb = new StringBuilder();
            for (Class<?> clazz : argumentsList) {
                sb.append(clazz.getName()).append(",");
            }
            // 去掉最后一个逗号
            if (sb.length() > 0) {
                sb.setLength(sb.length() - 1);
            }
            this.arguments = sb.toString();
        }
    }

    public List<Class<?>> getArgumentsListForRealClass() {
        List<Class<?>> result = new ArrayList<>();
        if (arguments != null) {
            String[] classNames = arguments.split(",");
            for (String className : classNames) {
                try {
                    result.add(Class.forName(className));
                } catch (ClassNotFoundException e) {
                    e.printStackTrace(); // 处理异常
                }
            }
        }
        return result;
    }
}
