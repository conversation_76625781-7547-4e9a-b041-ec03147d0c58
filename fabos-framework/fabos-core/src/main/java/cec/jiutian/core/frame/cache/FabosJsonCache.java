package cec.jiutian.core.frame.cache;

import java.util.Optional;
import java.util.function.Supplier;

/**
 */
public interface FabosJsonCache<V> {

    V put(String key, V v, long ttl);

    V get(String key);

    default V getAndSet(String key, long timeout, Supplier<V> supplier) {
        return Optional.ofNullable(this.get(key)).orElseGet(() -> this.put(key, supplier.get(), timeout));
    }

    void delete(String key);

}
