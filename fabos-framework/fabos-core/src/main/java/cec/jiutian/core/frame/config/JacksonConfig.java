package cec.jiutian.core.frame.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date ：2024/5/30 10:51
 * @description：
 */
@Configuration
public class JacksonConfig {
    //@Bean
//    public ObjectMapper objectMapper() {
//        ObjectMapper mapper = new ObjectMapper();
//        // 启用美化输出，方便查看JSON结构
//        mapper.enable(SerializationFeature.INDENT_OUTPUT);
//        return mapper;
//    }
    @Bean
    public ObjectMapper objectMapper() {
        ObjectMapper objectMapper=new ObjectMapper();
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        objectMapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.configure(SerializationFeature.FAIL_ON_SELF_REFERENCES, false);
        return objectMapper;
    }

}
