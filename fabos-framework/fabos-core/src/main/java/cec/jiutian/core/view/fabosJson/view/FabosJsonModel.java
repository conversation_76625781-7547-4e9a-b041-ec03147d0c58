package cec.jiutian.core.view.fabosJson.view;

import cec.jiutian.core.frame.enums.PageTemplateType;
import cec.jiutian.core.frame.invoke.DataProxyInvoke;
import cec.jiutian.core.frame.proxy.AnnotationProcess;
import cec.jiutian.core.frame.proxy.AnnotationProxy;
import cec.jiutian.core.frame.proxy.FabosJsonProxy;
import cec.jiutian.core.frame.proxy.ProxyContext;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.InheritStrategy;
import cec.jiutian.core.view.fabosJson.util.CloneSupport;
import cec.jiutian.view.FabosJsonI18n;
import cec.jiutian.view.OperationFixed;
import cec.jiutian.view.TemplateType;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date ：2024/5/23 14:24
 * @description：
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FabosJsonModel implements Cloneable{

    @JsonIgnore
    private transient Class<?> clazz;

    @JsonIgnore
    private transient FabosJson fabosJson;

    @JsonIgnore
    private transient InheritStrategy inheritStrategy;

    @JsonIgnore
    private transient AnnotationProxy<FabosJson, Void> fabosJsonAnnotationProxy = new FabosJsonProxy();

    private Map<String, FabosJsonFieldModel> fabosJsonFieldMap;

    private Map<String, FabosJsonFieldModel> subTableFieldMap;

    @JsonIgnore
    private transient boolean i18n;

    private String fabosJsonName;

    private String metadataId;

    private String templateType;

    private JSONObject fabosJsonObject;

    @JsonIgnore
    private transient HashSet<String> excludeParentFields;

    private List<FabosJsonFieldModel> fabosJsonFieldModels;

    //默认查询条件
    private Map<String, Object> searchCondition;

    private boolean extraRow = false;

    private boolean operationFixed = true;

    public FabosJsonModel(Class<?> fabosJsonClazz) {
        this.clazz = fabosJsonClazz;
        this.fabosJson = fabosJsonClazz.getAnnotation(FabosJson.class);
        this.fabosJson = fabosJsonAnnotationProxy.newProxy(this.getFabosJson());
        this.fabosJsonName = fabosJsonClazz.getSimpleName();
        this.templateType = fabosJsonClazz.getAnnotation(TemplateType.class) == null ? "usual" : fabosJsonClazz.getAnnotation(TemplateType.class).type();
        this.operationFixed = fabosJsonClazz.getAnnotation(OperationFixed.class) == null || fabosJsonClazz.getAnnotation(OperationFixed.class).fixed();
        this.i18n = null != clazz.getAnnotation(FabosJsonI18n.class);
        this.inheritStrategy = clazz.getAnnotation(InheritStrategy.class);
        this.excludeParentFields =  this.inheritStrategy == null ? new HashSet<>() : new HashSet<>(List.of(this.inheritStrategy.excludeParentFields()));
        DataProxyInvoke.invoke(this, it -> {
            try {
                it.getClass().getDeclaredMethod("extraRow", List.class);
                this.extraRow = true;
            } catch (NoSuchMethodException ignored) {
            }
        });
    }

    /**
     * FabosJsonModel的覆盖构造方法，主要覆盖了templateType参数
     *
     * @param fabosJsonClazz FabosJson类
     * @param templateType 模板类型
     */
    public FabosJsonModel(Class<?> fabosJsonClazz, PageTemplateType templateType) {
        this.clazz = fabosJsonClazz;
        this.fabosJson = fabosJsonClazz.getAnnotation(FabosJson.class);
        this.fabosJson = fabosJsonAnnotationProxy.newProxy(this.getFabosJson());
        this.fabosJsonName = fabosJsonClazz.getSimpleName();
        this.templateType = templateType.getValue();
        this.i18n = null != clazz.getAnnotation(FabosJsonI18n.class);
        DataProxyInvoke.invoke(this, it -> {
            try {
                it.getClass().getDeclaredMethod("extraRow", List.class);
                this.extraRow = true;
            } catch (NoSuchMethodException ignored) {
            }
        });
    }

    public FabosJson getFabosJson() {
        ProxyContext.set(clazz);
        return fabosJson;
    }

    @Override
    public FabosJsonModel clone() throws CloneNotSupportedException {
        FabosJsonModel fabosJsonModel = (FabosJsonModel) super.clone();
        fabosJsonModel.fabosJsonObject = AnnotationProcess.annotationToJsonByReflect(this.getFabosJson());
        fabosJsonModel.fabosJsonFieldModels = fabosJsonFieldModels.stream().map(CloneSupport::clone)
                .peek(FabosJsonFieldModel::serializable).collect(Collectors.toList());

        fabosJsonModel.fabosJsonFieldModels.forEach(f -> {
            if (fabosJsonModel.getSubTableFieldMap().containsKey(f.getFieldName())) {
                fabosJsonModel.getSubTableFieldMap().put(f.getFieldName(), f);
            }
        });
        return fabosJsonModel;
    }

}
