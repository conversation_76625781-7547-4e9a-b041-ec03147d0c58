package cec.jiutian.core.frame.constant;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.Getter;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Getter
public enum SecretLevelEnum {

    NONE(0, "非密"),
    LOW(1, "秘密"),
    MIDDLE(2, "机密"),
    HIGH(3, "绝密");

    private Integer level;
    private String desc;

    SecretLevelEnum(Integer level, String desc) {
        this.level = level;
        this.desc = desc;
    }


    public static class ChoiceFetch implements ChoiceFetchHandler {
        @Override
        public List<VLModel> fetch(String[] params) {
            return Stream.of(SecretLevelEnum.values()).map(secretLevelEnum ->
                    new VLModel(secretLevelEnum.getLevel()+"", secretLevelEnum.getDesc())).collect(Collectors.toList());
        }

    }

}
