package cec.jiutian.core.frame.enums;

import cec.jiutian.common.util.StringUtils;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @time 2024-10-11 10:27
 */

@Getter
public enum PageTemplateType {
    MULTI_TABLE("multiTable"),
    TREE_FORM("treeForm"),
    TREE_TABLE("treeTable"),
    USUAL("usual"),
    CUSTOMIZED("customized"),
    ;

    private final String value;

    PageTemplateType(String value) {
        this.value = value;
    }

    /**
     * 根据value获取枚举
     *
     * @param value  根据codeValue获取枚举
     * @return 解析出的枚举对象
     */
    public static PageTemplateType parseFromValue(String value) {
        for (PageTemplateType e : PageTemplateType.values()) {
            if (Objects.equals(e.value, value)) {
                return e;
            }
        }
        return null;
    }

    /**
     * 检查此值是否为枚举内的值
     *
     * @param value 传入需要检查的值
     * @return 如果为枚举内的值，返回true，否则返回false
     */
    public static boolean checkIfExistThisValue(String value) {
        if (StringUtils.isBlank(value)) {
            return false;
        }
        for (PageTemplateType e : PageTemplateType.values()) {
            if (Objects.equals(e.value, value)) {
                return true;
            }
        }
        return false;
    }


}
