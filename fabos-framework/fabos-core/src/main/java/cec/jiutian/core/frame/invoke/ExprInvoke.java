package cec.jiutian.core.frame.invoke;

import cec.jiutian.core.view.fabosJson.util.FabosJsonSpringUtil;
import cec.jiutian.view.expr.Expr;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.expr.ExprClass;
import cec.jiutian.view.expr.ExprFloat;
import cec.jiutian.view.expr.ExprInt;

/**
 */
public class ExprInvoke {

    public static String getExpr(Expr expr) {
        String value = expr.value();
        if (!expr.exprHandler().isInterface()) {
            value = FabosJsonSpringUtil.getBean(expr.exprHandler()).handler(value, expr.params());
        }
        return value;
    }

    public static Boolean getExpr(ExprBool expr) {
        boolean value = expr.value();
        if (!expr.exprHandler().isInterface()) {
            value = FabosJsonSpringUtil.getBean(expr.exprHandler()).handler(value, expr.params());
        }
        return value;
    }

    public static int getExpr(ExprInt expr) {
        int value = expr.value();
        if (!expr.exprHandler().isInterface()) {
            value = FabosJsonSpringUtil.getBean(expr.exprHandler()).handler(value, expr.params());
        }
        return value;
    }

    public static float getExpr(ExprFloat expr) {
        float value = expr.value();
        if (!expr.exprHandler().isInterface()) {
            value = FabosJsonSpringUtil.getBean(expr.exprHandler()).handler(value, expr.params());
        }
        return value;
    }

    public static Class<?> getExpr(ExprClass expr) {
        Class<?> value = expr.value();
        if (!expr.exprHandler().isInterface()) {
            value = FabosJsonSpringUtil.getBean(expr.exprHandler()).handler(value, expr.params());
        }
        return value;
    }


}
