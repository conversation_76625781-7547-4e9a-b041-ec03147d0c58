package cec.jiutian.core.frame.constant;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.Getter;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 */
@Getter
public enum MenuStatus {
    OPEN(1, "启用"),
    DISABLE(3, "禁用"),
    ;

    private final int value;
    private final String label;

    MenuStatus(int value, String label) {
        this.value = value;
        this.label = label;
    }

    public static class ChoiceFetch implements ChoiceFetchHandler {

        @Override
        public List<VLModel> fetch(String[] params) {
            return Stream.of(MenuStatus.values()).map(menuTypeEnum ->
                    new VLModel(menuTypeEnum.getValue() + "", menuTypeEnum.getLabel())).collect(Collectors.toList());
        }

    }
}
