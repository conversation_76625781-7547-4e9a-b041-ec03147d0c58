package cec.jiutian.core.frame.constant;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.Getter;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Getter
public enum MenuPageTypeEnum {

    MODEL("model", "模型页面", "填Model类名"),
    LOWCODE("lowCode", "低代码页面设计", "前端低代码页面设计"),
    STATIC("static", "静态页面", "静态页面设计"),
    ;
    private final String code;
    private final String name;
    private final String desc;

    MenuPageTypeEnum(String code, String name, String desc) {
        this.code = code;
        this.name = name;
        this.desc = desc;
    }

    public static class ChoiceFetch implements ChoiceFetchHandler {
        @Override
        public List<VLModel> fetch(String[] params) {
            return Stream.of(MenuPageTypeEnum.values()).map(menuPageTypeEnum ->
                    new VLModel(menuPageTypeEnum.getCode(), menuPageTypeEnum.getName(), menuPageTypeEnum.getName())).collect(Collectors.toList());
        }

    }

}
