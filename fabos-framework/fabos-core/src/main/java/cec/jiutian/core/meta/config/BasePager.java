package cec.jiutian.core.meta.config;

import lombok.Data;

import java.util.Collections;
import java.util.List;

/**
 * @author: <EMAIL>
 * @date: 2022-3-29 16:55
 * <p>
 * 用于Response前端分页
 */
@Data
public class BasePager<T> {
    private int page;
    private int perPage;
    private long count;
    private List<T> rows;

    public static BasePager emptyPage() {
        BasePager pager = new BasePager();
        pager.setCount(0);
        pager.setRows(Collections.EMPTY_LIST);
        return pager;
    }
}
