package cec.jiutian.core.handler;

import cec.jiutian.core.frame.cache.FabosJsonCacheLRU;
import cec.jiutian.core.frame.util.FabosJsonAssert;
import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import jakarta.annotation.Resource;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * date 2021/01/03 18:00
 */
@Component
public class SqlChoiceFetchHandler implements ChoiceFetchHandler {

    @Resource
    private JdbcTemplate jdbcTemplate;

    private final FabosJsonCacheLRU<List<VLModel>> eruptCache = new FabosJsonCacheLRU<>(500);

    @Override
    public List<VLModel> fetch(String[] params) {
        FabosJsonAssert.notNull(params, SqlChoiceFetchHandler.class.getSimpleName() + " → params not found");
        return eruptCache.getAndSet(SqlChoiceFetchHandler.class.getName() + ":" + params[0],
                params.length == 2 ? Long.parseLong(params[1]) : 3000,
                () -> jdbcTemplate.query(params[0], (rs, i) -> {
                    if (rs.getMetaData().getColumnCount() == 1) {
                        return new VLModel(rs.getString(1), rs.getString(1));
                    } else {
                        return new VLModel(rs.getString(1), rs.getString(2));
                    }
                })
        );
    }

    @Override
    public boolean isDataBaseQuery() {
        return true;
    }
}
