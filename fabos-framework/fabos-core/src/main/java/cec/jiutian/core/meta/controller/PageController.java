package cec.jiutian.core.meta.controller;

import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.core.meta.controller.init.InitController;
import cec.jiutian.core.meta.dto.AjaxResult;
import cec.jiutian.core.meta.dto.PublishPageDTO;
import cec.jiutian.core.meta.service.PageService;
import cec.jiutian.core.frame.constant.FabosJsonRestPath;
import cec.jiutian.meta.model.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Root;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * <AUTHOR>
 * @date ：2024/10/11 17:02
 * @description：
 */
@Tag(name =  "页面controller层")
@RestController
@RequestMapping(FabosJsonRestPath.FABOS_API)
public class PageController extends InitController {

    @Resource
    private PageService pageService;

    @Operation(summary = "查询已发布页面")
    @PostMapping("/page/getPageList")
    public AjaxResult getPageList() {
        return AjaxResult.success(pageService.getPageList());
    }

    @Operation(summary = "查询已发布页面")
    @PostMapping("/page/getPage/{id}")
    public AjaxResult getPageListById(@PathVariable("id") String id) {
        return AjaxResult.success(pageService.getPageById(id));
    }

    @Operation(summary = "发布页面")
    @PostMapping("/page/publish")
    public AjaxResult publish(@RequestBody PublishPageDTO publishPageDTO) {
        pageService.publish(publishPageDTO);
        return AjaxResult.success();
    }
}
