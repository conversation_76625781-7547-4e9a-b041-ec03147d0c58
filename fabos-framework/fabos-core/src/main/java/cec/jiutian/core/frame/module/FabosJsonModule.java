package cec.jiutian.core.frame.module;

import cec.jiutian.view.config.Comment;
import cec.jiutian.bc.urm.dto.MetaMenu;

import java.util.List;

/**
 */
public interface FabosJsonModule {

    @Comment("模块信息")
    ModuleInfo info();

    @Comment("初始化")
    default void run() {

    }

    @Comment("初始化菜单 → 仅执行一次，标识文件位置.fabos/.${moduleName}")
    default List<MetaMenu> initMenus() {
        return null;
    }

    @Comment("初始化方法 → 仅执行一次，标识文件位置.fabos/.${moduleName}")
    default void initFun() {

    }

}
