package cec.jiutian.core.frame.jpa.dao;

import cec.jiutian.common.context.UserContext;
import cec.jiutian.common.util.DateUtils;
import cec.jiutian.common.util.JacksonUtil;
import cec.jiutian.common.util.StringUtils;
import cec.jiutian.core.frame.constant.FabosJsonConst;
import cec.jiutian.core.frame.exception.FabosJsonWebApiRuntimeException;
import cec.jiutian.core.frame.jpa.service.EntityManagerService;
import cec.jiutian.core.frame.module.Page;
import cec.jiutian.core.frame.service.FabosJsonCoreService;
import cec.jiutian.core.frame.service.FabosJsonQuery;
import cec.jiutian.core.frame.util.FabosJsonUtil;
import cec.jiutian.core.frame.util.HqlUtil;
import cec.jiutian.core.service.FabosJsonSessionService;
import cec.jiutian.core.view.fabosJson.QueryModel;
import cec.jiutian.core.view.fabosJson.view.FabosJsonFieldModel;
import cec.jiutian.core.view.fabosJson.view.FabosJsonModel;
import cec.jiutian.meta.FabosJsonDataSource;
import cec.jiutian.meta.PermissionLevelEnum;
import cec.jiutian.view.field.View;
import cec.jiutian.view.query.Condition;
import cn.hutool.core.map.MapUtil;
import jakarta.annotation.Resource;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Query;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.stereotype.Repository;

import java.io.IOException;
import java.lang.reflect.Field;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static cec.jiutian.common.constant.SessionKey.FIELD_PERMISSIONS;
import static cec.jiutian.common.constant.SessionKey.ROW_PERMISSIONS;

/**
 * <AUTHOR>
 * @date ：2024/5/28 9:55
 * @description：
 */
@Slf4j
@Repository
public class FabosJsonJpaDao {

    @Resource
    private EntityManagerService entityManagerService;
    @Resource
    private FabosJsonSessionService sessionService;

    public void addEntity(Class<?> fabosJsonClass, Object entity) {
        entityManagerService.entityManagerTran(fabosJsonClass, (em) -> {
            em.persist(entity);
            em.flush();
        });
    }

    public void editEntity(Class<?> fabosJsonClass, Object entity) {
        entityManagerService.entityManagerTran(fabosJsonClass, (em) -> {
            em.merge(entity);
            em.flush();
        });
    }


    public void removeEntity(Class<?> fabosJsonClass, Object entity) {
        entityManagerService.entityManagerTran(fabosJsonClass, (em) -> {
            FabosJsonDataSource fabosJsonDataSource = fabosJsonClass.getAnnotation(FabosJsonDataSource.class);
            if (null == fabosJsonDataSource) {
                if (em.contains(entity)) {
                    em.remove(entity);
                } else {
                    em.remove(em.merge(entity));
                }
            } else {
                em.remove(em.merge(entity));
            }
            em.flush();
        });
    }

    /**
     * 处理where片段，将权限拼入，如果人员有多个角色，并且每个角色都有配置权限，则以 or 连接
     *
     * @param fabosJsonModel 模型
     * @param hqlArray       带有where 1 = 1的hql片段或完整hql，类似于：... where 1 = 1 ...
     * @return 类似于：where (权限filter1 or 权限filter2) and 1 = 1
     */
    public String[] applyRowPermission2HqlWhereSegment(FabosJsonModel fabosJsonModel, String... hqlArray) {
        if (hqlArray.length == 0 || Objects.isNull(fabosJsonModel) || StringUtils.isBlank(fabosJsonModel.getFabosJsonName())) {
            log.warn("未应用行权限过滤器，因为在处理行权限时接收到空的元数据ID或者未传入HQL片段");
            return hqlArray;
        }
        String metadataId = fabosJsonModel.getMetadataId();
        String rowPermission = "";
        UserContext.CurrentUser userInfo = UserContext.get();
        if (userInfo == null || userInfo.getSuperAdmin() || userInfo.getHashAdmin()) {
            log.debug("未应用行权限过滤器，因为无法找到用户或该用户为管理员");
            return hqlArray;
        }
        if (CollectionUtils.isNotEmpty(userInfo.getRoleIds())) {
            // 在 redis 找filter
            List<String> rowFilters = new ArrayList<>();
            for (String roleId : userInfo.getRoleIds()) {
                try {
                    Map<String, Object> cachedResult = JacksonUtil.fromJsonToMap(String.valueOf(
                            sessionService.get(ROW_PERMISSIONS + roleId)
                    ));
                    if (MapUtil.isNotEmpty(cachedResult) && cachedResult.containsKey(metadataId)) {
                        rowFilters.add(String.valueOf(cachedResult.get(metadataId)));
                    }
                } catch (IOException e) {
                    throw new FabosJsonWebApiRuntimeException("权限配置出现错误，请联系管理员");
                }
            }
            if (rowFilters.isEmpty()) {
                return hqlArray;
            }

            rowPermission += "((" + (String.join(") or (", rowFilters)) + ")) and ";

            rowPermission = replaceOutsideQuotes(rowPermission, fabosJsonModel.getFabosJsonName() + FabosJsonConst.DOT);

            HashMap<String, String> variables = new HashMap<>();
            variables.put("\\{\\{CURRENT_TIME\\}\\}", DateUtils.parseDateToString(new Date()));
            variables.put("\\{\\{CURRENT_ACCOUNT\\}\\}", userInfo.getUserId());
            variables.put("\\{\\{CURRENT_DEPARTMENT\\}\\}", userInfo.getOrgId());
            variables.put("\\{\\{CURRENT_SECRET_LEVEL\\}\\}", userInfo.getSecretLevel());

            for (Map.Entry<String, String> entry : variables.entrySet()) {
                rowPermission = rowPermission.replaceAll(fabosJsonModel.getFabosJsonName() + "." + entry.getKey(), "'" + entry.getValue() + "'");
            }
            for (int _i = 0; _i < hqlArray.length; _i++) {
                hqlArray[_i] = hqlArray[_i].replaceFirst(" where 1 = 1 ", " where " + rowPermission + " 1 = 1 ");
            }
        } else {
            log.warn("未应用行权限过滤器，因为无法找到用户和角色信息");
        }
        return hqlArray;
    }

    public String replaceOutsideQuotes(String input, String replacement) {
        // 使用正则表达式匹配不在单引号内的##

        Matcher matcher = Pattern.compile("##(?=(?:[^\']*'[^']*')*[^\']*$)").matcher(input);
        StringBuilder sb = new StringBuilder();

        while (matcher.find()) {
            matcher.appendReplacement(sb, replacement);
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    public Map<String, Integer> getColumnPermissionFromCache(List<String> metadataIds) {
        Map<String, Integer> realResult = new HashMap<>();
        UserContext.CurrentUser userInfo = UserContext.get();
        if (Objects.isNull(userInfo)
                || userInfo.getSuperAdmin()
                || CollectionUtils.isEmpty(userInfo.getRoleIds())
                || CollectionUtils.isEmpty(metadataIds)) {
            return new HashMap<>();
        }
        // 用set去重，并且其contains()时间复杂度为O(1)
        Set<String> metadataSet = new HashSet<>(metadataIds);

        // <roleId, <fieldId, permissionLevel>>
        Map<String, Map<String, Integer>> role2Permission = new HashMap<>();

        for (String roleId : userInfo.getRoleIds()) {
            Map<String, Object> cachedResult = null;
            try {
                cachedResult = JacksonUtil.fromJsonToMap(String.valueOf(
                        sessionService.get(FIELD_PERMISSIONS + roleId)
                ));
            } catch (Exception e) {
                log.error(e.getMessage());
                throw new FabosJsonWebApiRuntimeException("权限配置出现错误，请联系管理员");
            }
            // 从redis获取数据后，转换为Integer类型，如果是前端需要的权限，才写入
            if (MapUtil.isNotEmpty(cachedResult)) {
                Map<String, Integer> tempMapForTypeCast = new HashMap<>();
                cachedResult.forEach((fieldId, lvl) -> {
                    if (metadataSet.contains(fieldId)) {
                        tempMapForTypeCast.put(fieldId, Integer.parseInt(String.valueOf(lvl)));
                    }
                });
                role2Permission.put(roleId, tempMapForTypeCast);
            }
        }

        // 为全部未配置的 fieldId 补差赋值为读写
        metadataSet.forEach(f -> {
            role2Permission.forEach((roleId, permissionMap) -> {
                permissionMap.putIfAbsent(f, PermissionLevelEnum.READ_WRITE);
            });
        });

        //
        role2Permission.forEach((roleId, permissionMap) -> {
            permissionMap.forEach((fieldId, lvl) -> {
                if (!PermissionLevelEnum.NONE.getLevel().equals(lvl)
                        && !PermissionLevelEnum.READ_ONLY.getLevel().equals(lvl)
                        && !PermissionLevelEnum.READ_WRITE.equals(lvl)) {
                    throw new RuntimeException("Permission conf is incorrect");
                }
                if (metadataSet.contains(fieldId)) {
                    realResult.merge(fieldId, lvl, Integer::max);
                }
            });
        });
        // 移除realResult内value为 读写 的key
        realResult.entrySet().removeIf(entry -> Objects.equals(entry.getValue(), PermissionLevelEnum.READ_WRITE));
        return realResult;

    }

    public List<?> applyColumnPermission2ResultList(FabosJsonModel fabosJsonModel, List<?> resultList) {
        // 当前模型对应的所有字段的 metadataId -> FabosJsonFieldModel
        Map<String, FabosJsonFieldModel> metadataToField = fabosJsonModel.getFabosJsonFieldModels().stream().collect(Collectors.toMap(FabosJsonFieldModel::getMetadataId, Function.identity(), (v1, v2) -> v2));
        // 从缓存中获取当前用户的列权限
        Map<String, Integer> permissionFromCache = getColumnPermissionFromCache(new ArrayList<>(metadataToField.keySet()));

        Map<String, FabosJsonFieldModel> columnsNeedErase = new HashMap<>();
        permissionFromCache.forEach((fieldId, permissionLevel) -> {
            // 需要移除的字段
            if (PermissionLevelEnum.NONE.getLevel().equals(permissionLevel)) {
                FabosJsonFieldModel fieldModel = metadataToField.get(fieldId);
                columnsNeedErase.put(fieldModel.getFieldName(), fieldModel);
            }
        });
        resultList.forEach(r -> {
            columnsNeedErase.forEach((col, fieldModel) -> {
                // 移除确定名称的字段
                ((HashMap<?, ?>) r).remove(col);
                // 返回结果集中，有可能某些字段类似这样col_name
                View[] viewsArray = fieldModel.getFabosJsonField().views();
                if (null != viewsArray && viewsArray.length != 0) {
                    List<View> views = Arrays.asList(viewsArray);
                    // 移除余孽
                    views.forEach(v -> {
                        if (StringUtils.isNotBlank(v.column())) {
                            ((HashMap<?, ?>) r).remove(col + "_" + v.column());
                        }
                    });
                }
            });
        });
        return resultList;
    }

    public Page queryFabosJsonList(FabosJsonModel fabosJsonModel, Page page, FabosJsonQuery fabosJsonQuery) {
        boolean customHqlFlag = fabosJsonModel.getClazz().getAnnotation(QueryModel.class) != null;
        String hql;
        String countHql;
        if (!customHqlFlag) {
            hql = FabosJsonJpaUtils.generateFabosJsonJpaHql(fabosJsonModel, "new map(" + String.join(",", FabosJsonJpaUtils.getFabosJsonColJpaKeys(fabosJsonModel)) + ")", fabosJsonQuery, false);
            countHql = FabosJsonJpaUtils.generateFabosJsonJpaHql(fabosJsonModel, "count(*)", fabosJsonQuery, true);
        } else {
            String hqlStr = fabosJsonModel.getClazz().getAnnotation(QueryModel.class).hql();
            // 如果有自定义HQL，则获取自定义HQL，并拼接where子句
            // 若无，将忽略where子句
            if (hqlStr.contains("${customWhere}")) {
                //${customWhere}优先级更高
                String whereStr = FabosJsonJpaUtils.geneFabosJsonHqlCondition(fabosJsonModel, fabosJsonQuery.getConditions(), fabosJsonQuery.getConditionStrings(), fabosJsonModel.getClazz().getAnnotation(QueryModel.class).where());
                hql = hqlStr.replace("${customWhere}", whereStr);
            } else {
                String whereStr = FabosJsonJpaUtils.geneFabosJsonHqlCondition(fabosJsonModel, fabosJsonQuery.getConditions(), fabosJsonQuery.getConditionStrings());
                hql = HqlUtil.buildHqlQuery(hqlStr,whereStr);
            }
            countHql = FabosJsonJpaUtils.convertToCountHql(hql);
        }
        String[] hqlArray = applyRowPermission2HqlWhereSegment(fabosJsonModel, hql, countHql);
        String finalHql = hqlArray[0];
        String finalCountHql = hqlArray[1];
        log.info("通用列表查询queryFabosJsonList方法最终hql:{}", finalHql);
        log.info("通用列表查询queryFabosJsonList方法最终countHql:{}", finalCountHql);
        return entityManagerService.getEntityManager(fabosJsonModel.getClazz(), entityManager -> {
            Query query = entityManager.createQuery(finalHql);
            Query countQuery = entityManager.createQuery(finalCountHql);
            Map<String, FabosJsonFieldModel> fabosJsonFieldMap = fabosJsonModel.getFabosJsonFieldMap();
            //这里填充参数。需要兼容queryModelCondition逻辑。queryModelCondition不拼接hql。只填充参数。这里填充参数需要循环queryModelCondition和condition。
            List<Condition> conditions = new ArrayList<>();
            if (null != fabosJsonQuery.getQueryModelCondition()) {
                conditions.addAll(fabosJsonQuery.getQueryModelCondition());
            }
            if(null != fabosJsonQuery.getConditions()){
                conditions.addAll(fabosJsonQuery.getConditions());
            }
            for (Condition condition : conditions) {
                FabosJsonFieldModel fabosJsonFieldModel = fabosJsonFieldMap.get(condition.getKey());
                condition.setKey(condition.getKey().replace(FabosJsonConst.DOT, "_"));
                //如果是自定义sql。并且通过id查询。走特殊逻辑。兼容组合id问题。
                if (customHqlFlag && (condition.getKey().equals("id") || condition.getKey().equals("excludeList") || condition.getKey().equals("includeList"))) {
                    customHqlById(query, countQuery, condition);
                    continue;
                }
                switch (condition.getExpression()) {
                    case EQ:
                        Object convertedEQ = FabosJsonUtil.convertObjectType(fabosJsonFieldModel, condition.getValue());
                        if (Objects.nonNull(convertedEQ)) {
                            countQuery.setParameter(condition.getKey(), convertedEQ);
                            query.setParameter(condition.getKey(), convertedEQ);
                        } else {
                            continue;
                        }
                        break;
                    case LIKE:
                        countQuery.setParameter(condition.getKey(), FabosJsonJpaUtils.PERCENT + condition.getValue() + FabosJsonJpaUtils.PERCENT);
                        query.setParameter(condition.getKey(), FabosJsonJpaUtils.PERCENT + condition.getValue() + FabosJsonJpaUtils.PERCENT);
                        break;
                    case RANGE:
                        List<?> list = rangeCompatible(condition);
                        // "1,2,3"、","    、",3"
                        if (CollectionUtils.isNotEmpty(list) && (list.size() == 1 || list.size() == 2)) {
                            if (list.size() == 1) {
                                // 针对此情况： "1,"，只设置大于符号
                                countQuery.setParameter(FabosJsonJpaUtils.L_VAL_KEY + condition.getKey(), FabosJsonUtil.convertObjectType(fabosJsonFieldModel, list.get(0)));
                                query.setParameter(FabosJsonJpaUtils.L_VAL_KEY + condition.getKey(), FabosJsonUtil.convertObjectType(fabosJsonFieldModel, list.get(0)));
                            }
                            if (list.size() == 2) {
                                // ",1," 也会进入，但无伤大雅
                                Object possibleBlank = list.get(0);
                                if (Objects.isNull(possibleBlank) || ((possibleBlank instanceof String) && StringUtils.isBlank((String) possibleBlank))) {
                                    // 针对此情况： ",1"，只设置小于符号
                                    countQuery.setParameter(FabosJsonJpaUtils.R_VAL_KEY + condition.getKey(), FabosJsonUtil.convertObjectType(fabosJsonFieldModel, list.get(1)));
                                    query.setParameter(FabosJsonJpaUtils.R_VAL_KEY + condition.getKey(), FabosJsonUtil.convertObjectType(fabosJsonFieldModel, list.get(1)));
                                } else {
                                    Object leftValue = FabosJsonUtil.convertObjectType(fabosJsonFieldModel, possibleBlank);
                                    Object rightValue = FabosJsonUtil.convertObjectType(fabosJsonFieldModel, list.get(1));
                                    if (leftValue instanceof Date && rightValue instanceof Date && 0 == ((Date) leftValue).compareTo((Date) rightValue)) {
                                        LocalDateTime localDateTime = ((Date) rightValue).toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
                                        rightValue = Date.from(localDateTime.plusDays(1).atZone(ZoneId.systemDefault()).toInstant());
                                    } else if (leftValue instanceof LocalDate && rightValue instanceof LocalDate && ((LocalDate) leftValue).isEqual((LocalDate) rightValue)) {
                                        leftValue = ((LocalDate) rightValue).atStartOfDay();
                                        rightValue = ((LocalDate) rightValue).atStartOfDay().plusDays(1);
                                    } else if (leftValue instanceof LocalDateTime && rightValue instanceof LocalDateTime && ((LocalDateTime) leftValue).isEqual((LocalDateTime) rightValue)) {
                                        rightValue = ((LocalDateTime) rightValue).atZone(ZoneId.systemDefault()).toLocalDate().atStartOfDay().plusDays(1);
                                    }
                                    countQuery.setParameter(FabosJsonJpaUtils.L_VAL_KEY + condition.getKey(), leftValue);
                                    countQuery.setParameter(FabosJsonJpaUtils.R_VAL_KEY + condition.getKey(), rightValue);
                                    query.setParameter(FabosJsonJpaUtils.L_VAL_KEY + condition.getKey(), leftValue);
                                    query.setParameter(FabosJsonJpaUtils.R_VAL_KEY + condition.getKey(), rightValue);
                                }
                            }
                        }
                        break;
                    case IN, NOT:
                        List<Object> listIn = new ArrayList<>();
                        for (Object o : (List<?>) condition.getValue()) {
                            Object convertedIn = FabosJsonUtil.convertObjectType(fabosJsonFieldModel, o);
                            if (Objects.nonNull(convertedIn)) {
                                listIn.add(convertedIn);
                            } else {
                                continue;
                            }
                        }
                        countQuery.setParameter(condition.getKey(), listIn);
                        query.setParameter(condition.getKey(), listIn);
                        break;
                }
            }
            page.setTotal(Long.parseLong(countQuery.getSingleResult().toString()));
            if (page.getTotal() > 0) {
                List list = query.setMaxResults(page.getPerPage()).setFirstResult((page.getPage() - 1) * page.getPerPage()).getResultList();
                appendObjectOnResult(fabosJsonModel, list);
                page.setList(applyColumnPermission2ResultList(fabosJsonModel, list));
            } else {
                page.setList(new ArrayList<>(0));
            }
            return page;
        });
    }

    private void appendObjectOnResult(FabosJsonModel fabosJsonModel, List<?> resultList) {
        Map<String, String> fieldName2Pk = new HashMap<>();
        //
        Map<String, Set<String>> fieldName2DefinedColumn = new HashMap<>();
        Map<String, Set<String>> fieldName2Alias = new HashMap<>();
        fabosJsonModel.getFabosJsonFieldModels().forEach(field -> {
            String fieldName = field.getFieldName();
            Field javaField = field.getField();
            if (javaField.isAnnotationPresent(ManyToOne.class)) {
                FabosJsonModel referenced = FabosJsonCoreService.getFabosJson(field.getFieldReturnName());
                if (Objects.nonNull(referenced)) {
                    fieldName2Pk.put(fieldName, referenced.getFabosJson().primaryKeyCol());
                }
            }
            if (ArrayUtils.isNotEmpty(field.getFabosJsonField().views())) {
                if (javaField.isAnnotationPresent(ManyToOne.class)){
                    String definedColumn = field.getFabosJsonField().views()[0].column();
                    tryToSetAlias(definedColumn, fieldName2DefinedColumn, fieldName);
                }
                if (ArrayUtils.isNotEmpty(field.getFabosJsonField().views()[0].additionalColumns())) {
                    for (String additionalColumn : field.getFabosJsonField().views()[0].additionalColumns()) {
                        tryToSetAlias(additionalColumn, fieldName2Alias, fieldName);
                    }
                }
            }
        });

        // 对结果集做值处理
        resultList.forEach(r -> {
            // 处理pk
            fieldName2Pk.forEach((fieldName, pkCol) -> {
                Object pkValue = ((HashMap<?, ?>) r).get(fieldName);
                if (pkValue instanceof String || pkValue instanceof Long || pkValue instanceof Integer) {
                    ((HashMap) r).put(fieldName, new HashMap<>() {{
                        put(pkCol, pkValue);
                    }});
                }

            });
            // 处理additionalColumn
            fieldName2Alias.forEach((fieldName, aliasSet) -> {
                Object shouldBeAMap = ((HashMap<?, ?>) r).get(fieldName);
                if (Objects.isNull(shouldBeAMap)) {
                    ((HashMap) r).put(fieldName, new HashMap<String, String>());
                }
                aliasSet.forEach(alias -> {
                    Object value = ((HashMap<?, ?>) r).get(fieldName);
                    if (Objects.nonNull(value)) {
                        Object got = ((HashMap) r).get(fieldName + "_" + alias);
                        if (Objects.nonNull(got)) {
                            ((HashMap)((HashMap) r).get(fieldName)).put(alias, got);
                            // 如果前端确实需要addition内的值，请注释
                            ((HashMap) r).remove(fieldName + "_" + alias);
                        }
                    }
                });
            });
            // 处理column
            fieldName2DefinedColumn.forEach((fieldName, aliasSet) -> {
                Object shouldBeAMap = ((HashMap<?, ?>) r).get(fieldName);
//                if (Objects.isNull(shouldBeAMap)) {
//                    ((HashMap) r).put(fieldName, null);
//
//                }
                aliasSet.forEach(alias -> {
//                    Object value = ((HashMap<?, ?>) r).get(fieldName);
                    if (Objects.nonNull(shouldBeAMap)) {
                        Object got = ((HashMap) r).get(fieldName + "_" + alias);
                        if (Objects.nonNull(got) && got instanceof Map) {
                            ((HashMap)((HashMap) r).get(fieldName)).put(alias, got);
                        }
                    }
                });
            });

        });
    }

    private static void tryToSetAlias(String column, Map<String, Set<String>> fieldName2Alias, String fieldName) {
        if (StringUtils.isNotBlank(column)) {
            Set<String> valueSet = fieldName2Alias.get(fieldName);
            if (CollectionUtils.isEmpty(valueSet)) {
                HashSet<String> hashSet = new HashSet<>();
                hashSet.add(column);
                fieldName2Alias.put(fieldName, hashSet);
            } else {
                valueSet.add(column);
            }
        }
    }

    private void customHqlById(Query query, Query countQuery, Condition condition) {
        switch (condition.getExpression()) {
            case EQ:
                String[] values = condition.getValue().toString().split(",");
                for (int i = 0; i < values.length; i++) {
                    query.setParameter(condition.getKey() + i, values[i]);
                    countQuery.setParameter(condition.getKey() + i, values[i]);
                }
                break;
            case IN, NOT:
                if (condition.getValue() instanceof List<?> list) {
                    String example = list.get(0).toString();
                    String[] examples = example.split(FabosJsonJpaUtils.CUSTOM_SPLIT);
                    for (int i = 0; i < examples.length; i++) {
                        List<Object> listIn = new ArrayList<>();
                        for (Object o : list) {
                            String[] os = o.toString().split(FabosJsonJpaUtils.CUSTOM_SPLIT);
                            listIn.add(os[i]);
                        }
                        query.setParameter(condition.getKey() + i, listIn);
                        countQuery.setParameter(condition.getKey() + i, listIn);
                    }

                }
                break;
        }

    }

    public static List<?> rangeCompatible(Condition condition) {
        if (condition.getValue() instanceof String) {
            return Arrays.asList(((String) condition.getValue()).split(","));
        }
        return (List<?>) condition.getValue();
    }

}
