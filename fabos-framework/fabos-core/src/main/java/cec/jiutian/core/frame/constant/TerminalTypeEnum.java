package cec.jiutian.core.frame.constant;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.Getter;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Getter
public enum TerminalTypeEnum {
    WEB("WEB"),
    MOBILE("移动端"),
    All("所有"),
    ;

    private String type;

    TerminalTypeEnum(String type) {
        this.type = type;
    }

    public static class ChoiceFetch implements ChoiceFetchHandler {
        @Override
        public List<VLModel> fetch(String[] params) {
            return Stream.of(TerminalTypeEnum.values()).map(terminalTypeEnum ->
                    new VLModel(terminalTypeEnum.name(), terminalTypeEnum.getType())).collect(Collectors.toList());
        }

    }
}
