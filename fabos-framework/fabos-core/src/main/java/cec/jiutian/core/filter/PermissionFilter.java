//package cec.jiutian.core.filter;
//
//import cec.jiutian.bc.urm.dto.MetaUserinfo;
//import cec.jiutian.common.constant.SessionKey;
//import cec.jiutian.common.context.RequestContext;
//import cec.jiutian.common.context.UserContext;
//import cec.jiutian.common.util.StringUtils;
//import cec.jiutian.core.frame.constant.FabosJsonRestPath;
//import cec.jiutian.core.frame.enums.SystemSecrecyConfigEnum;
//import cec.jiutian.core.service.FabosJsonSessionService;
//import com.alibaba.fastjson2.JSON;
//import com.alibaba.fastjson2.JSONObject;
//import jakarta.servlet.Filter;
//import jakarta.servlet.FilterChain;
//import jakarta.servlet.FilterConfig;
//import jakarta.servlet.ServletException;
//import jakarta.servlet.ServletRequest;
//import jakarta.servlet.ServletResponse;
//import jakarta.servlet.http.HttpServletRequest;
//import jakarta.servlet.http.HttpServletResponse;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.BeanUtils;
//import org.springframework.core.annotation.Order;
//import org.springframework.http.HttpStatus;
//import org.springframework.stereotype.Component;
//
//import java.io.IOException;
//import java.util.Arrays;
//import java.util.List;
//import java.util.concurrent.TimeUnit;
//
//@Slf4j
//@Component
//@Order(10)
//public class PermissionFilter implements Filter {
//    private static final List<String> refreshTokenExcludeUrls = Arrays.asList("/queryAllBiz","/uninstallBiz","/loadingBiz","/fabos-cmp-urm/check","/fabos-cmp-urm/login","/fabos-cmp-urm/logout","/fabos-cmp-urm/code-img");
//    private static final String openApiRequestingUrl = FabosJsonRestPath.FABOS_OPEN_API + "/" + "requestToken";
//    private final FabosJsonSessionService sessionService;
//    public PermissionFilter(FabosJsonSessionService sessionService) {
//        this.sessionService = sessionService;
//    }
//
//    @Override
//    public void init(FilterConfig filterConfig) throws ServletException {
//        Filter.super.init(filterConfig);
//    }
//
//    @Override
//    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain chain) throws IOException, ServletException {
//        HttpServletRequest request = (HttpServletRequest) servletRequest;
//        RequestContext.setHeader(request);
//        HttpServletResponse response=(HttpServletResponse)servletResponse;
//        String url = request.getRequestURI();
//        String token = sessionService.getCurrentToken();
//
//        if (url.startsWith(FabosJsonRestPath.FABOS_OPEN_API + "/")) {
//            // 针对请求token的接口绕过
//            boolean authorized = checkAuthForOpenApiEndpoints(url, token, response);
//            if (authorized) {
//                chain.doFilter(request, response);
//            }
//            // 这等同于在下面的代码块整体else
//            return;
//        }
//
//        // 保存token
//        long tid = Thread.currentThread().getId();
//        log.info("********ThreadId: {},token: {}" , tid,token);
//        if(StringUtils.isNotBlank(token) &&!refreshTokenExcludeUrls.contains(url)){
//            //更新token时间
//            log.info("********token续期ThreadId: {},url:{},token: {}" , tid,url,token);
//            // todo 2024-11-19 续期token也要续到系统安全配置里的时间吗
//            sessionService.refreshTokenTime(token, SystemSecrecyConfigEnum.get().getConnectTime(), TimeUnit.MINUTES);
//        }
//        MetaUserinfo metaUserinfo= sessionService.getSimpleUserInfo();
//        if(metaUserinfo==null){
//            log.info("********token is invalid! ThreadId: {},token: {} " , tid,token);
//            chain.doFilter(request, response);
//            return;
//        }
//        UserContext.CurrentUser userParam = new UserContext.CurrentUser();
//        UserContext.set(userParam);
//        BeanUtils.copyProperties(metaUserinfo, userParam);
//        userParam.setUserId(metaUserinfo.getId());
//        userParam.setUserName(metaUserinfo.getName());
//        userParam.setAccount(metaUserinfo.getAccount());
//        userParam.setRoles(metaUserinfo.getRoles());
//        userParam.setHashAdmin(metaUserinfo.isAdmin());
//        userParam.setToken(token);
//        userParam.setUri(request.getRequestURI());
//        userParam.setMenuIds(metaUserinfo.getMenus());
//        UserContext.set(userParam);
//        UserContext.setToken(token);
//        chain.doFilter(request, response);
//    }
//
//    /**
//     * 对于开放接口，校验token和能否访问对应资源
//     *
//     * @param uri      请求URI
//     * @param token    请求偷啃
//     * @param response 相应
//     * @return 是否通过校验，是为ture
//     * @throws IOException 如果写入response失败
//     */
//    private boolean checkAuthForOpenApiEndpoints(String uri, String token, HttpServletResponse response) throws IOException {
//        JSONObject responseBody = new JSONObject();
//        boolean errorIndicator = false;
//        if (!openApiRequestingUrl.equals(uri)) {
//            if (StringUtils.isBlank(token)) {
//                responseBody.put("status", HttpStatus.UNAUTHORIZED.value());
//                responseBody.put("reason", "Token is required");
//                log.warn("在请求此URI时[{}]未提供token", uri);
//                errorIndicator = true;
//            }
//            String availableResources = sessionService.getAsString(SessionKey.OPEN_API_TOKENS + token);
//            if (StringUtils.isBlank(availableResources)) {
//                responseBody.put("status", HttpStatus.UNAUTHORIZED.value());
//                responseBody.put("reason", "Invalid token");
//                log.warn("在请求此URI时[{}]提供了非法token:{} ", uri, token);
//                errorIndicator = true;
//            } else {
//                List<String> availableResourceList = JSON.parseArray(availableResources, String.class);
//                String truncatedUri = uri.replaceFirst(FabosJsonRestPath.FABOS_OPEN_API, "");
//                if (!availableResourceList.contains(truncatedUri)) {
//                    responseBody.put("status", HttpStatus.FORBIDDEN.value());
//                    responseBody.put("reason", "Access unauthorized");
//                    errorIndicator = true;
//                }
//            }
//        }
//        if (errorIndicator) {
//            response.setStatus(responseBody.getInteger("status"));
//            response.getWriter().write(responseBody.toString());
//        }
//        return !errorIndicator;
//    }
//
//    @Override
//    public void destroy() {
//        Filter.super.destroy();
//        UserContext.clear();
//        RequestContext.clear();
//    }
//}
