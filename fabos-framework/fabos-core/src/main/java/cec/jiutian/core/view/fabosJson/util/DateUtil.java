package cec.jiutian.core.view.fabosJson.util;

import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Objects;

/**
 */
public class DateUtil {

    public static final String DATE = "yyyy-MM-dd";

    public static final String YEAR_MONTH = "yyyy-MM";

    public static final String HHmmssSSS = "HHmmssSSS";

    public static final String DATE_TIME = "yyyy-MM-dd HH:mm:ss";

    /**
     * LocalDateTime 转 Date
     */
    public static Date transformToDate(LocalDateTime localDateTime) {
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * Date 转 LocalDateTime
     */
    public static LocalDateTime transformToLocalDateTime(Date date) {
        return LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault());
    }

    public static String getSimpleFormatDateTime(Date date) {
        return getFormatDate(date, DATE_TIME);
    }

    public static String getSimpleFormatDate(Date date) {
        return getFormatDate(date, DATE);
    }

    public static String getFormatDate(Date date, String formatStr) {
        SimpleDateFormat sdf = new SimpleDateFormat(formatStr);
        return sdf.format(date);
    }

    public static boolean isDate(String str) {
        Date parsedDate = null;
        LocalDate parsedLocalDate = null;
        Date parsedDateWithTime = null;
        LocalDateTime parsedLocalDateTime = null;
        try {
            parsedDateWithTime = new SimpleDateFormat(DATE_TIME).parse(str);
        } catch (Exception ignored) {
        }
        if (Objects.nonNull(parsedDateWithTime)) {
            return true;
        }
        try {
            parsedLocalDateTime = LocalDateTime.parse(str, DateTimeFormatter.ofPattern(DATE_TIME));
        } catch (Exception ignored) {
        }
        if (Objects.nonNull(parsedLocalDateTime)) {
            return true;
        }
        try {
            parsedDate = new SimpleDateFormat(DATE).parse(str);
        } catch (Exception ignored) {
        }
        if (Objects.nonNull(parsedDate)) {
            return true;
        }
        try {
            parsedLocalDate = LocalDate.parse(str, DateTimeFormatter.ofPattern(DATE));
        } catch (Exception ignored) {
        }
        if (Objects.nonNull(parsedLocalDate)) {
            return true;
        }
        return false;
    }

    public static Object getDate(Class<?> requiredType, String str) {
        if (requiredType != Date.class
                && requiredType != LocalDate.class
                && requiredType != LocalDateTime.class) {
            throw new FabosJsonApiErrorTip(String.format("不支持此日期类型：[%s]", requiredType));
        }
        Date parsedDate = null;
        LocalDate parsedLocalDate = null;
        Date parsedDateWithTime = null;
        LocalDateTime parsedLocalDateTime = null;

        try {
            parsedDateWithTime = new SimpleDateFormat(DATE_TIME).parse(str);
        } catch (Exception ignored) {
        }
        if (Objects.nonNull(parsedDateWithTime)) {
            if (requiredType.equals(Date.class)) {
                return (Date) parsedDateWithTime;
            } else if (requiredType.equals(LocalDate.class)) {
                return (LocalDate) parsedDateWithTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            } else {
//                if (requiredType.equals(LocalDateTime.class)) {
                return (LocalDateTime) parsedDateWithTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
            }
        }

        try {
            parsedLocalDateTime = LocalDateTime.parse(str, DateTimeFormatter.ofPattern(DATE_TIME));
        } catch (Exception ignored) {
        }
        if (Objects.nonNull(parsedLocalDateTime)) {
            if (requiredType.equals(LocalDateTime.class)) {
                return (LocalDateTime) parsedLocalDateTime;
            } else if (requiredType.equals(Date.class)) {
                return (Date) Date.from(parsedLocalDateTime.atZone(ZoneId.systemDefault()).toInstant());
            } else {
//                if (requiredType.equals(LocalDate.class)) {
                return (LocalDate) parsedLocalDateTime.toLocalDate();
            }
        }

        try {
            parsedDate = new SimpleDateFormat(DATE).parse(str);
        } catch (Exception ignored) {
        }
        if (Objects.nonNull(parsedDate)) {
            if (requiredType.equals(Date.class)) {
                return (Date) parsedDate;
            } else if (requiredType.equals(LocalDate.class)) {
                return (LocalDate) parsedDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            } else {
//                if (requiredType.equals(LocalDateTime.class)) {
                return (LocalDateTime) parsedDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
            }
        }

        try {
            parsedLocalDate = LocalDate.parse(str, DateTimeFormatter.ofPattern(DATE));
        } catch (Exception ignored) {
        }
        if (Objects.nonNull(parsedLocalDate)) {
            if (requiredType.equals(LocalDate.class)) {
                return (LocalDate) parsedLocalDate;
            } else if (requiredType.equals(Date.class)) {
                return (Date) Date.from(parsedLocalDate.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
            } else {
//                if (requiredType.equals(LocalDateTime.class)) {
                return (LocalDateTime) parsedLocalDate.atStartOfDay();
            }
        }

        throw new RuntimeException("传入的日期格式不正确");
    }

}
