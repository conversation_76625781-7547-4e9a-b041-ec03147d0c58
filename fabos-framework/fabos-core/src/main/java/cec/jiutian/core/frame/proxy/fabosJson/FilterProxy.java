package cec.jiutian.core.frame.proxy.fabosJson;

import cec.jiutian.core.view.fabosJson.util.FabosJsonSpringUtil;
import cec.jiutian.core.frame.proxy.AnnotationProxy;
import cec.jiutian.view.fun.FilterHandler;
import cec.jiutian.view.type.Filter;
import org.aopalliance.intercept.MethodInvocation;

/**
 */
public class FilterProxy<P> extends AnnotationProxy<Filter, P> {

    @Override
    protected Object invocation(MethodInvocation invocation) {
        if (super.matchMethod(invocation, Filter::value)) {
            String condition = this.rawAnnotation.value();
            if (!this.rawAnnotation.conditionHandler().isInterface()) {
                FilterHandler ch = FabosJsonSpringUtil.getBean(this.rawAnnotation.conditionHandler());
                condition = ch.filter(condition, this.rawAnnotation.params());
            }
            return condition;
        }
        return this.invoke(invocation);
    }

}
