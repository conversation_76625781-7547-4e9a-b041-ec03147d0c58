package cec.jiutian.core.frame.jpa.prop;

import cec.jiutian.core.frame.jpa.config.HikariCpConfig;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.autoconfigure.orm.jpa.JpaProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;
import org.springframework.stereotype.Component;

/**
 */
@Getter
@Setter
@Component
@ConfigurationProperties("fabosjson")
public class FabosJsonPropForDb {

    //多数据源
    private DB[] dbs;


    @Getter
    @Setter
    public static class DB {

        @NestedConfigurationProperty
        private FabosPropDataSource datasource;

        @NestedConfigurationProperty
        private JpaProperties jpa;

        private String[] scanPackages;

    }

    @Getter
    @Setter
    public static class FabosPropDataSource {

        private String name;

        private String driverClassName;

        private String url;

        private String username;

        private String password;

        @NestedConfigurationProperty
        private HikariCpConfig hikari = new HikariCpConfig();

    }



}
