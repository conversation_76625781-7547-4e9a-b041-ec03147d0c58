package cec.jiutian.log.businessLog.entity;

import cec.jiutian.log.AsycLog.AsyncLog;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description:  主要用于保存数据  菜单显示使用BusinessLogRecordView
 */
@Entity
@Table(name = "fd_business_log_record")
@Getter
@Setter
public class BusinessLogRecord extends AsyncLog {

    private String modelName;

    private Integer buildVersion;

    private LocalDateTime recordTime;

    //增删改
    private String actionType;

    @Column(columnDefinition = "text")
    private String data;

    private String dataId;
    @OneToMany(cascade = CascadeType.ALL,fetch = FetchType.EAGER)
    @JoinColumn(name = "business_log_record_id")
    @OrderBy
    private List<BusinessLogRecordField> businessLogRecordFields;
}
