package cec.jiutian.log.businessLog.entity;

import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.View;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @description:
 */
@Entity
@Table(name = "fd_business_log_record_field")
@Getter
@Setter
public class BusinessLogRecordField extends MetaModel {
    @FabosJsonField(
            views = @View(title = "字段"),
            edit = @Edit(title = "字段")
    )
    private String columnName;
    @FabosJsonField(
            views = @View(title = "字段值"),
            edit = @Edit(title = "字段值")
    )
    private String columnValue;
    @ManyToOne
    @JsonIgnoreProperties("businessLogRecordFields")
    private BusinessLogRecord businessLogRecord;
}
