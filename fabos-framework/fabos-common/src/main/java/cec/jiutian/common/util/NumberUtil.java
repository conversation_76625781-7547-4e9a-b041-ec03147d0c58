package cec.jiutian.common.util;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @description: 返回非空Number 为空是返回0
 * @author: <EMAIL>
 */
public class NumberUtil {
    public static <T extends Number> T getNotNullNumber(T t, Class<T> tClass) {
        Object result = new Object();
        Double dblValue;
        if (t == null) {
            dblValue = new Double(0D);
        } else {
            return t;
        }
        switch (tClass.getTypeName()) {
            case "java.lang.Byte":
                result = dblValue.byteValue();
                break;
            case "java.lang.Short":
                result = dblValue.shortValue();
                break;
            case "java.lang.Integer":
                result = dblValue.intValue();
                break;
            case "java.lang.Long":
                result = dblValue.longValue();
                break;
            case "java.lang.Float":
                result = dblValue.floatValue();
                break;
            case "java.lang.Double":
                result = dblValue;
                break;
            default:
        }
        return (T) result;
    }

    public static <T extends Number> T getNumberByString(String str, Class<T> tClass) {
        Object result = new Object();
        Double dblValue;
        if (str == null) {
            dblValue = new Double(0D);
        } else {
            Pattern p = Pattern.compile("[^0-9]");
            Matcher m = p.matcher(str);
            String numStr = m.replaceAll("").trim();
            dblValue = Double.parseDouble(numStr);
        }
        switch (tClass.getTypeName()) {
            case "java.lang.Byte":
                result = dblValue.byteValue();
                break;
            case "java.lang.Short":
                result = dblValue.shortValue();
                break;
            case "java.lang.Integer":
                result = dblValue.intValue();
                break;
            case "java.lang.Long":
                result = dblValue.longValue();
                break;
            case "java.lang.Float":
                result = dblValue.floatValue();
                break;
            case "java.lang.Double":
                result = dblValue;
                break;
            default:
        }
        return (T) result;
    }

    /*
     * @Description 对版本做升级处理，适用于0001等中间无分隔符的版本
     * @param version 原版本
     * @return 升级后的版本
     **/
    public static String versionUpgrade(String version) {
        if (null == version) {
            return null;
        }
        return versionUpgrade(version, version.length());
    }

    public static String versionUpgrade(String version, int formatLength) {
        if (null == version) {
            return null;
        }
        int intValue = 0;
        String regularizeVersion = removeLeftZero(version);
        if (StringUtils.isNotEmpty(regularizeVersion)) {
            intValue = Integer.parseInt(regularizeVersion);
        }
        ++intValue;
        NumberFormat nf = NumberFormat.getInstance();
        nf.setGroupingUsed(false);
        nf.setMaximumIntegerDigits(formatLength);
        nf.setMinimumIntegerDigits(formatLength);
        return nf.format(intValue);
    }

    public static String removeLeftZero(String str) {
        if (null == str) {
            return null;
        } else {
            return str.replaceAll("^(0+)", "");
        }
    }

    public static String removeRightZero(String str) {
        if (null == str) {
            return null;
        } else {
            return str.replaceAll("0*$", "");
        }
    }

    /*
     * @Description //获取字符中的所有数字
     **/
    public static String getStringNum(String str) {
        String regEx = "[^0-9]";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(str);
        return m.replaceAll("").trim();
    }

    /*
     * 保留固定位小数
     **/
    public static <T extends Number> T setScale(T t, int scale) {
        Object result = new Object();
        BigDecimal bd;
        if (t == null) {
            return t;
        }
        switch (t.getClass().getTypeName()) {
            case "java.lang.Float":
                bd = new BigDecimal(Float.toString((Float) t));
                bd = bd.setScale(scale, RoundingMode.HALF_UP);
                float floatScale = bd.floatValue();
                result = floatScale;
                break;
            case "java.lang.Double":
                bd = new BigDecimal(Double.toString((Double) t));
                bd = bd.setScale(scale, RoundingMode.HALF_UP);
                double doubleScale = bd.doubleValue();
                result = doubleScale;
                break;
            default:
        }
        return (T) result;
    }

    /*
     * 对值为M的整数均分为N份
     **/
    public static int[] splitInteger(int M, int N) {
        if (Objects.isNull(N) || Objects.isNull(M) || N <= 0 || M <= 0) {
            return null;
        }
        int[] result = new int[N];
        int average = M / N;
        int remainder = M % N;

        for (int i = 0; i < N; i++) {
            result[i] = average;
            if (i < remainder) {
                result[i]++;
            }
        }

        return result;
    }

    //对值为M的double类型数均分为N份，并可以指定位数
    public static double[] splitDouble(double M, int N, int scale) {
        if (Objects.isNull(N) || Objects.isNull(M) || N <= 0 || M <= 0) {
            return null;
        }
        double[] result = new double[N];
        double sum = 0;
        double step = M / N;
        for (int i = 0; i < N; i++) {
            result[i] = setScale(step, scale);
            sum += result[i];
        }
        BigDecimal diff = (BigDecimal.valueOf(setScale(M, scale)).subtract(BigDecimal.valueOf(setScale(sum, scale)))).setScale(scale);
        result[N - 1] = result[N - 1] + diff.doubleValue();
        return result;
    }

    public static double[] splitDouble(double value, int n) {
        return splitDouble(value, n, 2);
    }


}
