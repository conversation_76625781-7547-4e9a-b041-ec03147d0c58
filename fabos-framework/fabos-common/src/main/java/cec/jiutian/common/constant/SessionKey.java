package cec.jiutian.common.constant;

public class SessionKey {

    private static final String AUTH_SPACE = "fabos-auth:";

    public static final String VERIFY_CODE = AUTH_SPACE + "verify-code:";

    public static final String MENU_VIEW = AUTH_SPACE + "menu-view:";  //菜单列表

    public static final String MENU_VALUE_MAP = AUTH_SPACE + "menu-value-map:"; //菜单类型值作为key的map

    public static final String TOKEN_OLINE = AUTH_SPACE + "token:"; //存储TOKEN使用情况

    public static final String USER_INFO = AUTH_SPACE + "user:"; //缓存用户信息
    //用户相关KEY
    public static final String[] USER_KEY_GROUP = {
            MENU_VIEW,
            MENU_VALUE_MAP,
            TOKEN_OLINE,
            USER_INFO
    };
    public static final String LOGIN_ERROR = AUTH_SPACE + "login-error:"; //登录失败次数

    public static final String TOKEN = "token";

    public static final String URL_PARAM_TOKEN = "_" + TOKEN;

    public static final String DISABLE_LOGIN_ACCOUNT = AUTH_SPACE + "disable-login-account:"; //登录失败超过规定次数，禁止登录的账号

    public static final String USER_PLATFORM = AUTH_SPACE + "user-platform:"; //用户登录的平台

    public static final String MOBILE_APP = USER_PLATFORM + "mobile:"; //从移动端登录的key（用于拼接到token的key中）

    public static final String DESKTOP_BROWSER = USER_PLATFORM + "desktop:"; //从桌面浏览器登录的key（用于拼接到token的key中）

    public static final String FIELD_PERMISSIONS = AUTH_SPACE + "field_permissions:"; //列权限缓存

    public static final String ROW_PERMISSIONS = AUTH_SPACE + "row_permissions:"; //行权限缓存

    public static final String OPEN_API_TOKENS = AUTH_SPACE + "open_api_online:"; //open api当前在线的token

    public static final String ONLINE_USERS = AUTH_SPACE + "online_users:";

    public static final String COLON = ":";
    public static final String UNDERLINE = "_";
    public static final String ASTERISK = "*";

    public static final String BACKGROUND_TASKS = AUTH_SPACE + "background-tasks" + COLON;
}
