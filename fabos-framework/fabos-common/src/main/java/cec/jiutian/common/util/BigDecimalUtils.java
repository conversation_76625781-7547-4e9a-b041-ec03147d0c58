package cec.jiutian.common.util;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/1/19
 */
public class BigDecimalUtils {
    public static Double add(Double a, Double b) {
        BigDecimal b1 = new BigDecimal(a.toString());
        BigDecimal b2 = new BigDecimal(b.toString());
        return b1.add(b2).doubleValue();
    }

    public static Double add(List<Double> list) {
        BigDecimal bigDecimal = BigDecimal.ZERO;
        for (Double num : list) {
            bigDecimal = bigDecimal.add(new BigDecimal(num.toString()));
        }
        return bigDecimal.doubleValue();
    }

    public static Double add(Double... num) {
        return add(Arrays.asList(num));
    }

    public static Double sub(Double a, Double b) {
        BigDecimal b1 = new BigDecimal(a.toString());
        BigDecimal b2 = new BigDecimal(b.toString());
        return b1.subtract(b2).doubleValue();
    }

    public static Double mul(Double a, Double b) {
        BigDecimal b1 = new BigDecimal(a.toString());
        BigDecimal b2 = new BigDecimal(b.toString());
        return b1.multiply(b2).doubleValue();
    }

    public static Double div(Double a, Double b, int scale) {
        BigDecimal b1 = new BigDecimal(a.toString());
        BigDecimal b2 = new BigDecimal(b.toString());
        return b1.divide(b2, scale, RoundingMode.HALF_UP).doubleValue();
    }

    public static Double div(Double a, Double b) {
        BigDecimal b1 = new BigDecimal(a.toString());
        BigDecimal b2 = new BigDecimal(b.toString());
        return b1.divide(b2, 10, RoundingMode.HALF_UP).doubleValue();
    }

    public static Double div(Integer a, Integer b, int scale) {
        BigDecimal b1 = new BigDecimal(a.toString());
        BigDecimal b2 = new BigDecimal(b.toString());
        return b1.divide(b2, scale, RoundingMode.HALF_UP).doubleValue();
    }

    public static Double div(Integer a, Integer b) {
        BigDecimal b1 = new BigDecimal(a.toString());
        BigDecimal b2 = new BigDecimal(b.toString());
        return b1.divide(b2, 10, RoundingMode.HALF_UP).doubleValue();
    }

    /**
     * 控制结果精确度和舍入方式
     */
    public static Double div(double d1, double d2, Integer scale, Integer roundMode) {
        BigDecimal b1 = new BigDecimal(Double.toString(d1));
        BigDecimal b2 = new BigDecimal(Double.toString(d2));
        return b1.divide(b2, scale, roundMode).doubleValue();
    }
}
