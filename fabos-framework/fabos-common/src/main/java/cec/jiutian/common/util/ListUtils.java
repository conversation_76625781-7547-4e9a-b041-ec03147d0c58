package cec.jiutian.common.util;

import lombok.extern.slf4j.Slf4j;
import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.StringJoiner;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.function.ToIntFunction;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023年05月11日 16:25
 */
@Slf4j
public final class ListUtils {
    private ListUtils() {

    }

    public static <T> List<T> nullToEmpty(List<T> list) {
        return Optional.ofNullable(list).orElse(Collections.emptyList());
    }

    public static <T, R> List<R> collect(List<T> list, Function<? super T, ? extends R> mapper) {
        return nullToEmpty(list).stream().map(mapper).collect(Collectors.toList());
    }

    public static <T, R> Set<R> distinctCollect(List<T> list, Function<? super T, ? extends R> mapper) {
        return nullToEmpty(list).stream().map(mapper).collect(Collectors.toSet());
    }

    public static <T, K> Map<K, List<T>> group(List<T> list, Function<? super T, ? extends K> classifier) {
        return nullToEmpty(list).stream().collect(Collectors.groupingBy(classifier));
    }

    public static <T> List<T> sort(List<T> list, Comparator<? super T> comparator) {
        return nullToEmpty(list).stream().sorted(comparator).collect(Collectors.toList());
    }

    public static <T> Integer sum(List<T> list, ToIntFunction<? super T> mapper) {
        return nullToEmpty(list).stream().collect(Collectors.summingInt(mapper));
    }

    public static @Nonnull <T> List<T> filter(@Nullable Collection<T> list, @Nonnull Predicate<T> predicate) {
        if (list == null || list.isEmpty()) {
            return Collections.emptyList();
        }
        final List<T> newList = new ArrayList<>();
        for (final T t : list) {
            if (predicate.test(t)) {
                newList.add(t);
            }
        }
        return newList;
    }

    public static @Nonnull <T, R> List<R> map(@Nullable Collection<T> list, @Nonnull Function<T, R> mapper) {
        if (list == null || list.isEmpty()) {
            return Collections.emptyList();
        }
        final List<R> newList = new ArrayList<>();
        for (final T t : list) {
            newList.add(mapper.apply(t));
        }
        return newList;
    }

    public static @Nonnull <T, R> List<R> map(@Nullable Collection<T> source, @Nonnull List<R> target, @Nonnull Function<T, R> mapper) {
        if (source == null || source.isEmpty()) {
            return target;
        }
        for (final T t : source) {
            target.add(mapper.apply(t));
        }
        return target;
    }

    public static @Nullable <T> T findFirst(@Nullable Collection<T> list, @Nonnull Predicate<T> predicate) {
        if (list == null || list.isEmpty()) {
            return null;
        }
        for (final T t : list) {
            if (predicate.test(t)) {
                return t;
            }
        }
        return null;
    }

    public static @Nonnull <T, R> Set<R> distinct(@Nullable Collection<T> list, @Nonnull Function<T, R> mapper) {
        if (list == null || list.isEmpty()) {
            return Collections.emptySet();
        }
        final Set<R> newList = new LinkedHashSet<>();
        for (final T t : list) {
            newList.add(mapper.apply(t));
        }
        return newList;
    }

    public static @Nonnull <K, V> Map<K, List<V>> groupBy(@Nullable Collection<V> list, @Nonnull Function<V, K> mapper) {
        return groupBy(new HashMap<>(), list, mapper);
    }

    public static @Nonnull <K, V> Map<K, List<V>> groupBy(@Nonnull Map<K, List<V>> map, @Nullable Collection<V> list, @Nonnull Function<V, K> mapper) {
        if (list == null || list.isEmpty()) {
            return map;
        }
        for (final V v : list) {
            map.computeIfAbsent(mapper.apply(v), (k) -> new ArrayList<>()).add(v);
        }
        return map;
    }

    public static @Nonnull <K, V> Map<K, V> toMap(@Nullable Collection<V> list, @Nonnull Function<V, K> mapper) {
        if (list == null || list.isEmpty()) {
            return Collections.emptyMap();
        }
        final Map<K, V> map = new HashMap<>();
        for (final V v : list) {
            map.put(mapper.apply(v), v);
        }
        return map;
    }

    public static @Nonnull <K, V, E> Map<K, V> toMap(
            @Nullable Collection<E> list,
            @Nonnull Function<E, K> keyMapper,
            @Nonnull Function<E, V> valueMapper
    ) {
        if (list == null || list.isEmpty()) {
            return Collections.emptyMap();
        }
        final Map<K, V> map = new HashMap<>();
        for (final E e : list) {
            map.put(keyMapper.apply(e), valueMapper.apply(e));
        }
        return map;
    }

    public static @Nonnull <T> List<T> page(@Nullable List<T> store, @Nonnull Integer pageNo, @Nonnull Integer pageSize) {
        if (store == null) {
            return Collections.emptyList();
        }
        final int size = store.size();
        final int offset = (pageNo - 1) * pageSize;
        if (offset >= size) {
            return Collections.emptyList();
        }
        final int endOffset = Math.min(offset + pageSize, size);
        return store.subList(offset, endOffset);
    }

    public static <T> void forEach(@Nullable List<T> list, @Nonnull Consumer<T> foreach) {
        if (list == null) {
            return;
        }
        list.forEach(foreach);
    }

    public static @Nonnull <T, R> String join(@Nullable List<T> list, @Nonnull CharSequence delimiter, @Nonnull Function<T, R> mapper) {
        if (list == null) {
            return "";
        }
        final StringJoiner joiner = new StringJoiner(delimiter);
        for (final T t : list) {
            joiner.add(mapper.apply(t).toString());
        }
        return joiner.toString();
    }

    public static <T> Predicate<T> distinctByKey(Function<? super T, Object> keyExtractor) {
        Map<Object, Boolean> seen = new ConcurrentHashMap<>();
        return t -> seen.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }

    public static @Nonnull <T> List<T> distinctListByKey(@Nullable List<T> list, Function<? super T, Object> keyExtractor) {
        return nullToEmpty(list).stream().filter(distinctByKey(keyExtractor)).collect(Collectors.toList());
    }
}
