package cec.jiutian.common.result;

import cec.jiutian.common.enums.HttpStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 返回结果集
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("统一结果集处理器")
public class RespResult<T> {

    /**
     * 状态码
     */
    @ApiModelProperty(value = "状态码")
    private Integer code;

    /**
     * 状态信息
     */
    @ApiModelProperty(value = "状态信息")
    private Integer status;

    /**
     * 返回信息
     */
    @ApiModelProperty(value = "返回信息")
    private String msg;

    /**
     * 数据
     */
    @ApiModelProperty(value = "数据")
    private T data;

    /**
     * 全参数方法
     *
     * @param code    状态码
     * @param status  状态
     * @param message 返回信息
     * @param data    返回数据
     * @param <T>     泛型
     * @return {@link RespResult<T>}
     */
    private static <T> RespResult<T> response(Integer code, int status, String message, T data) {
        RespResult<T> RespResult = new RespResult<>();
        RespResult.setCode(code);
        RespResult.setStatus(status);
        RespResult.setMsg(message);
        RespResult.setData(data);
        return RespResult;
    }

    /**
     * 全参数方法
     *
     * @param code    状态码
     * @param status  状态
     * @param message 返回信息
     * @param <T>     泛型
     * @return {@link RespResult<T>}
     */
    private static <T> RespResult<T> response(Integer code, int status, String message) {
        RespResult<T> RespResult = new RespResult<>();
        RespResult.setCode(code);
        RespResult.setStatus(status);
        RespResult.setMsg(message);
        return RespResult;
    }

    /**
     * 成功返回（无参）
     *
     * @param <T> 泛型
     * @return {@link RespResult<T>}
     */
    public static <T> RespResult<T> success() {
        return response(HttpStatusEnum.SUCCESS.getCode(), 0, HttpStatusEnum.SUCCESS.getMessage(), null);
    }

    /**
     * 成功返回（枚举参数）
     *
     * @param httpResponseEnum 枚举参数
     * @param <T>              泛型
     * @return {@link RespResult<T>}
     */
    public static <T> RespResult<T> success(HttpStatusEnum httpResponseEnum) {
        return response(httpResponseEnum.getCode(), 0, httpResponseEnum.getMessage());
    }

    /**
     * 成功返回（状态码+返回信息）
     *
     * @param code    状态码
     * @param message 返回信息
     * @param <T>     泛型
     * @return {@link RespResult<T>}
     */
    public static <T> RespResult<T> success(Integer code, String message) {
        return response(code, 0, message);
    }

    /**
     * 成功返回（返回信息 + 数据）
     *
     * @param message 返回信息
     * @param data    数据
     * @param <T>     泛型
     * @return {@link RespResult<T>}
     */
    public static <T> RespResult<T> success(String message, T data) {
        return response(HttpStatusEnum.SUCCESS.getCode(), 0, message, data);
    }

    /**
     * 成功返回（状态码+返回信息+数据）
     *
     * @param code    状态码
     * @param message 返回信息
     * @param data    数据
     * @param <T>     泛型
     * @return {@link RespResult<T>}
     */
    public static <T> RespResult<T> success(Integer code, String message, T data) {
        return response(code, 0, message, data);
    }

    /**
     * 成功返回（数据）
     *
     * @param data 数据
     * @param <T>  泛型
     * @return {@link RespResult<T>}
     */
    public static <T> RespResult<T> success(T data) {
        return response(HttpStatusEnum.SUCCESS.getCode(), 0, HttpStatusEnum.SUCCESS.getMessage(), data);
    }

    /**
     * 成功返回（返回信息）
     *
     * @param message 返回信息
     * @param <T>  泛型
     * @return {@link RespResult<T>}
     */
    public static <T> RespResult<T> success(String message) {
        return response(HttpStatusEnum.SUCCESS.getCode(), 0, message, null);
    }

    /**
     * 失败返回（无参）
     *
     * @param <T> 泛型
     * @return {@link RespResult<T>}
     */
    public static <T> RespResult<T> fail() {
        return response(HttpStatusEnum.ERROR.getCode(), -1, HttpStatusEnum.ERROR.getMessage(), null);
    }

    /**
     * 失败返回（枚举）
     *
     * @param httpResponseEnum 枚举
     * @param <T>              泛型
     * @return {@link RespResult<T>}
     */
    public static <T> RespResult<T> fail(HttpStatusEnum httpResponseEnum) {
        return response(httpResponseEnum.getCode(), -1, httpResponseEnum.getMessage());
    }

    /**
     * 失败返回（状态码+返回信息）
     *
     * @param status    状态码
     * @param message 返回信息
     * @param <T>     泛型
     * @return {@link RespResult<T>}
     */
    public static <T> RespResult<T> fail(Integer status, String message) {
        return response(null, status, message);
    }

    /**
     * 失败返回（返回信息+数据）
     *
     * @param message 返回信息
     * @param data    数据
     * @param <T>     泛型
     * @return {@link RespResult<T>}
     */
    public static <T> RespResult<T> fail(String message, T data) {
        return response(HttpStatusEnum.ERROR.getCode(), 0, message, data);
    }

    /**
     * 失败返回（状态码+返回信息+数据）
     *
     * @param code    状态码
     * @param message 返回消息
     * @param data    数据
     * @param <T>     泛型
     * @return {@link RespResult<T>}
     */
    public static <T> RespResult<T> fail(Integer code, String message, T data) {
        return response(code, -1, message, data);
    }

    /**
     * 失败返回（数据）
     *
     * @param data 数据
     * @param <T>  泛型
     * @return {@link RespResult<T>}
     */
    public static <T> RespResult<T> fail(T data) {
        return response(HttpStatusEnum.ERROR.getCode(), -1, HttpStatusEnum.ERROR.getMessage(), data);
    }

    /**
     * 失败返回（返回信息）
     *
     * @param message 返回信息
     * @param <T>  泛型
     * @return {@link RespResult<T>}
     */
    public static <T> RespResult<T> fail(String message) {
        return response(HttpStatusEnum.ERROR.getCode(), -1, message, null);
    }
}