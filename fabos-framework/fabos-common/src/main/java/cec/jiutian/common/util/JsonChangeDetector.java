package cec.jiutian.common.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.security.MessageDigest;

public class JsonChangeDetector {
    private static final ObjectMapper objectMapper = new ObjectMapper();
    // 方法：计算 JSON 数据的哈希值
    private static String getJsonHash(JsonNode jsonNode) throws Exception {
        // 将 JSON 数据转化为规范化的字符串（去除空格、换行符）
        String jsonString = objectMapper.writeValueAsString(jsonNode);
        // 计算哈希值
        MessageDigest digest = MessageDigest.getInstance("SHA-256");
        byte[] hashBytes = digest.digest(jsonString.getBytes());
        return bytesToHex(hashBytes);
    }

    // 将字节数组转换为十六进制字符串
    private static String bytesToHex(byte[] bytes) {
        StringBuilder hexString = new StringBuilder();
        for (byte b : bytes) {
            hexString.append(String.format("%02x", b));
        }
        return hexString.toString();
    }

    // 方法：比较两个 JSON 数据是否变化
    public static boolean isJsonChanged(String oldJsonStr, String newJsonStr) {
        try {
            JsonNode oldJson = objectMapper.readTree(oldJsonStr);
            JsonNode newJson = objectMapper.readTree(newJsonStr);
            //String oldJsonHash = getJsonHash(oldJson);
            //String newJsonHash = getJsonHash(newJson);
            //比较hash会有顺序问题  这里直接对比JsonNode
            return !oldJson.equals(newJson);
        } catch (Exception e) {
            throw new RuntimeException("Error while calculating JSON hash", e);
        }
    }

}


