package cec.jiutian.meta;



import cec.jiutian.view.config.Comment;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
@Documented
@Inherited
public @interface FabosJsonDataProcessor {

    @Comment("1、实现 IFabosJsonDataService 接口")
    @Comment("2、将实现类通过：DataProcessorManager.register('数据源名称', FabosJsonDataServiceXxx.class); 方法注册")
    @Comment("3、value值为已注册 '数据源' 名称")
    String value();

}
