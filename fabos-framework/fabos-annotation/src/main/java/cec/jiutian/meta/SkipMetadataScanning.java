package cec.jiutian.meta;

import java.lang.annotation.ElementType;
import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;


/**
 * 声明此模型的元数据不需要被扫描, 而会被<code>canOnlyBeScannedBy</code>定义的组件扫描（如不定义<code>canOnlyBeScannedBy</code>则不被所有组件扫描到）
 *
 * <AUTHOR>
 * @time 2024-10-22 13:16
 */

@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Inherited
public @interface SkipMetadataScanning {

    String canOnlyBeScannedBy() default "";

}
