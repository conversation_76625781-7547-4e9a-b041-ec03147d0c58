package cec.jiutian.meta;

import lombok.Getter;

/**
 * <AUTHOR>
 * @time 2024-10-31 16:45
 */

@Getter
public enum FabosJsonFunctionSourceType {
    NATIVE_METHODS("内部方法", "01"),
    CUSTOMIZED_METHODS("自定义方法", "02"),
    FRONTEND_DESIGNER("前端设计器", "03"),
    JOB_METHODS("定时任务", "04");

    private final String name;

    private final String code;

    FabosJsonFunctionSourceType(String name, String code) {
        this.name = name;
        this.code = code;
    }
}
