package cec.jiutian.view;

import cec.jiutian.view.config.Comment;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;


@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
@Documented
public @interface InputGroup {

    /**
     * 前缀
     */
    String prefix() default "";

    /**
     * 后缀
     */
    String postfix() default "";
}
