package cec.jiutian.view.field.edit;

import cec.jiutian.view.config.Comment;

import java.beans.Transient;

/**
 *
 */
public @interface InputType {

    @Comment("Maximum input length")
    int length() default 255;

    String type() default "text";

    @Comment("Display the whole line")
    boolean fullSpan() default false;

    //@Transient
    @Comment("Regex the input value")
    String regex() default "";

    //正则表达式匹配不正确时前端的提示。如果没有配置，前端默认显示   '请输入正确的#{title}' 主要是某些需要后端提示输入规则，比如 密码
    String regexErrMsg() default "";

    @Transient
    @Comment("Automatically trim input value")
    boolean autoTrim() default true;

    VL[] prefix() default {};

    VL[] suffix() default {};
}
