package cec.jiutian.view.type;


import cec.jiutian.view.config.Comment;
import cec.jiutian.view.fun.FilterHandler;

import java.beans.Transient;

/**
 *
 * date 2018-11-05.
 */
public @interface Filter {

    @Transient
    @Comment("数据过滤表达式")
    String value() default "";

    @Transient
    @Comment("可被conditionHandler获取")
    String[] params() default {};

    @Transient
    @Comment("动态处理过滤条件")
    Class<? extends FilterHandler> conditionHandler() default FilterHandler.class;
}
