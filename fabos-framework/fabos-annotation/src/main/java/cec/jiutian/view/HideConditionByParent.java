package cec.jiutian.view;

import cec.jiutian.view.config.Comment;

/**
 * <AUTHOR>
 * @date ：
 */
public @interface HideConditionByParent {

    @Comment("表达式")
    String expr() default "";

    @Comment("字段列表，逗号分隔")
    String fields() default "";

    @Comment("作用范围")
    ScopeEnum scope() default ScopeEnum.BOTH;

    enum ScopeEnum {
        @Comment("作用于表格列")
        TABLE,
        @Comment("作用于表单")
        FORM,
        @Comment("作用于表格和表单")
        BOTH
    }


}
