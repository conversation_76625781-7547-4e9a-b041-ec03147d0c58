package cec.jiutian.view.fun;

import cec.jiutian.view.type.Power;
import lombok.Getter;
import lombok.Setter;

/**
 *
 * date 2020-08-07
 */
@Getter
@Setter
public class PowerObject {

    private boolean add = true;

    private boolean edit = true;

    private boolean delete = true;

    private boolean query = true;

    private boolean viewDetails = true;

    private boolean export = true;

    private boolean importable = true;

    private boolean examine = true;

    private boolean examineDetails = true;

    private boolean print = true;

    public PowerObject(Power power) {
        this.add = power.add();
        this.delete = power.delete();
        this.edit = power.edit();
        this.query = power.query();
        this.viewDetails = power.viewDetails();
        this.export = power.export();
        this.importable = power.importable();
        this.examine = power.examine();
        this.examineDetails = power.examineDetails();
        this.print = power.print();
    }

    public PowerObject() {
    }

    public PowerObject(boolean initialValue) {
        this.add = initialValue;
        this.edit = initialValue;
        this.delete = initialValue;
        this.query = initialValue;
        this.viewDetails = initialValue;
        this.export = initialValue;
        this.importable = initialValue;
        this.examine = initialValue;
        this.examineDetails = initialValue;
        this.print = initialValue;
    }

    /**
     * 为每一个字段都求and
     *
     * @param another 另一对象
     * @return this
     */
    public PowerObject and(PowerObject another) {
        this.add = this.add && another.isAdd();
        this.delete = this.delete && another.isDelete();
        this.edit = this.edit && another.isEdit();
        this.query = this.query && another.isQuery();
        this.viewDetails = this.viewDetails && another.isViewDetails();
        this.export = this.export && another.isExport();
        this.importable = this.importable && another.isImportable();
        this.examine = this.examine && another.isExamine();
        this.examineDetails = this.examineDetails && another.isExamineDetails();
        this.print = this.print && another.isPrint();
        return this;
    }
}
