<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <title>Permission authentication</title>
    <meta charset="UTF-8">
    <style>
        /* 设置页面样式 */
        body, html {
            margin: 0;
            padding: 0;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #f0f0f0; /* 背景色 */
            font-family: Arial, sans-serif;
        }

        /* 设置认证信息容器样式 */
        .auth-container {
            display: flex;
            align-items: center;
            font-size: 20px; /* 字体大小 */
            color: #333; /* 文字颜色 */
        }

        /* 设置 Logo 样式 */
        .auth-container img {
            width: 24px; /* Logo 大小与文字一致 */
            height: 24px;
            margin-right: 10px; /* Logo 与文字的间距 */
        }

        /* 隐藏表单 */
        #loginForm {
            display: none;
        }
    </style>
    <script type="text/javascript">
        window.onload = function() {
            // 自动填充用户名和密码
            /* document.getElementById('username').value = 'superUser';
            document.getElementById('password').value = 'start123'; */

            // 自动提交表单
            document.getElementById('loginForm').submit();
        };
    </script>
</head>
<body>
<!-- 认证信息容器 -->
<div class="auth-container">
    <!-- Logo -->
    <img src="/logo.png" alt="Logo" /> <!-- 替换为你的 Logo URL -->
    <!-- 认证文字 -->
    <h2>Authenticating...</h2>
</div>

<!-- 登录表单 -->
<form th:action="@{/login}" method="post" id="loginForm" hidden="hidden">
    <div>
        <label for="username">Username:</label>
        <input type="text" id="username" name="username" th:value="${username}" />
    </div>
    <div>
        <label for="password">Password:</label>
        <input type="password" id="password" name="password" th:value="${password}" />
    </div>
    <button type="submit">Login</button>
</form>
</body>
</html>