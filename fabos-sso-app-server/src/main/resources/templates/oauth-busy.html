<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Service Busy</title>
    <style>
        /* 设置页面样式 */
        body, html {
            margin: 0;
            padding: 0;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #f0f0f0; /* 背景色 */
            font-family: Arial, sans-serif;
        }

        /* 设置认证信息容器样式 */
        .auth-container {
            display: flex;
            align-items: center;
            font-size: 20px; /* 字体大小 */
            color: #333; /* 文字颜色 */
        }

        /* 设置 Logo 样式 */
        .auth-container img {
            width: 24px; /* Logo 大小与文字一致 */
            height: 24px;
            margin-right: 10px; /* Logo 与文字的间距 */
        }

    </style>
</head>
<body>
<!-- 认证信息容器 -->
<div class="auth-container">
    <!-- Logo -->
    <img src="/logo.png" alt="Logo" /> <!-- 替换为你的 Logo URL -->
    <!-- 提示文字 -->
    <h2>Service busy, try again later...</h2>
</div>
</body>
</html>