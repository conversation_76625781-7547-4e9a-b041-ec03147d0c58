app:
  id: xlx-ubp

nacos:
    addr: 127.0.0.1:8848
#  addr: 172.16.200.101:8848

spring:
  profiles:
    active: local
  application:
    name: ${app.id}
  cloud:
    nacos:
      server-addr: ${nacos.addr}
      username: xlx
      password: xlx0228
      discovery:
        enabled: true
        namespace: fabos-xlx
        group: ${spring.profiles.active}
      config:
        shared-configs:
          - data-id: common  # 公共配置
            group: ${spring.profiles.active}
            refresh: true
        enabled: true
        namespace: fabos-xlx
        group: ${spring.profiles.active}
        prefix: ${app.id}

logging:
  level:
    com:
      alibaba:
        cloud: debug

springdoc:
  swagger-ui:
    path: /swagger-ui.html
    operationsSorter: method
    tagsSorter: alpha
    display-request-duration: true
  api-docs:
    path: /v3/api-docs