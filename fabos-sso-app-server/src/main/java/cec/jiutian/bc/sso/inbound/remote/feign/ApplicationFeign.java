package cec.jiutian.bc.sso.inbound.remote.feign;

import cec.jiutian.bc.sso.service.ApplicationService;
import cec.jiutian.common.util.StringUtils;
import cec.jiutian.core.frame.constant.FabosJsonRestPath;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR>
 * @time 2025-03-19 15:58
 */

@RestController
@RequestMapping(FabosJsonRestPath.FABOS_REMOTE_API)
public class ApplicationFeign {
    private final ApplicationService applicationService;

    public ApplicationFeign(ApplicationService applicationService) {
        this.applicationService = applicationService;
    }

    @PostMapping("/checkIfSystemAvailableForUser")
    public Boolean checkIfSystemAvailableForUser(@RequestBody(required = true) Map<String, String> params) {
        if (StringUtils.isNotBlank(params.get("svsCode")) && StringUtils.isNotBlank(params.get("userName"))) {
            return applicationService.checkSystemAvailableForUser(params.get("svsCode"), params.get("userName"));
        }
        return false;
    }
}

