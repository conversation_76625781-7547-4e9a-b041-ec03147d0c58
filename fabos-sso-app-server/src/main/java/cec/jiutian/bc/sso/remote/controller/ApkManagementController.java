
package cec.jiutian.bc.sso.remote.controller;


import cec.jiutian.bc.sso.domain.apk.model.ApkManagement;
import cec.jiutian.bc.sso.domain.apk.repository.ApkManagementRepository;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.core.frame.module.R;
import cec.jiutian.core.frame.service.FabosJsonCoreService;
import cec.jiutian.core.frame.util.FabosJsonUtil;
import cec.jiutian.core.view.fabosJson.view.FabosJsonModel;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequestMapping("/fabos-api/apk-management")
public class ApkManagementController {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private ApkManagementRepository apkManagementRepository;

    @GetMapping("/getLatestRelease")
    public R<Object> getLatestRelease() {
        try {
            FabosJsonModel fabosJsonModel = FabosJsonCoreService.getFabosJson(ApkManagement.class.getSimpleName());
            Map<String, Object> objectMap = FabosJsonUtil.generateFabosJsonDataMap(fabosJsonModel, apkManagementRepository.getLatestRelease());
            return R.ok(objectMap);
        } catch (Exception e) {
            return R.errorWithTypedParam("获取最新版本失败: " + e.getMessage());
        }
    }

    ;
}

