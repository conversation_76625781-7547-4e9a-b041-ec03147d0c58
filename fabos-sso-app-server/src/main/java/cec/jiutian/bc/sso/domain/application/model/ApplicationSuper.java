package cec.jiutian.bc.sso.domain.application.model;

import cec.jiutian.bc.sso.domain.application.proxy.ApplicationProxy;
import cec.jiutian.bc.sso.domain.usergroup.model.UserGroup;
import cec.jiutian.bc.sso.enumeration.CategoryEnum;
import cec.jiutian.bc.sso.enumeration.YesOrNoEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.config.Comment;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.BoolType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DependFieldDisplay;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.field.edit.TabTableReferType;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.RowBaseOperation;
import cec.jiutian.view.type.RowOperation;
import jakarta.persistence.ConstraintMode;
import jakarta.persistence.ForeignKey;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.MappedSuperclass;
import lombok.Getter;
import lombok.Setter;

import java.util.Set;

@FabosJson(
        name = "应用门户",
        orderBy = "Application.createTime desc",
        dataProxy = ApplicationProxy.class,
        filter = @Filter(value = "ubpFlag = false"),
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "edit"
                ),
                @RowBaseOperation(
                        code = "delete"
                ),
        },
        rowOperation = {
                @RowOperation(
                        code = "Application@JUMP",
                        mode = RowOperation.Mode.SINGLE,
                        menuButtonType = RowOperation.MenuButtonTypeEnum.JUMP,
                        callHint = "确定跳转到应用页面吗？",
                        title = "跳转到应用",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "Application@JUMP"
                        )
                )
        }
)
@Getter
@Setter
@MappedSuperclass
public class ApplicationSuper extends MetaModel {

    @FabosJsonField(
            views = @View(title = "应用名称"),
            edit = @Edit(title = "应用名称", search = @Search(), notNull = true, inputType = @InputType(length = 40))
    )
    private String name;

    @FabosJsonField(
            views = @View(title = "系统编码"),
            edit = @Edit(title = "系统编码", inputType = @InputType(length = 40))
    )
    private String systemCode;

    @FabosJsonField(
            views = @View(title = "是否生效"),
            edit = @Edit(title = "是否生效",
                    notNull = true,
                    defaultVal = "Y",
                    choiceType = @ChoiceType(fetchHandler = YesOrNoEnum.class)
            )
    )
    private String validFlag;

    @FabosJsonField(
            views = @View(title = "服务编码"),
            edit = @Edit(title = "服务编码", tips = "与目标服务配置中的spring.application.name或app.id一致",
                    inputType = @InputType(length = 40))
    )
    private String serviceCode;

    @FabosJsonField(
            views = @View(title = "应用类别"),
            edit = @Edit(title = "应用类别", notNull = true, type = EditType.CHOICE,
                    search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = CategoryEnum.class))
    )
    private String category;

    @FabosJsonField(
            views = @View(title = "应用图标"),
            edit = @Edit(title = "应用图标", type = EditType.ATTACHMENT, notNull = true)
    )
    private String icon;

    @FabosJsonField(
            views = @View(title = "客户端ID"),
            edit = @Edit(title = "客户端ID", show = false
            )
    )
    private String clientId;

    @Comment("后端自动生产UUID")
    @FabosJsonField(
            views = @View(title = "客户端密钥"),
            edit = @Edit(title = "客户端密钥", show = false)
    )
    private String clientSecret;

        @FabosJsonField(
            views = @View(title = "重定向URI"),
            edit = @Edit(title = "重定向URI", notNull = true,
                    desc = "应用的URL，用于单点登录跳转")
    )
    private String homeUrl;

    @FabosJsonField(
            views = @View(title = "OAuth2回调地址"),
            edit = @Edit(title = "OAuth2回调地址", notNull = true,
                    desc = "OAuth2回调地址，用于接收授权码",
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "category == 'External'"))
    )
    private String redirectUri;

//    @FabosJsonField(
//            views = @View(title = "应用日志URI"),
//            edit = @Edit(title = "应用日志URI", notNull = true)
//    )
//    private String logUri;

    @FabosJsonField(
            views = @View(title = "应用描述", toolTip = true),
            edit = @Edit(title = "应用描述", inputType = @InputType(), type = EditType.TEXTAREA)
    )
    private String description;

    @FabosJsonField(
            views = @View(title = "令牌有效期(分)"),
            edit = @Edit(title = "令牌有效期(分)", type = EditType.NUMBER, notNull = true, defaultVal = "30L",
                    desc = "访问令牌的有效期，单位：分",
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "category == 'External'")

            )
    )
    private Long accessTokenTime;

    @FabosJsonField(
            views = @View(title = "刷新令牌有效期(天)"),
            edit = @Edit(title = "刷新令牌有效期(天)", type = EditType.NUMBER, notNull = true, defaultVal = "7L",
                    desc = "刷新令牌的有效期，单位：天",
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "category == 'External'"))
    )
    private Long refreshTokenTime;

    @FabosJsonField(
            views = @View(title = "是否重用刷新令牌"),
            edit = @Edit(title = "是否重用刷新令牌", type = EditType.BOOLEAN, notNull = true, defaultVal = "true",
                    desc = "是否重用刷新令牌",
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "category == 'External'"))
    )
    private Boolean reuseRefreshTokens;

    @ManyToMany
    @JoinTable(
            name = "fd_application_with_group",
            inverseForeignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT),
            joinColumns = @JoinColumn(name = "application_id", referencedColumnName = "id"),
            inverseJoinColumns = @JoinColumn(name = "group_id", referencedColumnName = "id")
    )
    @FabosJsonField(
            views = @View(title = "用户组", column = "name", type = ViewType.TABLE_VIEW),
            edit = @Edit(title = "用户组",
                    notNull = false,
                    type = EditType.TAB_TABLE_REFER,
                    search = @Search(),
                    tabTableReferType = @TabTableReferType(type = TabTableReferType.SelectShowTypeMTM.LIST)
            )
    )
    private Set<UserGroup> userGroupSet;

    /**
     * 此字段为true，在应用门户和我的应用中均不回返回，且checkIfSystemAvailableForUser方法会返回true，即所有用户均可访问此项为true的系统（一般用于基础设施服务）
     */
    @Comment("标记当前系统为基础平台，查询时不返回")
    @FabosJsonField(
            views = @View(title = "基础平台标识", show = false),
            edit = @Edit(title = "基础平台标识", show = false)
    )
    private Boolean ubpFlag;

    @Comment("标记当前系统包含移动端")
    @FabosJsonField(
            views = @View(title = "标记当前系统包含移动端", show = false),
            edit = @Edit(title = "标记当前系统包含移动端", show = true,
                    type = EditType.BOOLEAN, boolType = @BoolType(trueText = "是", falseText = "否")
            ))
    private Boolean mobileFlag;

    @Comment("不支持应用权限管控的应用，此处配置为否，则在应用权限配置处不展示")
    @FabosJsonField(
            views = @View(title = "是否支持应用权限管控"),
            edit = @Edit(title = "是否支持应用权限管控", type = EditType.BOOLEAN, boolType = @BoolType(trueText = "是", falseText = "否"))
    )
    private Boolean authManageFlag;

}
