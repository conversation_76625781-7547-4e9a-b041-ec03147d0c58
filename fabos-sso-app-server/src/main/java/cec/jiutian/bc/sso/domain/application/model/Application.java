package cec.jiutian.bc.sso.domain.application.model;

import cec.jiutian.bc.sso.domain.application.proxy.ApplicationProxy;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.type.Filter;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

@FabosJson(
        name = "应用门户",
        dataProxy = ApplicationProxy.class,
        orderBy = "Application.createTime desc",
        filter = @Filter(value = "ubpFlag = false"))
@Entity
@Getter
@Setter
@Table(name = "fd_application",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"code"})
        }
)
public class Application extends ApplicationSuper {
}
