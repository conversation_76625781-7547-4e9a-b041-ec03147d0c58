FROM hub.fabos.com:8090/fabos/openjdk:17-jdk-alpine-with-fonts
LABEL maintainer="<EMAIL>"

#RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories && \
#    apk update && \
#    apk add --no-cache fontconfig ttf-dejavu
RUN apk add --no-cache eudev-libs

COPY target/fabos-sso-app-server-3.2.2-SNAPSHOT.jar /fabos-sso-app.jar

ENV PARAMS=""

ENTRYPOINT ["sh","-c","java $PARAMS -jar /fabos-sso-app.jar" ]
