#!/usr/bin/env bash

# 如果需要同时发布 dev 和 fat 环境，且为类 unix 系统，则可使用此脚本

# 构建项目，如果已经执行过 mvn install，则可以注释
# 请注意，如果 ~/.m2/settings.xml 不是项目settings，需要手工指定 -s 参数

#mvn clean package \
#--update-snapshots \
#-Dmaven.test.skip=true \
#-s ~/.m2/settings.xml \
#-f ../pom.xml &&


# 构建镜像
docker build --platform linux/amd64 \
-t hub.fabos.com:8090/fabos/fabos-sso-app:3.2.1-dev \
-t hub.fabos.com:8090/fabos/fabos-sso-app:3.2.1-fat . &&

# 推送镜像
docker push hub.fabos.com:8090/fabos/fabos-sso-app:3.2.1-dev &&
docker push hub.fabos.com:8090/fabos/fabos-sso-app:3.2.1-fat

# 使用 ctrl-c 以中断操作
