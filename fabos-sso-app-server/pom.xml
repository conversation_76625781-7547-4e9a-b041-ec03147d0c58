<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cec.jiutian</groupId>
        <artifactId>fabos-sso-app</artifactId>
        <version>3.2.2-SNAPSHOT</version>
    </parent>
    <version>3.2.2-SNAPSHOT</version>
    <artifactId>fabos-sso-app-server</artifactId>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
       </properties>

    <dependencies>
        <dependency>
            <groupId>org.apache.seata</groupId>
            <artifactId>seata-spring-boot-starter</artifactId>
            <version>${seata.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.antlr</groupId>
                    <artifactId>antlr4</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>guava</artifactId>
                    <groupId>com.google.guava</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>cec.jiutian</groupId>
            <artifactId>fabos-component-manage</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>cec.jiutian</groupId>
            <artifactId>fabos-faas</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>cec.jiutian</groupId>
            <artifactId>fabos-component-core</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>cec.jiutian</groupId>
            <artifactId>fabos-data-jpa</artifactId>
            <version>${project.version}</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>cec.jiutian</groupId>
            <artifactId>fabos-cmp-urm-biz</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>cec.jiutian</groupId>
            <artifactId>fabos-cmp-job-biz</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>cec.jiutian</groupId>
            <artifactId>fabos-cmp-calllog-biz</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>cec.jiutian</groupId>
            <artifactId>fabos-cmp-file-biz</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>cec.jiutian</groupId>
            <artifactId>fabos-cmp-ecs-biz</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>cec.jiutian</groupId>
            <artifactId>fabos-cmp-workflow-biz</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>cec.jiutian</groupId>
            <artifactId>fabos-ie</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!--指定spring security版本-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
            <version>3.4.2</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.30</version>
        </dependency>

        <!--指定oauth2 server版本-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-oauth2-authorization-server</artifactId>
        </dependency>

        <dependency>
            <groupId>com.nimbusds</groupId>
            <artifactId>nimbus-jose-jwt</artifactId>
            <version>9.37.1</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.oshi</groupId>
            <artifactId>oshi-core</artifactId>
            <version>6.6.5</version>
        </dependency>
        <dependency>
            <groupId>net.java.dev.jna</groupId>
            <artifactId>jna</artifactId>
            <version>5.16.0</version>
        </dependency>
        <dependency>
            <groupId>net.java.dev.jna</groupId>
            <artifactId>jna-platform</artifactId>
            <version>5.16.0</version>
        </dependency>
        <dependency>
            <groupId>com.konghq</groupId>
            <artifactId>unirest-java</artifactId>
            <version>3.13.2</version>
        </dependency>

    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>repackage</id>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                        <configuration>
                            <!--	fix https://github.com/koupleless/koupleless/issues/161		-->
                            <loaderImplementation>CLASSIC</loaderImplementation>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <!--            为了让三方依赖和 koupleless 模式适配，需要引入以下构建插件-->
            <plugin>
                <groupId>com.alipay.sofa.koupleless</groupId>
                <artifactId>koupleless-base-build-plugin</artifactId>
                <version>2.1.1</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>add-patch</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
