# 下拉表格单选

@ReferenceTableType -> type = Type.DROPDOWN_TABLE

需实现ColumnTitleHandler，以设置表格中需展示的列的中文名称

![image_36.png](image_36.png)

```java
@ManyToOne
@FabosJsonField(
        views = @View(title = "英雄", column = "name"),
        edit = @Edit(title = "英雄",
                type = EditType.REFERENCE_TABLE,
                //referenceTableType注意与多对多的属性tabTableReferType配置区分
                referenceTableType = @ReferenceTableType(type = ReferenceTableType.SelectShowTypeMTO.DROPDOWN_TABLE,
                paramsHandler = TesterHandler3.class                
                )
        )
)
private Hero hero;

class TesterHandler3 implements ColumnTitleHandler {
    @Override
    public List<ColumnAndTitle> handle() {
        return List.of(new ColumnAndTitle("materialCode", "编码"),
                new ColumnAndTitle("name", "物料名称"));
    }
}

```