# 通用工具模块 fabos-common

## 模块定义

模块公用依赖，通用方法工具类存放的模块。



## 模块及工具类使用说明

系统公共module。里面封装了一些简单的工具类和异常处理；另外还有一些链接文件服务器的工具类。该module不需要依赖其他module。所有的module都可以依赖该module。

### 文件服务工具类使用说明

工具类：FileOperationUtil  

配置类：FileOperationConfig

**配置说明**

使用前请先在springboot配置文件中配置以下属性，不配置则默认为配置类里默认配置

```java
@Configuration
@Data
public class FileOperationConfig {
    //文件服务部署地址
    @Value("${file.remote-addr:172.16.200.86:8009}")
    private String remoteAddr;
    //文件要存储的文件夹
    @Value("${file.upload-path:/fabos}")
    private String filePath;
    //系统编码
    @Value("${file.source-code:fabos}")
    private String sourceCode;
}
```

**工具类使用说明**

```java
@Slf4j
public class FileOperationUtil {
    /**
     * 单个文件上传
     *
     * @param file 上传的文件
     * @param functionFolder 某个功能存放文件的文件夹
     * @return
     * @throws Exception
     */
    public static String uploadFile(File file, String functionFolder) throws Exception 

    /**
     *
     * 批量文件上传
     *
     * @param files 上传的文件列表
     * @param functionFolder 某个功能的文件夹
     * @return 文件uuid列表
     * @throws Exception
     */
    public static List<String> uploadFiles(File[] files, String functionFolder) throws Exception {}

    /**
     * 下载文件
     *
     * @param uuid 文件uuid
     * @param destFile 要存放的目标文件
     */
    public static void download(String uuid, File destFile, int timeout) {}
    
      /**
     * 对文件进行重命名
     * @param uuid 文件uuid
     * @param newName 新的文件名
     * @return boolean 是否成功
     * @throws Exception
     */
    public static boolean changeFilename(String uuid, String newName) throws Exception { }

     /**
     * 使用多个uuid批量删除
     * @param uuids
     * @return boolean 是否成功
     * @throws Exception
     */
    public static boolean deleteBatchFile(List<String> uuids) throws Exception{}
    
    /**
     * 使用单个uuid删除
     * @param uuid
     * @return boolean 是否成功
     * @throws Exception
     */
    public static boolean deleteFileById(String uuid) throws Exception {}
}
```



**文件服务适配Erupt**

erupt提供的接入相关文档：[https://www.erupt.xyz/#!/doc](https://www.erupt.xyz/#!/doc)    搜索：文件上传 。 自定义文件上传：[https://www.yuque.com/erupts/erupt-back/famk6i](https://www.yuque.com/erupts/erupt-back/famk6i)

第一步：在启动类上添加@FabosJsonAttachmentUpload(FileProxy.class) 。 FileProxy需要自己实现。

```java
@Slf4j
@SpringBootApplication
@FabosJsonScan
@FabosJsonAttachmentUpload(FileProxy.class) 需要上传文件时使用
public class CmpUrmApp{
  ……
}
```

第二步：实现FileProxy

```java
@Service
public class FileProxy implements AttachmentProxy {

    @Override
    public String upLoad(MultipartFile file, String path) {
        File tempFile = null;
        try {
            tempFile = File.createTempFile(UUID.randomUUID().toString(), file.getOriginalFilename());
            file.transferTo(tempFile);
            return FileOperationUtil.uploadFile(tempFile, path);
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            if (tempFile != null && tempFile.exists()) {
                tempFile.delete();
            }
        }
    }

    /**
     *  注意使用完成destFile后记得删除
     **/
    @Override
    public File downLoad (String path){
        File destFile = new File("your_file_name.xxx");
        FileOperationUtil.download("fc9c8131-2762-439f-b4db-f44bce70017b",destFile , 10000);
        return destFile;
    }
    
    @Override
    public String fileDomain() {
        return "";
    }

    @Override
    public boolean isLocalSave() {
        return false;
    }
}
```



第三步：在相关类中设置文件地址存储字段，详情请看erupt相关文档 [https://www.erupt.xyz/#!/doc](https://www.erupt.xyz/#!/doc)  

第四步：在通用上传接口中进行测试。通用接口controller：cec.jiutian.model.core.controller.FabosFileController

```java
@SneakyThrows
@PostMapping("/upload/{fabosJson}/{field}")
public BaseApi upload(@PathVariable("fabosJson") String fabosJsonName, @PathVariable("field") String fieldName, @RequestParam("file") MultipartFile file) {
    …………
}
```

文件下载接口

```java
@GetMapping(value = DOWNLOAD_PATH + "/**", produces = {MediaType.APPLICATION_OCTET_STREAM_VALUE})
public void downloadAttachment(HttpServletRequest request, HttpServletResponse response) throws IOException {
    ………………
}
```
