# 一对多表引用(O2M)

##### 概述
用于OneToMany类型，且Many子模型中的数据来源于外部模型。通过弹窗展示外部模型列表，并多选数据后，在后端通过数据映射，并返回前端，构造出Many子模型数据。

##### 使用要点
1. @Edit
   * type = EditType.TAB_REFER_ADD
2. @referenceAddType
   * referenceClass = "SplitModelDto"， 外部模型名称
   * editable = {"qty"}， 可编辑列
   * queryCondition = "{\"currentStatus\":\"ACTIVE\"}"， 前端将filter拼接到查询condition中，需使用json格式
   * queryCondition = "{\"warehouseId\":\"${warehouseid}\"}"
   * referenceAddHandler = SplitModelp3ReferAddHandler.class，处理数据映射的方法
   * filter = "purchaseContract.examineStatus = '1' and purchaseContract.supplier.id = '${supplier.id}'"
   ~~* reset = "fieldA,fieldB"~~
3. @View
   * extraPK = "extraId"，自身子模型用来记录外部模型ID字段值的字段.在handler方法中，需要将值存入

#### 实现效果
![image_25.png](image_25.png)

* 点击新增后，弹窗选择外部模型数据

![image_23.png](image_23.png)

* 确定后，将数据带回到Many表格中

![image_24.png](image_24.png)

#### 实现示例（结合传输模型DTO实现）
1. 定义传输模型
使用传输模型，作为来源数据
```
@Data
@FabosJson(name = "库存台账",
        power = @Power(add = false,edit = false,delete = false)
)
@FabosJsonI18n
//@CustomHql(hql = "select new map (CONCAT(u.id, '+', r.id) as id,u.name as userName, r.name as roleName,u.state as state) from User u left join u.roles r")
@CustomHql(hql = "select new map (u.id as id,u.name as userName, r.name as roleName,u.state as state) from User u left join u.roles r")
public class SplitModelDto {

    @FabosJsonField(
            views = @View(title = "id"),
            edit = @Edit(title = "id"),
            customHqlField = "u.id"
    )
    @Id
    private String id;
    @FabosJsonField(
            views = @View(title = "用户姓名"),
            edit = @Edit(title = "用户姓名", search = @Search()),
            customHqlField = "u.name"
    )
    private String userName;
    @FabosJsonField(
            views = @View(title = "角色名称"),
            edit = @Edit(title = "角色名称", search = @Search()),
            customHqlField = "r.name"
    )
    private String roleName;

    @FabosJsonField(
            views = @View(title = "用户状态"),
            edit = @Edit(title = "状态", search = @Search(defaultVal = "Y"),
                    defaultVal = "Y",
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = {SwitchStatus.ChoiceFetch.class})),
            customHqlField = "u.state"
    )
    private String state;

}
```
2. 定义Many子模型，用来记录外部模型所选择的数据
```
@FabosJson(
        name = "组合模型p3"
)
@Table(name = "split_model_p3"
)
@Entity
@Getter
@Setter
public class SplitModelP3 extends MetaModel {
    @FabosJsonField(
            views = @View(title = "用户姓名"),
            edit = @Edit(title = "用户姓名", search = @Search())
    )
    private String userName;
    @FabosJsonField(
            views = @View(title = "角色名称"),
            edit = @Edit(title = "角色名称", search = @Search())
    )
    private String roleName;

    @FabosJsonField(
            views = @View(title = "用户状态"),
            edit = @Edit(title = "状态", search = @Search(defaultVal = "Y"),
                    defaultVal = "Y",
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = {SwitchStatus.ChoiceFetch.class}))
    )
    private String state;

    @FabosJsonField(
            edit = @Edit(title = "数量", notNull = false,
                    type = EditType.NUMBER)
    )
    private Integer qty;

    @FabosJsonField(
            views = @View(title = "用户DTO唯一标识", show = false),
            edit = @Edit(title = "用户DTO唯一标识", show = false)
    )
    private String extraId;

}
```
3. 在主模型中配置OneToMany子模型，类型设置为TAB_REFER_ADD
```
@OneToMany(cascade = CascadeType.ALL,orphanRemoval = false)
@OrderBy
@FabosJsonField(
        edit = @Edit(title = "引用添加的子项明细", type = EditType.TAB_REFER_ADD),
        referenceAddType = @ReferenceAddType(referenceClass = "SplitModelDto", editable = {"qty"}, //前端将filter拼接到查询condition中，需使用json格式
                referenceAddHandler = SplitModelp3ReferAddHandler.class),
        views = @View(title = "引用添加的子项明细", type= ViewType.TABLE_VIEW, extraPK = "extraId") // extraPK：自身子模型用来记录外部模型ID字段值的字段.在handler方法中，需要将值存入
)
private List<SplitModelP3> splitModelP3List;
```
4. 在handler中处理数据映射，并返回前端
```
@Component
public class SplitModelp3ReferAddHandler implements ReferenceAddType.ReferenceAddHandler<SplitModelFull, SplitModelDto> {
    @Resource
    private JpaCrud jpaCrud;

    @Override
    public Map<String, Object> handle(SplitModelFull splitModelFull, List<SplitModelDto> splitModelDtoList) {
        Map<String, Object> result = new HashMap<>();
        List<SplitModelP3> splitModelP3List = new ArrayList<>();
        if ( CollectionUtils.isNotEmpty(splitModelDtoList)) {
            splitModelDtoList.forEach(c -> {
                SplitModelP3 splitModelP3 = new SplitModelP3();
                splitModelP3.setExtraId(c.getId());
                splitModelP3.setUserName(c.getUserName());
                splitModelP3.setRoleName(c.getRoleName());
                splitModelP3.setState(c.getState());
                splitModelP3List.add(splitModelP3);
            });
            result.put("splitModelP3List", splitModelP3List);
        }
        return result;
    }
}
```