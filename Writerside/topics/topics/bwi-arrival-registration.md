# 物资到货登记

## 领域模型
1.物资到货登记（主模型）

物资到货登记为主子表结构，共三层，提供创建、修改、删除、审批、紧急调整等功能。

其中创建、修改功能根据检验方式的不同，提供不同的模型页面和操作逻辑。

| 字段名称   | 唯一键 | 数据类型   | 枚举 | 关联模型     | 关联方式 | 删除方法           | 说明                  |
|--------|-----|--------|----|----------|------|----------------|---------------------|
| 到货单号   | AK1 | String |    |          |      |                |                     |
| 物资收货计划 |     |        |    | ReceivePlanMTO |  ManyToOne    |                |                     |
| 检验方式   |     | String |  InspectTypeEnum  |          |      |                | 下厂检验、到厂检验、转合格证、现场确认 |
| 供应商    |     |        |    |    Supplier      |   ManyToOne   |                |                     |
| 货车吨位   |     | String |    |          |      |                |                     |
| 车牌号    |     | String |    |          |      |                |                     |
| 货位包装   |     | String |    |          |      |                |                     |
| 到货日期   |     | Date   |    |          |      |                |                     |
| 当前状态   |     | String |  OrderCurrentStateEnum  |          |      |                |                     |
| 处理状态   |     | String |  ProcessStateEnum  |          |      |                |                     |
| 备注     |     | String |    |          |      |                |                     |


2.到货明细 detail（第二级）

| 字段名称   | 唯一键 | 数据类型   | 枚举            | 关联模型                 | 关联方式      | 删除方法 | 说明 |
|--------|-----|--------|---------------|----------------------|-----------|------|----|
| 到货明细编号 |     | String |               |                      |           |      |    |
| 顺序号    |     | String |               |                      |           |      |    |
| 到货单id  |  | String |               |                      |           |      |    |
| 收货明细id |     | String |               | ReceivePlanDetailMTO | ManyToOne |      |    |
| 物料名称   |     | String |               |                      |           |      |    |
| 物料编码   |     | String |               |                      |           |      |    |
| 品号     |     | String |               |                      |           |      |    |
| 规格     |     | String |               |                      |           |      |    |
| 用途     |     | String |               |                      |           |      |    |
| 类别     |     | String | StockTypeEnum |                      |           |      |    |
| 子类别    |     | String | ProductEnum   |                      |           |      |    |
| 技术要求   |     | String |               |                      |           |      |    |
| 单价     |     | Float  |               |                      |           |      |    |
| 单位     |     | String |               |                      |           |      |    |
| 到货数量   |     | Float  |               |                      |           |      |    |


3.到货批次 lot（第三级）

| 字段名称   | 唯一键 | 数据类型   | 枚举 | 关联模型                 | 关联方式      | 删除方法 | 说明 |
|--------|-----|--------|----|----------------------|-----------|------|----|
| 到货明细id |     | String |    |                      |           |      |    |
| 本厂批号   |     | String |    |                      |           |      |    |
| 物料名称   |     | String |    |                      |           |      |    |
| 物料编码   |     | String |    |                      |           |      |    |
| 规格     |     | String |    |                      |           |      |    |
| 原厂批号   |     | String |    |                      |           |      |    |
| 原厂合格证  |     | String |    |                      |           |      |    |
| 单位     |     | String |    |                      |           |      |    |
| 生产单位   |     | String |    |                      |           |      |    |
| 批次数量   |     | Float  |    |                      |           |      |    |
| 生产日期   |     | Date   |    |                      |           |      |    |
| 有效日期   |     | Date   |    |                      |           |      |    |


4.物资到货登记-下厂检验

针对检验方式为下厂检验类型的收货计划，进行到货登记单创建和编辑

5.物资到货登记-进厂检验

针对检验方式为到厂检验、转合格证、现场确认类型的收货计划，进行到货登记单创建和编辑

## 领域服务
**1.下厂检验到货登记（自定义按钮）** 

使用“物资到货登记-下厂检验”模型，进行相关数据录入，点击确认后创建到货登记单，此时明细和批次都来源前置单据。

（1）收货计划过滤条件为：检验方式“下厂检验”，单据状态“执行中”，处理状态“未到货”或者“到货中”

（2）到货明细detail表（第二级）的数据来源为选中的收货计划的收货明细detail（第二级）

  选择收货明细后，其相关数据带入到到货明细中，到货数量为收货明细中的审定订货数量，批次数据也全部带入，针对一笔收货明细做一次性到货

（3）创建成功后，会将此次到货数量同步给收货计划，由收货计划那边累计到货数量并与收获计划数量比对后更新单据处理状态。

**2.进厂检验到货登记（自定义按钮）**

使用“物资到货登记-进厂检验”模型，进行相关数据录入，点击确认后创建到货登记单，此时明细来源于前置单据，批次信息在到货登记功能做新增（即拆批）。

（1）收货计划过滤条件为：检验方式“进厂检验”、“转合格证”或者“现场确认”，单据状态“执行中”，处理状态“未到货”或者“到货中”

（2）到货明细detail表（第二级）的数据来源为选中的收货计划的收货明细detail（第二级）

选择收货明细后，其相关数据带入到到货明细中，没有批次数据。到货数量可修改，批次信息需要新增。针对一笔收货明细可做多次到货

（3）创建成功后，会将此次到货数量同步给收货计划，由收货计划那边累计到货数量并与收获计划数量比对后更新单据处理状态。

**3.编辑-下厂检验（自定义按钮）**

检验方式为“下厂检验”、单据状态为“开立”的到货登记单可使用。处理逻辑同“下厂检验到货登记”功能。收货计划不可修改。

**4.编辑-进厂检验（自定义按钮）**

检验方式为“进厂检验” “转合格证”或者“现场确认”、单据状态为“开立”的到货登记单可使用。处理逻辑同“进厂检验到货登记”功能。收货计划不可修改。

**5.紧急调整（自定义按钮）**

（1）检验方式为“进厂检验”，审批状态为“审批通过”的到货登记单可使用。

（2）可对到货批次进行增、删、改操作，并将操作后的批次信息同步到对应请验单中。

新增的批次，要自动创建请验单；修改/删除的批次要先检验对应的请验单的证书开具状态是否为“已开具”，是则校验失败，此批次不可修改/删除，否则更新/删除该批次的请验单信息。

6.发起审批

（1）审批前会校验到货明细和到货批次是否有数据，无则校验失败。

（2）审批通过后，检验方式为“进厂检验”、“转合格证”类型的到货登记单会自动创建请验单，并更新自己的处理状态为“请验中”

7.查看审批详情

8.删除

删除后同步更新收货计划的累计到货数量。

## 端口 (port)
1. 更新收货明细，用于到货登记单增删改时，对收货计划明细中的到货数量进行更新。

