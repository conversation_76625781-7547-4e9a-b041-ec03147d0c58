# 跨组件调用示例

## 说明
* A组件调用B组件的方法时，B组件方法卸载remote的rpc中，A的调用方法写在port的client中

### B的被调用方法
```
@Component
public class ArrivalGoodsRpc {


    @Resource
    private ArrivalGoodsService arrivalGoodsService;

    @Resource
    private NamingRuleService namingRuleService;

    public String createIqc(String code, String params) {
        if (StringUtils.isNotBlank(params)){
            JSONObject jsonObject = JSONObject.parseObject(params);
            Iqc iqc = new Iqc();
            if(InspectTypeEnum.Enum.ToSupplier.name().equals(code)) {
                iqc.setInspectType(InspectTypeEnum.Enum.ToSupplier.name());
            } else {
                iqc.setInspectType(InspectTypeEnum.Enum.ArriveFactory.name());
            }
            iqc.setMaterialCode(jsonObject.getString("materialName"));
            iqc.setMaterialCode(jsonObject.getString("materialCode"));
            iqc.setBrandCode(jsonObject.getString("brandCode"));
            iqc.setMaterialSpecification(jsonObject.getString("materialSpecification"));
            iqc.setReceivedQuantity((Double) jsonObject.get("quantity"));
            iqc.setMeasureUnit(jsonObject.getString("measureUnit"));
            iqc.setSupplier(jsonObject.getString("serialLotId"));
            iqc.setOriginLotId(jsonObject.getString("originLotId"));
            iqc.setQuantity((Double) jsonObject.get("lotQuantity"));
            iqc.setCurrentState(IqcCurrentStateEnum.Enum.EDIT.name());
            iqc.setNumber(namingRuleService.getNameCode(NamingRuleCodeEnum.IqcNumber.name(), 1, null).get(0));
            iqc.setCreateTime(LocalDateTime.now());
            List<Iqc> iqcList = new ArrayList<>();
            iqcList.add(iqc);
            arrivalGoodsService.createIqc(iqcList);
            return "success";
        }
        return "";
    }
}
```

### A的调用方法
```
@Component
public class CreateIqcRpc {

    @Resource
    private DubboGenericAPI dubboGenericAPI;

    public Object planToIqc(ToSupplierInspectMaterial inspectMaterial, ToSupplierInspectMaterialLot materialLot) {
        InvokeDTO invokeDTO = new InvokeDTO();
        Object[] args = new Object[2];
        args[0] = InspectTypeEnum.Enum.ToSupplier.name();
        Map<String, Object> result = new HashMap<>();
        result.put("receivePlanCode",inspectMaterial.getReceivePlanCode());
        result.put("detailCode",inspectMaterial.getReceivePlanDetail().getReceivePlanDetailCode());
        result.put("materialName", inspectMaterial.getMaterialName());
        result.put("materialCode", inspectMaterial.getMaterialCode());
        result.put("brandCode", inspectMaterial.getBrandCode());
        result.put("materialSpecification", inspectMaterial.getMaterialSpecification());
        result.put("wl09", inspectMaterial.getWl09());
        result.put("quantity", inspectMaterial.getQuantity());
        result.put("price", inspectMaterial.getPrice());
        result.put("measureUnit", inspectMaterial.getMeasureUnit());
        result.put("serialLotId", materialLot.getSerialLotCode());
        result.put("originLotId", materialLot.getSourceLotCode());
        result.put("lotQuantity", materialLot.getLotQuantity());
        args[1] = JSONObject.toJSONString(result);
        invokeDTO.setArgs(args);
        invokeDTO.setGroup("fabos-cmp-ag");
        String[] argsType = {"java.lang.String", "java.lang.String"};
        invokeDTO.setArgsType(argsType);
        invokeDTO.setBizName("fabos-cmp-ag");
        invokeDTO.setClassFullName("cec.jiutian.bc.ag.remote.rpc.ArrivalGoodsRpc");
        invokeDTO.setMethodName("createIqc");
        invokeDTO.setModuleVersion("3.2.1-SNAPSHOT");
        return dubboGenericAPI.doInvoke(invokeDTO);
    }
}
```