# 枚举(String)

```Java
@Match("#item.type().toString()=='CHOICE'")
ChoiceType choiceType() default @ChoiceType;

@ChoiceType -> type = Type.SELECT (default)
```


## 枚举的两种使用方式
1. 系统枚举
* 系统枚举影响业务执行逻辑，不允许修改，通过枚举类定义在代码中，使用方式如下
  * 方式1：使用inplace枚举定义
```Java
    @FabosJsonField(
            views = @View(title = "分类"),
            edit = @Edit(title = "分类", search = @Search,
                    type = EditType.CHOICE,
                    notNull = true,
                    choiceType = @ChoiceType(vl = {@VL(label = "系统组件", value = "CORE"), @VL(label = "业务组件", value = "BIZ")}))
    )
    @SubTableField
    private String type;
```
    * 方式2：使用枚举class
```Java
    @FabosJsonField(
            views = @View(title = "性别"),
            edit = @Edit(title = "性别", search = @Search,
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = {GenderEnum.ChoiceFetch.class}))
    )
    private String gender;
```

2. 业务枚举
* 业务枚举不影响业务执行逻辑，允许修改，定义在数据字典表中
* 业务枚举在数据字典基础数据配置完成后，使用如下方式配置
  * fetchHandler 统一使用 DictChoiceFetchHandler.class
  * fetchHandlerParams 设置为数据字典编码
```Java
    @FabosJsonField(
            edit = @Edit(title = "收货类型", notNull = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(
                            fetchHandler = DictChoiceFetchHandler.class,
                            fetchHandlerParams = "Message@messageStatus"
                    )
            )
    )
    private String stockInType;
```