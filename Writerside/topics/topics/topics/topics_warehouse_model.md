# 仓储区域

##  领域模型  (domain-model)
1. 仓库（主模型）

仓储区域的根模型，定义仓库模型并聚合库区、库位，对外提供仓库相关增删改查功能。

| 字段名称 | 唯一键 | 数据类型   | 枚举     | 关联模型 | 关联方式 | 删除方法           | 说明                     |
|------|-----|--------|--------|------|------|----------------|------------------------|
| 仓库编码 | AK1 | String |        |      |      |                |                        |
| 仓库名称 |     | String |        |      |      |                |                        |
| 描述   |     | String |        |      |      |                |                        |
| 类型   |     | String | 仓库类型枚举 |      |      |                | 原材料库火工品库、化学品库、半成品库、成品库 |
| 保管员  |     | String |        |      |      |                |                        |
| 状态   |     | String | 仓库状态枚举 |      |      |                | 正常、锁定                  |

2. 库区（子模型）

定义库区，提供库区相关增删查改功能。

| 字段名称    | 唯一键 | 数据类型    | 枚举     | 关联模型 | 关联方式 | 说明      |
|---------|-----|---------|--------|------|------|---------|
| 所属仓库GID |     | String  |        | 仓库建模 | M2O  | 反向关联主模型 |
| 库区编码    | AK  | String  |        |      |      |         |
| 库区名称    |     | String  |        |      |      |         |
| 描述      |     | String  |        |      |      |         |
| 类型      |     | String  | 库区类型枚举 |      |      |         |
| 状态      |     | String  | 仓库状态枚举 |      |      | 正常、锁定   |
| 列数      |     | Integer |        |      |      |         |
| 行数      |     | Integer |        |      |      |         |

3. 库位（子模型）

定义库位信息，提供库位的增删查改功能。

| 字段名称    | 唯一键 | 数据类型   | 枚举     | 关联模型 | 关联方式 | 说明                        |
|---------|-----|--------|--------|------|------|---------------------------|
| 所属库区GID |     | String |        | 库区   | M2O  | 反向关联上级模型                  |
| 库位编码    | AK  | String |        |      |      |                           |
| 库位名称    |     | String |        |      |      |                           |
| 描述      |     | String |        |      |      |                           |
| 类型      |     | String | 库位类型枚举 |      |      |                           |
| 状态      |     | String | 仓库状态枚举 |      |      |                           |
| 库位管理模式  |     | String |        |      |      |                           |
| 库位是否已满  |     | String |        |      |      | 是、否                       |
| 库位使用类型  |     | String |        |      |      |                           |
| 承载类型    |     | String |        |      |      |                           |
| 盘点锁     |     | String |        |      |      |                           |

## 领域服务  (domain-service + proxy)
1. 仓库功能包括新增、修改、删除、查询、冻结、解冻，删除时需要先判断是否存在关联的库区，存在则提示先删除库区。
2. 库区功能包括新增、修改、删除、查询、冻结、解冻，删除时需要先判断是否存在关联的库位，存在则提示先删除库位。
3. 库位功能包括新增、修改、删除、查询、冻结、解冻。

## 应用服务 (service) （对应服务规约）
1. 仓库
新增
修改
删除
查询
冻结
解冻
2. 库区
新增
修改
删除
查询
冻结
解冻
3. 库位
新增
修改
删除
查询
冻结
解冻


## 模型交互动作 (handler)

## 远程服务 (remote)
1. 模型通过注册仓库查询、库区查询、库位查询的远程服务方法，为其他模型提供仓库、库区、库位查询功能。

## 端口 (port)
1. 编码生成功能，用于支持仓库编码、库区编码、库位编码自动生成。

## API
### 枚举
1. 状态枚举
- Normal：正常
- Locked：锁定

### RPC接口
1. 编码生成接口



