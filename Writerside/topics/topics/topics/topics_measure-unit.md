# 计量单位

## 模型设计
 
##  领域模型 （domain-model）
1. 计量单位（主模型）

计量单位的主模型，定义计量单位聚合换算规则，并对外提供计量单位增删查改、单位转换功能

| 字段名称     | 唯一键 | 数据类型   | 枚举 | 关联模型 | 关联方式 | 删除方法 | 说明 |
|----------|-----|--------|----|------|------|------|----|
| 计量单位编码   | AK1 | String |    |      |      |      |    |
| 计量单位英文名称 |     | String |    |      |      |      |    |
| 计量单位中文名称 |     | String |    |      |      |      |    |
| 计量单位分类   |     | String |    |      |      |      |    |

2. 换算规则（子模型）

定义换算规则，提供单位转换功能

| 字段名称     | 唯一键 | 数据类型   | 枚举 | 关联模型 | 关联方式 | 删除方法 | 说明          |
|----------|-----|--------|----|------|------|------|-------------|
| 换算规则编码   | AK1 | String |    |      |      |      |             |
| 原计量单位模型  |     | String |    |      |      |      |             |
| 目标计量单位模型 |     | String |    |      |      |      |             |
| 精确度      |     | Double |    |      |      |      |             |
| 换算公式     |     | String |    |      |      |      |             |
| 换算规则描述   |     | String |    |      |      |      | 如：1kg=1000g |

## 领域服务  (domain-service + proxy)
1. 计量单位增删查改功能
2. 换算规则增删查改功能
3. 换算规则试算结果反馈功能

## 应用服务 (service) （对应服务规约）
1. 计量单位增删查改功能
2. 换算规则增删查改功能

## 模型交互动作 (handler)

## 远程服务 (remote)
1. 模型通过注册计量单位查询的远程服务方法，为其他模型提供计量单位查询的能力。

## 端口 (port)

## API
### 枚举

### RPC接口
1. 编码生成接口
