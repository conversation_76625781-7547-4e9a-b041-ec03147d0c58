# 组件项目开发流程



# 一、组件开发流程

## 1.1创建组件开发项目源（JN WMS COMP）

**引入框架依赖、已开发的功能组件、业务组件依赖，创建业务组件原型**

### 1.1.1 创建业务组件流程

首先创建一个springboot项目。一个业务组件需要包含一个api包和biz包。然后需要将springboot项目转成组件。

**springboot项目转组件流程**

1. 首先需要在业务组件下引入koupleless的jar包

```xml
<dependency>
            <groupId>com.alipay.sofa.koupleless</groupId>
            <artifactId>koupleless-app-starter</artifactId>
            <version>${koupleless.runtime.version}</version>
            <scope>provided</scope>
        </dependency>
```

2. biz的pom.xml下需要配置打包的插件。需要修改bizName为组件名，修改webContextPath，webContextPath是发布到基座过后的路由，目前的规则是和bizName保持一致为组件名。

```xml
<plugins>
            <plugin>
                <groupId>com.alipay.sofa</groupId>
                <artifactId>sofa-ark-maven-plugin</artifactId>
                <version>3.1.3</version>
                <executions>
                    <execution>
                        <id>default-cli</id>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <skipArkExecutable>true</skipArkExecutable>
                    <outputDirectory>./target</outputDirectory>
                    <bizName>fabos-cmp-gm</bizName>
                    <webContextPath>fabos-cmp-gm</webContextPath>
                    <declaredMode>true</declaredMode>
                    <priority>2</priority>
                    <!--					打包、安装和发布 ark biz-->
                    <!--					静态合并部署需要配置-->
                    <!--					<attach>true</attach>-->
                    <!-- 多 bundle 使用该配置文件 conf/ark/rules.txt 排包，单 bundle 由于不存在依赖传递丢失问题建议使用 scope=provided 排包 -->
                    <packExcludesConfig>rules.txt</packExcludesConfig>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
```

3. 在启动类同级目录下新建一个××Component。配置组件元数据信息，注意@FabosComponentInfo里面的code不能和其他重复。
4. biz包下，在src同级目录下创建conf文件夹，在conf/ark下创建文件rules.txt文件。该文件是一些需要排除的包路径。可以直接copy现有组件的文件
5. 业务组件直接使用maven打包。如果配置成功将出现一个  -ark-biz.jar为后缀的jar包。该包就是发布到基座的代码包。
6. 直接调用发布接口。上传jar包进行发布。
7. 访问组件接口的路径为：基座ip:基座端口/业务组件webContextPath/controller接口路径

其他koupleless问题可参考官方文档:[koupleless文档](https://koupleless.io/docs/introduction/intro-and-scenario/)



## 1.2 模型开发

**功能开发首先以erupt实现为前提，优先通过后端配置实现功能，erupt不能满足的才可以新写接口**

模型开发目前是依赖于erupt框架进行。首先需要配置模型，配置的模型可参考erup官方文档[erupt文档](https://www.erupt.xyz/#!/doc)

我们将erupt源代码进行迁移改造。并将erupt改成了FabosJson。比如@erupt->@FabosJson；@eruptField->@FabosJsonField

可找到对应的元注解查看相关文档说明。目前erupt可以实现单表或者多表的增删改查以及一些表格的数据操作。一个模型默认包括新增、编辑、查看、删除、导入、导出等按钮。另外还可以通过配置自定义按钮去实现修改数据的功能，比如冻结、释放等等。都无需修改前端，通过配置完成。

模型配置可参考：

```java
package cec.jiutian.bc.urm.domain.role.entity;


import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.FabosJsonI18n;
import cec.jiutian.view.SubTableField;
import cec.jiutian.view.constant.AnnotationConst;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.BoolType;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.RowOperation;
import cec.jiutian.bc.urm.domain.menu.entity.Menu;
import cec.jiutian.bc.urm.domain.role.event.RoleAuthOperationHandler;
import cec.jiutian.bc.urm.domain.role.event.RoleDataProxy;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Entity
@Table(name = "fd_role")
@Data
@FabosJson(name = "角色管理", dataProxy = {RoleDataProxy.class},
            orderBy = "Role.sort asc, Role.createTime desc",
        rowOperation = {
                @RowOperation(
                        operationHandler = RoleAuthOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        code = "Role@AUTH",
                        type = RowOperation.Type.POPUP,
                        popupType = RowOperation.PopupType.TREE,
                        returnPopupDataField="menus",
                        fabosJsonClass = Menu.class,
                        title = "分配权限",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "Role@AUTH"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                )
        }
)
@FabosJsonI18n
@EqualsAndHashCode(callSuper = true)
public class Role extends MetaModel {

    public static final String CODE = "code";

    @Column(length = AnnotationConst.CODE_LENGTH)
    @FabosJsonField(
            views = @View(title = "编码"),
            edit = @Edit(title = "编码", notNull = true, search = @Search(vague = true))
    )
    @SubTableField
    private String code;

    @FabosJsonField(
            views = @View(title = "名称", toolTip=true),
            edit = @Edit(title = "名称", notNull = true, search = @Search(vague = true))
    )
    @SubTableField
    private String name;

    @FabosJsonField(
            views = @View(title = "角色说明",toolTip=true),
            edit = @Edit(title = "角色说明")
    )
    @SubTableField
    private String description;

    @FabosJsonField(
            views = @View(title = "显示顺序", sortable = true),
            edit = @Edit(title = "显示顺序" , notNull = true, numberType = @NumberType(min = 1, max = 9999))
    )
    private Integer sort;

    @FabosJsonField(
            views = @View(title = "状态", sortable = true),
            edit = @Edit(
                    title = "状态",
                    type = EditType.BOOLEAN,
                    notNull = true,
                    defaultVal = "true",
                    search = @Search(vague = true,defaultVal = "true"),
                    boolType = @BoolType(trueText = "有效", falseText = "失效")
            )
    )
    private Boolean status = true;

    @JoinTable(
            name = "fd_role_menu",
            joinColumns = @JoinColumn(name = "role_id", referencedColumnName = "id"),
            inverseJoinColumns = @JoinColumn(name = "menu_id", referencedColumnName = "id"))
    @FabosJsonField(
            views = @View(title = "菜单权限", export = false),
            edit = @Edit(
                    show = false,
                    title = "菜单权限",
                    filter = @Filter(value = "Menu.status in ('1','2')"),
                    type = EditType.TAB_TREE
            )
    )
    @ManyToMany(fetch = FetchType.EAGER)
    private List<Menu> menus;

}

```

模型配置完成过后。启动会自动创建表格。无需关注db

## 1.3 本地接口调试

在模型配置成功过后，需要在本地进行启动。首先组件就是一个springboot项目，可以直接不依赖基座启动。也可以启动基座过后，将组建发布到基座进行测试。（该阶段还属于开发阶段，如果基座和业务组件不在一个代码仓库下，通过基座+组件的模式进行测试代码没法进行普通debug。只能使用remote debug）

启动成功过后，可以直接通过erupt相关的接口进行模型测试。

如果单起组件，访问路径为：localhost:8086/fabos-api/build/Role（组件的端口）

如果是基座+组件启动，文档路径为：localhost:8082/fabos-cmp-urm/fabos-api/build/Role（端口为基座端口，fabos-cmp-urm为组组件）

这里需要注意：单起组件或者在基座上启动组件同一个组件只能选择其中一种，否则可能出现dubbo相关端口被占用的报错。

## 1.4 本地前端调试

因为通过erupt注解配置的模型，接口返回的参数很多，接口是否满足需求没法通过接口直观的显示出来。因此需要在本地启动前端项目进行本地测试。

### 1.4.1前端路由配置

前端的路由配置vite.config.ts文件中。以下配置以调试fabos-cmp-urm为例：

#### ******* 调试组件走本地+其他组件走服务器配置

如果是组件加基座的模式本地联调。配置如下

```
    proxy: {
      "/fabos-api/fabos-cmp-urm": {
        target: "http://localhost:8082",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/fabos-api/, ""),
      },
      "/fabos-api": {
        target: "http://*************:8082",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/fabos-api/, ""),
      },
      "/ocr": {
        target: "http://*************:8000/ocr", // 测试服务器
        // target: "http://*************:8000/ocr", // 李良波本地api
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/ocr/, ""),
      },
      "/api": {
        target: "http://*************:6012",
        changeOrigin: true,
      }
    }
```

以上配置表示fabos-cmp-urm组件走本地。除fabos-cmp-urm组件以外走线上。这里需要注意：

**/fabos-api/fabos-cmp-urm的路由必须要在/fabos-api路由之前，否则优先匹配到/fabos-api过后就不会往下匹配了**   

如果是组件单起的模式下。配置如下：

```
    proxy: {
      "/fabos-api/fabos-cmp-urm": {
        target: "http://localhost:8086",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/fabos-api\/fabos-cmp-urm/, ""),
      },
      "/fabos-api": {
        target: "http://*************:8082",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/fabos-api/, ""),
      },
      "/ocr": {
        target: "http://*************:8000/ocr", // 测试服务器
        // target: "http://*************:8000/ocr", // 李良波本地api
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/ocr/, ""),
      },
      "/api": {
        target: "http://*************:6012",
        changeOrigin: true,
      }
    }
```

#### ******* 调试组件走本地+其他组件也走本地

调试组件和其他组件都是发布到基座中：

```
proxy: {
      "/fabos-api": {
        target: "http://localhost:8082",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/fabos-api/, ""),
      },
      "/ocr": {
        target: "http://*************:8000/ocr", // 测试服务器
        // target: "http://*************:8000/ocr", // 李良波本地api
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/ocr/, ""),
      },
      "/api": {
        target: "http://*************:6012",
        changeOrigin: true,
      }
    }
```



 调试组件单独启动，其他组件发布到基座中：

注意这里可能会出现问题：点击单独启动组件所属菜单没反应。这是因为这里基座路由的本地，本地的基座中没有这个测试的组件。前端查询模型在基座中所属组件时没有查询到，导致前端该菜单没法路由。

解决办法：在本地基座中也需要加载单独启动的这个组件（也就是调试组件在基座中启动一次，本地单独启动一次。所以如果不是为了debug的话。就没必要在单独启动组件了，因为基座里面已经有该组件了。直接全部路由到本地即可）

```
proxy: {
      "/fabos-api/fabos-cmp-urm": {
        target: "http://localhost:8086",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/fabos-api\/fabos-cmp-urm/, ""),
      },
      "/fabos-api": {
        target: "http://localhost:8082",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/fabos-api/, ""),
      },
      "/ocr": {
        target: "http://*************:8000/ocr", // 测试服务器
        // target: "http://*************:8000/ocr", // 李良波本地api
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/ocr/, ""),
      },
      "/api": {
        target: "http://*************:6012",
        changeOrigin: true,
      }
    }
```

### 1.4.2 手动创建模型菜单

路由配置好过后，需要手动创建模型菜单用于测试。创建菜单需要注意：

1. 类型值需要填写模型名称：Role
2. 菜单类型必须要选择：表格视图 （表格视图才会自动创建erupt相关的按钮）

菜单创建好过后，需要给测试的账号分配对应权限。

## 1.5组件菜单初始化（非必须）

该步骤目前就是组件开发完成过后。部署基座的时候自动创建菜单的流程。这里是非必须的。如果需要自动创建菜单。就实现该部分功能。如果不需要自动创建菜单。这个流程可以跳过。

组件菜单初始化是在创建一个租户的时候触发。创建租户，选择一个组件过后。会实现自动创建该组件菜单（如果有）

### 1.5.1自动创建菜单接入流程

首先在resources目录下新建init文件件。放入一个初始化菜单配置init.menu.csv  (ecs的菜单初始化配置如下。可参考)

```
model-driven,modelClass,menuType,sort,menuIcon,menuName,menuValue,parentMenu
TRUE,GeneralIp,table,1,,,,
TRUE,Message,table,10,,,,
TRUE,MessageGroup,table,20,,,,
TRUE,MessageRecord,table,30,,,,
TRUE,Notice,table,40,,,,
TRUE,Template,table,50,,,,
TRUE,TemplateAttribute,table,60,,,,
TRUE,UserMessageRecord,table,80,,,,
FALSE,,static,70,,消息统计报表,MessageStatistics,
```

然后在代码中实现IInitDataProvider接口。ecs菜单初始化代码如下，可参考

```java
package cec.jiutian.bc.ecs.inbound.remote.provider;

import cec.jiutian.component.utils.ReadInitDictDataFromCSVUtil;
import cec.jiutian.component.utils.ReadInitMenuDataFromCSVUtil;
import cec.jiutian.bc.urm.dto.InitDataDTO;
import cec.jiutian.bc.urm.dto.MetaDict;
import cec.jiutian.bc.urm.dto.MetaMenu;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
public class EcsInitProvider implements InitDataDTO.IInitDataProvider {
    @Override
    public List<MetaMenu> initData(InitDataDTO dto) {
        List<MetaMenu> menus = new ArrayList<>();
        menus.add(MetaMenu.createRootMenu("$EcsCenter", "消息中心", "fa fa-cogs", 1, dto.getOid(), dto.getComponentName()));
        Optional.ofNullable(ReadInitMenuDataFromCSVUtil.convertCsvToList(menus.get(0))).ifPresent(e -> menus.addAll(e));
        return menus;
    }

    @Override
    public List<MetaDict> initDict(InitDataDTO dto) {
        List<MetaDict> metaDicts = new ArrayList<>();
        Optional.ofNullable(ReadInitDictDataFromCSVUtil.convertCsvToList(dto)).ifPresent(e -> metaDicts.addAll(e));
        return metaDicts;
    }
}

```

initDict这里不需要具体实现。和初始化菜单无关。



## 1.6 组件本地基座发布测试（重要）

因为组件发布基座过后，依赖的加载机制等原因会导致很多问题在单起组件测试无法暴露出来。因此在这个阶段无论使用什么方式进行测试，在发布到服务器之前必须保证在基座+组件方式进行测试没问题过后，方可发布服务器。



## 1.7 组件Install

最后就是发布服务器。使用发布接口发布







``` 
GMComponent  code不能重复
```