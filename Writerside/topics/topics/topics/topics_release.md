# 业务项目发布流程

如果在本地已经开发测试完成。经组件集成到基座测试无误过后。需要发布到服务器验证。

1. 如果修改的是公共依赖module。必选重启基座过后，分别发布基座下的所有组件，重启基座过后。会自动构建之前已发布过的组件（上一次发布过的jar包）。因此在发布修改过的组件时。需要先删除服务器上面的组件jar包。避免发布组件的时候出现组件已经注册。
   1. http://*************:8082/deleteFile  请求参数fileName需要传文件名称（全称）
   2. http://*************:8082/listFiles  查询现有的服务器组件包（该接口返回的组件都会在基座重启过后自动加载）
2. 如果修改的是单个业务组件或者是一个新组件。只需要发布该组件即可（前期卸载还存在问题，发布组件建议都重启基座过后发布。该问题在解决中）



**组件发布相关接口**：

1. http://localhost:8082/loadingBiz  组件注册接口。

   ![请求参数](images_image-20241029190105431.png)

2. http://localhost:1238/uninstallBiz  组件卸载接口 post请求参数:

   ```json
   {
       "bizName":"fabos-cmp-gm",//fabos-cmp-tenant
       "bizVersion":"3.2.1-SNAPSHOT"
   }
   ```

   

3. http://*************:1238/queryAllBiz  查询所有组件 post请求



