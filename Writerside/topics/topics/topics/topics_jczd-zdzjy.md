# 字段值前端校验

1. 数值类型使用`numberType = @NumberType`进行最大最小值控制
```java
public @interface NumberType {
    long max() default Integer.MAX_VALUE; //可输入最大值

    long min() default -Integer.MAX_VALUE; //可输入的最小值
}
```
示例：正整数输入
```java
@FabosJsonField(
        views = @View(title = "显示顺序"),
        edit = @Edit(title = "显示顺序", numberType = @NumberType(min = 1))
)
private Integer sort;
```
示例：支持使用变量
```Java
@FabosJsonField(
        views = @View(title = "货架层数"),
        edit = @Edit(title = "货架层数", numberType = @NumberType(maxExpr  = "${warehouseBlock.rowQuantity}"),
                dependFieldDisplay = @DependFieldDisplay(showOrHide = "type == 'Stereo'"))
)
private Integer blockRow;
```