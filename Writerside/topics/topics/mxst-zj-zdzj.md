# 字段注解

## @View
1. index
* 配置字段在表格和表单中的展示顺序
```
 @FabosJsonField(
         views = @View(title = "采购合同编号",index = 9)
 )
 private String purchaseContractCode;
```

2. fixed
* 配置字段在表格中的固定方式
* 配置示例```fixed = View.FixedType.left)```
* 相关枚举：
  * FixedType {
        left,
        right,
        none,
    }

3. type
* TABLE_FORM：表单弹窗展示对象明细
  * 支持根据ID动态展示，配置如下
    * 参数构造类型1：由ID构造
      ```Java
       ```Java
       public class OmMaterialByIdFormHandler implements TableFormParamsHandler {
       @Override
       public TableFormParams handle() {
           return new TableFormParams("stockType",  // 参数1：用于条件判断的表格字段，当无需判断时，此处写*，参数2中map的key也写*
                new HashMap<>() {{                  // 参数2：根据参数1在map中对应key中找value
               put("MATERIAL", new DependCondition("Material","materialId")); // 当stockType为MATERIAL时，使用Material模型，并取表单中material值去Material中PK查询
               put("PRODUCT", new DependCondition("Product","materialId"));
           }});
       }
      }
      
       public class OmMaterialByIdFormHandler implements TableFormParamsHandler {
       @Override
       public TableFormParams handle() {
           return new TableFormParams("*",  // 参数1：用于条件判断的表格字段，当无需判断时，此处写*，参数2中map的key也写*
                new HashMap<>() {{                  // 参数2：根据参数1在map中对应key中找value
               put("*", new DependCondition("Supplier","suplierId")); // 当stockType为MATERIAL时，使用Material模型，并取表单中material值去Material中PK查询
           }});
       }
      }
       ``` 
       ```
      * 参数构造类型2：由AK构造
       ```Java
       @Component
           public static class Handler implements TableFormParamsHandler {
               @Override
               public TableFormParams handle() {
                   return new TableFormParams("stockType",  //参数1：同构造类型1
                        new HashMap<>() {{           //参数2：根据参数1在map中对应key中找value
                       put("MATERIAL", new DependCondition("Material",   //当参数1为FROZEN时，使用Material模型，并依据下一参数定义的AK进行查询
                          new HashMap<>() {{put("materialCode", "materialCode"); // 第一个参数为模型的AK字段名，第二个参数为表中中取值的字段名
                       }}));
                       put("*", new DependCondition("MaterialTest", new HashMap<>() {{
                           put("code", "materialCode");
                       }}));
                   }});
               }
           }
       ```

      # 将handler配置到对象字段或字符串对象上
       @ManyToOne
       @FabosJsonField(
               views = @View(title = "存货项目", column = "name", type = ViewType.TABLE_FORM,
                       tableFormParamsHandler = TesterHandler2.class
               ),
               edit = @Edit(title = "存货项目", type = EditType.REFERENCE_TABLE
               )
       )
       private StockView stockItem;
    
       @FabosJsonField(
               views = @View(title = "存货名称",
                       tableFormParamsHandler = TesterHandler2.class, type = ViewType.TABLE_FORM),
               edit = @Edit(title = "存货名称", notNull = false,
                       show = false)
       )
       private String StockName;    
      ```
  




## @Edit
1. @dependFieldDisplay
可以根据条件动态控制字段是否为空、是否展示、是否只读
* 控制字段是否只读，使用示例：`dependFieldDisplay = @DependFieldDisplay(enableOrDisable = "type == 'Block'")`
* 控制字段根据条件显示，使用示例：`dependFieldDisplay = @DependFieldDisplay(showOrHide = "type == 'Block'")` （_注意，不要使用showBy，不生效。）
* 控制字段是否允许为空：使用示例：`dependFieldDisplay = @DependFieldDisplay(notNull = "type == 'Block'")`

2. @NumberType
* 配置数值类型的校验，支持配置最大最小值表达式，支持变量表达式
* 当定值和表达式同时存在，定值配置将失效
```Java
@FabosJsonField(
views = @View(title = "数量"),
edit = @Edit(title = "数量",
numberType = @NumberType(min = 10, max = 100, minExpr = "${refNum1 - 100}", maxExpr = "${refNum2 +111}"))
)
```

3. 一对多子模型控制可设置的数量是一个还是多个
* allowAddMultipleRows = true(默认)/false

4. 数值类型的精度配置
```Java
       @FabosJsonField(
                views = @View(title = "数量",rowEdit = true,width = "100px"),
                edit = @Edit(title = "数量", notNull = true,
                        numberType = @NumberType(min=0,max = 9999999,precision = 2))
        )
        private Double quantity;
```

5. 表单提示
* placeHolder：表单占位提示
* tips：lable上的悬浮提示

6. 表单项录入展示包裹
* inputGroup  
  * inputGroup = @InputGroup(postfix = "次")
  * inputGroup = @InputGroup(postfix = "${simpleField}")
  * inputGroup = @InputGroup(postfix = "${objectField.name}")
  * inputGroup = @InputGroup(postfix = "${objectField.name}次")


## @JsonIgnoreProperties
* 应用在对象类型上，指定对象类型中的某个字段不做序列化
```
    @JsonIgnoreProperties({"fields"})
    private MetadataModel referenceModel;
```

## @DynamicField
1. @DependFiled
控制该字段根据其他依赖字段的变化，自动调用后端方法进行取值，并返回给前端展示
当指定了buttonName时，在该字段旁会展示相应按钮，点击后，也手工调用handler(buttonName不写时不展示按钮)
```
   dynamicField = @DynamicField(
   dependFiled = @DependFiled(buttonName = "计算",changeBy = {"formula","sourceParam","precision"},
   dynamicHandler = CalculateDynamicHandler.class)
   )
```


2. @LinkedFiled
   控制字段值由其他对象字段变化后，将对象字段中的属性赋值到该字段
      @LinkedFiled(changeBy = "receivePlanDetail", beFilledBy = "materialCode")

## @FabosJsonField(
            views = @View(title = "采购合同编号",index = 9),
            edit = @Edit(title = "采购合同编号", show = false,
                    readonly = @Readonly(add = false),
                    search = @Search(vague = true),
                    inputType = @InputType(length = 20))
    )

## @SubTableField
* 用于标记该字段会在模型被用于ManyToMany子模型的子表格中展示
* 注意，如果模型字段在该某些作为子模型时也展示在表格中时，需配置该注解
子模型
```Java
public class FinalProductDTM extends MetaModel {

        @FabosJsonField(
                views = @View(title = "产品零部件代码"),
                edit = @Edit(title = "产品零部件代码")
        )
        @SubTableField
        private String productCode; 
```
主模型中引用该子模型
```Java
@ManyToMany
@JoinTable(
        name = "e_semiproduct_usefor_product", // 中间表表名，如下为中间表的定义
        joinColumns = @JoinColumn(name = "target_id", referencedColumnName = "id"),
        inverseJoinColumns = @JoinColumn(name = "source_id", referencedColumnName = "id"))
@FabosJsonField(
        views = @View(title = "用途", type = ViewType.TABLE_VIEW),
        edit = @Edit(title = "用途",
                type = EditType.TAB_TABLE_REFER,
                notNull = false,
                dependFieldDisplay = @DependFieldDisplay(showOrHide = "productMainCategory == 'SEMIPRODUCT'")
        )
)
private List<FinalProductDTM> useForProductlist;
```