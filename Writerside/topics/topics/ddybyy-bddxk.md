# 表单单选框

@ReferenceTableType -> type = Type.RADIO

![image_42.png](image_42.png)

```java
    @ManyToOne
    @FabosJsonField(
            views = @View(title = "英雄", column = "name"), //column 是选项展示的信息
            edit = @Edit(title = "英雄",
                    type = EditType.REFERENCE_TABLE,
                    //referenceTableType注意与多对多的属性tabTableReferType配置区分
                    referenceTableType = @ReferenceTableType(type = ReferenceTableType.SelectShowTypeMTO.RADIO)
            )
    )
    private Hero hero;
```