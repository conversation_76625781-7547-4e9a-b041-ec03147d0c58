<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <artifactId>fabos-xlx</artifactId>
    <version>3.2.0</version>
    <packaging>pom</packaging>
    <parent>
        <groupId>cec.jiutian</groupId>
        <artifactId>fabos-parent</artifactId>
        <version>3.2.2-SNAPSHOT</version>
    </parent>

    <modules>
        <module>xlx-wms</module>
        <module>xlx-eam</module>
        <module>xlx-iot</module>
        <module>xlx-qms</module>
        <module>xlx-lims</module>
        <module>xlx-crs</module>
        <module>xlx-ehs</module>
        <module>xlx-ems</module>
        <module>xlx-spc</module>
    </modules>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <fabos.framework.version>3.2.2-SNAPSHOT</fabos.framework.version>
        <zookeeper.version>3.9.1</zookeeper.version>
        <seata.version>2.1.0</seata.version>
        <zkclient.version>0.11</zkclient.version>
    </properties>

</project>