<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 引入spirng boot默认的logback配置文件 -->
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>
                <!--                %d{yyyy-MM-dd HH:mm:ss} [%thread] %magenta(%-5level) %green([%-50.50class]) >>> %cyan(%msg) %n-->
                <!--                %magenta(%d{HH:mm:ss.SSS}) %clr([%level]) - %cyan(%30.30(%logger{30})).%cyan(%-25.25(%method{25})) : %msg%n-->
                %d{HH:mm:ss.SSS} %clr([%level]) %magenta(%20.20(%method)) - %cyan(%-40.40class) : %msg%n
            </pattern>
        </encoder>
    </appender>

    <root level="INFO">
        <appender-ref ref="STDOUT"/>
    </root>

    <appender name="DAILY_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/app/logs/xlx-wms.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>/app/logs/archived/fabos-boot.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>90</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>
                %d{yyyy-MM-dd HH:mm:ss.SSS} [%level] %class#%method : %msg%n
            </pattern>
        </encoder>
    </appender>

    <springProfile name="local">
        <root level="INFO">
            <appender-ref ref="STDOUT"/>
        </root>
    </springProfile>

    <springProfile name="dev">
        <root level="INFO">
            <appender-ref ref="STDOUT"/>
<!--            <appender-ref ref="DAILY_FILE"/>-->
        </root>
    </springProfile>

    <springProfile name="fat">
        <root level="INFO">
            <appender-ref ref="STDOUT"/>
            <appender-ref ref="DAILY_FILE"/>
        </root>
    </springProfile>
</configuration>
