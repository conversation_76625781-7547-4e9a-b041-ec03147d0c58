app:
  id: xlx-lims
nacos:
  #  addr: 127.0.0.1:8848
  addr: 172.16.200.101:8848
spring:
  main:
    allow-bean-definition-overriding: true  #该配置配初始化早于nacos客户端初始化,不能配置在nacos
  profiles:
    active: dev
  application:
    name: ${app.id}
  cloud:
    nacos:
      server-addr: ${nacos.addr}
      username: xlx
      password: xlx0228
      discovery:
        enabled: true
        namespace: fabos-xlx
        group: ${spring.profiles.active}
      config:
        shared-configs:
          - data-id: common  # 公共配置
            group: ${spring.profiles.active}
            refresh: true
        enabled: true
        namespace: fabos-xlx
        group: ${spring.profiles.active}
        prefix: ${app.id}

logging:
  level:
    com:
      alibaba:
        cloud: debug