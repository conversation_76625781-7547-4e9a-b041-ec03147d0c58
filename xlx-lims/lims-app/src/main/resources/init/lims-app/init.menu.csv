model-driven,modelClass,menuType,sort,menuIcon,menuName,menuValue,parentMenu
FALSE,,usual,1,fa fa-cogs,基础数据,$limsBase,
TRUE,ExpLab,usual,55,,,,$limsBase,
TRUE,Lab,multiTable,60,,,,$limsBase,
TRUE,ExpMethod,multiTable,65,,,,$limsBase,
TRUE,ExpTakt,usual,67,,,,$limsBase,
TRUE,SkillManage,usual,70,,,,$limsBase,
TRUE,ReagentConfig,multiTable,75,,,,$limsBase,
TRUE,SkillGroup,usual,80,,,,$limsBase,
TRUE,LimsMaterialCategory,usual,90,,,,$limsBase,
TRUE,LimsMaterial,usual,100,,,,$limsBase,
TRUE,LimsWarehouse,multiTable,110,,,,$limsBase,
TRUE,LimsShelves,multiTable,120,,,,$limsBase,
TRUE,LimsCarrier,multiTable,130,,,,$limsBase,
FALSE,,usual,2,fa fa-cogs,人员档案,$limsPerson,
TRUE,PostManage,multiTable,1,,,,$limsPerson,
TRUE,PersonManage,usual,10,,,,$limsPerson,
TRUE,PersonSkill,multiTable,20,,,,$limsPerson
TRUE,PersonTraining,multiTable,30,,,,$limsPerson
TRUE,PersonExamine,multiTable,40,,,,$limsPerson
FALSE,,usual,3,fa fa-cogs,过程管理,$limsProcess,
TRUE,SampleTask,multiTable,10,,,,$limsProcess,
TRUE,InspectionTask,multiTable,20,,,,$limsProcess,
TRUE,MyInspectionTask,multiTable,25,,,,$limsProcess,
TRUE,LmReportTemplate,multiTable,30,,,,$limsProcess,
TRUE,ReagentConfigRequest,multiTable,40,,,,$limsProcess,
TRUE,ReagentUnseal,usual,50,,,,$limsProcess,
TRUE,ConsumeScrap,multiTable,60,,,,$limsProcess,
FALSE,,usual,4,fa fa-cogs,设备管理,$LimsDeviceManage,
FALSE,,usual,1,fa fa-cogs,设备档案管理,$LimsDeviceArchive,$LimsDeviceManage
TRUE,Equipment,usual,10,,,,$LimsDeviceArchive,
TRUE,EquipmentType,treeForm,1,,,,$LimsDeviceArchive,
TRUE,LimsEquipmentArchive,usual,20,,,,$LimsDeviceArchive,
FALSE,,usual,2,fa fa-cogs,设备点检,$LimsDeviceIns,$LimsDeviceManage
TRUE,DeviceInsStandard,multiTable,10,,,,$LimsDeviceIns
TRUE,DeviceInsPlan,multiTable,20,,,,$LimsDeviceIns
TRUE,DeviceInsTask,multiTable,30,,,,$LimsDeviceIns
TRUE,MyDeviceInsTask,multiTable,40,,,,$LimsDeviceIns
FALSE,,usual,3,fa fa-cogs,设备维修,$LimsDeviceMaintenance,$LimsDeviceManage
TRUE,MaintenanceRequest,usual,1,,,,$LimsDeviceMaintenance,
TRUE,MaintenanceTaskManage,usual,2,,,,$LimsDeviceMaintenance,
TRUE,MaintenanceRequestApprove,usual,3,,,,$LimsDeviceMaintenance,
TRUE,MaintenancePreparation,usual,4,,,,$LimsDeviceMaintenance,
TRUE,LimsRepairSupply,multiTable,5,,,,$LimsDeviceMaintenance,
TRUE,MaintenanceExecute,usual,6,,,,$LimsDeviceMaintenance,
TRUE,PermitToWorkManagement,multiTable,7,,,,$LimsDeviceMaintenance,
TRUE,PTWItem,usual,8,,,,$LimsDeviceMaintenance,
TRUE,CopperRelatedApplication,usual,9,,,,$LimsDeviceMaintenance,
TRUE,MaintenanceExperience,usual,10,,,,$LimsDeviceMaintenance,
TRUE,OutsourceMaintenance,usual,11,,,,$LimsDeviceMaintenance,
TRUE,MagneticParticleApplication,usual,12,,,,$LimsDeviceMaintenance,
FALSE,,usual,4,fa fa-cogs,备品备件管理,$sparePartsManagement,$LimsDeviceManage,
TRUE,WorkshopInventory,multiTable,1,,,,$sparePartsManagement,
FALSE,,usual,6,fa fa-cogs,点巡检管理,$EhsIns,,
TRUE,EhsInsItem,usual,10,,,,$EhsIns,
TRUE,EhsInsScheme,multiTable,20,,,,$EhsIns,
TRUE,EhsInsPlan,multiTable,30,,,,$EhsIns,
TRUE,EhsInsTask,multiTable,40,,,,$EhsIns,
TRUE,MyEhsInsTask,multiTable,50,,,,$EhsIns,
TRUE,EhsQuestion,multiTable,60,,,,$EhsIns,
TRUE,EhsMyQuestion,multiTable,70,,,,$EhsIns,
FALSE,,usual,8,fa fa-cogs,留样管理,$keepSample,
TRUE,LmKsStockIn,usual,10,,,,$keepSample,
TRUE,KsInventory,usual,20,,,,$keepSample,
TRUE,LmKsStockOut,usual,30,,,,$keepSample,
TRUE,KsStocktakingPlan,usual,40,,,,$keepSample,
TRUE,StocktakingOrder,multiTable,50,,,,$keepSample,
TRUE,MyStocktakingOrder,multiTable,60,,,,$keepSample,
TRUE,StocktakingReview,multiTable,70,,,,$keepSample,
TRUE,LmSampleReturn,multiTable,80,,,,$keepSample,
FALSE,,usual,9,fa fa-cogs,线边库管理,$limsLineEdge,
TRUE,LmLineStockIn,usual,10,,,,$limsLineEdge,
TRUE,LineInventory,multiTable,20,,,,$limsLineEdge,
TRUE,LmLineStockOut,multiTable,30,,,,$limsLineEdge,
TRUE,LineStocktakingPlan,usual,40,,,,$limsLineEdge,
TRUE,LineStocktakingOrder,multiTable,50,,,,$limsLineEdge,
TRUE,LineMyStocktakingOrder,multiTable,60,,,,$limsLineEdge,
TRUE,LineStocktakingReview,multiTable,70,,,,$limsLineEdge,
FALSE,,usual,10,fa fa-cogs,质控管理,$qualityControl,
TRUE,QcLedger,usual,10,,,,$qualityControl,
TRUE,QcPlan,usual,20,,,,$qualityControl,
TRUE,QcTask,multiTable,30,,,,$qualityControl,
TRUE,MyQcTask,multiTable,40,,,,$qualityControl,
FALSE,,usual,11,fa fa-cogs,对标管理,$compareManage,
TRUE,ComparePlan,multiTable,10,,,,$compareManage,
FALSE,,usual,12,fa fa-cogs,MSA管理,$MSAManage,
TRUE,MsaPlan,multiTable,10,,,,$MSAManage,
TRUE,MsaReportExamine,multiTable,10,,,,$MSAManage,