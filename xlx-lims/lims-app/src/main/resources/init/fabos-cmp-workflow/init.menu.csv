model-driven,modelClass,menuType,sort,menuIcon,menuName,menuValue,parentMenu
FALSE,,usual,90,fa fa-cogs,系统设置,$configManager,
FALSE,,usual,10,fa fa-cogs,流程管理,FlowTemplate,$configManager
FALSE,,static,20,fa fa-cogs,流程模型管理,FlowManage,FlowTemplate
FALSE,,button,21,fa fa-cogs,新增,FlowManage@ADD,FlowManage
FALSE,,button,22,fa fa-cogs,修改,FlowManage@EDIT,FlowManage
FALSE,,button,23,fa fa-cogs,版本管理,FlowManage@VERSION,FlowManage
FALSE,,button,24,fa fa-cogs,失效,FlowManage@INVALID,FlowManage
FALSE,,button,25,fa fa-cogs,生效,FlowManage@VALID,FlowManage
FALSE,,button,26,fa fa-cogs,删除,FlowManage@DELETE,FlowManage
FALSE,,static,30,fa fa-cogs,待审批流程模型,FlowToDoTemplate,FlowTemplate
FALSE,,button,31,fa fa-cogs,通过,FlowToDoTemplate@PASS,FlowToDoTemplate
FALSE,,button,32,fa fa-cogs,拒绝,FlowToDoTemplate@REFUSE,FlowToDoTemplate
FALSE,,usual,2,fa fa-cogs,我的流程,TaskManage,
FALSE,,static,50,fa fa-cogs,发起申请,FlowStart,TaskManage
FALSE,,button,51,fa fa-cogs,选择,FlowStart@SELECT,FlowStart
FALSE,,static,60,fa fa-cogs,我的发起,MyTask,TaskManage
FALSE,,button,61,fa fa-cogs,催办,MyTask@URGEPROCESSING,MyTask
FALSE,,button,62,fa fa-cogs,撤回,MyTask@WITHDRAW,MyTask
FALSE,,button,63,fa fa-cogs,删除,MyTask@DELETE,MyTask
FALSE,,button,64,fa fa-cogs,重新发起,MyTask@RESTART,MyTask
FALSE,,button,65,fa fa-cogs,查看详情,MyTask@VIEW_DETAIL,MyTask
FALSE,,static,70,fa fa-cogs,我的待办,TaskToDo,TaskManage
FALSE,,button,71,fa fa-cogs,处理,TaskToDo@HANDLE,TaskToDo
FALSE,,button,72,fa fa-cogs,查看详情,TaskToDo@VIEW_DETAIL,TaskToDo
FALSE,,static,80,fa fa-cogs,抄送我的,CopyToMe,TaskManage
FALSE,,button,81,fa fa-cogs,标记已读,CopyToMe@MARK,CopyToMe
FALSE,,button,82,fa fa-cogs,查看流程详情,CopyToMe@VIEW_DETAIL,CopyToMe
FALSE,,static,90,fa fa-cogs,已办任务,TaskDone,TaskManage
FALSE,,button,91,fa fa-cogs,查看详情,TaskDone@VIEW_DETAIL,TaskDone
FALSE,,button,92,fa fa-cogs,查看流程详情,TaskDone@FLOW_DETAIL,TaskDone
