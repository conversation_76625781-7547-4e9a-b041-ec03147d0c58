package cec.jiutian.bc.device.remote.provider;

import cec.jiutian.bc.device.domain.insPlan.model.DeviceInsPlan;
import cec.jiutian.bc.device.domain.insPlan.service.DeviceInsPlanService;
import cec.jiutian.bc.job.provider.IJobProvider;
import cec.jiutian.core.frame.annotation.FabosCustomizedService;
import cec.jiutian.meta.FabosJob;
import jakarta.annotation.Resource;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description:
 */
@FabosCustomizedService(value = DeviceInsPlan.class)
@Component
@Transactional
@Slf4j
public class DeviceInsPlanProvider implements IJobProvider {
    @Resource
    private DeviceInsPlanService deviceInsPlanService;
    @Override
    @FabosJob(comment = "自动创建设备点检任务方法")
    public String exec(String code, String param) {
        log.info("自动创建设备点检任务方法,定时任务开始执行");
        deviceInsPlanService.generateDeviceInsTask();
        return null;
    }
}
