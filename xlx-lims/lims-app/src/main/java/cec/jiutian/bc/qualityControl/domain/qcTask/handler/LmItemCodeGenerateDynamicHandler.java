package cec.jiutian.bc.qualityControl.domain.qcTask.handler;

import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.modeler.domain.inspectionItem.model.InspectionItem;
import cec.jiutian.bc.modeler.enumration.NamingRuleCodeEnum;
import cec.jiutian.bc.qualityControl.domain.qcTask.model.QcTaskDetail;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class LmItemCodeGenerateDynamicHandler implements DependFiled.DynamicHandler<QcTaskDetail> {

    @Resource
    private NamingRuleService namingRuleService;

    @Override
    public Map<String, Object> handle(QcTaskDetail qcTaskDetail) {
        Map<String, Object> map = new HashMap<>();
        map.put("inspectionItemCode",
                String.valueOf(namingRuleService.getNameCode(NamingRuleCodeEnum.InspectionItem.name(), 1, null).get(0)));
        return map;
    }
}
