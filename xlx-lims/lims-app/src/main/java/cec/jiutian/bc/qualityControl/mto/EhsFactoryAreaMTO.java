package cec.jiutian.bc.qualityControl.mto;

import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;

import java.util.Date;

/**
 * 车间、产线
 */
@Entity
@Table(name = "mo_fctry_ara")
@Getter
@FabosJson(
        name = "区域信息",
        orderBy = "createTime desc",
        power = @Power(add = false, edit = false, delete = false, print = false, importable = false)
)
public class EhsFactoryAreaMTO {

    @Id
    @FabosJsonField(
            edit = @Edit(title = "", show = false)
    )
    @Column(name = "id", columnDefinition = "int8")
    private Long id;

    @FabosJsonField(
            views = @View(title = "", show = false),
            edit = @Edit(title = "", show = false)
    )
    @Column(name = "pid", columnDefinition = "int8")
    private Long pid;

    @FabosJsonField(
            views = @View(title = "名称"),
            edit = @Edit(title = "名称",
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "fctry_ara_nm", length = 40)
    private String factoryAreaName;

    @FabosJsonField(
            views = @View(title = "编码"),
            edit = @Edit(title = "编码",
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "fctry_ara_cd", length = 40)
    private String factoryAreaCode;

    @FabosJsonField(
            views = @View(title = "简称"),
            edit = @Edit(title = "简称",
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "fctry_ara_shrt_nm", length = 40)
    private String factoryAreaShortName;

    @FabosJsonField(
            views = @View(title = "描述"),
            edit = @Edit(title = "描述",
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "fctry_ara_ds", length = 400)
    private String factoryAreaDescription;

    @FabosJsonField(
            views = @View(title = "层级ID", show = false),
            edit = @Edit(title = "层级ID",
                    show = false
            )
    )
    @Column(name = "hrchy_id", columnDefinition = "int8")
    private Long hierarchyId;

    @FabosJsonField(
            views = @View(title = "层级名称", show = false),
            edit = @Edit(title = "层级名称",
                    show = false
            )
    )
    @Column(name = "hrchy_nm", length = 40)
    private String hierarchyName;

    @Column(name = "fctry_ara_typ_cd", length = 40)
    private String factoryAreaTypeCode;

    @Column(name = "crte_tm")
    private Date createTime;
}
