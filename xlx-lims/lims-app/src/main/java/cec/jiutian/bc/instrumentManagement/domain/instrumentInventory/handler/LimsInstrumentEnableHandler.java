package cec.jiutian.bc.instrumentManagement.domain.instrumentInventory.handler;

import cec.jiutian.bc.instrumentManagement.domain.instrumentInventory.model.LimsInstrumentInventory;
import cec.jiutian.bc.instrumentManagement.enums.InstrumentStatusEnum;
import cec.jiutian.bc.sparePartsManagement.domain.inventoryLedger.enums.InventoryLedgerStatusEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@Component
public class LimsInstrumentEnableHandler implements OperationHandler<LimsInstrumentInventory, LimsInstrumentInventory> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<LimsInstrumentInventory> data, LimsInstrumentInventory modelObject, String[] param) {
        if (CollectionUtils.isEmpty(data)) {
            throw new RuntimeException("数据为空");
        }
        LimsInstrumentInventory limsInstrumentInventory = data.get(0);
        limsInstrumentInventory.setBusinessState(InventoryLedgerStatusEnum.Enum.normal.name());
        limsInstrumentInventory.setEnableDate(new Date());
        fabosJsonDao.mergeAndFlush(limsInstrumentInventory);
        return "alert('执行成功')";
    }
}
