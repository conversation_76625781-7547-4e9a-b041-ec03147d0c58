package cec.jiutian.bc.instrumentManagement.domain.instrumentInventory.mto;

import cec.jiutian.bc.enums.InventoryMaterialTypeEnum;
import cec.jiutian.bc.instrumentManagement.domain.instrumentInventory.handler.InsBlockCleanDynamicHandler;
import cec.jiutian.bc.instrumentManagement.domain.instrumentInventory.handler.InsShelfCleanDynamicHandler;
import cec.jiutian.bc.instrumentManagement.domain.instrumentInventory.handler.InstrumentEnableHandler;
import cec.jiutian.bc.instrumentManagement.domain.instrumentInventory.handler.MaterialInfoDynamicHandler;
import cec.jiutian.bc.instrumentManagement.domain.instrumentInventory.model.InstrumentInventoryOpening;
import cec.jiutian.bc.instrumentManagement.domain.instrumentInventory.proxy.InstrumentInventoryDataProxy;
import cec.jiutian.bc.mto.*;
import cec.jiutian.bc.sparePartsManagement.domain.inventoryLedger.enums.InventoryLedgerStatusEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowBaseOperation;
import cec.jiutian.view.type.RowOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;

import java.util.Date;

/**
 * <AUTHOR>
 * @description:  lims最开始需要引入了eam的仪器仪表数据单独管理。后期客户又要求去掉使用eam的仪器仪表使用eam  外部表
 */
@Getter
@Setter
@Entity
@Table(name = "eam_inventory_ledger_eam")
@FabosJson(
        name = "仪表仪器台账",
        filter = @Filter("materialType = 'instrument'"),
        orderBy = "stockInDate desc",
        dataProxy = InstrumentInventoryDataProxy.class,
        power = @Power(export = true),
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "businessState!='edit'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "businessState!='edit'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
        },
        rowOperation = {
                @RowOperation(
                        title = "启用",
                        code = "InstrumentInventory@ENABLE",
                        operationHandler = InstrumentEnableHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "确定启用吗？",
                        ifExpr = "businessState!='edit'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "InstrumentInventory@ENABLE"
                        )
                ),
                @RowOperation(
                        title = "期初录入",
                        code = "InstrumentInventory@OPENING",
                        fabosJsonClass = InstrumentInventoryOpening.class,
                        mode = RowOperation.Mode.BUTTON,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.ADD,
                        popupType = RowOperation.PopupType.FORM,
                        ifExpr = "1 != 1",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "InstrumentInventory@OPENING"
                        )
                ),
        }
)
public class InstrumentInventoryMTO extends MetaModel {
    @Transient
    @FabosJsonField(
            views = @View(title = "选择物料台账", show = false, column = "name"),
            edit = @Edit(title = "选择物料台账",
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    filter = @Filter(value = "specificationCode ='0111'"))
    )
    private SpecificationManage specificationManage;

    @FabosJsonField(
            views = @View(title = "仪器编号"),
            edit = @Edit(title = "仪器编号",
                    readonly = @Readonly(add = true, edit = true),
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "specificationManage", beFilledBy = "code"))

    )
    private String materialCode;

    @FabosJsonField(
            views = @View(title = "名称"),
            edit = @Edit(title = "名称",
                    readonly = @Readonly(add = true, edit = true),
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "specificationManage", beFilledBy = "name"))
    )
    private String materialName;

    @Transient
    @FabosJsonField(
            views = @View(title = "选择物料信息", show = false, column = "materialName"),
            edit = @Edit(title = "选择物料信息",
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "stockOutDetailCode"),
                    queryCondition = "{\"materialCode\":\"${materialCode}\"}",
                    filter = @Filter("StockOutDetail.stockOut.currentState = 'COMPLETE'"),
                    dependFieldDisplay = @DependFieldDisplay(enableOrDisable = "specificationManage == null")
            )
    )
    private StockOutDetail stockOutDetail;

    @FabosJsonField(
            views = @View(title = "件次号"),
            edit = @Edit(title = "件次号",
                    notNull = true,
                    readonly = @Readonly,
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled =
            @LinkedFiled(changeBy = "stockOutDetail", beFilledBy = "factoryLotIdentifier"))
    )
    private String lotSerialId;

    @FabosJsonField(
            views = @View(title = "原炉/原厂批号"),
            edit = @Edit(title = "原炉/原厂批号",
                    readonly = @Readonly,
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled =
            @LinkedFiled(changeBy = "stockOutDetail", beFilledBy = "materialLotIdentifier"))
    )
    private String supplierLotSerialId;

    @FabosJsonField(
            views = @View(title = "规格/型号"),
            edit = @Edit(title = "规格/型号", readonly = @Readonly),
            dynamicField = @DynamicField(
                    linkedFiled = @LinkedFiled(changeBy = "stockOutDetail", beFilledBy = "materialSpecification"))
    )
    private String materialSpecification;

    @FabosJsonField(
            views = @View(title = "单位"),
            edit = @Edit(title = "单位",
                    readonly = @Readonly
            ),
            dynamicField = @DynamicField(
                    linkedFiled = @LinkedFiled(changeBy = "stockOutDetail", beFilledBy = "unit"))
    )
    private String accountingUnit;

    @FabosJsonField(
            views = @View(title = "批次总数量"),
            edit = @Edit(title = "批次总数量", show = false,
                    inputType = @InputType(type = "number")
            )
    )
    private Double accountingUnitQuantity;

    @FabosJsonField(
            views = @View(title = "可用数量"),
            edit = @Edit(title = "可用数量",
                    show = false,
                    search = @Search(vague = true),
                    inputType = @InputType(type = "number"),
                    numberType = @NumberType(min = 0, precision = 2)
            )
    )
    private Double availableQuantity;

    @FabosJsonField(
            views = @View(title = "物料类别"),
            edit = @Edit(title = "物料类别",
                    readonly = @Readonly,
                    defaultVal = "instrument",
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(
                            fetchHandler = InventoryMaterialTypeEnum.class
                    )
            ),
            dynamicField = @DynamicField(passive = true)
    )
    private String materialType;

    @FabosJsonField(
            views = @View(title = "库存批次号"),
            edit = @Edit(title = "库存批次号",
                    readonly = @Readonly,
                    search = @Search(vague = true),
                    inputType = @InputType(length = 30)
            ),
            dynamicField = @DynamicField(passive = true)
    )
    private String inventoryCode;

    @FabosJsonField(
            views = @View(title = "供应商id", show = false),
            edit = @Edit(title = "供应商id", show = false),
            dynamicField = @DynamicField(dependFiled =
            @DependFiled(changeBy = {"stockOutDetail"}, dynamicHandler = MaterialInfoDynamicHandler.class))
    )
    private String supplierId;

    @FabosJsonField(
            views = @View(title = "供应商"),
            edit = @Edit(title = "供应商",
                    readonly = @Readonly,
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(passive = true)
    )
    private String supplierName;

    @Transient
    @FabosJsonField(
            views = @View(title = "选择负责人", show = false, column = "name"),
            edit = @Edit(title = "选择负责人",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType()
            )
    )
    private UserMTO reporterMTO;

    @FabosJsonField(
            views = @View(title = "负责人"),
            edit = @Edit(title = "负责人",
                    search = @Search(vague = true),
                    notNull = true,
                    readonly = @Readonly(add = true, edit = true),
                    inputType = @InputType(length = 30)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "reporterMTO",
                    beFilledBy = "name"))
    )
    @Column(length = 30)
    private String keeper;

    @FabosJsonField(
            views = @View(title = "负责人id", show = false),
            edit = @Edit(title = "负责人id",
                    show = false,
                    notNull = true,
                    inputType = @InputType(length = 30)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "reporterMTO",
                    beFilledBy = "id"))
    )
    private String keeperId;

    @Transient
    @FabosJsonField(
            views = @View(title = "选择仓库", column = "warehouseName", show = false),
            edit = @Edit(title = "选择仓库",
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "warehouseName")
            )
    )
    private WarehouseMTO warehouseMTO;

    @FabosJsonField(
            views = @View(title = "仓库名称"),
            edit = @Edit(title = "仓库名称",
                    notNull = true,
                    readonly = @Readonly,
                    inputType = @InputType(length = 20)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "warehouseMTO",
                    beFilledBy = "warehouseName"), passive = true)
    )
    private String warehouseName;

    @FabosJsonField(
            views = @View(title = "仓库ID", show = false),
            edit = @Edit(title = "仓库ID", show = false,
                    inputType = @InputType(type = "number")
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "warehouseMTO",
                    beFilledBy = "id"))
    )
    private Long warehouseId;

    @Transient
    @FabosJsonField(
            views = @View(title = "选择库区", column = "blockName", show = false),
            edit = @Edit(title = "选择库区",
                    type = EditType.REFERENCE_TABLE,
                    queryCondition = "{\"warehouseId\":\"${warehouseId}\"}",
                    referenceTableType = @ReferenceTableType(id = "id", label = "blockName"),
                    dependFieldDisplay = @DependFieldDisplay(enableOrDisable = "warehouseName == null")
            ),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "warehouseMTO",
                    dynamicHandler = InsBlockCleanDynamicHandler.class))
    )
    private WarehouseBlockMTO warehouseBlockMTO;


    @FabosJsonField(
            views = @View(title = "库区名称"),
            edit = @Edit(title = "库区名称",
                    readonly = @Readonly,
                    inputType = @InputType(length = 20)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "warehouseBlockMTO",
                    beFilledBy = "blockName"), passive = true)
    )
    private String blockName;

    @FabosJsonField(
            views = @View(title = "库区ID", show = false),
            edit = @Edit(title = "库区ID", show = false,
                    inputType = @InputType(type = "number"),
                    numberType = @NumberType(precision = 0)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "warehouseBlockMTO",
                    beFilledBy = "id"))
    )
    private Long blockId;

    @Transient
    @FabosJsonField(
            views = @View(title = "选择货位", column = "locationName", show = false),
            edit = @Edit(title = "选择货位",
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "locationName"),
                    queryCondition = "{\"blockId\":\"${blockId}\"}",
                    dependFieldDisplay = @DependFieldDisplay(enableOrDisable = "blockName == null")
            ),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "warehouseBlockMTO",
                    dynamicHandler = InsShelfCleanDynamicHandler.class))
    )
    private ShelfMTO shelf;

    @FabosJsonField(
            views = @View(title = "货位名称"),
            edit = @Edit(title = "货位名称",
                    readonly = @Readonly,
                    inputType = @InputType(length = 20)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "shelf",
                    beFilledBy = "locationName"), passive = true)
    )
    private String shelfName;

    @FabosJsonField(
            views = @View(title = "货位ID", show = false),
            edit = @Edit(title = "货位ID", show = false,
                    inputType = @InputType(type = "number")
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "shelf",
                    beFilledBy = "id"))
    )
    private Long shelfId;
    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",
                    defaultVal = "normal",
                    type = EditType.CHOICE,
                    readonly = @Readonly,
                    choiceType = @ChoiceType(fetchHandler = InventoryLedgerStatusEnum.class)
            ),
            dynamicField = @DynamicField(passive = true)
    )
    private String businessState;

    @FabosJsonField(
            views = @View(title = "启用日期"),
            edit = @Edit(title = "启用日期",
                    readonly = @Readonly,
                    dateType = @DateType(type = DateType.Type.DATE)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private Date enableDate;

    @FabosJsonField(
            views = @View(title = "是否必检"),
            edit = @Edit(title = "是否必检",
                    notNull = true,
                    type = EditType.BOOLEAN,
                    defaultVal = "false"
            )
    )
    @Column(nullable = false)
    private Boolean inspection;

    @FabosJsonField(
            views = @View(title = "领用部门"),
            edit = @Edit(title = "领用部门",
                    show = false
            )
    )
    private String borrowDep;

    @FabosJsonField(
            views = @View(title = "入库日期"),
            edit = @Edit(title = "入库日期",
                    readonly = @Readonly,
                    search = @Search(vague = true),
                    dateType = @DateType(type = DateType.Type.DATE)
            )
    )
    @CreationTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private Date stockInDate;

    @FabosJsonField(
            views = @View(title = "保质期天数"),
            edit = @Edit(title = "保质期天数",
                    notNull = true
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "specificationManage", beFilledBy = "guaranteeTime"))
    )
    private Integer guaranteeTime;

    //有效期
    @FabosJsonField(
            views = @View(title = "有效期"),
            edit = @Edit(title = "有效期",
                    notNull = true,
                    search = @Search(vague = true),
                    dateType = @DateType(type = DateType.Type.DATE)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private Date expireDate;
}
