package cec.jiutian.bc.process.domain.reagentConfigRequest.model;

import cec.jiutian.bc.process.domain.reagentConfigRequest.handler.ReagentConfigRequestDetailDynamicHandler;
import cec.jiutian.bc.process.domain.reagentConfigRequest.handler.ReagentConfigRequestDetailReferenceAddHandler;
import cec.jiutian.bc.process.domain.reagentConfigRequest.handler.ReagentDetailQuantityDynamicHandler;
import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.*;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @description:
 */
@FabosJson(
        name = "明细",
        orderBy = "ReagentConfigRequestDetail.createTime desc"
)
@Table(name = "lm_reagent_request_detail")
@Entity
@Getter
@Setter
public class ReagentConfigRequestDetail extends BaseModel {
    @FabosJsonField(
            views = {
                    @View(title = "试剂配置申请", column = "generalCode", show = false)
            },
            edit = @Edit(title = "试剂配置申请", type = EditType.REFERENCE_TABLE, show = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode")
            )
    )
    @ManyToOne
    @JsonIgnoreProperties("reagentConfigRequestDetails")
    private ReagentConfigRequest reagentConfigRequest;
    @FabosJsonField(
            views = @View(title = "配剂名称", width = "25%"),
            edit = @Edit(title = "配剂名称", notNull = true)
    )
    private String formulaName;

    @FabosJsonField(
            views = @View(title = "配剂物料编码", width = "25%"),
            edit = @Edit(title = "配剂物料编码", notNull = true)
    )
    private String formulaMaterialCode;

    @FabosJsonField(
            views = @View(title = "标准用量"),
            edit = @Edit(title = "标准用量", notNull = true,inputType = @InputType(length = 7), numberType = @NumberType(min = 0, precision = 2))
    )
    private Double standardUsage;
    //实际用量  一般和标准用量一样  可编辑
    @FabosJsonField(
            views = @View(title = "实际用量"),
            edit = @Edit(title = "实际用量", notNull = true,readonly = @Readonly,inputType = @InputType(length = 7), numberType = @NumberType(min = 0, precision = 2)),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "reagentConfigRequestDetailWares", dynamicHandler = ReagentDetailQuantityDynamicHandler.class))

    )
    private Double actualUsage;
    @FabosJsonField(
            views = @View(title = "单位"),
            edit = @Edit(title = "单位")
    )
    private String unit;

    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumn(name = "reagent_config_request_detail_id")
    @OrderBy
    @FabosJsonField(
            edit = @Edit(title = "配剂消耗明细", type = EditType.TAB_REFER_ADD),
            referenceAddType = @ReferenceAddType(referenceClass = "LineInventoryDetail", queryCondition = "{ \"materialCode\": \"${formulaMaterialCode}\", \"quantity\": \"1,\"}", editable = {"quantity"},
                    referenceAddHandler = ReagentConfigRequestDetailReferenceAddHandler.class),
            views = @View(title = "配剂消耗明细", type = ViewType.TABLE_VIEW, extraPK = "inventoryDetailId", column = "materialName")
    )
    private List<ReagentConfigRequestDetailWare> reagentConfigRequestDetailWares;
}
