package cec.jiutian.bc.process.domain.inspectionTask.handler;

import cec.jiutian.bc.equipmentArchive.domain.equipmentArchive.model.EquipmentArchive;
import cec.jiutian.bc.equipmentArchive.enumeration.EquipmentBusinessStateEnum;
import cec.jiutian.bc.process.domain.inspectionTask.model.DetailEquipPrepare;
import cec.jiutian.bc.process.enumeration.PrepareStatusEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.DependFiled;
import com.alibaba.cloud.commons.lang.StringUtils;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 设备检验合格--编辑表单时触发
 * <AUTHOR>
 * @date 2025/7/16 16:24
 */
@Component
public class EquipDynamicHandler implements DependFiled.DynamicHandler<DetailEquipPrepare> {
    @Resource
    private FabosJsonDao fabosJsonDao;
    // 故障预警
    private static final String FAULT_WARNING = EquipmentBusinessStateEnum.Enum.FaultWarning.name();
    // 维修中
    private static final String REPAIRING = EquipmentBusinessStateEnum.Enum.Repairing.name();

    @Override
    public Map<String, Object> handle(DetailEquipPrepare detailEquipPrepare) {
        Map<String, Object> map = new HashMap<>();
        String equipArchiveCode = detailEquipPrepare.getEquipArchiveCode();
        if (StringUtils.isNotBlank(equipArchiveCode)) {
            // 根据设备台账编码查询设备台账信息
            EquipmentArchive query = new EquipmentArchive();
            query.setGeneralCode(equipArchiveCode);
            EquipmentArchive equipmentArchive = fabosJsonDao.selectOne(query);
            if (equipmentArchive != null) {
                map.put("equipName", equipmentArchive.getName());
                // 设备编码
                String code = equipmentArchive.getEquipment().getCode();
                String businessState = equipmentArchive.getBusinessState();
                // 故障预警 && 维修中 则不通过
                if (FAULT_WARNING.equals(businessState) || REPAIRING.equals(businessState)) {
                    map.put("equipInspectStatus", PrepareStatusEnum.Enum.NOT_PASS.name());
                } else if (code.equals(detailEquipPrepare.getTestDeviceCode())) {
                    map.put("equipInspectStatus", PrepareStatusEnum.Enum.PASS.name());
                } else
                    map.put("equipInspectStatus", PrepareStatusEnum.Enum.NOT_PASS.name());
            } else {
                map.put("equipName", "");
                map.put("equipInspectStatus", PrepareStatusEnum.Enum.NOT_PASS.name());
            }
        } else {
            map.put("equipName", "");
            map.put("equipInspectStatus", PrepareStatusEnum.Enum.NOT_PASS.name());
        }
        return map;
    }
}
