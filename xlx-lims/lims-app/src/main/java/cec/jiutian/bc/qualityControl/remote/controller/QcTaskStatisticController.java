package cec.jiutian.bc.qualityControl.remote.controller;

import cec.jiutian.api.remoteCallLog.dto.RemoteCallResult;
import cec.jiutian.bc.base.domain.experimentMethod.service.ExpMethodService;
import cec.jiutian.bc.base.domain.lab.service.LabService;
import cec.jiutian.bc.person.domain.personManage.service.PersonManageService;
import cec.jiutian.bc.qualityControl.domain.qcTask.service.QcTaskService;
import cec.jiutian.bc.qualityControl.dto.QcQueryDTO;
import cec.jiutian.bc.qualityControl.enumeration.QcMethodEnum;
import cec.jiutian.bc.qualityControl.vo.QcTaskVO;
import cec.jiutian.bc.qualityControl.vo.QueryVO;
import cec.jiutian.core.frame.constant.FabosJsonRestPath;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 质控任务统计controller
 * <AUTHOR>
 * @date 2025/6/10 17:07
 */
@RestController
@RequestMapping("/fabos-lims-app"+ FabosJsonRestPath.FABOS_REMOTE_API)
public class QcTaskStatisticController {
    @Resource
    private LabService labService;
    @Resource
    private ExpMethodService expMethodService;
    @Resource
    private PersonManageService personManageService;
    @Resource
    private QcTaskService qcTaskService;
    // 短期质控
    private static final String SHORT = QcMethodEnum.Enum.SHORT_TERM_STABILITY.name();
    // 长期质控
    private static final String LONG = QcMethodEnum.Enum.LONG_TERM_STABILITY.name();
    // 标样
    private static final String ACCURACY = QcMethodEnum.Enum.LONG_TERM_ACCURACY.name();

    /**
     * 1、查询全部实验室
     * <AUTHOR>
     * @date 2025/6/10 17:28
     * @return
     */
    @RequestMapping("/getAllExpLabList2")
    public RemoteCallResult<List<QueryVO>> getAllExpLabList() {
        return RemoteCallResult.success(labService.getAllExpLabList2());
    }

    /**
     * 2、查询全部检测项目
     * <AUTHOR>
     * @date 2025/6/10 17:36
     * @return
     */
    @RequestMapping("/getAllInsItemList")
    public RemoteCallResult<List<QueryVO>> getAllInsItemList() {
        return RemoteCallResult.success(expMethodService.getAllInsItemList(null));
    }

    /**
     * 3、查询全部检测指标
     * <AUTHOR>
     * @date 2025/6/10 17:38
     * @param requestBody 请求体
     * @return
     */
    @RequestMapping("/getAllInsItemQuotaList")
    public RemoteCallResult<List<QueryVO>> getAllInsItemQuotaList(@RequestBody Map<String, String> requestBody) {
        String itemCode = requestBody.get("itemCode");
        return RemoteCallResult.success(expMethodService.getAllInsItemQuotaList(itemCode));
    }

    /**
     * 4、查询全部设备类型
     * <AUTHOR>
     * @date 2025/6/10 17:42
     * @return
     */
    @RequestMapping("/getAllequipTypeList")
    public RemoteCallResult<List<QueryVO>> getAllEquipTypeList() {
        return RemoteCallResult.success(labService.getAllEquipTypeList());
    }

    /**
     * 5、查询全部设备编号
     * <AUTHOR>
     * @date 2025/6/10 17:42
     * @return
     */
    @RequestMapping("/getAllEquipCodeList")
    public RemoteCallResult<List<QueryVO>> getAllEquipCodeList() {
        return RemoteCallResult.success(labService.getAllEquipCodeList());
    }

    /**
     * 6、查询全部人员
     * <AUTHOR>
     * @date 2025/6/10 17:47
     * @return
     */
    @RequestMapping("/getAllPersonList")
    public RemoteCallResult<List<QueryVO>> getAllPersonList() {
        return RemoteCallResult.success(personManageService.getAllPersonList());
    }

    /**
     * 7、查询质控样/标样
     * <AUTHOR>
     * @date 2025/6/16 11:32
     * @return
     */
    @RequestMapping("/getAllSampleList")
    public RemoteCallResult<List<QueryVO>> getAllSampleList() {
        return RemoteCallResult.success(qcTaskService.getAllSampleList());
    }

    /**
     * 查询短期质控列表by检测项目
     * <AUTHOR>
     * @date 2025/6/11 9:57
     * @return
     */
    @RequestMapping("/getShortQcListByItem")
    public RemoteCallResult<List<QcTaskVO>> getShortQcListByItem(@RequestBody QcQueryDTO qcQueryDTO) {
        return RemoteCallResult.success(qcTaskService.getQcListByItem(qcQueryDTO, SHORT));
    }

    /**
     * 查询短期质控列表by设备类型
     * <AUTHOR>
     * @date 2025/6/11 9:57
     * @return
     */
    @RequestMapping("/getShortQcListByEquipType")
    public RemoteCallResult<List<QcTaskVO>> getShortQcListByEquipType(@RequestBody QcQueryDTO qcQueryDTO) {
        return RemoteCallResult.success(qcTaskService.getQcListByEquipType(qcQueryDTO, SHORT));
    }

    /**
     * 查询短期质控列表by设备编号
     * <AUTHOR>
     * @date 2025/6/11 9:57
     * @return
     */
    @RequestMapping("/getShortQcListByEquipCode")
    public RemoteCallResult<List<QcTaskVO>> getShortQcListByEquipCode(@RequestBody QcQueryDTO qcQueryDTO) {
        return RemoteCallResult.success(qcTaskService.getQcListByEquipCode(qcQueryDTO, SHORT));
    }

    /**
     * 查询短期质控列表by人员
     * <AUTHOR>
     * @date 2025/6/11 9:57
     * @return
     */
    @RequestMapping("/getShortQcListByPerson")
    public RemoteCallResult<List<QcTaskVO>> getShortQcListByPerson(@RequestBody QcQueryDTO qcQueryDTO) {
        return RemoteCallResult.success(qcTaskService.getQcListByPerson(qcQueryDTO, SHORT));
    }

    /**
     * 查询短期质控超限分布by实验室
     * <AUTHOR>
     * @date 2025/6/11 9:57
     * @return
     */
    @RequestMapping("/getShortQcOutListByExplab")
    public RemoteCallResult<List<QcTaskVO>> getShortQcDisListByExplab(@RequestBody QcQueryDTO qcQueryDTO) {
        return RemoteCallResult.success(qcTaskService.getQcDisListByExplab(qcQueryDTO, SHORT));
    }

    /**
     * 查询短期质控超限分布by检测室
     * <AUTHOR>
     * @date 2025/6/11 9:57
     * @return
     */
    @RequestMapping("/getShortQcOutListByLab")
    public RemoteCallResult<List<QcTaskVO>> getShortQcDisListByLab(@RequestBody QcQueryDTO qcQueryDTO) {
        return RemoteCallResult.success(qcTaskService.getQcDisListByLab(qcQueryDTO, SHORT));
    }

    /**
     * 查询长期质控列表by检测项目
     * <AUTHOR>
     * @date 2025/6/11 9:57
     * @return
     */
    @RequestMapping("/getLongQcListByItem")
    public RemoteCallResult<Map<String, List<QcTaskVO>>> getLongQcListByItem(@RequestBody QcQueryDTO qcQueryDTO) {
        return RemoteCallResult.success(qcTaskService.getLongOrAccQcListByItem(qcQueryDTO, LONG));
    }

    /**
     * 查询长期质控列表by设备类型
     * <AUTHOR>
     * @date 2025/6/11 9:57
     * @return
     */
    @RequestMapping("/getLongQcListByEquipType")
    public RemoteCallResult<List<QcTaskVO>> getLongQcListByEquipType(@RequestBody QcQueryDTO qcQueryDTO) {
        return RemoteCallResult.success(qcTaskService.getQcListByEquipType(qcQueryDTO, LONG));
    }

    /**
     * 查询长期质控列表by设备编号
     * <AUTHOR>
     * @date 2025/6/11 9:57
     * @return
     */
    @RequestMapping("/getLongQcListByEquipCode")
    public RemoteCallResult<List<QcTaskVO>> getLongQcListByEquipCode(@RequestBody QcQueryDTO qcQueryDTO) {
        return RemoteCallResult.success(qcTaskService.getQcListByEquipCode(qcQueryDTO, LONG));
    }

    /**
     * 查询长期质控列表by人员
     * <AUTHOR>
     * @date 2025/6/11 9:57
     * @return
     */
    @RequestMapping("/getLongQcListByPerson")
    public RemoteCallResult<List<QcTaskVO>> getLongQcListByPerson(@RequestBody QcQueryDTO qcQueryDTO) {
        return RemoteCallResult.success(qcTaskService.getQcListByPerson(qcQueryDTO, LONG));
    }

    /**
     * 查询长期质控超限分布by实验室
     * <AUTHOR>
     * @date 2025/6/11 9:57
     * @return
     */
    @RequestMapping("/getLongQcOutListByExplab")
    public RemoteCallResult<List<QcTaskVO>> getLongQcDisListByExplab(@RequestBody QcQueryDTO qcQueryDTO) {
        return RemoteCallResult.success(qcTaskService.getQcDisListByExplab(qcQueryDTO, LONG));
    }

    /**
     * 查询长期质控超限分布by检测室
     * <AUTHOR>
     * @date 2025/6/11 9:57
     * @return
     */
    @RequestMapping("/getLongQcOutListByLab")
    public RemoteCallResult<List<QcTaskVO>> getLongQcDisListByLab(@RequestBody QcQueryDTO qcQueryDTO) {
        return RemoteCallResult.success(qcTaskService.getQcDisListByLab(qcQueryDTO, LONG));
    }


    /**
     * 查询标样质控列表by检测项目
     * <AUTHOR>
     * @date 2025/6/11 9:57
     * @return
     */
    @RequestMapping("/getAccQcListByItem")
    public RemoteCallResult<Map<String, List<QcTaskVO>>> getAccQcListByItem(@RequestBody QcQueryDTO qcQueryDTO) {
        return RemoteCallResult.success(qcTaskService.getLongOrAccQcListByItem(qcQueryDTO, ACCURACY));
    }

    /**
     * 查询标样质控列表by设备类型
     * <AUTHOR>
     * @date 2025/6/11 9:57
     * @return
     */
    @RequestMapping("/getAccQcListByEquipType")
    public RemoteCallResult<List<QcTaskVO>> getAccQcListByEquipType(@RequestBody QcQueryDTO qcQueryDTO) {
        return RemoteCallResult.success(qcTaskService.getQcListByEquipType(qcQueryDTO, ACCURACY));
    }

    /**
     * 查询标样质控列表by设备编号
     * <AUTHOR>
     * @date 2025/6/11 9:57
     * @return
     */
    @RequestMapping("/getAccQcListByEquipCode")
    public RemoteCallResult<List<QcTaskVO>> getAccQcListByEquipCode(@RequestBody QcQueryDTO qcQueryDTO) {
        return RemoteCallResult.success(qcTaskService.getQcListByEquipCode(qcQueryDTO, ACCURACY));
    }

    /**
     * 查询标样质控列表by人员
     * <AUTHOR>
     * @date 2025/6/11 9:57
     * @return
     */
    @RequestMapping("/getAccQcListByPerson")
    public RemoteCallResult<List<QcTaskVO>> getAccQcListByPerson(@RequestBody QcQueryDTO qcQueryDTO) {
        return RemoteCallResult.success(qcTaskService.getQcListByPerson(qcQueryDTO, ACCURACY));
    }

    /**
     * 查询标样质控超限分布by实验室
     * <AUTHOR>
     * @date 2025/6/11 9:57
     * @return
     */
    @RequestMapping("/getAccQcDisListByExplab")
    public RemoteCallResult<List<QcTaskVO>> getAccQcDisListByExplab(@RequestBody QcQueryDTO qcQueryDTO) {
        return RemoteCallResult.success(qcTaskService.getQcDisListByExplab(qcQueryDTO, ACCURACY));
    }

    /**
     * 查询标样质控超限分布by检测室
     * <AUTHOR>
     * @date 2025/6/11 9:57
     * @return
     */
    @RequestMapping("/getAccQcDisListByLab")
    public RemoteCallResult<List<QcTaskVO>> getAccQcDisListByLab(@RequestBody QcQueryDTO qcQueryDTO) {
        return RemoteCallResult.success(qcTaskService.getQcDisListByLab(qcQueryDTO, ACCURACY));
    }

}
