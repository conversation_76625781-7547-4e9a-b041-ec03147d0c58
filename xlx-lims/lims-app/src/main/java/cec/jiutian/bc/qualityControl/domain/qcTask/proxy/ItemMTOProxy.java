package cec.jiutian.bc.qualityControl.domain.qcTask.proxy;

import cec.jiutian.bc.base.domain.experimentMethod.model.ExpMethod;
import cec.jiutian.bc.base.domain.experimentMethod.mto.InspectionItemMTO;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import cec.jiutian.view.query.Condition;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class ItemMTOProxy implements DataProxy<InspectionItemMTO> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String beforeFetch(List<Condition> conditions) {
        ExpMethod qeuryCondition = new ExpMethod();
        List<ExpMethod> expMethodDbList = fabosJsonDao.select(qeuryCondition);
        // 取出expMethodDbList的InspectionItemMTO的id集合
        List<String> expMethodDbIdList = expMethodDbList.stream()
                .map(ExpMethod::getInspectionItemMTOId)
                .collect(Collectors.toList());
        // 拼接id, 如：  'a','b','c'
        String result = expMethodDbIdList.stream()
                .map(s -> "'" + s + "'")
                .collect(Collectors.joining(","));
        // 生成hql
        String hql = "ItemMTO.id in (" + result + ")";
        return hql;
    }
}
