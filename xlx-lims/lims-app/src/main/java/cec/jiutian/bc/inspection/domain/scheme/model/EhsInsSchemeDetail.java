package cec.jiutian.bc.inspection.domain.scheme.model;

import cec.jiutian.bc.inspection.enumeration.ResultRecordEnum;
import cec.jiutian.bc.urm.domain.dictionary.handler.DictChoiceFetchHandler;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.FabosJsonI18n;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.RowBaseOperation;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@FabosJson(name = "巡检项目清单",
        orderBy = "EhsInsSchemeDetail.createTime desc",
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "'1' == '1'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.HIDE
                )
        }
)
@Entity
@Getter
@Setter
@Table(name = "lm_ins_scheme_detail")
@FabosJsonI18n
@TemplateType(type = "multiTable")
public class EhsInsSchemeDetail extends MetaModel {
    // 巡检项名称
    @FabosJsonField(
            views = @View(title = "巡检项名称"),
            edit = @Edit(title = "巡检项名称", search = @Search,notNull = true)
    )
    private String name;

    // 巡检内容
    @FabosJsonField(
            views = @View(title = "巡检内容"),
            edit = @Edit(title = "巡检内容", notNull = true)
    )
    private String content;

    // 类型
    @FabosJsonField(
            views = @View(title = "类型"),
            edit = @Edit(title = "类型",type = EditType.CHOICE, notNull = true,
                    choiceType = @ChoiceType(fetchHandler = DictChoiceFetchHandler.class,
                            fetchHandlerParams = "LM_INS_ITEM_TYPE"),search = @Search(vague = true))
    )
    private String type;

    //结果记录方式
    @FabosJsonField(
            views = @View(title = "结果记录方式"),
            edit = @Edit(title = "结果记录方式",type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ResultRecordEnum.class))
    )
    private String resultRecord;

    // 正常值
    @FabosJsonField(
            views = @View(title = "正常值",toolTip = true),
            edit = @Edit(title = "正常值", desc = "一行数据为一个标准值", type = EditType.TEXTAREA,
                    inputType = @InputType(length = 200))
    )
    private String normalValue;

    // 异常值
    @FabosJsonField(
            views = @View(title = "异常值", toolTip = true),
            edit = @Edit(title = "异常值", desc = "一行数据为一个标准值", type = EditType.TEXTAREA,
                    inputType = @InputType(length = 200))
    )
    private String abnormalValue;

    // 上限值
    @FabosJsonField(
            views = @View(title = "上限值"),
            edit = @Edit(title = "上限值", inputType = @InputType(length = 7), numberType = @NumberType(min = 0,precision = 2))
    )
    private Double upperValue;

    // 下限值
    @FabosJsonField(
            views = @View(title = "下限值"),
            edit = @Edit(title = "下限值", inputType = @InputType(length = 7), numberType = @NumberType(min = 0,precision = 2))
    )
    private Double lowerValue;

    // 标准值
    @FabosJsonField(
            views = @View(title = "标准值"),
            edit = @Edit(title = "标准值", inputType = @InputType(length = 7), numberType = @NumberType(min = 0,precision = 2))
    )
    private String standardValue;

//    @FabosJsonField(
//            views = @View(title = "标准工时"),
//            edit = @Edit(title = "标准工时", inputType = @InputType(length = 7), numberType = @NumberType(min = 0, precision = 1),
//                    inputGroup = @InputGroup(postfix = "小时")
//            )
//    )
//    private Double standardHour;

    // 是否拍照
    @FabosJsonField(
            views = @View(title = "是否拍照"),
            edit = @Edit(title = "是否拍照")
    )
    private Boolean isTakePhoto;

    // 巡检项id, 不展示 用于排除重复选择
    @FabosJsonField(
            views = @View(title = "巡检项id", show = false),
            edit = @Edit(title = "巡检项id", show = false)
    )
    private String ehsInsItemId;
}
