package cec.jiutian.bc.instrumentManagement.domain.instrumentInventory.proxy;

import cec.jiutian.bc.instrumentManagement.domain.instrumentInventory.model.LimsInstrumentInventory;
import cec.jiutian.view.fun.DataProxy;

public class LimsInstrumentInventoryDataProxy implements DataProxy<LimsInstrumentInventory> {

    @Override
    public void beforeAdd(LimsInstrumentInventory limsInstrumentInventory) {
        limsInstrumentInventory.setAccountingUnitQuantity(1D);
        limsInstrumentInventory.setAvailableQuantity(1D);
    }
}
