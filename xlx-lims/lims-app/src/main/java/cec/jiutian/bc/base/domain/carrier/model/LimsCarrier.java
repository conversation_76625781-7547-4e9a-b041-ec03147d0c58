package cec.jiutian.bc.base.domain.carrier.model;

import cec.jiutian.bc.base.domain.carrier.proxy.LimsCarrierProxy;
import cec.jiutian.bc.base.domain.shelves.model.LimsShelves;
import cec.jiutian.bc.base.enumeration.LmNamingRuleCodeEnum;
import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleBaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.FabosJsonI18n;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.DependFieldDisplay;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @description:
 */
@Entity
@Table(name = "lm_carrier",uniqueConstraints = {
        @UniqueConstraint(columnNames = {"generalCode"})
})
@Getter
@Setter
@FabosJson(name = "载具",dataProxy = LimsCarrierProxy.class,
        orderBy = "LimsCarrier.createTime desc",power = @Power(export = false,importable = false,print = false)
)
@FabosJsonI18n
public class LimsCarrier extends NamingRuleBaseModel {
    @Override
    public String getNamingCode() {
        return LmNamingRuleCodeEnum.LIMS_CARRIER.name();
    }

    @FabosJsonField(
            views = @View(title = "货架",column = "name",index = 1),
            edit = @Edit(title = "货架",
                    allowAddMultipleRows = false,
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType()
            )
    )
    @ManyToOne
    private LimsShelves limsShelves;


    @FabosJsonField(
            views = @View(title = "载具名称",index = 2),
            edit = @Edit(title = "载具名称",notNull = true,search = @Search(vague = true))
    )
    private String name;

    @FabosJsonField(
            views = @View(title = "载具描述", toolTip = true,index = 3),
            edit = @Edit(title = "载具描述", search = @Search(vague = true),
                    type = EditType.TEXTAREA,
                    inputType = @InputType(length = 200)
            )
    )
    private String description;
    //规格
    @FabosJsonField(
            views = @View(title = "载具规格",index = 4),
            edit = @Edit(title = "载具规格")
    )
    private String specification;

    //工序号
    @FabosJsonField(
            views = @View(title = "载具工序号",index = 5),
            edit = @Edit(title = "载具工序号",notNull = true,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "limsShelves.isCheckSample == true")
            )
    )
    private String processNo;



}
