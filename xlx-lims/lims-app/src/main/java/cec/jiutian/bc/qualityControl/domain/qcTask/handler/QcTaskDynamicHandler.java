package cec.jiutian.bc.qualityControl.domain.qcTask.handler;

import cec.jiutian.bc.person.domain.personManage.model.PersonManage;
import cec.jiutian.bc.qualityControl.domain.qcPlan.model.QcPlan;
import cec.jiutian.bc.qualityControl.domain.qcTask.model.QcTask;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class QcTaskDynamicHandler implements DependFiled.DynamicHandler<QcTask>{
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public Map<String, Object> handle(QcTask qcTask) {
        HashMap<String, Object> map = new HashMap<>();
        QcPlan qcPlan = qcTask.getQcPlan();
        if (qcPlan == null) {
            return emptyMap();
        }
        map.put("qcProjectCode", qcPlan.getQcProjectCode());
        map.put("qcProjectName", qcPlan.getQcProjectName());
        map.put("qcMethod", qcPlan.getQcMethod());
        map.put("impScope", qcPlan.getImpScope());
        map.put("judAccord", qcPlan.getJudAccord());
        map.put("judStandard", qcPlan.getJudStandard());
        map.put("qcOperate", qcPlan.getQcOperate());
        map.put("takeSample", qcPlan.getSampling());
        PersonManage p = qcPlan.getStocktakingPerson();
        if (p != null) {
            PersonManage personDb= fabosJsonDao.getById(PersonManage.class, p.getId());
            map.put("executePerson", personDb == null ? "" : personDb);
        }
        return map;
    }

    /**
     * 返回一个空的map集合
     * <AUTHOR>
     * @date 2025/3/26 14:51
     * @return
     */
    private Map<String, Object> emptyMap() {
        HashMap<String, Object> map = new HashMap<>();
        map.put("qcProjectCode", "");
        map.put("qcProjectName", "");
        map.put("qcMethod", "");
        map.put("impScope", "");
        map.put("judAccord", "");
        map.put("judStandard", "");
        map.put("qcOperate", "");
        map.put("executePerson", null);
        map.put("takeSample", "");
        return map;
    }
}
