package cec.jiutian.bc.keepSample.enumeration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 现状
 * <AUTHOR>
 * @date 2025/2/27 17:43
 */
public class SampleReturnCurrentStatusEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum value : Enum.values()) {
            list.add(new VLModel(value.name(), value.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        //待取样
        TO_BE_PUBLISHED("待发布"),
        PUBLISHED("已发布"),
        STOCK_OUT("已出库"),
        COMPLETE("完成")
        ;

        private final String value;

    }
}

