package cec.jiutian.bc.process.port.client;

import cec.jiutian.api.remoteCallLog.dto.RemoteCallResult;
import cec.jiutian.core.frame.constant.FabosJsonRestPath;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

/**
 * <AUTHOR>
 * @description: cec.jiutian.bc.materialInspect.remote.feign.MaterialInspectFeign
 */
@Component
@FeignClient(name = "xlx-qms") // 对应目标服务的spring.application.name
public interface SampleFeignClient {

    @PostMapping("/fabos-qms-app"+ FabosJsonRestPath.FABOS_REMOTE_API+"/getSamplingTask")
    RemoteCallResult<Map<String,Object>> getSamplingTask(@RequestBody Map<String, Object> params);


    @PostMapping("/fabos-qms-app"+ FabosJsonRestPath.FABOS_REMOTE_API+"/createOtherSamplingTask")
    void createOtherSamplingTask(@RequestBody Map<String, Object> params);

    /**
     * 推送检测任务检测结果到QMS
     *
     * @param params 推送参数
     * <AUTHOR>
     * @date 2025/3/20 14:06
     */
    @PostMapping("/fabos-qms-app" + FabosJsonRestPath.FABOS_REMOTE_API + "/receiveLIMSInspectResult")
    void receiveLIMSInspectResult(@RequestBody Map<String, Object> params);
    //同步收样数据  如果收样不合格需要在异常处置流程同步  合格就在收样同步
    @PostMapping("/fabos-qms-app" + FabosJsonRestPath.FABOS_REMOTE_API + "/receiveSampleResult")
    public void receiveSampleResult(@RequestBody Map<String, Object> params);

}
