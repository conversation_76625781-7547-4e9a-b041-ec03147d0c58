package cec.jiutian.bc.instrumentManagement.domain.instrumentInventory.handler;

import cec.jiutian.bc.instrumentManagement.domain.instrumentInventory.model.LimsInstrumentInventoryOpening;
import cec.jiutian.bc.mto.ShelfMTO;
import cec.jiutian.bc.mto.WarehouseBlockMTO;
import cec.jiutian.view.DependFiled;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class LimsInsOBlockCleanDynamicHandler implements DependFiled.DynamicHandler<LimsInstrumentInventoryOpening> {

    @Override
    public Map<String, Object> handle(LimsInstrumentInventoryOpening model) {
        Map<String, Object> map = new HashMap<>();
        if (model.getWarehouseMTO() == null || model.getWarehouseMTO().getId() == null) {
            map.put("warehouseName", "");
            map.put("warehouseId", "");
        }
        map.put("warehouseBlockMTO", new WarehouseBlockMTO());
        map.put("blockName", "");
        map.put("blockId", "");
        map.put("shelf", new ShelfMTO());
        map.put("shelfName", "");
        map.put("shelfId", "");
        return map;
    }

}
