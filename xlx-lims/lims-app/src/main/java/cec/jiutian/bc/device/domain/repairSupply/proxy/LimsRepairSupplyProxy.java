package cec.jiutian.bc.device.domain.repairSupply.proxy;

import cec.jiutian.bc.device.domain.repairSupply.model.LimsRepairSupply;
import cec.jiutian.bc.enums.InventoryTypeEnum;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.view.fun.DataProxy;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/3/31
 * @description
 */
@Component
public class LimsRepairSupplyProxy implements DataProxy<LimsRepairSupply> {

    @Override
    public void beforeAdd(LimsRepairSupply repairSupply) {
        //来源固定只有仓库
        repairSupply.setInventoryType(InventoryTypeEnum.Enum.warehouse.name());
        repairSupply.setCurrentState(OrderCurrentStateEnum.Enum.EDIT.name());
    }
}
