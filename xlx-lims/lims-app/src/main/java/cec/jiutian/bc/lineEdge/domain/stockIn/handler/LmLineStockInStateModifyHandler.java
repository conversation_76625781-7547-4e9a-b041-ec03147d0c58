package cec.jiutian.bc.lineEdge.domain.stockIn.handler;

import cec.jiutian.bc.lineEdge.domain.inventory.model.LineInventory;
import cec.jiutian.bc.lineEdge.domain.inventory.model.LineInventoryDetail;
import cec.jiutian.bc.lineEdge.domain.inventory.service.LineInventoryService;
import cec.jiutian.bc.lineEdge.domain.stockIn.model.LmLineStockIn;
import cec.jiutian.bc.lineEdge.enumeration.LineStockInEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Component
public class LmLineStockInStateModifyHandler implements OperationHandler<LmLineStockIn, Void> {
    @Resource
    private FabosJsonDao fabosJsonDao;
    @Resource
    private LineInventoryService lineInventoryService;

    @Override
    @Transactional
    public String exec(List<LmLineStockIn> data, Void modelObject, String[] param) {
        // 开立 --> 完成
        data.forEach(lmLineStockIn -> {
            lmLineStockIn.setCurrentState(LineStockInEnum.Enum.COMPLETE.name());
            // 创建库存台账数据
            this.addLineInventory(lmLineStockIn);
            // 更新状态
            fabosJsonDao.updateAndFlush(lmLineStockIn);
        });
        return "success";
    }

    /**
     * 创建线边库库存台账
     * <AUTHOR>
     * @date 2025/3/10 18:14
     * @param lmLineStockIn
     */
    public void addLineInventory(LmLineStockIn lmLineStockIn){
        LineInventory lineInventory = new LineInventory();
        // 设置参数: 物料编码、物料名称、单位
        lineInventory.setMaterialCode(lmLineStockIn.getMaterialCode());
        lineInventory.setMaterialName(lmLineStockIn.getLimsMaterial().getMaterialName());
        lineInventory.setUnit(lmLineStockIn.getUnit());
        lineInventory.setModel(lmLineStockIn.getModel());
        lineInventory.setMaterialType(lmLineStockIn.getMaterialType());
        // 库存数量、仓库ID、仓库名称、批次号 存在台账明细中
        LineInventoryDetail ldetail = new LineInventoryDetail();
        ldetail.setQuantity(lmLineStockIn.getQuantity());
        ldetail.setBatchNumber(lmLineStockIn.getBatchNumber());
        ldetail.setWarehouseId(lmLineStockIn.getLimsWarehouse().getId());
        ldetail.setWarehouseName(lmLineStockIn.getLimsWarehouse().getName());
        ldetail.setMaterialCode(lmLineStockIn.getMaterialCode());
        ldetail.setMaterialName(lmLineStockIn.getLimsMaterial().getMaterialName());
        ldetail.setBatchNumber(lmLineStockIn.getBatchNumber());
        lineInventory.setLineInventoryDetails(List.of(ldetail));
        lineInventoryService.addLineInventory(lineInventory);
    }
}
