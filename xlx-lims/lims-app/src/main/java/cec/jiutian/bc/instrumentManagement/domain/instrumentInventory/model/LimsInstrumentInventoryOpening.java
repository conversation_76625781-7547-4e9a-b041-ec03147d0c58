package cec.jiutian.bc.instrumentManagement.domain.instrumentInventory.model;

import cec.jiutian.bc.base.domain.shelves.model.LimsShelves;
import cec.jiutian.bc.base.domain.warehouse.model.LimsWarehouse;
import cec.jiutian.bc.enums.InventoryMaterialTypeEnum;
import cec.jiutian.bc.instrumentManagement.domain.instrumentInventory.handler.LimsInsOBlockCleanDynamicHandler;
import cec.jiutian.bc.instrumentManagement.domain.instrumentInventory.handler.LimsInsOShelfCleanDynamicHandler;
import cec.jiutian.bc.instrumentManagement.domain.instrumentInventory.handler.LimsInsShelfCleanDynamicHandler;
import cec.jiutian.bc.instrumentManagement.domain.instrumentInventory.proxy.LimsInstrumentInventoryOpeningDataProxy;
import cec.jiutian.bc.instrumentManagement.enums.InstrumentStatusEnum;
import cec.jiutian.bc.mto.*;
import cec.jiutian.bc.sparePartsManagement.domain.inventoryLedger.enums.InventoryLedgerStatusEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;

import java.util.Date;


/**
 * 仪表仪器台账  copy自eam的cec.jiutian.bc.instrumentManagement.domain.instrumentInventory.model.InstrumentInventoryOpening;  调整了仓库等数据源
 */
@Getter
@Setter
@Entity
@Table(name = "eam_inventory_ledger")
@FabosJson(
        name = "仪表仪器期初台账",
        filter = @Filter("materialType = 'instrument'"),
        orderBy = "stockInDate desc",
        dataProxy = LimsInstrumentInventoryOpeningDataProxy.class,
        power = @Power(export = true)
)
public class LimsInstrumentInventoryOpening extends MetaModel {

    @Transient
    @FabosJsonField(
            views = @View(title = "选择物料台账", show = false, column = "name"),
            edit = @Edit(title = "选择物料台账",
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    filter = @Filter(value = "specificationCode ='0111'"))
    )
    private SpecificationManage specificationManage;

    @FabosJsonField(
            views = @View(title = "仪器编号"),
            edit = @Edit(title = "仪器编号",
                    notNull = true,
                    readonly = @Readonly(add = true, edit = true),
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "specificationManage", beFilledBy = "code"))

    )
    private String materialCode;

    @FabosJsonField(
            views = @View(title = "名称"),
            edit = @Edit(title = "名称",
                    notNull = true,
                    readonly = @Readonly(add = true, edit = true),
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "specificationManage", beFilledBy = "name"))
    )
    private String materialName;

    @FabosJsonField(
            views = @View(title = "件次号"),
            edit = @Edit(title = "件次号",
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    private String lotSerialId;

    @FabosJsonField(
            views = @View(title = "原炉/原厂批号"),
            edit = @Edit(title = "原炉/原厂批号",
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    private String supplierLotSerialId;

    @FabosJsonField(
            views = @View(title = "规格/型号"),
            edit = @Edit(title = "规格/型号", notNull = true)
            //期初导入暂时不禁止编辑。期初的可能关系不是现在维护的物料关系  只是回显
            ,dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "specificationManage", beFilledBy = "type"))
    )
    private String materialSpecification;

    @FabosJsonField(
            views = @View(title = "单位"),
            edit = @Edit(title = "单位",
                    notNull = true
            )
            //期初导入暂时不禁止编辑。期初的可能关系不是现在维护的物料关系  只是回显
            ,dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "specificationManage", beFilledBy = "accountingUnit"))
    )
    private String accountingUnit;

    @FabosJsonField(
            views = @View(title = "批次总数量"),
            edit = @Edit(title = "批次总数量", show = false)
    )
    private Double accountingUnitQuantity;

    @FabosJsonField(
            views = @View(title = "可用数量"),
            edit = @Edit(title = "可用数量", show = false)
    )
    private Double availableQuantity;

    @FabosJsonField(
            views = @View(title = "物料类别"),
            edit = @Edit(title = "物料类别",
                    readonly = @Readonly,
                    defaultVal = "instrument",
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(
                            fetchHandler = InventoryMaterialTypeEnum.class
                    )
                    //该模型通过自定义按钮构建的页面defaultVal配置不会生效。因此在dataProxy处理
                    ,show = false
            ),
            dynamicField = @DynamicField(passive = true)
    )
    private String materialType;

    @FabosJsonField(
            views = @View(title = "库存批次号"),
            edit = @Edit(title = "库存批次号",
                    notNull = true,
                    search = @Search(vague = true),
                    inputType = @InputType(length = 30)
            )
    )
    private String inventoryCode;
//    @Transient
//    @FabosJsonField(
//            views = @View(title = "供应商", show = false, column = "supplierCode"),
//            edit = @Edit(title = "供应商",notNull = true,
//                    type = EditType.REFERENCE_TABLE,
//                    allowAddMultipleRows = false,
//                    referenceTableType = @ReferenceTableType(label = "supplierCode"),
//                    filter = @Filter(value = "validateFlag = 'Y'")
//            )
//    )
//    private SupplierMTO supplier;
    @FabosJsonField(
            views = @View(title = "供应商id", show = false),
            edit = @Edit(title = "供应商id", show = false)
            ,dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "supplier", beFilledBy = "id"))

    )
    private String supplierId;

    @FabosJsonField(
            views = @View(title = "供应商"),
            edit = @Edit(title = "供应商",
                    search = @Search(vague = true)
            )
            ,dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "supplier", beFilledBy = "supplierName"))
    )
    private String supplierName;

    @FabosJsonField(
            views = @View(title = "供应商编码"),
            edit = @Edit(title = "供应商编码")
            ,dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "supplier", beFilledBy = "supplierCode"))

    )
    private String supplierCode;

    @Transient
    @FabosJsonField(
            views = @View(title = "选择仓库", column = "name", show = false),
            edit = @Edit(title = "选择仓库",
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name")
            )
    )
    private LimsWarehouse warehouseMTO;

    @FabosJsonField(
            views = @View(title = "仓库名称"),
            edit = @Edit(title = "仓库名称",
                    notNull = true,
                    readonly = @Readonly,
                    inputType = @InputType(length = 20)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "warehouseMTO",
                    beFilledBy = "name"), passive = true)
    )
    private String warehouseName;

    @FabosJsonField(
            views = @View(title = "仓库ID", show = false),
            edit = @Edit(title = "仓库ID", show = false,
                    inputType = @InputType(type = "number")
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "warehouseMTO",
                    beFilledBy = "id"))
    )
    private String limsWarehouseId;

//    @Transient
//    @FabosJsonField(
//            views = @View(title = "选择库区", column = "blockName", show = false),
//            edit = @Edit(title = "选择库区",
//                    type = EditType.REFERENCE_TABLE,
//                    queryCondition = "{\"warehouseId\":\"${warehouseId}\"}",
//                    referenceTableType = @ReferenceTableType(id = "id", label = "blockName"),
//                    dependFieldDisplay = @DependFieldDisplay(enableOrDisable = "warehouseName == null")
//            ),
//            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "warehouseMTO",
//                    dynamicHandler = LimsInsBlockCleanDynamicHandler.class))
//    )
//    private WarehouseBlockMTO warehouseBlockMTO;


//    @FabosJsonField(
//            views = @View(title = "库区名称"),
//            edit = @Edit(title = "库区名称",
//                    readonly = @Readonly,
//                    inputType = @InputType(length = 20)
//            ),
//            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "warehouseBlockMTO",
//                    beFilledBy = "blockName"), passive = true)
//    )
//    private String blockName;
//
//    @FabosJsonField(
//            views = @View(title = "库区ID", show = false),
//            edit = @Edit(title = "库区ID", show = false,
//                    inputType = @InputType(type = "number"),
//                    numberType = @NumberType(precision = 0)
//            ),
//            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "warehouseBlockMTO",
//                    beFilledBy = "id"))
//    )
//    private Long blockId;

    @Transient
    @FabosJsonField(
            views = @View(title = "选择货位", column = "name", show = false),
            edit = @Edit(title = "选择货位",
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    queryCondition = "{\"limsWarehouse.id\":\"${warehouseId}\"}",
                    dependFieldDisplay = @DependFieldDisplay(enableOrDisable = "warehouseName == null")
            ),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "warehouseBlockMTO",
                    dynamicHandler = LimsInsShelfCleanDynamicHandler.class))
    )
    private LimsShelves shelf;

    @FabosJsonField(
            views = @View(title = "货位名称"),
            edit = @Edit(title = "货位名称",
                    readonly = @Readonly,
                    inputType = @InputType(length = 20)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "shelf",
                    beFilledBy = "name"), passive = true)
    )
    private String shelfName;

    @FabosJsonField(
            views = @View(title = "货位ID", show = false),
            edit = @Edit(title = "货位ID", show = false,
                    inputType = @InputType(type = "number")
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "shelf",
                    beFilledBy = "id"))
    )
    private String limsShelfId;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",
                    defaultVal = "normal",
                    show = false,
                    type = EditType.CHOICE,
                    readonly = @Readonly,
                    choiceType = @ChoiceType(fetchHandler = InventoryLedgerStatusEnum.class)
            ),
            dynamicField = @DynamicField(passive = true)
    )
    private String businessState;

    @FabosJsonField(
            views = @View(title = "启用日期"),
            edit = @Edit(title = "启用日期",
                    notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private Date enableDate;

    @FabosJsonField(
            views = @View(title = "是否必检"),
            edit = @Edit(title = "是否必检",
                    notNull = true,
                    type = EditType.BOOLEAN,
                    defaultVal = "false"
            )
    )
    @Column(nullable = false)
    private Boolean inspection;

    @Transient
    @FabosJsonField(
            views = @View(title = "选择负责人", show = false, column = "name"),
            edit = @Edit(title = "选择负责人",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType()
            )
    )
    private UserMTO reporterMTO;

    @FabosJsonField(
            views = @View(title = "负责人"),
            edit = @Edit(title = "负责人",
                    search = @Search(vague = true),
                    notNull = true,
                    readonly = @Readonly(add = true,edit = true),
                    inputType = @InputType(length = 30)
            ),
            dynamicField =  @DynamicField(linkedFiled = @LinkedFiled(changeBy = "reporterMTO",
                    beFilledBy = "name"))
    )
    @Column(nullable = false, length = 30)
    private String keeper;

    @FabosJsonField(
            views = @View(title = "负责人id",show = false),
            edit = @Edit(title = "负责人id",
                    show = false,
                    notNull = true,
                    inputType = @InputType(length = 30)
            ),
            dynamicField =  @DynamicField(linkedFiled = @LinkedFiled(changeBy = "reporterMTO",
                    beFilledBy = "id"))
    )
    private String keeperId;

    @FabosJsonField(
            views = @View(title = "入库日期"),
            edit = @Edit(title = "入库日期",
                    show = false,
                    search = @Search(vague = true),
                    dateType = @DateType(type = DateType.Type.DATE)
            )
    )
    @CreationTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private Date stockInDate;

    //有效期
    @FabosJsonField(
            views = @View(title = "有效期"),
            edit = @Edit(title = "有效期",
                    notNull = true,
                    search = @Search(vague = true),
                    dateType = @DateType(type = DateType.Type.DATE)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private Date expireDate;

}
