package cec.jiutian.bc.process.domain.reagentConfigRequest.handler;

import cec.jiutian.bc.base.domain.reagentConfigRule.model.ReagentConfig;
import cec.jiutian.bc.process.domain.reagentConfigRequest.model.ReagentConfigRequest;
import cec.jiutian.bc.process.domain.reagentConfigRequest.model.ReagentConfigRequestDetail;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.data.jpa.JpaCrud;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：2024/11/14 18:13
 * @description：
 */
@Component
public class ReagentConfigRequestDetailDynamicHandler implements DependFiled.DynamicHandler<ReagentConfigRequest> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public Map<String, Object> handle(ReagentConfigRequest reagentConfigRequest) {
        Map<String, Object> result = new HashMap<>();
        List<ReagentConfigRequestDetail> list = new ArrayList<>();
        ReagentConfig reagentConfig = reagentConfigRequest.getReagentConfig();
        if (reagentConfig == null) {
            return emptyMap(result, list);
        }
        ReagentConfig config = fabosJsonDao.getById(ReagentConfig.class, reagentConfig.getId());
        if (config != null) {
            result.put("reagentUsage", config.getReagentUsage());
            result.put("reagentSpec", config.getReagentSpec());
            if (CollectionUtils.isNotEmpty(config.getReagentConfigDetails())) {
                config.getReagentConfigDetails().forEach(d -> {
                    ReagentConfigRequestDetail detail = new ReagentConfigRequestDetail();
                    detail.setFormulaMaterialCode(d.getFormulaMaterialCode());
                    detail.setFormulaName(d.getFormulaName());
                    detail.setStandardUsage(d.getStandardUsage());
                    detail.setUnit(d.getUnit());
                    list.add(detail);
                });
                result.put("reagentConfigRequestDetails", list);
            }
        }
        return result;
    }

    private Map<String, Object> emptyMap(Map<String, Object> map, List<ReagentConfigRequestDetail> list) {
        list.clear();
        map.put("reagentConfigRequestDetails", list);
        map.put("reagentUsage", "");
        map.put("reagentSpec", "");
        return map;
    }
}
