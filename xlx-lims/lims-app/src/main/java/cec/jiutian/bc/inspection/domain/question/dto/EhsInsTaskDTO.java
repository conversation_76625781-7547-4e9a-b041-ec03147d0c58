package cec.jiutian.bc.inspection.domain.question.dto;

import cec.jiutian.bc.urm.domain.dictionary.handler.DictChoiceFetchHandler;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.QueryModel;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.FabosJsonI18n;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.Search;
import jakarta.persistence.Id;
import lombok.Data;

/**
 * <AUTHOR>
 * @description:
 */
@FabosJson(name = "巡检任务-用于创建问题清单"
)
@FabosJsonI18n
@Data
@QueryModel(hql = "select " +
        "new map (d.id as id,t.name as name,t.generalCode as generalCode,t.description as description,d.ehsInsItemName as ehsInsItemName," +
        " e.name as ehsInsSchemeName,d.content as content,d.type as type,"+
        " d.insResult as insResult)"+
        " from EhsInsTask t join t.ehsInsTaskDetails d join t.ehsInsScheme e ${customWhere} and d.isAbnormal='DANGER' and d.isAdd is null and t.isSubmit='2'"
        )
public class EhsInsTaskDTO {
    //明细id
    @FabosJsonField(
            views = @View(title = "id",show = false),
            edit = @Edit(title = "id"))
    @Id
    private String id;
    @FabosJsonField(
            views = @View(title = "编码"),
            edit = @Edit(title = "编码", search = @Search(vague = true),readonly = @Readonly)
    )
    private String generalCode;
    // 任务名称
    @FabosJsonField(
            views = @View(title = "任务名称"),
            edit = @Edit(title = "任务名称", search = @Search(vague = true),readonly = @Readonly)
    )
    private String name;

    // 任务描述
    @FabosJsonField(
            views = @View(title = "任务描述", toolTip = true),
            edit = @Edit(title = "任务描述",readonly = @Readonly)
    )
    private String description;

    @FabosJsonField(
            views = @View(title = "巡检方案"),
            edit = @Edit(title = "巡检方案", readonly = @Readonly)
    )
    private String ehsInsSchemeName;
    @FabosJsonField(
            views = @View(title = "巡检项目"),
            edit = @Edit(title = "巡检项目",readonly = @Readonly)
    )
    private String ehsInsItemName;

    @FabosJsonField(
            views = @View(title = "巡检内容", toolTip = true),
            edit = @Edit(title = "巡检内容", search = @Search(vague = true), readonly = @Readonly)
    )
    private String content;

    @FabosJsonField(
            views = @View(title = "巡检类型"),
            edit = @Edit(title = "巡检类型", type = EditType.CHOICE, readonly = @Readonly,
                    choiceType = @ChoiceType(fetchHandler = DictChoiceFetchHandler.class,
                            fetchHandlerParams = "LM_INS_ITEM_TYPE"), search = @Search(vague = true))
    )
    private String type;

    @FabosJsonField(
            views = @View(title = "巡检结果", toolTip = true),
            edit = @Edit(title = "巡检结果",readonly = @Readonly)
    )
    private String insResult;

}
