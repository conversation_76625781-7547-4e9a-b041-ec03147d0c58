package cec.jiutian.bc.keepSample.domain.stocktakingOrder.handler;

import cec.jiutian.bc.keepSample.domain.stocktakingOrder.model.StocktakingOrder;
import cec.jiutian.bc.keepSample.domain.stocktakingOrder.service.StocktakingOrderService;
import cec.jiutian.common.util.StringUtils;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：2024/11/14 18:13
 * @description：
 */
@Component
public class StocktakingOrderDetailDynamicHandler implements DependFiled.DynamicHandler<StocktakingOrder> {
    @Resource
    private StocktakingOrderService stocktakingOrderService;

    @Override
    public Map<String, Object> handle(StocktakingOrder stocktakingOrder) {
        Map<String, Object> result = new HashMap<>();
        String stocktakingRange = "";
        if(stocktakingOrder.getKsStocktakingPlan()!=null){
            stocktakingRange = stocktakingOrder.getKsStocktakingPlan().getStocktakingRange();
        }
        if(StringUtils.isBlank(stocktakingRange)){
            stocktakingRange = stocktakingOrder.getStocktakingRange();
        }
        if(StringUtils.isNotBlank(stocktakingRange)){
            result.put("stocktakingOrderDetails", stocktakingOrderService.getStocktakingOrderDetailList(stocktakingRange));
        }else {
            result.put("stocktakingOrderDetails", new ArrayList<>());
        }
        return result;
    }
}
