package cec.jiutian.bc.process.domain.inspectionTask.handler;

import cec.jiutian.bc.modeler.enumration.InspectionValueTypeEnum;
import cec.jiutian.bc.process.domain.inspectionTask.model.InspectionManyResult;
import cec.jiutian.bc.process.domain.inspectionTask.model.InspectionTaskDetail;
import cec.jiutian.bc.process.domain.inspectionTask.model.InspectionTaskDetailQuota;
import cec.jiutian.bc.process.domain.inspectionTask.model.MyInspectionTask;
import cec.jiutian.bc.process.domain.inspectionTask.model.ResultInput;
import cec.jiutian.bc.process.enumeration.InspectResultEnum;
import cec.jiutian.bc.process.enumeration.PrepareStatusEnum;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
public class ResultInputHandler implements OperationHandler<MyInspectionTask, ResultInput> {
    @Resource
    private FabosJsonDao fabosJsonDao;
    // 值类型-文本
    private static final String TEXT = InspectionValueTypeEnum.Enum.text.name();

    @Override
    @Transactional
    public String exec(List<MyInspectionTask> data, ResultInput modelObject, String[] param) {
        MyInspectionTask mst = this.getDataByGeneralCode(modelObject.getGeneralCode());
        if (modelObject.getTestTimes() == null || modelObject.getTestTimes() == 1) {
            //单次测量
            this.oneResult(mst, modelObject);
        } else {
            //多次测量
            this.manyResult(mst,modelObject);
        }
        // 设置结果提交状态
        mst.setResultInStatus("1");
        // 设置准备状态为NOT_PASS：录入结果之后，这轮不能再进行录入
        mst.setPrepareStatus(PrepareStatusEnum.Enum.NOT_PASS.name());
        mst.setOriginalTestFile(modelObject.getOriginalTestFile());
        // 更新
        fabosJsonDao.update(mst);
        return "success";
    }

    /**
     * 单次测量
     * @param mst
     * @param modelObject
     */
    private void oneResult(MyInspectionTask mst,ResultInput modelObject){
        List<InspectionTaskDetailQuota> resultQuotaList = modelObject.getInspectionTaskDetailQuotas();
        // 数据完整性校验
        this.checkParam(resultQuotaList);
        // 以<id, object>的形式组装为map
        Map<String, InspectionTaskDetailQuota> resultQuotaMap = resultQuotaList.stream()
                .collect(Collectors.toMap(
                        InspectionTaskDetailQuota::getId,
                        quota -> quota
                ));
        // 将结果装载到测试任务详情的检测指标中, 然后更新
        List<InspectionTaskDetail> detailList = mst.getInspectionTaskDetails();
        detailList.forEach(d -> {
            List<InspectionTaskDetailQuota> quotaList = d.getInspectionTaskDetailQuotas();
            quotaList.forEach(q -> {
                // 如果匹配上了数据, 则进行检测值和检测结果的装载
                if (resultQuotaMap.containsKey(q.getId())) {
                    InspectionTaskDetailQuota resultQuota = resultQuotaMap.get(q.getId());
                    q.setInspectResult(resultQuota.getInspectResult());
                    q.setInspectValue(resultQuota.getInspectValue());
                }
            });
        });
        // 设置检测任务子单检测结果
        this.setDetailInsResult(detailList);
    }

    /**
     * 设置检测任务子单的检测结果： 合格、不合格
     * <AUTHOR>
     * @date 2025/3/20 11:18
     * @param detailList
     */
    private void setDetailInsResult(List<InspectionTaskDetail> detailList) {
        for (InspectionTaskDetail detail : detailList) {
            boolean isQualified = true;
            List<InspectionTaskDetailQuota> quotaList = detail.getInspectionTaskDetailQuotas();
            for (InspectionTaskDetailQuota detailQuota : quotaList) {
                if (detailQuota.getInspectResult().equals(InspectResultEnum.Enum.UNQUALIFIED.name())) {
                    isQualified = false;
                    break;
                }
            }
            // 若都合格，则设置该子单的检测结果为合格; 否则为不合格
            if (isQualified) {
                detail.setDetailInsResult(InspectResultEnum.Enum.QUALIFIED.name());
            } else {
                detail.setDetailInsResult(InspectResultEnum.Enum.UNQUALIFIED.name());
            }
        }
    }


    /**
     * 根据generalCode获取数据
     * <AUTHOR>
     * @date 2025/3/19 22:01
     * @param generalCode
     * @return
     */
    public MyInspectionTask getDataByGeneralCode(String generalCode) {
        MyInspectionTask query = new MyInspectionTask();
        query.setGeneralCode(generalCode);
        return fabosJsonDao.selectOne(query);
    }

    /**
     * 显示每一个测试任务详情的检测指标
     * @param data
     * @param fabosJsonForm
     * @param param
     * @return
     */
    @Override
    public ResultInput fabosJsonFormValue(List<MyInspectionTask> data, ResultInput fabosJsonForm, String[] param) {
        // 主单数据
        MyInspectionTask mst = data.get(0);
        // 转载主单数据
        BeanUtil.copyProperties(mst, fabosJsonForm);
        // 子单列表
        List<InspectionTaskDetail> detailList = mst.getInspectionTaskDetails();
        // 遍历子单 装载子单数据的检测指标数据
        ArrayList<InspectionTaskDetailQuota> insQuotaList = new ArrayList<>();
        detailList.forEach(d -> {
            insQuotaList.addAll(d.getInspectionTaskDetailQuotas());
        });
        if(CollectionUtils.isNotEmpty(insQuotaList)){
            InspectionManyResult inspectionManyResult = new InspectionManyResult();
            //inspectionManyResult.setInspectValue(0.0);
            List<InspectionManyResult> resultLists = new ArrayList<>();
            for (int i = 0; i < mst.getTestTimes(); i++) {
                resultLists.add(inspectionManyResult);
            }
            insQuotaList.forEach(d->{
                d.setInspectionManyResults(resultLists);
            });
        }
        fabosJsonForm.setSampleTaskNo(mst.getSampleTaskNo());
        fabosJsonForm.setInspectionTaskDetailQuotas(insQuotaList);
        return fabosJsonForm;
    }

    /**
     * 校验录入的数据： 数据不完整则抛出异常, 不允许提交
     * <AUTHOR>
     * @date 2025/3/18 16:14
     */
    public void checkParam(List<InspectionTaskDetailQuota> resultQuotaList) {
        resultQuotaList.forEach(q -> {
            if (q.getInspectResult() == null || q.getInspectValue() == null) {
                throw new FabosJsonApiErrorTip("录入数据不完整, 无法提交！");
            }
            // 校验数据合法性
            if (!TEXT.equals(q.getInspectionValueType())) {
                try {
                    Double.parseDouble(q.getInspectValue());
                } catch (Exception e) {
                    throw new FabosJsonApiErrorTip("检测值非数值类型，请确认！");
                }
            }
        });
    }

    /**
     * 多次测量
     * @param mst
     * @param modelObject
     */
    private void manyResult(MyInspectionTask mst,ResultInput modelObject){
        //测试次数多次
        modelObject.getInspectionTaskDetailQuotas().forEach(q -> {
            q.getInspectionManyResults().forEach(r -> {
                if(r.getInspectValue()==null){
                    throw new FabosJsonApiErrorTip("录入数据不完整, 请通过编辑按钮填写多次测试的结果！");
                }
            });
        });
        List<InspectionTaskDetailQuota> resultQuotaList = modelObject.getInspectionTaskDetailQuotas();
        // 以<id, object>的形式组装为map
        Map<String, InspectionTaskDetailQuota> resultQuotaMap = resultQuotaList.stream()
                .collect(Collectors.toMap(
                        InspectionTaskDetailQuota::getId,
                        quota -> quota
                ));
        // 将结果装载到测试任务详情的检测指标中, 然后更新
        List<InspectionTaskDetail> detailList = mst.getInspectionTaskDetails();
        detailList.forEach(d -> {
            List<InspectionTaskDetailQuota> quotaList = d.getInspectionTaskDetailQuotas();
            quotaList.forEach(q -> {
                // 如果匹配上了数据, 则进行检测值和检测结果的装载
                if (resultQuotaMap.containsKey(q.getId())) {
                    InspectionTaskDetailQuota resultQuota = resultQuotaMap.get(q.getId());
                    List<InspectionManyResult> manyResults = Optional.ofNullable(q.getInspectionManyResults())
                            .orElseGet(ArrayList::new);
                    manyResults.clear();
                    manyResults.addAll(resultQuota.getInspectionManyResults());
                    q.setInspectionManyResults(manyResults);
                }
            });
        });
    }
}
