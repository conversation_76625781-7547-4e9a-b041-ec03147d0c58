package cec.jiutian.bc.qualityControl.mto;

import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR> @date 2025/3/27
 * @description
 */
@Entity
@Table(name = "mf_prcs_oprtn")
@Getter
@FabosJson(
        name = "工序",
        orderBy = "createTime desc",
        power = @Power(add = false, edit = false, delete = false, print = false, importable = false)
)
@Setter
public class EhsProductProcessMTO {

    @Id
    @FabosJsonField(
            edit = @Edit(title = "", show = false)
    )
    @Column(name = "id", columnDefinition = "int8")
    private Long id;

    @FabosJsonField(
            views = @View(title = "工序编码"),
            edit = @Edit(title = "工序编码",
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "prcs_oprtn_cd", length = 40)
    private String code;

    @FabosJsonField(
            views = @View(title = "工序名称"),
            edit = @Edit(title = "工序名称",
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "prcs_oprtn_nm", length = 40)
    private String name;

    @Column(name = "crte_tm")
    private Date createTime;
}
