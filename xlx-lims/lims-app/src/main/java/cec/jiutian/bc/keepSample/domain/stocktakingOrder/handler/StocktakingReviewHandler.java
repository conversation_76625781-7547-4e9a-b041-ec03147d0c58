package cec.jiutian.bc.keepSample.domain.stocktakingOrder.handler;

import cec.jiutian.bc.base.domain.carrier.model.LimsCarrier;
import cec.jiutian.bc.base.domain.shelves.model.LimsShelves;
import cec.jiutian.bc.base.domain.warehouse.model.LimsWarehouse;
import cec.jiutian.bc.keepSample.domain.inventory.model.KsInventory;
import cec.jiutian.bc.keepSample.domain.stockin.model.LmKsStockIn;
import cec.jiutian.bc.keepSample.domain.stockout.model.LmKsStockOut;
import cec.jiutian.bc.keepSample.domain.stocktakingOrder.model.StockInResultReview;
import cec.jiutian.bc.keepSample.domain.stocktakingOrder.model.StocktakingOrderDetailRun;
import cec.jiutian.bc.keepSample.domain.stocktakingOrder.model.StocktakingReview;
import cec.jiutian.bc.keepSample.enumeration.LmKsStockInTypeEnum;
import cec.jiutian.bc.keepSample.enumeration.StockReviewResultEnum;
import cec.jiutian.bc.keepSample.enumeration.StocktakingOrderStateEnum;
import cec.jiutian.bc.keepSample.enumeration.SubsequentOperaEnum;
import cec.jiutian.bc.lineEdge.enumeration.LineStockInEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * 任务审核结果提交
 * <AUTHOR>
 * @date 2025/3/23 19:48
 */
@Component
public class StocktakingReviewHandler implements OperationHandler<StocktakingReview, StockInResultReview> {


    @Resource
    private FabosJsonDao fabosJsonDao;


    @Override
    public String exec(List<StocktakingReview> data, StockInResultReview modelObject, String[] param) {
        String generalCode = modelObject.getGeneralCode();
        StocktakingReview query = new StocktakingReview();
        query.setGeneralCode(generalCode);
        StocktakingReview stocktakingReview = fabosJsonDao.selectOne(query);
        stocktakingReview.setReviewResult(modelObject.getReviewResult());
        // 如果审核通过, 需要进行盘盈入库或者盘亏出库
        if (StockReviewResultEnum.Enum.PASS.name().equals(modelObject.getReviewResult())) {
            this.createStockInOrOut(stocktakingReview);
            // 设置单据状态为已完成
            stocktakingReview.setCurrentState(StocktakingOrderStateEnum.Enum.FINISH.name());
        } else {
            // 如果审核不通过, 且后续操作为重盘
            if (SubsequentOperaEnum.Enum.RE_INVENTORY.name().equals(modelObject.getSubsequentOpera())) {
                // 单号状态修改为已确认
                stocktakingReview.setCurrentState(StocktakingOrderStateEnum.Enum.CONFIRMED.name());
                stocktakingReview.setSubsequentOpera(SubsequentOperaEnum.Enum.RE_INVENTORY.name());
            } else {
                // 后续操作为关闭
                stocktakingReview.setCurrentState(StocktakingOrderStateEnum.Enum.CLOSED.name());
                stocktakingReview.setSubsequentOpera(SubsequentOperaEnum.Enum.CLOSE.name());
            }
        }
        fabosJsonDao.update(stocktakingReview);
        return "success";
    }

    @Override
    public StockInResultReview fabosJsonFormValue(List<StocktakingReview> data, StockInResultReview fabosJsonForm, String[] param) {
        StocktakingReview stocktakingReview = data.get(0);
        BeanUtil.copyProperties(stocktakingReview, fabosJsonForm);
        return fabosJsonForm;
    }

    /**
     * 通过后创建盘盈入库单或盘亏出库单
     * <AUTHOR>
     * @date 2025/3/23 20:04
     * @param stocktakingReview
     */
    public void createStockInOrOut(StocktakingReview stocktakingReview) {
        List<StocktakingOrderDetailRun> detailRunList = stocktakingReview.getStocktakingOrderDetails();
        // 根据明细中的实盘数量来进行库存调整
        detailRunList.forEach(d -> {
            // 实盘数量 > 库存数量：盘盈入库, 创建入库单, 增加库存台账数量
            if (d.getStocktakingQuantity() > d.getQuantity()) {
                LmKsStockIn lmKsStockIn = new LmKsStockIn();
                // 样品单号
                lmKsStockIn.setSampleTaskNo(d.getSampleTaskNo());
                lmKsStockIn.setMaterialCode(d.getMaterialCode());
                lmKsStockIn.setMaterialName(d.getMaterialName());
                lmKsStockIn.setModel(d.getModel());
                lmKsStockIn.setUnit(d.getUnit());
                lmKsStockIn.setSampleType(d.getSampleType());
                // 只保留小数点后两位
                double quantity = Math.round((d.getStocktakingQuantity() - d.getQuantity()) * 100) / 100.0;
                lmKsStockIn.setQuantity(quantity);
                // 仓库
                LimsWarehouse limsWarehouse = new LimsWarehouse();
                limsWarehouse.setId(d.getWarehouseId());
                lmKsStockIn.setLimsWarehouse(limsWarehouse);
                // 货架
                LimsShelves limsShelves = new LimsShelves();
                limsShelves.setId(d.getShelvesId());
                lmKsStockIn.setLimsShelves(limsShelves);
                // 载具
                LimsCarrier limsCarrier = new LimsCarrier();
                limsCarrier.setId(d.getCarrierId());
                lmKsStockIn.setLimsCarrier(limsCarrier);
                lmKsStockIn.setCurrentState(LineStockInEnum.Enum.COMPLETE.name());
                // 入库类型
                lmKsStockIn.setStockInType(LmKsStockInTypeEnum.Enum.INVENTORY_PROFIT.name());
                // 密封时间、有效截止日期
                KsInventory query = new KsInventory();
                query.setMaterialCode(d.getMaterialCode());
                KsInventory ksInventoryDb = fabosJsonDao.selectOne(query);
                if (ksInventoryDb != null) {
                    lmKsStockIn.setSealDate(ksInventoryDb.getSealDate());
                    lmKsStockIn.setExpDate(ksInventoryDb.getExpDate());
                 }
                fabosJsonDao.mergeAndFlush(lmKsStockIn);
                // 调整台账数量
                ksInventoryDb.setQuantity(d.getStocktakingQuantity());
                fabosJsonDao.update(ksInventoryDb);
            }
            // 实盘数量 < 库存数量：盘亏出库, 创建出库单, 减少库存台账数量
            if (d.getStocktakingQuantity() < d.getQuantity()) {
                LmKsStockOut lmKsStockOut = new LmKsStockOut();
                // 样品单号
                lmKsStockOut.setSampleTaskNo(d.getSampleTaskNo());
                lmKsStockOut.setMaterialCode(d.getMaterialCode());
                lmKsStockOut.setMaterialName(d.getMaterialName());
                lmKsStockOut.setModel(d.getModel());
                lmKsStockOut.setUnit(d.getUnit());
                lmKsStockOut.setSampleType(d.getSampleType());
                double quantity = Math.round((d.getQuantity() - d.getStocktakingQuantity()) * 100) / 100.0;
                lmKsStockOut.setQuantity(quantity);
                // 仓库
                lmKsStockOut.setWarehouseId(d.getWarehouseId());
                // 货架
                lmKsStockOut.setShelvesId(d.getShelvesId());
                // 载具
                lmKsStockOut.setCarrierId(d.getCarrierId());
                lmKsStockOut.setCurrentState(LineStockInEnum.Enum.COMPLETE.name());
                // 出库时间
                lmKsStockOut.setExpDate(new Date());
                fabosJsonDao.mergeAndFlush(lmKsStockOut);
                // 调整台账数量
                KsInventory query = new KsInventory();
                query.setMaterialCode(d.getMaterialCode());
                KsInventory ksInventoryDb = fabosJsonDao.selectOne(query);
                ksInventoryDb.setQuantity(d.getStocktakingQuantity());
                fabosJsonDao.update(ksInventoryDb);
            }
        });
    }
}
