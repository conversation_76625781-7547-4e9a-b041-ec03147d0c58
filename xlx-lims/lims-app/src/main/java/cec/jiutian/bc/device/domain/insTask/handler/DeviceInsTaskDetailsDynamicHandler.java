package cec.jiutian.bc.device.domain.insTask.handler;

import cec.jiutian.bc.device.domain.insStandard.model.DeviceInsStandard;
import cec.jiutian.bc.device.domain.insStandard.model.DeviceInsStandardItem;
import cec.jiutian.bc.device.domain.insTask.model.DeviceInsTask;
import cec.jiutian.bc.device.domain.insTask.model.DeviceInsTaskDetail;
import cec.jiutian.common.util.BeanUtils;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class DeviceInsTaskDetailsDynamicHandler implements DependFiled.DynamicHandler<DeviceInsTask> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public Map<String, Object> handle(DeviceInsTask deviceInsTask) {
        HashMap<String, Object> map = new HashMap<>();
        // 设备点检标准 或者 检测项数据为空 则返回一个空的map
        DeviceInsStandard deviceInsStandard = deviceInsTask.getDeviceInsStandard();
        if (deviceInsStandard == null) {
            map.put("deviceInsTaskDetails", null);
            return map;
        }
        List<DeviceInsTaskDetail> resultList = new ArrayList<>();
        DeviceInsStandard deviceInsStandardDb = fabosJsonDao.getById(DeviceInsStandard.class, deviceInsStandard.getId());
        List<DeviceInsStandardItem> standardItemList = deviceInsStandardDb.getDeviceInsStandardItemList();
        if (standardItemList == null) {  // 防御性判空
            standardItemList = Collections.emptyList();
        }
        for (DeviceInsStandardItem standardItem : standardItemList) {
            DeviceInsTaskDetail deviceInsTaskDetail = new DeviceInsTaskDetail();
            BeanUtils.copyProperties(standardItem, deviceInsTaskDetail);
            deviceInsTaskDetail.setId(null);
            resultList.add(deviceInsTaskDetail);
        }
        map.put("deviceInsTaskDetails", resultList);
        return map;
    }

}
