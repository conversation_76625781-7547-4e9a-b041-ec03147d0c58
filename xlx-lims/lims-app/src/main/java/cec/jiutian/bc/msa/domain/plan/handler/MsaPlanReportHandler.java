package cec.jiutian.bc.msa.domain.plan.handler;

import cec.jiutian.bc.msa.domain.plan.model.MsaPlan;
import cec.jiutian.bc.msa.enumeration.MsaPlanCurrentStatusEnum;
import cec.jiutian.bc.msa.remote.manage.MsaReportManage;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * 待发布 --> 执行中
 * <AUTHOR>
 * @date 2025/3/27 13:50
 */
@Component
@Slf4j
public class MsaPlanReportHandler implements OperationHandler<MsaPlan, Void> {
    @Resource
    private MsaReportManage msaReportManage;

    @Override
    public DownloadableFile fileOperator(List<MsaPlan> selectedData, String[] param) {
        String msaPlanId = selectedData.get(0).getId();
        Workbook workbook = null;
        try {
            workbook = msaReportManage.generate(msaPlanId);
            if(workbook!=null){
                ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                workbook.write(outputStream);
                workbook.close();
                String fileExtension = workbook instanceof HSSFWorkbook ? ".xls" : ".xlsx";
                //return new DownloadableFile(outputStream,msaReportManage.getFileName(msaPlanId)+fileExtension);
                return new DownloadableFile(outputStream, msaReportManage.getFileName(msaPlanId)+fileExtension, workbook instanceof HSSFWorkbook ?
                        "application/vnd.ms-excel" :
                        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            }
        } catch (Exception e) {
            log.error("文件生成失败: ", e);
            throw new FabosJsonApiErrorTip("系统异常");
        }
        return OperationHandler.super.fileOperator(selectedData, param);
    }


}
