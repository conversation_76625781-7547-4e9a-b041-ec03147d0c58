package cec.jiutian.bc.process.domain.inspectionTask.proxy;

import cec.jiutian.bc.compare.mto.SpecificationManageMTO;
import cec.jiutian.bc.compare.util.BcUtil;
import cec.jiutian.bc.process.domain.inspectionTask.model.InspectionTask;
import cec.jiutian.bc.process.domain.sample.model.SampleTask;
import cec.jiutian.bc.process.enumeration.ExamineTypeEnum;
import cec.jiutian.bc.process.enumeration.InspectionTaskStateEnum;
import cec.jiutian.bc.process.enumeration.TaskStatusEnum;
import cec.jiutian.bc.urm.domain.org.entity.Org;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import com.alibaba.cloud.commons.lang.StringUtils;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class InspectionTaskProxy implements DataProxy<InspectionTask> {
    @Resource
    private FabosJsonDao fabosJsonDao;
    @Resource
    private BcUtil bcUtil;

    // 任务状态-关闭
    private static final String CLOSED = InspectionTaskStateEnum.Enum.CLOSED.name();
    // 任务状态-完成
    private static final String COMPLETE = InspectionTaskStateEnum.Enum.COMPLETE.name();
    // 样品接收状态-待检测
    private static final String TO_BE_TESTED = TaskStatusEnum.Enum.TO_BE_TESTED.name();


    @Override
    public void beforeAdd(InspectionTask inspectionTask) {
        this.checkParam(inspectionTask);
        // 手动创建 设置主单状态为"待发布"
        inspectionTask.setTaskStatus(InspectionTaskStateEnum.Enum.UNDER_RELEASED.name());
        // 设置数据源id
        SampleTask querySampleTask = new SampleTask();
        querySampleTask.setGeneralCode(inspectionTask.getSampleTaskNo());
        SampleTask sampleTask = fabosJsonDao.selectOne(querySampleTask);
        if (sampleTask != null) {
            inspectionTask.setDataSourceId(sampleTask.getDataSourceId());
        }
        this.setInsTaskCreateInfo(inspectionTask);
        // 设置样品来源
        inspectionTask.setSampleSource(sampleTask.getSampleSource());
        // 设置样品类型
        SpecificationManageMTO specification = bcUtil.getMaterialSpecification(inspectionTask.getMaterialCode());
        if (specification != null && StringUtils.isNotBlank(specification.getSpecificationName())) {
            inspectionTask.setSampleType(specification.getSpecificationName());
        }
        // 设置检测次数
        // 查询相同样品的委托申请单号
        InspectionTask taskQuery = new InspectionTask();
        taskQuery.setSampleTaskNo(inspectionTask.getSampleTaskNo());
        List<InspectionTask> listDb = fabosJsonDao.select(taskQuery);
        // 如果列表为空则返回null
        InspectionTask mostRecentTask = listDb.stream()
                .sorted(Comparator.comparing(InspectionTask::getCreateTime).reversed())
                .findFirst()
                .orElse(null);
        // 如果存在最近相同样品单号的委托单，则沿用上次的检测次数，若没有，则设置为1
        if (mostRecentTask != null) {
            inspectionTask.setTestTimes(mostRecentTask.getTestTimes());
        } else {
            inspectionTask.setTestTimes(1);
        }
        // 设置审批类型为 手动审批
        inspectionTask.setExamineType(ExamineTypeEnum.Enum.MANUAL.name());
    }

    /**
     * 手动创建时参数校验
     * <AUTHOR>
     * @date 2025/7/10 14:13
     * @param inspectionTask 委托申请单
     */
    private void checkParam(InspectionTask inspectionTask) {
        // 新增时需要卡控不能重复创建数据，同样的样品单号在检测流程中就不能重复创建委托申请单
        List<InspectionTask> list = fabosJsonDao.select(new InspectionTask());
        if (!CollectionUtils.isEmpty(list)) {
            List<InspectionTask> repeatList = list.stream()
                    .filter(ins -> ins.getSampleTaskNo() != null
                            && ins.getSampleTaskNo().equals(inspectionTask.getSampleTaskNo())
                            && !CLOSED.equals(ins.getTaskStatus())
                            && !COMPLETE.equals(ins.getTaskStatus()))
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(repeatList)) {
                throw new FabosJsonApiErrorTip("该样品单号正在检测流程中，无法重复创建，请确认！");
            }
        }
        // 样品接收单状态为 待检测、完成 才能够创建委托申请单
        SampleTask querySampleTask = new SampleTask();
        querySampleTask.setGeneralCode(inspectionTask.getSampleTaskNo());
        SampleTask sampleTask = fabosJsonDao.selectOne(querySampleTask);
        // 样品接收单 不是 待检测、完成 状态，则不能创建
        if (sampleTask != null && !TO_BE_TESTED.equals(sampleTask.getTaskStatus())
                    && !COMPLETE.equals(sampleTask.getTaskStatus())) {
            throw new FabosJsonApiErrorTip("该样品单状态非待检测或完成，无法创建委托申请单，请确认！");
        }

    }

    /**
     * 设置委托申请单创建信息
     * <AUTHOR>
     * @date 2025/7/8 18:08
     * @param inspectionTask 委托申请单
     */
    public void setInsTaskCreateInfo(InspectionTask inspectionTask) {
        // 设置申请人、申请部门、委托单创建时间
        UserContext.CurrentUser currentUser = UserContext.get();
        if (currentUser != null) {
            // 创建时间
            inspectionTask.setCreTime(new Date());
            // 申请人id、申请人名称
            inspectionTask.setCrePersonName(currentUser.getUserName());
            inspectionTask.setCrePersonId(currentUser.getUserId());
            if (currentUser.getOrgId() != null) {
                Org org = fabosJsonDao.getById(Org.class, currentUser.getOrgId());
                if (org != null) {
                    // 申请部门id、申请部门名称
                    inspectionTask.setCreDeptId(org.getId());
                    inspectionTask.setCreDeptName(org.getName());
                }
            }
        }
    }

}
