package cec.jiutian.bc.process.enumeration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 现状
 * <AUTHOR>
 * @date 2025/2/27 17:43
 */
public class TaskStatusEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum value : Enum.values()) {
            list.add(new VLModel(value.name(), value.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        //待取样
        TO_BE_PICKED_UP("待取样"),
        TO_BE_SAMPLED("待收样"),
//        SAMPLED_COMPLETED("送样完成"),
        DIVIDED_SAMPLE("待分样"),
        RECEIVED_SAMPLE_UNQUALIFIED("已收样（外观异常）"),
        TO_BE_TESTED("待检测"),//已收样外观合格就是待检测
        DETECTING("检测中"),
        COMPLETE("完成"),
        ABNORMAL("异常关闭")
        ;

        private final String value;

    }
}

