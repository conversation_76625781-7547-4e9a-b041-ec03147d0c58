package cec.jiutian.bc.process.domain.inspectionTask.model;

import cec.jiutian.bc.modeler.enumration.ComparisonMethodEnum;
import cec.jiutian.bc.modeler.enumration.InspectionValueTypeEnum;
import cec.jiutian.bc.process.domain.inspectionTask.handler.InspectResultDynamicHandler;
import cec.jiutian.bc.process.enumeration.InspectResultEnum;
import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.FabosJsonI18n;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Power;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;


@Entity
@Table(name = "lm_inspection_task_detail_quota")
@Getter
@Setter
@FabosJson(name = "检测任务明细指标",
        orderBy = "InspectionTaskDetailQuota.createTime desc",
        power = @Power(export = false,importable = false,print = false)

)
@FabosJsonI18n
public class InspectionTaskDetailQuota extends BaseModel {
    @FabosJsonField(
            views = @View(title = "检测项指标名称"),
            edit = @Edit(title = "检测项指标名称", search = @Search(vague = true), notNull = true,
                    inputType = @InputType(length = 40))
    )
    private String name;

    @FabosJsonField(
            views = @View(title = "值类型"),
            edit = @Edit(title = "值类型", type = EditType.CHOICE, search = @Search(), notNull = true,
                    choiceType = @ChoiceType(fetchHandler = InspectionValueTypeEnum.class))
    )
    private String inspectionValueType;

    @FabosJsonField(
            views = @View(title = "比较方式"),
            edit = @Edit(title = "比较方式",
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "inspectionValueType == 'number' || inspectionValueType == 'percentage'"),
                    search = @Search(),
                    type = EditType.CHOICE,
                    notNull = true,
                    choiceType = @ChoiceType(fetchHandler = ComparisonMethodEnum.class))
    )
    private String comparisonMethod;

    @FabosJsonField(
            views = @View(title = "质量分数或单位"),
            edit = @Edit(title = "质量分数或单位", inputType = @InputType(length = 40))
    )
    private String unit;

    @FabosJsonField(
            views = @View(title = "标准值"),
            edit = @Edit(title = "标准值",notNull = true,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "inspectionValueType == 'text' || comparisonMethod == 'equal'"))
    )
    //标准值可能是文本
    private String standardValue;

    @FabosJsonField(
            views = @View(title = "规格上限"),
            edit = @Edit(title = "规格上限",
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "comparisonMethod == 'range'"),
                    numberType = @NumberType(min=0,max = 9999999,precision = 6))
    )
    private Double upperValue;

    @FabosJsonField(
            views = @View(title = "是否包含上限值", show = false),
            edit = @Edit(title = "是否包含上限值", defaultVal = "true", show = false)
    )
    private Boolean isContainUpper;

    @FabosJsonField(
            views = @View(title = "规格下限"),
            edit = @Edit(title = "规格下限",
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "comparisonMethod == 'range' || comparisonMethod == 'lowerLimit'"),
                    numberType = @NumberType(min=0,max = 9999999,precision = 6))
    )
    private Double lowerValue;

    @FabosJsonField(
            views = @View(title = "是否包含下限值", show = false),
            edit = @Edit(title = "是否包含下限值", defaultVal = "true", show = false)
    )
    private Boolean isContainLower;

    @FabosJsonField(
            views = @View(title = "检测值",show = false),
            edit = @Edit(title = "检测值", notNull = true,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "testTimes == 1")
            )
    )
    //检测结果精度不确定。使用String类型+  type = EditType.NUMBER  控制前端输入。
    private String inspectValue;
    @FabosJsonField(
            views = @View(title = "检测结果"),
            edit = @Edit(title = "检测结果", type = EditType.CHOICE, search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = InspectResultEnum.class)),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "inspectValue",
                    dynamicHandler = InspectResultDynamicHandler.class))
    )
    private String inspectResult;

    @FabosJsonField(
            views = @View(title = "样品任务明细", column = "testMethodName",show = false),
            edit = @Edit(title = "样品任务明细", type = EditType.REFERENCE_TABLE,show = false,
                    referenceTableType = @ReferenceTableType(label = "testMethodCode")
            )
    )
    @ManyToOne
    @JsonIgnoreProperties("inspectionTaskDetailQuotas")
    private InspectionTaskDetail inspectionTaskDetail;

    //多次测量目前只有msa才会存在
    @FabosJsonField(
            views = @View(title = "检测结果集", type= ViewType.TABLE_VIEW),
            edit = @Edit(title = "检测结果集", type = EditType.TAB_TABLE_ADD)
    )
    @JoinColumn(name = "inspection_task_detail_quota_id")
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true,fetch = FetchType.EAGER)
    private List<InspectionManyResult> inspectionManyResults;

}
