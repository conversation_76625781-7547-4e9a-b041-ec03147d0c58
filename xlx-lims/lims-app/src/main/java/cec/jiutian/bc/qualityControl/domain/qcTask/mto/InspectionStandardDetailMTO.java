package cec.jiutian.bc.qualityControl.domain.qcTask.mto;

import cec.jiutian.bc.modeler.domain.inspectionItem.model.InspectionItem;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.type.Filter;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@FabosJson(
        name = "质检标准详情MTO"
)
@Table(name = "bd_inspection_standard_detail")
@Entity
@Getter
@Setter
public class InspectionStandardDetailMTO extends MetaModel {

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "质检标准", column = "generalCode", show = false),
            edit = @Edit(title = "质检标准", type = EditType.REFERENCE_TABLE, show = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode")
            )
    )
    @JsonIgnoreProperties("details")
    private InspectionStandardMTO inspectionStandard;

    @FabosJsonField(
            views = @View(title = "样本数量"),
            edit = @Edit(title = "样本数量", readonly = @Readonly,
                    numberType = @NumberType(min = 0, max = 999999999, precision = 2))
    )
    private Double sampleQuantity;

    @FabosJsonField(
            views = @View(title = "检测项目", column = "name"),
            edit = @Edit(title = "检测项目",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    filter = @Filter(value = "InspectionItem.status = 'Effective'")
            )
    )
    @ManyToOne
    private InspectionItem inspectionItem;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "检测项目组", column = "groupName"),
            edit = @Edit(title = "检测项目组",
                    notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "groupName"),
                    filter = @Filter(value = "status = 'ACTIVE'"),
                    queryCondition = "{\"_includeList\":\"${SPLIT(itemGroupIds, ',')}\"}"
            )
    )
    private InspectionItemGroupMTO inspectionItemGroup;

}
