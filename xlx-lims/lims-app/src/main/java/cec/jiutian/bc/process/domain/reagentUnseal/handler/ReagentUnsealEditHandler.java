package cec.jiutian.bc.process.domain.reagentUnseal.handler;

import cec.jiutian.bc.process.domain.reagentUnseal.model.ReagentUnseal;
import cec.jiutian.bc.process.domain.reagentUnseal.model.ReagentUnsealEdit;
import cec.jiutian.bc.process.domain.reagentUnseal.proxy.ReagentUnsealProxy;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Component
public class ReagentUnsealEditHandler implements OperationHandler<ReagentUnseal, ReagentUnsealEdit> {
    @Resource
    private FabosJsonDao fabosJsonDao;
    @Resource
    private ReagentUnsealProxy reagentUnsealProxy;

    @Override
    @Transactional
    public String exec(List<ReagentUnseal> data, ReagentUnsealEdit modelObject, String[] param) {
        ReagentUnseal reagentUnseal = data.get(0);
        ReagentUnseal ruDb = fabosJsonDao.getById(ReagentUnseal.class, reagentUnseal.getId());
        ruDb.setStartDate(modelObject.getStartDate());
        ruDb.setEndDate(modelObject.getEndDate());
        reagentUnsealProxy.dealCurrentState(ruDb);
        fabosJsonDao.update(ruDb);
        return null;
    }

    @Override
    public ReagentUnsealEdit fabosJsonFormValue(List<ReagentUnseal> data, ReagentUnsealEdit fabosJsonForm, String[] param) {
        ReagentUnseal reagentUnseal = data.get(0);
        BeanUtil.copyProperties(reagentUnseal, fabosJsonForm);
        return fabosJsonForm;
    }
}
