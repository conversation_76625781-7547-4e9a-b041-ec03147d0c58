package cec.jiutian.bc.msa.domain.plan.handler;

import cec.jiutian.bc.msa.domain.plan.model.MsaPlan;
import cec.jiutian.bc.msa.domain.plan.model.MsaPlanAudit;
import cec.jiutian.bc.msa.enumeration.MsaPlanAuditEnum;
import cec.jiutian.bc.msa.enumeration.MsaPlanCurrentStatusEnum;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 审核
 */
@Component
public class MsaPlanAuditHandler implements OperationHandler<MsaPlan, MsaPlanAudit> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    @Transactional
    public String exec(List<MsaPlan> data, MsaPlanAudit msaPlanAudit, String[] param) {
        MsaPlan msaPlan  = fabosJsonDao.findById(MsaPlan.class, msaPlanAudit.getId());
        //审核通过可以生成任务   审核不通过需要将状态修改为  待发布
        if (MsaPlanAuditEnum.Enum.PASS.name().equals(msaPlanAudit.getAudit())) {
            msaPlan.setCurrentState(MsaPlanCurrentStatusEnum.Enum.TO_BE_RUN.name());
        }else {
            msaPlan.setCurrentState(MsaPlanCurrentStatusEnum.Enum.TO_BE_PUBLISHED.name());
        }
        msaPlan.setAudit(msaPlanAudit.getAudit());
        msaPlan.setAuditDesc(msaPlanAudit.getAuditDesc());
        msaPlan.setAttachment(msaPlanAudit.getAttachment());
        msaPlan.setAuditPerson(UserContext.getUserName());
        msaPlan.setAuditPersonId(UserContext.getUserId());
        fabosJsonDao.mergeAndFlush(msaPlan);
        return "success";
    }




}
