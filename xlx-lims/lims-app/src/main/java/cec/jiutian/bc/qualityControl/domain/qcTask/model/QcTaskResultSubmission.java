package cec.jiutian.bc.qualityControl.domain.qcTask.model;

import cec.jiutian.bc.qualityControl.enumeration.QcStateEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.Search;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@FabosJson(name = "结果提交")
@Entity
@Getter
@Setter
@Table(name = "lm_quantity_task")
public class QcTaskResultSubmission extends MetaModel {
    @FabosJsonField(
            views = @View(title = "质控任务单号", show = false),
            edit = @Edit(title = "质控任务单号", show = false)
    )
    private String generalCode;

    // 新增、编辑时不展示
    @FabosJsonField(
            views = @View(title = "检测结果"),
            edit = @Edit(title = "检测结果", type = EditType.CHOICE,
                    notNull = true,
                    search = @Search(vague = true),
                    choiceType = @ChoiceType(fetchHandler = QcStateEnum.class))
    )
    private String detectionResult;

    @FabosJsonField(
            views = @View(title = "备注", toolTip = true),
            edit = @Edit(title = "备注", type = EditType.TEXTAREA, inputType = @InputType(length = 400))
    )
    private String remark;
}
