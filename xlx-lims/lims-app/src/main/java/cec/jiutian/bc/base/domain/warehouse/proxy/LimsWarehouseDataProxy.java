package cec.jiutian.bc.base.domain.warehouse.proxy;

import cec.jiutian.bc.base.domain.warehouse.model.LimsWarehouse;
import cec.jiutian.bc.keepSample.domain.inventory.model.KsInventory;
import cec.jiutian.bc.lineEdge.domain.inventory.model.LineInventoryDetail;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @description:
 */
@Component
public class LimsWarehouseDataProxy implements DataProxy<LimsWarehouse> {
    @Resource
    private FabosJsonDao fabosJsonDao;
    @Override
    public void beforeDelete(LimsWarehouse limsWarehouse) {
        //删除需要校验  1.仓库下是否存在物料   2.仓库下的载具是否存在样品
        LineInventoryDetail query = new LineInventoryDetail();
        query.setWarehouseId(limsWarehouse.getId());
        List<LineInventoryDetail> lineInventoryDetails = fabosJsonDao.select(query);
        if(CollectionUtils.isNotEmpty(lineInventoryDetails)){
            throw new FabosJsonApiErrorTip("删除失败，关联物料："+lineInventoryDetails.get(0).getMaterialName());
        }
        KsInventory ksInventoryQuery = new KsInventory();
        ksInventoryQuery.setWarehouseId(limsWarehouse.getId());
        List<KsInventory> ksInventories = fabosJsonDao.select(ksInventoryQuery);
        if(CollectionUtils.isNotEmpty(ksInventories)){
            throw new FabosJsonApiErrorTip("删除失败，关联样品："+ksInventories.get(0).getMaterialName());
        }

    }
}
