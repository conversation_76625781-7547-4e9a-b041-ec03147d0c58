package cec.jiutian.bc.process.enumeration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 检测任务状态枚举
 * <AUTHOR>
 * @date 2025/3/17 9:32
 */
public class InspectionTaskStateEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum value : Enum.values()) {
            list.add(new VLModel(value.name(), value.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        TAKING_SAMPLE("取样中"),
        UNDER_RELEASED("待申请"),
        UNDER_INSPECTION("待检测"),
        INSPECTING("检测中"),
        PENDING_APPROVAL("待审核"),
        APPROVAL_FAILED("审核失败"),
        CLOSED("已关闭"),
        COMPLETE("已完成");
        private final String value;

    }
}
