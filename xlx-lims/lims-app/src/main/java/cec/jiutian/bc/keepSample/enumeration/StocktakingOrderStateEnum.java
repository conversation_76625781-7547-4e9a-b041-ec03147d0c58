package cec.jiutian.bc.keepSample.enumeration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2025/3/10 16:10
 */
public class StocktakingOrderStateEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum value : Enum.values()) {
            list.add(new VLModel(value.name(), value.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        OPEN("开立"),//新建任务
        CONFIRMED("已确认"),//盘点人确认
        REVIEWED("待审核"),//盘点人提交结果。所有明细提交完成  才会到待审核
        FINISH("已完成"),//审核人审核完成
        CLOSED("已关闭"),//审核不通过, 后续操作为关闭
        ;

        private final String value;

    }
}
