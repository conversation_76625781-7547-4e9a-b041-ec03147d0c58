package cec.jiutian.bc.qualityControl.domain.qcPlan.handler;

import cec.jiutian.bc.qualityControl.domain.qcPlan.model.QcPlan;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/28
 * @description
 */
@Component
public class QcPlanOperationHandler implements OperationHandler<QcPlan,Void> {

    @Resource
    private FabosJsonDao fabosJsonDao;
    @Override
    @Transactional
    public String exec(List<QcPlan> data, Void modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            QcPlan qcPlan = data.get(0);
            String state = param[0];
            qcPlan.setCurrentStatus(state);
            fabosJsonDao.mergeAndFlush(qcPlan);
        }
        return "msg.success('操作成功')";
    }
}
