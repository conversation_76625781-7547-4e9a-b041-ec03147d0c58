package cec.jiutian.bc.inspection.enumeration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2025/3/10 16:10
 */
public class EhsQuestionAnalysisEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum value : Enum.values()) {
            list.add(new VLModel(value.name(), value.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        EIGHT_D("8D"),
        ONE_PAGE("一页纸"),
        FIVE_WHY("5why分析"),
        PCA("预防纠正措施")
        ;
        private final String value;

    }
}
