package cec.jiutian.bc.qualityControl.enumeration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 *  计划周期  通用枚举
 * <AUTHOR>
 * @date 2025/3/10 16:10
 */
public class QcPlanCycleEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum value : Enum.values()) {
            list.add(new VLModel(value.name(), value.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        YEAR("年"),
        QUARTER("季度"),
        MONTH("月"),
        WEEK("周"),
        DAY("天"),
        //HOUR("时"),
        ;

        private final String value;

    }
    //通过name返回value
    public static String getValueByName(String name) {
        for (Enum value : Enum.values()) {
            if (value.name().equals(name)) {
                return value.getValue();
            }
        }
        return null;
    }
}
