package cec.jiutian.bc.inspection.domain.question.model;

import cec.jiutian.bc.inspection.domain.question.proxy.EhsQuestionANADataProxy;
import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.FabosJsonI18n;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.type.Power;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @description:
 */
@Entity
@Table(name = "lm_ins_question")
@Getter
@Setter
@FabosJson(name = "问题分析",power = @Power(add = false,delete = false),
        orderBy = "EhsQuestionANA.createTime desc",
        dataProxy = EhsQuestionANADataProxy.class

)
@FabosJsonI18n
public class EhsQuestionANA extends BaseModel {

    //原因分析
    @FabosJsonField(
            views = @View(title = "原因分析", toolTip = true),
            edit = @Edit(title = "原因分析",type = EditType.TEXTAREA)
    )
    private String causeAnalysis;



    @FabosJsonField(
            views = @View(title = "整改任务下所有责任人的id集合,我的整改任务查询数据使用", show = false),
            edit = @Edit(title = "整改任务下所有责任人的id集合,我的整改任务查询数据使用",show = false)
    )
    private String allUserIds;


    @FabosJsonField(
            edit = @Edit(title = "整改措施", type = EditType.TAB_TABLE_ADD),
            views = @View(title = "整改措施", column = "correction", type= ViewType.TABLE_VIEW)
    )
    @JoinColumn(name = "ehs_question_id")
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    private List<EhsQuestionANACorrection> ehsQuestionCorrections;

}
