package cec.jiutian.bc.process.domain.reagentConfigRequest.handler;

import cec.jiutian.bc.base.domain.material.model.LimsMaterial;
import cec.jiutian.bc.base.enumeration.ConsumeScrapEnum;
import cec.jiutian.bc.base.enumeration.GeneralStateEnum;
import cec.jiutian.bc.base.enumeration.LmNamingRuleCodeEnum;
import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.process.domain.consumeScrap.model.ConsumeScrapDetail;
import cec.jiutian.bc.process.domain.consumeScrap.service.ConsumeScrapService;
import cec.jiutian.bc.process.domain.consumeScrap.model.ConsumeScrap;
import cec.jiutian.bc.process.domain.reagentConfigRequest.model.ReagentConfigRequest;
import cec.jiutian.bc.process.domain.reagentUnseal.model.ReagentUnseal;
import cec.jiutian.bc.process.enumeration.AvailableTypeEnum;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Component
public class ReagentRequestStateModifyHandler implements OperationHandler<ReagentConfigRequest, Void> {
    @Resource
    private FabosJsonDao fabosJsonDao;
    @Resource
    private ConsumeScrapService consumeScrapService;
    @Resource
    private NamingRuleService namingRuleService;

    @Override
    @Transactional
    public String exec(List<ReagentConfigRequest> data, Void modelObject, String[] param) {
        // 发布、确认 后设置单据状态为待确认： 新建 --> 待确认; 待确认 --> 完成
        data.forEach(r -> {
            if (GeneralStateEnum.Enum.NEWLY_BUILT.name().equals(r.getCurrentState())) {
                //发布前校验数据是否完整
                checkData(r);

                r.setCurrentState(GeneralStateEnum.Enum.TO_BE_CONFIRMED.name());
            } else if (GeneralStateEnum.Enum.TO_BE_CONFIRMED.name().equals(r.getCurrentState())) {
                //确认，需要生产启封台账和消耗台账
                createSealingLedger(r);
                consumeScrapService.createConsumeLedger(genConsumeLedger(r));
                r.setCurrentState(GeneralStateEnum.Enum.COMPLETE.name());
                r.setConfigTime(new Date());
            }
            // 更新状态
            fabosJsonDao.updateAndFlush(r);
        });
        return "success";
    }

    //数据校验
    private void checkData(ReagentConfigRequest r) {
        if(CollectionUtils.isEmpty(r.getReagentConfigRequestDetails())){
            throw new FabosJsonApiErrorTip("试剂明细为空，检查试剂配置规则是否存在明细");
        }
        r.getReagentConfigRequestDetails().forEach(d->{
            if(d.getActualUsage()==null||d.getActualUsage()==0){
                throw new FabosJsonApiErrorTip("配剂【"+d.getFormulaName()+"】实际使用数量不能为0");
            }
            if(CollectionUtils.isEmpty(d.getReagentConfigRequestDetailWares())){
                throw new FabosJsonApiErrorTip("配剂明细不能为空");
            }
        });
    }

    //创建启封台账
    private void createSealingLedger(ReagentConfigRequest r) {
        ReagentUnseal reagentUnseal = new ReagentUnseal();
        //创建台账的时候生成的  就相当于启封台账编号 这里直接使用申请单号
        //创建启封台账编号
        String generalCode = namingRuleService.getNameCode(LmNamingRuleCodeEnum.REAGENT_UNSEAL.name(),
                1, null).get(0);
        reagentUnseal.setGeneralCode(generalCode);
        reagentUnseal.setReagentNumber(r.getGeneralCode());
        reagentUnseal.setReagentName(r.getReagentConfig().getReagentName());
        reagentUnseal.setStartPerson(r.getDistributionPerson());
        reagentUnseal.setReagentSpec(r.getReagentSpec());
        //启用日期就是配剂日期
        reagentUnseal.setStartDate(r.getDistributionTime());
        reagentUnseal.setEndDate(r.getEffectiveDate());
        reagentUnseal.setCurrentState(AvailableTypeEnum.Enum.AVAILABLE.name());
        fabosJsonDao.persistAndFlush(reagentUnseal);
    }

    private List<ConsumeScrap> genConsumeLedger(ReagentConfigRequest r) {
        List<ConsumeScrap> list = new ArrayList<>();
        r.getReagentConfigRequestDetails().forEach(d->{
            ConsumeScrap consumeScrap = new ConsumeScrap();
            consumeScrap.setDate(new Date());
            consumeScrap.setConsumeScrapUser(r.getDistributionPerson());
            consumeScrap.setConsumeScrapType(ConsumeScrapEnum.Enum.CONSUME.name());
            consumeScrap.setPurpose(r.getReagentUsage());//用途
            consumeScrap.setUnit(d.getUnit());
            consumeScrap.setLimsMaterial(this.getLimsMaterial(d.getFormulaMaterialCode()));
            consumeScrap.setMaterialCode(d.getFormulaMaterialCode());
            consumeScrap.setCurrentState(GeneralStateEnum.Enum.COMPLETE.name());//完成
            consumeScrap.setUseAmount(d.getActualUsage());//实际用量
            //明细
            List<ConsumeScrapDetail> details = new ArrayList<>();
            d.getReagentConfigRequestDetailWares().forEach(w->{
                if(w.getQuantity()!=null&&w.getQuantity()>0){
                    ConsumeScrapDetail consumeScrapDetail = new ConsumeScrapDetail();
                    consumeScrapDetail.setInventoryDetailId(w.getInventoryDetailId());
                    consumeScrapDetail.setMaterialCode(w.getMaterialCode());
                    consumeScrapDetail.setMaterialName(w.getMaterialName());
                    consumeScrapDetail.setQuantity(w.getQuantity());
                    consumeScrapDetail.setWarehouseId(w.getWarehouseId());
                    consumeScrapDetail.setWarehouseName(w.getWarehouseName());
                    details.add(consumeScrapDetail);
                }
            });
            consumeScrap.setConsumeScrapDetails(details);
            list.add(consumeScrap);
        });
        return list;
    }

    /**
     * 根据配剂物料编码查询物料
     * <AUTHOR>
     * @date 2025/3/12 14:35
     * @param formulaMaterialCode
     * @return
     */
    private LimsMaterial getLimsMaterial(String formulaMaterialCode){
        LimsMaterial query = new LimsMaterial();
        query.setMaterialCode(formulaMaterialCode);
        return fabosJsonDao.selectOne(query);
    }

}
