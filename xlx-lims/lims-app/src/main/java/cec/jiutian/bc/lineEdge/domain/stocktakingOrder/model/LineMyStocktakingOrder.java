package cec.jiutian.bc.lineEdge.domain.stocktakingOrder.model;

import cec.jiutian.bc.base.domain.materialCategory.model.LimsMaterialCategory;
import cec.jiutian.bc.base.domain.warehouse.model.LimsWarehouse;
import cec.jiutian.bc.base.enumeration.LmNamingRuleCodeEnum;
import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleBaseModel;
import cec.jiutian.bc.keepSample.domain.stocktakingPlan.model.KsStocktakingPlan;
import cec.jiutian.bc.keepSample.enumeration.StocktakingOrderStateEnum;
import cec.jiutian.bc.lineEdge.domain.stocktakingOrder.handler.LineMyStockOrderConfirmedOperationHandler;
import cec.jiutian.bc.lineEdge.domain.stocktakingOrder.handler.LineStocktakingOrderDetailDynamicHandler;
import cec.jiutian.bc.lineEdge.domain.stocktakingOrder.handler.LineStocktakingOrderDynamicHandler;
import cec.jiutian.bc.lineEdge.domain.stocktakingOrder.proxy.LineMyStocktakingOrderDataProxy;
import cec.jiutian.bc.lineEdge.domain.stocktakingPlan.model.LineStocktakingPlan;
import cec.jiutian.bc.person.domain.personManage.model.PersonManage;
import cec.jiutian.bc.process.enumeration.SampleTypeEnum;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.*;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowOperation;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 */
@Entity
@Table(name = "lm_line_stocktaking_order",uniqueConstraints = {
        @UniqueConstraint(columnNames = {"generalCode"})
})
@Getter
@Setter
@FabosJson(name = "我的盘点任务",dataProxy = LineMyStocktakingOrderDataProxy.class,
        orderBy = "LineMyStocktakingOrder.createTime desc",power = @Power(add = false,delete = false,edit = false,export = false,importable = false),
        rowOperation = {
                @RowOperation(
                        title = "任务确认",
                        code = "LineMyStocktakingOrder@CONFIRMED",
                        operationHandler = LineMyStockOrderConfirmedOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "是否确定操作？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "LineMyStocktakingOrder@CONFIRMED"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "currentState != 'OPEN'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "任务执行",
                        code = "LineMyStocktakingOrder@RUN",
                        mode = RowOperation.Mode.SINGLE,
                        type = RowOperation.Type.POPUP,
                        //operationHandler = SampleTaskCOLLHandler.class,
                        submitMethod = RowOperation.SubmitMethod.UPDATE,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = LineStocktakingOrderRun.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "LineMyStocktakingOrder@RUN"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "currentState != 'CONFIRMED'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                )
        }
)
@FabosJsonI18n
public class LineMyStocktakingOrder extends NamingRuleBaseModel {
    //盘点计划
    @ManyToOne
    @FabosJsonField(
            views = @View(title = "盘点计划", column = "generalCode"),
            edit = @Edit(title = "盘点计划",
                    type = EditType.REFERENCE_TABLE,
                    filter = @Filter("ksStocktakingPlan.stocktakingPlanState = 'EFFECT'"),
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode"),
                    search = @Search())
    )
    private LineStocktakingPlan ksStocktakingPlan;
    //盘点范围
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "mto_id",foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @FabosJsonField(
            views = @View(title = "盘点范围", column = "name"),
            edit = @Edit(title = "盘点范围",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(type = ReferenceTableType.SelectShowTypeMTO.LIST),
                    allowAddMultipleRows = false,
                    search = @Search()
            ),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "ksStocktakingPlan", dynamicHandler = LineStocktakingOrderDynamicHandler.class))

    )
    private LimsMaterialCategory stocktakingRange;

    //计划开始时间
    @FabosJsonField(
            views = @View(title = "开始时间"),
            edit = @Edit(title = "开始时间", notNull = true, type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE),
                    inputType = @InputType(length = 40))
    )
    private Date stocktakingStartDate;
    //计划结束时间
    @FabosJsonField(
            views = @View(title = "截止时间"),
            edit = @Edit(title = "截止时间",notNull = true,  type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE),
                    inputType = @InputType(length = 40))
    )
    private Date stocktakingEndDate;
    //盘点人员
    @ManyToOne
    @FabosJsonField(
            views = @View(title = "盘点区域", column = "name"),
            edit = @Edit(title = "盘点区域",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    search = @Search())
    )
    private LimsWarehouse limsWarehouse;
    //盘点人员
    @ManyToOne
    @FabosJsonField(
            views = @View(title = "盘点人员", column = "name"),
            edit = @Edit(title = "盘点人员",
                    type = EditType.REFERENCE_TABLE,
                    filter = @Filter("PersonManage.personState = 'AVAILABLE'"),
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    search = @Search())
    )
    private PersonManage stocktakingPerson;
    //审核人员
    @ManyToOne
    @FabosJsonField(
            views = @View(title = "审核人员", column = "name"),
            edit = @Edit(title = "审核人员",
                    type = EditType.REFERENCE_TABLE,
                    filter = @Filter("PersonManage.personState = 'AVAILABLE'"),
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    search = @Search())
    )
    private PersonManage stocktakingReviewPerson;

    //计划状态
    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",show = false,search = @Search(),type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = StocktakingOrderStateEnum.class))
    )
    private String currentState;


    @Override
    public String getNamingCode() {
        return LmNamingRuleCodeEnum.LM_LINE_STOCKTAKING_ORDER.name();
    }

    @OneToMany(cascade = CascadeType.ALL)
    @FabosJsonField(
            views = @View(title = "明细", type = ViewType.TABLE_VIEW, index = 8),
            edit = @Edit(title = "明细", type = EditType.TAB_REFERENCE_GENERATE),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = {"stocktakingRange","ksStocktakingPlan","limsWarehouse"}, dynamicHandler = LineStocktakingOrderDetailDynamicHandler.class)),
            referenceGenerateType = @ReferenceGenerateType()
    )
    @JoinColumn(name = "stocktaking_order_id")
    private List<LineStocktakingOrderDetailRun> stocktakingOrderDetails;

}
