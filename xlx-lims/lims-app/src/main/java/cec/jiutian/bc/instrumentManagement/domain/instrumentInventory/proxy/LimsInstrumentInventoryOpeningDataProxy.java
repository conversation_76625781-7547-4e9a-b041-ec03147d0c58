package cec.jiutian.bc.instrumentManagement.domain.instrumentInventory.proxy;

import cec.jiutian.bc.instrumentManagement.domain.instrumentInventory.model.LimsInstrumentInventoryOpening;
import cec.jiutian.bc.instrumentManagement.enums.InstrumentStatusEnum;
import cec.jiutian.bc.sparePartsManagement.domain.inventoryLedger.enums.InventoryLedgerStatusEnum;
import cec.jiutian.view.fun.DataProxy;

public class LimsInstrumentInventoryOpeningDataProxy implements DataProxy<LimsInstrumentInventoryOpening> {

    @Override
    public void beforeAdd(LimsInstrumentInventoryOpening instrumentInventory) {
        instrumentInventory.setAccountingUnitQuantity(1D);
        instrumentInventory.setAvailableQuantity(1D);
        instrumentInventory.setMaterialType("instrument");
        instrumentInventory.setBusinessState(InventoryLedgerStatusEnum.Enum.normal.name());
    }
}
