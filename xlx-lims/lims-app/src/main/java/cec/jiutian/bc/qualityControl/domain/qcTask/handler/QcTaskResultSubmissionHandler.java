package cec.jiutian.bc.qualityControl.domain.qcTask.handler;

import cec.jiutian.bc.process.port.client.SpcProcessFeignClient;
import cec.jiutian.bc.qualityControl.domain.qcLedger.service.QcLedgerService;
import cec.jiutian.bc.qualityControl.domain.qcPlan.model.QcPlan;
import cec.jiutian.bc.qualityControl.domain.qcTask.model.QcTask;
import cec.jiutian.bc.qualityControl.domain.qcTask.model.QcTaskResultSubmission;
import cec.jiutian.bc.qualityControl.domain.qcTask.service.QcNotifyService;
import cec.jiutian.bc.qualityControl.domain.qcTask.service.QcTaskService;
import cec.jiutian.bc.qualityControl.enumeration.QcTaskStateEnum;
import cec.jiutian.common.util.StringUtils;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Component
@Slf4j
public class QcTaskResultSubmissionHandler implements OperationHandler<QcTask, QcTaskResultSubmission> {
    @Resource
    private FabosJsonDao fabosJsonDao;
    @Resource
    private QcLedgerService qcLedgerService;
    @Resource
    private QcNotifyService qcNotifyService;
    @Resource
    private QcTaskService qcTaskService;
    // 定义线程池
    private static final ExecutorService asyncExecutor = Executors.newFixedThreadPool(5);


    @Override
    @Transactional
    public String exec(List<QcTask> data, QcTaskResultSubmission modelObject, String[] param) {
        QcTask qcTaskDb = fabosJsonDao.getById(QcTask.class, modelObject.getId());
        qcTaskDb.setDetectionResult(modelObject.getDetectionResult());
        if (!StringUtils.isBlank(modelObject.getRemark())) {
            if (!StringUtils.isBlank(qcTaskDb.getRemark())) {
                qcTaskDb.getRemark().concat(modelObject.getRemark());
            } else
                qcTaskDb.setRemark(modelObject.getRemark());
        }
        // 设置状态为 已完成
        qcTaskDb.setCurrentState(QcTaskStateEnum.Enum.COMPLETE.name());
        // 提交结果之后, 设置任务完成时间、设置提交标志为2
        qcTaskDb.setFinishTime(new Date());
        qcTaskDb.setSubmissionFlag("2");
        fabosJsonDao.update(qcTaskDb);
        // 调用质控台账服务, 更新相关的质控台账数据
        String planId = qcTaskDb.getQcPlan().getId();
        QcPlan qcPlan = fabosJsonDao.getById(QcPlan.class, planId);
        if (qcPlan != null && qcPlan.getQcLedger() != null) {
            qcLedgerService.updateQcLedger(qcPlan.getQcLedger().getId(), qcTaskDb);
        }
        //发送数据到spc
        qcTaskService.sendSPCData(qcTaskDb);
        CompletableFuture.runAsync(() -> {
            // 调用质控任务告警服务
            try {
                qcNotifyService.notifyByQcTaskType(qcTaskDb);
            } catch (Exception e) {
                log.info("----------------质控任务告警报错----------------", e);
                throw new RuntimeException(e);
            }
        }, asyncExecutor);
        return null;
    }

    @Override
    public QcTaskResultSubmission fabosJsonFormValue(List<QcTask> data, QcTaskResultSubmission fabosJsonForm, String[] param) {
        QcTask qcTask = data.get(0);
        BeanUtil.copyProperties(qcTask, fabosJsonForm);
        return fabosJsonForm;
    }
}
