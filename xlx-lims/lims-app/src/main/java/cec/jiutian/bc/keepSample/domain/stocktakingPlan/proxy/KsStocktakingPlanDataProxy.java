package cec.jiutian.bc.keepSample.domain.stocktakingPlan.proxy;

import cec.jiutian.bc.base.domain.carrier.model.LimsCarrier;
import cec.jiutian.bc.base.domain.shelves.model.LimsShelves;
import cec.jiutian.bc.base.domain.warehouse.model.LimsWarehouse;
import cec.jiutian.bc.base.enumeration.WarehouseTypeEnum;
import cec.jiutian.bc.keepSample.domain.stocktakingPlan.model.KsStocktakingPlan;
import cec.jiutian.bc.keepSample.domain.stocktakingPlan.utils.StocktakingDateUtil;
import cec.jiutian.bc.keepSample.enumeration.EffectOrLapseEnum;
import cec.jiutian.common.util.StringUtils;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class KsStocktakingPlanDataProxy implements DataProxy<KsStocktakingPlan> {

    @Override
    public void beforeAdd(KsStocktakingPlan ksStocktakingPlan) {
        if(!StocktakingDateUtil.checkDate(ksStocktakingPlan.getStocktakingPlanStartDate(),ksStocktakingPlan.getStocktakingPlanEndDate())){
            throw new FabosJsonApiErrorTip("开始日期不能晚于截止日期");
        }
        ksStocktakingPlan.setStocktakingPlanState(EffectOrLapseEnum.Enum.LAPSE.name());
    }
}
