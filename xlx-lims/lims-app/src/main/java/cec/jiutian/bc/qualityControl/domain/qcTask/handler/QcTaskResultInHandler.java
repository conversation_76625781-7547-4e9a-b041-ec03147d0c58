package cec.jiutian.bc.qualityControl.domain.qcTask.handler;

import cec.jiutian.bc.qualityControl.domain.qcTask.model.MyQcTask;
import cec.jiutian.bc.qualityControl.domain.qcTask.model.QcTaskDetail;
import cec.jiutian.bc.qualityControl.domain.qcTask.model.QcTaskDetailQuota;
import cec.jiutian.bc.qualityControl.domain.qcTask.model.QcTaskResultInput;
import cec.jiutian.bc.qualityControl.enumeration.QcTaskDetailResultEnum;
import cec.jiutian.bc.qualityControl.enumeration.QcTaskDetailStateEnum;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 结果录入处理器
 * <AUTHOR>
 * @date 2025/3/30 21:47
 */
@Component
public class QcTaskResultInHandler implements OperationHandler<MyQcTask, QcTaskResultInput> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    @Transactional
    public String exec(List<MyQcTask> data, QcTaskResultInput modelObject, String[] param) {
        // 提交之前校验数据的完整性, 缺少数据, 则不允许提交
        this.checkParam(modelObject);
        List<QcTaskDetailQuota> qcTaskDetailQuota = modelObject.getQcTaskDetailQuota();
        qcTaskDetailQuota.forEach(
                qcQuota -> fabosJsonDao.update(qcQuota)
        );
        // 此处需要对子单的状态、检测人、检测结论进行更新
        String qcTaskId = modelObject.getParentTaskId();
        // 根据检测项目查询子单, 如果子单上的所有检测项都为"通过"，则子单状态为"通过"，否则为"不通过"
        MyQcTask myQcTaskDb = fabosJsonDao.getById(MyQcTask.class, qcTaskId);
        myQcTaskDb.getQcTaskDetails().forEach(
                // 校验所有检测指标是否都为"通过"
                myQcTaskDetail -> {
                    // 统计未通过指标的数量, 只要有一个未通过 则子单检测结论为未通过
                    long count = myQcTaskDetail.getQcTaskDetailQuotas().stream()
                            .filter(quota -> QcTaskDetailResultEnum.Enum.NOT_PASS.name().equals(quota.getInspectResult()))
                            .count();
                    // 如果都通过, 则质控任务子单通过
                    if (count == 0) {
                        myQcTaskDetail.setInspectResult(QcTaskDetailResultEnum.Enum.PASS.name());
                    } else {
                        myQcTaskDetail.setInspectResult(QcTaskDetailResultEnum.Enum.NOT_PASS.name());
                    }
                    // 设置质控任务子单状态为已完成
                    myQcTaskDetail.setState(QcTaskDetailStateEnum.Enum.COMPLETE.name());
                }
        );
        // 设置结果提交标志为1
        myQcTaskDb.setSubmissionFlag("1");
        fabosJsonDao.update(myQcTaskDb);
        return null;
    }

    @Override
    public QcTaskResultInput fabosJsonFormValue(List<MyQcTask> data, QcTaskResultInput fabosJsonForm, String[] param) {
        MyQcTask myQcTask = data.get(0);
        List<QcTaskDetail> qcTaskDetails = myQcTask.getQcTaskDetails();
        List<QcTaskDetailQuota> qcTaskDetailQuotaList = new ArrayList<>();
        // 将明细数据中的指标列表 装载到弹窗列表中
        qcTaskDetails.forEach(qcTaskDetail -> {
            List<QcTaskDetailQuota> qcTaskDetailQuotas = qcTaskDetail.getQcTaskDetailQuotas();
            if (CollectionUtils.isNotEmpty(qcTaskDetailQuotas)) {
                qcTaskDetailQuotaList.addAll(qcTaskDetailQuotas);
            }
        });
        // 设置主单id
        fabosJsonForm.setParentTaskId(myQcTask.getId());
        fabosJsonForm.setQcTaskDetailQuota(qcTaskDetailQuotaList);
        return fabosJsonForm;
    }

    /**
     * 校验提交数据的完整性：检测值和检测结果都完整才能提交
     * <AUTHOR>
     * @date 2025/3/29 16:13
     * @param modelObject
     */
    private void checkParam(QcTaskResultInput modelObject) {
        modelObject.getQcTaskDetailQuota().forEach(
                qcTaskDetailQuota -> {
                    if (qcTaskDetailQuota.getInspectValue() == null || qcTaskDetailQuota.getInspectResult() == null) {
                        throw new FabosJsonApiErrorTip("录入数据不完整，无法提交，请确认！");
                    }
                }
        );
    }
}
