package cec.jiutian.bc.process.enumeration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 现状
 * <AUTHOR>
 * @date 2025/2/27 17:43
 */
public class UnqualifiedHandleWayEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum value : Enum.values()) {
            list.add(new VLModel(value.name(), value.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        RESAMPLE("重新取样"),
        REPACK("重新包装"),
        PRODUCT_ANOMALY_HANDLING("产品异常处置")
        ;

        private final String value;

    }
}

