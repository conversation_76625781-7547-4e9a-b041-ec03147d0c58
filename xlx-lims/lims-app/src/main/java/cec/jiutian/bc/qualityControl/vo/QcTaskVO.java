package cec.jiutian.bc.qualityControl.vo;

import lombok.Data;

@Data
public class QcTaskVO {
    /**
     * 时间
     */
    private String time;

    /**
     * 检测指标
     */
    private String value;

    /**
     * 参考线1
     */
    private String referLine1;

    /**
     * 参考线2
     */
    private String referLine2;

    /**
     * 最小值
     */
    private String min;

    /**
     * 最大值
     */
    private String max;

    /**
     * 中位数
     */
    private String median;

    /**
     * 第一四分位数
     */
    private String q1;

    /**
     * 第三四分位数
     */
    private String q3;

    /**
     * 设备类型（名称）
     */
    private String equipType;

    /**
     * 设备编号
     */
    private String equipCode;

    /**
     * 人员id
     */
    private String personId;

    /**
     * 人员（姓名）
     */
    private String personName;

    /**
     * 实验室id
     */
    private String expLabId;

    /**
     * 实验室名称
     */
    private String expLabName;

    /**
     * 检测室id
     */
    private String labId;

    /**
     * 检测室名称
     */
    private String labName;

    /**
     * 超限个数
     */
    private String outCount;

    /**
     * 累计比例
     */
    private String totalRatio;

}

