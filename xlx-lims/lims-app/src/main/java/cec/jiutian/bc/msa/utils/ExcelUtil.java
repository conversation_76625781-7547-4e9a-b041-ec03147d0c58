package cec.jiutian.bc.msa.utils;

import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellUtil;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.List;
/**
 * <AUTHOR>
 * @description:
 */
@Slf4j
public class ExcelUtil {
    public static Workbook createWorkbook(InputStream inputStream) throws IOException {
        BufferedInputStream is = new BufferedInputStream(inputStream);
        boolean isMarkSupported = is.markSupported();
        log.info("输入流是否支持 mark/reset: " + isMarkSupported);
        is.mark(8);
        byte[] fileHeader = new byte[4];
        int bytesRead = is.read(fileHeader);
        is.reset();

        if (bytesRead < 4) {
            throw new IOException("无效的Excel文件");
        }

        // 检测文件头
        if (isXlsxFile(fileHeader)) {
            return new XSSFWorkbook(is);
        } else if (isXlsFile(fileHeader)) {
            return new HSSFWorkbook(is);
        } else {
            throw new IllegalArgumentException("不支持的Excel格式");
        }
    }

    // 判断XLSX格式（PK\x03\x04）
    public static boolean isXlsxFile(byte[] header) {
        return header[0] == 0x50 && header[1] == 0x4B &&
                header[2] == 0x03 && header[3] == 0x04;
    }

    // 判断XLS格式（D0 CF 11 E0）
    public static boolean isXlsFile(byte[] header) {
        return header[0] == (byte)0xD0 && header[1] == (byte)0xCF &&
                header[2] == 0x11 && header[3] == (byte)0xE0;
    }

    /**
     * 将List<Double>中的元素依次写入Excel工作表（Sheet）的特定 列 中，从指定的起始行开始往下添加
     * @param sheet
     * @param values   填充的数据列表。如果是规定的模版，需要严格校验数据个数是否满足，超过会出现覆盖的情况
     * @param startRow   起始行号（基于0的索引）。
     * @param colIndex   目标列的索引号（基于0的索引）。
     */
    public static void fillData(Sheet sheet, List<Double> values, int startRow, int colIndex) {
        if (values == null) return;

        for (int i = 0; i < values.size(); i++) {
            Row row = sheet.getRow(startRow + i);
            if (row == null) {
                row = sheet.createRow(startRow + i);
            }
            Cell cell = row.getCell(colIndex, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
            cell.setCellValue(values.get(i));
        }
    }

    /**
     * 为已合并的单元格赋值
     *
     * @param sheet 要操作的 Sheet
     * @param row   目标行索引（从 0 开始）  合并单元格的左上角单元格行号
     * @param col   目标列索引（从 0 开始）  合并单元格的左上角单元格列号
     * @param value 要设置的值
     * @param <T>   值的类型
     * @return 赋值是否成功
     */
    public static <T> boolean setValueToMergedCell(Sheet sheet, int row, int col, T value) {
        if (value == null) return false;
        // 获取合并区域列表
        List<CellRangeAddress> mergedRegions = sheet.getMergedRegions();

        // 遍历合并区域
        for (CellRangeAddress region : mergedRegions) {
            // 检查目标单元格是否在合并区域内
            if (region.isInRange(row, col)) {
                // 获取合并区域的起始行和列
                int firstRow = region.getFirstRow();
                int firstCol = region.getFirstColumn();

                // 获取合并区域的第一个单元格（内容写入这里）
                Row firstRowObj = sheet.getRow(firstRow);
                if (firstRowObj == null) {
                    firstRowObj = sheet.createRow(firstRow);
                }
                Cell firstCell = firstRowObj.getCell(firstCol);
                if (firstCell == null) {
                    firstCell = firstRowObj.createCell(firstCol);
                }

                // 对合并单元格设置值
                if (value instanceof String) {
                    firstCell.setCellValue((String) value);
                } else if (value instanceof Number) {
                    firstCell.setCellValue(((Number) value).doubleValue());
                } else if (value instanceof Boolean) {
                    firstCell.setCellValue((Boolean) value);
                } else if (value instanceof RichTextString) {
                    firstCell.setCellValue((RichTextString) value);
                } else {
                    firstCell.setCellValue(value.toString());
                }

                return true;
            }
        }
        return false;
    }


    /**
     * 填充数据到Sheet指定区域
     * @param sheet       工作表对象
     * @param startRow    起始行号（从0开始）
     * @param endRow      结束行号（包含，从0开始）
     * @param startCol    起始列号（从0开始）
     * @param endCol      结束列号（包含，从0开始）
     * @param dataList    要填充的数据列表（按行优先顺序排列）  数量必须要完全匹配  而且必须是连续的整块区域。
     */
    public static void fillDataToRegion(Sheet sheet,
                                        int startRow, int endRow,
                                        int startCol, int endCol,
                                        List<Double> dataList) {
        // 参数校验
        int totalRows = endRow - startRow + 1;
        int colsPerRow = endCol - startCol + 1;
        int requiredSize = totalRows * colsPerRow;

//        if (dataList.size() != requiredSize) {
//            throw new FabosJsonApiErrorTip("数据量不匹配，需要" + requiredSize + "个数据，实际收到" + dataList.size());
//        }

//        // 逐行填充数据
//        for (int rowOffset = 0; rowOffset < totalRows; rowOffset++) {
//            int currentRowNum = startRow + rowOffset;
//            Row row = getOrCreateRow(sheet, currentRowNum);
//
//            // 获取当前行对应的数据段（每行colsPerRow个数据）
//            List<Double> rowData = dataList.subList(
//                    rowOffset * colsPerRow,
//                    (rowOffset + 1) * colsPerRow
//            );
//
//            fillRowData(row, rowData, startCol);
//        }
        // 逐行填充数据
        int dataIdx = 0; // 新增数据索引变量
        for (int rowOffset = 0; rowOffset < totalRows; rowOffset++) {
            int currentRowNum = startRow + rowOffset;
            Row row = getOrCreateRow(sheet, currentRowNum);

            // 计算当前行需要填充的列数（以实际剩余数据为准）
            int colsToFill = Math.min(colsPerRow, dataList.size() - dataIdx);

            // 如果没有更多数据要填充，跳出循环
            if (colsToFill <= 0) {
                break;
            }

            // 填充当前行的数据
            for (int colOffset = 0; colOffset < colsToFill; colOffset++) {
                int currentCol = startCol + colOffset;
                Cell cell = getOrCreateCell(row, currentCol);
                cell.setCellValue(dataList.get(dataIdx++));
            }
        }
    }

    private static Row getOrCreateRow(Sheet sheet, int rowNum) {
        Row row = sheet.getRow(rowNum);
        if (row == null) {
            row = sheet.createRow(rowNum);
        }
        return row;
    }

    private static void fillRowData(Row row, List<Double> rowData, int startCol) {
        for (int colOffset = 0; colOffset < rowData.size(); colOffset++) {
            int currentCol = startCol + colOffset;
            Cell cell = getOrCreateCell(row, currentCol);
            cell.setCellValue(rowData.get(colOffset));
        }
    }

    private static Cell getOrCreateCell(Row row, int col) {
        Cell cell = row.getCell(col);
        if (cell == null) {
            cell = row.createCell(col);
        }
        return cell;
    }


    /**
     * 填充单元格值  非合并单元格
     * @param sheet
     * @param row
     * @param col
     * @param value
     * @param <T>
     */
    public static <T> void fillCellValue(Sheet sheet, int row, int col, T value) {
        if(value==null){
            return;
        }
        // 获取指定行，如果不存在则创建
        Row targetRow = CellUtil.getRow(row, sheet);
        // 获取指定列，如果不存在则创建
        Cell targetCell = CellUtil.getCell(targetRow, col);

        // 根据value的类型设置单元格值
        if (value instanceof String) {
            targetCell.setCellValue((String) value);
        } else if (value instanceof Integer || value instanceof Long) {
            targetCell.setCellValue(((Number) value).doubleValue());
        } else if (value instanceof Double || value instanceof Float) {
            targetCell.setCellValue(((Number) value).doubleValue());
        } else if (value instanceof Boolean) {
            targetCell.setCellValue((Boolean) value);
        } else if (value instanceof Date) {
            // 设置日期格式
            CellStyle dateStyle = sheet.getWorkbook().createCellStyle();
            CreationHelper createHelper = sheet.getWorkbook().getCreationHelper();
            dateStyle.setDataFormat(createHelper.createDataFormat().getFormat("yyyy-MM-dd"));
            targetCell.setCellStyle(dateStyle);
            targetCell.setCellValue((Date) value);
        } else {
            // 处理其他类型或默认为字符串
            targetCell.setCellValue(value.toString());
        }
    }
}
