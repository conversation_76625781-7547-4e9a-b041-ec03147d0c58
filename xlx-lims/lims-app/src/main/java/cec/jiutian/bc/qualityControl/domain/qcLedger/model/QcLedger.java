package cec.jiutian.bc.qualityControl.domain.qcLedger.model;

import cec.jiutian.bc.base.domain.lab.model.Lab;
import cec.jiutian.bc.base.enumeration.LmNamingRuleCodeEnum;
import cec.jiutian.bc.equipmentArchive.domain.equipment.model.Equipment;
import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleBaseModel;
import cec.jiutian.bc.person.domain.personManage.model.PersonManage;
import cec.jiutian.bc.qualityControl.domain.qcLedger.proxy.QcLedgerDataProxy;
import cec.jiutian.bc.qualityControl.enumeration.EffectiveEnum;
import cec.jiutian.bc.qualityControl.enumeration.QcMethodEnum;
import cec.jiutian.bc.qualityControl.enumeration.QcStateEnum;
import cec.jiutian.bc.qualityControl.enumeration.QcTypeEnum;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.FabosJsonI18n;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DateType;
import cec.jiutian.view.field.edit.DependFieldDisplay;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@FabosJson(name = "质控台账",
        orderBy = "QcLedger.createTime desc",
        power = @Power(export = false, print = false),
        dataProxy = QcLedgerDataProxy.class
)
@Entity
@Getter
@Setter
@Table(name = "lm_quantity_ledger")
@FabosJsonI18n
@TemplateType(type = "usual")
public class QcLedger extends NamingRuleBaseModel {
    @Override
    public String getNamingCode() {
        return LmNamingRuleCodeEnum.LM_QC_LEDGER.name();
    }

    @FabosJsonField(
            views = @View(title = "质控项目名称"),
            edit = @Edit(title = "质控项目名称", notNull = true, search = @Search(vague = true))
    )
    private String qcProjectName;

    @FabosJsonField(
            views = @View(title = "质控方式"),
            edit = @Edit(title = "质控方式", notNull = true, type = EditType.CHOICE, search = @Search(vague = true),
                    choiceType = @ChoiceType(fetchHandler = QcMethodEnum.class))
    )
    private String qcMethod;

    @FabosJsonField(
            views = @View(title = "质控类型"),
            edit = @Edit(title = "质控类型", notNull = true, type = EditType.CHOICE, search = @Search(vague = true),
                    choiceType = @ChoiceType(fetchHandler = QcTypeEnum.class))
    )
    private String qcType;

    // 非必填
    @FabosJsonField(
            views = @View(title = "关联设备", column = "name"),
            edit = @Edit(title = "关联设备",
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "qcType == 'EQUIP_QC'"),
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name")
            )
    )
    @ManyToOne
    private Equipment equipment;

    @FabosJsonField(
            views = @View(title = "关联检测室", column = "labName"),
            edit = @Edit(title = "关联检测室",
                    notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "labName")
            )
    )
    @ManyToOne
    private Lab lab;

    @FabosJsonField(
            views = @View(title = "实施范围", toolTip = true),
            edit = @Edit(title = "实施范围", notNull = true, type = EditType.TEXTAREA, inputType = @InputType(length = 400))
    )
    private String impScope;

    @FabosJsonField(
            views = @View(title = "判定依据", toolTip = true),
            edit = @Edit(title = "判定依据", notNull = true, type = EditType.TEXTAREA, inputType = @InputType(length = 400))
    )
    private String judAccord;

    @FabosJsonField(
            views = @View(title = "判定标准", toolTip = true),
            edit = @Edit(title = "判定标准", notNull = true, type = EditType.TEXTAREA, inputType = @InputType(length = 400))
    )
    private String judStandard;

    @FabosJsonField(
            views = @View(title = "质控操作", toolTip = true),
            edit = @Edit(title = "质控操作", notNull = true, type = EditType.TEXTAREA, inputType = @InputType(length = 400))
    )
    private String qcOperate;

    @FabosJsonField(
            views = @View(title = "是否启用"),
            edit = @Edit(title = "是否启用", type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = EffectiveEnum.class), defaultVal = "Y")
    )
    private String effective;

    // 新增编辑时不展示
    @ManyToOne
    @FabosJsonField(
            views = @View(title = "创建人", column = "name"),
            edit = @Edit(title = "创建人",
                    type = EditType.REFERENCE_TABLE,
                    show = false,
                    filter = @Filter("PersonManage.personState = 'AVAILABLE'"),
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    search = @Search())
    )
    private PersonManage createPerson;

    // 新增编辑时不展示
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "创建时间"),
            edit = @Edit(title = "创建时间", show = false, type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE_TIME),  search = @Search(vague = true))
    )
    private Date creTime;

    // 新增编辑时不展示
    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态", search = @Search(), type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = QcStateEnum.class))
    )
    private String currentState;

    // 新增编辑时不展示
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "最新测试时间"),
            edit = @Edit(title = "最新测试时间", show = false, type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE_TIME),
                    inputType = @InputType(length = 40))
    )
    private Date newDetectTime;

    @FabosJsonField(
            views = @View(title = "质控任务编号"),
            edit = @Edit(title = "质控任务编号", show = false)
    )
    private String qcTaskNo;

    @FabosJsonField(
            views = @View(title = "产品牌号"),
            edit = @Edit(title = "产品牌号", show = false)
    )
    private String brandCode;

    @FabosJsonField(
            views = @View(title = "备注", toolTip = true),
            edit = @Edit(title = "备注", type = EditType.TEXTAREA, inputType = @InputType(length = 400))
    )
    private String remark;
}
