package cec.jiutian.bc.qualityControl.domain.qcTask.handler;

import cec.jiutian.bc.qualityControl.domain.qcTask.model.QcTask;
import cec.jiutian.bc.qualityControl.enumeration.QcTaskStateEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 待发布 --> 执行中
 * <AUTHOR>
 * @date 2025/3/27 13:50
 */
@Component
public class QcTaskInvalidStateHandler implements OperationHandler<QcTask, Void> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    @Transactional
    public String exec(List<QcTask> data, Void modelObject, String[] param) {
        QcTask qcTask = data.get(0);
        if (QcTaskStateEnum.Enum.UNDER_RELEASED.name().equals(qcTask.getCurrentState())) {
            qcTask.setCurrentState(QcTaskStateEnum.Enum.DEPRECATED.name());
            fabosJsonDao.update(qcTask);
        }
        return null;
    }
}
