package cec.jiutian.bc.qualityControl.enumeration;

import cec.jiutian.bc.generalModeler.enumeration.YesOrNoEnum;
import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 是否枚举
 * <AUTHOR>
 * @date 2025/3/25 14:45
 */
public class EffectiveEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum value : Enum.values()) {
            list.add(new VLModel(value.name(), value.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        Y("是"),
        N("否");

        private final String value;

    }
}