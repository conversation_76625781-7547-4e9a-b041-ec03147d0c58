package cec.jiutian.bc.msa.domain.examine.proxy;

import cec.jiutian.view.fun.FlowProxy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


@Component
@Slf4j
public class MsaReportFlowProxy extends FlowProxy {

    @Override
    public void onEvent(Object event, Object entity) {

        log.info("回调成功");
    }

    @Override
    public void beforeSubmit(Object entity) {

    }

    @Override
    public void afterSubmit(Object entity) {

    }

}
