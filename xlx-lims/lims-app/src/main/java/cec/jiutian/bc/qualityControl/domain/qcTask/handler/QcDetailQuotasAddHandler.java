package cec.jiutian.bc.qualityControl.domain.qcTask.handler;

import cec.jiutian.bc.compare.util.BcUtil;
import cec.jiutian.bc.generalModeler.util.StringUtils;
import cec.jiutian.bc.modeler.enumration.InspectionValueTypeEnum;
import cec.jiutian.bc.qualityControl.domain.qcTask.model.QcTaskDetailQuota;
import cec.jiutian.bc.qualityControl.domain.qcTask.model.QcTaskResultInput;
import cec.jiutian.bc.qualityControl.enumeration.QcTaskDetailResultEnum;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class QcDetailQuotasAddHandler implements DependFiled.DynamicHandler<QcTaskResultInput> {
    @Resource
    private BcUtil bcUtil;
    private static final String PASS = QcTaskDetailResultEnum.Enum.PASS.name();
    private static final String NOT_PASS = QcTaskDetailResultEnum.Enum.NOT_PASS.name();
    // 值类型
    private static final String TEXT = InspectionValueTypeEnum.Enum.text.name();
    @Override
    public Map<String, Object> handle(QcTaskResultInput qcTaskResultInput) {
        Map<String, Object> map = new HashMap<>();
        List<QcTaskDetailQuota> quotaList = qcTaskResultInput.getQcTaskDetailQuota();
        for (QcTaskDetailQuota quota : quotaList) {
            String inspectValue = quota.getInspectValue();
            if (StringUtils.isBlank(inspectValue)) {
                quota.setInspectResult("");
                continue;
            }
            // 如果检测值类型是文本，则直接跳过，不自动检测是否合格
            if (TEXT.equals(quota.getInspectionValueType())) {
                continue;
            }
            // 如果检测指标值不符合规范直接跳过
            boolean validQuota = bcUtil.isValidQuota(quota.getComparisonMethod(), quota.getStandardValue(), quota.getUpperValue(),
                    quota.getLowerValue(), inspectValue);
            if (!validQuota) {
                continue;
            }
            boolean qualifiedQuota = bcUtil.isQualifiedQuota(quota.getComparisonMethod(), quota.getStandardValue(),
                    quota.getUpperValue(), quota.getLowerValue(), inspectValue);
            if (qualifiedQuota) {
                quota.setInspectResult(PASS);
            } else {
                quota.setInspectResult(NOT_PASS);
            }
        }
        map.put("qcTaskDetailQuota", quotaList);
        return map;
    }
}
