package cec.jiutian.bc.qualityControl.domain.qcTask.handler;

import cec.jiutian.bc.base.domain.experimentMethod.model.ExpMethod;
import cec.jiutian.bc.compare.util.BcUtil;
import cec.jiutian.bc.generalModeler.util.StringUtils;
import cec.jiutian.bc.keepSample.domain.inventory.model.KsInventory;
import cec.jiutian.bc.modeler.domain.inspectionItem.model.InspectionItem;
import cec.jiutian.bc.modeler.domain.inspectionMethod.model.InspectionMethod;
import cec.jiutian.bc.process.domain.sample.mto.WmsInventoryMTO;
import cec.jiutian.bc.qualityControl.domain.qcTask.model.QcTask;
import cec.jiutian.bc.qualityControl.domain.qcTask.model.QcTaskDetail;
import cec.jiutian.bc.qualityControl.domain.qcTask.model.QcTaskDetailQuota;
import cec.jiutian.bc.qualityControl.domain.qcTask.mto.InspectionItemGroupMTO;
import cec.jiutian.bc.qualityControl.domain.qcTask.mto.InspectionStandardDetailMTO;
import cec.jiutian.bc.qualityControl.domain.qcTask.mto.InspectionStandardMTO;
import cec.jiutian.bc.qualityControl.enumeration.QcSampleTypeEnum;
import cec.jiutian.bc.qualityControl.enumeration.QcTaskDetailStateEnum;
import cec.jiutian.common.util.BeanUtils;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class QcTaskDetailAddHandler implements DependFiled.DynamicHandler<QcTask>{
    @Resource
    private FabosJsonDao fabosJsonDao;
    @Resource
    private BcUtil bcUtil;
    // 样品类型-库存
    private static final String INVENTORY = QcSampleTypeEnum.Enum.INVENTORY.name();
    // 样品类型-留样
    private static final String KEEP_SAMPLE = QcSampleTypeEnum.Enum.KEEP_SAMPLE.name();
    // 样品类型-产线
    private static final String PRODUCT_LINE = QcSampleTypeEnum.Enum.PRODUCT_LINE.name();

    @Override
    public Map<String, Object> handle(QcTask qcTask) {
        HashMap<String, Object> resultMap = new HashMap<>();
        List<QcTaskDetail> qcTaskDetailList = new ArrayList<>();
        // 针对于添加明细时，选择检测组 反显送检点做的改动
        if (CollectionUtils.isEmpty(qcTask.getQcTaskDetails())) {
            // 针对于初次添加质控任务明细的情况
            boolean isSuccess = this.addQcTaskDetailFirst(qcTask, qcTaskDetailList);
            if (!isSuccess) {
                return emptyMap();
            }
        } else {
            this.addQcTaskDetailMore(qcTask, qcTaskDetailList);
        }
        resultMap.put("qcTaskDetails", qcTaskDetailList);
        return resultMap;
    }

    /**
     * 初次添加质控任务明显
     * <AUTHOR>
     * @date 2025/6/3 14:31
     * @param qcTask
     */
    private boolean addQcTaskDetailFirst(QcTask qcTask, List<QcTaskDetail> qcTaskDetailList) {
        InspectionStandardMTO standardMTO = qcTask.getInspectionStandardMTO();
        // 若没有质检标准数据, 直接返回空
        if (standardMTO == null) {
            return false;
        }
        InspectionStandardMTO standardDb = fabosJsonDao.findById(InspectionStandardMTO.class, standardMTO.getId());
        List<InspectionStandardDetailMTO> details = standardDb.getDetails();
        if (CollectionUtils.isEmpty(details)) {
            return false;
        }
        // 获取样本量map
        Map<String, Double> sampleSizeMap = null;
        if (StringUtils.isNotBlank(qcTask.getSampleType())) {
            sampleSizeMap = this.getSampleSizeMap(sampleSizeMap, standardDb, qcTask);
        }
        // 将质检标准明细中的检测项数据装载到 qcTaskDetail中
        for (InspectionStandardDetailMTO d : details) {
            InspectionItem inspectionItem = d.getInspectionItem();
            QcTaskDetail qcTaskDetail = new QcTaskDetail();
            BeanUtils.copyProperties(inspectionItem, qcTaskDetail);
            // 检测项目id、编号、检测项目名称
            qcTaskDetail.setInspectionItemId(inspectionItem.getId());
            qcTaskDetail.setInspectionItemCode(inspectionItem.getGeneralCode());
            qcTaskDetail.setInspectionItemName(inspectionItem.getName());
            // 特性
            qcTaskDetail.setFeature(inspectionItem.getFeature());
            qcTaskDetail.setId(null);
            // 装载检测方法步骤描述
            InspectionMethod method = this.getInspectionMethod(inspectionItem);
            qcTaskDetail.setInspectStepDescription(method.getInspectStepDescription());
            // 试验设备、试验设备编码
            this.loadDeviceInfo(inspectionItem.getGeneralCode(), qcTaskDetail);
            // 设置是否为引用数据
            qcTaskDetail.setIsReferData("1");
            // 装载检测项指标列表
            List<QcTaskDetailQuota> qcTaskDetailQuotaList = this.loadDetailQuota(inspectionItem);
            qcTaskDetail.setQcTaskDetailQuotas(qcTaskDetailQuotaList);
            // 设置子单状态为待检测
            qcTaskDetail.setState(QcTaskDetailStateEnum.Enum.UNDER_INSPECTION.name());
            // 设置样本量
            if (qcTaskDetail.getSampleQuantity() == null) {
                String generalCode = inspectionItem.getGeneralCode();
                if (sampleSizeMap != null && sampleSizeMap.containsKey(generalCode)) {
                    qcTaskDetail.setSampleQuantity(sampleSizeMap.get(generalCode));
                } else
                    qcTaskDetail.setSampleQuantity(null);
            }
            // 设置检测组
            this.setGroup(d, qcTaskDetail);
            qcTaskDetailList.add(qcTaskDetail);
        }
        return true;
    }

    /**
     * 非初次添加质控任务明显： 即添加其他明细数据时
     * <AUTHOR>
     * @date 2025/6/3 14:38
     * @param qcTask
     * @param qcTaskDetailList
     */
    private void addQcTaskDetailMore(QcTask qcTask, List<QcTaskDetail> qcTaskDetailList) {
        InspectionStandardMTO standardMTO = qcTask.getInspectionStandardMTO();
        // 若没有质检标准数据, 直接返回空
        if (standardMTO == null) {
            return;
        }
        InspectionStandardMTO standardDb = fabosJsonDao.findById(InspectionStandardMTO.class, standardMTO.getId());
        List<InspectionStandardDetailMTO> details = standardDb.getDetails();
        if (CollectionUtils.isEmpty(details)) {
            return;
        }
        // 获取样本量map
        Map<String, Double> sampleSizeMap = null;
        if (StringUtils.isNotBlank(qcTask.getSampleType())) {
            sampleSizeMap = this.getSampleSizeMap(sampleSizeMap, standardDb, qcTask);
        }
        List<QcTaskDetail> detailList = qcTask.getQcTaskDetails();
        for (QcTaskDetail qcTaskDetail : detailList) {
            // 如果非引用数据，则根据选择的检测组进行处理
            if (!"1".equals(qcTaskDetail.getIsReferData())) {
                InspectionItemGroupMTO grp = qcTaskDetail.getInspectionItemGroupMTO();
                if (grp != null) {
                    InspectionItemGroupMTO grpDb = fabosJsonDao.getById(InspectionItemGroupMTO.class, grp.getId());
                    qcTaskDetail.setGroupId(grpDb.getId());
                    qcTaskDetail.setSamplingPoint(grpDb.getSamplePoint());
                    if (grpDb.getSendPointMTO() != null) {
                        qcTaskDetail.setSendInspectPoint(grpDb.getSendPointMTO().getName());
                    }
                }
            }
            // 设置样本量
            if (qcTaskDetail.getSampleQuantity() == null) {
                String generalCode = qcTaskDetail.getInspectionItemCode();
                if (sampleSizeMap != null && sampleSizeMap.containsKey(generalCode)) {
                    qcTaskDetail.setSampleQuantity(sampleSizeMap.get(generalCode));
                } else
                    qcTaskDetail.setSampleQuantity(null);
            }
        }
        qcTaskDetailList.addAll(detailList);
    }

    /**
     * 根据检测项目编号，设置质控任务明细上的设备信息
     * <AUTHOR>
     * @date 2025/6/13 9:20
     * @param inspectionItemCode 检测项目编号
     * @param qcTaskDetail 质控任务明细
     */
    public void loadDeviceInfo(String inspectionItemCode, QcTaskDetail qcTaskDetail) {
        ExpMethod query = new ExpMethod();
        query.setGeneralCode(inspectionItemCode);
        ExpMethod expMethod = fabosJsonDao.selectOne(query);
        if (expMethod == null) {
            throw new FabosJsonApiErrorTip("检测项目【" + inspectionItemCode + "】无对应试验方法，请在试验方法中创建！");
        } else {
            if (!"COMPLETE".equals(expMethod.getCurrentState())) {
                throw new FabosJsonApiErrorTip("试验方法【" + inspectionItemCode + "】处于未发布状态，请在【试验方法】中进行发布！");
            }
            qcTaskDetail.setTestDevice(expMethod.getEquipment().getName());
            qcTaskDetail.setTestDeviceCode(expMethod.getEquipmentCode());
        }
    }

    /**
     * 装载检测项指标列表
     * <AUTHOR>
     * @date 2025/3/27 15:41
     * @param inspectionItem 检测项
     */
    private List<QcTaskDetailQuota> loadDetailQuota(InspectionItem inspectionItem) {
        // 查询编号对应的试验方法
        ExpMethod query = new ExpMethod();
        query.setGeneralCode(inspectionItem.getGeneralCode());
        ExpMethod expMethod = fabosJsonDao.selectOne(query);
        // 如果未绑定试验方法 抛出异常
        if (expMethod == null) {
            throw new FabosJsonApiErrorTip("检测项目【" + inspectionItem.getGeneralCode() + "】无对应试验方法，请在试验方法中创建！");
        }
        List<QcTaskDetailQuota> qcTaskDetailQuotaList = new ArrayList<>();
        expMethod.getExpMethodDetails().forEach(
                detail -> {
                    QcTaskDetailQuota quota = new QcTaskDetailQuota();
                    BeanUtils.copyProperties(detail, quota);
                    quota.setId(null);
                    qcTaskDetailQuotaList.add(quota);
                }
        );
        return qcTaskDetailQuotaList;
    }

    /**
     * 获取检测方法步骤描述
     * <AUTHOR>
     * @date 2025/3/26 16:38
     * @param inspectionItem 检测项
     * @return
     */
    private InspectionMethod getInspectionMethod(InspectionItem inspectionItem) {
        String id = inspectionItem.getInspectionMethod().getId();
        InspectionMethod inspectionMethod = fabosJsonDao.getById(InspectionMethod.class, id);
        return inspectionMethod;
    }

    /**
     * 返回一个空的map
     * <AUTHOR>
     * @date 2025/3/26 16:24
     * @return
     */
    public Map<String, Object> emptyMap() {
        HashMap<String, Object> map = new HashMap<>();
        List<QcTaskDetail> qcTaskDetails = new ArrayList<>();
        map.put("qcTaskDetails", qcTaskDetails);
        return map;
    }

    /**
     * 设置检测组数据 包括取样点、送检点
     * <AUTHOR>
     * @date 2025/3/31 10:49
     * @param stdDetail 质检标准明细
     * @param qcTaskDetail 质控任务明细
     */
    public void setGroup (InspectionStandardDetailMTO stdDetail, QcTaskDetail qcTaskDetail) {
        // 选择质检标准明细下绑定的检测组
        InspectionItemGroupMTO group = stdDetail.getInspectionItemGroup();
        if (group == null) {
            return;
        }
        InspectionItemGroupMTO groupMTO = fabosJsonDao.getById(InspectionItemGroupMTO.class, group.getId());
        if (groupMTO != null) {
            qcTaskDetail.setInspectionItemGroupMTO(groupMTO);
            qcTaskDetail.setGroupId(groupMTO.getId());
            qcTaskDetail.setSamplingPoint(groupMTO.getSamplePoint());
            if (groupMTO.getSendPointMTO() != null) {
                qcTaskDetail.setSendInspectPoint(groupMTO.getSendPointMTO().getName());
            }
        }
    }

    public Map<String, Double> getSampleSizeMap(Map<String, Double> sampleSizeMap, InspectionStandardMTO std,
                                                QcTask qcTask) {
        // 样本类型
        String sampleType = qcTask.getSampleType();
        // 库存类型
        if (INVENTORY.equals(sampleType)) {
            if (qcTask.getWmsInventoryMTO() != null) {
                // 查询库存数据
                WmsInventoryMTO inventoryMTODb = fabosJsonDao.getById(WmsInventoryMTO.class,
                        qcTask.getWmsInventoryMTO().getId());
                // 计算根据工具类计算样本量（带入可用批次数量）
                sampleSizeMap = bcUtil.getSampleSize(std, inventoryMTODb.getAvailableQuantity());
            }
        }
        // 留样类型
        if (KEEP_SAMPLE.equals(sampleType)) {
            if (qcTask.getKsInventory() != null) {
                // 获取库存数据
                KsInventory inventoryDb = fabosJsonDao.getById(KsInventory.class, qcTask.getKsInventory().getId());
                // 计算根据工具类计算样本量（带入库存数量）
                sampleSizeMap = bcUtil.getSampleSize(std, inventoryDb.getQuantity());
            }
        }
        // 产线类型则无需传入库存数据
        if (PRODUCT_LINE.equals(sampleType)) {
            sampleSizeMap = bcUtil.getSampleSize(std, 0.0);
        }
        return sampleSizeMap;
    }
}
