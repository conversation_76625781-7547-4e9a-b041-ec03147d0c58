package cec.jiutian.bc.process.domain.sample.handler;

import cec.jiutian.bc.base.domain.experimentMethod.model.ExpMethod;
import cec.jiutian.bc.base.domain.experimentMethod.mto.InspectionItemMTO;
import cec.jiutian.bc.process.domain.sample.constant.RedisKey;
import cec.jiutian.bc.process.domain.sample.model.*;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.data.redis.utils.BaseRedisCacheUtil;
import cec.jiutian.view.ReferenceAddType;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date ：2024/11/18 17:35
 * @description：
 */
@Component
public class SampleTaskFRADetailReferenceAddHandler implements ReferenceAddType.ReferenceAddHandler<SampleTaskFRAData, SampleTaskFRADetailADD> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public Map<String, Object> handle(SampleTaskFRAData sampleTask, List<SampleTaskFRADetailADD> expMethods) {
        Map<String, Object> result = new HashMap<>();
        if (CollectionUtils.isEmpty(expMethods)) {
            return result;
        }
        //获取expMethods的主键ID集合
//        List<String> detailIds = expMethods.stream()
//                .map(SampleTaskFRADetailADD::getId)
//                .toList();
//        //新增缓存  在dataproxy处用于剔除已经选中的detailId
//        BaseRedisCacheUtil.hPutForObject(RedisKey.SAMPLE_TASK_CODE_KEY+sampleTask.getGeneralCode(),sampleTask.getFractCode(),detailIds);
        List<SampleTaskFRADetail> sampleTaskDetails = expMethods.stream()
                .map(method  -> {
                    //检测指标需要单独查询一次
                    SampleTaskFRADetail detail = new SampleTaskFRADetail();
                    detail.setId(method.getId());
                    detail.setDetailId(method.getId());
                    detail.setTestMethodCode(method.getTestMethodCode());
                    detail.setTestMethodName(method.getTestMethodName());
                    detail.setSampleSize(method.getSampleSize());
                    //detail.setSamplingPoint(method.getSamplingPoint());
                    //detail.setSendInspectPoint(method.getSendInspectPoint());
                    detail.setTestDevice(method.getTestDevice());
                    detail.setInspectStepDescription(method.getInspectStepDescription());
                    SampleTaskFRADetailADD sampleTaskFRADetailADD = fabosJsonDao.findById(SampleTaskFRADetailADD.class, method.getId());
                    detail.setSampleTaskDetailQuotas(sampleTaskFRADetailADD.getSampleTaskDetailQuotas());
//                    ExpMethod expMethod = fabosJsonDao.findById(ExpMethod.class, method.getId());
//                    if(expMethod!=null&&CollectionUtils.isNotEmpty(expMethod.getExpMethodDetails())){
//                        detail.setSampleTaskDetailQuotas(expMethod.getExpMethodDetails().stream().map(d->{
//                            SampleTaskDetailQuota sampleTaskDetailQuota = new SampleTaskDetailQuota();
//                            sampleTaskDetailQuota.setName(d.getName());
//                            sampleTaskDetailQuota.setInspectionValueType(d.getInspectionValueType());
//                            sampleTaskDetailQuota.setUnit(d.getUnit());
//                            sampleTaskDetailQuota.setStandardValue(d.getStandardValue());
//                            sampleTaskDetailQuota.setUpperValue(d.getUpperValue());
//                            sampleTaskDetailQuota.setLowerValue(d.getLowerValue());
//                            sampleTaskDetailQuota.setIsContainUpper(d.getIsContainUpper());
//                            sampleTaskDetailQuota.setIsContainLower(d.getIsContainLower());
//                            return sampleTaskDetailQuota;
//                        }).collect(Collectors.toList())
//                        );
//                    }

                    return detail;
                })
                .collect(Collectors.toList());

        result.put("sampleTaskFRADetailList", sampleTaskDetails);
        return result;
    }


}
