package cec.jiutian.bc.qualityControl.domain.qcTask.handler;

import cec.jiutian.bc.base.domain.experimentMethod.model.ExpMethod;
import cec.jiutian.bc.base.domain.experimentMethod.model.ExpMethodDetail;
import cec.jiutian.bc.generalModeler.util.StringUtils;
import cec.jiutian.bc.modeler.domain.inspectionItem.model.InspectionItem;
import cec.jiutian.bc.modeler.domain.inspectionMethod.model.InspectionMethod;
import cec.jiutian.bc.qualityControl.domain.qcTask.model.QcTask;
import cec.jiutian.bc.qualityControl.domain.qcTask.model.QcTaskDetail;
import cec.jiutian.bc.qualityControl.domain.qcTask.model.QcTaskDetailQuota;
import cec.jiutian.bc.qualityControl.domain.qcTask.mto.InspectionItemGroupMTO;
import cec.jiutian.bc.qualityControl.domain.qcTask.mto.ItemMTO;
import cec.jiutian.bc.qualityControl.enumeration.QcTaskDetailStateEnum;
import cec.jiutian.common.util.BeanUtils;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.ReferenceAddType;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class QcTaskDetailReferAddHandler implements ReferenceAddType.ReferenceAddHandler<QcTask, ItemMTO>{
    @Resource
    private FabosJsonDao fabosJsonDao;
    @Resource
    private QcTaskDetailAddHandler qcTaskDetailAddHandler;

    @Override
    public Map<String, Object> handle(QcTask qcTask, List<ItemMTO> itemList) {
        if (itemList.isEmpty()) {
            return emptyMap();
        }
        List<QcTaskDetail> qcTaskDetailList = new ArrayList<>();
        for (ItemMTO itemMTO : itemList) {
            InspectionItem item = fabosJsonDao.getById(InspectionItem.class, itemMTO.getId());
            if (item == null) {
                continue;
            }
            QcTaskDetail qcTaskDetail = new QcTaskDetail();
            BeanUtils.copyProperties(item, qcTaskDetail);
            // 检测项目id、编号、检测项目名称
            qcTaskDetail.setInspectionItemId(item.getId());
            qcTaskDetail.setInspectionItemCode(item.getGeneralCode());
            qcTaskDetail.setInspectionItemName(item.getName());
            // 特性
            qcTaskDetail.setFeature(item.getFeature());
            qcTaskDetail.setId(null);
            // 装载检测方法步骤描述
            InspectionMethod method = this.getInspectionMethod(item);
            qcTaskDetail.setInspectStepDescription(method.getInspectStepDescription());
            // 试验设备、试验设备编码
            this.qcTaskDetailAddHandler.loadDeviceInfo(item.getGeneralCode(), qcTaskDetail);
            // 设置是否为引用数据
            qcTaskDetail.setIsReferData(null);
            // 装载检测项指标列表
            List<QcTaskDetailQuota> qcTaskDetailQuotaList = this.loadDetailQuota(item);
            qcTaskDetail.setQcTaskDetailQuotas(qcTaskDetailQuotaList);
            // 设置子单状态为待检测
            qcTaskDetail.setState(QcTaskDetailStateEnum.Enum.UNDER_INSPECTION.name());
            // 设置检测组
            this.setGroup(item, qcTaskDetail);
            qcTaskDetailList.add(qcTaskDetail);
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("qcTaskDetails", qcTaskDetailList);
        return map;
    }

    /**
     * 装载检测项指标列表
     * <AUTHOR>
     * @date 2025/3/27 15:41
     * @param item 检测项
     */
    private List<QcTaskDetailQuota> loadDetailQuota(InspectionItem item) {
        ExpMethod query = new ExpMethod();
        query.setGeneralCode(item.getGeneralCode());
        ExpMethod expMethod = fabosJsonDao.selectOne(query);
        List<ExpMethodDetail> expMethodDetailList = expMethod.getExpMethodDetails();
        if (CollectionUtils.isEmpty(expMethodDetailList)) {
            return null;
        }
        List<QcTaskDetailQuota> qcTaskDetailQuotaList = new ArrayList<>();
        expMethodDetailList.forEach(
                ed -> {
                    QcTaskDetailQuota quota = new QcTaskDetailQuota();
                    BeanUtils.copyProperties(ed, quota);
                    quota.setId(null);
                    qcTaskDetailQuotaList.add(quota);
                }
        );
        return qcTaskDetailQuotaList;
    }

    /**
     * 获取检测方法步骤描述
     * <AUTHOR>
     * @date 2025/3/26 16:38
     * @param item 检测项
     * @return
     */
    private InspectionMethod getInspectionMethod(InspectionItem item) {
        String id = item.getInspectionMethod().getId();
        InspectionMethod inspectionMethod = fabosJsonDao.getById(InspectionMethod.class, id);
        return inspectionMethod;
    }

    /**
     * 返回一个空的map
     * <AUTHOR>
     * @date 2025/3/26 16:24
     * @return
     */
    public Map<String, Object> emptyMap() {
        HashMap<String, Object> map = new HashMap<>();
        List<QcTaskDetail> qcTaskDetails = new ArrayList<>();
        map.put("qcTaskDetails", qcTaskDetails);
        return map;
    }

    /**
     * 设置检测组数据
     * <AUTHOR>
     * @date 2025/3/31 10:49
     * @param item
     * @param qcTaskDetail
     */
    public void setGroup (InspectionItem item, QcTaskDetail qcTaskDetail) {
        // qms所属检测组: 质检标准和检测组必然是一对一
        String groupId = item.getGroupId();
        if (StringUtils.isBlank(groupId)) {
            return;
        }
        InspectionItemGroupMTO groupMTO = fabosJsonDao.getById(InspectionItemGroupMTO.class, groupId);
        if (groupMTO != null) {
            qcTaskDetail.setInspectionItemGroupMTO(groupMTO);
            qcTaskDetail.setGroupId(groupMTO.getId());
        }
    }
}
