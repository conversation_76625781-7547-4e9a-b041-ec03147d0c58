package cec.jiutian.bc.process.domain.inspectionTask.model;

import cec.jiutian.bc.process.domain.inspectionTask.handler.DetailQuotasAddHandler;
import cec.jiutian.bc.process.enumeration.InsTaskReportTypeEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.*;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.AttachmentType;
import cec.jiutian.view.field.edit.ChoiceType;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@FabosJson(name = "结果解析")
@Entity
@Getter
@Setter
@Table(name = "lm_inspection_result_report")
public class ReportResult extends MetaModel {
    @FabosJsonField(
            views = @View(title = "委托申请单号", show = false),
            edit = @Edit(title = "委托申请单号", show = false)
    )
    private String generalCode;
    @FabosJsonField(
            views = @View(title = "样本条码号"),
            edit = @Edit(title = "样本条码号",readonly = @Readonly)
    )
    private String sampleTaskNo;
    //类型
    @FabosJsonField(
            views = @View(title = "文件类型"),
            edit = @Edit(title = "文件类型", type = EditType.CHOICE,notNull = true,
                    choiceType = @ChoiceType(fetchHandler = InsTaskReportTypeEnum.class))
    )
    private String type;


    @FabosJsonField(
            views = @View(title = "解析文件"),
            edit = @Edit(title = "解析文件", type = EditType.ATTACHMENT, attachmentType = @AttachmentType(size = 5120, maxLimit = 1, type = AttachmentType.Type.BASE))
    )
    private String originalTestFile;

}
