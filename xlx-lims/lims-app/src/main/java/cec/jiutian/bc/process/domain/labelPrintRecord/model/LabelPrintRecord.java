package cec.jiutian.bc.process.domain.labelPrintRecord.model;

import cec.jiutian.bc.base.domain.lab.proxy.LabDataProxy;
import cec.jiutian.bc.process.domain.labelPrintRecord.proxy.LabelPrintRecordProxy;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.DateType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;

import java.util.Date;

/**
 * <AUTHOR>
 * @description:   fd_print_history的MTO
 */
@Table(name = "fd_print_history")
@Entity
@Setter
@Getter
@FabosJson(name = "标签打印记录"
        , power = @Power(edit = false, add = false, delete = false, importable = false, examine = false, examineDetails = false, export = false, print = false, query = true)
        , orderBy = "printTime desc",filter = @Filter("modelCode in ('InspectionTask', 'LmReportTemplate')"),
        dataProxy = LabelPrintRecordProxy.class
)
public class LabelPrintRecord extends MetaModel {
    @FabosJsonField(
            views = @View(title = "测试单号", toolTip = true),
            edit = @Edit(title = "测试单号",search = @Search(vague = true), readonly = @Readonly(add = false))
    )
    private String generalCode;
    @CreatedBy
    @FabosJsonField(
            edit = @Edit(title = "打印用户", readonly = @Readonly(add = false),search = @Search(vague = true)),
            views = @View(title = "打印用户")
    )
    private String printBy;

    @CreatedDate
    @FabosJsonField(
            edit = @Edit(title = "打印时间", readonly = @Readonly(add = false), search = @Search(vague = true),type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE_TIME),
                    inputType = @InputType(length = 40)),
            views = @View(title = "打印时间")
    )
    private Date printTime;
    @FabosJsonField(
            edit = @Edit(title = "打印次数"),
            views = @View(title = "打印次数")
    )
    @Transient
    //不存数据库
    private String printNumStr;
    @FabosJsonField(
            views = @View(title = "打印原因", toolTip = true),
            edit = @Edit(title = "打印原因", readonly = @Readonly())
    )
    private String printComment;
    @FabosJsonField(
            views = @View(title = "打印数据", toolTip = true),
            edit = @Edit(title = "打印数据", readonly = @Readonly(add = false),
                    type = EditType.TEXTAREA)
    )
    @Column(columnDefinition = "TEXT")
    private String printData;
    @FabosJsonField(
            views = @View(title = "模型编码", toolTip = true,show = false),
            edit = @Edit(title = "模型编码",readonly = @Readonly(add = false),show = false)
    )
    private String modelCode;
}
