package cec.jiutian.bc.lineEdge.domain.stocktakingOrder.model;

import cec.jiutian.bc.base.domain.materialCategory.model.LimsMaterialCategory;
import cec.jiutian.bc.base.domain.warehouse.model.LimsWarehouse;
import cec.jiutian.bc.base.enumeration.LmNamingRuleCodeEnum;
import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleBaseModel;
import cec.jiutian.bc.keepSample.domain.stocktakingPlan.model.KsStocktakingPlan;
import cec.jiutian.bc.keepSample.enumeration.StocktakingOrderStateEnum;
import cec.jiutian.bc.lineEdge.domain.stocktakingOrder.handler.LineStocktakingOrderDetailDynamicHandler;
import cec.jiutian.bc.lineEdge.domain.stocktakingOrder.handler.LineStocktakingOrderDynamicHandler;
import cec.jiutian.bc.lineEdge.domain.stocktakingOrder.proxy.LineStocktakingOrderRunDataProxy;
import cec.jiutian.bc.lineEdge.domain.stocktakingPlan.model.LineStocktakingPlan;
import cec.jiutian.bc.person.domain.personManage.model.PersonManage;
import cec.jiutian.bc.process.enumeration.SampleTypeEnum;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.*;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowBaseOperation;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 */
@Entity
@Table(name = "lm_line_stocktaking_order",uniqueConstraints = {
        @UniqueConstraint(columnNames = {"generalCode"})
})
@Getter
@Setter
@FabosJson(name = "盘点任务",dataProxy = LineStocktakingOrderRunDataProxy.class,
        orderBy = "LineStocktakingOrderRun.createTime desc",power = @Power(export = false,importable = false),
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "currentState != 'OPEN'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "currentState != 'OPEN'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                )
        }
)
@FabosJsonI18n
public class LineStocktakingOrderRun extends NamingRuleBaseModel {
    //盘点计划
    @ManyToOne
    @FabosJsonField(
            views = @View(title = "盘点计划", column = "generalCode"),
            edit = @Edit(title = "盘点计划",
                    type = EditType.REFERENCE_TABLE,
                    filter = @Filter("stocktakingPlanState = 'EFFECT'"),
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode"),
                    search = @Search(),readonly = @Readonly)
    )
    private LineStocktakingPlan ksStocktakingPlan;
    //盘点范围
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "mto_id",foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @FabosJsonField(
            views = @View(title = "盘点范围", column = "name"),
            edit = @Edit(title = "盘点范围",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(type = ReferenceTableType.SelectShowTypeMTO.LIST),
                    allowAddMultipleRows = false,
                    search = @Search(),readonly = @Readonly
            ),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "ksStocktakingPlan", dynamicHandler = LineStocktakingOrderDynamicHandler.class))

    )
    private LimsMaterialCategory stocktakingRange;
    //盘点区域
    @ManyToOne
    @FabosJsonField(
            views = @View(title = "盘点区域", column = "name"),
            edit = @Edit(title = "盘点区域",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    search = @Search())
    )
    private LimsWarehouse limsWarehouse;

    //计划开始时间
    @FabosJsonField(
            views = @View(title = "开始时间"),
            edit = @Edit(title = "开始时间",  type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE),
                    inputType = @InputType(length = 40),readonly = @Readonly)
    )
    private Date stocktakingStartDate;
    //计划结束时间
    @FabosJsonField(
            views = @View(title = "截止时间"),
            edit = @Edit(title = "截止时间",  type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE),
                    inputType = @InputType(length = 40),readonly = @Readonly)
    )
    private Date stocktakingEndDate;
    //盘点人员
    @ManyToOne
    @FabosJsonField(
            views = @View(title = "盘点人员", column = "name"),
            edit = @Edit(title = "盘点人员",
                    type = EditType.REFERENCE_TABLE,
                    filter = @Filter("PersonManage.personState = 'AVAILABLE'"),
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    search = @Search(),readonly = @Readonly)
    )
    private PersonManage stocktakingPerson;
    //审核人员
    @ManyToOne
    @FabosJsonField(
            views = @View(title = "审核人员", column = "name"),
            edit = @Edit(title = "审核人员",
                    type = EditType.REFERENCE_TABLE,
                    filter = @Filter("PersonManage.personState = 'AVAILABLE'"),
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    search = @Search(),readonly = @Readonly)
    )
    private PersonManage stocktakingReviewPerson;

    //计划状态
    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",show = false,search = @Search(),type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = StocktakingOrderStateEnum.class),readonly = @Readonly)
    )
    private String currentState;


    @Override
    public String getNamingCode() {
        return LmNamingRuleCodeEnum.LM_LINE_STOCKTAKING_ORDER.name();
    }

    @OneToMany(cascade = CascadeType.ALL)
    @FabosJsonField(
            views = @View(title = "明细", type = ViewType.TABLE_VIEW, index = 8),
            edit = @Edit(title = "明细", type = EditType.TAB_REFERENCE_GENERATE),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = {"stocktakingRange","ksStocktakingPlan","limsWarehouse"}, dynamicHandler = LineStocktakingOrderDetailDynamicHandler.class)),
            referenceGenerateType = @ReferenceGenerateType(editable = "stocktakingQuantity")
    )
    @JoinColumn(name = "stocktaking_order_id")
    private List<LineStocktakingOrderDetailRun> stocktakingOrderDetails;

}
