package cec.jiutian.bc.qualityControl.domain.qcTask.model;

import cec.jiutian.bc.modeler.enumration.ItemTypeEnum;
import cec.jiutian.bc.qualityControl.domain.qcTask.handler.LmItemCodeGenerateDynamicHandler;
import cec.jiutian.bc.qualityControl.domain.qcTask.mto.InspectionItemGroupMTO;
import cec.jiutian.bc.qualityControl.enumeration.QcTaskDetailResultEnum;
import cec.jiutian.bc.qualityControl.enumeration.QcTaskDetailStateEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.FabosJsonI18n;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DependFieldDisplay;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.RowBaseOperation;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@FabosJson(
        name = "质控任务明细",
        orderBy = "QcTaskDetail.createTime desc",
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "isReferData == '1'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                )
        }
)
@Entity
@Getter
@Setter
@Table(name = "lm_quantity_task_detail")
@FabosJsonI18n
public class QcTaskDetail extends MetaModel {
    @FabosJsonField(
            views = @View(title = "检测项目id", show = false),
            edit = @Edit(title = "检测项目id", show = false)
    )
    private String inspectionItemId;

    @FabosJsonField(
            views = @View(title = "检测项目编号", width = "210px"),
            edit = @Edit(title = "检测项目编号", notNull = true, search = @Search(vague = true)),
            dynamicField = @DynamicField(dependFiled = @DependFiled(buttonName = "生成", changeBy = "id",
                    dynamicHandler = LmItemCodeGenerateDynamicHandler.class))
    )
    private String inspectionItemCode;

    @FabosJsonField(
            views = @View(title = "检测项目名称"),
            edit = @Edit(title = "检测项目名称", notNull = true, search = @Search(vague = true))
    )
    private String inspectionItemName;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "检测组", column = "groupName"),
            edit = @Edit(title = "检测组",
                    allowAddMultipleRows = false,
                    type = EditType.REFERENCE_TABLE,
                    filter = @Filter("InspectionItemGroupMTO.status = 'ACTIVE'"),
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "groupName"),
                    dependFieldDisplay = @DependFieldDisplay(enableOrDisable = "isReferData == '1'")
            )
    )
    private InspectionItemGroupMTO inspectionItemGroupMTO;

    @FabosJsonField(
            views = @View(title = "所属检测组id", show = false),
            edit = @Edit(title = "所属检测组id", show = false)
    )
    private String groupId;

    // 数据来源于检测项目
    @FabosJsonField(
            views = @View(title = "特性", toolTip = true),
            edit = @Edit(title = "特性", search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    private String feature;

    // 数据来源于检测项目
    @FabosJsonField(
            views = @View(title = "取样点"),
            edit = @Edit(title = "取样点",notNull = true,
                    inputType = @InputType(length = 40))
    )
    private String samplingPoint;

    // 数据来源于检测项目
    @FabosJsonField(
            views = @View(title = "送检点"),
            edit = @Edit(title = "送检点",
                    inputType = @InputType(length = 40))
    )
    private String sendInspectPoint;

    @FabosJsonField(
            views = @View(title = "样本数量"),
            edit = @Edit(title = "样本数量",
                    numberType = @NumberType(min = 0, max = 9999999, precision = 2))
    )
    private Double sampleQuantity;

    @FabosJsonField(
            views = @View(title = "检测项类型"),
            edit = @Edit(title = "检测项类型",type = EditType.CHOICE,notNull = true,
                    inputType = @InputType(length = 40),
                    choiceType = @ChoiceType(fetchHandler = ItemTypeEnum.class))
    )
    private String itemType;

    // 试验设备名称
    @FabosJsonField(
            views = @View(title = "检测设备"),
            edit = @Edit(title = "检测设备")
    )
    private String testDevice;

    // 试验设备编码
    @FabosJsonField(
            views = @View(title = "检测设备编码", show = false),
            edit = @Edit(title = "检测设备编码", show = false)
    )
    private String testDeviceCode;

    @FabosJsonField(
            views = @View(title = "方法步骤描述", toolTip = true),
            edit = @Edit(title = "方法步骤描述",
                    type = EditType.TEXTAREA)
    )
    private String inspectStepDescription;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态", show = false, search = @Search(), type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = QcTaskDetailStateEnum.class))
    )
    private String state;

    @FabosJsonField(
            views = @View(title = "检测结论"),
            edit = @Edit(title = "检测结论", show = false, type = EditType.CHOICE, search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = QcTaskDetailResultEnum.class))
    )
    private String inspectResult;

    @FabosJsonField(
            views = @View(title = "委托申请单号"),
            edit = @Edit(title = "委托申请单号", readonly = @Readonly, show = false)
    )
    private String inspectionTaskNo;

    // 关联主表, 不展示
    @FabosJsonField(
            views = @View(title = "质控任务", show = false,column = "generalCode"),
            edit = @Edit(title = "质控任务", show = false,type = EditType.REFERENCE_TABLE, referenceTableType = @ReferenceTableType(label = "generalCode"))
    )
    @ManyToOne
    @JsonIgnoreProperties("qcTaskDetails")
    private QcTask qcTask;

    // 用于设置引用数据是否能编辑, 不展示   默认 null 不是; 1 是
    @FabosJsonField(
            views = @View(title = "是否为引用数据", show = false),
            edit = @Edit(title = "是否为引用数据", show = false)
    )
    private String isReferData;

    @FabosJsonField(
            views = @View(title = "检测指标", column = "name", type= ViewType.TABLE_VIEW),
            edit = @Edit(title = "检测指标", type = EditType.TAB_TABLE_ADD)
    )
    @JoinColumn(name = "qc_task_detail_id")
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    private List<QcTaskDetailQuota> qcTaskDetailQuotas;
}
