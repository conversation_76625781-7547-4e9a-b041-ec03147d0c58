package cec.jiutian.bc.base.domain.experimentMethod.mto;

import cec.jiutian.bc.base.domain.experimentMethod.proxy.InspectionItemMTOProxy;
import cec.jiutian.bc.modeler.domain.inspectionItem.handler.ItemCodeGenerateDynamicHandler;
import cec.jiutian.bc.modeler.domain.inspectionItem.model.InspectionItemTarget;
import cec.jiutian.bc.modeler.domain.inspectionMethod.model.InspectionMethod;
import cec.jiutian.bc.modeler.domain.samplingPlan.model.SamplingPlan;
import cec.jiutian.bc.modeler.enumration.ItemTypeEnum;
import cec.jiutian.bc.modeler.enumration.PackageTypeEnum;
import cec.jiutian.bc.modeler.enumration.StatusEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.InputGroup;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import jakarta.persistence.CascadeType;
import jakarta.persistence.ConstraintMode;
import jakarta.persistence.Entity;
import jakarta.persistence.ForeignKey;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@FabosJson(
        name = "检测项目MTO",
        dataProxy = InspectionItemMTOProxy.class
)
@Table(name = "bd_inspection_item")
@Entity
@Getter
@Setter
public class InspectionItemMTO extends MetaModel {

    @FabosJsonField(
            views = @View(title = "编号"),
            edit = @Edit(title = "编号", notNull = true, search = @Search(vague = true)),
            dynamicField = @DynamicField(dependFiled = @DependFiled(buttonName = "生成", changeBy = "id",
                    dynamicHandler = ItemCodeGenerateDynamicHandler.class))
    )
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "检测项目名称"),
            edit = @Edit(title = "检测项目名称", search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    private String name;

    @FabosJsonField(
            views = @View(title = "特性"),
            edit = @Edit(title = "特性", search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    private String feature;
    @FabosJsonField(
            views = @View(title = "检测项类型"),
            edit = @Edit(title = "检测项类型",type = EditType.CHOICE,
                    inputType = @InputType(length = 40),
                    choiceType = @ChoiceType(fetchHandler = ItemTypeEnum.class))
    )
    private String itemType;

    @ManyToOne
    @JoinColumn(foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT, name = "none"))
    @FabosJsonField(
            views = @View(title = "检测方法", column = "name", type = ViewType.TABLE_FORM),
            edit = @Edit(title = "检测方法",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    search = @Search()
            )
    )
    private InspectionMethod inspectionMethod;
    // TODO: 2025/3/4 管理方案

    @FabosJsonField(
            views = @View(title = "是否自动判断"),
            edit = @Edit(title = "是否自动判断", defaultVal = "false"
            )
    )
    private Boolean isAutoJudged;

    @FabosJsonField(
            views = @View(title = "包装方式"),
            edit = @Edit(title = "包装方式", type = EditType.CHOICE,search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = PackageTypeEnum.class))
    )
    private String packageType;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "抽样方案", column = "name", type = ViewType.TABLE_FORM),
            edit = @Edit(title = "抽样方案",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    search = @Search()
            )
    )
    private SamplingPlan samplingPlan;

    @FabosJsonField(
            views = @View(title = "样本量"),
            edit = @Edit(title = "样本量",notNull = true,
                    numberType = @NumberType(min=0,max = 9999999,precision = 2))
    )
    private Double sampleSize;

    @FabosJsonField(
            views = @View(title = "检验频次"),
            edit = @Edit(title = "检验频次", notNull = true,
                    inputGroup = @InputGroup(postfix = "次"), numberType = @NumberType(min = 0))
    )
    private Integer inspectFrequency;

    @FabosJsonField(
            views = @View(title = "是否开线首检"),
            edit = @Edit(title = "是否开线首检", defaultVal = "false"
            )
    )
    private Boolean isOpenLine;

    @FabosJsonField(
            views = @View(title = "是否开机首检"),
            edit = @Edit(title = "是否开机首检", defaultVal = "false"
            )
    )
    private Boolean isOpenDevice;

    @FabosJsonField(
            views = @View(title = "是否原材料首检"),
            edit = @Edit(title = "是否原材料首检", defaultVal = "false"
            )
    )
    private Boolean isRawMaterial;

    @FabosJsonField(
            views = @View(title = "取样点"),
            edit = @Edit(title = "取样点",
                    inputType = @InputType(length = 40))
    )
    private String samplingPoint;

    @FabosJsonField(
            views = @View(title = "送检点"),
            edit = @Edit(title = "送检点",
                    inputType = @InputType(length = 40))
    )
    private String sendInspectPoint;

    @FabosJsonField(
            views = @View(title = "结果等待"),
            edit = @Edit(title = "结果等待",
                    inputType = @InputType(length = 40))
    )
    private String resultWait;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态", type = EditType.CHOICE,search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = StatusEnum.class))
    )
    private String status;

    @FabosJsonField(
            views = @View(title = "备注"),
            edit = @Edit(title = "备注",
                    type = EditType.TEXTAREA)
    )
    private String remark;

    @FabosJsonField(
            views = @View(title = "所属检测组",show = false),
            edit = @Edit(title = "所属检测组",show = false)
    )
    private String groupId;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = false)
    @JoinColumn(name = "inspection_item_id")
    @FabosJsonField(
            views = @View(title = "检测项指标", type= ViewType.TABLE_VIEW),
            edit = @Edit(title = "检测项指标", type = EditType.TAB_TABLE_ADD)
    )
    private List<InspectionItemTarget> inspectionItemTargetList;
}
