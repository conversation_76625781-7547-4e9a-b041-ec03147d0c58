package cec.jiutian.bc.instrumentManagement.domain.instrumentInventory.handler;

import cec.jiutian.bc.instrumentManagement.domain.instrumentInventory.model.LimsInstrumentInventory;
import cec.jiutian.bc.mto.InventoryMTO;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;


@Component
public class LimsMaterialInfoDynamicHandler implements DependFiled.DynamicHandler<LimsInstrumentInventory> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public Map<String, Object> handle(LimsInstrumentInventory limsInstrumentInventory) {
        Map<String, Object> result = new HashMap<>();
        if(limsInstrumentInventory.getStockOutDetail()==null){
            return result;
        }
        String inventoryId = limsInstrumentInventory.getStockOutDetail().getInventoryId();
        InventoryMTO inventoryMTO = fabosJsonDao.findById(InventoryMTO.class, inventoryId);
        if (inventoryMTO == null) {
            throw new ServiceException("库存台账不存在");
        }
        String supplierId = inventoryMTO.getSupplierId();
        String supplierName = inventoryMTO.getSupplierName();
        String inventoryLotId = inventoryMTO.getInventoryLotId();
        result.put("supplierId",supplierId);
        result.put("supplierName",supplierName);
        result.put("inventoryCode",inventoryLotId);
        return result;
    }
}
