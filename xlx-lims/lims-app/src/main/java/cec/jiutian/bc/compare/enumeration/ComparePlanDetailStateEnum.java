package cec.jiutian.bc.compare.enumeration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 对标计划明细（检测任务）状态枚举
 * <AUTHOR>
 * @date 2025/4/28 15:12
 */
public class ComparePlanDetailStateEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum value : Enum.values()) {
            list.add(new VLModel(value.name(), value.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        TAKING_SAMPLE("取样中"),
        INSPECTING("检测中"),
        CLOSED("已关闭"),
        COMPLETE("已完成");

        private final String value;

    }


}
