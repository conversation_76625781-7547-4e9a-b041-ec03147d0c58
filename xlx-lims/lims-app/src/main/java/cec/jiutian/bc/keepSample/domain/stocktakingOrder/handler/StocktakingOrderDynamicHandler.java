package cec.jiutian.bc.keepSample.domain.stocktakingOrder.handler;

import cec.jiutian.bc.keepSample.domain.stocktakingOrder.model.StocktakingOrder;
import cec.jiutian.bc.person.domain.personManage.model.PersonManage;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：2024/11/14 18:13
 * @description：
 */
@Component
public class StocktakingOrderDynamicHandler implements DependFiled.DynamicHandler<StocktakingOrder> {

    @Resource
    private FabosJsonDao fabosJsonDao;
    @Override
    public Map<String, Object> handle(StocktakingOrder stocktakingOrder) {
        Map<String, Object> result = new HashMap<>();
        if(stocktakingOrder.getKsStocktakingPlan()==null){
            return result;
        }
        result.put("stocktakingRange",stocktakingOrder.getKsStocktakingPlan().getStocktakingRange());
        if(stocktakingOrder.getKsStocktakingPlan().getStocktakingPerson()!=null){
            PersonManage personManage = fabosJsonDao.findById(PersonManage.class, stocktakingOrder.getKsStocktakingPlan().getStocktakingPerson().getId());
            result.put("stocktakingPerson", personManage);
        }
        if(stocktakingOrder.getKsStocktakingPlan().getStocktakingReviewPerson()!=null){
            PersonManage personManage = fabosJsonDao.findById(PersonManage.class, stocktakingOrder.getKsStocktakingPlan().getStocktakingReviewPerson().getId());
            result.put("stocktakingReviewPerson", personManage);
        }
        return result;
    }
}
