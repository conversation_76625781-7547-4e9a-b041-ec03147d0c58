package cec.jiutian.bc.qualityControl.domain.qcTask.model;

import cec.jiutian.bc.base.enumeration.LmNamingRuleCodeEnum;
import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleBaseModel;
import cec.jiutian.bc.keepSample.domain.inventory.model.KsInventory;
import cec.jiutian.bc.mto.FactoryArea;
import cec.jiutian.bc.mto.ProductProcess;
import cec.jiutian.bc.person.domain.personManage.model.PersonManage;
import cec.jiutian.bc.process.domain.sample.mto.WmsInventoryMTO;
import cec.jiutian.bc.process.enumeration.UrgentEnum;
import cec.jiutian.bc.qualityControl.domain.qcPlan.model.QcPlan;
import cec.jiutian.bc.qualityControl.domain.qcTask.handler.InsStdDynamicHandler;
import cec.jiutian.bc.qualityControl.domain.qcTask.handler.QcTaskDetailAddHandler;
import cec.jiutian.bc.qualityControl.domain.qcTask.handler.QcTaskDetailReferAddHandler;
import cec.jiutian.bc.qualityControl.domain.qcTask.handler.QcTaskDynamicHandler;
import cec.jiutian.bc.qualityControl.domain.qcTask.handler.QcTaskInvalidStateHandler;
import cec.jiutian.bc.qualityControl.domain.qcTask.handler.QcTaskResultSubmissionHandler;
import cec.jiutian.bc.qualityControl.domain.qcTask.handler.QcTaskUpdateStateHandler;
import cec.jiutian.bc.qualityControl.domain.qcTask.mto.InspectionStandardMTO;
import cec.jiutian.bc.qualityControl.domain.qcTask.proxy.QcTaskDataProxy;
import cec.jiutian.bc.qualityControl.enumeration.QcMethodEnum;
import cec.jiutian.bc.qualityControl.enumeration.QcSampleTypeEnum;
import cec.jiutian.bc.qualityControl.enumeration.QcStateEnum;
import cec.jiutian.bc.qualityControl.enumeration.QcTaskStateEnum;
import cec.jiutian.core.frame.constant.YesOrNoStatus;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.FabosJsonI18n;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.ReferenceAddType;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DateType;
import cec.jiutian.view.field.edit.DependFieldDisplay;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowBaseOperation;
import cec.jiutian.view.type.RowOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;


@FabosJson(name = "质控任务",
        orderBy = "QcTask.createTime desc",
        power = @Power(export = false, print = false),
        dataProxy = QcTaskDataProxy.class,
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "currentState != 'UNDER_RELEASED'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "currentState != 'DEPRECATED'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                )
        },
        rowOperation = {
                @RowOperation(
                        title = "发布",
                        code = "QcTask@PUBLISH",
                        operationHandler = QcTaskUpdateStateHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "是否确定操作？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "QcTask@PUBLISH"
                        ),
                        ifExpr = "currentState != 'UNDER_RELEASED'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "作废",
                        code = "QcTask@INVALID",
                        operationHandler = QcTaskInvalidStateHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "是否确定操作？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "QcTask@INVALID"
                        ),
                        ifExpr = "currentState != 'UNDER_RELEASED'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "结果提交",
                        code = "QcTask@RESULTSUBMISSION",
                        operationHandler = QcTaskResultSubmissionHandler.class,
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = QcTaskResultSubmission.class,
                        show = @ExprBool(
                                value = true,
                                params = "QcTask@RESULTSUBMISSION",
                                exprHandler = UserRowOperationExprHandler.class
                        ),
                        ifExpr = "selectedItems[0].submissionFlag != '1'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                )
        }
)
@Entity
@Getter
@Setter
@Table(name = "lm_quantity_task")
@FabosJsonI18n
@TemplateType(type = "multiTable")
public class QcTask extends NamingRuleBaseModel {
    @Override
    public String getNamingCode() {
        return LmNamingRuleCodeEnum.LM_QC_TASK.name();
    }

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "质控计划", column = "generalCode"),
            edit = @Edit(title = "质控计划",
                    type = EditType.REFERENCE_TABLE,
                    filter = @Filter("QcPlan.currentStatus = 'EFFECTIVE'"),
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode"),
                    search = @Search(vague = true))
    )
    private QcPlan qcPlan;

    @FabosJsonField(
            views = @View(title = "质控项目编号"),
            edit = @Edit(title = "质控项目编号", show = false, search = @Search(vague = true), readonly = @Readonly),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "qcPlan",
                    dynamicHandler = QcTaskDynamicHandler.class))
    )
    private String qcProjectCode;

    // 数据来源于质控计划
    @FabosJsonField(
            views = @View(title = "质控项目名称"),
            edit = @Edit(title = "质控项目名称", show = false, search = @Search(vague = true), readonly = @Readonly),
            dynamicField = @DynamicField(passive = true)
    )
    private String qcProjectName;

    // 数据来源于质控计划
    @FabosJsonField(
            views = @View(title = "质控方式"),
            edit = @Edit(title = "质控方式", show = false, type = EditType.CHOICE, search = @Search(vague = true),
                    choiceType = @ChoiceType(fetchHandler = QcMethodEnum.class), readonly = @Readonly),
            dynamicField = @DynamicField(passive = true)
    )
    private String qcMethod;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "质检标准", column = "generalCode"),
            edit = @Edit(title = "质检标准",
                    type = EditType.REFERENCE_TABLE,
                    // 选择Lims类型的质检标准
                    queryCondition = "{\"type\":\"LIMS\", \"status\":\"Effective\"}",
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode"),
                    search = @Search())
    )
    private InspectionStandardMTO inspectionStandardMTO;

    @FabosJsonField(
            views = @View(title = "是否取样"),
            edit = @Edit(title = "是否取样", notNull = true, readonly = @Readonly, search = @Search(),
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = YesOrNoStatus.ChoiceFetch.class)),
            dynamicField = @DynamicField(passive = true)
    )
    private String takeSample;

    @FabosJsonField(
            views = @View(title = "样品类型"),
            edit = @Edit(title = "样品类型", notNull = true, type = EditType.CHOICE, search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = QcSampleTypeEnum.class),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "takeSample == 'Y'"))
    )
    private String sampleType;

    // 来源于qms的质检标准主单
    @FabosJsonField(
            views = @View(title = "产品编码"),
            edit = @Edit(title = "产品编码", readonly = @Readonly),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "inspectionStandardMTO",
                    dynamicHandler = InsStdDynamicHandler.class))
    )
    private String materialCode;

    @FabosJsonField(
            views = @View(title = "产品名称"),
            edit = @Edit(title = "产品名称", readonly = @Readonly, search = @Search(vague = true)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "wmsInventoryMTO",
                    beFilledBy = "materialName"))
    )
    private String materialName;

    @FabosJsonField(
            views = @View(title = "样品来源"),
            edit = @Edit(title = "样品来源", show = false)
    )
    private String sampleSource;

    @Transient
    @FabosJsonField(
            views = @View(title = "车间", show = false, column = "factoryAreaName"),
            edit = @Edit(title = "车间",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "factoryAreaName"),
                    filter = @Filter(value = "factoryAreaTypeCode = '02'"),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "sampleType == 'PRODUCT_LINE'")
            )
    )
    private FactoryArea factoryArea;

    @FabosJsonField(
            views = @View(title = "车间id", show = false),
            edit = @Edit(title = "车间id", show = false)
    )
    private String factoryAreaId;

    @FabosJsonField(
            views = @View(title = "车间名称", show = false),
            edit = @Edit(title = "车间名称", show = false)
    )
    private String factoryAreaName;

    @Transient
    @FabosJsonField(
            views = @View(title = "产线", show = false, column = "factoryAreaName"),
            edit = @Edit(title = "产线",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "factoryAreaName"),
                    queryCondition = "{\"pid\": \"${factoryArea.id}\"}",
                    filter = @Filter(value = "factoryAreaTypeCode = '03'"),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "sampleType == 'PRODUCT_LINE'")
            )
    )
    private FactoryArea factoryLine;

    @FabosJsonField(
            views = @View(title = "产线id", show = false),
            edit = @Edit(title = "产线id", show = false)
    )
    private String factoryLineId;

    @FabosJsonField(
            views = @View(title = "产线名称", show = false),
            edit = @Edit(title = "产线名称", show = false)
    )
    private String factoryLineName;

    @Transient
    @FabosJsonField(
            views = @View(title = "工序", show = false, column = "name"),
            edit = @Edit(title = "工序",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "sampleType == 'PRODUCT_LINE'")
            )
    )
    private ProductProcess process;

    @FabosJsonField(
            views = @View(title = "工序编码", show = false),
            edit = @Edit(title = "工序编码", show = false)
    )
    private String processCode;

    @FabosJsonField(
            views = @View(title = "工序名称", show = false),
            edit = @Edit(title = "工序名称", show = false)
    )
    private String processName;

    // 选择留样库存中的数据
    @FabosJsonField(
            views = @View(title = "留样库存", column = "sampleTaskNo", show = false),
            edit = @Edit(title = "留样库存",
                    allowAddMultipleRows = false,
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "sampleTaskNo"),
                    // 需要与质检标准中的物料编码进行匹配
                    queryCondition = "{\"materialCode\": \"${materialCode}\", \"storageStatus\":\"NORMAL\", \"quantity\":\"0.01,\"}",
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "sampleType == 'KEEP_SAMPLE' && inspectionStandardMTO != null")
            )
    )
    @ManyToOne
    private KsInventory ksInventory;

    // 样品类型为库存时, 选择wms中的物料批次
    @FabosJsonField(
            views = @View(title = "物料批次", column = "inventoryLotId", show = false),
            edit = @Edit(title = "物料批次",
                    allowAddMultipleRows = false,
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "inventoryLotId"),
                    // 需要与质检标准中的物料编码进行匹配
                    queryCondition = "{\"currentState\":\"normal\",  \"materialCode\": \"${materialCode}\"}",
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "sampleType == 'INVENTORY' && inspectionStandardMTO != null")
            )
    )
    @ManyToOne
    private WmsInventoryMTO wmsInventoryMTO;

    // 明细中的样本量之和
    @FabosJsonField(
            views = @View(title = "取样量"),
            edit = @Edit(title = "取样量", show = false,
                    numberType = @NumberType(min = 0, max = 999999999, precision = 2))
    )
    private Double totalSampleQuantity;

    // 数据来源于质控计划，可修改
    @ManyToOne
    @FabosJsonField(
            views = @View(title = "执行人", column = "name"),
            edit = @Edit(title = "执行人",
                    tips = "数据源于质控计划，可修改执行人",
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "qcPlan != null"),
                    type = EditType.REFERENCE_TABLE,
                    filter = @Filter("PersonManage.personState = 'AVAILABLE'"),
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    search = @Search()),
            dynamicField = @DynamicField(passive = true)
    )
    private PersonManage executePerson;

    // 新增编辑时不展示
    @ManyToOne
    @FabosJsonField(
            views = @View(title = "创建人", column = "name"),
            edit = @Edit(title = "创建人",
                    type = EditType.REFERENCE_TABLE,
                    show = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    search = @Search(vague = true))
    )
    private PersonManage createPerson;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "创建时间"),
            edit = @Edit(title = "创建时间", show = false, type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE_TIME), search = @Search(vague = true))
    )
    private Date creTime;

    // 数据来源于质控计划
    @FabosJsonField(
            views = @View(title = "实施范围", toolTip = true),
            edit = @Edit(title = "实施范围", show = false, readonly = @Readonly, type = EditType.TEXTAREA, inputType = @InputType(length = 400)),
            dynamicField = @DynamicField(passive = true)
    )
    private String impScope;

    // 数据来源于质控计划
    @FabosJsonField(
            views = @View(title = "判定依据", toolTip = true),
            edit = @Edit(title = "判定依据", show = false, readonly = @Readonly, type = EditType.TEXTAREA, inputType = @InputType(length = 400)),
            dynamicField = @DynamicField(passive = true)
    )
    private String judAccord;

    // 数据来源于质控计划
    @FabosJsonField(
            views = @View(title = "判定标准", toolTip = true),
            edit = @Edit(title = "判定标准", show = false, readonly = @Readonly, type = EditType.TEXTAREA, inputType = @InputType(length = 400)),
            dynamicField = @DynamicField(passive = true)
    )
    private String judStandard;

    // 数据来源于质控计划
    @FabosJsonField(
            views = @View(title = "质控操作", toolTip = true),
            edit = @Edit(title = "质控操作", show = false, readonly = @Readonly, type = EditType.TEXTAREA, inputType = @InputType(length = 400)),
            dynamicField = @DynamicField(passive = true)
    )
    private String qcOperate;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态", show = false, search = @Search(vague = true), type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = QcTaskStateEnum.class))
    )
    private String currentState;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "任务完成时间"),
            edit = @Edit(title = "任务完成时间", show = false, type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE_TIME), search = @Search(vague = true))
    )
    private Date finishTime;

    // 新增、编辑时不展示
    @FabosJsonField(
            views = @View(title = "检测结果"),
            edit = @Edit(title = "检测结果", type = EditType.CHOICE, show = false,
                    search = @Search(vague = true),
                    tips = "检测结果",
                    choiceType = @ChoiceType(fetchHandler = QcStateEnum.class))
    )
    private String detectionResult;

    @FabosJsonField(
            views = @View(title = "紧急度"),
            edit = @Edit(title = "紧急度", type = EditType.CHOICE, notNull = true,
                    choiceType = @ChoiceType(fetchHandler = UrgentEnum.class), defaultVal = "N"),
            dynamicField = @DynamicField(passive = true)
    )
    private String isUrgent;

    @FabosJsonField(
            views = @View(title = "备注", toolTip = true),
            edit = @Edit(title = "备注", type = EditType.TEXTAREA, inputType = @InputType(length = 400))
    )
    private String remark;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "qc_task_id")
    @FabosJsonField(
            views = @View(title = "质控任务明细", type= ViewType.TABLE_VIEW, column = "name", extraPK = "inspectionItemId"),
            edit = @Edit(title = "质控任务明细", type = EditType.TAB_REFER_ADD,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "(inspectionStandardMTO != null && sampleType != null) || (inspectionStandardMTO != null && takeSample == 'N')")),
            referenceAddType = @ReferenceAddType(referenceClass = "ItemMTO",
                    queryCondition = "{ \"status\": \"Effective\"}",
                    referenceAddHandler = QcTaskDetailReferAddHandler.class,  editable = {"inspectionItemGroupMTO", "sampleQuantity"}),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = {"inspectionStandardMTO", "sampleType",
                    "ksInventory", "wmsInventoryMTO", "qcTaskDetails"},
                    dynamicHandler = QcTaskDetailAddHandler.class))
    )
    private List<QcTaskDetail> qcTaskDetails;

    // 结果提交标志： 默认 null 不可提交; 1 可提交结果; 2 已提交
    @FabosJsonField(
            views = @View(title = "结果提交标志", show = false),
            edit = @Edit(title = "结果提交标志", show = false)
    )
    private String submissionFlag;

    // 根据质控台账中绑定的检测室信息
    @FabosJsonField(
            views = @View(title = "所属检测室", show = false),
            edit = @Edit(title = "所属检测室", show = false)
    )
    private String belongLabName;

    // 根据质控台账中绑定的检测室信息， 查出所属实验室
    @FabosJsonField(
            views = @View(title = "所属检测基地", show = false),
            edit = @Edit(title = "所属检测基地", show = false)
    )
    private String belongExpLabName;
}
