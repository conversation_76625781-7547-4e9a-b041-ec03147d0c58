package cec.jiutian.bc.qualityControl.domain.qcTask.handler;

import cec.jiutian.bc.qualityControl.domain.qcTask.model.QcTask;
import cec.jiutian.bc.qualityControl.domain.qcTask.mto.InspectionStandardMTO;
import cec.jiutian.bc.qualityControl.enumeration.QcSampleTypeEnum;
import cec.jiutian.common.util.StringUtils;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class InsStdDynamicHandler implements DependFiled.DynamicHandler<QcTask> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    // 样品类型-产线
    private static final String PRODUCT_LINE = QcSampleTypeEnum.Enum.PRODUCT_LINE.name();


    @Override
    public Map<String, Object> handle(QcTask qcTask) {
        InspectionStandardMTO insStd = qcTask.getInspectionStandardMTO();
        if (insStd == null) {
            return emptyMap();
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("materialName", insStd.getMaterialName() == null ? "" : insStd.getMaterialName());
        map.put("materialCode", insStd.getMaterialCode() == null ? "" : insStd.getMaterialCode());
        return map;
    }

    /**
     * 返回一个空的map
     * <AUTHOR>
     * @date 2025/3/26 16:25
     * @return
     */
    private Map<String, Object> emptyMap() {
        HashMap<String, Object> map = new HashMap<>();
        map.put("materialName", "");
        map.put("materialCode", "");
        return map;
    }
}
