package cec.jiutian.bc.process.domain.sample.model;

import cec.jiutian.bc.process.domain.sample.handler.SampleTaskFractDynamicHandler;
import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.*;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @description:
 */
@Entity
@Table(name = "lm_sample_task")
@Getter
@Setter
@FabosJson(name = "样品任务-分样",
        orderBy = "SampleTaskFRA.createTime desc",power = @Power(export = false,importable = false,print = false)

)
@FabosJsonI18n
public class SampleTaskFRA extends BaseModel {
    @FabosJsonField(
            views = @View(title = "样品任务单号"),
            edit = @Edit(title = "样品任务单号",readonly = @Readonly)
    )
    private String generalCode;

    //物料批次号
    @FabosJsonField(
            views = @View(title = "分样组数"),
            edit = @Edit(title = "分样组数",notNull = true)
    )
    private Double num;

    @FabosJsonField(
            views = @View(title = "分样", type = ViewType.TABLE_VIEW, index = 8),
            edit = @Edit(title = "分样", type = EditType.TAB_REFERENCE_GENERATE),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "num", dynamicHandler = SampleTaskFractDynamicHandler.class)),
            referenceGenerateType = @ReferenceGenerateType(editable = {"fraQuantity"})
    )
    @JoinColumn(name = "sample_task_fra_id")
    @OneToMany(cascade = CascadeType.ALL)
    private List<SampleTaskFRAData> sampleTaskFRADataList;


}
