package cec.jiutian.bc.qualityControl.dto;

import lombok.Data;

/**
 * 质控任务查询DTO
 * <AUTHOR>
 * @date 2025/6/11 9:51
 */
@Data
public class QcQueryDTO {
    /**
     * 是否自定义时间查询: 1 全部时间； 2 自定义时间
     */
    private String isDiyTime;

    /**
     * 全部时间（查询）：1 本年度； 2 本季度； 3 本月度； 4 本周
     */
    private String timeType;

    /**
     * 开始时间（自定义查询时间）
     */
    private String startTime;

    /**
     * 结束时间（自定义查询时间）
     */
    private String endTime;

    /**
     * 检测项目编码
     */
    private String itemCode;

    /**
     * 检测指标名称
     */
    private String quotaName;

    /**
     * 实验室名称
     */
    private String expLabName;

    /**
     * 检测室名称
     */
    private String labName;

    /**
     * 设备类型（名称）
     */
    private String equipType;

    /**
     * 设备编号
     */
    private String equipCode;

    /**
     * 人员id
     */
    private String personId;

    /**
     * 物料编码
     */
    private String materialCode;
}
