package cec.jiutian.bc.inspection.domain.question.proxy;

import cec.jiutian.bc.inspection.domain.question.model.EhsQuestion;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class EhsInsQuestionDataProxy implements DataProxy<EhsQuestion> {

    @Resource
    private FabosJsonDao fabosJsonDao;
    @Override
    public void afterFetch(Collection<Map<String, Object>> list) {
        if(CollectionUtils.isNotEmpty(list)){
            list.forEach(d->{
                //判断权限标识  通过某个数据的责任人和当前登录人判断是否存在权限 有权限则赋值rowOperationAuthFlag=1  自定义按钮在通过rowOperationAuthFlag字段去控制权限
                String userId = UserContext.getUserId();
                Map<String,String> metaUser = (Map<String, String>)d.get("metaUser");
                if(userId.equals(metaUser.get("id").toString())){
                    d.put("rowOperationAuthFlag",1);
                }
            });
        }
    }

    @Override
    public void afterSingleFetch(Map<String, Object> map) {
        Map<String,Object> ehsMap = new HashMap<>();
        ehsMap.put("id",map.get("ehsInsTaskId"));
        ehsMap.put("generalCode",map.get("taskGeneralCode"));
        map.put("ehsInsTaskDTO",ehsMap);
        map.put("ehsInsTaskDTO_generalCode",map.get("taskGeneralCode"));
        List<HashMap<String,Object>> ehsQuestionCorrections = (List<HashMap<String,Object>>) map.get("ehsQuestionCorrections");
        UserContext.CurrentUser currentUser = UserContext.get();
        HashMap<String,Object> userMap = new HashMap<>();
        userMap.put("id",currentUser.getUserId());
        userMap.put("name",currentUser.getUserName());
        ehsQuestionCorrections.forEach(d->{
            d.putIfAbsent("verifyMetaUser", userMap);
        });
        map.put("ehsQuestionCorrections",ehsQuestionCorrections);
    }


}
