package cec.jiutian.bc.qualityControl.domain.qcPlan.proxy;

import cec.jiutian.bc.qualityControl.domain.qcPlan.model.QcPlan;
import cec.jiutian.bc.qualityControl.enumeration.QcPlanCycleEnum;
import cec.jiutian.bc.qualityControl.enumeration.QcPlanStatusEnum;
import cec.jiutian.view.fun.DataProxy;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;

@Component
public class QcPlanDataProxy implements DataProxy<QcPlan> {

    @Override
    public void beforeAdd(QcPlan qcPlan) {
        qcPlan.setCurrentStatus(QcPlanStatusEnum.Enum.PUBLISH.name());
    }

    @Override
    public void afterFetch(Collection<Map<String, Object>> list) {
        if(CollectionUtils.isNotEmpty(list)){
            list.forEach(d->{
                if(d.get("cycleNum")!=null&&d.get("cycle")!=null){
                    d.put("cycle",d.get("cycleNum").toString()+ QcPlanCycleEnum.getValueByName(d.get("cycle").toString()));
                }
            });
        }
    }
}
