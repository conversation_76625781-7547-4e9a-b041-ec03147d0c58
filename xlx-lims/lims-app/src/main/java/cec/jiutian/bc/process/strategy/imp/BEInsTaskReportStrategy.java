package cec.jiutian.bc.process.strategy.imp;

import cec.jiutian.bc.process.domain.inspectionTask.model.InspectionTask;
import cec.jiutian.bc.process.enumeration.InsTaskReportTypeEnum;
import cec.jiutian.bc.process.strategy.InsTaskReportStrategy;
import cec.jiutian.common.util.StringUtils;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.ss.usermodel.*;
import org.springframework.stereotype.Service;

import java.io.FileInputStream;
import java.io.InputStream;
import java.text.DecimalFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @description: 蓝电
 * 检测指标可能存在多次。两次结果求平均值，大于两次最终结果是（多次结果去掉最大值和最小值，剩下的求平均值。）
 */
@Service
public class BEInsTaskReportStrategy implements InsTaskReportStrategy {
    @Override
    public boolean isTypeMatch(String type) {
        return InsTaskReportTypeEnum.Enum.BE.name().equals(type);
    }

    @Override
    public void analysis(InputStream inputStream, InspectionTask inspectionTask) throws Exception {
        Map<String, Map<String, List<Object>>> result = parseExcel(inputStream);
        for (Map.Entry<String, Map<String, List<Object>>> entry : result.entrySet()) {
            //  样品任务单号
            String sampleTaskNo = entry.getKey();
            if(StringUtils.isBlank(sampleTaskNo)){
                continue;
            }
            if(sampleTaskNo.equals(inspectionTask.getSampleTaskNo())){
                //样品任务单号匹配上
                Map<String, List<Object>> indicators = entry.getValue();
                //处理检测指标.检测结果值是最终结果
                Map<String,Double> indicatorValues = new HashMap<>();
                for (Map.Entry<String, List<Object>> indicatorEntry : indicators.entrySet()) {
                    //检测指标
                    String indicator = indicatorEntry.getKey();
                    List<Object> values = indicatorEntry.getValue();
                    //平均值
                    Double average = getAverage(values);
                    if(average!=null){
                        indicatorValues.put(indicator,average);
                    }
                }
                //匹配检测指标
                inspectionTask.getInspectionTaskDetails().forEach(d->{
                    d.getInspectionTaskDetailQuotas().forEach(q->{
                        Double aDouble = indicatorValues.get(q.getName());
                        if(aDouble!=null){
                            //匹配上
                            q.setInspectValue(aDouble.toString());
                        }
                    });
                });
                //任务单匹配上就不往下循环了
                break;
            }
        }
    }

    /**
     * 平均值 一个值直接返回  两个求平均值   两个以上去掉最大值和最小值求平均值
     * @param values
     * @return
     */
    private Double getAverage(List<Object> values) {
        // 过滤无效数据
        List<Double> validValues = values.stream()
                .filter(obj -> obj != null && isConvertibleToDouble(obj))
                .map(this::convertToDouble)
                .toList();
        if(CollectionUtils.isEmpty(validValues)){
            return null;
        }

        int count = validValues.size();

        // 根据有效数值数量采取不同策略
        if (count == 1) {
            return validValues.get(0); // 直接返回单个值
        } else if (count == 2) {
            return (validValues.get(0) + validValues.get(1)) / 2.0; // 返回平均值
        } else {
            // 去掉最大值和最小值后计算平均值
            List<Double> sortedValues = new ArrayList<>(validValues.stream()
                    .sorted()
                    .toList());

            // 去掉最大值和最小值
            sortedValues.remove(0);
            sortedValues.remove(sortedValues.size() - 1);

            // 计算平均值  保留两位小数  excel模版平均值也是两位小数
            DecimalFormat decimalFormat = new DecimalFormat("#.##");
            return Double.parseDouble(decimalFormat.format(sortedValues.stream()
                    .mapToDouble(Double::doubleValue)
                    .average()
                    .orElse(0.0)));
        }
    }

    /**
     * 判断对象是否可以转换为double
     * @param obj
     * @return
     */
    private boolean isConvertibleToDouble(Object obj) {
        try {
            convertToDouble(obj);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 将对象转换为double
     * @param obj
     * @return
     */
    private double convertToDouble(Object obj) {
        // 如果已经是数字类型，直接转换
        if (obj instanceof Number) {
            return ((Number) obj).doubleValue();
        }
        // 如果是String且可以解析为double，转换
        else if (obj instanceof String) {
            return Double.parseDouble((String) obj);
        }
        // 其他类型不支持转换
        throw new NumberFormatException("Cannot convert " + obj + " to double");
    }

    public static void main(String[] args) {
        String excelPath = "/Users/<USER>/app/BE.xlsx"; // 替换为您的Excel文件路径

        try {
            Map<String, Map<String, List<Object>>> result = parseExcel(new FileInputStream(excelPath));

            // 打印结果
            for (Map.Entry<String, Map<String, List<Object>>> entry : result.entrySet()) {
                System.out.println("任务单号: " + entry.getKey());

                Map<String, List<Object>> indicators = entry.getValue();
                for (Map.Entry<String, List<Object>> indicatorEntry : indicators.entrySet()) {
                    System.out.print("检测指标: " + indicatorEntry.getKey() + " -> [");
                    List<Object> values = indicatorEntry.getValue();
                    for (int i = 0; i < values.size(); i++) {
                        System.out.print(values.get(i));
                        if (i < values.size() - 1) {
                            System.out.print(", ");
                        }
                    }
                    System.out.println("]");
                }
                System.out.println("----------------------------------------");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static Map<String, Map<String, List<Object>>> parseExcel(InputStream inputStream) throws Exception {
        Map<String, Map<String, List<Object>>> resultMap = new LinkedHashMap<>();

        try (Workbook workbook = WorkbookFactory.create(inputStream)) {
            Sheet sheet = workbook.getSheetAt(0); // 获取第一个工作表

            // 获取表头行
            Row headerRow = sheet.getRow(0);
            if (headerRow == null) {
                throw new IllegalArgumentException("Excel 文件没有表头行");
            }

            // 提取检测指标列的表头名称（从C列之后开始）
            List<String> indicatorHeaders = new ArrayList<>();
            // C列的索引固定为2（从0开始计数）
            int startColIndex = 2; // C列的索引

            // 从D列开始提取检测指标表头
            for (int i = startColIndex + 1; i < headerRow.getLastCellNum(); i++) {
                Cell cell = headerRow.getCell(i);
                Object cellValue = getCellValue(cell);
                String headerName = cellValue !=null ? cellValue.toString() : "未知指标_" + i;
                indicatorHeaders.add(headerName);
            }

            // 解析数据行
            for (int rowNum = 1; rowNum <= sheet.getLastRowNum(); rowNum++) {
                Row dataRow = sheet.getRow(rowNum);
                if (dataRow == null) {
                    continue;
                }

                // 获取任务单号（C列）
                Cell taskNumberCell = dataRow.getCell(startColIndex);
                Object taskNumberObj = getCellValue(taskNumberCell);
                String taskNumber = taskNumberObj ==null ? "" : taskNumberObj.toString();

                if (taskNumber == null || taskNumber.trim().isEmpty()) {
                    continue; // 跳过没有任务单号的行
                }

                // 初始化任务单号对应的检测指标数据
                Map<String, List<Object>> indicatorMap = resultMap.computeIfAbsent(taskNumber, k -> new LinkedHashMap<>());

                // 解析检测指标数据
                for (int i = 0; i < indicatorHeaders.size(); i++) {
                    String indicatorKey = indicatorHeaders.get(i);
                    int cellIndex = startColIndex + 1 + i; // D列及之后的列索引

                    Cell cell = dataRow.getCell(cellIndex);
                    Object cellValue = getCellValue(cell);

                    // 初始化指标列表
                    List<Object> values = indicatorMap.computeIfAbsent(indicatorKey, k -> new ArrayList<>());
                    values.add(cellValue);
                }
            }
        }

        return resultMap;
    }

    /**
     * 获取单元格值（兼容各种数据类型）
     *
     * @param cell 单元格
     * @return 单元格值
     */
    private static Object getCellValue(Cell cell) {
        if (cell == null) {
            return null;
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getRichStringCellValue().getString();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue();
                } else {
                    double numericValue = cell.getNumericCellValue();
                    // 尝试将数字转换为整数，如果小数部分为0
                    if (numericValue == (long) numericValue) {
                        return (long) numericValue;
                    } else {
                        return numericValue;
                    }
                }
            case BOOLEAN:
                return cell.getBooleanCellValue();
            case FORMULA:
                // 尝试计算公式值
                try {
                    cell.setCellType(CellType.NUMERIC);
                    return getCellValue(cell);
                } catch (IllegalStateException e) {
                    return cell.getCellFormula();
                }
            case BLANK:
                return null;
            case ERROR:
                return "ERROR:" + cell.getErrorCellValue();
            default:
                return null;
        }
    }
}
