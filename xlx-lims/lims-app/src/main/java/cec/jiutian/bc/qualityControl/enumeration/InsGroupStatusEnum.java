package cec.jiutian.bc.qualityControl.enumeration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.Getter;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Getter
public enum InsGroupStatusEnum {

//    创建 → 编辑 → 生效 → 失效‌
    CREATED("创建"),
    EDIT("编辑"),
    ACTIVE("生效"),
    INACTIVE("失效");

    private final String value;
    InsGroupStatusEnum(String value) {
        this.value = value;
    }

    public static class ChoiceFetch implements ChoiceFetchHandler {
        @Override
        public List<VLModel> fetch(String[] params) {
            return Stream.of(InsGroupStatusEnum.values()).map(groupStatusEnum ->
                    new VLModel(groupStatusEnum.name(), groupStatusEnum.getValue())).collect(Collectors.toList());
        }

    }


}