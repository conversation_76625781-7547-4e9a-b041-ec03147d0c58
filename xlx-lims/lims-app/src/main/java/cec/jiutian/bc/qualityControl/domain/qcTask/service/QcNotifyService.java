package cec.jiutian.bc.qualityControl.domain.qcTask.service;

import cec.jiutian.bc.compare.util.BcUtil;
import cec.jiutian.bc.ecs.enums.MessageWayEnum;
import cec.jiutian.bc.qualityControl.constant.QcConstant;
import cec.jiutian.bc.qualityControl.domain.qcTask.model.QcTask;
import cec.jiutian.bc.qualityControl.domain.qcTask.model.QcTaskDetailQuota;
import cec.jiutian.bc.qualityControl.enumeration.QcMethodEnum;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 质控任务告警服务类
 * <AUTHOR>
 * @date 2025/6/17 14:24
 */
@Service
public class QcNotifyService {
    @Resource
    private BcUtil bcUtil;
    // 质控类型：短期
    private static final String SHORT = QcMethodEnum.Enum.SHORT_TERM_STABILITY.name();
    // 质控类型：长期
    private static final String LONG = QcMethodEnum.Enum.LONG_TERM_STABILITY.name();
    // 质控类型：准确性
    private static final String ACCURACY = QcMethodEnum.Enum.LONG_TERM_ACCURACY.name();

    /**
     * 根据质控任务类型进行告警
     * <AUTHOR>
     * @date 2025/6/17 15:20
     * @param qcTask
     */
    public void notifyByQcTaskType(QcTask qcTask) {
        String qcMethod = qcTask.getQcMethod();
        List<QcTaskDetailQuota> quotaList = qcTask.getQcTaskDetails().stream()
                .flatMap(detail -> detail.getQcTaskDetailQuotas().stream())
                .collect(Collectors.toList());
        if (SHORT.equals(qcMethod)) {
            checkAndNotify(qcTask, quotaList, QcConstant.SHORT_QC_REFER_LINE_1, null);
        } else if (LONG.equals(qcMethod)) {
            checkAndNotify(qcTask, quotaList, QcConstant.LONG_QC_REFER_LINE_1, QcConstant.LONG_QC_REFER_LINE_2);
        } else if (ACCURACY.equals(qcMethod)) {
            checkAndNotify(qcTask, quotaList, QcConstant.ACC_QC_REFER_LINE_1, QcConstant.ACC_QC_REFER_LINE_2);
        }
    }

    /**
     * 检测结果是否超规格并告警
     * <AUTHOR>
     * @date 2025/6/17 17:15
     * @param qcTask 质控任务
     * @param quotaList 检测指标集合
     * @param upperLimit 参考规格值（上限）
     * @param lowerLimit 参考规格值（下限）
     */
    public void checkAndNotify(QcTask qcTask, List<QcTaskDetailQuota> quotaList,
                                String upperLimit, String lowerLimit) {
        List<QcTaskDetailQuota> overQuotaList = quotaList.stream()
                .filter(quota -> isOverLimit(quota.getInspectValue(), upperLimit, lowerLimit))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(overQuotaList)) {
            return;
        }
        String limitDesc = lowerLimit == null
                ? "参考规格值（上限）：" + upperLimit
                : "参考规格值（上限）：" + upperLimit + "，参考规格值（下限）：" + lowerLimit;
        String notifyMessage = "质控任务编号【" + qcTask.getGeneralCode() + "】，" +
                overQuotaList.stream()
                        .map(quota -> "检测指标【" + quota.getName() + "】检测结果值：" + quota.getInspectValue() +
                                "，" + limitDesc + "，检测结果超规格")
                        .collect(Collectors.joining("；", "", "。"));
        Set<String> phoneSet = this.getPhoneSet(qcTask);
        if (CollectionUtils.isNotEmpty(phoneSet)) {
            bcUtil.sendPersonMessage("质控任务检测指标超规格", notifyMessage, null, MessageWayEnum.App, phoneSet);
        }
    }

    /**
     * 获取通知手机号码集合
     * <AUTHOR>
     * @date 2025/6/17 17:15
     * @param qcTask 质控任务
     * @return
     */
    public Set<String> getPhoneSet(QcTask qcTask) {
        Set<String> phoneSet = new HashSet<String>();
        String qcMethod = qcTask.getQcMethod();
        // 短期质控
        if (SHORT.equals(qcMethod)) {
            phoneSet = bcUtil.getLimsRoleUserPhoneSet("1");
        }
        // 准确性质控
        if (ACCURACY.equals(qcMethod)) {
            phoneSet = bcUtil.getRoleUserPhoneSet("1", "2", "3");
        }
        // 长期质控
        if (LONG.equals(qcMethod)) {
            phoneSet = bcUtil.getLimsRoleUserPhoneSet("1");
        }
        return phoneSet;
    }

    /**
     * 判断检测结果是否超规格
     * <AUTHOR>
     * @date 2025/6/17 17:15
     * @param inspectValue 检测结果值
     * @param upperLimit 参考规格值（上限）
     * @param lowerLimit 参考规格值（下限）
     * @return
     */
    private boolean isOverLimit(String inspectValue, String upperLimit, String lowerLimit) {
        double value = Double.parseDouble(inspectValue);
        return (upperLimit != null && value > Double.parseDouble(upperLimit)) ||
                (lowerLimit != null && value < Double.parseDouble(lowerLimit));
    }

}
