package cec.jiutian.bc.process.domain.sample.schedule;

import cec.jiutian.bc.job.provider.IJobProvider;
import cec.jiutian.bc.process.domain.sample.model.SampleTask;
import cec.jiutian.bc.process.domain.sample.service.SampleTaskService;
import cec.jiutian.core.frame.annotation.FabosCustomizedService;
import cec.jiutian.meta.FabosJob;
import jakarta.annotation.Resource;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@FabosCustomizedService(value = SampleTask.class)
@Component
@Transactional
@Slf4j
public class SampleTaskProvider implements IJobProvider {
    @Resource
    private SampleTaskService sampleTaskService;

    @Override
    @FabosJob(comment = "样品任务告警定时任务方法")
    public String exec(String code, String param) {
        log.info("样品任务告警定时任务开始执行");
        sampleTaskService.alarmNotify();
        return null;
    }

}
