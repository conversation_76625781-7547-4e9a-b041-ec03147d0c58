package cec.jiutian.bc.compare.domain.comparePlan.model;

import cec.jiutian.bc.compare.domain.comparePlan.handler.ComTaskGenHandler;
import cec.jiutian.bc.compare.domain.comparePlan.handler.CompResultInHandler;
import cec.jiutian.bc.compare.domain.comparePlan.handler.ComparePlanCancelHandler;
import cec.jiutian.bc.compare.domain.comparePlan.handler.ComparePlanCodeGenerateHandler;
import cec.jiutian.bc.compare.domain.comparePlan.handler.ComparePlanConfirmHandler;
import cec.jiutian.bc.compare.domain.comparePlan.handler.UploadReportHandler;
import cec.jiutian.bc.compare.domain.comparePlan.proxy.ComparePlanProxy;
import cec.jiutian.bc.compare.enumeration.CompareCategoryEnum;
import cec.jiutian.bc.compare.enumeration.CompareStateEnum;
import cec.jiutian.bc.compare.enumeration.CompareTypeEnum;
import cec.jiutian.bc.process.domain.sample.mto.WmsInventoryMTO;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.FabosJsonI18n;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.AttachmentType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DateType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowBaseOperation;
import cec.jiutian.view.type.RowOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OrderBy;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@FabosJson(name = "对标计划",
        orderBy = "ComparePlan.createTime desc",
        power = @Power(importable = false, print = false, viewDetails = false, export = true),
        dataProxy = ComparePlanProxy.class,
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "currentState != 'CLOSED'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "currentState != 'TO_BE_CONFIRMED'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                )
        },
        rowOperation = {
                @RowOperation(
                        title = "确认",
                        code = "ComparePlan@CONFIRM",
                        operationHandler = ComparePlanConfirmHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "该操作不可撤回，确定执行吗？",
                        ifExpr = "currentState != 'TO_BE_CONFIRMED'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "ComparePlan@CONFIRM"
                        )
                ),
                @RowOperation(
                        title = "作废",
                        code = "ComparePlan@CANCEL",
                        operationHandler = ComparePlanCancelHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "该操作不可撤回，确定执行吗？",
                        ifExpr = "currentState != 'TO_BE_CONFIRMED'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "ComparePlan@CANCEL"
                        )
                ),
                @RowOperation(
                        title = "任务生成",
                        code = "ComparePlan@TASKGEN",
                        operationHandler = ComTaskGenHandler.class,
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = CompTaskGen.class,
                        show = @ExprBool(
                                params = "ComparePlan@TASKGEN",
                                exprHandler = UserRowOperationExprHandler.class
                        ),
                        ifExpr = "selectedItems[0].currentState != 'TO_BE_EXECUTE'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "结果录入",
                        code = "ComparePlan@RESULTIN",
                        operationHandler = CompResultInHandler.class,
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = CompResultIn.class,
                        show = @ExprBool(
                                params = "ComparePlan@RESULTIN",
                                exprHandler = UserRowOperationExprHandler.class
                        ),
                        ifExpr = "selectedItems[0].isResultIn != '1'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "上传报告",
                        code = "ComparePlan@UPLOADREPORT",
                        operationHandler = UploadReportHandler.class,
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = UploadReport.class,
                        show = @ExprBool(
                                params = "ComparePlan@UPLOADREPORT",
                                exprHandler = UserRowOperationExprHandler.class
                        ),
                        // 需要先录入结果 再上传报告
                        ifExpr = "selectedItems[0].isUploaded != '1'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                )
        }
)
@Getter
@Setter
@Entity
@Table(name = "lm_compare_plan",
        uniqueConstraints = {
            @UniqueConstraint(columnNames = {"generalCode"})
})
@TemplateType(type = "multiTable")
@FabosJsonI18n
public class ComparePlan extends MetaModel {
    @FabosJsonField(
            views = @View(title = "对标计划编号"),
            edit = @Edit(title = "对标计划编号", notNull = true),
            dynamicField = @DynamicField(
                    dependFiled = @DependFiled(buttonName = "生成", changeBy = {"id"},
                            dynamicHandler = ComparePlanCodeGenerateHandler.class))
    )
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "对标类别"),
            edit = @Edit(title = "对标类别", notNull = true, type = EditType.CHOICE, search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = CompareCategoryEnum.class))
    )
    private String category;

    @FabosJsonField(
            views = @View(title = "对标类型"),
            edit = @Edit(title = "对标类型", notNull = true, type = EditType.CHOICE, search = @Search(),
                    choiceType = @ChoiceType(
                            dependField = "category",
                            fetchHandler = CompareTypeEnum.class))
    )
    private String type;

    @FabosJsonField(
            views = @View(title = "对标对象"),
            edit = @Edit(title = "对标对象", notNull = true, search = @Search(),
                tips = "对标对象一 & 对标对象二")
    )
    private String object;

    @FabosJsonField(
            views = @View(title = "物料编号", column = "materialCode"),
            edit = @Edit(title = "物料编号",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "materialCode"),
                    queryCondition = "{\"currentState\":\"normal\"}"
            )
    )
    @ManyToOne
    @JoinColumn(name = "wms_inventory_id")
    //获取wms台账批次
    private WmsInventoryMTO wmsInventoryMTO;

    @FabosJsonField(
            views = @View(title = "物料编号", show = false),
            edit = @Edit(title = "物料编号", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "wmsInventoryMTO",
                    beFilledBy = "materialCode"))
    )
    private String materialCode;

    @FabosJsonField(
            views = @View(title = "物料名称"),
            edit = @Edit(title = "物料名称", readonly = @Readonly, search = @Search(vague = true)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "wmsInventoryMTO",
                    beFilledBy = "materialName"))
    )
    private String materialName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "预计完成时间"),
            edit = @Edit(title = "预计完成时间", notNull = true, type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE_TIME), search = @Search(vague = true))
    )
    private Date expectTime;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态", show = false, search = @Search(), type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = CompareStateEnum.class))
    )
    private String currentState;

    @FabosJsonField(
            views = @View(title = "对标结果"),
            edit = @Edit(title = "对标结果", type = EditType.ATTACHMENT, show = false,
                    attachmentType = @AttachmentType)
    )
    private String compResult;

    @FabosJsonField(
            views = @View(title = "对标结论", toolTip = true),
            edit = @Edit(title = "对标结论", show = false)
    )
    private String conclusion;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "完成时间"),
            edit = @Edit(title = "完成时间", type = EditType.DATE, show = false,
                    dateType = @DateType(type = DateType.Type.DATE_TIME), search = @Search(vague = true))
    )
    private Date finishTime;

    // 外部检测报告
    @FabosJsonField(
            views = @View(title = "检测报告"),
            edit = @Edit(title = "检测报告", type = EditType.ATTACHMENT, show = false,
                    attachmentType = @AttachmentType)
    )
    private String externalReport;

    @FabosJsonField(
            views = @View(title = "备注", toolTip = true),
            edit = @Edit(title = "备注", type = EditType.TEXTAREA)
    )
    private String remark;

    // 关联子表
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "compare_plan_id")
    @OrderBy
    @FabosJsonField(
            views = @View(title = "检测任务", type = ViewType.TABLE_VIEW),
            edit = @Edit(title = "检测任务", type = EditType.TAB_REFERENCE_GENERATE, show = false)
    )
    private List<ComparePlanDetail> comparePlanDetails;

    // 是否需要上传报告标准 只有"样品发货"才能上传报告 默认 null 不需要上传; 1 需要上传
/*    @FabosJsonField(
            views = @View(title = "是否需要上传报告", show = false),
            edit = @Edit(title = "是否需要上传报告", show = false)
    )
    private String isNeedUpload;*/

    // 结果录入标志 默认 null 未录入; 1 允许录入; 2 已录入 不能重复录入
    @FabosJsonField(
            views = @View(title = "是否能进行结果录入", show = false),
            edit = @Edit(title = "是否能进行结果录入", show = false)
    )
    private String isResultIn;

    // 是否已经上传报告标准 只有"样品发货"才能上传报告 默认 null 未上传; 1 允许上传; 2 已经上传 不可重复上传
    @FabosJsonField(
            views = @View(title = "是否已经上传报告", show = false),
            edit = @Edit(title = "是否已经上传报告", show = false)
    )
    private String isUploaded;
}
