package cec.jiutian.bc.qualityControl.domain.qcTask.model;

import cec.jiutian.bc.qualityControl.domain.qcTask.handler.QcDetailQuotasAddHandler;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.ReferenceGenerateType;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@FabosJson(name = "结果录入")
@Entity
@Getter
@Setter
@Table(name = "lm_quantity_task_detail")
public class QcTaskResultInput extends MetaModel {
    @FabosJsonField(
            views = @View(title = "质检任务id", show = false),
            edit = @Edit(title = "质检任务id", show = false)
    )
    private String parentTaskId;

    // 编辑检测值和检测结果
    @FabosJsonField(
            views = @View(title = "检测指标", column = "name", type= ViewType.TABLE_VIEW),
            edit = @Edit(title = "检测指标", type = EditType.TAB_REFERENCE_GENERATE),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "qcTaskDetailQuota",
                    dynamicHandler = QcDetailQuotasAddHandler.class)),
            referenceGenerateType = @ReferenceGenerateType(editable = {"inspectValue", "inspectResult"})
    )
    @JoinColumn(name = "inspection_task_detail_id")
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    private List<QcTaskDetailQuota> qcTaskDetailQuota;
}
