package cec.jiutian.bc.qualityControl.domain.qcTask.handler;

import cec.jiutian.bc.base.domain.experimentMethod.model.ExpMethod;
import cec.jiutian.bc.base.domain.experimentMethod.model.ExpMethodDetail;
import cec.jiutian.bc.base.enumeration.LmNamingRuleCodeEnum;
import cec.jiutian.bc.compare.domain.comparePlan.handler.ComTaskGenHandler;
import cec.jiutian.bc.compare.mto.SpecificationManageMTO;
import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.keepSample.domain.inventory.model.KsInventory;
import cec.jiutian.bc.process.domain.inspectionTask.model.InspectionTask;
import cec.jiutian.bc.process.domain.inspectionTask.model.InspectionTaskDetail;
import cec.jiutian.bc.process.domain.inspectionTask.service.InspectionTaskService;
import cec.jiutian.bc.process.domain.sample.model.SampleTask;
import cec.jiutian.bc.process.domain.sample.model.SampleTaskDetail;
import cec.jiutian.bc.process.domain.sample.model.SampleTaskDetailQuota;
import cec.jiutian.bc.process.domain.sample.mto.WmsInventoryMTO;
import cec.jiutian.bc.process.enumeration.CheckTypeEnum;
import cec.jiutian.bc.process.enumeration.SampleTaskSourceEnum;
import cec.jiutian.bc.process.enumeration.SampleTypeEnum;
import cec.jiutian.bc.process.enumeration.TaskStatusEnum;
import cec.jiutian.bc.qualityControl.domain.qcTask.model.QcTask;
import cec.jiutian.bc.qualityControl.domain.qcTask.model.QcTaskDetail;
import cec.jiutian.bc.qualityControl.domain.qcTask.mto.InspectionItemGroupMTO;
import cec.jiutian.bc.qualityControl.domain.qcTask.proxy.QcTaskDataProxy;
import cec.jiutian.bc.qualityControl.enumeration.QcSampleTypeEnum;
import cec.jiutian.bc.qualityControl.enumeration.QcTaskDetailStateEnum;
import cec.jiutian.bc.qualityControl.enumeration.QcTaskStateEnum;
import cec.jiutian.common.util.BeanUtils;
import cec.jiutian.core.frame.constant.YesOrNoStatus;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 待发布 --> 执行中
 * <AUTHOR>
 * @date 2025/3/27 13:50
 */
@Component
public class QcTaskUpdateStateHandler implements OperationHandler<QcTask, Void> {
    @Resource
    private FabosJsonDao fabosJsonDao;
    @Resource
    private NamingRuleService namingRuleService;
    @Resource
    private InspectionTaskService inspectionTaskService;
    @Resource
    private ComTaskGenHandler comTaskGenHandler;
    @Resource
    private QcTaskDataProxy qcTaskDataProxy;

    // 样品类型-库存
    private static final String INVENTORY = QcSampleTypeEnum.Enum.INVENTORY.name();
    // 样品类型-库存
    private static final String KEEP_SAMPLE = QcSampleTypeEnum.Enum.KEEP_SAMPLE.name();
    // 样品类型-产线
    private static final String PRODUCT_LINE = QcSampleTypeEnum.Enum.PRODUCT_LINE.name();

    @Override
    @Transactional
    public String exec(List<QcTask> data, Void modelObject, String[] param) {
        QcTask qcTask = data.get(0);
        // 校验数据 若子单为空  则不允许发布
        if (CollectionUtils.isEmpty(qcTask.getQcTaskDetails())) {
            throw new FabosJsonApiErrorTip("质控任务明细为空，不允许提交！");
        }
        if (QcTaskStateEnum.Enum.UNDER_RELEASED.name().equals(qcTask.getCurrentState())) {
            // 设置主单状态为执行中
            qcTask.setCurrentState(QcTaskStateEnum.Enum.EXECUTE.name());
            // 设置所有子单状态为检测中
            qcTask.getQcTaskDetails().forEach(qcTaskDetail -> {
                qcTaskDetail.setState(QcTaskDetailStateEnum.Enum.INSPECTING.name());
            });
        }
        // 需要取样才会同步创建样品任务、检测任务
        if ("Y".equals(qcTask.getTakeSample())) {
            List<SampleTask> sampleTaskList = this.createSampleTask(qcTask);
            List<InspectionTask> insTaskList = new ArrayList<>();
            for (SampleTask task : sampleTaskList) {
                InspectionTask inspectionTask = inspectionTaskService.createInsTaskBySampleTask(
                        task.getGeneralCode(), qcTask.getExecutePerson(), qcTask.getSampleSource());
                insTaskList.add(inspectionTask);
            }
            // 遍历委托申请单集合，以<检测项目编号，委托申请单号>组成map集合
            Map<String, String> map = new HashMap<>();
            for (InspectionTask task : insTaskList) {
                List<InspectionTaskDetail> details = task.getInspectionTaskDetails();
                for (InspectionTaskDetail detail : details) {
                    map.put(detail.getTestMethodCode(), task.getGeneralCode());
                }
            }
            // 遍历质控任务，装载委托申请单号
            for (QcTaskDetail detail : qcTask.getQcTaskDetails()) {
                if (map.containsKey(detail.getInspectionItemCode())) {
                    detail.setInspectionTaskNo(map.get(detail.getInspectionItemCode()));
                }
            }
        }
        fabosJsonDao.update(qcTask);
        return null;
    }

    /**
     * 根据质控任务创建样品任务
     * <AUTHOR>
     * @date 2025/3/30 22:32
     * @param qcTask
     */
    public List<SampleTask> createSampleTask(QcTask qcTask) {
        List<SampleTask> newSampleTaskList = new ArrayList<>();
        List<QcTaskDetail> qcTaskDetailList = qcTask.getQcTaskDetails();
        // 将明细中的数据按照检测组, 分成不同的子集合
        List<List<QcTaskDetail>> groupedList = qcTaskDetailList.stream()
                .collect(Collectors.groupingBy(QcTaskDetail::getGroupId))
                .values()
                .stream()
                .collect(Collectors.toList());
        for (List<QcTaskDetail> detailList : groupedList) {
            SampleTask newSampleTask = setBaseSampleTask(qcTask);
            // 任务编号
            String generalCode = namingRuleService.getNameCode(LmNamingRuleCodeEnum.SamplingTask.name(),
                    1, null).get(0);
            newSampleTask.setGeneralCode(generalCode);
            // 子单 试验方法 需要将检测项转化为试验方法
            List<SampleTaskDetail> sampleTaskDetailList = new ArrayList<>();
            for (QcTaskDetail qcTaskDetail : detailList) {
                // 设置检测组相关数据：取样点、送样点、包装方式
                this.setGroupRelatedData(qcTaskDetail, newSampleTask);
                SampleTaskDetail sampleTaskDetail = new SampleTaskDetail();
                // 查询检测项目编号对应的试验方法
                ExpMethod query = new ExpMethod();
                query.setInspectionItemMTOId(qcTaskDetail.getInspectionItemId());
                ExpMethod expMethod = fabosJsonDao.selectOne(query);
                if (expMethod != null) {
                    // 二级子单 检测指标
                    this.setSampleTaskDetail(expMethod, sampleTaskDetail, qcTaskDetail);
                    sampleTaskDetail.setId(null);
                    sampleTaskDetailList.add(sampleTaskDetail);
                }
            }
            // 计算样本总量
            double sum = detailList.stream()
                    .mapToDouble(qcTaskDetail -> qcTaskDetail.getSampleQuantity())
                    .sum();
            newSampleTask.setTotalQuantity(sum);
            newSampleTask.setSampleTaskDetails(sampleTaskDetailList);
            // 设置数据来源id
            newSampleTask.setDataSourceId(qcTask.getId());
            fabosJsonDao.mergeAndFlush(newSampleTask);
            newSampleTaskList.add(newSampleTask);
        }
        return newSampleTaskList;
    }

    /**
     * 设置检测组相关数据：取样点、送样点、包装方式
     * <AUTHOR>
     * @date 2025/5/13 11:03
     * @param qcTaskDetail 质控任务明细
     * @param sampleTask 样品任务
     */
    private void setGroupRelatedData(QcTaskDetail qcTaskDetail, SampleTask sampleTask) {
        // 获取检测组
        InspectionItemGroupMTO group = qcTaskDetail.getInspectionItemGroupMTO();
        InspectionItemGroupMTO groupDb = fabosJsonDao.getById(InspectionItemGroupMTO.class,
                group.getId());
        if (groupDb != null) {
            sampleTask.setSamplePoint(groupDb.getSamplePoint());
            if (groupDb.getSendPointMTO() != null) {
                sampleTask.setSendPoint(groupDb.getSendPointMTO().getName());
            }
            sampleTask.setPackageType(groupDb.getPackageType());
        }
    }

    /**
     * 设置样品任务明细数据
     * <AUTHOR>
     * @date 2025/3/31 15:04
     * @param expMethod
     * @param sampleTaskDetail
     */
    public void setSampleTaskDetail(ExpMethod expMethod, SampleTaskDetail sampleTaskDetail, QcTaskDetail qcTaskDetail) {
        // 试验方法id
        sampleTaskDetail.setExpMethodId(expMethod.getId());
        // 试验方法名称、编码
        sampleTaskDetail.setTestMethodName(expMethod.getName());
        sampleTaskDetail.setTestMethodCode(expMethod.getGeneralCode());
        // 试验设备
        sampleTaskDetail.setTestDevice(expMethod.getEquipment() != null ? expMethod.getEquipment().getName() : null);
        sampleTaskDetail.setTestDeviceCode(expMethod.getEquipmentCode());
        sampleTaskDetail.setInspectStepDescription(expMethod.getInspectStepDescription());
        // 样本量： 数据源于质控任务明细单的样本量
        sampleTaskDetail.setSampleSize(qcTaskDetail.getSampleQuantity());
        // 装载检测指标
        List<SampleTaskDetailQuota> sampleTaskDetailQuotas = new ArrayList<>();
        List<ExpMethodDetail> expMethodDetails = expMethod.getExpMethodDetails();
        for (ExpMethodDetail expMethodDetail : expMethodDetails) {
            SampleTaskDetailQuota quota = new SampleTaskDetailQuota();
            BeanUtils.copyProperties(expMethodDetail, quota);
            quota.setId(null);
            sampleTaskDetailQuotas.add(quota);
        }
        sampleTaskDetail.setSampleTaskDetailQuotas(sampleTaskDetailQuotas);
    }



    /**
     * 根据质控任务设置样品任务基本信息
     * <AUTHOR>
     * @date 2025/3/31 14:18
     * @param qcTask
     * @return
     */
    @NotNull
    private SampleTask setBaseSampleTask(QcTask qcTask) {
        SampleTask newSampleTask = new SampleTask();
        // 物料名称、物料编码、型号规格
        newSampleTask.setMaterialName(qcTask.getMaterialName());
        newSampleTask.setMaterialCode(qcTask.getMaterialCode());
        SpecificationManageMTO specificationManage = comTaskGenHandler.getSpecificationManage(qcTask.getMaterialCode(),
                qcTask.getMaterialName());
        if (specificationManage != null) {
            newSampleTask.setModelSpecification(specificationManage.getType());
        }
        // 物料批次号: 如果是库存、则设置物料批次号
        if (INVENTORY.equals(qcTask.getSampleType())) {
            WmsInventoryMTO wmsInventoryMTO = qcTask.getWmsInventoryMTO();
            WmsInventoryMTO inventoryDb = fabosJsonDao.getById(WmsInventoryMTO.class, wmsInventoryMTO.getId());
            newSampleTask.setMaterialBatchNo(inventoryDb.getInventoryLotId());
        }
        if (KEEP_SAMPLE.equals(qcTask.getSampleType())) {
            KsInventory ksInventory = qcTask.getKsInventory();
            KsInventory ksInventoryDb = fabosJsonDao.getById(KsInventory.class, ksInventory.getId());
            newSampleTask.setMaterialBatchNo(ksInventoryDb.getSampleTaskNo());
        }
        // 检测类型、取样总量
        newSampleTask.setCheckType(CheckTypeEnum.Enum.CONTROL.name());
        newSampleTask.setTotalQuantity(qcTask.getTotalSampleQuantity());
        // 是否分样、是否加急、是否留样、任务状态、取样人、取样时间
        newSampleTask.setIsSplitSample(YesOrNoStatus.NO.getValue());
        newSampleTask.setIsUrgent(qcTask.getIsUrgent());
        newSampleTask.setIsLeave(YesOrNoStatus.NO.getValue());
        newSampleTask.setTaskStatus(TaskStatusEnum.Enum.TO_BE_PICKED_UP.name());
        // 样品类型
        if (KEEP_SAMPLE.equals(qcTask.getSampleType())) {
            newSampleTask.setSampleType(SampleTypeEnum.Enum.QC_KEEP_SAMPLING.name());
        }
        if (INVENTORY.equals(qcTask.getSampleType())) {
            newSampleTask.setSampleType(SampleTypeEnum.Enum.QC_INVENTORY_SAMPLING.name());
        }
        if (PRODUCT_LINE.equals(qcTask.getSampleType())) {
            newSampleTask.setSampleType(SampleTypeEnum.Enum.QC_SAMPLING.name());
        }
        // 样品单来源
        newSampleTask.setSampleTaskSource(SampleTaskSourceEnum.Enum.LIMS.name());
        // 样品来源
        newSampleTask.setSampleSource(qcTask.getSampleSource());
        return newSampleTask;
    }
}
