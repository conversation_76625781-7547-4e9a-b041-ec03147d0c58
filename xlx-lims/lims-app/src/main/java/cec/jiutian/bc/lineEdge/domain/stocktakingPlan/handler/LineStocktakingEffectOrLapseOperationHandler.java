package cec.jiutian.bc.lineEdge.domain.stocktakingPlan.handler;

import cec.jiutian.bc.device.domain.insPlan.model.DeviceInsPlan;
import cec.jiutian.bc.keepSample.domain.stocktakingPlan.model.KsStocktakingPlan;
import cec.jiutian.bc.keepSample.domain.stocktakingPlan.utils.StocktakingDateUtil;
import cec.jiutian.bc.keepSample.enumeration.EffectOrLapseEnum;
import cec.jiutian.bc.keepSample.enumeration.StocktakingCycleEnum;
import cec.jiutian.bc.lineEdge.domain.stocktakingOrder.service.LineStocktakingOrderService;
import cec.jiutian.bc.lineEdge.domain.stocktakingPlan.model.LineStocktakingPlan;
import cec.jiutian.core.frame.constant.YesOrNoStatus;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/28
 * @description
 */
@Component
public class LineStocktakingEffectOrLapseOperationHandler implements OperationHandler<LineStocktakingPlan,Void> {

    @Resource
    private FabosJsonDao fabosJsonDao;
    @Resource
    private LineStocktakingOrderService lineStocktakingOrderService;

    @Override
    public String exec(List<LineStocktakingPlan> data, Void modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            LineStocktakingPlan lineStocktakingPlan = data.get(0);
            String state = param[0];
            lineStocktakingPlan.setStocktakingPlanState(state);
            this.checkPlan(lineStocktakingPlan);
            //生效过后需要校验 下一次生成任务的日期
            if(EffectOrLapseEnum.Enum.EFFECT.name().equals(state)){
                this.handleNextGenerateDate(lineStocktakingPlan);
            }
            fabosJsonDao.mergeAndFlush(lineStocktakingPlan);
        }
        return "msg.success('操作成功')";
    }
    private void checkPlan(LineStocktakingPlan plan){
        if(EffectOrLapseEnum.Enum.EFFECT.name().equals(plan.getStocktakingPlanState())&& plan.getStocktakingPlanEndDate().before(new Date())){
            throw new FabosJsonApiErrorTip("已过计划截止日期，不能生效");
        }
    }
    /**
     * 1. 生效时，需要判断下一次生成任务的日期是否有值。
     *          如果没值，说明是第一次生效计划。计划开始时间和生效时间（当前时间）比较。比如计划开始时间在当前时间之前，生成任务，下次生成时间就是当前时间的周期之后。计划时间在当前时间之后，就下次生成时间就是计划时间开始时间
     *          如果有值，说明不是第一次生效计划。中途失效过计划，因此需要判断下一次生成任务的日期是否在当前时间之前，如果是，生成任务，并需要更新下一次生成任务的日期为当前时间的下一个周期时间。如果不是，则不更新下一次任务生成时间
     *
     * 2. 如果下一次生成任务的时间是当前时间。那么立马生成任务，并按照周期生成下一次的任务生成时间
     */
    private void handleNextGenerateDate(LineStocktakingPlan ksStocktakingPlan){
        Date nowDate = new Date();
        Date stocktakingPlanStartDate = ksStocktakingPlan.getStocktakingPlanStartDate();
        if(ksStocktakingPlan.getNextGenerateDate()==null){
            if(nowDate.after(stocktakingPlanStartDate)){
                //当前生效时间在计划开始时间之后  生成任务
                this.handleGenerateOrder(ksStocktakingPlan,nowDate);
            }else {
                ksStocktakingPlan.setNextGenerateDate(stocktakingPlanStartDate);
            }
        }else {
            if(nowDate.after(ksStocktakingPlan.getNextGenerateDate())){
                //当前生效时间在下一次生成任务时间之后  说明可以生成任务了并更新下次生成任务时间
                this.handleGenerateOrder(ksStocktakingPlan,nowDate);
            }
            //不处理下一次生成任务时间
        }


    }
    //处理生成任务
    private void handleGenerateOrder(LineStocktakingPlan ksStocktakingPlan, Date nowDate){
        lineStocktakingOrderService.generateStocktakingOrder(ksStocktakingPlan);
        ksStocktakingPlan.setNextGenerateDate(StocktakingDateUtil.addTime(nowDate, StocktakingCycleEnum.Enum.valueOf(ksStocktakingPlan.getStocktakingCycle())));
    }
}
