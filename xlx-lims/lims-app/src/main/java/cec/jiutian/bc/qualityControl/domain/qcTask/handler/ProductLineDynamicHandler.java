package cec.jiutian.bc.qualityControl.domain.qcTask.handler;

import cec.jiutian.bc.qualityControl.domain.qcTask.model.QcTask;
import cec.jiutian.bc.qualityControl.domain.qcTask.mto.InspectionStandardMTO;
import cec.jiutian.bc.qualityControl.enumeration.QcSampleTypeEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class ProductLineDynamicHandler implements DependFiled.DynamicHandler<QcTask> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    // 样品类型-产线

    private static final String PRODUCT_LINE = QcSampleTypeEnum.Enum.PRODUCT_LINE.name();

    @Override
    public Map<String, Object> handle(QcTask qcTask) {
        HashMap<String, Object> map = new HashMap<>();
        String sampleType = qcTask.getSampleType();
        InspectionStandardMTO std = qcTask.getInspectionStandardMTO();
        if (PRODUCT_LINE.equals(sampleType) && std != null) {
            InspectionStandardMTO insStdDb = fabosJsonDao.getById(InspectionStandardMTO.class, std.getId());
            if (insStdDb != null) {
                map.put("factoryAreaId", insStdDb.getFactoryAreaId());
                map.put("factoryAreaName", insStdDb.getFactoryAreaName());
                map.put("factoryLineId", insStdDb.getFactoryLineId());
                map.put("factoryLineName", insStdDb.getFactoryLineName());
                map.put("processCode", insStdDb.getProcessCode());
                map.put("processName", insStdDb.getProcessName());
            }
        } else {
            map = this.emptyMap(map);
        }
        return map;
    }

    private HashMap<String, Object> emptyMap(HashMap<String, Object> map) {
        map.put("factoryAreaId", "");
        map.put("factoryAreaName", "");
        map.put("factoryLineId", "");
        map.put("factoryLineName", "");
        map.put("processCode", "");
        map.put("processName", "");
        return map;
    }
}
