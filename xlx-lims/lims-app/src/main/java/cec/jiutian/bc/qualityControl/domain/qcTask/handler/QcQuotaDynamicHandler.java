package cec.jiutian.bc.qualityControl.domain.qcTask.handler;

import cec.jiutian.bc.compare.util.BcUtil;
import cec.jiutian.bc.qualityControl.domain.qcTask.model.QcTaskDetailQuota;
import cec.jiutian.bc.qualityControl.enumeration.QcTaskDetailResultEnum;
import cec.jiutian.view.DependFiled;
import com.alibaba.cloud.commons.lang.StringUtils;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class QcQuotaDynamicHandler implements DependFiled.DynamicHandler<QcTaskDetailQuota> {
    @Resource
    private BcUtil bcUtil;

    @Override
    public Map<String, Object> handle(QcTaskDetailQuota qcQuota) {
        HashMap<String, Object> resultMap = new HashMap<>();
        String inspectValue = qcQuota.getInspectValue();
        if (StringUtils.isBlank(inspectValue)) {
            resultMap.put("inspectResult", "");
            return resultMap;
        }
        // 如果检测指标不合规范，则跳过
        boolean validQuota = bcUtil.isValidQuota(qcQuota.getComparisonMethod(), qcQuota.getStandardValue(),
                qcQuota.getUpperValue(), qcQuota.getLowerValue(), inspectValue);
        if (!validQuota) {
            resultMap.put("inspectResult", "");
            return resultMap;
        }
        boolean qualifiedQuota = bcUtil.isQualifiedQuota(qcQuota.getComparisonMethod(), qcQuota.getStandardValue(),
                qcQuota.getUpperValue(), qcQuota.getLowerValue(), inspectValue);
        if (qualifiedQuota) {
            resultMap.put("inspectResult", QcTaskDetailResultEnum.Enum.PASS.name());
        } else {
            resultMap.put("inspectResult", QcTaskDetailResultEnum.Enum.NOT_PASS.name());
        }
        return resultMap;
    }
}
