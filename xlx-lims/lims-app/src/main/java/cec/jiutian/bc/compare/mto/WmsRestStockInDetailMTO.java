package cec.jiutian.bc.compare.mto;

import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.View;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@FabosJson(name = "WMS其他入库明细MTO")
@Entity
@Getter
@Setter
@Table(name = "purchase_stock_in_detail")
public class WmsRestStockInDetailMTO extends MetaModel {
    @FabosJsonField(
            views = @View(title = "入库明细编号"),
            edit = @Edit(title = "采购入库明细编号")
    )
    private String purchaseStockInDetailCode;

    @FabosJsonField(
            views = @View(title = "来源单明细编号", show = false)
    )
    private String originDetailCode;

    @FabosJsonField(
            views = @View(title = "物料主键id", show = false),
            edit = @Edit(title = "物料主键id", show = false)
    )
    private String materialId;

    @FabosJsonField(
            views = @View(title = "物料编码"),
            edit = @Edit(title = "物料编码")
    )
    private String materialCode;

    @FabosJsonField(
            views = @View(title = "物料名称"),
            edit = @Edit(title = "物料名称")
    )
    private String materialName;

    @FabosJsonField(
            views = @View(title = "规格"),
            edit = @Edit(title = "规格")
    )
    private String materialSpecification;

    @FabosJsonField(
            views = @View(title = "来料批号"),
            edit = @Edit(title = "来料批号")
    )
    private String materialLotIdentifier;

    @FabosJsonField(
            views = @View(title = "本厂批号"),
            edit = @Edit(title = "本厂批号")
    )
    private String factoryLotIdentifier;

    @FabosJsonField(
            views = @View(title = "入库数量"),
            edit = @Edit(title = "入库数量")
    )
    private Double quantity;

    @FabosJsonField(
            views = @View(title = "单位"),
            edit = @Edit(title = "单位")
    )
    private String unit;

    @FabosJsonField(
            views = @View(title = "是否样品"),
            edit = @Edit(title = "是否样品")
    )
    private String sampleFlag;
}
