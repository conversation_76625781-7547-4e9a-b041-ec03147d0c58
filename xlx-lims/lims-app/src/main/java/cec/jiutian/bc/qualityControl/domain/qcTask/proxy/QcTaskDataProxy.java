package cec.jiutian.bc.qualityControl.domain.qcTask.proxy;

import cec.jiutian.bc.base.domain.lab.model.Lab;
import cec.jiutian.bc.mto.FactoryArea;
import cec.jiutian.bc.mto.ProductProcess;
import cec.jiutian.bc.person.domain.personManage.model.PersonManage;
import cec.jiutian.bc.qualityControl.domain.qcTask.model.QcTask;
import cec.jiutian.bc.qualityControl.domain.qcTask.model.QcTaskDetail;
import cec.jiutian.bc.qualityControl.enumeration.QcSampleTypeEnum;
import cec.jiutian.bc.qualityControl.enumeration.QcTaskStateEnum;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.common.util.StringUtils;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Optional;

@Component
public class QcTaskDataProxy implements DataProxy<QcTask> {
    @Resource
    private FabosJsonDao fabosJsonDao;


    @Override
    public void beforeAdd(QcTask qcTask) {
        // 校验参数
        this.checkParam(qcTask);
        qcTask.setCreTime(new Date());
        qcTask.setCurrentState(QcTaskStateEnum.Enum.UNDER_RELEASED.name());
        // 设置参数
        this.setParam(qcTask);
    }

    /**
     * 设置参数：创建人、样本来源、取样量
     * <AUTHOR>
     * @date 2025/5/30 16:28
     * @param qcTask
     */
    private void setParam(QcTask qcTask) {
        String takeSample = qcTask.getTakeSample();
        // 装载创建人
        String hql = "FROM PersonManage pm WHERE pm.user.id = :userId";
        EntityManager entityManager = fabosJsonDao.getEntityManager();
        TypedQuery<PersonManage> query = entityManager.createQuery(hql, PersonManage.class);
        query.setParameter("userId", UserContext.getUserId());
        List<PersonManage> personManageList = query.getResultList();
        if (!CollectionUtils.isEmpty(personManageList)) {
            qcTask.setCreatePerson(personManageList.get(0));
        }
        // 设置样品来源
        if ("Y".equals(takeSample)) {
            String sampleSource = this.getSampleSource(qcTask);
            qcTask.setSampleSource(sampleSource);
        }
        // 设置取样量
        double sum = qcTask.getQcTaskDetails().stream()
                .mapToDouble(qcTaskDetail -> qcTaskDetail.getSampleQuantity() != null ?
                        qcTaskDetail.getSampleQuantity() : 0.0)
                .sum();
        qcTask.setTotalSampleQuantity(sum);
        // 装载所属检测室名称、所属实验室名称
        String labQueryHql = "from Lab l " +
                "join QcLedger ql on ql.lab.id = l.id " +
                "join QcPlan qp on qp.qcLedger.id = ql.id " +
                "where qp.id = :qcPlanId ";
        List<Lab> labList = entityManager.createQuery(labQueryHql, Lab.class)
                .setParameter("qcPlanId", qcTask.getQcPlan().getId())
                .getResultList();
        if (CollectionUtils.isNotEmpty(labList)) {
            Lab lab = labList.get(0);
            qcTask.setBelongLabName(lab.getLabName());
            qcTask.setBelongExpLabName(lab.getExpLab().getExpLabName());
        }
        // 车间-产线-工序
        if (QcSampleTypeEnum.Enum.PRODUCT_LINE.name().equals(qcTask.getSampleType())) {
            if (qcTask.getFactoryArea() != null) {
                qcTask.setFactoryAreaId(qcTask.getFactoryArea().getId().toString());
                qcTask.setFactoryAreaName(qcTask.getFactoryArea().getFactoryAreaName());
            }
            if (qcTask.getFactoryLine() != null) {
                qcTask.setFactoryLineId(qcTask.getFactoryLine().getId().toString());
                qcTask.setFactoryLineName(qcTask.getFactoryLine().getFactoryAreaName());
            }
            if (qcTask.getProcess() != null) {
                qcTask.setProcessCode(qcTask.getProcess().getCode());
                qcTask.setProcessName(qcTask.getProcess().getName());
            }
        }
    }

    /**
     * 获取样本来源
     * <AUTHOR>
     * @date 2025/5/12 14:26
     * @param qcTask
     * @return
     */
    public String getSampleSource(QcTask qcTask) {
        String sampleType = qcTask.getSampleType();
        String res = null;
        switch (sampleType) {
            case "KEEP_SAMPLE":
                res = "留样库存";
                break;
            case "PRODUCT_LINE":
                // 车间-产线-工序
                String areaName = Optional.ofNullable(qcTask.getFactoryArea())
                        .map(FactoryArea::getFactoryAreaName)
                        .orElse("");
                String lineName = Optional.ofNullable(qcTask.getFactoryLine())
                        .map(FactoryArea::getFactoryAreaName)
                        .orElse("");
                String processName = Optional.ofNullable(qcTask.getProcess())
                        .map(ProductProcess::getName)
                        .orElse("");
                res = String.join("-", areaName, lineName, processName);
                break;
            case "INVENTORY":
                // 仓库名称
                res = qcTask.getWmsInventoryMTO().getWarehouseName();
                break;
        }
        return res;
    }

    @Override
    public void beforeUpdate(QcTask qcTask) {
        this.checkParam(qcTask);
        this.setParam(qcTask);
    }

    private void checkParam(QcTask qcTask) {
        List<QcTaskDetail> qcTaskDetails = qcTask.getQcTaskDetails();
        if (CollectionUtils.isEmpty(qcTaskDetails)) {
            throw new FabosJsonApiErrorTip("质控任务缺失明细数据！请确认！");
        }
        qcTaskDetails.forEach(qcTaskDetail -> {
            if (qcTaskDetail.getInspectionItemGroupMTO() == null) {
                throw new FabosJsonApiErrorTip("检测组不能为空，请在编辑中添加检测组数据！");
            } else {
                if (StringUtils.isBlank(qcTaskDetail.getGroupId())) {
                    qcTaskDetail.setGroupId(qcTaskDetail.getInspectionItemGroupMTO().getId());
                }
            }
            if (qcTaskDetail.getSampleQuantity() == null) {
                throw new FabosJsonApiErrorTip("样本数量不能为空，请在编辑中添加样本数量！");
            } else {
                if (qcTaskDetail.getSampleQuantity() <= 0) {
                    throw new FabosJsonApiErrorTip("样本数量不能小于或等于0，请确认！");
                }
            }
            // 质控任务明细中的检测项指标不能为空
            if (CollectionUtils.isEmpty(qcTaskDetail.getQcTaskDetailQuotas())) {
                throw new FabosJsonApiErrorTip("质控任务明细中检测指标为空，无法创建任务，请确认！");
            }
        });
    }
}
