package cec.jiutian.bc.qualityControl.util;

import cec.jiutian.bc.qualityControl.constant.QcConstant;
import cec.jiutian.bc.qualityControl.dto.QcQueryDTO;
import cec.jiutian.bc.qualityControl.vo.QcTaskVO;
import cec.jiutian.bc.qualityControl.vo.QueryVO;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import jakarta.annotation.Resource;
import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class QcUtil {
    @Resource
    private FabosJsonDao fabosJsonDao;

    /**
     * 获取通用查询列表: 下拉选项查询value, label
     * <AUTHOR>
     * @date 2025/6/11 14:00
     * @return
     */
    public List<QueryVO> getCommonQueryList(String hql) {
        List<QueryVO> queryVOList = new ArrayList<>();
        EntityManager entityManager = fabosJsonDao.getEntityManager();
        Query query = entityManager.createQuery(hql);
        List resultList = query.getResultList();
        if (resultList.isEmpty()) {
            return queryVOList;
        }for (Object o : resultList) {
            if (o instanceof Object[]) {
                QueryVO queryVO = new QueryVO();
                Object[] resultArray = (Object[]) o;
                String value = (String) resultArray[0];
                String label = (String) resultArray[1];
                queryVO.setValue(value);
                queryVO.setLabel(label);
                queryVOList.add(queryVO);
            }
        }
        return queryVOList;
    }

    /**
     * 获取通用查询结果列表: 下拉选项查询value, label
     * <AUTHOR>
     * @date 2025/6/13 16:28
     * @param sql 源sql
     * @param type 查询结果类型 1 实验室; 2 检测室
     * @return
     */
    public List<QcTaskVO> getCommonResltVOList(String sql, String type) {
        List<QcTaskVO> qcTaskVOList = new ArrayList<>();
        EntityManager entityManager = fabosJsonDao.getEntityManager();
        Query query = entityManager.createNativeQuery(sql);
        List resultList = query.getResultList();
        if (resultList.isEmpty()) {
            return qcTaskVOList;
        }
        // 映射查询结果
        for (Object o : resultList) {
            if (o instanceof Object[]) {
                QueryVO queryVO = new QueryVO();
                Object[] resultArray = (Object[]) o;
                String label = (String) resultArray[0];
                String value1 = String.valueOf(resultArray[1]);
                String value2 = String.valueOf(resultArray[2]);
                QcTaskVO qcTaskVOByType = this.createQcTaskVOByType(label, value1, value2, type);
                qcTaskVOList.add(qcTaskVOByType);
            }
        }
        return qcTaskVOList;
    }

    /**
     * 创建QcTaskVO对象
     * <AUTHOR>
     * @date 2025/6/13 16:49
     * @param label 标签： 实验室、检测室
     * @param value1 超限数量
     * @param value2 超限比例
     * @param type 类型 1实验室; 2检测室
     * @return
     */
    private QcTaskVO createQcTaskVOByType(String label, String value1, String value2, String type) {
        QcTaskVO vo = new QcTaskVO();
        switch (type) {
            case "1": vo.setExpLabName(label); break;
            case "2": vo.setLabName(label); break;

        }
        vo.setOutCount(value1);
        vo.setTotalRatio(value2);
        return vo;
    }


    /**
     * 根据查询条件获取时间序列字符串sql
     * <AUTHOR>
     * @date 2025/6/11 15:25
     * @param qcQueryDTO 查询条件
     * @return
     */
    public String getDateSeriesSql(QcQueryDTO qcQueryDTO) {
        String isDiyTime = qcQueryDTO.getIsDiyTime();
        String startTime = qcQueryDTO.getStartTime();
        String endTime = qcQueryDTO.getEndTime();
        String query = qcQueryDTO.getTimeType();
        StringBuilder sql = new StringBuilder();
        sql.append("WITH date_series AS (\n");
        sql.append("    SELECT\n");
        sql.append("        generate_series(\n");
        sql.append("            ");
        if ("2".equals(isDiyTime)) {
            sql.append("-- 自定义时间范围（按天）\n");
            sql.append("            TO_DATE('").append(startTime).append("', 'YYYY-MM-DD'),\n");
            sql.append("            TO_DATE('").append(endTime).append("', 'YYYY-MM-DD'),\n");
            sql.append("            INTERVAL '1 day'");
        } else if ("1".equals(query)) {
            sql.append("-- 本年数据（按天展示）\n");
            sql.append("            DATE_TRUNC('year', CURRENT_DATE),\n");
            sql.append("            DATE_TRUNC('year', CURRENT_DATE) + INTERVAL '1 year - 1 day',\n");
            sql.append("            INTERVAL '1 day'  -- 改为按天生成");
        } else if ("2".equals(query)) {
            sql.append("-- 本季度数据（按天展示）\n");
            sql.append("            DATE_TRUNC('quarter', CURRENT_DATE),\n");
            sql.append("            DATE_TRUNC('quarter', CURRENT_DATE) + INTERVAL '3 months - 1 day',\n");
            sql.append("            INTERVAL '1 day'  -- 改为按天生成");
        } else if ("3".equals(query)) {
            sql.append("-- 本月数据（按天展示）\n");
            sql.append("            DATE_TRUNC('month', CURRENT_DATE),\n");
            sql.append("            DATE_TRUNC('month', CURRENT_DATE) + INTERVAL '1 month - 1 day',\n");
            sql.append("            INTERVAL '1 day'");
        } else {
            sql.append("-- 默认本周数据（按天）\n");
            sql.append("            DATE_TRUNC('week', CURRENT_DATE),\n");
            sql.append("            DATE_TRUNC('week', CURRENT_DATE) + INTERVAL '6 days',\n");
            sql.append("            INTERVAL '1 day'");
        }
        sql.append("\n        )\\:\\:date AS date_value\n");
        sql.append("),");
        return sql.toString();
    }

    /**
     * 根据全部时间类型 返回时间查询条件
     * <AUTHOR>
     * @date 2025/6/12 10:40
     * @param timeType 全部时间：1 本年度； 2 本季度； 3 本月度； 4 本周
     * @return
     */
    public String getTimeQueryCondition(String timeType) {
        switch (timeType) {
            // 本年度
            case "1":
                return " between date_trunc('year', CURRENT_DATE) " +
                        "and date_trunc('year', CURRENT_DATE) + interval '1 year'";
            // 本季度
            case "2":
                return " between date_trunc('quarter', CURRENT_DATE) " +
                        "and date_trunc('quarter', CURRENT_DATE) + interval '3 months'";
            // 本月度
            case "3":
                return " between date_trunc('month', CURRENT_DATE) " +
                        "and date_trunc('month', CURRENT_DATE) + interval '1 month'";
            // 默认 本周
            default:
                return " between date_trunc('week', CURRENT_DATE) " +
                        "and date_trunc('week', CURRENT_DATE) + interval '7 days'";
        }
    }

    /**
     * 根据查询类型设置参考线
     * <AUTHOR>
     * @date 2025/6/16 17:34
     * @param qcTaskVO 查询结果数据
     * @param type 查询类型： 短期、长期、准确性
     */
    public void setReferLineByType(QcTaskVO qcTaskVO, String type) {
        switch (type) {
            case "SHORT_TERM_STABILITY":
                qcTaskVO.setReferLine1(QcConstant.SHORT_QC_REFER_LINE_1);
                return;
            case "LONG_TERM_STABILITY":
                qcTaskVO.setReferLine1(QcConstant.LONG_QC_REFER_LINE_1);
                qcTaskVO.setReferLine2(QcConstant.LONG_QC_REFER_LINE_2);
                return;
            case "LONG_TERM_ACCURACY":
                qcTaskVO.setReferLine1(QcConstant.ACC_QC_REFER_LINE_1);
                qcTaskVO.setReferLine2(QcConstant.ACC_QC_REFER_LINE_2);
                return;
        }
    }

}
