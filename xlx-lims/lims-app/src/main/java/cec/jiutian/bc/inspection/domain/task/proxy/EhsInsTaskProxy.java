package cec.jiutian.bc.inspection.domain.task.proxy;

import cec.jiutian.bc.inspection.domain.task.model.EhsInsTask;
import cec.jiutian.bc.inspection.domain.task.model.EhsInsTaskDetail;
import cec.jiutian.bc.inspection.enumeration.EhsInsTaskSourceEnum;
import cec.jiutian.bc.inspection.enumeration.EhsInsTaskStateEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class EhsInsTaskProxy implements DataProxy<EhsInsTask> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public void beforeAdd(EhsInsTask ehsInsTask) {
        // 任务来源、巡检状态
        ehsInsTask.setTaskSource(EhsInsTaskSourceEnum.Enum.SELF_BUILD.name());
        // 如果是临时任务，那么任务状态就是巡检中
        ehsInsTask.setCurrentState(EhsInsTaskStateEnum.Enum.PROCESSING.name());
        // 设置是否跳过巡检为否
        ehsInsTask.setIsSkip("N");
        // 设置任务子单可编辑状态为1
        for (EhsInsTaskDetail ehsInsTaskDetail : ehsInsTask.getEhsInsTaskDetails()) {
            ehsInsTaskDetail.setIsEditable("1");
        }
    }
}
