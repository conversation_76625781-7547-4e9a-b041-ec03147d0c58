package cec.jiutian.bc.qualityControl.domain.qcTask.mto;

import cec.jiutian.bc.modeler.domain.dto.SendPointMTO;
import cec.jiutian.bc.modeler.domain.inspectionItem.model.InspectionItem;
import cec.jiutian.bc.qualityControl.enumeration.GroupType;
import cec.jiutian.bc.qualityControl.enumeration.InsGroupStatusEnum;
import cec.jiutian.bc.urm.domain.dictionary.handler.DictChoiceFetchHandler;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.field.edit.TabTableReferType;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.Column;
import jakarta.persistence.ConstraintMode;
import jakarta.persistence.Entity;
import jakarta.persistence.ForeignKey;
import jakarta.persistence.Index;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * ‌检测项目组核心属性管理‌
 * <p>
 * 需包含组编号、名称、使用部门、组类型、备注字段‌1
 * <p>
 * 组编号需唯一且支持自动生成规则（如G-YYYYMMDD-001）‌2
 * <p>
 * 支持多部门数据隔离（如质量部仅管理本部门的检测项目组）‌3
 * ‌检测项目动态关联‌
 * <p>
 * 支持多对多关联检测项目（可动态增减项目）‌1
 * 检测项目调整仅允许在创建/编辑状态下操作‌4
 * ‌生命周期状态控制‌
 * <p>
 * 状态流转：创建 → 编辑 → 生效 → 失效‌5
 * 生效状态的组不可编辑/删除，仅失效后可删除‌6
 * ‌数据操作支持‌
 * <p>
 * 支持复制已有组生成新组（需深拷贝关联的检测项目）‌1
 * 提供批量导入功能（需校验部门及项目有效性）‌
 */
@Getter
@Setter
@Entity
@Table(name = "inspection_item_group",
        indexes = {
                @Index(name = "idx_group_code", columnList = "group_code", unique = true),  // 组编号唯一索引
                @Index(name = "idx_department_id", columnList = "department_id")                 // 部门查询优化
        })
@FabosJson(
        name = "检测项目组",
        orderBy = "InspectionItemGroupMTO.createdAt desc"
)
public class InspectionItemGroupMTO extends MetaModel {

    // // 组编号（系统生成规则）
    @FabosJsonField(
            views = @View(title = "组编号"),
            edit = @Edit(title = "组编号")
    )
    @Column(name = "group_code", nullable = false, length = 50)
    private String groupCode;

    // 组名称
    @FabosJsonField(
            views = @View(title = "组名称"),
            edit = @Edit(title = "组名称",
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "group_name", nullable = false, length = 100)
    private String groupName;

    // 组类型（IQC/OQC等）
    @FabosJsonField(
            views = @View(title = "组类型"),
            edit = @Edit(title = "组类型",
                    notNull = true,
                    type = EditType.CHOICE,
                    search = @Search(vague = true),
                    choiceType = @ChoiceType(fetchHandler = GroupType.ChoiceFetch.class)
            )
    )
    @Column(name = "group_type", length = 20)
    private String groupType;


   /* @Transient
    @FabosJsonField(
            views = @View(title = "使用部门", show = false, column = "name"),
            edit = @Edit(title = "使用部门",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType()
            )
    )
    private OrgMTO orgMTO;*/

    // 使用部门（如"质量部"）
    @FabosJsonField(
            views = @View(title = "使用部门"),
            edit = @Edit(title = "使用部门",
                    search = @Search(vague = true),
                    readonly = @Readonly(add = true, edit = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "orgMTO", beFilledBy = "name"))
    )
    @Column(name = "department", length = 50)
    private String department;

    //部门ID
    @FabosJsonField(
            views = @View(title = "部门ID"),
            edit = @Edit(title = "部门ID",
                    search = @Search,
                    readonly = @Readonly(add = true, edit = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "orgMTO", beFilledBy = "id"))
    )
    @Column(name = "department_id", length = 50)
    private String departmentId;

    @FabosJsonField(
            views = @View(title = "包装方式"),
            edit = @Edit(title = "包装方式", notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = DictChoiceFetchHandler.class,
                            fetchHandlerParams = "packageType"),
                    search = @Search(vague = true)
            )
    )
    private String packageType;

    @FabosJsonField(
            views = @View(title = "取样点"),
            edit = @Edit(title = "取样点",
                    search = @Search(vague = true)
            )
    )
    //取样点
    @Column(name = "sample_point", length = 50)
    private String samplePoint;

    @FabosJsonField(
            views = @View(title = "送检点", column = "name"),
            edit = @Edit(title = "送检点", notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    queryCondition = "{\"type\":\"${sendPointType}\"}",
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType(type = TabTableReferType.SelectShowTypeMTM.LIST)
            )
    )
    @ManyToOne
    @JoinColumn(name = "send_point_mto_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private SendPointMTO sendPointMTO;


    @FabosJsonField(
            views = @View(title = "送样点"),
            edit = @Edit(title = "送样点",
                    search = @Search(vague = true)
            )
    )
    //送样点
    @Column(name = "send_point", length = 50)
    private String sendPoint;

    // 备注
    @FabosJsonField(
            views = @View(title = "备注"),
            edit = @Edit(title = "备注",
                    type = EditType.TEXTAREA
            )
    )
    @Column(name = "remark", length = 500)
    private String remark;

    // 状态（CREATED/EDIT/ACTIVE/INACTIVE）
    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",
                    readonly = @Readonly(
                            add = true,
                            edit = false
                    ),
                    defaultVal = "CREATED",
                    type = EditType.CHOICE,
                    search = @Search,
                    choiceType = @ChoiceType(fetchHandler = InsGroupStatusEnum.ChoiceFetch.class)
            )
    )
    @Column(name = "status", nullable = false, length = 20)
    private String status;

    // 创建人
    @FabosJsonField(
            views = @View(title = "创建人"),
            edit = @Edit(title = "创建人",
                    show = false,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "created_by", length = 50)
    private String createdBy;

    @Column(name = "created_by_id", length = 50)
    private String createdById;

    // 创建时间
    @FabosJsonField(
            views = @View(title = "创建时间"),
            edit = @Edit(title = "创建时间",
                    show = false
            )
    )
    @CreationTimestamp
    @Column(name = "created_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date createdAt;

    // 最后修改时间
    @FabosJsonField(
            views = @View(title = "最后修改时间"),
            edit = @Edit(title = "最后修改时间",
                    show = false
            )
    )
    @UpdateTimestamp
    @Column(name = "updated_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date updatedAt;
    @FabosJsonField(
            views = @View(title = "单位"),
            edit = @Edit(title = "单位", notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = DictChoiceFetchHandler.class,
                            fetchHandlerParams = "USUAL_UNIT"),
                    search = @Search(vague = true)
            )
    )
    private String usualUnit;
    // 关联检测项目（多对多关系）
    @FabosJsonField(
            views = @View(title = "关联检测项目", column = "name"),
            edit = @Edit(title = "关联检测项目",
                    type = EditType.TAB_TABLE_REFER,
                    referenceTableType = @ReferenceTableType()
            )
    )
    @ManyToMany
    @JoinTable(name = "group_item_mapping",
            joinColumns = @JoinColumn(name = "group_id"),
            inverseJoinColumns = @JoinColumn(name = "item_id"))
    private List<InspectionItem> items;


    public void addItem(InspectionItem item) {
        if (CollectionUtils.isEmpty(this.items)) {
            ArrayList<InspectionItem> inspectionItems = new ArrayList<>(1);
            inspectionItems.add(item);
            this.setItems(inspectionItems);
        }
        for (InspectionItem inspectionItem : this.items) {
            if (inspectionItem.getId().equals(item.getId())) {
                return;
            }
        }
        this.items.add(item);
    }
}
