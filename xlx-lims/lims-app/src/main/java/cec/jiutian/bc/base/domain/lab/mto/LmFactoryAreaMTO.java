package cec.jiutian.bc.base.domain.lab.mto;

import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.QueryModel;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.Search;
import jakarta.persistence.Column;
import jakarta.persistence.Id;
import lombok.Data;

/**
 * 区域信息MTO: 排除掉已经被检测室绑定好了的生产区域
 * <AUTHOR>
 * @date 2025/4/24 9:12
 */
@FabosJson(name = "区域信息查询模型")
@Data
// 查询出设备台账中尚未被检测室绑定的记录, 排除掉已经被检测室绑定的设备
@QueryModel(hql = "select new map(f.id as id, f.factoryAreaName as factoryAreaName, f.factoryAreaCode as factoryAreaCode, " +
        "f.factoryAreaShortName as factoryAreaShortName, f.factoryAreaDescription as factoryAreaDescription ) " +
        "from FactoryArea f " +
        "${customWhere} and f.id not in (select cast(l.factoryAreaId as long) from Lab l where l.factoryAreaId is not null and l.factoryAreaId != '' )"
)
public class LmFactoryAreaMTO {

    @Id
    @FabosJsonField(
            edit = @Edit(title = "", show = false),
            customHqlField = "f.id"
    )
    private String id;

    @FabosJsonField(
            views = @View(title = "名称"),
            edit = @Edit(title = "名称",
                    notNull = true,
                    search = @Search(vague = true)
            ),
            customHqlField = "f.factoryAreaName"
    )
    @Column(name = "fctry_ara_nm", length = 40)
    private String factoryAreaName;

    @FabosJsonField(
            views = @View(title = "编码"),
            edit = @Edit(title = "编码",
                    notNull = true,
                    search = @Search(vague = true)
            ),
            customHqlField = "f.factoryAreaCode"
    )
    @Column(name = "fctry_ara_cd", length = 40)
    private String factoryAreaCode;

    @FabosJsonField(
            views = @View(title = "简称"),
            edit = @Edit(title = "简称",
                    notNull = true,
                    search = @Search(vague = true)
            ),
            customHqlField = "f.factoryAreaShortName"
    )
    @Column(name = "fctry_ara_shrt_nm", length = 40)
    private String factoryAreaShortName;

    @FabosJsonField(
            views = @View(title = "描述"),
            edit = @Edit(title = "描述",
                    notNull = true,
                    search = @Search(vague = true)
            ),
            customHqlField = "f.factoryAreaDescription"
    )
    @Column(name = "fctry_ara_ds", length = 400)
    private String factoryAreaDescription;
}
