package cec.jiutian.bc.process.domain.sample.handler;

import cec.jiutian.bc.process.domain.inspectionTask.model.InspectionTask;
import cec.jiutian.bc.process.domain.inspectionTask.service.InspectionTaskService;
import cec.jiutian.bc.process.domain.sample.model.SampleTask;
import cec.jiutian.bc.process.domain.sample.model.SampleTaskCOLL;
import cec.jiutian.bc.process.domain.sample.model.SampleTaskDetail;
import cec.jiutian.bc.process.domain.sample.model.SampleTaskDetailQuota;
import cec.jiutian.bc.process.domain.sample.service.SampleTaskService;
import cec.jiutian.bc.process.enumeration.*;
import cec.jiutian.common.util.BeanUtils;
import cec.jiutian.core.frame.constant.YesOrNoStatus;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class SampleTaskCOLLHandler implements OperationHandler<SampleTask, SampleTaskCOLL> {
    @Resource
    private FabosJsonDao fabosJsonDao;
    @Resource
    private SampleTaskService sampleTaskService;
    @Resource
    private InspectionTaskService inspectionTaskService;

    @Override
    @Transactional
    public String exec(List<SampleTask> data, SampleTaskCOLL sampleTaskCOLL, String[] param) {


        /**
         * 1. 收样根据sampleTaskCOLL参数。如果存在generalCode样品任务单号。说明是从lims创建的单子。只需要做部分参数的修改
         * 2. 如果不存在generalCode样品任务单号。说明是从QMS创建的单子。需要通过taskNo取样任务单号，然后创建新的样品任务单号
         *
         * 收样填写的参数主要是 复验数量  外观是否合格  是否分样
         *   如果是留样的样品任务单。收样完成过后就直接完成。然后从留样管理的入库去操作 入库流程
         *   外观不合格：状态为：        RECEIVED_SAMPLE("已收样（外观异常）"),   不管是否需要分样都不再进行分样了
         *   外观合格：
         *      需要分样：状态为：        DIVIDED_SAMPLE("待分样"),  分样完成过后为待检测
         *      不需要分样：状态为：      TO_BE_TESTED("待检测"),
         *
         */
        SampleTask query = new SampleTask();
        query.setGeneralCode(sampleTaskCOLL.getGeneralCode());
        SampleTask sampleTask = fabosJsonDao.selectOne(query);
        if(sampleTask!=null){
            //LIMS的单子
            log.info("lims单子收样：{}",sampleTaskCOLL.getGeneralCode());
            //状态为待收样的才可以收样
            if(!TaskStatusEnum.Enum.TO_BE_SAMPLED.name().equals(sampleTask.getTaskStatus())){
                throw new FabosJsonApiErrorTip("状态异常，待收样单据才可以进行收样");
            }
            if(sampleTask.getSampleTaskDetails().size()==1&&YesOrNoStatus.YES.getValue().equals(sampleTaskCOLL.getIsSplitSample())){
                throw new FabosJsonApiErrorTip("样品类型只存在一个检测项，不需要分样");
            }
            this.handlerCOLL(sampleTaskCOLL,sampleTask);
            //取样人和取样时间  qms
            sampleTask.setSampleTime(sampleTaskCOLL.getSampleTime());
            sampleTask.setSamplePerson(sampleTaskCOLL.getSamplePerson());
            //对标  质控 msa不允许分样   这三种样品任务在创建样品任务时就需要同步创建检测任务。因此不允许再分样
            if(CheckTypeEnum.Enum.COMPARE.name().equals(sampleTask.getCheckType())||CheckTypeEnum.Enum.CONTROL.name().equals(sampleTask.getCheckType())||CheckTypeEnum.Enum.MSA.name().equals(sampleTask.getCheckType())){
                sampleTask.setIsSplitSample(YesOrNoStatus.NO.getValue());
            }
            // 设置样品信息审核数据
            sampleTask.setInfoReview(sampleTaskCOLL.getInfoReview());
            fabosJsonDao.mergeAndFlush(sampleTask);
            //外观合格
            if(CheckResultEnum.Enum.QUALIFIED.name().equals(sampleTaskCOLL.getAppearanceCheck())){
                //lims单子收样。需要修改lims检测任务的状态。lims来的样品任务。一般都是通过质控、对标、msa等流程。这部分单子会同时创建样品任务和检测任务单子。这里修改状态即可
                this.updateInsTask(sampleTaskCOLL.getGeneralCode());
            }
        }else {
            //QMS的单子
            log.info("qms单子收样：{}，是否留样：{}",sampleTaskCOLL.getGeneralCode(),sampleTaskCOLL.getIsLeave());
            if("N".equals(sampleTaskCOLL.getIsLeave())&&sampleTaskCOLL.getSampleTaskDetails().size()==1&&YesOrNoStatus.YES.getValue().equals(sampleTaskCOLL.getIsSplitSample())){
                // 不留样   明细一个  还分样   报错提示
                throw new FabosJsonApiErrorTip("样品任务只存在一个检测项，不需要分样");
            }
            SampleTask add = getSampleTask(sampleTaskCOLL);
            //BeanUtils.copyNotEmptyProperties(sampleTaskCOLL,add);
            this.handlerCOLL(sampleTaskCOLL,add);
            fabosJsonDao.persistAndFlush(add);
            //外观合格
            if(YesOrNoStatus.NO.getValue().equals(sampleTaskCOLL.getIsLeave())&&CheckResultEnum.Enum.QUALIFIED.name().equals(sampleTaskCOLL.getAppearanceCheck())&&YesOrNoStatus.NO.getValue().equals(sampleTaskCOLL.getIsSplitSample())){
                //qms来的单子  如果收样合格并且不分样 不留样  就直接创建检测任务单子 状态 待检测   如果需要分样  就在分样操作下生成检测任务  留样的不允许分样也没有检测任务
                this.addInsTask(sampleTaskCOLL.getGeneralCode());
            }
        }
        //收样完成，如果外观检测合格 && 样品信息审核通过，同步收样数据到qms。如果不合格，需要在异常处置流程里面同步到qms
        if(CheckResultEnum.Enum.QUALIFIED.name().equals(sampleTaskCOLL.getAppearanceCheck())
                && InfoReviewEnum.Enum.Y.name().equals(sampleTaskCOLL.getInfoReview())){
            //合格
            sampleTaskService.receiveSampleResultToQMSByGeneralCode(sampleTaskCOLL.getGeneralCode());
        }
        return "success";
    }

    @Override
    public SampleTaskCOLL fabosJsonFormValue(List<SampleTask> data, SampleTaskCOLL fabosJsonForm, String[] param) {
        fabosJsonForm.setGeneralCode("");
        return OperationHandler.super.fabosJsonFormValue(data, fabosJsonForm, param);
    }

    /**
     * qms收样封装参数
     * @param sampleTaskCOLL
     * @return
     */
    private SampleTask getSampleTask(SampleTaskCOLL sampleTaskCOLL){
        SampleTask add = new SampleTask();
        BeanUtils.copyNotEmptyProperties(sampleTaskCOLL,add);
        add.setSampleTaskSource(SampleTaskSourceEnum.Enum.QMS.name());
        //留样没有明细
        if(YesOrNoStatus.YES.getValue().equals(sampleTaskCOLL.getIsLeave())){
            return add;
        }
        //明细
        List<SampleTaskDetail> sampleTaskDetails = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(sampleTaskCOLL.getSampleTaskDetails())){
            sampleTaskCOLL.getSampleTaskDetails().forEach(d->{
                SampleTaskDetail detail = new SampleTaskDetail();
                BeanUtils.copyNotEmptyProperties(d,detail);
//                if(CollectionUtils.isNotEmpty(d.getSampleTaskDetailQuotas())){
//                    //指标
//                    List<SampleTaskDetailQuota> sampleTaskDetailQuotas = new ArrayList<>();
//                    d.getSampleTaskDetailQuotas().forEach(q->{
//                        SampleTaskDetailQuota sampleTaskDetailQuotaAdd = new SampleTaskDetailQuota();
//                        BeanUtils.copyNotEmptyProperties(q, sampleTaskDetailQuotaAdd);
//                        sampleTaskDetailQuotas.add(sampleTaskDetailQuotaAdd);
//                    });
//                    detail.setSampleTaskDetailQuotas(sampleTaskDetailQuotas);
//                }
                sampleTaskDetails.add(detail);
            });
        }
        add.setSampleTaskDetails(sampleTaskDetails);
        return add;
    }

    /**
     * qms的样品任务单  收样合格自动创建检测任务单
     * @param sampleNo
     */
    private void addInsTask(String sampleNo){
        inspectionTaskService.createInsTaskBySampleTask(sampleNo);
    }

    /**
     * 调整检测任务单子状态
     * @param sampleNo
     */
    private void updateInsTask(String sampleNo){
        sampleTaskService.syncSampleTaskState(sampleNo);
    }

    /**
     * 处理收样逻辑
     * @param sampleTaskCOLL
     * @param sampleTask
     */
    private void handlerCOLL(SampleTaskCOLL sampleTaskCOLL,SampleTask sampleTask){
        sampleTask.setReceiveTime(new Date());
        sampleTask.setReceivePerson(sampleTaskCOLL.getReceivePerson());

        sampleTask.setCheckQuantity(sampleTaskCOLL.getCheckQuantity());
        //是否合格
        String appearanceCheck = sampleTaskCOLL.getAppearanceCheck();
        sampleTask.setAppearanceCheck(appearanceCheck);
        //是否分样
        String isSplitSample = sampleTaskCOLL.getIsSplitSample();
        sampleTask.setIsSplitSample(isSplitSample);

        if (YesOrNoStatus.YES.getValue().equals(sampleTask.getIsLeave())) {
            sampleTask.setTaskStatus(TaskStatusEnum.Enum.COMPLETE.name());
        } else {
            // 外观检测合格 && 样品信息审核通过
            if (CheckResultEnum.Enum.QUALIFIED.name().equals(appearanceCheck)
                    && InfoReviewEnum.Enum.Y.name().equals(sampleTaskCOLL.getInfoReview())) {
                sampleTask.setTaskStatus(
                        YesOrNoStatus.YES.getValue().equals(isSplitSample)
                                ? TaskStatusEnum.Enum.DIVIDED_SAMPLE.name()
                                : TaskStatusEnum.Enum.TO_BE_TESTED.name()
                );
            } else {
                // 不通过则设置状态 走不合格处理
                sampleTask.setTaskStatus(TaskStatusEnum.Enum.RECEIVED_SAMPLE_UNQUALIFIED.name());
            }
        }
    }



}
