package cec.jiutian.bc.process.remote.controller;

import cec.jiutian.api.remoteCallLog.dto.RemoteCallResult;
import cec.jiutian.bc.base.domain.experimentMethod.service.ExpMethodService;
import cec.jiutian.bc.base.domain.lab.service.LabService;
import cec.jiutian.bc.process.domain.inspectionTask.service.InspectionTaskRPTService;
import cec.jiutian.bc.process.remote.dto.RPTQuery;
import cec.jiutian.bc.process.remote.dto.RPTResult;
import cec.jiutian.bc.qualityControl.vo.QueryVO;
import cec.jiutian.core.frame.module.R;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description:  测试相关报表接口
 */
@RestController
@RequestMapping("/insTask")
public class LimsInsTaskRPTController {
    @Resource
    private LabService labService;
    @Resource
    private ExpMethodService expMethodService;
    @Resource
    private InspectionTaskRPTService inspectionTaskRPTService;

    /**
     * 所有实验室
     * @return
     */
    @RequestMapping("/getAllExpLabList")
    public RemoteCallResult<List<QueryVO>> getAllExpLabList() {
        return RemoteCallResult.success(labService.getAllExpLabList2());
    }

    /**
     * 检测组
     * @param requestBody
     * @return
     */
    @RequestMapping("/getAllLabListByExpLabName")
    public RemoteCallResult<List<QueryVO>> getAllLabListByExpLabName(@RequestBody Map<String, Object> requestBody) {
        List<String> expLabNames = (List<String>) requestBody.get("expLabNames");
        return RemoteCallResult.success(labService.getAllLabListByExpLabName(expLabNames));
    }

    /**
     * 检测项目
     * @param requestBody
     * @return
     */
    @RequestMapping("/getAllInsItemListByExpLabName")
    public RemoteCallResult<List<QueryVO>> getAllInsItemList(@RequestBody Map<String, Object> requestBody) {
        List<String> labNames = (List<String>) requestBody.get("labNames");
        return RemoteCallResult.success(expMethodService.getAllInsItemList(labNames));
    }
    /**
     * 所有物料类别
     * @return
     */
    @RequestMapping("/getAllSpecification")
    public RemoteCallResult<List<QueryVO>> getAllSpecification() {
        return RemoteCallResult.success(inspectionTaskRPTService.getAllSpecification());
    }

    @PostMapping("/getInsTaskRPTResult")
    public R<RPTResult> getInsTaskRPTResult(@RequestBody RPTQuery rptQuery) {
        return R.ok(inspectionTaskRPTService.getInsTaskRPTResult(rptQuery));
    }

    @PostMapping("/getInsTaskSpread")
    public R<Map<String,RPTResult>> getInsTaskSpread(@RequestBody RPTQuery rptQuery) {
        return R.ok(inspectionTaskRPTService.getInsTaskSpread(rptQuery));
    }
}
