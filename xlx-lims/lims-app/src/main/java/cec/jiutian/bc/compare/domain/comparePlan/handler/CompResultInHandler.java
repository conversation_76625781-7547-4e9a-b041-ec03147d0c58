package cec.jiutian.bc.compare.domain.comparePlan.handler;

import cec.jiutian.bc.compare.domain.comparePlan.model.CompResultIn;
import cec.jiutian.bc.compare.domain.comparePlan.model.ComparePlan;
import cec.jiutian.bc.compare.enumeration.CompareStateEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Component
public class CompResultInHandler implements OperationHandler<ComparePlan, CompResultIn> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    @Transactional
    public String exec(List<ComparePlan> data, CompResultIn modelObject, String[] param) {
        ComparePlan comparePlan = data.get(0);
        comparePlan.setCompResult(modelObject.getCompResult());
        comparePlan.setConclusion(modelObject.getConclusion());
        // 录入结果后任务变为已完成
        comparePlan.setIsResultIn("2");
        comparePlan.setCurrentState(CompareStateEnum.Enum.COMPLETE.name());
        comparePlan.setFinishTime(new Date());
        fabosJsonDao.update(comparePlan);
        return null;
    }

    @Override
    public CompResultIn fabosJsonFormValue(List<ComparePlan> data, CompResultIn fabosJsonForm, String[] param) {
        ComparePlan comparePlan = data.get(0);
        BeanUtil.copyProperties(comparePlan, fabosJsonForm);
        fabosJsonForm.setId(null);
        return fabosJsonForm;
    }
}
