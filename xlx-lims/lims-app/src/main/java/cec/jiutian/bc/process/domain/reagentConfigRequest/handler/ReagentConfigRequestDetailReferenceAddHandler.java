package cec.jiutian.bc.process.domain.reagentConfigRequest.handler;

import cec.jiutian.bc.lineEdge.domain.inventory.model.LineInventoryDetail;
import cec.jiutian.bc.process.domain.reagentConfigRequest.model.ReagentConfigRequestDetail;
import cec.jiutian.bc.process.domain.reagentConfigRequest.model.ReagentConfigRequestDetailWare;
import cec.jiutian.view.ReferenceAddType;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：2024/11/18 17:35
 * @description：
 */
@Component
public class ReagentConfigRequestDetailReferenceAddHandler implements ReferenceAddType.ReferenceAddHandler<ReagentConfigRequestDetail, LineInventoryDetail> {
    @Override
    public Map<String, Object> handle(ReagentConfigRequestDetail reagentConfigRequestDetail, List<LineInventoryDetail> requestList) {
        Map<String, Object> result = new HashMap<>();
        List<ReagentConfigRequestDetailWare> cuttingOrderDetailLots = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(requestList)) {
            requestList.forEach(c -> {
                ReagentConfigRequestDetailWare reagentConfigRequestDetailWare = new ReagentConfigRequestDetailWare();
                reagentConfigRequestDetailWare.setWarehouseId(c.getWarehouseId());
                reagentConfigRequestDetailWare.setWarehouseName(c.getWarehouseName());
                reagentConfigRequestDetailWare.setInventoryDetailId(c.getId());
                reagentConfigRequestDetailWare.setMaterialCode(c.getMaterialCode());
                reagentConfigRequestDetailWare.setMaterialName(c.getMaterialName());
                cuttingOrderDetailLots.add(reagentConfigRequestDetailWare);
            });
            result.put("reagentConfigRequestDetailWares", cuttingOrderDetailLots);
        }
        return result;
    }
}
