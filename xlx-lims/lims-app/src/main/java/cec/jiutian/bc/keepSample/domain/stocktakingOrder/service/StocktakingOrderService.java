package cec.jiutian.bc.keepSample.domain.stocktakingOrder.service;

import cec.jiutian.bc.base.enumeration.LmNamingRuleCodeEnum;
import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.keepSample.domain.inventory.model.KsInventory;
import cec.jiutian.bc.keepSample.domain.stocktakingOrder.model.StocktakingOrder;
import cec.jiutian.bc.keepSample.domain.stocktakingOrder.model.StocktakingOrderDetail;
import cec.jiutian.bc.keepSample.domain.stocktakingPlan.model.KsStocktakingPlan;
import cec.jiutian.bc.keepSample.enumeration.StocktakingOrderStateEnum;
import cec.jiutian.common.util.BeanUtils;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 */
@Service
public class StocktakingOrderService {
    @Resource
    private FabosJsonDao fabosJsonDao;
    @Resource
    private NamingRuleService namingRuleService;

    /**
     * 通过盘点计划生成盘点任务
     */
    public void generateStocktakingOrder(KsStocktakingPlan ksStocktakingPlan){
        StocktakingOrder stocktakingOrder = new StocktakingOrder();
        stocktakingOrder.setGeneralCode(namingRuleService.getNameCode(LmNamingRuleCodeEnum.LM_KS_STOCKTAKING_ORDER.name(), 1, null).get(0));
        stocktakingOrder.setKsStocktakingPlan(ksStocktakingPlan);
        stocktakingOrder.setStocktakingRange(ksStocktakingPlan.getStocktakingRange());
        stocktakingOrder.setStocktakingPerson(ksStocktakingPlan.getStocktakingPerson());
        stocktakingOrder.setStocktakingReviewPerson(ksStocktakingPlan.getStocktakingReviewPerson());
        stocktakingOrder.setCurrentState(StocktakingOrderStateEnum.Enum.OPEN.name());
        stocktakingOrder.setStocktakingOrderDetails(getStocktakingOrderDetailList(ksStocktakingPlan.getStocktakingRange()));
        fabosJsonDao.persistAndFlush(stocktakingOrder);
    }

    public List<StocktakingOrderDetail> getStocktakingOrderDetailList(String stocktakingRange){
        //通过样品类型  查询样品台账
        KsInventory query = new KsInventory();
        query.setSampleType(stocktakingRange);
        List<KsInventory> ksInventoryList = fabosJsonDao.select(query);
        List<StocktakingOrderDetail> list = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(ksInventoryList)){
            ksInventoryList.forEach(d -> {
                StocktakingOrderDetail detail = new StocktakingOrderDetail();
                BeanUtils.copyNotEmptyProperties(d,detail);
                detail.setInventoryId(d.getId());
                detail.setId(null);
                list.add(detail);
            });
        }
        return list;
    }
}
