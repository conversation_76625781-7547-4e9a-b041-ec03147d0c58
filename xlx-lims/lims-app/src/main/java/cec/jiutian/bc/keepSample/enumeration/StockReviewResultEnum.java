package cec.jiutian.bc.keepSample.enumeration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 审核结果枚举
 * <AUTHOR>
 * @date 2025/3/18 14:48
 */
public class StockReviewResultEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum value : Enum.values()) {
            list.add(new VLModel(value.name(), value.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        PASS("通过"),
        NOT_PASS("不通过");

        private final String value;

    }
}
