package cec.jiutian.bc.msa.enumeration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 样本类型枚举
 * <AUTHOR>
 * @date 2025/4/28 9:44
 */
public class MsaSampleTypeEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum value : Enum.values()) {
            list.add(new VLModel(value.name(), value.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        KEEP_SAMPLE("留样"),
        PRODUCT_LINE("产线"),
        INVENTORY("库存");

        private final String value;

    }
}
