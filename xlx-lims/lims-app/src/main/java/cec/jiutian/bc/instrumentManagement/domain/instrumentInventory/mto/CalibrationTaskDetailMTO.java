package cec.jiutian.bc.instrumentManagement.domain.instrumentInventory.mto;

import cec.jiutian.bc.instrumentManagement.enums.CheckResultEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@FabosJson(
        name = "校验仪器台账明细"
)
@Table(name = "im_calibration_task_detail_eam")
@Entity
@Getter
@Setter
public class CalibrationTaskDetailMTO extends MetaModel {

    @FabosJsonField(
            views = @View(title = "仪器库存id", show = false),
            edit = @Edit(title = "仪器库存id", show = false)
    )
    private String inventoryId;

    @FabosJsonField(
            views = @View(title = "件次号"),
            edit = @Edit(title = "件次号")
    )
    private String lotSerialId;

    @FabosJsonField(
            views = @View(title = "批次总数量"),
            edit = @Edit(title = "批次总数量")
    )
    private Double accountingUnitQuantity;

    @FabosJsonField(
            views = @View(title = "可用数量"),
            edit = @Edit(title = "可用数量")
    )
    private Double availableQuantity;

    @FabosJsonField(
            views = @View(title = "单位"),
            edit = @Edit(title = "单位")
    )
    private String accountingUnit;

    @FabosJsonField(
            views = @View(title = "供应商"),
            edit = @Edit(title = "供应商")
    )
    private String supplier;

    @FabosJsonField(
            views = @View(title = "校验结果"),
            edit = @Edit(title = "校验结果", type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = CheckResultEnum.class))
    )
    private String result;

}
