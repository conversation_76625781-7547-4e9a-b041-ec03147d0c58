package cec.jiutian.bc.keepSample.domain.stocktakingOrder.model;

import cec.jiutian.bc.base.enumeration.LmNamingRuleCodeEnum;
import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleBaseModel;
import cec.jiutian.bc.keepSample.domain.stocktakingPlan.handler.StocktakingEffectOrLapseOperationHandler;
import cec.jiutian.bc.keepSample.domain.stocktakingPlan.model.KsStocktakingPlan;
import cec.jiutian.bc.keepSample.domain.stocktakingPlan.proxy.KsStocktakingPlanDataProxy;
import cec.jiutian.bc.keepSample.enumeration.StocktakingOrderStateEnum;
import cec.jiutian.bc.person.domain.personManage.model.PersonManage;
import cec.jiutian.bc.process.domain.reagentConfigRequest.model.ReagentConfigRequest;
import cec.jiutian.bc.process.enumeration.SampleTypeEnum;
import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.FabosJsonI18n;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowBaseOperation;
import cec.jiutian.view.type.RowOperation;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @description:
 */
@Entity
@Table(name = "lm_ks_stocktaking_order_detail")
@Getter
@Setter
@FabosJson(name = "盘点任务明细",dataProxy = KsStocktakingPlanDataProxy.class,
        orderBy = "StocktakingOrderDetail.createTime desc",power = @Power(edit = false,export = false,importable = false),
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "1==1",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.HIDE
                )

        }

)
@FabosJsonI18n
public class StocktakingOrderDetail extends BaseModel {
    @FabosJsonField(
            views = {
                    @View(title = "盘点任务", column = "generalCode", show = false)
            },
            edit = @Edit(title = "盘点任务", type = EditType.REFERENCE_TABLE, show = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode")
            )
    )
    @ManyToOne
    @JsonIgnoreProperties("stocktakingOrderDetails")
    private StocktakingOrder stocktakingOrder;



    @FabosJsonField(
            views = @View(title = "样品单号"),
            edit = @Edit(title = "样品单号", notNull = true, search = @Search(vague = true))
    )
    private String sampleTaskNo;

    @FabosJsonField(
            views = @View(title = "物料编码"),
            edit = @Edit(title = "物料编码", notNull = true, search = @Search(vague = true))
    )
    private String materialCode;
    @FabosJsonField(
            views = @View(title = "物料名称"),
            edit = @Edit(title = "物料名称", notNull = true,search = @Search(vague = true))
    )
    private String materialName;
    @FabosJsonField(
            views = @View(title = "型号"),
            edit = @Edit(title = "型号",readonly = @Readonly)
    )
    //SampleTaskCOLLDynamicHandler回显
    private String model;
    @FabosJsonField(
            views = @View(title = "样品类型"),
            edit = @Edit(title = "样品类型",readonly = @Readonly,type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = SampleTypeEnum.class))
    )
    private String sampleType;

    //单位
    @FabosJsonField(
            views = @View(title = "单位"),
            edit = @Edit(title = "单位")
    )
    private String unit;
    //库存数量
    @FabosJsonField(
            views = @View(title = "库存数量"),
            edit = @Edit(title = "库存数量", type = EditType.NUMBER,inputType = @InputType(length = 7),numberType = @NumberType(min = 0,precision = 2)
            )
    )
    private Double quantity;

    @FabosJsonField(
            views = @View(title = "仓库ID",show = false),
            edit = @Edit(title = "仓库ID", notNull = true,show = false)
    )
    //仓库  和产品禹修确认 线边仓台账只需要显示仓库名称
    private String warehouseId;
    @FabosJsonField(
            views = @View(title = "仓库"),
            edit = @Edit(title = "仓库", notNull = true)
    )
    private String warehouseName;
    //货架名称
    @FabosJsonField(
            views = @View(title = "货架名称"),
            edit = @Edit(title = "货架名称", notNull = true)
    )
    private String shelvesName;
    //货架ID
    @FabosJsonField(
            views = @View(title = "货架ID",show = false),
            edit = @Edit(title = "货架ID", notNull = true,show = false)
    )
    private String shelvesId;
    //载具名称
    @FabosJsonField(
            views = @View(title = "载具名称"),
            edit = @Edit(title = "载具名称", notNull = true)
    )
    private String carrierName;
    //载具ID
    @FabosJsonField(
            views = @View(title = "载具ID",show = false),
            edit = @Edit(title = "载具ID", notNull = true,show = false)
    )
    private String carrierId;

    //库存台账ID
    @FabosJsonField(
            views = @View(title = "库存台账ID",show = false),
            edit = @Edit(title = "库存台账ID", show = false)
    )
    private String inventoryId;

}
