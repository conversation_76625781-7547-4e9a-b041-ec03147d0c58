package cec.jiutian.bc.inspection.port.client;

import cec.jiutian.api.remoteCallLog.dto.RemoteCallResult;
import cec.jiutian.bc.modeler.vo.CorrectPreventMeasureVO;
import cec.jiutian.bc.modeler.vo.QuestionPaperVO;
import cec.jiutian.bc.modeler.vo.Report8DAddVO;
import cec.jiutian.core.frame.constant.FabosJsonRestPath;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

/**
 * <AUTHOR>
 * @description: cec.jiutian.bc.questionPaperManagement.remote.feign.QuestionFeign
 */
@Component
@FeignClient(name = "xlx-qms") // 对应目标服务的spring.application.name
public interface QuestionFeignClient {
    @PostMapping("/fabos-qms-app"+ FabosJsonRestPath.FABOS_REMOTE_API+"/createQuestionPaper")
    RemoteCallResult createQuestionPaper(@RequestBody QuestionPaperVO params);

    @PostMapping("/fabos-qms-app"+ FabosJsonRestPath.FABOS_REMOTE_API+"/createQuestionCPA")
    RemoteCallResult createQuestionCPA(@RequestBody CorrectPreventMeasureVO params);
    @PostMapping("/fabos-qms-app"+ FabosJsonRestPath.FABOS_REMOTE_API+"/createReport8D")
    RemoteCallResult createReport8D(@RequestBody Report8DAddVO params);

}
