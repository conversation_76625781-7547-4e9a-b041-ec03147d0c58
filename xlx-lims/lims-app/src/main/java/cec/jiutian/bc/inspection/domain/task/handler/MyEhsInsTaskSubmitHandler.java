package cec.jiutian.bc.inspection.domain.task.handler;

import cec.jiutian.bc.compare.util.BcUtil;
import cec.jiutian.bc.ecs.enums.MessageWayEnum;
import cec.jiutian.bc.inspection.domain.task.model.EhsInsTaskDetail;
import cec.jiutian.bc.inspection.domain.task.model.MyEhsInsTask;
import cec.jiutian.bc.inspection.enumeration.EhsInsIsAbnormalEnum;
import cec.jiutian.bc.inspection.enumeration.EhsInsTaskStateEnum;
import cec.jiutian.bc.urm.domain.user.entity.User;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Component
public class MyEhsInsTaskSubmitHandler implements OperationHandler<MyEhsInsTask, Void> {
    @Resource
    private FabosJsonDao fabosJsonDao;
    @Resource
    private BcUtil bcUtil;

    // 隐患
    private static final String DANGER = EhsInsIsAbnormalEnum.Enum.DANGER.name();

    @Transactional
    @Override
    public String exec(List<MyEhsInsTask> data, Void modelObject, String[] param) {
        // 提交后修改状态为已完成
        MyEhsInsTask myEhsInsTask = data.get(0);
        // 设置提交状态为2 已经提交, 不可再提交
        myEhsInsTask.setIsSubmit("2");
        myEhsInsTask.setCurrentState(EhsInsTaskStateEnum.Enum.COMPLETE.name());
        // 设置完成时间
        myEhsInsTask.setFinishTime(new Date());
        // 设置异常数量
        List<EhsInsTaskDetail> ehsInsTaskDetails = myEhsInsTask.getEhsInsTaskDetails();
        int exceptionCount = (int) ehsInsTaskDetails.stream()
                .filter(ehsInsTaskDetail -> DANGER.equals(ehsInsTaskDetail.getIsAbnormal()))
                .count();
        myEhsInsTask.setExceptionNum(exceptionCount);
        fabosJsonDao.update(myEhsInsTask);
        // 接入告警：如果异常数量 > 0 则需要告警  告警对象：lims管理员、任务负责人
        if (exceptionCount > 0) {
            // 获取lims管理员
            Set<User> notifyUserSet = bcUtil.getLimsRoleUserSet("1");
            if (myEhsInsTask.getInsPerson() != null) {
                notifyUserSet.add(myEhsInsTask.getInsPerson());
            }
            if (CollectionUtils.isNotEmpty(notifyUserSet)) {
                Set<String> phoneSet = notifyUserSet.stream()
                        .map(user -> user.getPhoneNumber())
                        .collect(Collectors.toSet());
                String title = "巡检任务存在异常";
                String content = String.format("巡检任务存在异常，请及时处理！巡检任务单号：%s。",
                        myEhsInsTask.getGeneralCode());
                bcUtil.sendPersonMessage(title, content, null, MessageWayEnum.App, phoneSet);
            }
        }
        return null;
    }
}
