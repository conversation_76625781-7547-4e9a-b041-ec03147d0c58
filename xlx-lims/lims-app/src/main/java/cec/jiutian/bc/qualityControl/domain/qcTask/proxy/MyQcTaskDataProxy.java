package cec.jiutian.bc.qualityControl.domain.qcTask.proxy;

import cec.jiutian.bc.qualityControl.domain.qcTask.model.MyQcTask;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.view.fun.DataProxy;
import cec.jiutian.view.query.Condition;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class MyQcTaskDataProxy implements DataProxy<MyQcTask> {
    @Value("${system.manage.super-account}")
    private String superUserAccount;

    /**
     * 只查询我的质控任务
     * @param conditions
     * @return
     */
    @Override
    public String beforeFetch(List<Condition> conditions) {
        if (UserContext.getAccount().equals(superUserAccount)) {
            return "";
        }
        String queryHql = "MyQcTask.executePerson.user.id = \'" + UserContext.getUserId() + "\'";
        return queryHql;
    }
}
