package cec.jiutian.bc.qualityControl.domain.qcLedger.service;

import cec.jiutian.bc.qualityControl.domain.qcLedger.model.QcLedger;
import cec.jiutian.bc.qualityControl.domain.qcTask.model.QcTask;
import cec.jiutian.bc.qualityControl.enumeration.QcStateEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 质控台账服务类：质控任务完成时, 需要回写 状态、最新测试时间、质控任务编号、产品牌号 数据
 * <AUTHOR>
 * @date 2025/3/29 17:19
 */
@Service
@Transactional
@Slf4j
public class QcLedgerService {
    @Resource
    private FabosJsonDao fabosJsonDao;


    /**
     * 更新质控台账数据： 状态、最新测试时间、质控任务编号、产品牌号
     *
     * @param qcLedgerId 质控台账id
     * @param qcTask     质控任务
     * <AUTHOR>
     * @date 2025/3/29 17:21
     */
    @Transactional
    public void updateQcLedger(String qcLedgerId, QcTask qcTask) {
        QcLedger qcLedgerDb = fabosJsonDao.getById(QcLedger.class, qcLedgerId);
        if (qcLedgerDb == null) {
            log.info("qcLedgerDb query failed, no such data!");
            return;
        }
        // 根据质控任务来设置质控台账的状态：合格/不合格
        if (QcStateEnum.Enum.QUALIFIED.name().equals(qcTask.getDetectionResult())) {
            qcLedgerDb.setCurrentState(QcStateEnum.Enum.QUALIFIED.name());
        } else
            qcLedgerDb.setCurrentState(QcStateEnum.Enum.UMQUALIFIED.name());
        // 质控任务编号、最新测试时间、产品牌号
        qcLedgerDb.setQcTaskNo(qcTask.getGeneralCode());
        qcLedgerDb.setNewDetectTime(qcTask.getFinishTime());
        qcLedgerDb.setBrandCode(qcTask.getMaterialCode());
        fabosJsonDao.update(qcLedgerDb);
    }
}
