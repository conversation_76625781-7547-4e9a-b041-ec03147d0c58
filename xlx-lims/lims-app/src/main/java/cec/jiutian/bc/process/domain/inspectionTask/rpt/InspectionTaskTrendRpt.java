package cec.jiutian.bc.process.domain.inspectionTask.rpt;

import cec.jiutian.bc.process.domain.inspectionTask.model.InspectionTask;
import cec.jiutian.bc.process.domain.inspectionTask.repository.InspectionTaskSpecifications;
import cec.jiutian.bc.process.remote.dto.RPTQuery;
import cec.jiutian.bc.process.remote.dto.RPTResult;
import cec.jiutian.common.util.StringUtils;
import org.springframework.data.jpa.domain.Specification;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.WeekFields;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description:  测试及时率趋势图   会根据时间维度展示数据
 */
public class InspectionTaskTrendRpt {
    public static List<TimelyRateResultDTO> getTimelyRateStatistics(List<InspectionTask> tasks, RPTQuery query) {


        // 按时间维度分组统计
        Map<String, TimelyRateResultDTO> resultMap = new LinkedHashMap<>();

        for (InspectionTask task : tasks) {
            // 为每个时间维度生成分组键
            for (String dimension : query.getTimeDimensions()) {
                String dimensionKey = getDimensionKey(task.getFinishTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime(), dimension);
                resultMap.computeIfAbsent(dimensionKey, k ->
                        new TimelyRateResultDTO(dimensionKey, 0L, 0L, 0.0));

                TimelyRateResultDTO dto = resultMap.get(dimensionKey);
                if ("Y".equals(task.getIsTimely())) {
                    dto.setTimelyCount(dto.getTimelyCount() + 1);
                } else {
                    dto.setUntimelyCount(dto.getUntimelyCount() + 1);
                }
            }
        }

        // 计算及时率并排序  保留4位小数
        return resultMap.values().stream()
                .peek(dto -> dto.setTimelyRate(
                        (dto.getTimelyCount() + dto.getUntimelyCount()) > 0
                                ? BigDecimal.valueOf(dto.getTimelyCount()).divide(BigDecimal.valueOf(dto.getTimelyCount() + dto.getUntimelyCount()), 4, RoundingMode.HALF_UP).doubleValue()
                                : 0.0))
                .sorted((dto1, dto2) -> {
                    // 解析维度类型和实际时间
                    DimensionInfo info1 = parseDimension(dto1.getDimensionName());
                    DimensionInfo info2 = parseDimension(dto2.getDimensionName());

                    // 首先按维度优先级排序（年>季度>月>周）
                    if (info1.priority != info2.priority) {
                        return Integer.compare(info1.priority, info2.priority);
                    }

                    // 相同维度下按时间正序排列（更早的时间在前） 降序：info2.date.compareTo(info1.date)
                    return info1.date.compareTo(info2.date);
                })
                //.limit(10)
                .collect(Collectors.toList());
    }
    // 辅助类：存储维度解析结果  临时
    private static class DimensionInfo {
        int priority; // 1-年 2-季度 3-月 4-周
        LocalDate date; // 可比较的实际日期
        String original; // 原始维度名称
    }
    // 解析维度名称
    private static DimensionInfo parseDimension(String dimensionName) {
        DimensionInfo info = new DimensionInfo();
        info.original = dimensionName;

        try {
            if (dimensionName.matches("^\\d{4}年$")) {
                // 年维度，如"2025年"
                info.priority = 1;
                int year = Integer.parseInt(dimensionName.replace("年", ""));
                info.date = LocalDate.of(year, 1, 1);
            } else if (dimensionName.matches("^\\d{4}年第[1-4]季度$")) {
                // 季度维度，如"2025年Q1"
                info.priority = 2;
                String[] parts = dimensionName.split("年第|季度");
                int year = Integer.parseInt(parts[0]);
                int quarter = Integer.parseInt(parts[1]);
                info.date = LocalDate.of(year, (quarter - 1) * 3 + 1, 1);
            } else if (dimensionName.matches("^\\d{4}年\\d{2}月$")) {
                // 月维度，如"2025年01月"
                info.priority = 3;
                String[] parts = dimensionName.split("年|月");
                int year = Integer.parseInt(parts[0]);
                int month = Integer.parseInt(parts[1]);
                info.date = LocalDate.of(year, month, 1);
            } else if (dimensionName.matches("^\\d{4}年第\\d+周$")) {
                // 周维度，如"2025年第1周"
                info.priority = 4;
                String[] parts = dimensionName.split("年第|周");
                int year = Integer.parseInt(parts[0]);
                int week = Integer.parseInt(parts[1]);
                info.date = LocalDate.of(year, 1, 1)
                        .with(WeekFields.of(Locale.getDefault()).weekOfYear(), week)
                        .with(WeekFields.of(Locale.getDefault()).dayOfWeek(), 1);
            } else {
                info.priority = 5;
                info.date = LocalDate.MIN;
            }
        } catch (Exception e) {
            info.priority = 5;
            info.date = LocalDate.MIN;
        }

        return info;
    }

    private static String getDimensionKey(LocalDateTime dateTime, String dimension) {
        switch (dimension) {
            case "1": // 年
                return dateTime.format(DateTimeFormatter.ofPattern("yyyy年"));
            case "2": // 季度
                int quarter = (dateTime.getMonthValue() - 1) / 3 + 1;
                return dateTime.format(DateTimeFormatter.ofPattern("yyyy年")) + "第" + quarter+"季度";
            case "3": // 月
                return dateTime.format(DateTimeFormatter.ofPattern("yyyy年MM月"));
            case "4": // 周
                return dateTime.format(DateTimeFormatter.ofPattern("yyyy年")) + "第" +
                        dateTime.get(WeekFields.of(Locale.getDefault()).weekOfYear()) + "周";
            default:
                return "";
        }
    }
    public static LocalDateTime[] resolveDateRange(RPTQuery query) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime start = null;
        LocalDateTime end = null;

        switch (query.getTimeType()) {
            case "1": // 本年度
                start = LocalDateTime.of(now.getYear(), 1, 1, 0, 0);
                end = LocalDateTime.of(now.getYear(), 12, 31, 23, 59, 59);
                break;
            case "2": // 本季度
                int quarter = (now.getMonthValue() - 1) / 3 + 1;
                start = LocalDateTime.of(now.getYear(), (quarter - 1) * 3 + 1, 1, 0, 0);
                end = start.plusMonths(3).minusSeconds(1);
                break;
            case "3": // 本月度
                start = LocalDateTime.of(now.getYear(), now.getMonth(), 1, 0, 0);
                end = start.plusMonths(1).minusSeconds(1);
                break;
            case "4": // 本周
                start = now.with(DayOfWeek.MONDAY).withHour(0).withMinute(0).withSecond(0);
                end = start.plusDays(7).minusSeconds(1);
                break;
            case "5": // 自定义时间
                if (StringUtils.isNotBlank(query.getStartTime())) {
                    start = LocalDateTime.parse(query.getStartTime() + " 00:00:00",
                            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                }
                if (StringUtils.isNotBlank(query.getEndTime())) {
                    end = LocalDateTime.parse(query.getEndTime() + " 23:59:59",
                            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                }
                break;
            default: // 全部时间
                break;
        }

        return new LocalDateTime[]{start, end};
    }

    /**
     * 处理时间  将时间横坐标返回给前端使用  开始日期和结束日期  如果调整了横坐标的描述  也需要调整对应的正则表达式
     */
    public static Map<String, Object> parseTimeRangeByXAxis(List<String> data) {
        Map<String, Object> result = new HashMap<>();
        data.forEach(item -> {
            Map<String, String> timeRange = parseTimeRange(item);
            result.put(item, timeRange);
        });
        return result;
    }
    private static Map<String, String> parseTimeRange(String input) {
        // 定义正则表达式匹配四种格式
        Matcher matcherYear = Pattern.compile("^(\\d{4})年$").matcher(input);
        Matcher matcherQuarter = Pattern.compile("^(\\d{4})年第(\\d)季度$").matcher(input);
        Matcher matcherMonth = Pattern.compile("^(\\d{4})年(0[1-9]|1[0-2])月$").matcher(input);
        Matcher matcherWeek = Pattern.compile("^(\\d{4})年第(\\d{1,2})周$").matcher(input);

        if (matcherYear.matches()) {
            return handleYear(matcherYear.group(1));
        } else if (matcherQuarter.matches()) {
            return handleQuarter(matcherQuarter.group(1), matcherQuarter.group(2));
        } else if (matcherMonth.matches()) {
            return handleMonth(matcherMonth.group(1), matcherMonth.group(2));
        } else if (matcherWeek.matches()) {
            return handleWeek(matcherWeek.group(1), matcherWeek.group(2));
        } else {
            throw new IllegalArgumentException("无效的时间格式: " + input);
        }
    }

    // 处理年份格式 "2025年"
    private static Map<String, String> handleYear(String yearStr) {
        int year = Integer.parseInt(yearStr);
        LocalDate start = LocalDate.of(year, 1, 1);
        LocalDate end = LocalDate.of(year, 12, 31);
        return createResultMap(start, end);
    }

    // 处理季度格式 "2025年第2季度"
    private static Map<String, String> handleQuarter(String yearStr, String quarterStr) {
        int year = Integer.parseInt(yearStr);
        int quarter = Integer.parseInt(quarterStr);

        if (quarter < 1 || quarter > 4) {
            throw new IllegalArgumentException("季度必须在1-4之间: " + quarter);
        }

        int startMonth = 3 * quarter - 2; // 季度起始月份
        int endMonth = 3 * quarter;       // 季度结束月份

        LocalDate start = LocalDate.of(year, startMonth, 1);
        LocalDate end = YearMonth.of(year, endMonth).atEndOfMonth();

        return createResultMap(start, end);
    }

    // 处理月份格式 "2025年04月"
    private static Map<String, String> handleMonth(String yearStr, String monthStr) {
        int year = Integer.parseInt(yearStr);
        int month = Integer.parseInt(monthStr);

        LocalDate start = LocalDate.of(year, month, 1);
        LocalDate end = YearMonth.of(year, month).atEndOfMonth();

        return createResultMap(start, end);
    }

    // 处理周格式 "2025年第14周"
    private static Map<String, String> handleWeek(String yearStr, String weekStr) {
        int year = Integer.parseInt(yearStr);
        int week = Integer.parseInt(weekStr);

        if (week < 1 || week > 53) {
            throw new IllegalArgumentException("周数必须在1-53之间: " + week);
        }

        // 使用ISO周定义（周一为周起始日）
        WeekFields weekFields = WeekFields.ISO;
        LocalDate baseDate = LocalDate.of(year, 1, 1);

        // 计算周一的日期
        LocalDate startDate = baseDate.with(weekFields.weekBasedYear(), year)
                .with(weekFields.weekOfWeekBasedYear(), week)
                .with(weekFields.dayOfWeek(), 1);

        // 计算周日的日期（结束时间）
        LocalDate endDate = startDate.plusDays(6);

        return createResultMap(startDate, endDate);
    }

    // 创建结果Map，使用"yyyy-MM-dd"格式
    private static Map<String, String> createResultMap(LocalDate start, LocalDate end) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        Map<String, String> result = new LinkedHashMap<>();
        result.put("startTime", start.format(formatter));
        result.put("endTime", end.format(formatter));
        return result;
    }


}
