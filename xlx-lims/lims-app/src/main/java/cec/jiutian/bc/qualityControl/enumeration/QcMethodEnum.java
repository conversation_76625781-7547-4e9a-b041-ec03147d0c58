package cec.jiutian.bc.qualityControl.enumeration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 质控方式枚举
 * <AUTHOR>
 * @date 2025/3/25 14:49
 */
public class QcMethodEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum value : Enum.values()) {
            list.add(new VLModel(value.name(), value.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        SHORT_TERM_STABILITY("短期稳定性"),
        LONG_TERM_STABILITY("长期稳定性"),
        // 标样
        LONG_TERM_ACCURACY("准确性"),
        GRR("GRR");

        private final String value;

    }
}