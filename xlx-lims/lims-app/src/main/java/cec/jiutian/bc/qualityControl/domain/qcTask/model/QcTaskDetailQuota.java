package cec.jiutian.bc.qualityControl.domain.qcTask.model;

import cec.jiutian.bc.modeler.enumration.ComparisonMethodEnum;
import cec.jiutian.bc.modeler.enumration.InspectionValueTypeEnum;
import cec.jiutian.bc.qualityControl.domain.qcTask.handler.QcQuotaDynamicHandler;
import cec.jiutian.bc.qualityControl.enumeration.QcTaskDetailResultEnum;
import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.FabosJsonI18n;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DependFieldDisplay;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;


@Entity
@Table(name = "lm_quantity_task_detail_quota")
@Getter
@Setter
@FabosJson(name = "质控任务明细指标",
        orderBy = "QcTaskDetailQuota.createTime desc",
        power = @Power(export = false, importable = false, print = false)

)
@FabosJsonI18n
public class QcTaskDetailQuota extends BaseModel {
    @FabosJsonField(
            views = @View(title = "检测项指标名称"),
            edit = @Edit(title = "检测项指标名称", search = @Search(vague = true), notNull = true,
                    inputType = @InputType(length = 40))
    )
    private String name;

    @FabosJsonField(
            views = @View(title = "值类型"),
            edit = @Edit(title = "值类型", type = EditType.CHOICE, search = @Search(), notNull = true,
                    choiceType = @ChoiceType(fetchHandler = InspectionValueTypeEnum.class))
    )
    private String inspectionValueType;

    @FabosJsonField(
            views = @View(title = "比较方式"),
            edit = @Edit(title = "比较方式",
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "inspectionValueType == 'number' || inspectionValueType == 'percentage'"),
                    search = @Search(),
                    type = EditType.CHOICE,
                    notNull = true,
                    choiceType = @ChoiceType(fetchHandler = ComparisonMethodEnum.class))
    )
    private String comparisonMethod;

    @FabosJsonField(
            views = @View(title = "质量分数或单位"),
            edit = @Edit(title = "质量分数或单位", inputType = @InputType(length = 40))
    )
    private String unit;

    @FabosJsonField(
            views = @View(title = "标准值"),
            edit = @Edit(title = "标准值",notNull = true,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "inspectionValueType == 'text' || comparisonMethod == 'equal'"))
    )
    //标准值可能是文本
    private String standardValue;

    @FabosJsonField(
            views = @View(title = "检出上限"),
            edit = @Edit(title = "检出上限",
                    notNull = true,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "comparisonMethod == 'range'"),
                    numberType = @NumberType(min=0,max = 9999999,precision = 2))
    )
    private Double upperValue;

    @FabosJsonField(
            views = @View(title = "是否包含上限值", show = false),
            edit = @Edit(title = "是否包含上限值", defaultVal = "true", show = false)
    )
    private Boolean isContainUpper;

    @FabosJsonField(
            views = @View(title = "检出下限"),
            edit = @Edit(title = "检出下限",
                    notNull = true,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "comparisonMethod == 'range' || comparisonMethod == 'lowerLimit'"),
                    numberType = @NumberType(min=0,max = 9999999,precision = 2))
    )
    private Double lowerValue;

    @FabosJsonField(
            views = @View(title = "是否包含下限值", show = false),
            edit = @Edit(title = "是否包含下限值", defaultVal = "true", show = false)
    )
    private Boolean isContainLower;

    @FabosJsonField(
            views = @View(title = "检测值"),
            edit = @Edit(title = "检测值", notNull = true)
    )
    //检测结果精度不确定。使用String类型+  type = EditType.NUMBER  控制前端输入。
    private String inspectValue;

    @FabosJsonField(
            views = @View(title = "检测结果"),
            edit = @Edit(title = "检测结果", type = EditType.CHOICE, search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = QcTaskDetailResultEnum.class)),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "inspectValue",
                    dynamicHandler = QcQuotaDynamicHandler.class))
    )
    private String inspectResult;

    @FabosJsonField(
            views = @View(title = "质控任务明细", column = "inspectionItemCode", show = false),
            edit = @Edit(title = "质控任务明细", type = EditType.REFERENCE_TABLE, show = false,
                    referenceTableType = @ReferenceTableType(label = "inspectionItemCode")
            )
    )
    @ManyToOne
    @JsonIgnoreProperties("qcTaskDetailQuotas")
    private QcTaskDetail qcTaskDetail;

}
