package cec.jiutian.bc.qualityControl.enumeration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 质控任务-样品类型枚举
 * <AUTHOR>
 * @date 2025/5/12 10:13
 */
public class QcSampleTypeEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum value : Enum.values()) {
            list.add(new VLModel(value.name(), value.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        KEEP_SAMPLE("留样"),
        PRODUCT_LINE("产线"),
        INVENTORY("库存");

        private final String value;

    }
}