package cec.jiutian.bc.process.domain.inspectionTask.handler;

import cec.jiutian.bc.compare.util.BcUtil;
import cec.jiutian.bc.ecs.enums.MessageWayEnum;
import cec.jiutian.bc.process.domain.inspectionTask.model.InspectionTask;
import cec.jiutian.bc.process.domain.inspectionTask.model.InspectionTaskDetail;
import cec.jiutian.bc.process.domain.inspectionTask.model.InspectionTaskDetailQuota;
import cec.jiutian.bc.process.domain.inspectionTask.model.ResultReview;
import cec.jiutian.bc.process.domain.inspectionTask.model.ReviewLog;
import cec.jiutian.bc.process.domain.inspectionTask.service.InspectionTaskService;
import cec.jiutian.bc.process.domain.sample.model.SampleTask;
import cec.jiutian.bc.process.enumeration.CheckTypeEnum;
import cec.jiutian.bc.process.enumeration.InspectResultEnum;
import cec.jiutian.bc.process.enumeration.InspectionResultEnum;
import cec.jiutian.bc.process.enumeration.InspectionTaskStateEnum;
import cec.jiutian.bc.process.enumeration.PrepareStatusEnum;
import cec.jiutian.bc.process.enumeration.ReviewCommentEnum;
import cec.jiutian.bc.process.enumeration.TaskStatusEnum;
import cec.jiutian.bc.process.port.client.SampleFeignClient;
import cec.jiutian.bc.qualityControl.domain.qcTask.model.QcTask;
import cec.jiutian.bc.qualityControl.domain.qcTask.model.QcTaskDetail;
import cec.jiutian.bc.qualityControl.domain.qcTask.model.QcTaskDetailQuota;
import cec.jiutian.bc.qualityControl.enumeration.QcTaskDetailResultEnum;
import cec.jiutian.bc.qualityControl.enumeration.QcTaskDetailStateEnum;
import cec.jiutian.bc.urm.config.AsyncContextHolder;
import cec.jiutian.bc.urm.inbound.local.context.BackgroundTaskTraceContext;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.common.util.StringUtils;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import dm.jdbc.util.StringUtil;
import jakarta.annotation.Resource;
import jakarta.persistence.EntityManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;
import org.apache.seata.core.context.RootContext;

/**
 * 检测任务结果审核
 * <AUTHOR>
 * @date 2025/3/14 17:33
 */
@Component
@Slf4j
public class ResultReviewHandler implements OperationHandler<InspectionTask, ResultReview> {
    @Resource
    private FabosJsonDao fabosJsonDao;
    @Resource
    private AllocationHandler allocationHandler;
    @Resource
    private SampleFeignClient sampleFeignClient;
    @Resource
    private InspectionTaskService inspectionTaskService;
    @Resource
    private BcUtil bcUtil;

    // 主单检测结果 合格
    private static final String QUALIFIED = InspectionResultEnum.Enum.QUALIFIED.name();
    // 定义线程池
    private static final ExecutorService asyncExecutor = Executors.newFixedThreadPool(5);

    @Override
    @Transactional
    public String exec(List<InspectionTask> data, ResultReview modelObject, String[] param) {
        InspectionTask inspectionTask = allocationHandler.getInsByGeneralCode(modelObject.getGeneralCode());
        // 获取当前用户信息
        UserContext.CurrentUser currentUser = UserContext.get();
        // 将提交结果装载到日志表中
        List<ReviewLog> reviewLogs = modelObject.getReviewLogs();
        // 校验提交的审核数据, 每次只能提交一条, 新增一条以上则抛出异常
        long count = reviewLogs.stream()
                .filter(reviewLog -> reviewLog.getId() == null)
                .count();
        if (count > 1) {
            throw new FabosJsonApiErrorTip("每次只能提交一条新的审核记录！");
        }
        if (count == 0) {
            throw new FabosJsonApiErrorTip("请添加新的审核记录");
        }
        // 只对新增的数据设置审核人信息、审核时间
        int index = reviewLogs.size() - 1;
        ReviewLog newLog = reviewLogs.get(index);
        // 审核时间、审核人信息
        newLog.setReceiveTime(new Date());
        newLog.setReviewPersonName(currentUser.getUserName());
        newLog.setReviewPersonAccount(currentUser.getAccount());
        newLog.setIsHisData(true);
        inspectionTask.getReviewLogs().add(newLog);
        // 设置主单业务状态--只更新最新的一条记录的状态
        // 审核通过
        if (newLog.getReviewComment().equals(ReviewCommentEnum.Enum.PASS.name())) {
            // 设置单据状态为完成
            inspectionTask.setTaskStatus(InspectionTaskStateEnum.Enum.COMPLETE.name());
        } else {
            // 如果这一轮没有通过的话, 设置审批结果为失败, 设置测前准备状态为PASS(即可再次进行结果录入和后续操作)
            inspectionTask.setTaskStatus(InspectionTaskStateEnum.Enum.APPROVAL_FAILED.name());
            inspectionTask.setPrepareStatus(PrepareStatusEnum.Enum.PASS.name());
        };
        fabosJsonDao.update(inspectionTask);
        // 主线程执行完成后，再执行异步任务，保持事务一致
        if (newLog.getReviewComment().equals(ReviewCommentEnum.Enum.PASS.name())) {
            // 异步执行审核通过后的任务
            // 在启动异步任务前捕获当前请求上下文
            RequestAttributes requestAttributes = RequestContextHolder.currentRequestAttributes();
            String authorization = ((ServletRequestAttributes) requestAttributes).getRequest().getHeader("Authorization");
            String seataXid = RootContext.getXID();
            CompletableFuture.runAsync(() -> {
                try {
                    log.info("【异步执行】开始执行审核通过后的任务...");
                    // 同步任务状态到对标计划中或者msa计划
                    inspectionTaskService.syncTaskState(inspectionTask);
                    // 需要同步样品任务状态为完成
                    SampleTask qeury = new SampleTask();
                    qeury.setGeneralCode(inspectionTask.getSampleTaskNo());
                    SampleTask sampleTaskDb = fabosJsonDao.selectOne(qeury);
                    sampleTaskDb.setTaskStatus(TaskStatusEnum.Enum.COMPLETE.name());
                    fabosJsonDao.update(sampleTaskDb);
                    // 如果是检测类型是 质控检, 则根据数据来源id回写数据到质控任务中
                    if (CheckTypeEnum.Enum.CONTROL.name().equals(inspectionTask.getCheckType())) {
                        this.pushData2QcTask(inspectionTask);
                    }
                    // 手动设置请求上下文
                    // 将主线程上下文绑定到当前线程
                    AsyncContextHolder.setAuthorization(authorization);
                    if (StringUtils.isNotBlank(seataXid)) {
                        //绑定主线程的seataXid feignAuthConfig可以直接获取到。和上下文没关系
                        RootContext.bind(seataXid);
                    }
                    // 审核通过，审核spc控制图
                    inspectionTaskService.sendSPCData(inspectionTask);
                    // 如果单据状态为完成、且数据来源于QMS, 则需要将结果推送数据到QMS
                    if (StringUtil.isNotEmpty(inspectionTask.getCheckTaskNo())) {
                        InspectionTask fullTask = inspectionTaskService.loadWithDetails(inspectionTask.getId());
                        this.pushData2Qms(fullTask);
                    }
                } catch (Exception e) {
                    log.error("审核通过后的异步任务执行失败", e);
                }finally {
                    // 清理线程本地变量
                    AsyncContextHolder.clear();
                    RootContext.unbind();
                }
            }, asyncExecutor);

        }
        // 如果检测任务明细存在不合格情况, 则进行异步告警
        CompletableFuture.runAsync(() -> checkAndNotifyUnqualified(inspectionTask), asyncExecutor)
                .exceptionally(e -> {
                    log.error("检测任务异步告警处理失败", e);
                    return null;
                });
        return "success";
    }

    /**
     * 检测任务结果有不合格项，则进行异步告警，告警角色： IQC、IPQC、OQC、lims主管
     * <AUTHOR>
     * @date 2025/6/25 14:21
     * @param task 检测任务
     */
    private void checkAndNotifyUnqualified(InspectionTask task) {
        if (!QUALIFIED.equals(task.getInspectionResult())) {
            String message = "检测任务单号：" + task.getGeneralCode() + "，检测结果有不合格项，请及时处理！";
            Set<String> roleUserPhoneSet = bcUtil.getRoleUserPhoneSet("IQC", "IPQC", "OQC", "limsSupervisor");
            if (CollectionUtils.isNotEmpty(roleUserPhoneSet)) {
                bcUtil.sendPersonMessage("检测任务样品不合格", message, null, MessageWayEnum.App, roleUserPhoneSet);
            }
        }
    }

    @Override
    public ResultReview fabosJsonFormValue(List<InspectionTask> data, ResultReview fabosJsonForm, String[] param) {
        InspectionTask inspectionTask = data.get(0);
        BeanUtil.copyProperties(inspectionTask, fabosJsonForm);
        return fabosJsonForm;
    }

    /**
     * 审核通过-推送检测任务数据到QMS
     * <AUTHOR>
     * @date 2025/3/20 14:10
     * @param inspectionTask
     */
    public void pushData2Qms(InspectionTask inspectionTask) {
        HashMap<String, Object> dataMap = new HashMap<>();
        dataMap.put("inspectionTaskCode", inspectionTask.getCheckTaskNo());
        dataMap.put("inspectionTaskType", inspectionTask.getCheckType());
        // 装载子单数据
        List<InspectionTaskDetail> details = inspectionTask.getInspectionTaskDetails();
        List<HashMap<String, Object>> itemList = new ArrayList<>();
        details.forEach(detail -> {
            HashMap<String, Object> itemListMap = new HashMap<>();
            itemListMap.put("detailId", detail.getQmsDetailId());
            itemListMap.put("itemResult", detail.getDetailInsResult());
            itemList.add(itemListMap);
        });
        dataMap.put("itemList", itemList);
        log.info("检测结果发送qms请求参数：{}", JSONUtil.toJsonStr(dataMap));
        sampleFeignClient.receiveLIMSInspectResult(dataMap);
    }

    /**
     * 推送数据到质控任务
     * <AUTHOR>
     * @date 2025/3/31 23:13
     * @param inspectionTask
     */
    @Transactional
    public void pushData2QcTask(InspectionTask inspectionTask) {
        // 查询该委托申请关联的 质控任务明细列表（一个委托申请可能关联多个质控任务明细）
        EntityManager entityManager = fabosJsonDao.getEntityManager();
        String hql = "from QcTaskDetail q where q.inspectionTaskNo = :generalCode";
        List<QcTaskDetail> qcTaskDetailList = entityManager.createQuery(hql, QcTaskDetail.class)
                .setParameter("generalCode", inspectionTask.getGeneralCode())
                .getResultList();
        if (CollectionUtils.isEmpty(qcTaskDetailList)) {
            return;
        }
        // 委托申请以<检测项目编号，检测项目>为map
        String hql3 = "from InspectionTaskDetail i where i.inspectionTask.id = :taskId";
        List<InspectionTaskDetail> insDetailList = entityManager.createQuery(hql3, InspectionTaskDetail.class)
                .setParameter("taskId", inspectionTask.getId())
                .getResultList();
        Map<String, InspectionTaskDetail> map = insDetailList.stream()
                .collect(Collectors.toMap(InspectionTaskDetail::getTestMethodCode, detail -> detail));
        // 遍历质控任务明细, 匹配检测项目编号，装载检测指标数据
        for (QcTaskDetail qcTaskDetail : qcTaskDetailList) {
            if (map.containsKey(qcTaskDetail.getInspectionItemCode())) {
                InspectionTaskDetail insDetail = map.get(qcTaskDetail.getInspectionItemCode());
                // 委托申请指标map
                List<InspectionTaskDetailQuota> insQuotaList = insDetail.getInspectionTaskDetailQuotas();
                Map<String, InspectionTaskDetailQuota> quotaMap = insQuotaList.stream()
                        .collect(Collectors.toMap(InspectionTaskDetailQuota::getName,
                                quota -> quota, (existing, replacement) -> existing));
                // 装载委托申请指标到质控任务指标上去
                String hql4 = "from QcTaskDetailQuota q where q.qcTaskDetail.id = :qcTaskDetailId";
                List<QcTaskDetailQuota> qcQuotaList = entityManager.createQuery(hql4, QcTaskDetailQuota.class)
                        .setParameter("qcTaskDetailId", qcTaskDetail.getId())
                        .getResultList();
                // 合格标志
                boolean qualified = true;
                for (QcTaskDetailQuota quota : qcQuotaList) {
                    if (quotaMap.containsKey(quota.getName())) {
                        InspectionTaskDetailQuota insDetailQuota = quotaMap.get(quota.getName());
                        // 设置检测值
                        quota.setInspectValue(insDetailQuota.getInspectValue());
                        // 设置检测结果
                        if (InspectResultEnum.Enum.QUALIFIED.name().equals(insDetailQuota.getInspectResult())) {
                            quota.setInspectResult(QcTaskDetailResultEnum.Enum.PASS.name());
                        } else {
                            quota.setInspectResult(QcTaskDetailResultEnum.Enum.NOT_PASS.name());
                            qualified = false;
                        }
                    }
                }
                fabosJsonDao.updateBatchById(qcQuotaList);
                // 设置质控任务明细状态为完成
                qcTaskDetail.setState(QcTaskDetailStateEnum.Enum.COMPLETE.name());
                // 如果指标中有任意指标不合格，则质控明细检测结论不合格，否则为合格
                if (qualified) {
                    qcTaskDetail.setInspectResult(QcTaskDetailResultEnum.Enum.PASS.name());
                } else {
                    qcTaskDetail.setInspectResult(QcTaskDetailResultEnum.Enum.NOT_PASS.name());
                }
            }
        }
        fabosJsonDao.updateBatchById(qcTaskDetailList);
        // 处理提交标志
        this.updateQcSubmissionFlag(qcTaskDetailList.get(0));
    }

    private void updateQcSubmissionFlag(QcTaskDetail qcTaskDetail) {
        // 如果明细上的任务都完成了，设置质控任务提交状态为1
        String hql2 = "from QcTask q join QcTaskDetail qd on qd.qcTask.id = q.id where qd.id = :detailId";
        QcTask qcTaskDb = fabosJsonDao.getEntityManager().createQuery(hql2, QcTask.class)
                .setParameter("detailId", qcTaskDetail.getId())
                .getResultList()
                .get(0);
        if (qcTaskDb == null) {
            return;
        }
        // 若子单都完成则设置提交状态为1
        String hql5 = "from QcTaskDetail q where q.qcTask.id = :qcTaskId";
        List<QcTaskDetail> detailList = fabosJsonDao.getEntityManager().createQuery(hql5, QcTaskDetail.class)
                .setParameter("qcTaskId", qcTaskDb.getId())
                .getResultList();
        long count = detailList.stream()
                .filter(detail ->
                        !QcTaskDetailStateEnum.Enum.COMPLETE.name().equals(detail.getState()))
                .count();
        if (count == 0) {
            qcTaskDb.setSubmissionFlag("1");
        }
        fabosJsonDao.update(qcTaskDb);
    }
}