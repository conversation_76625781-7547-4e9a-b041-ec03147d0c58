package cec.jiutian.bc.msa.strategy;

import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @description:
 */
@Component
@Slf4j
public class ReportGenerateStrategyFactory {

    @Autowired
    private List<ReportGenerateStrategy> reportGenerateStrategies;

    public ReportGenerateStrategy getImpl(String type){
        return reportGenerateStrategies.stream()
                .filter(handle -> handle.isTypeMatch(type))
                .findAny()
                .orElseThrow(()->new FabosJsonApiErrorTip("无对应报告模版"));
    }
}
