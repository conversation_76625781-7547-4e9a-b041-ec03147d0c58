package cec.jiutian.bc.qualityControl.domain.qcTask.service;

import cec.jiutian.bc.base.domain.experimentMethod.model.ExpMethod;
import cec.jiutian.bc.base.domain.experimentMethod.model.ExpMethodDetail;
import cec.jiutian.bc.compare.mto.SpecificationManageMTO;
import cec.jiutian.bc.generalModeler.util.StringUtils;
import cec.jiutian.bc.process.domain.inspectionTask.model.InspectionTask;
import cec.jiutian.bc.process.port.client.SpcProcessFeignClient;
import cec.jiutian.bc.qualityControl.domain.qcTask.model.QcTask;
import cec.jiutian.bc.qualityControl.domain.qcTask.model.QcTaskDetail;
import cec.jiutian.bc.qualityControl.dto.QcQueryDTO;
import cec.jiutian.bc.qualityControl.mto.EhsProductProcessMTO;
import cec.jiutian.bc.qualityControl.util.QcUtil;
import cec.jiutian.bc.qualityControl.vo.QcTaskVO;
import cec.jiutian.bc.qualityControl.vo.QueryVO;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cn.hutool.json.JSONUtil;
import jakarta.annotation.Resource;
import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.math3.stat.descriptive.DescriptiveStatistics;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class QcTaskService {
    @Resource
    private QcUtil qcUtil;
    @Resource
    private FabosJsonDao fabosJsonDao;
    @Resource
    private SpcProcessFeignClient spcProcessFeignClient;
    // 参考线值
    private static final String REFER_LINE = "80";

    /**
     * 查询质控样/标样列表
     * <AUTHOR>
     * @date 2025/6/16 11:34
     * @return
     */
    public List<QueryVO> getAllSampleList() {
        String sql = "select distinct lqt.materialCode, materialName\n" +
                " from QcTask lqt";
        return qcUtil.getCommonQueryList(sql);
    }

    /**
     * 查询质控检测指标by检测项目
     * <AUTHOR>
     * @date 2025/6/11 15:14
     * @param qcQueryDTO 查询参数
     * @param queryType 查询类型
     * @return
     */
    public List<QcTaskVO> getQcListByItem(QcQueryDTO qcQueryDTO, String queryType) {
        List<QcTaskVO> qcTaskVOList = new ArrayList<>();
        String quotaName = qcQueryDTO.getQuotaName();
        if (StringUtils.isBlank(quotaName)) {
            return qcTaskVOList;
        }
        String dateSeriesSql = qcUtil.getDateSeriesSql(qcQueryDTO);
        String sql = "date_display AS (\n" +
                "    SELECT \n" +
                "        date_value, \n" +
                "        TO_CHAR(date_value, 'FMMM月DD日') AS time_display\n" +
                "    FROM date_series\n" +
                "),\n" +
                "inspect_data AS (\n" +
                "   select lqt.finish_time\\:\\:date as finish_time, lq.inspect_value \n" +
                "   from lm_quantity_task_detail ld\n" +
                "   join lm_quantity_task_detail_quota lq on lq.qc_task_detail_id = ld.id \n" +
                "   join lm_quantity_task lqt on lqt.id = ld.qc_task_id \n" +
                "   where lqt.qc_method = \'" + queryType + "\' and lqt.current_state = 'COMPLETE'\n" +
                "       and lq.inspect_value is not null";

        // 实验室查询条件
        sql += addConditionIfNotBlank(qcQueryDTO.getExpLabName(), "lqt.belong_exp_lab_name");
        // 检测项目查询条件
        sql += addConditionIfNotBlank(qcQueryDTO.getItemCode(), "ld.inspection_item_code");
        // 拼接检测指标查询条件
        sql += addConditionIfNotBlank(quotaName, "lq.name");
        // 物料编码
        sql += addConditionIfNotBlank(qcQueryDTO.getMaterialCode(), "lqt.material_code");
        String sqlEnd = ")\n" +
                "SELECT \n" +
                "    d.time_display AS time_dimension,\n" +
                "    coalesce(i.inspect_value, '0') \n" +
                "FROM date_display d\n" +
                "LEFT JOIN inspect_data i ON d.date_value = i.finish_time\n" +
                "ORDER BY d.date_value";
        sql += sqlEnd;
        String nativeSql = dateSeriesSql + sql;
        EntityManager entityManager = fabosJsonDao.getEntityManager();
        Query query = entityManager.createNativeQuery(nativeSql);
        List resultList = query.getResultList();
        if (resultList.isEmpty()) {
            return qcTaskVOList;
        }
        for (Object o : resultList) {
            if (o instanceof Object[]) {
                QcTaskVO qcTaskVO = new QcTaskVO();
                Object[] resultArray = (Object[]) o;
                String time = String.valueOf(resultArray[0]);
                String value = String.valueOf(resultArray[1]);
                qcTaskVO.setTime(time);
                if (value == null) {
                    qcTaskVO.setValue("");
                } else {
                    qcTaskVO.setValue(value);
                }
                qcTaskVOList.add(qcTaskVO);
            }
        }
        qcUtil.setReferLineByType(qcTaskVOList.get(0), queryType);
        return qcTaskVOList;
    }

    /**
     * 查询短期质控检测指标by设备类型
     * <AUTHOR>
     * @date 2025/6/11 19:25
     * @param qcQueryDTO 查询参数
     * @param queryType 查询类型
     * @return
     */
    public List<QcTaskVO> getQcListByEquipType(QcQueryDTO qcQueryDTO, String queryType) {
        String sql = "select ld.test_device, lq.inspect_value \n" +
                "from lm_quantity_task_detail ld\n" +
                "join lm_quantity_task_detail_quota lq on lq.qc_task_detail_id = ld.id \n" +
                "join lm_quantity_task lqt on lqt.id = ld.qc_task_id \n" +
                "where lqt.qc_method = \'" + queryType + "\' and lqt.current_state = 'COMPLETE'\n" +
                "   and lq.inspect_value is not null ";
        List<QcTaskVO> list = this.getQcListByType(qcQueryDTO, sql, "1");
        if (CollectionUtils.isNotEmpty(list)) {
            qcUtil.setReferLineByType(list.get(0), queryType);
        }
        return list;
    }

    /**
     * 查询短期质控检测指标by设备编码
     * <AUTHOR>
     * @date 2025/6/12 11:21
     * @param qcQueryDTO 查询参数
     * @param queryType 查询类型
     * @return
     */
    public List<QcTaskVO> getQcListByEquipCode(QcQueryDTO qcQueryDTO, String queryType) {
        String sql = "select ld.test_device_code, lq.inspect_value \n" +
                "from lm_quantity_task_detail ld \n" +
                "join lm_quantity_task_detail_quota lq on lq.qc_task_detail_id = ld.id \n" +
                "join lm_quantity_task lqt on lqt.id = ld.qc_task_id \n" +
                "where lqt.qc_method = \'" + queryType + "\' and lqt.current_state = 'COMPLETE'\n" +
                "   and lq.inspect_value is not null ";
        List<QcTaskVO> list = this.getQcListByType(qcQueryDTO, sql, "2");
        if (CollectionUtils.isNotEmpty(list)) {
            qcUtil.setReferLineByType(list.get(0), queryType);
        }
        return list;
    }

    /**
     * 获取短期质控检测指标by人员
     * <AUTHOR>
     * @date 2025/6/12 13:56
     * @param qcQueryDTO 查询参数
     * @param queryType 查询类型
     * @return
     */
    public List<QcTaskVO> getQcListByPerson(QcQueryDTO qcQueryDTO, String queryType) {
        String sql = "select lpm.name, lq.inspect_value \n" +
                "from lm_quantity_task_detail ld\n" +
                "join lm_quantity_task_detail_quota lq on lq.qc_task_detail_id = ld.id \n" +
                "join lm_quantity_task lqt on lqt.id = ld.qc_task_id \n" +
                "join lm_person_manage lpm on lpm.id = lqt.execute_person_id \n" +
                "where lqt.qc_method = \'" + queryType + "\' and lqt.current_state = 'COMPLETE'\n" +
                "   and lq.inspect_value is not null ";
        List<QcTaskVO> list = this.getQcListByType(qcQueryDTO, sql, "3");
        if (CollectionUtils.isNotEmpty(list)) {
            qcUtil.setReferLineByType(list.get(0), queryType);
        }
        return list;
    }

    /**
     * 查询短期质控指标： by设备类型、by设备编码、by人员 公用方法
     * <AUTHOR>
     * @date 2025/6/12 11:13
     * @param qcQueryDTO 查询参数
     * @param sql 自定义查询sql
     * @param type 查询类型： 1 by设备类型; 2 by设备编码; 3 by人员
     * @return
     */
    public List<QcTaskVO> getQcListByType(QcQueryDTO qcQueryDTO, String sql, String type) {
        // 参数校验
        if (StringUtils.isAnyBlank(qcQueryDTO.getItemCode(), qcQueryDTO.getQuotaName())) {
            return Collections.emptyList();
        }
        // 构建基础查询条件
        String queryCondition = String.format(
                "   and ld.inspection_item_code = '%s' \n" +
                "   and lq.name = '%s'\n",
                qcQueryDTO.getItemCode(), qcQueryDTO.getQuotaName()
        );
        queryCondition += addConditionIfNotBlank(qcQueryDTO.getExpLabName(), "lqt.belong_exp_lab_name");
        // 添加类型相关条件
        switch (type) {
            case "1":
                queryCondition += addConditionIfNotBlank(qcQueryDTO.getEquipType(), "ld.test_device");
                break;
            case "2":
                queryCondition += addConditionIfNotBlank(qcQueryDTO.getEquipCode(), "ld.test_device_code");
                break;
            case "3":
                queryCondition += addConditionIfNotBlank(qcQueryDTO.getPersonId(), "lqt.execute_person_id");
                break;
        }
        // 添加时间条件
        queryCondition += buildTimeCondition(qcQueryDTO);
        // 添加质控样/标样筛选条件
        queryCondition += addConditionIfNotBlank(qcQueryDTO.getMaterialCode(), "lqt.material_code");
        // 便于查询完整sql，方便调试
        sql = sql + queryCondition;
        // 执行查询
        List<Object[]> resultList = fabosJsonDao.getEntityManager()
                .createNativeQuery(sql)
                .getResultList();
        if (CollectionUtils.isEmpty(resultList)) {
            return Collections.emptyList();
        }
        // 转换结果
        List<QcTaskVO> qcTaskVOList = resultList.stream()
                .filter(o -> o instanceof Object[])
                .map(o -> (Object[]) o)
                .map(arr -> createQcTaskVO((String) arr[0], (String) arr[1], type))
                .collect(Collectors.toList());
        // 分组计算统计值
        Map<String, List<String>> groupedData = qcTaskVOList.stream()
                .collect(Collectors.groupingBy(
                        getGroupingKey(type),
                        Collectors.mapping(QcTaskVO::getValue, Collectors.toList())
                ));
        return groupedData.entrySet().stream()
                .map(entry -> buildStatisticsVO(entry.getKey(), entry.getValue(), type))
                .collect(Collectors.toList());
    }


    // 辅助方法：添加条件（如果值不为空）
    private String addConditionIfNotBlank(String value, String field) {
        return StringUtils.isNotBlank(value) ? String.format(" and %s = '%s'\n", field, value) : "";
    }

    // 辅助方法：构建时间条件
    private String buildTimeCondition(QcQueryDTO qcQueryDTO) {
        if ("1".equals(qcQueryDTO.getIsDiyTime())) {
            return " and lqt.finish_time " + qcUtil.getTimeQueryCondition(qcQueryDTO.getTimeType());
        }
        StringBuilder condition = new StringBuilder();
        if (qcQueryDTO.getStartTime() != null) {
            condition.append(String.format(" and lqt.finish_time >= '%s'\n", qcQueryDTO.getStartTime()));
        }
        if (qcQueryDTO.getEndTime() != null) {
            condition.append(String.format(" and lqt.finish_time <= '%s'\n", qcQueryDTO.getEndTime()));
        }
        return condition.toString();
    }

    // 辅助方法：创建QcTaskVO对象
    private QcTaskVO createQcTaskVO(String label, String value, String type) {
        QcTaskVO vo = new QcTaskVO();
        switch (type) {
            case "1": vo.setEquipType(label); break;
            case "2": vo.setEquipCode(label); break;
            case "3": vo.setPersonName(label); break;
        }
        vo.setValue(value != null ? value.toString() : "");
        return vo;
    }

    // 辅助方法：获取分组键
    private Function<QcTaskVO, String> getGroupingKey(String type) {
        switch (type) {
            case "1": return QcTaskVO::getEquipType;
            case "2": return QcTaskVO::getEquipCode;
            case "3": return QcTaskVO::getPersonName;
            default: throw new IllegalArgumentException("Invalid type: " + type);
        }
    }

    // 辅助方法：构建统计VO
    private QcTaskVO buildStatisticsVO(String label, List<String> values, String type) {
        QcTaskVO vo = new QcTaskVO();
        switch (type) {
            case "1": vo.setEquipType(label); break;
            case "2": vo.setEquipCode(label); break;
            case "3": vo.setPersonName(label); break;
        }
        DescriptiveStatistics stats = new DescriptiveStatistics();
        values.stream().map(Double::parseDouble).forEach(stats::addValue);
        vo.setMin(String.valueOf(stats.getMin()));
        vo.setMax(String.valueOf(stats.getMax()));
        vo.setMedian(String.valueOf(stats.getPercentile(50)));
        vo.setQ1(String.valueOf(stats.getPercentile(25)));
        vo.setQ3(String.valueOf(stats.getPercentile(75)));
        return vo;
    }

    /**
     * 查询短期质控超限分布by实验室
     * <AUTHOR>
     * @date 2025/6/13 16:58
     * @param qcQueryDTO 查询参数
     * @param queryType 查询类型
     * @return
     */
    public List<QcTaskVO> getQcDisListByExplab(QcQueryDTO qcQueryDTO, String queryType) {
        String timeQueryCondition = this.buildTimeCondition2(qcQueryDTO);
        String sql =
                "with test_data as (\n" +
                "   select lel.exp_lab_name, COUNT(lq.id) as outCount \n" +
                "   from lm_exp_lab lel \n" +
                "   join lm_quantity_task l on l.belong_exp_lab_name = lel.exp_lab_name \n" +
                "       and l.current_state = 'COMPLETE' \n" +
                "       and l.qc_method = \'" + queryType + "\' \n" +
                "   join lm_quantity_task_detail ld on ld.qc_task_id = l.id \n" +
                "   join lm_quantity_task_detail_quota lq on lq.qc_task_detail_id = ld.id \n" +
                "       and (CAST(lq.inspect_value as double precision) > lq.upper_value \n" +
                "            or CAST(lq.inspect_value as double precision) < lq.lower_value) \n" +
                "   where lq.inspect_value is not null \n";
        sql = sql + timeQueryCondition;
        String sql2 =
                "   group by lel.exp_lab_name \n" +
                "),\n" +
                "total_count as ( \n" +
                "   select COALESCE(SUM(outCount), 0) AS total  \n" +
                "   from test_data \n" +
                ")\n" +
                "select le.exp_lab_name, coalesce(t.outCount, 0) as outCount, \n" +
                "   ROUND(case when (select total from total_count) = 0 then 0 \n" +
                "       else coalesce(t.outCount, 0) * 100.0 / (select total from total_count) \n" +
                "       end, 2) as totalRatio \n" +
                "from lm_exp_lab le \n" +
                "left join test_data t on t.exp_lab_name = le.exp_lab_name \n";
        sql = sql + sql2;
        // 拼接查询结果
        String expLabName = qcQueryDTO.getExpLabName();
        if (StringUtils.isNotBlank(expLabName)) {
            sql += "where le.exp_lab_name = \'" + expLabName + "\'\n";
        }
        String sqlEnd = "order by outCount desc";
        sql += sqlEnd;
        List<QcTaskVO> commonResltVOList = qcUtil.getCommonResltVOList(sql, "1");
        QcTaskVO qcTaskVO = commonResltVOList.get(0);
        qcTaskVO.setReferLine1(REFER_LINE);
        return commonResltVOList;
    }

    // 自定义查询条件
    private String buildTimeCondition2(QcQueryDTO qcQueryDTO) {
        if ("1".equals(qcQueryDTO.getIsDiyTime())) {
            return " and l.finish_time " + qcUtil.getTimeQueryCondition(qcQueryDTO.getTimeType());
        }
        StringBuilder condition = new StringBuilder();
        if (qcQueryDTO.getStartTime() != null) {
            condition.append(String.format(" and l.finish_time >= '%s'\n", qcQueryDTO.getStartTime()));
        }
        if (qcQueryDTO.getEndTime() != null) {
            condition.append(String.format(" and l.finish_time <= '%s'\n", qcQueryDTO.getEndTime()));
        }
        return condition.toString();
    }

    /**
     * 查询短期质控超限分布by检测室
     * <AUTHOR>
     * @date 2025/6/13 18:01
     * @param qcQueryDTO 查询参数
     * @param queryType 查询类型
     * @return
     */
    public List<QcTaskVO> getQcDisListByLab(QcQueryDTO qcQueryDTO, String queryType) {
        String timeQueryCondition = this.buildTimeCondition2(qcQueryDTO);
        String sql =
                "with test_data as (\n" +
                        "   select l.belong_lab_name as labName, COUNT(lq.id) as outCount \n" +
                        "   from lm_quantity_task l\n" +
                        "   join lm_quantity_task_detail ld on ld.qc_task_id = l.id \n" +
                        "   join lm_quantity_task_detail_quota lq on lq.qc_task_detail_id = ld.id \n" +
                        "       and (CAST(lq.inspect_value as double precision) > lq.upper_value \n" +
                        "            or CAST(lq.inspect_value as double precision) < lq.lower_value) " +
                        "   where lq.inspect_value is not null \n" +
                        "       and l.qc_method = \'" + queryType + "\' \n";
        sql = sql + timeQueryCondition;
        String sql2 =
                "   group by l.belong_lab_name \n" +
                        "),\n" +
                        "total_count as ( \n" +
                        "   select COALESCE(SUM(outCount), 0) AS total  \n" +
                        "   from test_data \n" +
                        ")\n" +
                        "select le.lab_name, coalesce(t.outCount, 0) as outCount, \n" +
                        "   ROUND(case when (select total from total_count) = 0 then 0 \n" +
                        "       else coalesce(t.outCount, 0) * 100.0 / (select total from total_count) \n" +
                        "       end, 2) as totalRatio \n" +
                        "from lm_lab le \n" +
                        "left join test_data t on t.labName = le.lab_name \n" +
                        "join lm_exp_lab lel on lel.id = le.exp_lab_id \n";
        sql = sql + sql2;
        // 实验室、检测室查询条件
        String sql3 = "where '1' = '1' \n";
        String expLabName = qcQueryDTO.getExpLabName();
        String labName = qcQueryDTO.getLabName();
        if (StringUtils.isNotBlank(expLabName)) {
            sql3 += "and lel.exp_lab_name = \'" + expLabName + "\'\n";
        }
        if (StringUtils.isNotBlank(labName)) {
            sql3 += "and le.lab_name = \'" + labName + "\'\n";
        }
        sql += sql3 + "order by outCount desc";
        List<QcTaskVO> commonResltVOList = qcUtil.getCommonResltVOList(sql, "2");
        QcTaskVO qcTaskVO = commonResltVOList.get(0);
        qcTaskVO.setReferLine1(REFER_LINE);
        return commonResltVOList;
    }

    /**
     * 获取 长期/准确性 质控检测指标列表by检测项目
     * <AUTHOR>
     * @date 2025/6/16 16:56
     * @param qcQueryDTO 查询参数
     * @param queryType 查询类型: 长期、准确性
     * @return
     */
    public Map<String, List<QcTaskVO>>  getLongOrAccQcListByItem(QcQueryDTO qcQueryDTO, String queryType) {
        Map<String, List<QcTaskVO>> map = new HashMap<>();
        List<QueryVO> allSampleList = this.getAllSampleList();
        for (QueryVO queryVO : allSampleList) {
            qcQueryDTO.setMaterialCode(queryVO.getValue());
            List<QcTaskVO> qcTaskVOList = this.getQcListByItem(qcQueryDTO, queryType);
            map.put(queryVO.getLabel(), qcTaskVOList);
        }
        return map;
    }
    public void sendSPCData(QcTask qcTask){
        if(qcTask==null){
            return;
        }
        try {
            this.sendSPCByQcTask(qcTask);
        }catch (Exception e){
            log.error("质控任务提交------------》发送spc生成控制图异常",e);
        }
    }

    private void sendSPCByQcTask(QcTask qcTask){
        Map<String,Object> params = new HashMap<>();
        params.put("subGroupSize",1);
        //物料
        SpecificationManageMTO query = new SpecificationManageMTO();
        query.setCode(qcTask.getMaterialCode());
        SpecificationManageMTO specificationManageMTO = fabosJsonDao.selectOne(query);
        if(specificationManageMTO!=null){
            params.put("materialId",specificationManageMTO.getId());
            params.put("materialName",specificationManageMTO.getName());
        }
        //车间  产线 工序
        this.handleControlLocation(qcTask,params);
        //指标数据
        for (QcTaskDetail detail : qcTask.getQcTaskDetails()){
            ExpMethod exQuery = new ExpMethod();
            //检测项目标号和实验方法编号是同一个
            exQuery.setGeneralCode(detail.getInspectionItemCode());
            ExpMethod expMethod = fabosJsonDao.selectOne(exQuery);
            if(expMethod==null){
                continue;
            }
            //同一个试验方法下指标名称唯一
            Map<String, String> map = expMethod.getExpMethodDetails().stream()
                    .collect(Collectors.toMap(
                            ExpMethodDetail::getName, // key: name
                            ExpMethodDetail::getId   // value: id
                    ));
            detail.getQcTaskDetailQuotas().forEach(detailQuota -> {
                //指标id  通过实验方法编码和指标名称获取
                params.put("inspectionTargetId",map.get(detailQuota.getName()));
                params.put("inspectionTargetName","LIMS_"+expMethod.getName()+"_"+detailQuota.getName());
                List<Map<String,String>> items = new ArrayList<>();
                Map<String,String> item = new HashMap<>();
                item.put("itemValue", detailQuota.getInspectValue());
                items.add(item);
                params.put("itemList",items);
                this.sendSpc(params);
            });
        }

    }

    private void handleControlLocation(QcTask qcTask,Map<String, Object> params){
        if(qcTask!=null){
            params.put("workshopId",qcTask.getFactoryAreaId());
            params.put("workshopName",qcTask.getFactoryAreaName());

            params.put("productionLineId",qcTask.getFactoryLineId());
            params.put("productionLineName",qcTask.getFactoryLineName());

            if(cec.jiutian.common.util.StringUtils.isNotBlank(qcTask.getProcessCode())){
                EhsProductProcessMTO pQuery = new EhsProductProcessMTO();
                pQuery.setCode(qcTask.getProcessCode());
                EhsProductProcessMTO productProcessMTO = fabosJsonDao.selectOne(pQuery);
                if(productProcessMTO!=null){
                    params.put("processId",productProcessMTO.getId());
                    params.put("processOperationName",productProcessMTO.getName());

                }
            }
        }
    }
    /**
     * 发送spc生成控制图
     * @param params
     */
    private void sendSpc(Map<String,Object> params){
        log.info("质控任务发送spc生成控制图,请求参数：{}", JSONUtil.toJsonStr(params));
        String string = spcProcessFeignClient.processData(params);
        log.info("质控任务发送spc生成控制图结果{}", string);
    }
}
