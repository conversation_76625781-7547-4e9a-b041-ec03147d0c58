package cec.jiutian.bc.lineEdge.domain.stocktakingOrder.handler;

import cec.jiutian.bc.base.domain.materialCategory.model.LimsMaterialCategory;
import cec.jiutian.bc.base.domain.warehouse.model.LimsWarehouse;
import cec.jiutian.bc.lineEdge.domain.stocktakingOrder.model.LineStocktakingOrder;
import cec.jiutian.bc.person.domain.personManage.model.PersonManage;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：2024/11/14 18:13
 * @description：
 */
@Component
public class LineStocktakingOrderDynamicHandler implements DependFiled.DynamicHandler<LineStocktakingOrder> {

    @Resource
    private FabosJsonDao fabosJsonDao;
    @Override
    public Map<String, Object> handle(LineStocktakingOrder lineStocktakingOrder) {
        Map<String, Object> result = new HashMap<>();
        if(lineStocktakingOrder.getKsStocktakingPlan()==null){
            return result;
        }
        if(lineStocktakingOrder.getKsStocktakingPlan().getStocktakingRange()!=null){
            LimsMaterialCategory limsMaterialCategory = fabosJsonDao.findById(LimsMaterialCategory.class, lineStocktakingOrder.getKsStocktakingPlan().getStocktakingRange().getId());
            result.put("stocktakingRange", limsMaterialCategory);
        }
        if(lineStocktakingOrder.getKsStocktakingPlan().getStocktakingPerson()!=null){
            PersonManage personManage = fabosJsonDao.findById(PersonManage.class, lineStocktakingOrder.getKsStocktakingPlan().getStocktakingPerson().getId());
            result.put("stocktakingPerson", personManage);
        }
        if(lineStocktakingOrder.getKsStocktakingPlan().getStocktakingReviewPerson()!=null){
            PersonManage personManage = fabosJsonDao.findById(PersonManage.class, lineStocktakingOrder.getKsStocktakingPlan().getStocktakingReviewPerson().getId());
            result.put("stocktakingReviewPerson", personManage);
        }
        if(lineStocktakingOrder.getKsStocktakingPlan().getLimsWarehouse()!=null){
            LimsWarehouse limsWarehouse = fabosJsonDao.findById(LimsWarehouse.class, lineStocktakingOrder.getKsStocktakingPlan().getLimsWarehouse().getId());
            result.put("limsWarehouse", limsWarehouse);
        }
        return result;
    }
}
