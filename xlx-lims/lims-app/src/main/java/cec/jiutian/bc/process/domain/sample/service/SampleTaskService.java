package cec.jiutian.bc.process.domain.sample.service;

import cec.jiutian.api.remoteCallLog.dto.RemoteCallResult;
import cec.jiutian.bc.compare.domain.comparePlan.service.ComparePlanService;
import cec.jiutian.bc.compare.util.BcUtil;
import cec.jiutian.bc.ecs.enums.MessageWayEnum;
import cec.jiutian.bc.modeler.enumration.TaskBusinessStateEnum;
import cec.jiutian.bc.msa.domain.plan.service.MsaPlanService;
import cec.jiutian.bc.process.domain.inspectionTask.model.InspectionTask;
import cec.jiutian.bc.process.domain.sample.model.SampleTask;
import cec.jiutian.bc.process.domain.sample.utils.AlarmNotifyUtil;
import cec.jiutian.bc.process.domain.sample.utils.ConvertDataUtil;
import cec.jiutian.bc.process.enumeration.CheckResultEnum;
import cec.jiutian.bc.process.enumeration.InspectionTaskStateEnum;
import cec.jiutian.bc.process.enumeration.TaskStatusEnum;
import cec.jiutian.bc.process.enumeration.UnqualifiedHandleWayEnum;
import cec.jiutian.bc.process.port.client.SampleFeignClient;
import cec.jiutian.bc.urm.domain.user.entity.User;
import cec.jiutian.common.util.StringUtils;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import jakarta.annotation.Resource;
import jakarta.persistence.TypedQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Transactional
@Slf4j
public class SampleTaskService {
    // 待收样
    private static final String TO_BE_SAMPLED = TaskStatusEnum.Enum.TO_BE_SAMPLED.name();
    // 待检测
    private static final String TO_BE_TESTED = TaskStatusEnum.Enum.TO_BE_TESTED.name();
    // 告警标题
    private static final String ALARM_TITLE = "样品任务超时告警";

    @Resource
    private FabosJsonDao fabosJsonDao;
    @Resource
    private SampleFeignClient sampleFeignClient;
    @Resource
    private BcUtil bcUtil;
    @Resource
    private MsaPlanService msaPlanService;
    @Resource
    private ComparePlanService comparePlanService;
    @Resource
    private AlarmNotifyUtil alarmNotifyUtil;

    /**
     * 告警通知：样品任务-收样超时告警(只针对待收样的数据)
     * <AUTHOR>
     * @date 2025/3/21 14:55
     * 触发条件：<1>当前时间 - 执行取样的时间 > 阈值时间;</>
     *         <2>样品任务 还没有填写收样人（有收样人表示已收样）
     * 轮询时间：每隔 配置时间 进行调用
     * 告警对象：lims管理员
     */
    public void alarmNotify() {
        // 查询出所有"待收样"的 样品任务数据
        SampleTask sampleTaskQuery = new SampleTask();
        sampleTaskQuery.setTaskStatus(TO_BE_SAMPLED);
        List<SampleTask> taskList = fabosJsonDao.select(sampleTaskQuery);
        if (CollectionUtils.isEmpty(taskList)) {
            return;
        }
        taskList.forEach(sampleTask -> {
            // 判断两个时间是否超过阈值时间
            Date executeSampleTime = sampleTask.getExecuteSampleTime();
            if (executeSampleTime != null) {
                boolean overAlarmThreshold = alarmNotifyUtil.isOverAlarmThreshold(new Date(),
                        sampleTask.getExecuteSampleTime());
                // 时间超过阈值 并且样品任务还没有填写收样人
                if (overAlarmThreshold && sampleTask.getReceivePerson() == null) {
                    // 获取lims管理员
                    Set<User> notifyUserSet = bcUtil.getLimsRoleUserSet("1");
                    if (sampleTask.getReceivePerson() != null) {
                        User user = bcUtil.getUserByPersonManage(sampleTask.getReceivePerson());
                        if (user != null) {
                            notifyUserSet.add(user);
                        }
                    }
                    if (CollectionUtils.isNotEmpty(notifyUserSet)) {
                        Set<String> phoneSet = notifyUserSet.stream()
                                .map(user -> user.getPhoneNumber())
                                .collect(Collectors.toSet());
                        // 调用告警通知服务
                        log.info("{} 样品任务收样超时告警", sampleTask.getGeneralCode());
                        String content = "样品任务收样超期，任务单号：" + sampleTask.getGeneralCode() + "。";
                        bcUtil.sendPersonMessage(ALARM_TITLE, content, null, MessageWayEnum.App, phoneSet);
                    }
                }
            }
        });
    }

    /**
     * lims收样  查询qms
     * @param generalCode
     * @return
     */
    public Map<String, Object> getQmsSampleTask(String generalCode){
        HashMap<String, Object> param = new HashMap<>();
        param.put("samplingTaskCode", generalCode);
        RemoteCallResult<Map<String,Object>> qmsSampleTask = sampleFeignClient.getSamplingTask(param);
        if(qmsSampleTask==null||qmsSampleTask.getData()==null||qmsSampleTask.getData().get("samplingTaskCode")==null){
            throw new FabosJsonApiErrorTip("无样品数据");
        }
        if(qmsSampleTask.getData().get("businessState")==null&&StringUtils.isBlank(qmsSampleTask.getData().get("businessState").toString())){
            throw new FabosJsonApiErrorTip("qms状态businessState异常");
        }
        String businessState = qmsSampleTask.getData().get("businessState").toString();
        //cec.jiutian.bc.modeler.enumration.TaskBusinessStateEnum   qms状态枚举
        if(TaskBusinessStateEnum.Enum.BE_SAMPLING.name().equals(businessState)){
            throw new FabosJsonApiErrorTip("qms样品任务状态异常,请执行取样");
        }
        if(TaskBusinessStateEnum.Enum.SAMPLING_FINISH.name().equals(businessState)){
            throw new FabosJsonApiErrorTip("qms样品任务状态异常,请执行送样");
        }
        // SEND_SAMPLE("已送样"), RECEIVED_SAMPLE("已收样"),   可以进行收样
        if(TaskBusinessStateEnum.Enum.SEND_SAMPLE.name().equals(businessState)||TaskBusinessStateEnum.Enum.RECEIVED_SAMPLE.name().equals(businessState)){
            return qmsSampleTask.getData();
        }else {
            throw new FabosJsonApiErrorTip("qms样品任务状态异常");
        }
    }


    public void receiveSampleResultToQMSByGeneralCode(String generalCode){
        SampleTask query = new SampleTask();
        query.setGeneralCode(generalCode);
        SampleTask sampleTask = fabosJsonDao.selectOne(query);
        Map<String, Object> params = new HashMap<>();
        params.put("samplingTaskCode", sampleTask.getGeneralCode());
        params.put("appearanceInspect",sampleTask.getAppearanceCheck());
        params.put("receivePersonId",sampleTask.getReceivePerson().getId());
        params.put("receivePerson",sampleTask.getReceivePerson().getName());
        params.put("reviewQuantity",sampleTask.getCheckQuantity());
        params.put("receiveSampleDate", DateUtil.format(sampleTask.getReceiveTime(), DatePattern.NORM_DATETIME_FORMATTER));
        //如果外观检测不合格  需要传异常处置流程
        if(CheckResultEnum.Enum.UNQUALIFIED.name().equals(sampleTask.getAppearanceCheck())){
            //不合格   qms 不合格处理枚举：UnqualifiedHandleWayEnum
            params.put("unqualifiedHandle", ConvertDataUtil.getQmsUnqualifiedHandleWayByLims(UnqualifiedHandleWayEnum.Enum.valueOf(sampleTask.getUnqualifiedHandleWay())));
            if(StringUtils.isNotBlank(sampleTask.getDiscoverPersonId())){
                params.put("discoveredPersonId", sampleTask.getDiscoverPersonId());
            }
            if(sampleTask.getDiscoverTime()!=null){
                //时间转时分秒格式的字符串
                String time = DateUtil.format(sampleTask.getDiscoverTime(), DatePattern.NORM_DATETIME_FORMATTER);
                params.put("submissionTime",time);
            }
            if(StringUtils.isNotBlank(sampleTask.getAttachment())){
                params.put("abnormalAttachments", sampleTask.getAttachment());
            }
            if(StringUtils.isNotBlank(sampleTask.getUnqualifiedDesc())){
                params.put("unqualifiedDesc", sampleTask.getUnqualifiedDesc());
            }
        }
        log.info("收样同步到qms数据:{}", JSONUtil.toJsonStr(params));
        sampleFeignClient.receiveSampleResult(params);
    }


    //收样过后外观合格就同步状态 需要同步的有检测任务。msa和对标的明细
    public void syncSampleTaskState(String sampleNo){
        List<InspectionTask> inspectionTasks = this.updateInsTask(sampleNo);
        if(CollectionUtils.isNotEmpty(inspectionTasks)){
            inspectionTasks.forEach(d->{
                //对标和msa都需要改为检测中
                msaPlanService.syncTaskState(d);
                comparePlanService.syncDetailTaskState(d);
            });
        }
    }

    private List<InspectionTask> updateInsTask(String sampleNo){
        // 创建 HQL 查询
        String hql ="SELECT t FROM InspectionTask t WHERE t.checkType IN ('CONTROL','COMPARE','MSA') and t.taskStatus = 'TAKING_SAMPLE' and t.sampleTaskNo = :sampleNo";
        TypedQuery<InspectionTask> q = fabosJsonDao.getEntityManager().createQuery(
                hql, InspectionTask.class
        );
        // 设置查询参数
        q.setParameter("sampleNo", sampleNo);
        // 执行查询并获取结果
        List<InspectionTask> resultList = q.getResultList();
        if(CollectionUtils.isNotEmpty(resultList)){
            //不为空
            resultList.forEach(d->{
                d.setTaskStatus(InspectionTaskStateEnum.Enum.INSPECTING.name());
                fabosJsonDao.mergeAndFlush(d);
            });
        }
        return resultList;
    }
}
