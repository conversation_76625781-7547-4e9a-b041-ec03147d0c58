package cec.jiutian.bc.qualityControl.domain.qcPlan.model;

import cec.jiutian.bc.base.enumeration.LmNamingRuleCodeEnum;
import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleBaseModel;
import cec.jiutian.bc.person.domain.personManage.model.PersonManage;
import cec.jiutian.bc.process.domain.sample.handler.SampleTaskRunModifyHandler;
import cec.jiutian.bc.qualityControl.domain.qcLedger.model.QcLedger;
import cec.jiutian.bc.qualityControl.domain.qcLedger.proxy.QcLedgerDataProxy;
import cec.jiutian.bc.qualityControl.domain.qcPlan.handler.QcPlanOperationHandler;
import cec.jiutian.bc.qualityControl.domain.qcPlan.proxy.QcPlanDataProxy;
import cec.jiutian.bc.qualityControl.enumeration.QcMethodEnum;
import cec.jiutian.bc.qualityControl.enumeration.QcPlanCycleEnum;
import cec.jiutian.bc.qualityControl.enumeration.QcPlanStatusEnum;
import cec.jiutian.core.frame.constant.YesOrNoStatus;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.*;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowBaseOperation;
import cec.jiutian.view.type.RowOperation;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @description:
 */
@FabosJson(name = "质控计划",
        orderBy = "QcPlan.createTime desc",
        power = @Power(export = false),
        dataProxy = QcPlanDataProxy.class,
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "currentStatus != 'PUBLISH'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "currentStatus != 'PUBLISH'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                )
        },
        rowOperation = {
                @RowOperation(
                        title = "发布",
                        code = "QcPlan@PUBLISH",
                        operationParam={"EFFECTIVE"},
                        operationHandler = QcPlanOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "请确认是否执行发布操作？",
                        ifExpr = "currentStatus != 'PUBLISH'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "QcPlan@PUBLISH"
                        )
                ),
                @RowOperation(
                        title = "作废",
                        code = "QcPlan@VOID",
                        operationParam={"DEPRECATED"},
                        operationHandler = QcPlanOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "请确认是否执行作废操作",
                        ifExpr = "currentStatus != 'EFFECTIVE'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "QcPlan@VOID"
                        )
                ),
        }
)
@Entity
@Getter
@Setter
@Table(name = "lm_quantity_plan")
@FabosJsonI18n
public class QcPlan extends NamingRuleBaseModel {
    @Override
    public String getNamingCode() {
        return LmNamingRuleCodeEnum.LM_QC_PLAN.name();
    }
    //盘点人员
    @ManyToOne
    @FabosJsonField(
            views = @View(title = "质控台账", column = "qcProjectName"),
            edit = @Edit(title = "质控台账",
                    type = EditType.REFERENCE_TABLE,
                    filter = @Filter("QcLedger.effective = 'Y'"),
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "qcProjectName"),
                    search = @Search())
    )
    private QcLedger qcLedger;
    @FabosJsonField(
            views = @View(title = "质控项目编号"),
            edit = @Edit(title = "质控项目编号", notNull = true, search = @Search(vague = true),readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "qcLedger", beFilledBy = "generalCode"))
    )
    private String qcProjectCode;
    @FabosJsonField(
            views = @View(title = "质控项目名称"),
            edit = @Edit(title = "质控项目名称", notNull = true, search = @Search(vague = true),readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "qcLedger", beFilledBy = "qcProjectName"))
    )
    private String qcProjectName;

    @FabosJsonField(
            views = @View(title = "质控方式"),
            edit = @Edit(title = "质控方式", notNull = true, type = EditType.CHOICE, search = @Search(vague = true),
                    choiceType = @ChoiceType(fetchHandler = QcMethodEnum.class),readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "qcLedger", beFilledBy = "qcMethod"))
    )
    private String qcMethod;

    @FabosJsonField(
            views = @View(title = "实施范围", toolTip = true),
            edit = @Edit(title = "实施范围", notNull = true, type = EditType.TEXTAREA, inputType = @InputType(length = 400),readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "qcLedger", beFilledBy = "impScope"))
    )
    private String impScope;

    @FabosJsonField(
            views = @View(title = "判定依据", toolTip = true),
            edit = @Edit(title = "判定依据", notNull = true, type = EditType.TEXTAREA, inputType = @InputType(length = 400),readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "qcLedger", beFilledBy = "judAccord"))
    )
    private String judAccord;

    @FabosJsonField(
            views = @View(title = "判定标准", toolTip = true),
            edit = @Edit(title = "判定标准", notNull = true, type = EditType.TEXTAREA, inputType = @InputType(length = 400),readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "qcLedger", beFilledBy = "judStandard"))
    )
    private String judStandard;

    @FabosJsonField(
            views = @View(title = "质控操作", toolTip = true),
            edit = @Edit(title = "质控操作", notNull = true, type = EditType.TEXTAREA, inputType = @InputType(length = 400),readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "qcLedger", beFilledBy = "qcOperate"))
    )
    private String qcOperate;

    //计划开始时间
    @FabosJsonField(
            views = @View(title = "计划开始日期"),
            edit = @Edit(title = "计划开始日期", notNull = true, type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE),
                    inputType = @InputType(length = 40))
    )
    private Date planStartDate;
    //计划结束时间
    @FabosJsonField(
            views = @View(title = "计划截止日期"),
            edit = @Edit(title = "计划截止日期", notNull = true, type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE),
                    inputType = @InputType(length = 40))
    )
    private Date planEndDate;
    @FabosJsonField(
            views = @View(title = "周期",show = false),
            edit = @Edit(title = "周期",notNull = true,inputGroup = @InputGroup(postfix = "#{cycle}")
            )
    )
    private Integer cycleNum;
    @FabosJsonField(
            views = @View(title = "周期"),
            edit = @Edit(title = "周期",search = @Search(),notNull = true,type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = QcPlanCycleEnum.class),defaultVal = "DAY")
    )
    private String cycle;
    @FabosJsonField(
            views = @View(title = "是否取样"),
            edit = @Edit(title = "是否取样",search = @Search(),notNull = true,type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = YesOrNoStatus.ChoiceFetch.class))
    )
    private String sampling;
    //盘点人员
    @ManyToOne
    @FabosJsonField(
            views = @View(title = "执行人员", column = "name"),
            edit = @Edit(title = "执行人员",
                    type = EditType.REFERENCE_TABLE,
                    filter = @Filter("PersonManage.personState = 'AVAILABLE'"),
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    search = @Search())
    )
    private PersonManage stocktakingPerson;
    //备注
    @FabosJsonField(
            views = @View(title = "备注",toolTip = true),
            edit = @Edit(title = "备注", type = EditType.TEXTAREA, inputType = @InputType(length = 400))
    )
    private String remark;
    //状态
    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",type = EditType.CHOICE,show = false,
                    choiceType = @ChoiceType(fetchHandler = QcPlanStatusEnum.class)
        )
    )
    private String currentStatus;


}
