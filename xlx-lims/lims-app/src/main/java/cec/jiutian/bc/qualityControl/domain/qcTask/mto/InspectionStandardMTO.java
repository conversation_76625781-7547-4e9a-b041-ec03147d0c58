package cec.jiutian.bc.qualityControl.domain.qcTask.mto;

import cec.jiutian.bc.generalModeler.enumeration.YesOrNoEnum;
import cec.jiutian.bc.qualityControl.enumeration.StatusEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.Search;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OrderBy;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@FabosJson(
        name = "质检标准MTO",
        orderBy = "InspectionStandardMTO.createTime desc"
)
@Table(name = "bd_inspection_standard")
@Entity
@Getter
@Setter
@TemplateType(type = "multiTable")
public class InspectionStandardMTO extends MetaModel {
    @FabosJsonField(
            views = @View(title = "质检标准编码"),
            edit = @Edit(title = "质检标准编码")
    )
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "名称"),
            edit = @Edit(title = "名称", search = @Search(vague = true))
    )
    private String name;

    @FabosJsonField(
            views = @View(title = "物料/产品编码"),
            edit = @Edit(title = "物料/产品编码")
    )
    private String materialCode;

    @FabosJsonField(
            views = @View(title = "物料/产品名称"),
            edit = @Edit(title = "物料/产品名称")
    )
    private String materialName;

    @FabosJsonField(
            views = @View(title = "工艺编码"),
            edit = @Edit(title = "工艺编码")
    )
    private String processCode;

    @FabosJsonField(
            views = @View(title = "工艺名称"),
            edit = @Edit(title = "工艺名称")
    )
    private String processName;

    @FabosJsonField(
            views = @View(title = "车间ID", show = false),
            edit = @Edit(title = "车间ID", show = false)
    )
    private String factoryAreaId;

    @FabosJsonField(
            views = @View(title = "车间名称"),
            edit = @Edit(title = "车间名称")
    )
    private String factoryAreaName;

    @FabosJsonField(
            views = @View(title = "产线Id", show = false),
            edit = @Edit(title = "产线Id", show = false)
    )
    private String factoryLineId;

    @FabosJsonField(
            views = @View(title = "产线名称"),
            edit = @Edit(title = "产线名称")
    )
    private String factoryLineName;

    @FabosJsonField(
            views = @View(title = "描述"),
            edit = @Edit(title = "描述", type = EditType.TEXTAREA)
    )
    private String description;

    @FabosJsonField(
            views = @View(title = "是否留样"),
            edit = @Edit(title = "是否留样", type = EditType.CHOICE, search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = YesOrNoEnum.class))
    )
    private String keepSampleFlag;

    @FabosJsonField(
            views = @View(title = "留样量"),
            edit = @Edit(title = "留样量")
    )
    private Double sampleQuantity;

    @FabosJsonField(
            views = @View(title = "留样单位"),
            edit = @Edit(title = "留样单位")
    )
    private String sampleUnit;

    @FabosJsonField(
            views = @View(title = "适用检验类型"),
            edit = @Edit(title = "适用检验类型")
    )
    private String type;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态", type = EditType.CHOICE, search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = StatusEnum.class))
    )
    private String status;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "inspection_standard_id")
    @OrderBy
    @FabosJsonField(
            edit = @Edit(title = "质检标准详情", type = EditType.TAB_TABLE_ADD),
            views = @View(title = "质检标准详情", type = ViewType.TABLE_VIEW)
    )
    private List<InspectionStandardDetailMTO> details;

}
