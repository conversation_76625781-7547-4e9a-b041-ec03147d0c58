package cec.jiutian.bc.qualityControl.domain.qcLedger.proxy;

import cec.jiutian.bc.person.domain.personManage.model.PersonManage;
import cec.jiutian.bc.qualityControl.domain.qcLedger.model.QcLedger;
import cec.jiutian.bc.qualityControl.enumeration.EffectiveEnum;
import cec.jiutian.bc.qualityControl.enumeration.QcTypeEnum;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@Component
public class QcLedgerDataProxy implements DataProxy<QcLedger> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public void beforeAdd(QcLedger qcLedger) {
        // 设置创建时间
        qcLedger.setCreTime(new Date());
        this.setQcLeader(qcLedger);
    }

    @Override
    public void beforeUpdate(QcLedger qcLedger) {
        this.setQcLeader(qcLedger);
        if (QcTypeEnum.Enum.ENV_QC.name().equals(qcLedger.getQcType())) {
            qcLedger.setEquipment(null);
        }
    }

    /**
     * 设置主单数据
     * <AUTHOR>
     * @date 2025/3/25 15:13
     * @param q
     */
    private void setQcLeader(QcLedger q) {
        // 装载创建人
        String hql = "FROM PersonManage pm WHERE pm.user.id = :userId";
        EntityManager entityManager = fabosJsonDao.getEntityManager();
        TypedQuery<PersonManage> query = entityManager.createQuery(hql, PersonManage.class);
        query.setParameter("userId", UserContext.getUserId());
        List<PersonManage> personManageList = query.getResultList();
        if (!CollectionUtils.isEmpty(personManageList)) {
            q.setCreatePerson(personManageList.get(0));
        }
        // 设置默认的启用状态, 默认启用
        if (q.getEffective() == null) {
            q.setEffective(EffectiveEnum.Enum.Y.name());
        }
    }

}
