package cec.jiutian.bc.keepSample.domain.stocktakingPlan.service;

import cec.jiutian.bc.keepSample.domain.stocktakingOrder.service.StocktakingOrderService;
import cec.jiutian.bc.keepSample.domain.stocktakingPlan.model.KsStocktakingPlan;
import cec.jiutian.bc.keepSample.domain.stocktakingPlan.utils.StocktakingDateUtil;
import cec.jiutian.bc.keepSample.enumeration.StocktakingCycleEnum;
import cec.jiutian.bc.lineEdge.domain.stocktakingOrder.service.LineStocktakingOrderService;
import cec.jiutian.bc.lineEdge.domain.stocktakingPlan.model.LineStocktakingPlan;
import cec.jiutian.common.util.DateUtils;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import jakarta.annotation.Resource;
import jakarta.persistence.TypedQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 */
@Service
@Slf4j
public class StocktakingPlanService {
    @Resource
    private StocktakingOrderService stocktakingOrderService;
    @Resource
    private FabosJsonDao fabosJsonDao;

    /**
     * 定时任务  处理 生成任务
     */
    @Transactional
    public void generateStocktakingOrder() {
        log.info("定时任务：留样盘点计划生成盘点任务 开始执行-------------");
        List<KsStocktakingPlan> ksStocktakingPlans = findPlans();
        if(CollectionUtils.isNotEmpty(ksStocktakingPlans)){
            //生成任务
            ksStocktakingPlans.forEach(d->{
                log.info("定时任务：留样盘点计划生成盘点任务 -------------{}",d.getGeneralCode());
                stocktakingOrderService.generateStocktakingOrder(d);
                Date nextGenerateDate = StocktakingDateUtil.addTime(d.getNextGenerateDate(), StocktakingCycleEnum.Enum.valueOf(d.getStocktakingCycle()));
                if(nextGenerateDate.before(d.getStocktakingPlanEndDate())){
                    d.setNextGenerateDate(nextGenerateDate);
                }else {
                    d.setNextGenerateDate(null);
                }
                fabosJsonDao.mergeAndFlush(d);
            });
        }
    }

    /**
     * 查询需要生成任务的计划
     * @return
     */
    private List<KsStocktakingPlan> findPlans() {
        Date now = new Date();
        Date[] dateRange = DateUtils.getStartAndEndOfDay(now);

        // 创建 HQL 查询
        String hql = "SELECT l FROM KsStocktakingPlan l " +
                "WHERE l.stocktakingPlanEndDate > :currentDate " +
                "AND l.stocktakingPlanState = 'EFFECT'"+
                "AND l.stocktakingPlanStartDate < :currentDate "+
                "AND l.nextGenerateDate BETWEEN :startOfDay AND :endOfDay";
        // 执行查询
        TypedQuery<KsStocktakingPlan> query = fabosJsonDao.getEntityManager().createQuery(hql, KsStocktakingPlan.class);
        query.setParameter("currentDate", now);
        query.setParameter("startOfDay", dateRange[0]);
        query.setParameter("endOfDay", dateRange[1]);
        return query.getResultList();
    }

}
