package cec.jiutian.bc.person.enumeration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 考核方式枚举类
 * <AUTHOR>
 * @date 2025/4/18 14:37
 */
public class ExamineMethodEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum value : Enum.values()) {
            list.add(new VLModel(value.name(), value.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        EXAMINE("考试"),
        PRACTICAL_OPERATION("实操"),
        CERTIFICATE("证件"),
        SUPERIOR_RATING("上级评分");

        private final String value;

    }
}

