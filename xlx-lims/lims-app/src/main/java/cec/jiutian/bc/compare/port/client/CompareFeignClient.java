package cec.jiutian.bc.compare.port.client;

import cec.jiutian.api.remoteCallLog.dto.RemoteCallResult;
import cec.jiutian.core.frame.constant.FabosJsonRestPath;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

@Component
@FeignClient(name = "xlx-wms")
public interface CompareFeignClient {
    @PostMapping("/fabos-qms-app"+ FabosJsonRestPath.FABOS_REMOTE_API + "/getSamplingTask")
    RemoteCallResult<Map<String,Object>> tempName(@RequestBody Map<String, Object> params);
}
