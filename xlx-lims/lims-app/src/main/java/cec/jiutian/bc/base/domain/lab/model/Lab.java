package cec.jiutian.bc.base.domain.lab.model;

import cec.jiutian.bc.base.domain.lab.handler.CustomizeChoiceFetchHandler;
import cec.jiutian.bc.base.domain.lab.handler.LabEquipmentAddHandler;
import cec.jiutian.bc.base.domain.lab.handler.LabReferenceAddHandler;
import cec.jiutian.bc.base.domain.lab.mto.LmFactoryAreaMTO;
import cec.jiutian.bc.base.domain.lab.proxy.LabDataProxy;
import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.FabosJsonI18n;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.ReferenceAddType;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DependFieldDisplay;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @description:
 */
@Entity
@Table(name = "lm_lab",uniqueConstraints = {
        @UniqueConstraint(columnNames = {"labCode"}),
        @UniqueConstraint(columnNames = {"labName"})
})
@Getter
@Setter
@FabosJson(name = "检测室管理",
        orderBy = "Lab.createTime desc",
        power = @Power(export = false, importable = false, print = false, viewDetails = false),
        dataProxy = LabDataProxy.class
)
@FabosJsonI18n
public class Lab extends BaseModel {

    //编码
    @FabosJsonField(
            views = @View(title = "编号"),
            edit = @Edit(title = "编号",notNull = true,search = @Search(vague = true))
    )
    private String labCode;
    //名称
    @FabosJsonField(
            views = @View(title = "名称"),
            edit = @Edit(title = "名称",notNull = true,search = @Search(vague = true))
    )
    private String labName;
    //地址
    @FabosJsonField(
            views = @View(title = "地址"),
            edit = @Edit(title = "地址")
    )
    private String labAddress;
    //属性
    @FabosJsonField(
            views = @View(title = "属性"),
            edit = @Edit(title = "属性")
    )
    private String labAttribute;

    // 类型：包括检测中心、过程检测室、其他用户自定义的类型
    @FabosJsonField(
            views = @View(title = "类型"),
            edit = @Edit(title = "类型", notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = CustomizeChoiceFetchHandler.class,
                            fetchHandlerParams = "LM_LAB_TYPE"),
                    search = @Search(vague = true))
    )
    private String type;

    // 生产区域: factoryAreaTypeCode 01 工厂; 02 车间; 03 产线; 04 工段; 05 班组
    @Transient
    @FabosJsonField(
            views = @View(title = "生产区域", show = false, column = "factoryAreaCode"),
            edit = @Edit(title = "生产区域",
                    // type必须为过程检测室（02）时，才选择生产区域数据
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "type == 'LM_PROCESS_LAB'"),
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "factoryAreaCode"),
                    filter = @Filter(value = "factoryAreaTypeCode = '02'")
            )
    )
    private LmFactoryAreaMTO lmFactoryAreaMTO;

    @FabosJsonField(
            views = @View(title = "区域ID", show = false),
            edit = @Edit(title = "区域ID", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "lmFactoryAreaMTO", beFilledBy = "id"))
    )
    private String factoryAreaId;

    // 车间名称
    @FabosJsonField(
            views = @View(title = "生产区域名称"),
            edit = @Edit(title = "生产区域名称",
                    // type必须为过程检测室（02）时，才选择生产区域数据
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "type == 'LM_PROCESS_LAB'"),
                    search = @Search(vague = true), readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "lmFactoryAreaMTO", beFilledBy = "factoryAreaName"))
    )
    private String factoryAreaName;


    // 变更04.17 类型 修改为 所属检测基地
    @FabosJsonField(
            views = @View(title = "所属检测基地", column = "expLabName"),
            edit = @Edit(title = "所属检测基地",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "expLabName")
            )
    )
    @ManyToOne
    private ExpLab expLab;

    //实验室介绍
    @FabosJsonField(
            views = @View(title = "介绍"),
            edit = @Edit(title = "介绍")
    )
    private String labIntroduce;
    @FabosJsonField(
            edit = @Edit(title = "人员明细", type = EditType.TAB_REFER_ADD,readonly = @Readonly(add = false)),
            referenceAddType = @ReferenceAddType(referenceClass = "PersonManage",
                    referenceAddHandler = LabReferenceAddHandler.class),
            views = @View(title = "人员明细", type= ViewType.TABLE_VIEW,extraPK = "personId")
    )
    @JoinColumn(name = "lab_id")
    @OneToMany(cascade = CascadeType.ALL)
    private List<LabDetail> labDetails;

    // TODO 此处重复选择有报错, 后续需要解决
    @FabosJsonField(
            edit = @Edit(title = "实验室设备", type = EditType.TAB_REFER_ADD, readonly = @Readonly(add = false)),
            referenceAddType = @ReferenceAddType(referenceClass = "EquipmentArcMTO",
                    referenceAddHandler = LabEquipmentAddHandler.class),
            views = @View(title = "实验室设备", column = "name", type= ViewType.TABLE_VIEW, extraPK = "equipmentArchiveId")
    )
    @JoinColumn(name = "lab_id")
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    private List<LabEquipment> labEquipments;

}
