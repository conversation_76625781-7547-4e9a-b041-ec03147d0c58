package cec.jiutian.module;

import cec.jiutian.component.manage.domain.fabModule.service.FabModuleService;
import cec.jiutian.core.frame.module.FabModule;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Service
@Order(Integer.MAX_VALUE - 1)
public class ModuleInit implements ApplicationRunner {
    private static final String version = "3.2.0";
    private static final String activated = "activated";

    private static List<FabModule> modules = new ArrayList<>();

    static {
        FabModule lims = creatModule("lims-app");
        FabModule urm = creatModule("fabos-cmp-urm");
        FabModule gm = creatModule("fabos-cmp-gm");
        FabModule job = creatModule("fabos-cmp-job");
        modules.add(gm);
        modules.add(job);
        modules.add(urm);
        modules.add(lims);
    }

    public static FabModule creatModule(String moduleName) {
        FabModule fabModule = new FabModule();
        fabModule.setName(moduleName);
        fabModule.setUrl("/" + moduleName);
        fabModule.setVersion(version);
        fabModule.setState(activated);
        fabModule.setCreateTime(LocalDateTime.now());
        return fabModule;
    }

    private final FabModuleService fabModuleService;

    public ModuleInit(FabModuleService fabModuleService) {
        this.fabModuleService = fabModuleService;
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        for (FabModule module : modules) {
            fabModuleService.createOrUpdateBiz(module.getName(), module.getVersion(),
                    module.getState(), module.getUrl());
        }
    }
}
