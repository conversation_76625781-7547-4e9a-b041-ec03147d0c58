package cec.jiutian.config;

import com.fasterxml.jackson.datatype.hibernate6.Hibernate6Module;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Hibernate 懒加载失效 因为json序列化
 */
@Configuration
public class HibernateJsonConfig {

    /**
     * 注册一个额外的Jackson模块
     *
     * @return Module
     */
    @Bean
    public Hibernate6Module hibernate5Module() {
        Hibernate6Module module = new Hibernate6Module();
        //禁用(表示要忽略@Transient字段属性,默认为true,设置为false禁用)
        module.disable(Hibernate6Module.Feature.USE_TRANSIENT_ANNOTATION);
        //延时加载的对象不使用时设置为null
        module.enable(Hibernate6Module.Feature.SERIALIZE_IDENTIFIER_FOR_LAZY_NOT_LOADED_OBJECTS);
        return module;
    }

}