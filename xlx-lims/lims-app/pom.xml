<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cec.jiutian</groupId>
        <artifactId>xlx-lims</artifactId>
        <version>3.2.0</version>
    </parent>

    <artifactId>lims-app</artifactId>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <dependencies>
        <dependency>
            <groupId>cec.jiutian</groupId>
            <artifactId>fabos-component-manage</artifactId>
            <version>${fabos.framework.version}</version>
        </dependency>
        <dependency>
            <groupId>cec.jiutian</groupId>
            <artifactId>fabos-cmp-urm-biz</artifactId>
            <version>${fabos.framework.version}</version>
        </dependency>
        <!-- 基础POI依赖 -->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>5.2.3</version>
        </dependency>

        <!-- OOXML支持 -->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>5.2.3</version>
        </dependency>

        <!-- 其他Spring Boot基础依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>cec.jiutian</groupId>
            <artifactId>fabos-ie</artifactId>
            <version>${fabos.framework.version}</version>
        </dependency>

        <dependency>
            <groupId>com.101tec</groupId>
            <artifactId>zkclient</artifactId>
            <version>${zkclient.version}</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty</artifactId>
                </exclusion>
                <!--这里排除zookeeper的原因是版本过低，不兼容JDK17，所以在下方重新引入新版本-->
                <exclusion>
                    <groupId>zookeeper</groupId>
                    <artifactId>org.apache.zookeeper</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.zookeeper</groupId>
            <artifactId>zookeeper</artifactId>
            <version>${zookeeper.version}</version>
        </dependency>
        <dependency>
            <groupId>cec.jiutian</groupId>
            <artifactId>fabos-cmp-job-biz-api</artifactId>
            <version>3.2.2-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>cec.jiutian</groupId>
            <artifactId>general-modeler-biz</artifactId>
            <version>3.2.2-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>cec.jiutian</groupId>
            <artifactId>fabos-cmp-workflow-biz</artifactId>
            <version>3.2.2-SNAPSHOT</version>
        </dependency>
        <!--QMS系统依赖-->
        <dependency>
            <groupId>cec.jiutian</groupId>
            <artifactId>qms-api</artifactId>
            <version>3.2.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>cec.jiutian</groupId>
            <artifactId>fabos-cmp-job-biz</artifactId>
            <version>3.2.2-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>cec.jiutian</groupId>
            <artifactId>eam-biz</artifactId>
            <version>3.2.0</version>
        </dependency>
        <dependency>
            <groupId>cec.jiutian</groupId>
            <artifactId>fabos-cmp-ecs-biz-api</artifactId>
            <version>${fabos.framework.version}</version>
        </dependency>
        <dependency>
            <groupId>com.googlecode.juniversalchardet</groupId>
            <artifactId>juniversalchardet</artifactId>
            <version>1.0.3</version>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>repackage</id>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                        <configuration>
                            <!--	fix https://github.com/koupleless/koupleless/issues/161		-->
                            <loaderImplementation>CLASSIC</loaderImplementation>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <!--            为了让三方依赖和 koupleless 模式适配，需要引入以下构建插件-->
            <plugin>
                <groupId>com.alipay.sofa.koupleless</groupId>
                <artifactId>koupleless-base-build-plugin</artifactId>
                <version>2.1.1</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>add-patch</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>