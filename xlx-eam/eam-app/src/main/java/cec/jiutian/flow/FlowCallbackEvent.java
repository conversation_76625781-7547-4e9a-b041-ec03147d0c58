package cec.jiutian.flow;

import cec.jiutian.bc.flow.event.FabosWorkflowEvent;
import cec.jiutian.bc.flow.event.FabosWorkflowEventListener;
import cec.jiutian.bc.flow.event.FlowStatusUpdate;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
public class FlowCallbackEvent implements FabosWorkflowEventListener {

    @Resource
    private FlowStatusUpdate flowStatusUpdate;

    @Override
    public void onEvent(FabosWorkflowEvent event) {
        flowStatusUpdate.onEvent(event);
    }
}
