package cec.jiutian.bc.equipmentMaintenance.domain.maintenanceExecute.handler;

import cec.jiutian.bc.equipmentMaintenance.domain.maintenanceExecute.model.MaintenanceExecute;
import cec.jiutian.bc.equipmentMaintenance.enums.MRStatusEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/7
 * @description TODO
 */
@Component
public class FinishOperationHandler implements OperationHandler<MaintenanceExecute, Void> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<MaintenanceExecute> data, Void modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            MaintenanceExecute maintenanceExecute = data.get(0);
            maintenanceExecute.setBusinessStatus(MRStatusEnum.Enum.repairComplete.name());
            maintenanceExecute.setRepairFinishTime(new Date());
            Duration duration = Duration.between(maintenanceExecute.getStartMaintenanceTime().toInstant(), maintenanceExecute.getRepairFinishTime().toInstant());
            double hours = duration.toMillis() / 3600000.0;
            maintenanceExecute.setRepairTime(Math.round(hours * 100) / 100.0);


            fabosJsonDao.mergeAndFlush(maintenanceExecute);
        }
        return "alert(操作成功)";
    }
}
