package cec.jiutian.bc.specialEquipmentManagement.domain.specialEquipmentVerificationTask.model;

import cec.jiutian.bc.equipmentArchive.domain.equipmentArchive.model.EquipmentArchive;
import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleBaseModel;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowBaseOperation;
import cec.jiutian.view.type.RowOperation;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;


@Entity
@Table(name = "special_equipment_verification_task",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        })
@Getter
@Setter
@FabosJson(
        name = "校验任务管理",
        orderBy = "createTime desc",
        power = @Power(add = false, edit = false),
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "currentState != 'EDIT'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
        },
        rowOperation = {
                @RowOperation(
                        title = "下发",
                        code = "SpecialEquipmentVerificationTask@PUBLISH",
                        mode = RowOperation.Mode.SINGLE,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.UPDATE,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = SpecialEquipmentVerificationTaskPublish.class,
                        ifExpr = "currentState != 'EDIT'", //禁用按钮
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "SpecialEquipmentVerificationTask@PUBLISH"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
        }
)
public class SpecialEquipmentVerificationTask extends NamingRuleBaseModel {

    @FabosJsonField(
            views = @View(title = "计划编号"),
            edit = @Edit(title = "计划编号", search = @Search(vague = true))
    )
    private String planCode;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "特种设备台账", column = "generalCode"),
            edit = @Edit(title = "特种设备台账",
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode")
            )
    )
    @JoinColumn(foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private EquipmentArchive equipmentArchive;

    @FabosJsonField(
            views = @View(title = "设备名称"),
            edit = @Edit(title = "设备名称",
                    search = @Search(vague = true)
            )
    )
    private String deviceName;

    @FabosJsonField(
            views = @View(title = "项目编号"),
            edit = @Edit(title = "项目编号")
    )
    private String itemCode;

    @FabosJsonField(
            views = @View(title = "项目名称"),
            edit = @Edit(title = "项目名称",
                    inputType = @InputType(length = 50)
            )
    )
    private String itemName;

    @FabosJsonField(
            views = @View(title = "描述"),
            edit = @Edit(title = "描述")
    )
    private String description;

    @FabosJsonField(
            views = @View(title = "注意事项"),
            edit = @Edit(title = "注意事项")
    )
    private String remark;

    @FabosJsonField(
            views = @View(title = "校验人ID", show = false),
            edit = @Edit(title = "校验人ID", show = false)
    )
    private String verifierId;

    @FabosJsonField(
            views = @View(title = "校验人"),
            edit = @Edit(title = "校验人", search = @Search(vague = true))
    )
    private String verifierName;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",
                    search = @Search(),
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = OrderCurrentStateEnum.class)
            )
    )
    private String currentState;

}
