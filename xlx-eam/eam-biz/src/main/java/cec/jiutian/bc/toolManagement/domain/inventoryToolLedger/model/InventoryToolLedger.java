package cec.jiutian.bc.toolManagement.domain.inventoryToolLedger.model;

import cec.jiutian.bc.toolManagement.domain.inventoryToolLedger.proxy.InventoryToolLedgerDataProxy;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "line_inventory_view")
@FabosJson(
        name = "在库工具台账",
        power = @Power(add = false, edit = false, delete = false, export = false, importable = false),
        dataProxy = InventoryToolLedgerDataProxy.class
)
public class InventoryToolLedger {
    @Id
    @FabosJsonField(
            edit = @Edit(title = "", show = false)
    )
    private String id;

    // 物料编码
    @FabosJsonField(
            views = @View(title = "物料编码"),
            edit = @Edit(title = "物料编码", search = @Search(vague = true))
    )
    private String materialCode;

    // 物料名称
    @FabosJsonField(
            views = @View(title = "物料名称"),
            edit = @Edit(title = "物料名称", search = @Search(vague = true))
    )
    private String materialName;


    // 本厂批次号
    @FabosJsonField(
            views = @View(title = "本厂批号"),
            edit = @Edit(title = "本厂批号", search = @Search(vague = true))
    )
    private String lotSerialId;

    @FabosJsonField(
            views = @View(title = "规格"),
            edit = @Edit(title = "规格")
    )
    private String materialSpecification;

    // 线边仓
    @FabosJsonField(
            views = @View(title = "线边仓"),
            edit = @Edit(title = "线边仓", search = @Search(vague = true))
    )
    private String warehouseName;

    // 可用数量
    @FabosJsonField(
            views = @View(title = "可用数量"),
            edit = @Edit(title = "可用数量")
    )
    private Double availableQuantity;

    // 单位
    @FabosJsonField(
            views = @View(title = "单位"),
            edit = @Edit(title = "单位")
    )
    private String unit;

    @FabosJsonField(
            views = @View(title = "物料类别", show = false),
            edit = @Edit(title = "物料类别", show = false)
    )
    private String materialType;

    @FabosJsonField(
            views = @View(title = "物料类别名称"),
            edit = @Edit(title = "物料类别名称")
    )
    private String materialTypeName;
}
