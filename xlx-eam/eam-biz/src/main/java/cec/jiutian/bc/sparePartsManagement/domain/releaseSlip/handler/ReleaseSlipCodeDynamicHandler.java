package cec.jiutian.bc.sparePartsManagement.domain.releaseSlip.handler;

import cec.jiutian.bc.enums.NamingRuleCodeEnum;
import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.sparePartsManagement.domain.releaseSlip.model.ReleaseSlip;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class ReleaseSlipCodeDynamicHandler implements DependFiled.DynamicHandler<ReleaseSlip> {

    @Resource
    private NamingRuleService namingRuleService;

    @Override
    public Map<String, Object> handle(ReleaseSlip releaseSlip) {
        Map<String, Object> map = new HashMap<>();
        map.put("generalCode", String.valueOf(namingRuleService.getNameCode(
                        NamingRuleCodeEnum.ReleaseSlip.name(), 1, null).get(0)));
        return map;
    }

}
