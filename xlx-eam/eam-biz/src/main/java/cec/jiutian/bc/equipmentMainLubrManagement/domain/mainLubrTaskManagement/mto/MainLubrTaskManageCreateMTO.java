package cec.jiutian.bc.equipmentMainLubrManagement.domain.mainLubrTaskManagement.mto;

import cec.jiutian.bc.equipmentArchive.domain.equipmentArchive.model.EquipmentArchive;
import cec.jiutian.bc.equipmentMainLubrManagement.domain.mainLubrTaskManagement.enumration.MltManagementBusinessStatusEnum;
import cec.jiutian.bc.equipmentMainLubrManagement.domain.mainLubrTaskManagement.enumration.MltManagementCreationMethodEnum;
import cec.jiutian.bc.equipmentMainLubrManagement.domain.mainLubrTaskManagement.handler.MainLubrTaskManageAddHandler;
import cec.jiutian.bc.equipmentMainLubrManagement.domain.mainLubrTaskManagement.handler.MainLubrTaskanagementDynamicHandler;
import cec.jiutian.bc.equipmentMainLubrManagement.domain.mainLubrTaskManagement.handler.MainLubrTaskManageDetailClearHandler;
import cec.jiutian.bc.equipmentMainLubrManagement.domain.mainLubrTaskManagement.model.MainLubrTaskManageDetail;
import cec.jiutian.bc.mto.OrgMTO;
import cec.jiutian.bc.mto.SpecificationManage;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.*;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Filter;
import jakarta.persistence.*;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/13
 * @description
 */
@FabosJson(
        name = "创建保养润滑任务"
)
@Table(name = "eam_elm_task_management",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Data
public class MainLubrTaskManageCreateMTO extends MetaModel {
    @FabosJsonField(
            views = @View(title = "任务编号"),
            edit = @Edit(title = "任务编号",
                    notNull = true,
                    readonly = @Readonly(edit = false)
            ),
            dynamicField = @DynamicField(dependFiled = @DependFiled(buttonName = "生成", changeBy = "id",
                    dynamicHandler = MainLubrTaskanagementDynamicHandler.class))
    )
    private String generalCode;

    @ManyToOne
    @JoinColumn(name = "org_mto_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @FabosJsonField(
            views = @View(title = "执行班组", column = "name"),
            edit = @Edit(title = "执行班组",
                    notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    filter = @Filter(value = "orgType = '05'"),
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType()
            )
    )
    private OrgMTO orgMTO;

    @FabosJsonField(
            views = @View(title = "执行班组id", show = false),
            edit = @Edit(title = "执行班组id", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "orgMTO", beFilledBy = "id"))
    )
    private String executionTeamId;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "设备", column = "generalCode"),
            edit = @Edit(title = "设备",
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode"),
                    notNull = true
            )
    )
    private EquipmentArchive equipment;

    @Transient
    @FabosJsonField(
            views = @View(title = "设备编码", show = false),
            edit = @Edit(title = "设备编码", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "equipment", beFilledBy = "code"))
    )
    private String code;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "设备润滑材料", column = "name"),
            edit = @Edit(title = "设备润滑材料",
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    notNull = true,
                    filter = @Filter(value = "SpecificationManage.specificationCode in ('0101', '0102', '0103', '0104', '0105', '0106', '0107', '0108', '0109', '0110', '0111', '0112')"))
    )
    private SpecificationManage equiLubrMaterial;

    @FabosJsonField(
            views = @View(title = "特质特性"),
            edit = @Edit(title = "特质特性",
                    notNull = true,
                    search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    private String characteristicTrait;

    @FabosJsonField(
            views = @View(title = "作业票"),
            edit = @Edit(title = "作业票",
                    type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(size = 5120, maxLimit = 10, type = AttachmentType.Type.BASE)
            )
    )
    private String attachment;

    @FabosJsonField(
            views = @View(title = "设备保养标准",
                    column = "maintenanceStandardName",
                    type = ViewType.TABLE_VIEW,
                    extraPK = "popId"),
            edit = @Edit(title = "设备保养标准",
                    type = EditType.TAB_REFER_ADD
            ),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "equipment", dynamicHandler = MainLubrTaskManageDetailClearHandler.class)),
            referenceAddType = @ReferenceAddType(referenceClass = "EquipmentMaintenanceStandard",
                    filter = "equipment.code='${code}'",
                    referenceAddHandler = MainLubrTaskManageAddHandler.class
            )
    )
    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumn(name = "eam_elm_task_management_id")
    private List<MainLubrTaskManageDetail> mainLubrTaskManageDetailList;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",
                    readonly = @Readonly(),
                    show = false,
                    search = @Search,
                    type = EditType.CHOICE,
                    defaultVal = "TO_BE_PUBLISHED",
                    choiceType = @ChoiceType(fetchHandler = MltManagementBusinessStatusEnum.class)
            )
    )
    private String businessStatus;

    @FabosJsonField(
            views = @View(title = "创建类型"),
            edit = @Edit(title = "创建类型",
                    show = false,
                    readonly = @Readonly(),
                    search = @Search,
                    type = EditType.CHOICE,
                    defaultVal = "HAND",
                    choiceType = @ChoiceType(fetchHandler = MltManagementCreationMethodEnum.class)
            )
    )
    private String creationMethod;
}
