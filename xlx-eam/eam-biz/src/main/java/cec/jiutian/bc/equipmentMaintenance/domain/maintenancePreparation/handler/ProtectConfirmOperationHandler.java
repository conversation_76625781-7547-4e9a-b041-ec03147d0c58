package cec.jiutian.bc.equipmentMaintenance.domain.maintenancePreparation.handler;

import cec.jiutian.bc.equipmentMaintenance.domain.maintenancePreparation.model.MaintenancePreparation;
import cec.jiutian.bc.equipmentMaintenance.domain.maintenancePreparation.mto.MaintenancePreparationProtectConfirmMTO;
import cec.jiutian.bc.equipmentMaintenance.enums.CheckResultEnum;
import cec.jiutian.bc.equipmentMaintenance.enums.MRStatusEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/8
 * @description TODO
 */
@Component
public class ProtectConfirmOperationHandler implements OperationHandler<MaintenancePreparation, MaintenancePreparationProtectConfirmMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<MaintenancePreparation> data, MaintenancePreparationProtectConfirmMTO modelObject, String[] param) {
        MaintenancePreparation maintenancePreparation = data.get(0);
        if (modelObject != null && CheckResultEnum.Enum.Pass.name().equals(modelObject.getCheckResult())) {
            maintenancePreparation.setBusinessStatus(MRStatusEnum.Enum.waitRepair.name());
            fabosJsonDao.mergeAndFlush(maintenancePreparation);
        }
        return "alert(操作成功)";
    }

    @Override
    public MaintenancePreparationProtectConfirmMTO fabosJsonFormValue(List<MaintenancePreparation> data, MaintenancePreparationProtectConfirmMTO fabosJsonForm, String[] param) {
        BeanUtil.copyProperties(data.get(0), fabosJsonForm);
        return fabosJsonForm;
    }
}
