package cec.jiutian.bc.instrumentManagement.domain.calibrationStandard.model;

import cec.jiutian.bc.instrumentManagement.domain.calibrationStandard.handler.CalibrationItemCodeGenerateDynamicHandler;
import cec.jiutian.bc.instrumentManagement.domain.calibrationStandard.proxy.CalibrationItemProxy;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025/4/17
 * @description TODO
 */
@FabosJson(
        name = "仪器仪表校验项",
        orderBy = "CalibrationItem.createTime desc",
        dataProxy = CalibrationItemProxy.class
)
@Table(name = "eam_im_calibration_item",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Getter
@Setter
public class CalibrationItem extends MetaModel {

    @FabosJsonField(
            views = @View(title = "校验项编号"),
            edit = @Edit(title = "校验项编号", notNull = true),
            dynamicField = @DynamicField(dependFiled = @DependFiled(buttonName = "生成", changeBy = "id",
                    dynamicHandler = CalibrationItemCodeGenerateDynamicHandler.class))
    )
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "校验项名称"),
            edit = @Edit(title = "校验项名称",notNull = true)
    )
    private String name;

    @FabosJsonField(
            views = @View(title = "内容",type = ViewType.text),
            edit = @Edit(title = "内容",notNull = true)
    )
    private String content;

    @FabosJsonField(
            views = @View(title = "注意事项"),
            edit = @Edit(title = "注意事项",type = EditType.TEXTAREA)
    )
    private String note;
}
