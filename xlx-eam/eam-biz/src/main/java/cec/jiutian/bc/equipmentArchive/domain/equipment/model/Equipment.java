package cec.jiutian.bc.equipmentArchive.domain.equipment.model;

import cec.jiutian.bc.enums.EquipmentTypeEnum;
import cec.jiutian.bc.equipmentArchive.domain.equipment.handler.EquipmentCategoryOprDynamicHandler;
import cec.jiutian.bc.equipmentArchive.domain.equipment.proxy.EquipmentProxy;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.*;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DependFieldDisplay;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.LinkTree;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025/4/1
 * @description TODO
 */
@FabosJson(
        name = "设备编码",
        orderBy = "Equipment.createTime desc",
        linkTree = @LinkTree(field = "equipmentType"),
        dataProxy = EquipmentProxy.class
)
@Table(name = "eam_ea_equipment_info",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Getter
@Setter
@TemplateType(type = "treeTable")
public class Equipment extends MetaModel {

        @FabosJsonField(
                views = @View(title = "设备编码"),
                edit = @Edit(title = "设备编码",
                        notNull = true,
                        search = @Search(vague = true)
                )
        )
        private String code;

        @FabosJsonField(
                views = @View(title = "设备名称"),
                edit = @Edit(title = "设备名称",
                        notNull = true,
                        search = @Search(vague = true)
                )
        )
        private String name;

        @FabosJsonField(
                views = @View(title = "设备简称"),
                edit = @Edit(title = "设备简称",
                        notNull = true,
                        search = @Search(vague = true)
                )
        )
        private String abbreviation;

        @FabosJsonField(
                views = @View(title = "规格型号"),
                edit = @Edit(title = "规格型号",
                        notNull = true,
                        search = @Search(vague = true)
                )
        )
        private String specification;

        @FabosJsonField(
                views = @View(title = "是否特种设备"),
                edit = @Edit(title = "是否特种设备", notNull = true, type = EditType.BOOLEAN, defaultVal = "false")
        )
        private Boolean isSpecialEquipment;

        @FabosJsonField(
                views = @View(title = "有效周期"),
                edit = @Edit(title = "有效周期",inputGroup = @InputGroup(postfix = "天"),numberType = @NumberType(min = 0),
                dependFieldDisplay = @DependFieldDisplay(notNull = "isSpecialEquipment == true"))
        )
        private Integer validityPeriod;

        @ManyToOne
        @FabosJsonField(
                views = @View(title = "设备类型名称", column = "name"),
                edit = @Edit(title = "设备类型名称", notNull = true,
                        type = EditType.REFERENCE_TREE,
                        referenceTreeType = @ReferenceTreeType(pid = "parent.id")
                )
        )
        private EquipmentType equipmentType;

        @FabosJsonField(
                views = @View(title = "设备大类"),
                edit = @Edit(title = "设备大类",
                        type = EditType.CHOICE,
                        choiceType = @ChoiceType(fetchHandler = EquipmentTypeEnum.class),
                        readonly = @Readonly),
                dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "equipmentType", dynamicHandler = EquipmentCategoryOprDynamicHandler.class))
        )
        private String type;
}
