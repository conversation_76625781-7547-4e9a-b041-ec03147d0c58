package cec.jiutian.bc.specialEquipmentManagement.domain.specialEquipmentMaintenance.model;

import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleModel;
import cec.jiutian.bc.specialEquipmentManagement.domain.specialEquipmentMaintenance.proxy.MySEMaintenanceTaskExecuteDataProxy;
import cec.jiutian.bc.specialEquipmentManagement.enums.SEMaintenanceStateEnum;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.AttachmentType;
import cec.jiutian.view.field.edit.ChoiceType;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;


@Entity
@Table(name = "special_equipment_maintenance_task",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        })
@Getter
@Setter
@FabosJson(
        name = "我的维修任务-执行",
        dataProxy = MySEMaintenanceTaskExecuteDataProxy.class
)
public class MySpecialEquipmentMaintenanceTaskExecute extends NamingRuleModel {

    @FabosJsonField(
            views = @View(title = "故障描述"),
            edit = @Edit(title = "故障描述", notNull = true)
    )
    private String faultDescription;

    @FabosJsonField(
            views = @View(title = "故障原因"),
            edit = @Edit(title = "故障原因", notNull = true)
    )
    private String faultReason;

    @FabosJsonField(
            views = @View(title = "采取措施"),
            edit = @Edit(title = "采取措施", notNull = true)
    )
    private String measure;

    @FabosJsonField(
            views = @View(title = "附件"),
            edit = @Edit(title = "附件", type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(maxLimit = 10))
    )
    private String attachment;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",
                    show = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = SEMaintenanceStateEnum.class)
            )
    )
    private String currentState;

}
