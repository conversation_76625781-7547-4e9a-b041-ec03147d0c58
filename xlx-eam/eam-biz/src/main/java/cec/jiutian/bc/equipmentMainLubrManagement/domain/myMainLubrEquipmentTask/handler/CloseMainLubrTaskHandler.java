package cec.jiutian.bc.equipmentMainLubrManagement.domain.myMainLubrEquipmentTask.handler;

import cec.jiutian.bc.equipmentArchive.domain.equipmentArchive.repository.EquipmentArchiveRepository;
import cec.jiutian.bc.equipmentMainLubrManagement.domain.mainLubrTaskManagement.enumration.MltManagementBusinessStatusEnum;
import cec.jiutian.bc.equipmentMainLubrManagement.domain.myMainLubrEquipmentTask.mto.CloseMainLubrTaskMTO;
import cec.jiutian.bc.equipmentMainLubrManagement.domain.myMainLubrEquipmentTask.model.MyMainLubrEquipmentTask;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/6
 * @description
 */
@Slf4j
@Component
public class CloseMainLubrTaskHandler implements OperationHandler<MyMainLubrEquipmentTask, CloseMainLubrTaskMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private EquipmentArchiveRepository equipmentArchiveRepository;

    @Override
    @Transactional
    public String exec(List<MyMainLubrEquipmentTask> data, CloseMainLubrTaskMTO modelObject, String[] param) {
        if (modelObject == null) {
            throw new ServiceException("数据异常");
        }
        MyMainLubrEquipmentTask model = data.get(0);
        BeanUtil.copyProperties(modelObject, model, "createBy", "createTime");
        model.setBusinessStatus(MltManagementBusinessStatusEnum.Enum.CLOSE.name());
        fabosJsonDao.mergeAndFlush(model);
        return "msg.success('操作成功')";
    }
}
