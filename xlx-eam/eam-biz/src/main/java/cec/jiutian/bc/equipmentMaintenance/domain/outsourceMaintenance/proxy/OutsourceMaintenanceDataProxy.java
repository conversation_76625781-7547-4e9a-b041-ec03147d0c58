package cec.jiutian.bc.equipmentMaintenance.domain.outsourceMaintenance.proxy;

import cec.jiutian.bc.equipmentMaintenance.domain.outsourceMaintenance.model.OutsourceMaintenance;
import cec.jiutian.bc.equipmentMaintenance.enums.MRStatusEnum;
import cec.jiutian.bc.equipmentMaintenance.service.EquipmentMaintenanceService;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
public class OutsourceMaintenanceDataProxy implements DataProxy<OutsourceMaintenance> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private EquipmentMaintenanceService equipmentMaintenanceService;

    @Override
    public void beforeAdd(OutsourceMaintenance entity) {
        entity.setCurrentState(OrderCurrentStateEnum.Enum.EDIT.name());
    }

    @Override
    public void afterAdd(OutsourceMaintenance entity) {
        // 关闭维修申请单
        equipmentMaintenanceService.updateMaintenanceRequestStatus(entity.getMaintenanceRequest().getId(), MRStatusEnum.Enum.repairComplete.name());
    }

    @Override
    public void beforeUpdate(OutsourceMaintenance entity) {
        if (!entity.getCurrentState().equals(OrderCurrentStateEnum.Enum.EDIT.name())) {
            throw new FabosJsonApiErrorTip("开立状态允许编辑");
        }
    }

    @Override
    public void beforeDelete(OutsourceMaintenance entity) {
        if (!entity.getCurrentState().equals(OrderCurrentStateEnum.Enum.EDIT.name())) {
            throw new FabosJsonApiErrorTip("开立状态允许删除");
        }
    }

    @Override
    public void afterDelete(OutsourceMaintenance entity) {
        // 还原需求单状态
        equipmentMaintenanceService.updateMaintenanceRequestStatus(entity.getMaintenanceRequest().getId(), entity.getRequestStatus());
    }

}
