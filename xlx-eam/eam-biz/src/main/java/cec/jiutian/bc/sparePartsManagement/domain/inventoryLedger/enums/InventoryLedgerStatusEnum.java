package cec.jiutian.bc.sparePartsManagement.domain.inventoryLedger.enums;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

public class InventoryLedgerStatusEnum implements ChoiceFetchHandler {

    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum data : Enum.values()) {
            list.add(new VLModel(data.name(), data.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        //线边领料申请、线边领用出库
        normal("正常"),
        exception("异常"),
        checking("校验中"),
        //借出、归还、失效
        out("借出"),
        invalid("失效");
        ;
        private final String value;
    }
}
