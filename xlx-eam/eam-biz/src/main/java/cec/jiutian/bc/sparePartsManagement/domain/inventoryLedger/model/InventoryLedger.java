package cec.jiutian.bc.sparePartsManagement.domain.inventoryLedger.model;

import cec.jiutian.bc.enums.InventoryMaterialTypeEnum;
import cec.jiutian.bc.mto.ShelfMTO;
import cec.jiutian.bc.mto.WarehouseBlockMTO;
import cec.jiutian.bc.mto.WarehouseMTO;
import cec.jiutian.bc.sparePartsManagement.domain.inventoryLedger.enums.InventoryLedgerStatusEnum;
import cec.jiutian.bc.sparePartsManagement.domain.inventoryLedger.handler.InvBlockCleanDynamicHandler;
import cec.jiutian.bc.sparePartsManagement.domain.inventoryLedger.handler.InvShelfCleanDynamicHandler;
import cec.jiutian.bc.sparePartsManagement.domain.inventoryLedger.proxy.InventoryLedgerDataProxy;
import cec.jiutian.bc.sparePartsManagement.domain.receiveStockIn.mto.MaterialDetailConfirmMTO;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Getter;
import lombok.Setter;


@Entity
@Table(name = "eam_inventory_ledger")
@Getter
@Setter
@FabosJson(
        name = "线边仓库存台账",
        orderBy = "createTime desc",
        filter = @Filter("materialType = 'spareParts'"),
        power = @Power(
                importable = false, export = true, add = false
        ),
        dataProxy = InventoryLedgerDataProxy.class,
        rowBaseOperation = {

        },
        rowOperation = {

        }
)
public class InventoryLedger extends MetaModel {

    @FabosJsonField(
            views = @View(title = "本厂批号"),
            edit = @Edit(title = "本厂批号",
                    notNull = true,
                    search = @Search(vague = true))
    )
    private String lotSerialId;

    @FabosJsonField(
            views = @View(title = "库存批次"),
            edit = @Edit(title = "库存批次",
                    search = @Search(vague = true),
                    inputType = @InputType(length = 20))
    )
    private String inventoryLotId;

    @FabosJsonField(
            views = @View(title = "原炉/原厂批号"),
            edit = @Edit(title = "原炉/原厂批号",
                    search = @Search(vague = true)
            )
    )
    private String supplierLotSerialId;

    @FabosJsonField(
            views = @View(title = "供应商id", show = false),
            edit = @Edit(title = "供应商id", show = false)
    )
    private String supplierId;

    @FabosJsonField(
            views = @View(title = "供应商"),
            edit = @Edit(title = "供应商",
                    search = @Search(vague = true)
            )
    )
    private String supplierName;

    @FabosJsonField(
            views = @View(title = "物料编号"),
            edit = @Edit(title = "物料编号",
                    search = @Search(vague = true)
            )
    )
    private String materialCode;

    @FabosJsonField(
            views = @View(title = "物料名称"),
            edit = @Edit(title = "物料名称",
                    search = @Search(vague = true)
            )
    )
    private String materialName;

    @FabosJsonField(
            views = @View(title = "物料类别"),
            edit = @Edit(title = "物料类别",
                    readonly = @Readonly,
                    defaultVal = "spareParts",
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(
                            fetchHandler = InventoryMaterialTypeEnum.class
                    )
            )
    )
    private String materialType;

    @FabosJsonField(
            views = @View(title = "规格"),
            edit = @Edit(title = "规格")
    )
    private String materialSpecification;

    @FabosJsonField(
            views = @View(title = "单位"),
            edit = @Edit(title = "单位")
    )
    private String accountingUnit;

    @FabosJsonField(
            views = @View(title = "批次总数量"),
            edit = @Edit(title = "批次总数量")
    )
    private Double accountingUnitQuantity;

    @FabosJsonField(
            views = @View(title = "可用数量"),
            edit = @Edit(title = "可用数量",search = @Search(vague = true))
    )
    private Double availableQuantity;

    @FabosJsonField(
            views = @View(title = "锁定数量"),
            edit = @Edit(title = "锁定数量",readonly = @Readonly)
    )
    private Double lockQuantity;

    @Transient
    @FabosJsonField(
            views = @View(title = "选择仓库", column = "warehouseName", show = false),
            edit = @Edit(title = "选择仓库",
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "warehouseName")
            )
    )
    private WarehouseMTO warehouseMTO;

    @FabosJsonField(
            views = @View(title = "仓库名称"),
            edit = @Edit(title = "仓库名称",
                    notNull = true,
                    readonly = @Readonly,
                    inputType = @InputType(length = 20)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "warehouseMTO",
                    beFilledBy = "warehouseName"))
    )
    private String warehouseName;

    @FabosJsonField(
            views = @View(title = "仓库ID", show = false),
            edit = @Edit(title = "仓库ID", show = false
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "warehouseMTO",
                    beFilledBy = "id"))
    )
    private Long warehouseId;

    @Transient
    @FabosJsonField(
            views = @View(title = "选择库区", column = "blockName", show = false),
            edit = @Edit(title = "选择库区",
                    type = EditType.REFERENCE_TABLE,
                    queryCondition = "{\"warehouseId\":\"${warehouseId}\"}",
                    referenceTableType = @ReferenceTableType(id = "id", label = "blockName"),
                    dependFieldDisplay = @DependFieldDisplay(enableOrDisable = "warehouseName == null")
            ),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "warehouseMTO",
                    dynamicHandler = InvBlockCleanDynamicHandler.class))
    )
    private WarehouseBlockMTO warehouseBlockMTO;


    @FabosJsonField(
            views = @View(title = "库区名称"),
            edit = @Edit(title = "库区名称",
                    readonly = @Readonly,
                    inputType = @InputType(length = 20)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "warehouseBlockMTO",
                    beFilledBy = "blockName"), passive = true)
    )
    private String blockName;

    @FabosJsonField(
            views = @View(title = "库区ID", show = false),
            edit = @Edit(title = "库区ID", show = false,
                    inputType = @InputType(type = "number"),
                    numberType = @NumberType(precision = 0)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "warehouseBlockMTO",
                    beFilledBy = "id"))
    )
    private Long blockId;

    @Transient
    @FabosJsonField(
            views = @View(title = "选择货位", column = "locationName", show = false),
            edit = @Edit(title = "选择货位",
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "locationName"),
                    queryCondition = "{\"blockId\":\"${blockId}\"}",
                    dependFieldDisplay = @DependFieldDisplay(enableOrDisable = "blockName == null")
            ),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "warehouseBlockMTO",
                    dynamicHandler = InvShelfCleanDynamicHandler.class))
    )
    private ShelfMTO shelf;

    @FabosJsonField(
            views = @View(title = "货位名称"),
            edit = @Edit(title = "货位名称",
                    readonly = @Readonly,
                    inputType = @InputType(length = 20)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "shelf",
                    beFilledBy = "locationName"), passive = true)
    )
    private String shelfName;

    @FabosJsonField(
            views = @View(title = "货位ID", show = false),
            edit = @Edit(title = "货位ID", show = false,
                    inputType = @InputType(type = "number")
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "shelf",
                    beFilledBy = "id"))
    )
    private Long shelfId;


    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",
                    defaultVal = "normal",
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = InventoryLedgerStatusEnum.class)
            )
    )
    private String businessState;

    @FabosJsonField(
            views = @View(title = "领用部门"),
            edit = @Edit(title = "领用部门",
                    show = false
            )
    )
    private String borrowDep;

    public static InventoryLedger createByMaterial(MaterialDetailConfirmMTO materialDetail) {
        InventoryLedger inventoryLedger = new InventoryLedger();
        inventoryLedger.setInventoryLotId(materialDetail.getInventoryLotId());
        inventoryLedger.setLotSerialId(materialDetail.getLotSerialId());
        inventoryLedger.setSupplierLotSerialId(materialDetail.getSupplierLotSerialId());
        inventoryLedger.setMaterialCode(materialDetail.getMaterialCode());
        inventoryLedger.setSupplierId(materialDetail.getSupplierId());
        inventoryLedger.setSupplierName(materialDetail.getSupplierName());
        inventoryLedger.setMaterialName(materialDetail.getMaterialName());
        inventoryLedger.setMaterialSpecification(materialDetail.getMaterialSpecification());
        inventoryLedger.setAccountingUnit(materialDetail.getAccountingUnit());
        inventoryLedger.setAccountingUnitQuantity(materialDetail.getApplyQuantity());
        inventoryLedger.setAvailableQuantity(materialDetail.getApplyQuantity());
        inventoryLedger.setWarehouseId(materialDetail.getWarehouseId());
        inventoryLedger.setWarehouseName(materialDetail.getWarehouseName());
        inventoryLedger.setBlockId(materialDetail.getBlockId());
        inventoryLedger.setBlockName(materialDetail.getBlockName());
        inventoryLedger.setShelfId(materialDetail.getShelfId());
        inventoryLedger.setShelfName(materialDetail.getShelfName());

        inventoryLedger.setBusinessState(InventoryLedgerStatusEnum.Enum.normal.name());
        inventoryLedger.setMaterialType(InventoryMaterialTypeEnum.Enum.spareParts.name());

        return inventoryLedger;
    }
}
