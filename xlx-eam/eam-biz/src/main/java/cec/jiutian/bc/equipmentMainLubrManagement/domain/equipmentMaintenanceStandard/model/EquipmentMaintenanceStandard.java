package cec.jiutian.bc.equipmentMainLubrManagement.domain.equipmentMaintenanceStandard.model;

import cec.jiutian.bc.equipmentArchive.domain.equipment.model.Equipment;
import cec.jiutian.bc.equipmentMainLubrManagement.domain.equipmentMaintenanceStandard.handler.EmStandardDynamicHandler;
import cec.jiutian.bc.equipmentMainLubrManagement.domain.equipmentMaintenanceStandard.proxy.EmStandardDataProxy;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.InputGroup;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025/06/05
 * @description
 */
@FabosJson(
        name = "设备保养标准",
        orderBy = "EquipmentMaintenanceStandard.createTime desc",
        dataProxy = EmStandardDataProxy.class
)
@Table(name = "eam_elm_equipment_maintenance_standard",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Getter
@Setter
public class EquipmentMaintenanceStandard extends MetaModel {

    @FabosJsonField(
            views = @View(title = "编码"),
            edit = @Edit(title = "编码",
                    readonly = @Readonly(add = false, edit = true),
                    search = @Search(vague = true), notNull = true),
            dynamicField = @DynamicField(dependFiled = @DependFiled(buttonName = "生成", changeBy = "id",
                    dynamicHandler = EmStandardDynamicHandler.class))
    )
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "保养标准名称"),
            edit = @Edit(title = "保养标准名称",
                    notNull = true,
                    search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    private String maintenanceStandardName;

    @FabosJsonField(
            views = @View(title = "保养计划工时(小时)"),
            edit = @Edit(title = "保养计划工时(小时)",
                    inputType = @InputType(length = 7),
                    numberType = @NumberType(min = 0, precision = 1),
                    inputGroup = @InputGroup(postfix = "小时")
            )
    )
    private Double maintenanceScheduleHour;

    @FabosJsonField(
            views = @View(title = "适用设备", column = "code"),
            edit = @Edit(title = "适用设备",
                    type = EditType.REFERENCE_TABLE,
                    filter = @Filter(value = "Equipment.equipmentType.type = 'Equipment'"),
                    notNull = true, search = @Search(vague = true),
                    referenceTableType = @ReferenceTableType(id = "id", label = "code")
                    , allowAddMultipleRows = false
            )
    )
    @ManyToOne
    @JoinColumn(name = "equipment_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private Equipment equipment;

    @FabosJsonField(
            views = @View(title = "保养部位"),
            edit = @Edit(title = "保养部位",
                    search = @Search(vague = true))
    )
    private String maintenancePart;

    @FabosJsonField(
            views = @View(title = "保养注意事项"),
            edit = @Edit(title = "保养注意事项",
                    search = @Search(vague = true))
    )
    private String maintenancePrecaution;

    @FabosJsonField(
            views = @View(title = "标准"),
            edit = @Edit(title = "标准",
                    search = @Search(vague = true))
    )
    private String standard;

    @FabosJsonField(
            views = @View(title = "描述"),
            edit = @Edit(title = "描述",
                    type = EditType.TEXTAREA)
    )
    private String description;
}
