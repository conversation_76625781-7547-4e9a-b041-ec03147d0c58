package cec.jiutian.bc.equipmentMainLubrManagement.domain.myMainLubrEquipmentTask.handler;

import cec.jiutian.bc.equipmentMainLubrManagement.domain.mainLubrTaskManagement.enumration.MltManagementBusinessStatusEnum;
import cec.jiutian.bc.equipmentMainLubrManagement.domain.myMainLubrEquipmentTask.model.MyMainLubrEquipmentTask;
import cec.jiutian.bc.equipmentMainLubrManagement.domain.myMainLubrEquipmentTask.model.MyMainLubrEquipmentTaskResult;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/7
 * @description
 */
@Component
public class MyMainLubrEquipmentTaskResultOprHandler implements OperationHandler<MyMainLubrEquipmentTask, MyMainLubrEquipmentTaskResult> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<MyMainLubrEquipmentTask> data, MyMainLubrEquipmentTaskResult modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            MyMainLubrEquipmentTask task = data.get(0);
            BeanUtil.copyProperties(modelObject, task, "createBy", "createTime");
            task.setLubricatingPerson(task.getExecutor());
            fabosJsonDao.mergeAndFlush(task);
        }
        return "alert(操作成功)";
    }

}
