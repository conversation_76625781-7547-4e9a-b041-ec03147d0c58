package cec.jiutian.bc.sparePartsManagement.domain.sparePartsBorrowingForm.handler;

import cec.jiutian.bc.enums.NamingRuleCodeEnum;
import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.sparePartsManagement.domain.sparePartsBorrowingForm.model.SparePartsBorrowingForm;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/*
 *
 * <AUTHOR>
 * @date 2025/04/16
 */
@Component
public class SpBorrowingFormCodeDynamicHandler implements DependFiled.DynamicHandler<SparePartsBorrowingForm> {

    @Resource
    private NamingRuleService namingRuleService;

    @Override
    public Map<String, Object> handle(SparePartsBorrowingForm materialRequisition) {
        Map<String, Object> map = new HashMap<>();
        map.put("generalCode", String.valueOf(namingRuleService.getNameCode(
                        NamingRuleCodeEnum.SPARE_PARTS_BORROWING_FORM.name(), 1, null).get(0)));
        return map;
    }

}
