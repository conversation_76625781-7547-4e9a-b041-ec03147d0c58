package cec.jiutian.bc.equipmentStatsReport.domain.alarmInformation.handler;

import cec.jiutian.bc.enums.NamingRuleCodeEnum;
import cec.jiutian.bc.equipmentStatsReport.domain.alarmInformation.model.AlarmInformation;
import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/06/12
 * @description
 */
@Component
public class AlarmInfoDynamicHandler implements DependFiled.DynamicHandler<AlarmInformation> {

    @Resource
    private NamingRuleService namingRuleService;

    @Override
    public Map<String, Object> handle(AlarmInformation inspectionInstrument) {
        Map<String, Object> map = new HashMap<>();
        List<String> result = namingRuleService.getNameCode(NamingRuleCodeEnum.AlarmInformation.name(), 1, null);
        map.put("generalCode",result.get(0));
        return map;
    }
}
