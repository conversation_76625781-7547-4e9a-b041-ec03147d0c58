package cec.jiutian.bc.toolManagement.domain.sharedToolLedger.model;

import cec.jiutian.bc.mto.UserMTO;
import cec.jiutian.bc.toolManagement.domain.personalToolLedger.handler.PersonalToolLedgerBorrowingHandler;
import cec.jiutian.bc.toolManagement.domain.personalToolLedger.handler.PersonalToolLedgerReturnHandler;
import cec.jiutian.bc.toolManagement.domain.personalToolLedger.model.PersonalToolLedgerBorrowing;
import cec.jiutian.bc.toolManagement.domain.personalToolLedger.model.PersonalToolLedgerReturn;
import cec.jiutian.bc.toolManagement.domain.sharedToolLedger.handler.SharedToolLedgerBorrowingHandler;
import cec.jiutian.bc.toolManagement.domain.sharedToolLedger.handler.SharedToolLedgerReturnHandler;
import cec.jiutian.bc.toolManagement.domain.sharedToolLedger.proxy.SharedToolLedgerDataProxy;
import cec.jiutian.bc.toolManagement.enums.BorrowSourceStatusEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowOperation;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "eam_tm_shared_tool_ledger")
@FabosJson(
        name = "公用工具台账",
        orderBy = "SharedToolLedger.createTime desc",
        power = @Power(add = false, delete = false, edit = false,viewDetails = false, export = false),
        dataProxy = SharedToolLedgerDataProxy.class,
        rowOperation = {
                @RowOperation(
                        title = "借出",
                        code = "SharedToolLedger@BORROWING",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        operationHandler = SharedToolLedgerBorrowingHandler.class,
                        fabosJsonClass = SharedToolLedgerBorrowing.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "SharedToolLedger@BORROWING"
                        )
                ),
                @RowOperation(
                        title = "归还",
                        code = "SharedToolLedger@RETURN",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        operationHandler = SharedToolLedgerReturnHandler.class,
                        fabosJsonClass = SharedToolLedgerReturn.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "SharedToolLedger@RETURN"
                        )
                )
        }
)
public class SharedToolLedger extends MetaModel {

    // 借入人
    @Transient
    @FabosJsonField(
            views = @View(title = "选择借入人", column = "name", show = false),
            edit = @Edit(
                    title = "选择借入人",
                    type = EditType.REFERENCE_TABLE,
                    //queryCondition = "{\"_excludeList\":\"${SPLIT(outPerson.id, ',')}\"}",
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType()
            )
    )
    private UserMTO borrowPerson;

    @FabosJsonField(
            views = @View(title = "借入人", show = false),
            edit = @Edit(
                    title = "借入人",
                    inputType = @InputType(length = 20),
                    readonly = @Readonly(add = true, edit = true),
                    notNull = true
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "borrowPerson", beFilledBy = "name"))
    )
    @Column(length = 20)
    private String borrowPersonName;

    @FabosJsonField(
            views = @View(title = "借入人ID", show = false),
            edit = @Edit(
                    show = false,
                    title = "借入人ID",
                    inputType = @InputType(length = 50)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "borrowPerson", beFilledBy = "id"))
    )
    @Column(length = 50)
    private String borrowPersonId;

    //归还人
    @FabosJsonField(
            views = @View(title = "归还人", show = false),
            edit = @Edit(title = "归还人", show = false,
                    inputType = @InputType(length = 20)
            )
    )
    @Column(length = 20)
    private String returnName;

    @FabosJsonField(
            views = @View(title = "归还人ID", show = false),
            edit = @Edit(title = "归还人ID", show = false,
                    inputType = @InputType(length = 50)
            )
    )
    @Column(length = 50)
    private String returnId;

    @FabosJsonField(
            views = @View(title = "物料id", show = false),
            edit = @Edit(title = "物料id", show = false)
    )
    private String materialId;

    // 物料编码
    @FabosJsonField(
            views = @View(title = "物料编码"),
            edit = @Edit(title = "物料编码")
    )
    private String materialCode;

    // 物料名称
    @FabosJsonField(
            views = @View(title = "物料名称"),
            edit = @Edit(title = "物料名称")
    )
    private String materialName;

    // 本厂批次号
    @FabosJsonField(
            views = @View(title = "本厂批号"),
            edit = @Edit(title = "本厂批号")
    )
    private String lotSerialId;

    // 可用数量
    @FabosJsonField(
            views = @View(title = "可用数量"),
            edit = @Edit(title = "可用数量")
    )
    private Double availableQuantity;

    // 单位
    @FabosJsonField(
            views = @View(title = "单位"),
            edit = @Edit(title = "单位")
    )
    private String accountingUnit;

    // 借用数量
    @FabosJsonField(
            views = @View(title = "借用数量", show = false),
            edit = @Edit(title = "借用数量", show = false)
    )
    private Double quantity;

    // 借用的来源
    @FabosJsonField(
            views = @View(title = "借用的来源", show = false),
            edit = @Edit(title = "借用的来源", inputType = @InputType(length = 20),
                    type = EditType.CHOICE, show = false,
                    choiceType = @ChoiceType(fetchHandler = BorrowSourceStatusEnum.class)
            )
    )
    @Column(length = 20)
    private String borrowSource;

    // 借用的来源id
    @FabosJsonField(
            views = @View(title = "借用的来源id", show = false),
            edit = @Edit(title = "借用的来源id", show = false)
    )
    private String borrowSourceId;

}
