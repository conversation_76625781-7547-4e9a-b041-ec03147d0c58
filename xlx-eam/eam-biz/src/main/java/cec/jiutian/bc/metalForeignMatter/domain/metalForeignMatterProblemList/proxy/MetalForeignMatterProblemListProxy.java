package cec.jiutian.bc.metalForeignMatter.domain.metalForeignMatterProblemList.proxy;

import cec.jiutian.bc.metalForeignMatter.domain.metalForeignMatterProblemList.model.MetalForeignMatterProblem;
import cec.jiutian.bc.mto.UserMTO;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class MetalForeignMatterProblemListProxy implements DataProxy<MetalForeignMatterProblem> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public void afterSingleFetch(Map<String, Object> map) {
//        if (map.get("ledgerId") != null) {
//            MetalForeignMatterLedgerInformation ledgerInformation = fabosJsonDao.findById(MetalForeignMatterLedgerInformation.class, map.get("ledgerId"));
//            if (ledgerInformation != null) {
//                map.put("ledgerInformation", ledgerInformation);
//                map.put("ledgerInformation_serialLotId", ledgerInformation.getSerialLotId());
//            } else {
//                throw new FabosJsonApiErrorTip("未查到金属异物台账源数据，请确认");
//            }
//        }
        if (map.get("responsiblePersonId") != null) {
            UserMTO userMTO = fabosJsonDao.findById(UserMTO.class, map.get("responsiblePersonId"));
            if (userMTO != null) {
                map.put("responsiblePersonMTO", userMTO);
                map.put("responsiblePersonMTO_name", userMTO.getName());
            } else {
                throw new FabosJsonApiErrorTip("未查到责任人源数据，请确认");
            }
        }
    }

    @Override
    public void beforeAdd(MetalForeignMatterProblem entity) {
        entity.setCurrentState(OrderCurrentStateEnum.Enum.EDIT.name());
    }

    @Override
    public void beforeUpdate(MetalForeignMatterProblem entity) {
        if (!entity.getCurrentState().equals(OrderCurrentStateEnum.Enum.EDIT.name())) {
            throw new FabosJsonApiErrorTip("开立状态允许编辑");
        }
    }

    @Override
    public void beforeDelete(MetalForeignMatterProblem entity) {
        if (!entity.getCurrentState().equals(OrderCurrentStateEnum.Enum.EDIT.name())) {
            throw new FabosJsonApiErrorTip("开立状态允许删除");
        }
    }

}
