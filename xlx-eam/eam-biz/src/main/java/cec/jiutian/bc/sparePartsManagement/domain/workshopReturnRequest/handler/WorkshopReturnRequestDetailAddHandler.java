package cec.jiutian.bc.sparePartsManagement.domain.workshopReturnRequest.handler;

import cec.jiutian.bc.sparePartsManagement.domain.workshopInventory.model.WorkshopInventory;
import cec.jiutian.bc.sparePartsManagement.domain.workshopReturnRequest.model.WorkshopReturnRequest;
import cec.jiutian.bc.sparePartsManagement.domain.workshopReturnRequest.model.WorkshopReturnRequestDetail;
import cec.jiutian.common.util.BeanUtils;
import cec.jiutian.view.ReferenceAddType;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class WorkshopReturnRequestDetailAddHandler implements ReferenceAddType.ReferenceAddHandler<WorkshopReturnRequest, WorkshopInventory> {

    @Override
    public Map<String, Object> handle(WorkshopReturnRequest request, List<WorkshopInventory> inventories) {
        Map<String, Object> result = new HashMap<>();
        List<WorkshopReturnRequestDetail> details = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(inventories)) {
            inventories.forEach(c -> {
                WorkshopReturnRequestDetail detail = new WorkshopReturnRequestDetail();
                BeanUtils.copyProperties(c, detail);
                detail.setId(null);
                detail.setWorkshopInventoryId(c.getId());
                details.add(detail);
            });
            result.put("details", details);
        }
        return result;
    }
}
