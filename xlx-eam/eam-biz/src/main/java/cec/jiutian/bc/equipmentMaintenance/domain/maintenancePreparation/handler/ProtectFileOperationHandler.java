package cec.jiutian.bc.equipmentMaintenance.domain.maintenancePreparation.handler;

import cec.jiutian.bc.equipmentMaintenance.domain.maintenancePreparation.model.MaintenancePreparation;
import cec.jiutian.bc.equipmentMaintenance.domain.maintenancePreparation.mto.MaintenancePreparationProtectMTO;
import cec.jiutian.bc.generalModeler.util.StringUtils;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/8
 * @description TODO
 */
@Component
public class ProtectFileOperationHandler implements OperationHandler<MaintenancePreparation, MaintenancePreparationProtectMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<MaintenancePreparation> data, MaintenancePreparationProtectMTO modelObject, String[] param) {
        MaintenancePreparation maintenancePreparation = data.get(0);
        if (modelObject != null && StringUtils.isNotEmpty(modelObject.getProtectFile())) {
            maintenancePreparation.setProtectFile(modelObject.getProtectFile());
            fabosJsonDao.mergeAndFlush(maintenancePreparation);
        }
        return OperationHandler.super.exec(data, modelObject, param);
    }

    @Override
    public MaintenancePreparationProtectMTO fabosJsonFormValue(List<MaintenancePreparation> data, MaintenancePreparationProtectMTO fabosJsonForm, String[] param) {
        BeanUtil.copyProperties(data.get(0), fabosJsonForm);
        return fabosJsonForm;
    }
}
