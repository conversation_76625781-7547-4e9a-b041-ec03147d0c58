package cec.jiutian.bc.metalForeignMatter.domain.metalForeignMatterLedgerInformation.model;

import cec.jiutian.bc.equipmentArchive.domain.equipmentArchive.model.EquipmentArchive;
import cec.jiutian.bc.equipmentArchive.domain.equipmentArchive.model.EquipmentComponentDetail;
import cec.jiutian.bc.equipmentArchive.domain.equipmentArchive.model.EquipmentPartDetail;
import cec.jiutian.bc.metalForeignMatter.domain.metalForeignMatterLedgerInformation.handler.*;
import cec.jiutian.bc.metalForeignMatter.domain.metalForeignMatterLedgerInformation.proxy.MetalRegionalInfoDataProxy;
import cec.jiutian.bc.metalForeignMatter.domain.metalForeignMatterRegionalLedger.model.MetalForeignMatterRegionalLedger;
import cec.jiutian.bc.metalForeignMatter.enumeration.*;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.*;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.RowBaseOperation;
import cec.jiutian.view.type.RowOperation;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/26
 * @description TODO
 */
@FabosJson(
        name = "金属异物台账信息",
        orderBy = "MetalForeignMatterLedgerInformation.createTime desc",
        dataProxy = MetalRegionalInfoDataProxy.class,
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "currentState != 'TO_BE_CONFIRMED'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                )
        },
        rowOperation = {
                @RowOperation(
                        title = "确认",
                        code = "MetalForeignMatterLedgerInformation@CONFIRM",
                        operationHandler = MetalRegionalInfoConfirmOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        ifExpr = "currentState != 'TO_BE_CONFIRMED'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "MetalForeignMatterLedgerInformation@CONFIRM"
                        )
                ),
        }
)
@Table(name = "eam_mf_metal_foreign_matter_ledger_info",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"region_id", "process_id", "device_id", "component_id", "material_code_id"})
        }
)
@Entity
@Getter
@Setter
@TemplateType(type = "treeTable")
public class MetalForeignMatterLedgerInformation extends MetaModel {

    @FabosJsonField(
            views = @View(title = "本厂批次"),
            edit = @Edit(title = "本厂批次",notNull = true)
    )
    private String serialLotId;

    @ManyToOne
    @JoinColumn(name = "region_id")
    @FabosJsonField(
            views = @View(title = "区域", column = "locationName"),
            edit = @Edit(title = "区域",
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "locationName"),
                    search = @Search(vague = true)
            )
    )
    private MetalForeignMatterRegionalLedger region;

    @ManyToOne
    @JoinColumn(name = "device_id")
    @FabosJsonField(
            views = @View(title = "设备", column = "name"),
            edit = @Edit(title = "设备",
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    search = @Search(vague = true)
            )
    )
    private EquipmentArchive device;

    @FabosJsonField(
            views = @View(title = "工序ID",show = false),
            edit = @Edit(title = "工序ID",
                    show = false
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "device", beFilledBy = "processId"))
    )
    private String processId;

    @FabosJsonField(
            views = @View(title = "工序名称"),
            edit = @Edit(title = "工序名称",
                    readonly = @Readonly(add = true,edit = true),
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "device", beFilledBy = "processName"))
    )
    private String processName;

    @FabosJsonField(
            views = @View(title = "部件", column = "code"),
            edit = @Edit(title = "部件",
                    readonly = @Readonly(add = false),
                    type = EditType.REFERENCE_TABLE,
                    notNull = true, search = @Search(vague = true),
                    referenceTableType = @ReferenceTableType(id = "id", label = "code"),
                    queryCondition = "{\"equipmentArchive.id\": \"${device.id}\"}"
                    , allowAddMultipleRows = false
            )
    )
    @ManyToOne
    @JoinColumn(name = "equipment_component_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private EquipmentComponentDetail component;

    @FabosJsonField(
            views = @View(title = "零件", column = "code"),
            edit = @Edit(title = "零件",
                    readonly = @Readonly(add = false),
                    type = EditType.REFERENCE_TABLE,
                    search = @Search(vague = true),
                    referenceTableType = @ReferenceTableType(id = "id", label = "code"),
                    queryCondition = "{\"equipmentComponentDetail.id\": \"${component.id}\"}"
                    , allowAddMultipleRows = false
            )
    )
    @ManyToOne
    @JoinColumn(name = "equipment_part_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private EquipmentPartDetail part;

    @FabosJsonField(
            views = @View(title = "物资名称",show = false),
            edit = @Edit(title = "物资名称",show = false)
    )
    private String materialName;

    @FabosJsonField(
            views = @View(title = "物资编码",show = false),
            edit = @Edit(title = "物资编码",show = false)
    )
    private String materialCode;

    @FabosJsonField(
            views = @View(title = "零件数量"),
            edit = @Edit(title = "零件数量",  search = @Search(vague = true),
                    numberType = @NumberType(min = 0))
    )
    private Integer partNumber;

    @FabosJsonField(
            views = @View(title = "材质"),
            edit = @Edit(title = "材质", type = EditType.CHOICE, search = @Search(vague = true),
                    choiceType = @ChoiceType(fetchHandler = MaterialEnum.class))
    )
    private String material;

    @FabosJsonField(
            views = @View(title = "严重度"),
            edit = @Edit(title = "严重度", readonly = @Readonly, search = @Search(vague = true)),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "material",
                    dynamicHandler = MetalRegionalInfoServerityDynamicHandler.class))
    )
    private String serverity;

    @FabosJsonField(
            views = @View(title = "接触方式"),
            edit = @Edit(title = "接触方式", type = EditType.CHOICE, search = @Search(vague = true),
                    choiceType = @ChoiceType(fetchHandler = ContactMethodEnum.class))
    )
    private String contactMethod;

    @FabosJsonField(
            views = @View(title = "磨损产生可能"),
            edit = @Edit(title = "磨损产生可能", type = EditType.CHOICE, search = @Search(vague = true),
                    choiceType = @ChoiceType(fetchHandler = PossibleWearTearEnum.class))
    )
    private String possibleWearTear;

    @FabosJsonField(
            views = @View(title = "频度"),
            edit = @Edit(title = "频度", readonly = @Readonly, search = @Search(vague = true)),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = {"contactMethod", "possibleWearTear"},
                    dynamicHandler = MetalRegionalInfoFrequDynamicHandler.class))
    )
    private String frequentness;

    @FabosJsonField(
            views = @View(title = "探测时机"),
            edit = @Edit(title = "探测时机", type = EditType.CHOICE, search = @Search(vague = true),
                    choiceType = @ChoiceType(fetchHandler = DetectionTimingEnum.class))
    )
    private String detectionTiming;

    @FabosJsonField(
            views = @View(title = "方法有效性"),
            edit = @Edit(title = "方法有效性", type = EditType.CHOICE, search = @Search(vague = true),
                    choiceType = @ChoiceType(fetchHandler = MethodEffectivenessEnum.class))
    )
    private String methodEffectiveness;

    @FabosJsonField(
            views = @View(title = "探测度"),
            edit = @Edit(title = "探测度", readonly = @Readonly, search = @Search(vague = true)),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = {"detectionTiming", "methodEffectiveness"},
                    dynamicHandler = MetalRegionalInfoDetecDynamicHandler.class))
    )
    private String detectivity;


    @FabosJsonField(
            views = @View(title = "AP"),
            edit = @Edit(title = "AP", readonly = @Readonly, search = @Search(vague = true))
    )
    private String ap;

    @FabosJsonField(
            views = @View(title = "图片", export = false),
            edit = @Edit(title = "图片",
                    type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(
                            fileTypes = {".jpg,.png"},
                            size = 5120,
                            maxLimit = 10,
                            type = AttachmentType.Type.IMAGE)
            )
    )
    private String pictureAttachment;

    @FabosJsonField(
            views = @View(title = "业务状态"),
            edit = @Edit(title = "业务状态", show = true, type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = BusinessStatusEnum.class),
                    defaultVal = "NORMAL",
                    search = @Search(vague = true))
    )
    private String businessStatus;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态", show = false, type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = StatusEnum.class),
                    search = @Search(vague = true))
    )
    private String currentState;

    @FabosJsonField(
            edit = @Edit(title = "XRF识别成分（%）", type = EditType.TAB_TABLE_ADD),
            views = @View(title = "XRF识别成分（%）", type= ViewType.TABLE_VIEW)
    )
    @JoinColumn(name = "eam_mf_metal_foreign_matter_ledger_info_id")
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true,fetch = FetchType.EAGER)
    @OrderBy
    private List<MetalForeignMatterXRFIdentifyComponent> metalForeignMatterXRFIdentifyComponents;
}
