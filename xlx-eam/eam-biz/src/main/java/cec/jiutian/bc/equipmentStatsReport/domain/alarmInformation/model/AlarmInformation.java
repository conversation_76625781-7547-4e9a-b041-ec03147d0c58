package cec.jiutian.bc.equipmentStatsReport.domain.alarmInformation.model;

import cec.jiutian.bc.equipmentStatsReport.domain.alarmInformation.handler.AlarmInfoDynamicHandler;
import cec.jiutian.bc.equipmentStatsReport.domain.alarmInformation.proxy.AlarmInfoDataProxy;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.Search;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/06/11
 * @description
 */
@FabosJson(
        name = "报警信息查询",
        orderBy = "AlarmInformation.createTime desc",
        dataProxy = AlarmInfoDataProxy.class
)
@Table(name = "eam_elm_alarm_information",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Getter
@Setter
public class AlarmInformation extends MetaModel {

    @FabosJsonField(
            views = @View(title = "编码"),
            edit = @Edit(title = "编码",
                    readonly = @Readonly(add = false, edit = true),
                    search = @Search(vague = true), notNull = true),
            dynamicField = @DynamicField(dependFiled = @DependFiled(buttonName = "生成", changeBy = "id",
                    dynamicHandler = AlarmInfoDynamicHandler.class))
    )
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "设备编号"),
            edit = @Edit(title = "设备编号",
                    notNull = true,
                    search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    private String deviceNumber;

    @FabosJsonField(
            views = @View(title = "设备名称"),
            edit = @Edit(title = "设备名称",
                    notNull = true,
                    search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    private String deviceName;

    @FabosJsonField(
            views = @View(title = "车间"),
            edit = @Edit(title = "车间",
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    private String factoryArea;

    @FabosJsonField(
            views = @View(title = "产线"),
            edit = @Edit(title = "产线",
                    search = @Search(vague = true))
    )
    private String factoryLine;

    @FabosJsonField(
            views = @View(title = "设备类别"),
            edit = @Edit(title = "设备类别",
                    search = @Search(vague = true))
    )
    private String deviceType;

    @FabosJsonField(
            views = @View(title = "报警等级"),
            edit = @Edit(title = "报警等级",
                    search = @Search(vague = true))
    )
    private String alarmLevel;

    @FabosJsonField(
            views = @View(title = "报警时间"),
            edit = @Edit(title = "报警时间",
                    search = @Search(vague = true))
    )
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "Asia/Shanghai"
    )
    private Date alarmTime;

    @FabosJsonField(
            views = @View(title = "报警内容"),
            edit = @Edit(title = "报警内容",
                    type = EditType.TEXTAREA)
    )
    private String alarmContent;
}