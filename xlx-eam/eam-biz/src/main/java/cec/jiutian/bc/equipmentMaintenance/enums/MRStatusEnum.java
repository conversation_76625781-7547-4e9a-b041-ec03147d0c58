package cec.jiutian.bc.equipmentMaintenance.enums;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

public class MRStatusEnum implements ChoiceFetchHandler {

    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum data : Enum.values()) {
            list.add(new VLModel(data.name(), data.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        //创建，班组长审批，待接单，待维修，维修中，维修完成
        created("开立"),
        approval("审批中"),
        reject("驳回"),
        waitOrder("待接单"),
        waitRepair("待维修"),
        WaitProtectConfirm("防护准备待确认"),
        repairing("维修中"),
        repairComplete("维修完成"),
        CheckFinish("验收通过"),
        ;
        private final String value;
    }
}
