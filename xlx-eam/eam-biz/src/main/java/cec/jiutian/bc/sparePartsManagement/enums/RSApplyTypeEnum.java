package cec.jiutian.bc.sparePartsManagement.enums;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

public class RSApplyTypeEnum implements ChoiceFetchHandler {

    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum data : Enum.values()) {
            list.add(new VLModel(data.name(), data.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        //线边领料申请、线边领用出库
        LINE_IN("线边领料申请"),
        LINE_OUT("线边领用出库"),
        TOOL_OUT("工具领用申请出库"),
        ;
        private final String value;
    }
}
