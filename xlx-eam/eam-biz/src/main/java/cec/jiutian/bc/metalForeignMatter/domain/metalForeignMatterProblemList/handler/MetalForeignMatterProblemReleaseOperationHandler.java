package cec.jiutian.bc.metalForeignMatter.domain.metalForeignMatterProblemList.handler;

import cec.jiutian.bc.metalForeignMatter.domain.metalForeignMatterProblemList.model.MetalForeignMatterProblem;
import cec.jiutian.bc.metalForeignMatter.domain.metalForeignMatterProblemList.model.MetalForeignMatterProblemRelease;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class MetalForeignMatterProblemReleaseOperationHandler implements OperationHandler<MetalForeignMatterProblem, MetalForeignMatterProblemRelease> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<MetalForeignMatterProblem> data, MetalForeignMatterProblemRelease modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            MetalForeignMatterProblem entity = data.get(0);
            entity.setPreCompleteDate(modelObject.getPreCompleteDate());
            entity.setCurrentState(OrderCurrentStateEnum.Enum.EXECUTE.name());
            fabosJsonDao.mergeAndFlush(entity);

            // todo: 通知责任人

        }
        return "msg.success('操作成功')";
    }
}
