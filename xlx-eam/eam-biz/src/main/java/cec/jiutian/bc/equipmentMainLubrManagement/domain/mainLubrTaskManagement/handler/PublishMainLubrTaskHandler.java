package cec.jiutian.bc.equipmentMainLubrManagement.domain.mainLubrTaskManagement.handler;

import cec.jiutian.bc.equipmentArchive.domain.equipmentArchive.repository.EquipmentArchiveRepository;
import cec.jiutian.bc.equipmentMainLubrManagement.domain.mainLubrTaskManagement.enumration.MltManagementBusinessStatusEnum;
import cec.jiutian.bc.equipmentMainLubrManagement.domain.mainLubrTaskManagement.model.MainLubrTaskManage;
import cec.jiutian.bc.equipmentMainLubrManagement.domain.mainLubrTaskManagement.mto.PublishMainLubrTaskMTO;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/6
 * @description
 */
@Slf4j
@Component
public class PublishMainLubrTaskHandler implements OperationHandler<MainLubrTaskManage, PublishMainLubrTaskMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private EquipmentArchiveRepository equipmentArchiveRepository;

    @Override
    @Transactional
    public String exec(List<MainLubrTaskManage> data, PublishMainLubrTaskMTO modelObject, String[] param) {
        if (modelObject == null) {
            throw new ServiceException("数据异常");
        }
        MainLubrTaskManage model = data.get(0);
        BeanUtil.copyProperties(modelObject, model, "createBy", "createTime");
        model.setBusinessStatus(MltManagementBusinessStatusEnum.Enum.TO_BE_ALLOCATED.name());
        fabosJsonDao.mergeAndFlush(model);
        return "msg.success('操作成功')";
    }

    @Override
    public PublishMainLubrTaskMTO fabosJsonFormValue(List<MainLubrTaskManage> data,
                                                           PublishMainLubrTaskMTO fabosJsonForm, String[] param) {
        if (CollectionUtils.isEmpty(data)) {
            throw new ServiceException("请选择操作数据");
        }
        MainLubrTaskManage mainLubrTaskManage = data.get(0);
        BeanUtils.copyProperties(mainLubrTaskManage, fabosJsonForm);
        return fabosJsonForm;
    }

}
