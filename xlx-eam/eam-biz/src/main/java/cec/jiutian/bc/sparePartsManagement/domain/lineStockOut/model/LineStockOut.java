package cec.jiutian.bc.sparePartsManagement.domain.lineStockOut.model;

import cec.jiutian.bc.mto.FactoryArea;
import cec.jiutian.bc.sparePartsManagement.domain.lineStockOut.handler.LineStockOutComfirmOprHandler;
import cec.jiutian.bc.sparePartsManagement.domain.lineStockOut.handler.LineStockOutReleaseOprHandler;
import cec.jiutian.bc.sparePartsManagement.domain.lineStockOut.handler.OutLineInventoryReferenceAddHandler;
import cec.jiutian.bc.sparePartsManagement.domain.lineStockOut.handler.StockOutCodeGenerateDynamicHandler;
import cec.jiutian.bc.sparePartsManagement.domain.lineStockOut.proxy.LineStockOutProxy;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.frame.module.MetaUser;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.*;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DependFieldDisplay;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.RowBaseOperation;
import cec.jiutian.view.type.RowOperation;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/16
 * @description TODO
 */
@FabosJson(
        name = "线边领用出库",
        orderBy = "LineStockOut.createTime desc",
        dataProxy = LineStockOutProxy.class,
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "businessState != 'EDIT'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "businessState != 'EDIT'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
        },
        rowOperation = {
                @RowOperation(
                        title = "审核",
                        code = "LineStockOut@RELEASE",
                        operationHandler = LineStockOutReleaseOprHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "是否确定操作？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "LineStockOut@RELEASE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "businessState != 'EDIT'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "确认",
                        code = "LineStockOut@COMFIRM",
                        operationHandler = LineStockOutComfirmOprHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "是否确定操作？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "LineStockOut@COMFIRM"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "businessState != 'EXECUTE'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
        }
)
@Table(name = "eam_spm_line_stock_out",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Getter
@Setter
@TemplateType(type = "multiTable")
public class LineStockOut extends MetaModel {

    @FabosJsonField(
            views = @View(title = "领用出库单号"),
            edit = @Edit(title = "领用出库单号", notNull = true,search = @Search),
            dynamicField = @DynamicField(dependFiled = @DependFiled(buttonName = "生成", changeBy = "id",
                    dynamicHandler = StockOutCodeGenerateDynamicHandler.class))
    )
    private String generalCode;

    @Transient
    @FabosJsonField(
            views = @View(title = "车间", show = false, column = "factoryAreaName"),
            edit = @Edit(title = "车间",
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "factoryAreaName"),
                    filter = @Filter(value = "factoryAreaTypeCode = '02'")
            )
    )
    private FactoryArea factoryArea;

    @FabosJsonField(
            views = @View(title = "车间ID", show = false),
            edit = @Edit(title = "车间ID",
                    show = false
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "factoryArea", beFilledBy = "id"))
    )
    private String factoryAreaId;

    @FabosJsonField(
            views = @View(title = "车间名称"),
            edit = @Edit(title = "车间名称",
                    readonly = @Readonly(add = true, edit = true),
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "factoryArea", beFilledBy = "factoryAreaName"))
    )
    private String factoryAreaName;

    @Transient
    @FabosJsonField(
            views = @View(title = "产线", show = false, column = "factoryAreaName"),
            edit = @Edit(title = "产线",
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "factoryAreaName"),
                    queryCondition = "{\"pid\": \"${factoryArea.id}\"}",
                    filter = @Filter(value = "factoryAreaTypeCode = '03'")
            )
    )
    private FactoryArea factoryLine;

    @FabosJsonField(
            views = @View(title = "产线Id", show = false),
            edit = @Edit(title = "产线Id",
                    show = false
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "factoryLine", beFilledBy = "id"))
    )
    private String factoryLineId;

    @FabosJsonField(
            views = @View(title = "产线名称"),
            edit = @Edit(title = "产线名称",
                    search = @Search(vague = true),
                    readonly = @Readonly(add = true, edit = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "factoryLine", beFilledBy = "factoryAreaName"))
    )
    private String factoryLineName;

    @ManyToOne
    @JoinColumn(name = "request_person_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @FabosJsonField(
            views = @View(title = "申请人", column = "name", type = ViewType.TABLE_FORM),
            edit = @Edit(title = "申请人",
                    filter = @Filter(value = "MetaUser.state = 'Y'"),
                    readonly = @Readonly,
                    type = EditType.REFERENCE_TABLE,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "protectFlag == true"),
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"))
    )
    private MetaUser requestPerson;

    @FabosJsonField(
            views = @View(title = "当前状态"),
            edit = @Edit(title = "当前状态", show = false, type = EditType.CHOICE, search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = OrderCurrentStateEnum.class))
    )
    private String businessState;

    @FabosJsonField(
            views = @View(title = "设备标识",show = false),
            edit = @Edit(title = "设备标识", show = false)
    )
    private String equipmentSign;

    @FabosJsonField(
            views = @View(title = "备注"),
            edit = @Edit(title = "备注",
                    type = EditType.TEXTAREA)
    )
    private String remark;

    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumn(name = "line_stock_out_id")
    @OrderBy
    @FabosJsonField(
            edit = @Edit(title = "线边领用出库明细", type = EditType.TAB_REFER_ADD
            ),
            referenceAddType = @ReferenceAddType(referenceClass = "InventoryLedger",
                    queryCondition = "{\"currentState\":\"normal\",\"availableQuantity\": \"1,\"}",
                    editable = {"stockOutQuantity"},
                    referenceAddHandler = OutLineInventoryReferenceAddHandler.class),
            views = @View(title = "线边领用出库明细", type = ViewType.TABLE_VIEW, extraPK = "inventoryId", column = "lotSerialId")
    )
    private List<LineStockOutDetail> detailList;
}
