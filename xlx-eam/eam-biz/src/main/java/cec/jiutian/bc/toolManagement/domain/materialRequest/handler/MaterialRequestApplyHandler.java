package cec.jiutian.bc.toolManagement.domain.materialRequest.handler;

import cec.jiutian.bc.toolManagement.domain.materialRequest.model.InventoryMaterial;
import cec.jiutian.bc.toolManagement.domain.materialRequest.model.MaterialRequest;
import cec.jiutian.bc.toolManagement.enums.MaterialRequestStatusEnum;
import cec.jiutian.bc.toolManagement.port.client.BusinessMesFeignClient;
import cec.jiutian.bc.toolManagement.port.dto.StockInDTO;
import cec.jiutian.bc.toolManagement.port.dto.StockInDetailDTO;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class MaterialRequestApplyHandler implements OperationHandler<MaterialRequest, Void> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private BusinessMesFeignClient businessMesFeignClient;

    @Override
    public String exec(List<MaterialRequest> data, Void modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            MaterialRequest materialRequest = data.get(0);
            // wms会调用接口改状态 [待接收]
            if (!MaterialRequestStatusEnum.Enum.WAIT_RECEIVED.name().equals(materialRequest.getStatus())) {
                return "msg.error('wms中领料申请未处于待接收状态，不可确认')";
            }
            // 调用MES接口，创建线边仓入库单
            mesStockIn(materialRequest);

            materialRequest.setStatus(MaterialRequestStatusEnum.Enum.CONFIRMED.name());
            fabosJsonDao.mergeAndFlush(materialRequest);
        }
        return "msg.success('操作成功')";
    }

    private void mesStockIn(MaterialRequest materialRequest) {
        if (CollectionUtils.isEmpty(materialRequest.getInventoryMaterialList())) {
            return;
        }
        StockInDTO stockInDTO = StockInDTO.buildStockInDTO(
                "EAM_RAW_MATERIAL",//领料入库
                materialRequest.getId(),
                materialRequest.getGeneralCode(),
                materialRequest.getApplicantName(),
                materialRequest.getApplicantId());

        List<StockInDetailDTO> stockInDetailDTOS = new ArrayList<>();
        for (InventoryMaterial material : materialRequest.getInventoryMaterialList()) {
            stockInDetailDTOS.add(StockInDetailDTO.buildStockInDetailDTO(material));
        }
        stockInDTO.setStockInDetailList(stockInDetailDTOS);

        try {
            String stockIn = businessMesFeignClient.createStockIn(stockInDTO);
            log.info("创建mes入库单参数：{}, 返回入库单id：{}", stockInDTO, stockIn);
        } catch (Exception e) {
            log.error("创建mes入库单报错：", e);
            throw new ServiceException("创建mes入库单报错：" + e.getMessage());
        }
    }

}
