package cec.jiutian.bc.equipmentMainLubrManagement.domain.myMainLubrEquipmentTask.model;

import cec.jiutian.bc.equipmentArchive.domain.equipment.model.Equipment;
import cec.jiutian.bc.equipmentArchive.domain.equipmentArchive.model.EquipmentArchive;
import cec.jiutian.bc.equipmentMainLubrManagement.domain.mainLubrTaskManagement.enumration.MltResultEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.InputGroup;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.RowBaseOperation;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025/06/05
 * @description
 */
@FabosJson(
        name = "设备保养标准",
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "1==1",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.HIDE

                ),
                @RowBaseOperation(
                        code = "add",
                        ifExpr = "1==1",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.HIDE
                )
        },
        orderBy = "MyMainLubrTaskManageDetail.createTime desc"
)
@Table(name = "eam_elm_task_management_detail")
@Entity
@Getter
@Setter
public class MyMainLubrTaskManageDetail extends MetaModel {

    @FabosJsonField(
            views = @View(title = "编码"),
            edit = @Edit(title = "编码",
                    readonly = @Readonly(),
                    search = @Search(vague = true)
            )
    )
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "弹出表主键", show = false),
            edit = @Edit(title = "弹出表主键", show = false)
    )
    private String popId;

    @FabosJsonField(
            views = @View(title = "保养标准名称"),
            edit = @Edit(title = "保养标准名称",
                    readonly = @Readonly(),
                    search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    private String maintenanceStandardName;

    @FabosJsonField(
            views = @View(title = "保养计划工时(小时)"),
            edit = @Edit(title = "保养计划工时(小时)",
                    readonly = @Readonly(),
                    inputType = @InputType(length = 7),
                    numberType = @NumberType(min = 0, precision = 1),
                    inputGroup = @InputGroup(postfix = "小时")
            )
    )
    private Double maintenanceScheduleHour;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "适用设备", column = "code"),
            edit = @Edit(title = "适用设备",
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "code"),
                    readonly = @Readonly()
            )
    )
    private Equipment equipment;

    @FabosJsonField(
            views = @View(title = "保养部位"),
            edit = @Edit(title = "保养部位",
                    readonly = @Readonly(),
                    search = @Search(vague = true))
    )
    private String maintenancePart;

    @FabosJsonField(
            views = @View(title = "保养注意事项"),
            edit = @Edit(title = "保养注意事项",
                    readonly = @Readonly(),
                    search = @Search(vague = true))
    )
    private String maintenancePrecaution;

    @FabosJsonField(
            views = @View(title = "标准"),
            edit = @Edit(title = "标准",
                    readonly = @Readonly(),
                    search = @Search(vague = true))
    )
    private String standard;

    @FabosJsonField(
            views = @View(title = "描述"),
            edit = @Edit(title = "描述",
                    readonly = @Readonly(),
                    type = EditType.TEXTAREA)
    )
    private String description;

    @FabosJsonField(
            views = @View(title = "结果描述"),
            edit = @Edit(title = "结果描述",
                    search = @Search(vague = true))
    )
    private String resultDescription;

    @FabosJsonField(
            views = @View(title = "保养实际工时(小时)"),
            edit = @Edit(title = "保养实际工时(小时)",
                    notNull = true,
                    inputType = @InputType(length = 7),
                    numberType = @NumberType(min = 0, precision = 1),
                    inputGroup = @InputGroup(postfix = "小时")
            )
    )
    private Double maintenanceActualHour;

    @FabosJsonField(
            views = @View(title = "现场图片"),
            edit = @Edit(title = "现场图片",
                    type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(size = 5120, maxLimit = 10, type = AttachmentType.Type.BASE)
            )
    )
    private String scenePicture;

    @FabosJsonField(
            views = @View(title = "校验结果"),
            edit = @Edit(title = "校验结果",
                    type = EditType.CHOICE,
                    notNull = true,
                    choiceType = @ChoiceType(fetchHandler = MltResultEnum.class))
    )
    private String result;
}
