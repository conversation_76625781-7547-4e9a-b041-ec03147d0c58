package cec.jiutian.bc.equipmentMaintenance.domain.maintenanceExecute.handler;

import cec.jiutian.bc.ecs.dto.SendMsgGroupDTO;
import cec.jiutian.bc.ecs.provider.EcsMessageProvider;
import cec.jiutian.bc.equipmentArchive.domain.equipmentArchive.model.EquipmentArchive;
import cec.jiutian.bc.equipmentArchive.domain.equipmentArchive.model.EquipmentComponentDetail;
import cec.jiutian.bc.equipmentArchive.domain.equipmentArchive.model.EquipmentPartDetail;
import cec.jiutian.bc.equipmentMaintenance.domain.maintenanceExecute.model.MaintenanceExecute;
import cec.jiutian.bc.equipmentMaintenance.domain.maintenanceExecute.mto.EquipmentArchiveMTO;
import cec.jiutian.bc.equipmentMaintenance.domain.maintenanceExecute.mto.EquipmentComponentDetailMTO;
import cec.jiutian.bc.equipmentMaintenance.domain.maintenanceExecute.mto.EquipmentPartDetailMTO;
import cec.jiutian.bc.generalModeler.util.StringUtils;
import cec.jiutian.bc.metalForeignMatter.domain.metalForeignMatterLedgerInformation.model.MetalForeignMatterLedgerInformation;
import cec.jiutian.bc.metalForeignMatter.domain.metalForeignMatterReplaceRecord.model.MetalForeignMatterReplaceRecord;
import cec.jiutian.bc.metalForeignMatter.enumeration.BusinessStatusEnum;
import cec.jiutian.bc.sparePartsManagement.domain.workshopInventory.model.WorkshopInventory;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/4/8
 * @description TODO
 */
@Component
public class ReplaceOperationHandler implements OperationHandler<MaintenanceExecute, EquipmentArchiveMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private EcsMessageProvider ecsMessageProvider;

    @Override
    public String exec(List<MaintenanceExecute> data, EquipmentArchiveMTO modelObject, String[] param) {
        // TODO: 2025/4/27 推送金属异物台账消息到qms
        MaintenanceExecute maintenanceExecute = fabosJsonDao.findById(MaintenanceExecute.class,modelObject.getMaintenanceId());
        EquipmentArchive condition = new EquipmentArchive();
        condition.setGeneralCode(maintenanceExecute.getDeviceNumber());
        EquipmentArchive equipmentArchiveBefore = fabosJsonDao.selectOne(condition);
        if (equipmentArchiveBefore != null) {
            // 零件替换
            getPartReplaceInfo(equipmentArchiveBefore,modelObject);

            List<EquipmentComponentDetail> componentDetailList = equipmentArchiveBefore.getComponentDetailList();
            List<EquipmentComponentDetailMTO> componentDetailMTOList = modelObject.getComponentDetailList();
            // 被替换部件
            List<EquipmentComponentDetail> uninstallComponentDetailList = componentDetailList.stream().filter(detail -> {
                List<String> inventoryIdList = componentDetailMTOList.stream().map(EquipmentComponentDetailMTO::getInventoryId).toList();
                return !inventoryIdList.contains(detail.getInventoryId());
            }).toList();

            // 替换部件
            List<EquipmentComponentDetailMTO> installComponentDetailList = componentDetailMTOList.stream().filter(detail -> {
                List<String> inventoryIdList = componentDetailList.stream().map(EquipmentComponentDetail::getInventoryId).toList();
                return !inventoryIdList.contains(detail.getInventoryId());
            }).toList();

            // 新添部件
            List<EquipmentComponentDetailMTO> installNewComponentDetailList = installComponentDetailList.stream().filter(detail -> {
                List<String> codeList = uninstallComponentDetailList.stream().map(EquipmentComponentDetail::getCode).toList();
                return !codeList.contains(detail.getCode());
            }).toList();

            componentReplaceHandle(equipmentArchiveBefore,uninstallComponentDetailList,installComponentDetailList,installNewComponentDetailList);
            fabosJsonDao.mergeAndFlush(modelObject);
        }
        return "alert(操作成功)";
    }

    @Override
    public EquipmentArchiveMTO fabosJsonFormValue(List<MaintenanceExecute> data, EquipmentArchiveMTO fabosJsonForm, String[] param) {
        EquipmentArchiveMTO condition = new EquipmentArchiveMTO();
        condition.setGeneralCode(data.get(0).getDeviceNumber());
        EquipmentArchiveMTO equipmentArchiveMTO = fabosJsonDao.selectOne(condition);
        if (equipmentArchiveMTO == null) {
            throw new FabosJsonApiErrorTip("未查询到设备档案信息，请确认");
        }
        equipmentArchiveMTO.setMaintenanceId(data.get(0).getId());
        return equipmentArchiveMTO;
    }

    /**
     * 创建部件更换信息
     */
    private void componentReplaceHandle(EquipmentArchive equipmentArchive,List<EquipmentComponentDetail> uninstallComponentDetailList,
                                        List<EquipmentComponentDetailMTO> installComponentDetailList,
                                        List<EquipmentComponentDetailMTO> installNewComponentDetailList) {
        if (CollectionUtils.isNotEmpty(uninstallComponentDetailList) && CollectionUtils.isNotEmpty(installComponentDetailList)) {
            // 创建部件更换记录
            uninstallComponentDetailList.forEach(component -> {
                MetalForeignMatterReplaceRecord metalForeignMatterReplaceRecord = new MetalForeignMatterReplaceRecord();
                metalForeignMatterReplaceRecord.setEquipmentArchive(equipmentArchive);
                metalForeignMatterReplaceRecord.setOldLotSerialId(component.getLotSerialId());
                metalForeignMatterReplaceRecord.setOldMaterialCode(component.getCode());
                metalForeignMatterReplaceRecord.setOldMaterialName(component.getName());
                metalForeignMatterReplaceRecord.setQuantity(component.getQuantity());
                // 更新金属异物台账
                updateMetalForeignInfo(equipmentArchive, component.getLotSerialId(), null);
                // 更新车间库存
                updateWorkShopInventory(component.getInventoryId(),null,null);

                Optional<EquipmentComponentDetailMTO> optional = installComponentDetailList.stream().filter(data -> data.getCode().equals(component.getCode())).findFirst();
                if (optional.isPresent()) {
                    EquipmentComponentDetailMTO installComponent = optional.get();
                    metalForeignMatterReplaceRecord.setNewLotSerialId(installComponent.getLotSerialId());
                    metalForeignMatterReplaceRecord.setNewMaterialCode(installComponent.getCode());
                    metalForeignMatterReplaceRecord.setNewMaterialName(installComponent.getName());
                    metalForeignMatterReplaceRecord.setQuantity(installComponent.getQuantity());
                    fabosJsonDao.mergeAndFlush(metalForeignMatterReplaceRecord);

                    // 更新金属异物台账
                    updateMetalForeignInfo(equipmentArchive, null, installComponent.getLotSerialId());
                    // 更新车间库存
                    updateWorkShopInventory(null, installComponent.getInventoryId() ,installComponent.getQuantity());
                }
            });
        }

        if (CollectionUtils.isNotEmpty(installNewComponentDetailList)) {
            installNewComponentDetailList.forEach(newComponent -> {
                MetalForeignMatterReplaceRecord metalForeignMatterReplaceRecord = new MetalForeignMatterReplaceRecord();
                metalForeignMatterReplaceRecord.setEquipmentArchive(equipmentArchive);
                metalForeignMatterReplaceRecord.setNewLotSerialId(newComponent.getLotSerialId());
                metalForeignMatterReplaceRecord.setNewMaterialCode(newComponent.getCode());
                metalForeignMatterReplaceRecord.setNewMaterialName(newComponent.getName());
                metalForeignMatterReplaceRecord.setQuantity(newComponent.getQuantity());
                fabosJsonDao.mergeAndFlush(metalForeignMatterReplaceRecord);
                // 更新金属异物台账
                updateMetalForeignInfo(equipmentArchive, null, newComponent.getLotSerialId());
                // 更新车间库存
                updateWorkShopInventory(null, newComponent.getInventoryId(), newComponent.getQuantity());
            });
        }
    }

    /**
     * 获取零件更换信息
     */
    private void getPartReplaceInfo(EquipmentArchive equipmentArchiveBefore, EquipmentArchiveMTO modelObject) {
        List<EquipmentPartDetailMTO> partDetailMTOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(modelObject.getComponentDetailList())) {
            modelObject.getComponentDetailList().forEach(componentMTODetail -> {
                if (CollectionUtils.isNotEmpty(componentMTODetail.getPartDetailList())) {
                    partDetailMTOList.addAll(componentMTODetail.getPartDetailList());
                }
            });
        }
        List<EquipmentPartDetail> partDetailList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(equipmentArchiveBefore.getComponentDetailList())) {
            equipmentArchiveBefore.getComponentDetailList().forEach(componentDetail -> {
                if (CollectionUtils.isNotEmpty(componentDetail.getPartDetailList())) {
                    partDetailList.addAll(componentDetail.getPartDetailList());
                }
            });
        }

        // 被替换零件
        List<EquipmentPartDetail> uninstallPartDetailList = partDetailList.stream().filter(detail -> {
            List<String> inventoryIdList = partDetailMTOList.stream().map(EquipmentPartDetailMTO::getInventoryId).toList();
            return !inventoryIdList.contains(detail.getInventoryId());
        }).toList();

        // 替换零件
        List<EquipmentPartDetailMTO> installPartDetailList = partDetailMTOList.stream().filter(detail -> {
            List<String> inventoryIdList = partDetailList.stream().map(EquipmentPartDetail::getInventoryId).toList();
            return !inventoryIdList.contains(detail.getInventoryId());
        }).toList();

        // 新添零件
        List<EquipmentPartDetailMTO> installNewPartDetailList = installPartDetailList.stream().filter(detail -> {
            List<String> codeList = uninstallPartDetailList.stream().map(EquipmentPartDetail::getCode).toList();
            return !codeList.contains(detail.getCode());
        }).toList();
        partReplaceHandle(equipmentArchiveBefore, uninstallPartDetailList, installPartDetailList, installNewPartDetailList);
    }

    /**
     * 创建零件更换记录
     */
    private void partReplaceHandle(EquipmentArchive equipmentArchive, List<EquipmentPartDetail> uninstallPartDetailList,
                                   List<EquipmentPartDetailMTO> installPartDetailList,
                                   List<EquipmentPartDetailMTO> installNewPartDetailList) {
        if (CollectionUtils.isNotEmpty(uninstallPartDetailList) && CollectionUtils.isNotEmpty(installPartDetailList)) {
            uninstallPartDetailList.forEach(uninstallPartDetail -> {
                MetalForeignMatterReplaceRecord metalForeignMatterReplaceRecord = new MetalForeignMatterReplaceRecord();
                metalForeignMatterReplaceRecord.setEquipmentArchive(equipmentArchive);
                metalForeignMatterReplaceRecord.setOldLotSerialId(uninstallPartDetail.getLotSerialId());
                metalForeignMatterReplaceRecord.setOldMaterialCode(uninstallPartDetail.getCode());
                metalForeignMatterReplaceRecord.setOldMaterialName(uninstallPartDetail.getName());
                metalForeignMatterReplaceRecord.setQuantity(uninstallPartDetail.getQuantity());
                // 更新金属异物台账
                updateMetalForeignInfo(equipmentArchive, uninstallPartDetail.getLotSerialId(), null);
                // 更新车间库存
                updateWorkShopInventory(uninstallPartDetail.getInventoryId(),  null,null);

                Optional<EquipmentPartDetailMTO> optional = installPartDetailList.stream().filter(data -> data.getCode().equals(uninstallPartDetail.getCode())).findFirst();
                if (optional.isPresent()) {
                    EquipmentPartDetailMTO installPart = optional.get();
                    metalForeignMatterReplaceRecord.setNewLotSerialId(installPart.getLotSerialId());
                    metalForeignMatterReplaceRecord.setNewMaterialCode(installPart.getCode());
                    metalForeignMatterReplaceRecord.setNewMaterialName(installPart.getName());
                    metalForeignMatterReplaceRecord.setQuantity(installPart.getQuantity());
                    fabosJsonDao.mergeAndFlush(metalForeignMatterReplaceRecord);

                    // 更新金属异物台账
                    updateMetalForeignInfo(equipmentArchive, null, installPart.getLotSerialId());
                    // 更新车间库存
                    updateWorkShopInventory(null, installPart.getInventoryId() ,installPart.getQuantity());
                }
            });
        }

        if (CollectionUtils.isNotEmpty(installNewPartDetailList)) {
            installNewPartDetailList.forEach(newPart -> {
                MetalForeignMatterReplaceRecord metalForeignMatterReplaceRecord = new MetalForeignMatterReplaceRecord();
                metalForeignMatterReplaceRecord.setEquipmentArchive(equipmentArchive);
                metalForeignMatterReplaceRecord.setNewLotSerialId(newPart.getLotSerialId());
                metalForeignMatterReplaceRecord.setNewMaterialCode(newPart.getCode());
                metalForeignMatterReplaceRecord.setNewMaterialName(newPart.getName());
                metalForeignMatterReplaceRecord.setQuantity(newPart.getQuantity());
                fabosJsonDao.mergeAndFlush(metalForeignMatterReplaceRecord);
                // 更新金属异物台账
                updateMetalForeignInfo(equipmentArchive, null, newPart.getLotSerialId());
                // 更新车间库存
                updateWorkShopInventory(null, newPart.getInventoryId(), newPart.getQuantity());
            });
        }

    }

    /**
     * 更新金属异物台账
     */
    private void updateMetalForeignInfo(EquipmentArchive equipmentArchive, String lotSerialId,String installLotSerialId) {
        if (StringUtils.isNotEmpty(lotSerialId)) {
            MetalForeignMatterLedgerInformation infoCondition = new MetalForeignMatterLedgerInformation();
            infoCondition.setSerialLotId(lotSerialId);
            MetalForeignMatterLedgerInformation metalForeignMatterLedgerInformation = fabosJsonDao.selectOne(infoCondition);
            if (metalForeignMatterLedgerInformation != null) {
                metalForeignMatterLedgerInformation.setDevice(null);
                metalForeignMatterLedgerInformation.setBusinessStatus(BusinessStatusEnum.Enum.TO_BE_EVALUATED.name());
                fabosJsonDao.mergeAndFlush(metalForeignMatterLedgerInformation);

                // 推送消息到QMS
                SendMsgGroupDTO sendMsgGroupDTO = new SendMsgGroupDTO();
                sendMsgGroupDTO.setMessageGroupCode("MetalForeignInfo");
                sendMsgGroupDTO.setContent("金属异物台账已更新，备件编码为" + metalForeignMatterLedgerInformation.getMaterialCode());
                //sendMsgGroupDTO.setParameters("{\"a\":1}");
                ecsMessageProvider.sendGroupMessage(sendMsgGroupDTO);
            }
        }

        if (StringUtils.isNotEmpty(installLotSerialId)) {
            MetalForeignMatterLedgerInformation installCondition = new MetalForeignMatterLedgerInformation();
            installCondition.setSerialLotId(installLotSerialId);
            MetalForeignMatterLedgerInformation installMetalForeignMatterLedgerInformation = fabosJsonDao.selectOne(installCondition);
            if (installMetalForeignMatterLedgerInformation != null) {
                installMetalForeignMatterLedgerInformation.setDevice(equipmentArchive);
                installMetalForeignMatterLedgerInformation.setBusinessStatus(BusinessStatusEnum.Enum.IN_USE.name());
                fabosJsonDao.mergeAndFlush(installMetalForeignMatterLedgerInformation);

                // 推送消息到QMS
                SendMsgGroupDTO sendMsgGroupDTO = new SendMsgGroupDTO();
                sendMsgGroupDTO.setMessageGroupCode("MetalForeignInfo");
                sendMsgGroupDTO.setContent("金属异物台账已更新，备件编码为" + installMetalForeignMatterLedgerInformation.getMaterialCode());
                //sendMsgGroupDTO.setParameters("{\"a\":1}");
                ecsMessageProvider.sendGroupMessage(sendMsgGroupDTO);
            }
        }
    }

    /**
     * 更新车间库存
     */
    private void updateWorkShopInventory(String inventoryId,String installInventoryId,Double installQuantity) {
        if (StringUtils.isNotEmpty(inventoryId)) {
            WorkshopInventory uninstallWorkshopInventory = fabosJsonDao.findById(WorkshopInventory.class, inventoryId);
            if (uninstallWorkshopInventory != null) {
                uninstallWorkshopInventory.setBusinessStatus(BusinessStatusEnum.Enum.TO_BE_EVALUATED.name());
                fabosJsonDao.mergeAndFlush(uninstallWorkshopInventory);
            }
        }

        if (StringUtils.isNotEmpty(installInventoryId)) {
            WorkshopInventory workshopInventoryData = fabosJsonDao.getById(WorkshopInventory.class, installInventoryId);
            if (workshopInventoryData != null) {
                workshopInventoryData.setAccountingUnitQuantity(Math.max((workshopInventoryData.getAccountingUnitQuantity() - installQuantity), 0D));
                fabosJsonDao.mergeAndFlush(workshopInventoryData);

                WorkshopInventory installWorkshopInventory = new WorkshopInventory();
                BeanUtil.copyProperties(workshopInventoryData,installWorkshopInventory);
                installWorkshopInventory.setId(null);
                installWorkshopInventory.setAccountingUnitQuantity(installQuantity);
                installWorkshopInventory.setBusinessStatus(BusinessStatusEnum.Enum.IN_USE.name());
                fabosJsonDao.mergeAndFlush(installWorkshopInventory);
            }
        }
    }
}
