package cec.jiutian.bc.equipmentMainLubrManagement.domain.mainLubrPlanManagement.handler;

import cec.jiutian.bc.equipmentMainLubrManagement.domain.mainLubrPlanManagement.enumration.MlpEffectiveStatusEnum;
import cec.jiutian.bc.equipmentMainLubrManagement.domain.mainLubrPlanManagement.enumration.MlpManagementBusinessStatusEnum;
import cec.jiutian.bc.equipmentMainLubrManagement.domain.mainLubrPlanManagement.model.MainLubrPlanManagement;
import cec.jiutian.bc.equipmentMainLubrManagement.domain.mainLubrPlanManagement.mto.MainLubrPlanManagementCreateMTO;
import cec.jiutian.bc.flow.enums.ExamineStatusEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/6
 * @description
 */
@Component
public class MainLubrPlanManagementCreateHandler implements OperationHandler<MainLubrPlanManagement, MainLubrPlanManagementCreateMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<MainLubrPlanManagement> data, MainLubrPlanManagementCreateMTO modelObject, String[] param) {
        if (modelObject != null) {
            modelObject.setBusinessStatus(MlpManagementBusinessStatusEnum.Enum.PENDING_APPROVAL.name());
            modelObject.setEffectiveStatus(MlpEffectiveStatusEnum.Enum.UNAPPROVED.name());
            modelObject.setExamineStatus(ExamineStatusEnum.UNAUDITED.getCode());
            fabosJsonDao.mergeAndFlush(modelObject);
        } else {
            return "alert(数据异常)";
        }
        return "alert(操作成功)";
    }
}
