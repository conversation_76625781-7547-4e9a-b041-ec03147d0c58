package cec.jiutian.bc.equipmentMaintenance.domain.maintenanceRequest.proxy;

import cec.jiutian.bc.equipmentArchive.domain.equipmentArchive.model.EquipmentArchive;
import cec.jiutian.bc.equipmentArchive.domain.equipmentArchive.repository.EquipmentArchiveRepository;
import cec.jiutian.bc.equipmentMaintenance.domain.maintenanceRequest.model.MaintenanceRequest;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Component
public class MaintenanceRequestDataProxy implements DataProxy<MaintenanceRequest> {

    @Resource
    private EquipmentArchiveRepository equipmentRepository;

    @Override
    public void beforeAdd(MaintenanceRequest maintenanceRequest) {
        if (StringUtils.isBlank(maintenanceRequest.getDeviceNumber())) {
            throw new ServiceException("设备编号不能为空");
        }
        EquipmentArchive equipmentArchive = equipmentRepository.findByGeneralCode(maintenanceRequest.getDeviceNumber());
        if (equipmentArchive == null) {
            throw new ServiceException("设备不存在");
        }
    }

    @Override
    public void beforeUpdate(MaintenanceRequest maintenanceRequest) {
        if (StringUtils.isBlank(maintenanceRequest.getDeviceNumber())) {
            throw new ServiceException("设备编号不能为空");
        }
        EquipmentArchive equipmentArchive = equipmentRepository.findByGeneralCode(maintenanceRequest.getDeviceNumber());
        if (equipmentArchive == null) {
            throw new ServiceException("设备不存在");
        }
    }
}
