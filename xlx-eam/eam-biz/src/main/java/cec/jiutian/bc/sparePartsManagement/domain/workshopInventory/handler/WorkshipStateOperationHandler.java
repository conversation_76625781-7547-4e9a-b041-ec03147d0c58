package cec.jiutian.bc.sparePartsManagement.domain.workshopInventory.handler;

import cec.jiutian.bc.metalForeignMatter.enumeration.BusinessStatusEnum;
import cec.jiutian.bc.sparePartsManagement.domain.workshopInventory.model.WorkshopInventory;
import cec.jiutian.bc.sparePartsManagement.domain.workshopInventory.mto.WorkshopInventoryStateMTO;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import jakarta.persistence.TypedQuery;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/8
 * @description TODO
 */
@Component
public class WorkshipStateOperationHandler implements OperationHandler<WorkshopInventory, WorkshopInventoryStateMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<WorkshopInventory> data, WorkshopInventoryStateMTO modelObject, String[] param) {
        WorkshopInventory workshopInventory = data.get(0);
        if (BusinessStatusEnum.Enum.NORMAL.name().equals(modelObject.getBusinessStatus())) {
            String hql = "from WorkshopInventory  wi where wi.id != :id and wi.lotSerialId = :lotSerialId and wi.businessStatus = :businessStatus";
            TypedQuery<WorkshopInventory> query = fabosJsonDao.getEntityManager().createQuery(hql, WorkshopInventory.class);
            query.setParameter("id",modelObject.getId());
            query.setParameter("lotSerialId",modelObject.getLotSerialId());
            query.setParameter("businessStatus",BusinessStatusEnum.Enum.NORMAL.name());
            List<WorkshopInventory> workshopInventoryList = query.getResultList();
            if (CollectionUtils.isNotEmpty(workshopInventoryList)) {
                WorkshopInventory workshopInventoryData = workshopInventoryList.get(0);
                workshopInventoryData.setAccountingUnitQuantity(workshopInventoryData.getAccountingUnitQuantity() + modelObject.getAccountingUnitQuantity());
                fabosJsonDao.mergeAndFlush(workshopInventoryData);
                fabosJsonDao.deleteById(WorkshopInventory.class, modelObject.getId());
            }else {
                workshopInventory.setBusinessStatus(modelObject.getBusinessStatus());
                fabosJsonDao.mergeAndFlush(workshopInventory);
            }
        }else {
            workshopInventory.setBusinessStatus(modelObject.getBusinessStatus());
            fabosJsonDao.mergeAndFlush(workshopInventory);
        }
        return "alert(操作成功)";
    }

    @Override
    public WorkshopInventoryStateMTO fabosJsonFormValue(List<WorkshopInventory> data, WorkshopInventoryStateMTO fabosJsonForm, String[] param) {
        WorkshopInventoryStateMTO workshopInventoryStateMTO = fabosJsonDao.getById(WorkshopInventoryStateMTO.class, data.get(0).getId());
        return workshopInventoryStateMTO;
    }
}
