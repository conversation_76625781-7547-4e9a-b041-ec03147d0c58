package cec.jiutian.bc.equipmentMaintenance.remote.controller;

import cec.jiutian.bc.equipmentMaintenance.service.EquipmentMaintenanceService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/6/12
 * @description
 */
@RequestMapping("/equipmentMaintenanceController")
@RestController
public class EquipmentMaintenanceController {

    @Resource
    private EquipmentMaintenanceService equipmentMaintenanceService;

    @PostMapping("/getEquipmentFaultRate")
    public List<Map<String,Object>> getEquipmentFaultRate(@RequestBody Map<String, Object> params) {
        return equipmentMaintenanceService.getEquipmentFaultRate(params);
    }

    @PostMapping("/getEquipmentOEE")
    public List<Map<String,Object>> getEquipmentOEE(@RequestBody Map<String, Object> params) {
        return equipmentMaintenanceService.getEquipmentOEE(params);
    }
}