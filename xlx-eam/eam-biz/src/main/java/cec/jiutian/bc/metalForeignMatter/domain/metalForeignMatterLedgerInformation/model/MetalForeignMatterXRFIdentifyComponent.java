package cec.jiutian.bc.metalForeignMatter.domain.metalForeignMatterLedgerInformation.model;

import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.InputGroup;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;


@FabosJson(name = "XRF识别成分（%）",
        orderBy = "MetalForeignMatterXRFIdentifyComponent.createTime desc"
)
@Table(name = "eam_mf_metal_foreign_matter_xrf_idencomp")
@Entity
@Getter
@Setter
public class MetalForeignMatterXRFIdentifyComponent extends BaseModel {
    @ManyToOne
    @FabosJsonField(
            views = {
                    @View(title = "金属异物台账信息", column = "materialCodeName", show = false)
            },
            edit = @Edit(title = "金属异物台账信息", type = EditType.REFERENCE_TABLE, show = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "materialCodeName")
            )
    )
    @JsonIgnoreProperties("components")
    private MetalForeignMatterLedgerInformation metalForeignMatterLedgerInformation;

    @FabosJsonField(
            views = @View(title = "Cr"),
            edit = @Edit(title = "Cr",inputGroup = @InputGroup(postfix = "%"),
                    numberType = @NumberType(min = 0, max = 100, precision = 4))
    )
    private Double crContent;

    @FabosJsonField(
            views = @View(title = "Fe"),
            edit = @Edit(title = "Fe",inputGroup = @InputGroup(postfix = "%"),
                    numberType = @NumberType(min = 0, max = 100, precision = 4))
    )
    private Double feContent;

    @FabosJsonField(
            views = @View(title = "Ni"),
            edit = @Edit(title = "Ni",inputGroup = @InputGroup(postfix = "%"),
                    numberType = @NumberType(min = 0, max = 100, precision = 4))
    )
    private Double niContent;

    @FabosJsonField(
            views = @View(title = "Cu"),
            edit = @Edit(title = "Cu",inputGroup = @InputGroup(postfix = "%"),
                    numberType = @NumberType(min = 0, max = 100, precision = 4))
    )
    private Double cuContent;

    @FabosJsonField(
            views = @View(title = "Zn"),
            edit = @Edit(title = "Zn",inputGroup = @InputGroup(postfix = "%"),
                    numberType = @NumberType(min = 0, max = 100, precision = 4))
    )
    private Double znContent;

    @FabosJsonField(
            views = @View(title = "Mg"),
            edit = @Edit(title = "Mg",inputGroup = @InputGroup(postfix = "%"),
                    numberType = @NumberType(min = 0, max = 100, precision = 4))
    )
    private Double mgContent;

    @FabosJsonField(
            views = @View(title = "Al"),
            edit = @Edit(title = "Al",inputGroup = @InputGroup(postfix = "%"),
                    numberType = @NumberType(min = 0, max = 100, precision = 4))
    )
    private Double alContent;

    @FabosJsonField(
            views = @View(title = "Si"),
            edit = @Edit(title = "Si",inputGroup = @InputGroup(postfix = "%"),
                    numberType = @NumberType(min = 0, max = 100, precision = 4))
    )
    private Double siContent;

    @FabosJsonField(
            views = @View(title = "P"),
            edit = @Edit(title = "P",inputGroup = @InputGroup(postfix = "%"),
                    numberType = @NumberType(min = 0, max = 100, precision = 4))
    )
    private Double pContent;

    @FabosJsonField(
            views = @View(title = "S"),
            edit = @Edit(title = "S",inputGroup = @InputGroup(postfix = "%"),
                    numberType = @NumberType(min = 0, max = 100, precision = 4))
    )
    private Double sContent;

    @FabosJsonField(
            views = @View(title = "Cl"),
            edit = @Edit(title = "Cl",inputGroup = @InputGroup(postfix = "%"),
                    numberType = @NumberType(min = 0, max = 100, precision = 4))
    )
    private Double clContent;

    @FabosJsonField(
            views = @View(title = "K"),
            edit = @Edit(title = "K",inputGroup = @InputGroup(postfix = "%"),
                    numberType = @NumberType(min = 0, max = 100, precision = 4))
    )
    private Double kContent;

    @FabosJsonField(
            views = @View(title = "Ca"),
            edit = @Edit(title = "Ca",inputGroup = @InputGroup(postfix = "%"),
                    numberType = @NumberType(min = 0, max = 100, precision = 4))
    )
    private Double caContent;

    @FabosJsonField(
            views = @View(title = "Ti"),
            edit = @Edit(title = "Ti",inputGroup = @InputGroup(postfix = "%"),
                    numberType = @NumberType(min = 0, max = 100, precision = 4))
    )
    private Double tiContent;

    @FabosJsonField(
            views = @View(title = "V"),
            edit = @Edit(title = "V",inputGroup = @InputGroup(postfix = "%"),
                    numberType = @NumberType(min = 0, max = 100, precision = 4))
    )
    private Double vContent;

    @FabosJsonField(
            views = @View(title = "Mn"),
            edit = @Edit(title = "Mn",inputGroup = @InputGroup(postfix = "%"),
                    numberType = @NumberType(min = 0, max = 100, precision = 4))
    )
    private Double mnContent;

    @FabosJsonField(
            views = @View(title = "Co"),
            edit = @Edit(title = "Co",inputGroup = @InputGroup(postfix = "%"),
                    numberType = @NumberType(min = 0, max = 100, precision = 4))
    )
    private Double coContent;

    @FabosJsonField(
            views = @View(title = "As"),
            edit = @Edit(title = "As",inputGroup = @InputGroup(postfix = "%"),
                    numberType = @NumberType(min = 0, max = 100, precision = 4))
    )
    private Double asContent;

    @FabosJsonField(
            views = @View(title = "Se"),
            edit = @Edit(title = "Se", inputGroup = @InputGroup(postfix = "%"),
                    numberType = @NumberType(min = 0, max = 100, precision = 4))
    )
    private Double seContent;

    @FabosJsonField(
            views = @View(title = "Rb"),
            edit = @Edit(title = "Rb", inputGroup = @InputGroup(postfix = "%"),
                    numberType = @NumberType(min = 0, max = 100, precision = 4))
    )
    private Double rbContent;

    @FabosJsonField(
            views = @View(title = "Sr"),
            edit = @Edit(title = "Sr", inputGroup = @InputGroup(postfix = "%"),
                    numberType = @NumberType(min = 0, max = 100, precision = 4))
    )
    private Double srContent;

    @FabosJsonField(
            views = @View(title = "Zr"),
            edit = @Edit(title = "Zr", inputGroup = @InputGroup(postfix = "%"),
                    numberType = @NumberType(min = 0, max = 100, precision = 4))
    )
    private Double zrContent;

    @FabosJsonField(
            views = @View(title = "Nb"),
            edit = @Edit(title = "Nb", inputGroup = @InputGroup(postfix = "%"),
                    numberType = @NumberType(min = 0, max = 100, precision = 4))
    )
    private Double nbContent;

    @FabosJsonField(
            views = @View(title = "Mo"),
            edit = @Edit(title = "Mo", inputGroup = @InputGroup(postfix = "%"),
                    numberType = @NumberType(min = 0, max = 100, precision = 4))
    )
    private Double moContent;

    @FabosJsonField(
            views = @View(title = "Ru"),
            edit = @Edit(title = "Ru", inputGroup = @InputGroup(postfix = "%"),
                    numberType = @NumberType(min = 0, max = 100, precision = 4))
    )
    private Double ruContent;

    @FabosJsonField(
            views = @View(title = "Pd"),
            edit = @Edit(title = "Pd", inputGroup = @InputGroup(postfix = "%"),
                    numberType = @NumberType(min = 0, max = 100, precision = 4))
    )
    private Double pdContent;

    @FabosJsonField(
            views = @View(title = "Ag"),
            edit = @Edit(title = "Ag", inputGroup = @InputGroup(postfix = "%"),
                    numberType = @NumberType(min = 0, max = 100, precision = 4))
    )
    private Double agContent;

    @FabosJsonField(
            views = @View(title = "Cd"),
            edit = @Edit(title = "Cd", inputGroup = @InputGroup(postfix = "%"),
                    numberType = @NumberType(min = 0, max = 100, precision = 4))
    )
    private Double cdContent;

    @FabosJsonField(
            views = @View(title = "Sn"),
            edit = @Edit(title = "Sn", inputGroup = @InputGroup(postfix = "%"),
                    numberType = @NumberType(min = 0, max = 100, precision = 4))
    )
    private Double snContent;

    @FabosJsonField(
            views = @View(title = "Sb"),
            edit = @Edit(title = "Sb", inputGroup = @InputGroup(postfix = "%"),
                    numberType = @NumberType(min = 0, max = 100, precision = 4))
    )
    private Double sbContent;

    @FabosJsonField(
            views = @View(title = "Ba"),
            edit = @Edit(title = "Ba", inputGroup = @InputGroup(postfix = "%"),
                    numberType = @NumberType(min = 0, max = 100, precision = 4))
    )
    private Double baContent;

    @FabosJsonField(
            views = @View(title = "W"),
            edit = @Edit(title = "W", inputGroup = @InputGroup(postfix = "%"),
                    numberType = @NumberType(min = 0, max = 100, precision = 4))
    )
    private Double wContent;

    @FabosJsonField(
            views = @View(title = "Re"),
            edit = @Edit(title = "Re", inputGroup = @InputGroup(postfix = "%"),
                    numberType = @NumberType(min = 0, max = 100, precision = 4))
    )
    private Double reContent;

    @FabosJsonField(
            views = @View(title = "Au"),
            edit = @Edit(title = "Au", inputGroup = @InputGroup(postfix = "%"),
                    numberType = @NumberType(min = 0, max = 100, precision = 4))
    )
    private Double auContent;

    @FabosJsonField(
            views = @View(title = "Pb"),
            edit = @Edit(title = "Pb", inputGroup = @InputGroup(postfix = "%"),
                    numberType = @NumberType(min = 0, max = 100, precision = 4))
    )
    private Double pbContent;

    @FabosJsonField(
            views = @View(title = "Bi"),
            edit = @Edit(title = "Bi", inputGroup = @InputGroup(postfix = "%"),
                    numberType = @NumberType(min = 0, max = 100, precision = 4))
    )
    private Double biContent;

}
