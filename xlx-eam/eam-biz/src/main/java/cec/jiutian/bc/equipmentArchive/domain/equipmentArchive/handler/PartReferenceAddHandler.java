package cec.jiutian.bc.equipmentArchive.domain.equipmentArchive.handler;

import cec.jiutian.bc.equipmentArchive.domain.equipmentArchive.model.EquipmentComponentDetail;
import cec.jiutian.bc.equipmentArchive.domain.equipmentArchive.model.EquipmentPartDetail;
import cec.jiutian.bc.sparePartsManagement.domain.workshopInventory.model.WorkshopInventory;
import cec.jiutian.view.ReferenceAddType;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/4/8
 * @description TODO
 */
public class PartReferenceAddHandler implements ReferenceAddType.ReferenceAddHandler<EquipmentComponentDetail, WorkshopInventory> {
    @Override
    public Map<String, Object> handle(EquipmentComponentDetail equipmentComponentDetail, List<WorkshopInventory> workshopInventoryList) {
        Map<String, Object> result = new HashMap<>();
        List<EquipmentPartDetail> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(workshopInventoryList)) {
            workshopInventoryList.forEach(workshopInventory -> {
                EquipmentPartDetail equipmentPartDetail = new EquipmentPartDetail();
                equipmentPartDetail.setCode(workshopInventory.getMaterialCode());
                equipmentPartDetail.setName(workshopInventory.getMaterialName());
                equipmentPartDetail.setSpecification(workshopInventory.getMaterialSpecification());
                equipmentPartDetail.setInventoryId(workshopInventory.getId());
                equipmentPartDetail.setLotSerialId(workshopInventory.getLotSerialId());
                equipmentPartDetail.setSupplierLotSerialId(workshopInventory.getSupplierLotSerialId());
                equipmentPartDetail.setAvailableQuantity(workshopInventory.getAccountingUnitQuantity());
                list.add(equipmentPartDetail);
            });
        }
        result.put("partDetailList", list);
        return result;
    }
}
