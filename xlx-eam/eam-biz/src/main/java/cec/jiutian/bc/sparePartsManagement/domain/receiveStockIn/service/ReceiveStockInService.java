package cec.jiutian.bc.sparePartsManagement.domain.receiveStockIn.service;

import cec.jiutian.bc.enums.NamingRuleCodeEnum;
import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.sparePartsManagement.domain.materialRequisition.model.MaterialDetail;
import cec.jiutian.bc.sparePartsManagement.domain.materialRequisition.model.MaterialRequisition;
import cec.jiutian.bc.sparePartsManagement.domain.receiveStockIn.model.ReceiveStockIn;
import cec.jiutian.bc.sparePartsManagement.domain.receiveStockIn.mto.MaterialDetailConfirmMTO;
import cec.jiutian.bc.sparePartsManagement.enums.ReleaseSlipEnum;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class ReceiveStockInService {

    @Resource
    private NamingRuleService namingRuleService;

    @Resource
    private FabosJsonDao fabosJsonDao;

    public ReceiveStockIn createReleaseSlipByMR(MaterialRequisition materialRequisition) {
        if (materialRequisition == null) {
            throw new ServiceException("领料申请数据异常。");
        }
        ReceiveStockIn receiveStockIn = new ReceiveStockIn();
        receiveStockIn.setApplyCode(materialRequisition.getGeneralCode());
        receiveStockIn.setApplyType(materialRequisition.getApplyType());
        receiveStockIn.setFactoryAreaId(materialRequisition.getFactoryAreaId());
        receiveStockIn.setFactoryAreaName(materialRequisition.getFactoryAreaName());
        receiveStockIn.setApplierId(materialRequisition.getApplierId());
        receiveStockIn.setApplierName(materialRequisition.getApplierName());
        receiveStockIn.setStockOutCode(materialRequisition.getGeneralCode());
        receiveStockIn.setDepartmentId(materialRequisition.getDepartmentId());
        receiveStockIn.setDepartmentName(materialRequisition.getDepartmentName());
        String code = namingRuleService.getNameCode(NamingRuleCodeEnum.ReceiveStockIn.name(), 1, null).get(0);
        receiveStockIn.setGeneralCode(code);
        receiveStockIn.setBusinessStatus(ReleaseSlipEnum.Enum.DRAFT.name());
        fabosJsonDao.persist(receiveStockIn);
        List<MaterialDetail> detailList = materialRequisition.getMaterialDetailList();
        if (CollectionUtils.isNotEmpty(detailList)) {
            ArrayList<MaterialDetailConfirmMTO> mtos = new ArrayList<>(detailList.size());
            for (MaterialDetail materialDetail : detailList) {
                MaterialDetailConfirmMTO mto = fabosJsonDao.findById(MaterialDetailConfirmMTO.class, materialDetail.getId());
                mtos.add(mto);
            }
            receiveStockIn.setMaterialDetailList(mtos);
        }
        fabosJsonDao.mergeAndFlush(receiveStockIn);
        return receiveStockIn;
    }
}
