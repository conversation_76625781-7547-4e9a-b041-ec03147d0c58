package cec.jiutian.bc.equipmentMaintenance.domain.maintenancePreparation.model;

import cec.jiutian.bc.equipmentMaintenance.domain.copperrelatedApplication.model.CopperRelatedApplication;
import cec.jiutian.bc.equipmentMaintenance.domain.maintenancePreparation.handler.PTWPreparationHandler;
import cec.jiutian.bc.equipmentMaintenance.domain.maintenancePreparation.handler.ProtectConfirmOperationHandler;
import cec.jiutian.bc.equipmentMaintenance.domain.maintenancePreparation.handler.ProtectFileOperationHandler;
import cec.jiutian.bc.equipmentMaintenance.domain.maintenancePreparation.handler.StartMaintenanceTaskHandler;
import cec.jiutian.bc.equipmentMaintenance.domain.maintenancePreparation.mto.MaintenancePreparationMTO;
import cec.jiutian.bc.equipmentMaintenance.domain.maintenancePreparation.mto.MaintenancePreparationProtectConfirmMTO;
import cec.jiutian.bc.equipmentMaintenance.domain.maintenancePreparation.mto.MaintenancePreparationProtectMTO;
import cec.jiutian.bc.equipmentMaintenance.domain.maintenancePreparation.proxy.MaintenancePreparationDataProxy;
import cec.jiutian.bc.equipmentMaintenance.domain.permitToWork.model.PermitToWorkManagement;
import cec.jiutian.bc.equipmentMaintenance.enums.MRStatusEnum;
import cec.jiutian.bc.equipmentMaintenance.enums.RepairTypeEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.frame.module.MetaUser;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;

import java.util.Date;

@Getter
@Setter
@Entity
@Table(name = "eam_maintenance_request")
@FabosJson(
        name = "维修执行准备",
        dataProxy = MaintenancePreparationDataProxy.class,
        filter = @Filter("MaintenancePreparation.businessStatus in ('waitRepair','WaitProtectConfirm')"),
        orderBy = "approveTime desc",
        power = @Power(importable = false, print = false, add = false, edit = false, delete = false),
        rowOperation = {
                @RowOperation(
                        title = "准备作业许可证",
                        code = "MaintenancePreparation@LICENSES",
                        operationHandler = PTWPreparationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        type = RowOperation.Type.POPUP,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = MaintenancePreparationMTO.class,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        ifExpr = "businessStatus !='waitRepair'", //禁用按钮
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "MaintenancePreparation@LICENSES"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
                @RowOperation(
                        title = "上传防护准备附件",
                        code = "MaintenancePreparation@PROTECT",
                        operationHandler = ProtectFileOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        type = RowOperation.Type.POPUP,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = MaintenancePreparationProtectMTO.class,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        ifExpr = "businessStatus !='WaitProtectConfirm'", //禁用按钮
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "MaintenancePreparation@PROTECT"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
                @RowOperation(
                        title = "防护准备确认",
                        code = "MaintenancePreparation@PROTECTCONFIRM",
                        operationHandler = ProtectConfirmOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        type = RowOperation.Type.POPUP,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = MaintenancePreparationProtectConfirmMTO.class,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        ifExpr = "businessStatus !='WaitProtectConfirm'", //禁用按钮
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "MaintenancePreparation@PROTECTCONFIRM"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
                @RowOperation(
                        code = "MaintenancePreparation@START",
                        title = "开始维修",
                        mode = RowOperation.Mode.SINGLE,
                        ifExpr = "businessStatus != 'waitRepair'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        operationHandler = StartMaintenanceTaskHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "MaintenancePreparation@START"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                )
        }
)
public class MaintenancePreparation extends MetaModel {

    @FabosJsonField(
            views = @View(title = "需求单号"),
            edit = @Edit(title = "需求单号",
                    readonly = @Readonly(edit = true, add = true),
                    notNull = true,
                    search = @Search)
    )
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "设备编号"),
            edit = @Edit(title = "设备编号",
                    search = @Search(vague = true),
                    readonly = @Readonly(edit = true, add = true),
                    inputType = @InputType(length = 50)
            )
    )
    @Column(nullable = false, length = 50)
    private String deviceNumber;

    @FabosJsonField(
            views = @View(title = "设备名称"),
            edit = @Edit(title = "设备名称",
                    search = @Search(vague = true),
                    notNull = true,
                    inputType = @InputType(length = 50),
                    readonly = @Readonly(edit = true, add = true)
            )
    )
    @Column(nullable = false, length = 50)
    private String deviceName;


    @FabosJsonField(
            views = @View(title = "设备型号"),
            edit = @Edit(title = "设备型号",
                    readonly = @Readonly(edit = true, add = true)
            )
    )
    private String specification;

    @FabosJsonField(
            views = @View(title = "车间ID", show = false),
            edit = @Edit(title = "车间ID",
                    show = false
            )
    )
    private String factoryAreaId;

    @FabosJsonField(
            views = @View(title = "车间名称"),
            edit = @Edit(title = "车间名称",
                    readonly = @Readonly(add = true, edit = true),
                    search = @Search(vague = true)
            )
    )
    private String factoryAreaName;

    @FabosJsonField(
            views = @View(title = "产线Id", show = false),
            edit = @Edit(title = "产线Id",
                    show = false
            )
    )
    private String factoryLineId;

    @FabosJsonField(
            views = @View(title = "产线名称"),
            edit = @Edit(title = "产线名称",
                    search = @Search(vague = true),
                    readonly = @Readonly(add = true, edit = true)
            )
    )
    private String factoryLineName;

    @FabosJsonField(
            views = @View(title = "工序ID", show = false),
            edit = @Edit(title = "工序ID",
                    show = false
            )
    )
    private String processId;

    @FabosJsonField(
            views = @View(title = "工序名称"),
            edit = @Edit(title = "工序名称",
                    readonly = @Readonly(add = true, edit = true),
                    search = @Search(vague = true)
            )
    )
    private String processName;

    @FabosJsonField(
            views = @View(title = "异常照片"),
            edit = @Edit(title = "异常照片",
                    inputType = @InputType(length = 600),
                    type = EditType.ATTACHMENT,
                    tips = "支持jpg,png格式，图片大小小于5M，不超过10张",
                    attachmentType = @AttachmentType(fileTypes = {".jpg,.png"},
                            size = 5120,
                            maxLimit = 10,
                            type = AttachmentType.Type.IMAGE)
            )
    )
    @Column(length = 600)
    private String photos;

    @FabosJsonField(
            views = @View(title = "异常描述"),
            edit = @Edit(title = "异常描述",
                    inputType = @InputType(type = "text", length = 120),
                    type = EditType.TEXTAREA
            )
    )
    @Column(length = 120, columnDefinition = "text")
    private String description;


    @FabosJsonField(
            views = @View(title = "维修类型"),
            edit = @Edit(title = "维修类型",
                    search = @Search,
                    inputType = @InputType(length = 20),
                    readonly = @Readonly(edit = true, add = true),
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = RepairTypeEnum.class)
            )
    )
    private String repairType;

    @FabosJsonField(
            views = @View(title = "报修人"),
            edit = @Edit(title = "报修人",
                    search = @Search(vague = true),
                    notNull = true,
                    inputType = @InputType(length = 30)
            )
    )
    @Column(nullable = false, length = 30)
    private String reporter;

    @FabosJsonField(
            views = @View(title = "维修员ID", show = false),
            edit = @Edit(title = "维修员ID",
                    show = false,
                    readonly = @Readonly(add = true, edit = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "user", beFilledBy = "id"))
    )
    private String userId;

    @FabosJsonField(
            views = @View(title = "维修员姓名"),
            edit = @Edit(title = "维修员姓名",
                    readonly = @Readonly(add = true, edit = true),
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "user", beFilledBy = "name"))
    )
    private String userName;

    @FabosJsonField(
            views = @View(title = "是否需要安全作业许可证"),
            edit = @Edit(title = "是否需要安全作业许可证",
                    notNull = true,
                    type = EditType.BOOLEAN,
                    defaultVal = "true"
            ),
            dynamicField = @DynamicField(passive = true)
    )
    private Boolean needLicense;

    @OneToOne(cascade = CascadeType.DETACH)
    @JoinColumn(name = "ptw_id")
    @FabosJsonField(
            views = @View(title = "安全作业许可证", column = "generalCode"),
            edit = @Edit(title = "安全作业许可证",
                    type = EditType.REFERENCE_TABLE,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "needLicense == true"),
                    referenceTableType = @ReferenceTableType(label = "generalCode")
            )
    )
    private PermitToWorkManagement ptwManagement;

    @FabosJsonField(
            views = @View(title = "是否涉铜"),
            edit = @Edit(title = "是否涉铜",
                    notNull = true,
                    type = EditType.BOOLEAN,
                    defaultVal = "false"
            ),
            dynamicField = @DynamicField(passive = true)
    )
    private Boolean copper;

    @ManyToOne(cascade = CascadeType.DETACH)
    @JoinColumn(name = "copper_id")
    @FabosJsonField(
            views = @View(title = "涉铜许可证", column = "copperRelatedFormNumber"),
            edit = @Edit(title = "涉铜许可证",
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "copperRelatedFormNumber"),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "copper == true")
            )
    )
    private CopperRelatedApplication copperManagement;

    @FabosJsonField(
            views = @View(title = "是否涉磁"),
            edit = @Edit(title = "是否涉磁",
                    notNull = true,
                    type = EditType.BOOLEAN,
                    defaultVal = "false"
            ),
            dynamicField = @DynamicField(passive = true)
    )
    private Boolean magnetic;

    @FabosJsonField(
            views = @View(title = "单据状态"),
            edit = @Edit(title = "单据状态",
                    show = false,
                    notNull = true,
                    type = EditType.CHOICE,
                    defaultVal = "waitRepair",
                    choiceType = @ChoiceType(fetchHandler = MRStatusEnum.class)
            )
    )
    private String businessStatus;

    @FabosJsonField(
            views = @View(title = "创建时间"),
            edit = @Edit(title = "创建时间",
                    show = false,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @CreationTimestamp
    @Column(name = "report_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date reportTime;

    @FabosJsonField(
            views = @View(title = "审批时间"),
            edit = @Edit(title = "审批时间",
                    show = false,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date approveTime;

    @FabosJsonField(
            views = @View(title = "接单时间"),
            edit = @Edit(title = "接单时间",
                    show = false,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date receiveTime;

    @FabosJsonField(
            views = @View(title = "开始维修时间"),
            edit = @Edit(title = "开始维修时间",
                    show = false,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date startMaintenanceTime;

    @FabosJsonField(
            views = @View(title = "防护标记"),
            edit = @Edit(title = "防护标记",notNull = true,type = EditType.BOOLEAN, defaultVal = "false")
    )
    private Boolean protectFlag;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "防护确认人", column = "name", type = ViewType.TABLE_FORM),
            edit = @Edit(title = "防护确认人",
                    filter = @Filter(value = "MetaUser.state = 'Y'"),
                    notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "protectFlag == true"),
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"))
    )
    private MetaUser protectConfirmPerson;

    @FabosJsonField(
            views = @View(title = "防护准备完成附件"),
            edit = @Edit(title = "防护准备完成附件",notNull = true,
                    type = EditType.ATTACHMENT,
            attachmentType = @AttachmentType(maxLimit = 100),
            dependFieldDisplay = @DependFieldDisplay(showOrHide = "protectFlag == true"))
    )
    private String protectFile;


    //是否超时
    @Column(nullable = true)
    private Boolean overTimeNote;


}
