package cec.jiutian.bc.sparePartsManagement.domain.workshopReturnRequest.handler;

import cec.jiutian.bc.sparePartsManagement.domain.workshopReturnRequest.model.WorkshopReturnRequest;
import cec.jiutian.bc.sparePartsManagement.domain.workshopReturnRequest.model.WorkshopReturnRequestApproval;
import cec.jiutian.bc.sparePartsManagement.enums.WorkshopReturnRequestStatusEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class WorkshopReturnRequestRejectHandler implements OperationHandler<WorkshopReturnRequestApproval, Void> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<WorkshopReturnRequestApproval> data, Void modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            WorkshopReturnRequest entity = fabosJsonDao.findById(WorkshopReturnRequest.class, data.get(0).getId());
            entity.setBusinessStatus(WorkshopReturnRequestStatusEnum.Enum.REJECT.name());

            fabosJsonDao.mergeAndFlush(entity);
        }
        return "提交成功";
    }
}
