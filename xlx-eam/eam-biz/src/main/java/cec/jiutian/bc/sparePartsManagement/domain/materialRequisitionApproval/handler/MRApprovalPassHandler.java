package cec.jiutian.bc.sparePartsManagement.domain.materialRequisitionApproval.handler;

import cec.jiutian.bc.sparePartsManagement.domain.materialRequisition.model.MaterialDetail;
import cec.jiutian.bc.sparePartsManagement.domain.materialRequisition.model.MaterialRequisition;
import cec.jiutian.bc.sparePartsManagement.domain.materialRequisitionApproval.model.MaterialRequisitionApproval;
import cec.jiutian.bc.sparePartsManagement.domain.releaseSlip.model.ReleaseSlip;
import cec.jiutian.bc.sparePartsManagement.domain.releaseSlip.service.ReleaseSlipService;
import cec.jiutian.bc.sparePartsManagement.enums.MaterialRequisitionStatusEnum;
import cec.jiutian.bc.sparePartsManagement.port.client.MaterialRequisitionFeignClient;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.common.util.JacksonUtil;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Component
public class MRApprovalPassHandler implements OperationHandler<MaterialRequisitionApproval, MaterialRequisitionApproval> {

    private static final Logger log = LoggerFactory.getLogger(MRApprovalPassHandler.class);
    @Resource
    private FabosJsonDao fabosJsonDao;


    @Resource
    private ReleaseSlipService releaseSlipService;

    @Resource
    private MaterialRequisitionFeignClient materialRequisitionFeignClient;

    @Override
    @Transactional
    public String exec(List<MaterialRequisitionApproval> data, MaterialRequisitionApproval modelObject, String[] param) {
        if (CollectionUtils.isEmpty(data) || data.get(0).getId() == null) {
            throw new ServiceException("数据异常");
        }
        MaterialRequisitionApproval materialRequisitionApproval = data.get(0);
        //创建放行条
        MaterialRequisition materialRequisition = fabosJsonDao.findById(MaterialRequisition.class, materialRequisitionApproval.getId());
        ReleaseSlip releaseSlip = releaseSlipService.createReleaseSlipByMRService(materialRequisition);
        materialRequisition.setReleaseSlip(releaseSlip);
        materialRequisition.setBusinessStatus(MaterialRequisitionStatusEnum.Enum.IN_PROGRESS.name());
        materialRequisition.setExaminePersonId(UserContext.getUserId());
        materialRequisition.setExaminePersonName(UserContext.getUserName());
        materialRequisition.setBusinessStatus(MaterialRequisitionStatusEnum.Enum.IN_PROGRESS.name());
       String stockOutCode = sendApplyToWms(materialRequisition);
        materialRequisition.setStockOutCode(stockOutCode);
        fabosJsonDao.mergeAndFlush(materialRequisition);
        return "提交成功";
    }

    private String sendApplyToWms(MaterialRequisition materialRequisition) {
        List<MaterialDetail> materialDetailList = materialRequisition.getMaterialDetailList();
        if (CollectionUtils.isEmpty(materialDetailList)) {
            throw new ServiceException("物料申请明细不能为空");
        }
        HashMap<String, Object> args = new HashMap<>();
        args.put("org_id", materialRequisition.getDepartmentId());
        args.put("sourceTaskNumber", materialRequisition.getGeneralCode());

        ArrayList<HashMap<String, Object>> details = new ArrayList<>(materialDetailList.size());
        for (MaterialDetail materialDetail : materialDetailList) {
            HashMap<String, Object> detail = new HashMap<>();
            detail.put("serialLotId", materialDetail.getLotSerialId());
            detail.put("inventoryId", materialDetail.getInventoryId());
            detail.put("materialId", materialDetail.getMaterialId());
            detail.put("materialCode", materialDetail.getMaterialCode());
            detail.put("materialName", materialDetail.getMaterialName());
            detail.put("measureUnit", materialDetail.getAccountingUnit());
            detail.put("materialSpecification", materialDetail.getMaterialSpecification());
            detail.put("requestQuantity", materialDetail.getApplyQuantity());
            details.add(detail);
        }
        args.put("details", details);
        try {
            String json = JacksonUtil.toJson(args);
            log.info("创建wms出库单参数：{}", json);
            return materialRequisitionFeignClient.createOtherStockOutApply(args);
        } catch (Exception e) {
            log.error("创建wms出库单报错：", e);
            throw new ServiceException("创建wms出库单报错：" + e.getMessage());
        }
    }
}
