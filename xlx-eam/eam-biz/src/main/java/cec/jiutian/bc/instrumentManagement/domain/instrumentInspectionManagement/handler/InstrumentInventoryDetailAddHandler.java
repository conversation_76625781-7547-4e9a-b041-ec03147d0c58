package cec.jiutian.bc.instrumentManagement.domain.instrumentInspectionManagement.handler;

import cec.jiutian.bc.instrumentManagement.domain.instrumentInspectionManagement.model.InstrumentInspectionManagement;
import cec.jiutian.bc.instrumentManagement.domain.instrumentInspectionManagement.model.InstrumentInventoryDetail;
import cec.jiutian.bc.instrumentManagement.domain.instrumentInventory.model.InstrumentInventory;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.view.ReferenceAddType;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/4/22
 * @description TODO
 */
@Component
public class InstrumentInventoryDetailAddHandler implements ReferenceAddType.ReferenceAddHandler<InstrumentInspectionManagement, InstrumentInventory> {
    public Map<String, Object> handle(InstrumentInspectionManagement countingTaskWithMaterialView, List<InstrumentInventory> instrumentInventories) {
        if (CollectionUtils.isEmpty(instrumentInventories)) {
            throw new FabosJsonApiErrorTip("送检批次不能为空");
        }
        ArrayList<InstrumentInventoryDetail> inventoryDetails = new ArrayList<>(instrumentInventories.size());
        Map<String, Object> result = new HashMap<>();
        for (InstrumentInventory instrumentInventory : instrumentInventories) {
            inventoryDetails.add(InstrumentInventoryDetail.createByInventoryLedger(instrumentInventory));
        }
        result.put("instrumentInventoryList", inventoryDetails);
        return result;
    }
}
