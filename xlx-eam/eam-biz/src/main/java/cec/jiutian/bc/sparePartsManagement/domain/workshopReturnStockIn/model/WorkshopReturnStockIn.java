package cec.jiutian.bc.sparePartsManagement.domain.workshopReturnStockIn.model;

import cec.jiutian.bc.enums.NamingRuleCodeEnum;
import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleBaseModel;
import cec.jiutian.bc.sparePartsManagement.domain.workshopReturnRequest.model.WorkshopReturnRequest;
import cec.jiutian.bc.sparePartsManagement.domain.workshopReturnStockIn.handler.WorkshopReturnStockInCompleteOperationHandler;
import cec.jiutian.bc.sparePartsManagement.domain.workshopReturnStockIn.handler.WorkshopReturnStockInDetailDynamicHandler;
import cec.jiutian.bc.sparePartsManagement.domain.workshopReturnStockIn.handler.WorkshopReturnStockInReleaseOperationHandler;
import cec.jiutian.bc.sparePartsManagement.domain.workshopReturnStockIn.proxy.WorkshopReturnStockInProxy;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.ReferenceGenerateType;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowBaseOperation;
import cec.jiutian.view.type.RowOperation;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OrderBy;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@FabosJson(
        name = "车间退料入库",
        orderBy = "createTime desc",
        dataProxy = WorkshopReturnStockInProxy.class,
        power = @Power(importable = false, export = false),
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "currentState!='EDIT'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "currentState!='EDIT'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
        },
        rowOperation = {
                @RowOperation(
                        title = "发布",
                        code = "WorkshopReturnStockIn@RELEASE",
                        operationHandler = WorkshopReturnStockInReleaseOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "是否确定操作？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "WorkshopReturnStockIn@RELEASE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "currentState != 'EDIT'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE
                ),
                @RowOperation(
                        title = "完成",
                        code = "WorkshopReturnStockIn@COMPLETE",
                        operationHandler = WorkshopReturnStockInCompleteOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "是否确定操作？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "WorkshopReturnStockIn@COMPLETE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "currentState != 'EXECUTE'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE
                )
        }
)
@Table(name = "spm_workshop_return_stock_in")
@Entity
@Getter
@Setter
@TemplateType(type = "multiTable")
public class WorkshopReturnStockIn extends NamingRuleBaseModel {

    @Override
    public String getNamingCode() {
        return NamingRuleCodeEnum.WorkshopReturnStockIn.name();
    }

    @FabosJsonField(
            views = @View(title = "申请单号", column = "generalCode"),
            edit = @Edit(title = "申请单号",
                    readonly = @Readonly(add = false),
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode"),
                    filter = @Filter(value = "WorkshopReturnRequest.businessStatus = 'AWAIT_STOCK_IN'")
            )
    )
    @ManyToOne
    private WorkshopReturnRequest request;

    @FabosJsonField(
            views = @View(title = "申请部门id", show = false),
            edit = @Edit(title = "申请部门id", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "request", beFilledBy = "departmentId"))
    )
    private String departmentId;

    @FabosJsonField(
            views = @View(title = "申请部门"),
            edit = @Edit(title = "申请部门",
                    search = @Search(vague = true),
                    readonly = @Readonly()
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "request", beFilledBy = "departmentName"))
    )
    private String departmentName;

    @FabosJsonField(
            views = @View(title = "申请人ID", show = false),
            edit = @Edit(title = "申请人ID", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "request", beFilledBy = "applierId"))
    )
    @Column(length = 50)
    private String applierId;

    @FabosJsonField(
            views = @View(title = "申请人"),
            edit = @Edit(title = "申请人",
                    readonly = @Readonly(),
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "request", beFilledBy = "applierName"))
    )
    @Column(length = 30)
    private String applierName;

    @FabosJsonField(
            views = @View(title = "仓库名称"),
            edit = @Edit(title = "仓库名称", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "request", beFilledBy = "warehouseName"))
    )
    private String warehouseName;

    @FabosJsonField(
            views = @View(title = "仓库ID", show = false),
            edit = @Edit(title = "仓库ID", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "request", beFilledBy = "warehouseId"))
    )
    private Long warehouseId;

    @FabosJsonField(
            views = @View(title = "当前状态"),
            edit = @Edit(title = "当前状态", show = false,
                    search = @Search(),
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = OrderCurrentStateEnum.class)
            )
    )
    private String currentState;

    @FabosJsonField(
            views = @View(title = "备注"),
            edit = @Edit(title = "备注", type = EditType.TEXTAREA)
    )
    private String remark;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.EAGER)
    @JoinColumn(name = "stock_in_id")
    @OrderBy
    @FabosJsonField(
            edit = @Edit(title = "入库明细", type = EditType.TAB_REFERENCE_GENERATE),
            views = @View(title = "入库明细", type = ViewType.TABLE_VIEW),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = {"request"}, dynamicHandler = WorkshopReturnStockInDetailDynamicHandler.class)),
            referenceGenerateType = @ReferenceGenerateType(editable = {"stockInQuantity", "warehouseBlock", "warehouseShelf", "remark"})
    )
    private List<WorkshopReturnStockInDetail> details;

}
