package cec.jiutian.bc.equipmentMaintenance.domain.maintenanceTaskManage.handler;

import cec.jiutian.bc.ecs.dto.SendMsgToPersonDTO;
import cec.jiutian.bc.ecs.enums.MessageWayEnum;
import cec.jiutian.bc.equipmentArchive.domain.equipmentArchive.repository.EquipmentArchiveRepository;
import cec.jiutian.bc.equipmentArchive.enumeration.EquipmentBusinessStateEnum;
import cec.jiutian.bc.equipmentMaintenance.domain.maintenanceTaskManage.model.MaintenanceTaskManage;
import cec.jiutian.bc.equipmentMaintenance.domain.maintenanceTaskManage.mto.DistributeMaintenanceTaskMTO;
import cec.jiutian.bc.equipmentMaintenance.enums.MRStatusEnum;
import cec.jiutian.bc.mto.UserMTO;
import cec.jiutian.bc.utils.MsgUtil;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import jakarta.persistence.LockModeType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashSet;
import java.util.List;

@Slf4j
@Component
public class DistributeMaintenanceTaskHandler implements OperationHandler<MaintenanceTaskManage, DistributeMaintenanceTaskMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private EquipmentArchiveRepository equipmentArchiveRepository;


    @Override
    @Transactional
    public String exec(List<MaintenanceTaskManage> data, DistributeMaintenanceTaskMTO modelObject, String[] param) {
        if (modelObject == null || StringUtils.isBlank(modelObject.getUserId())) {
            throw new ServiceException("数据异常");
        }

        MaintenanceTaskManage taskManage =fabosJsonDao.getEntityManager().find(MaintenanceTaskManage.class, modelObject.getId(), LockModeType.PESSIMISTIC_WRITE);
        if (taskManage.getUserId() != null || taskManage.getUserName() != null) {
            throw new ServiceException("该维修任务已被领取/分配");
        }
        if (taskManage.getProtectFlag()) {
            taskManage.setBusinessStatus(MRStatusEnum.Enum.WaitProtectConfirm.name());
        }else {
            taskManage.setBusinessStatus(MRStatusEnum.Enum.waitRepair.name());
        }
        taskManage.setUserId(modelObject.getUserId());
        taskManage.setUserName(modelObject.getUserName());
        taskManage.setReceiveTime(new Date());
        fabosJsonDao.mergeAndFlush(taskManage);
        int i = equipmentArchiveRepository.updateState(EquipmentBusinessStateEnum.Enum.Repairing.name(),
                taskManage.getDeviceNumber());
        if (i < 1) {
//            throw new ServiceException("操作失败:更新设备状态失败");
            log.error("更新设备状态失败:{}", taskManage.getDeviceNumber());
        }
        return sendMessageToReporter(modelObject);
    }

    @Override
    public DistributeMaintenanceTaskMTO fabosJsonFormValue(List<MaintenanceTaskManage> data,
                                                           DistributeMaintenanceTaskMTO fabosJsonForm, String[] param) {
        if (CollectionUtils.isEmpty(data)) {
            throw new ServiceException("请选择操作数据");
        }
        MaintenanceTaskManage maintenanceTaskManage = data.get(0);
        fabosJsonForm = new DistributeMaintenanceTaskMTO();
        BeanUtils.copyProperties(maintenanceTaskManage, fabosJsonForm);
        return fabosJsonForm;
    }


    //接单的信息需要反馈给维修需求的申请人
    private String sendMessageToReporter(DistributeMaintenanceTaskMTO distributeMaintenanceTaskMTO) {
        String reporterId = distributeMaintenanceTaskMTO.getReporterId();
        UserMTO user = fabosJsonDao.findById(UserMTO.class, reporterId);
        HashSet<String> phone = new HashSet<>();
        phone.add(user.getPhoneNumber());
        SendMsgToPersonDTO sendMsgToPersonDTO = new SendMsgToPersonDTO();
        sendMsgToPersonDTO.setTitle("维修需求进度通知");

        sendMsgToPersonDTO.setContent("您提交的维修需求已被：" + distributeMaintenanceTaskMTO.getUserName() + "接单");
        sendMsgToPersonDTO.setReceivers(phone);
        sendMsgToPersonDTO.setSendBy("QMS系统消息");
        try {
            sendMsgToPersonDTO.setWay(MessageWayEnum.App);
            MsgUtil.sendPersonMessage(sendMsgToPersonDTO);
            sendMsgToPersonDTO.setWay(MessageWayEnum.WeChat);
            MsgUtil.sendPersonMessage(sendMsgToPersonDTO);
            return "操作成功";
        } catch (Exception e) {
            return "alert('任务已被领取/分配，通知需求提交人失败')";
        }
    }
}
