package cec.jiutian.bc.sparePartsManagement.domain.lineStockOut.handler;

import cec.jiutian.bc.sparePartsManagement.domain.inventoryLedger.model.InventoryLedger;
import cec.jiutian.bc.sparePartsManagement.domain.lineStockOut.model.LineStockOut;
import cec.jiutian.bc.sparePartsManagement.domain.lineStockOut.model.LineStockOutDetail;
import cec.jiutian.view.ReferenceAddType;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/3/31
 * @description TODO
 */
@Component
public class OutLineInventoryReferenceAddHandler implements ReferenceAddType.ReferenceAddHandler<LineStockOut, InventoryLedger> {
    @Override
    public Map<String, Object> handle(LineStockOut lineStockOut, List<InventoryLedger> inventoryLedgerList) {
        Map<String, Object> result = new HashMap<>();
        List<LineStockOutDetail> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(inventoryLedgerList)) {
            inventoryLedgerList.forEach(inventoryLedger -> {
                LineStockOutDetail lineStockOutDetail = new LineStockOutDetail();
                lineStockOutDetail.setInventoryId(inventoryLedger.getId());
                lineStockOutDetail.setMaterialName(inventoryLedger.getMaterialName());
                lineStockOutDetail.setMaterialCode(inventoryLedger.getMaterialCode());
                lineStockOutDetail.setMaterialSpecification(inventoryLedger.getMaterialSpecification());
                lineStockOutDetail.setAccountingUnit(inventoryLedger.getAccountingUnit());
                lineStockOutDetail.setAvailableQuantity(inventoryLedger.getAvailableQuantity());
                lineStockOutDetail.setLotSerialId(inventoryLedger.getLotSerialId());
                lineStockOutDetail.setSupplierLotSerialId(inventoryLedger.getSupplierLotSerialId());
                lineStockOutDetail.setSupplierId(inventoryLedger.getSupplierId());
                lineStockOutDetail.setSupplierName(inventoryLedger.getSupplierName());
                lineStockOutDetail.setInventoryLotId(inventoryLedger.getInventoryLotId());
                lineStockOutDetail.setMaterialType(inventoryLedger.getMaterialType());
                list.add(lineStockOutDetail);
            });
        }
        result.put("detailList", list);
        return result;
    }
}
