package cec.jiutian.bc.toolManagement.domain.personalToolLedger.model;

import cec.jiutian.bc.mto.UserMTO;
import cec.jiutian.bc.toolManagement.enums.BorrowSourceStatusEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.*;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "eam_tm_personal_tool_ledger")
@FabosJson(
        name = "个人工具台账-归还",
        orderBy = "PersonalToolLedgerReturn.createTime desc"
)
public class PersonalToolLedgerReturn extends MetaModel {

    //归还人
    @FabosJsonField(
            views = @View(title = "归还人"),
            edit = @Edit(
                    title = "归还人",
                    inputType = @InputType(length = 20),
                    readonly = @Readonly(add = true, edit = true),
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "returnPerson", beFilledBy = "name"))
    )
    @Column(length = 20)
    private String returnName;

    @FabosJsonField(
            views = @View(title = "归还人ID", show = false),
            edit = @Edit(
                    show = false,
                    title = "归还人ID",
                    inputType = @InputType(length = 50)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "returnPerson", beFilledBy = "id"))
    )
    @Column(length = 50)
    private String returnId;


    @Transient
    @FabosJsonField(
            edit = @Edit(title = "请填写归还数量", type = EditType.DIVIDE)
    )
    private String divide;


    @FabosJsonField(
            views = @View(title = "物料id", show = false),
            edit = @Edit(title = "物料id", show = false)
    )
    private String materialId;

    // 物料编码
    @FabosJsonField(
            views = @View(title = "物料编码"),
            edit = @Edit(title = "物料编码", readonly = @Readonly())
    )
    private String materialCode;

    // 物料名称
    @FabosJsonField(
            views = @View(title = "物料名称"),
            edit = @Edit(title = "物料名称", readonly = @Readonly())
    )
    private String materialName;

    // 本厂批次号
    @FabosJsonField(
            views = @View(title = "本厂批号"),
            edit = @Edit(title = "本厂批号", readonly = @Readonly())
    )
    private String lotSerialId;

    // 可用数量
    @FabosJsonField(
            views = @View(title = "可用数量"),
            edit = @Edit(title = "可用数量", readonly = @Readonly())
    )
    private Double availableQuantity;

    // 单位
    @FabosJsonField(
            views = @View(title = "单位"),
            edit = @Edit(title = "单位", readonly = @Readonly())
    )
    private String accountingUnit;

    // 归还数量
    @FabosJsonField(
            views = @View(title = "归还数量", rowEdit = true, width = "100px"),
            edit = @Edit(title = "归还数量",
                    notNull = true,
                    numberType = @NumberType(min = 0, maxExpr = "${availableQuantity}", precision = 2))
    )
    private Double returnQuantity;

    // 借用的来源
    @FabosJsonField(
            views = @View(title = "借用的来源", show = false),
            edit = @Edit(title = "借用的来源", inputType = @InputType(length = 20),
                    type = EditType.CHOICE, show = false,
                    choiceType = @ChoiceType(fetchHandler = BorrowSourceStatusEnum.class)
            )
    )
    @Column(length = 20)
    private String borrowSource;

    // 借用的来源id
    @FabosJsonField(
            views = @View(title = "借用的来源id", show = false),
            edit = @Edit(title = "借用的来源id", show = false)
    )
    private String borrowSourceId;
}
