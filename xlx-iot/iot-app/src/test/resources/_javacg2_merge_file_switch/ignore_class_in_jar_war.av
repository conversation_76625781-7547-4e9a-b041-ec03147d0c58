## （作用）在 合并需要解析的目录或jar/war文件时 使用的表达式
## （作用）若当前配置文件中的表达式执行结果为 true，则跳过合并（及解析）对应的文件
## （作用）若表达式执行结果为 false，或未指定表达式，则当前配置不会跳过对应的文件
## （范围）指定是否跳过合并jar/war中的class文件
## （表达式使用示例文件）请参考 el_example.md
## （允许使用的变量）{变量名称} {变量类型} {变量描述} {变量值示例}
## {file_path} {String} {jar/war文件中的文件相对路径, 相对根目录的路径, 以斜杠/为分隔符，不以分隔符开头} {a/b/c.jar, a/b/c.xml}
## {file_dir_path} {String} {jar/war文件中的文件所在目录相对路径, 相对根目录的路径, 以斜杠/为分隔符，不以分隔符开头或结尾} {a/b, a/b}
## {file_name} {String} {文件名称} {a.class, a.xml}
## {class_file_path} {String} {jar/war文件中的class文件的相对路径, 相对根目录，或WEB-INF/classes、BOOT-INF/classes目录的路径, 以斜杠/为分隔符，不以分隔符开头} {a/b/c.class}
