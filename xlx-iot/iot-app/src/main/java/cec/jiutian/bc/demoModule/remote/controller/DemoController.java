package cec.jiutian.bc.demoModule.remote.controller;

//import cec.jiutian.api.remoteCallLog.model.RemoteCallLog;
//import cec.jiutian.api.remoteCallLog.port.mq.RemoteCallLogSender;
import cec.jiutian.bc.alarmSetting.service.AlarmSettingService;
import cec.jiutian.core.frame.constant.FabosJsonRestPath;
import cec.jiutian.core.view.fabosJson.view.FabosJsonApiModel;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(FabosJsonRestPath.FABOS_API)
public class DemoController {


    private final AlarmSettingService alarmSettingService;

    public DemoController(AlarmSettingService alarmSettingService) {
        this.alarmSettingService = alarmSettingService;
    }

    @GetMapping("/checkAlarm")
    public void checkAlarm(){
        alarmSettingService.checkAlarm();
    }

//    private final RemoteCallLogSender remoteCallLogSender;
//
//    public DemoController(RemoteCallLogSender remoteCallLogSender) {
//        this.remoteCallLogSender = remoteCallLogSender;
//    }
//
//    @PostMapping("/sendRemoteCallLogMessage")
//    public FabosJsonApiModel sendRemoteCallLog(@RequestBody RemoteCallLog remoteCallLog) {
//        remoteCallLogSender.sendRemoteCallLogMessage(remoteCallLog);
//        return FabosJsonApiModel.successApi("success");
//    }

    @PostMapping("/demo0520")
    public FabosJsonApiModel demo0520(@RequestBody String request) {
        // 处理请求
        System.out.println("Received request: " + request);
        return FabosJsonApiModel.successApi("success");
    }
}
