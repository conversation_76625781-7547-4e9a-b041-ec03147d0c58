package cec.jiutian.bc.alarmSetting.service;

import cec.jiutian.bc.alarmManage.dto.AlarmSettingDTO;
import cec.jiutian.bc.alarmRule.domain.model.AlarmRule;
import cec.jiutian.bc.alarmRule.domain.model.AlarmRuleCondition;
import cec.jiutian.bc.alarmRule.domain.model.AlarmRuleExecute;
import cec.jiutian.bc.alarmRule.domain.model.AlarmRuleExecuteParam;
import cec.jiutian.bc.alarmSetting.domain.dto.*;
import cec.jiutian.bc.alarmSetting.domain.model.AlarmRecord;
import cec.jiutian.bc.alarmSetting.domain.model.AlarmSetting;
import cec.jiutian.bc.alarmSetting.execel.ExcelDataListener;
import cec.jiutian.bc.alarmSetting.prop.PSpaceProp;
import cec.jiutian.bc.enums.*;
import cec.jiutian.bc.equipmentArchive.domain.equipmentArchive.model.EquipmentArchive;
import cec.jiutian.bc.equipmentArchive.domain.equipmentArchive.model.EquipmentPoint;
import cec.jiutian.bc.utils.PSpaceDataUtil;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.persistence.Transient;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/28
 * @description TODO
 */
@Slf4j
@Service
public class AlarmSettingService {

    private final FabosJsonDao fabosJsonDao;

    private final PSpaceProp pSpaceProp;

    public AlarmSettingService(FabosJsonDao fabosJsonDao, PSpaceProp pSpaceProp) {
        this.fabosJsonDao = fabosJsonDao;
        this.pSpaceProp = pSpaceProp;
    }

    public String addSetting(AlarmRule alarmRule, AlarmSettingDTO alarmSettingDTO) {
        AlarmSetting alarmSetting=new AlarmSetting();
        BeanUtils.copyProperties(alarmSettingDTO,alarmSetting);
        alarmSetting.setEquipmentArchive(getEquipmentArchiveCode(alarmSettingDTO.getEquipmentArchiveCode()));
        alarmSetting.setAlarmRule(alarmRule);
        alarmSetting.setState(AlarmSettingStatusEnum.Enum.enable.name());
        fabosJsonDao.insert(alarmSetting);
        return alarmSetting.getId();
    }

    public EquipmentArchive getEquipmentArchiveCode(String equipmentArchiveCode){
        EquipmentArchive equipmentArchive = new EquipmentArchive();
        equipmentArchive.setGeneralCode(equipmentArchiveCode);
        return fabosJsonDao.selectOne(equipmentArchive);
    }

    public boolean deleteSetting(String gid) {
        return fabosJsonDao.deleteById(AlarmRule.class,gid)>0;
    }

    public boolean checkAlarm(){
        AlarmSetting setting=new AlarmSetting();
        setting.setState(AlarmSettingStatusEnum.Enum.enable.name());
        List<AlarmSetting> alarmSettingList= fabosJsonDao.select(setting);
        if(CollectionUtils.isEmpty(alarmSettingList)){
            log.info("未发现报警设置!");
            return false;
        }
        Date nowDate=DateUtil.date();
        alarmSettingList = alarmSettingList.stream().filter(it->checkAlarmTime(nowDate,it)).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(alarmSettingList)){
            log.info("未发现满足报警时间的报警设置!,nowDate={}",nowDate);
            return false;
        }
        Date lastExecuteTime;
        AlarmSetting lastAlarm = alarmSettingList.stream().filter(it->it.getLastExecuteTime()!=null).sorted(Comparator.comparing(AlarmSetting::getLastExecuteTime).reversed()).findFirst().orElse(null);
        if(lastAlarm==null){
            //第一次执行只设置开始时间
            lastExecuteTime=new Date();
            alarmSettingList.forEach(it->it.setLastExecuteTime(lastExecuteTime));
            fabosJsonDao.updateBatchById(alarmSettingList);
            return false;
        } else {
            lastExecuteTime = lastAlarm.getLastExecuteTime();
        }
        Set<String> pointList=new HashSet<>();
        alarmSettingList.forEach(alarm->{
            List<String> equipmentArchiveList = alarm.getAlarmRule().getConditionList().stream().filter(it->it.getEquipmentPoint()!=null).map(it->it.getEquipmentPoint().getName()).collect(Collectors.toList());
            pointList.addAll(equipmentArchiveList);
        });

        List<PSpaceHistoryDataDTO> historyDataDList=getEquipmentHistoryPointData(lastExecuteTime,nowDate,pointList);
        if(CollectionUtils.isEmpty(historyDataDList)){
            log.info("未发现点位报警! startTime={},endTime={}",lastExecuteTime,nowDate);
            alarmSettingList.forEach(it->it.setLastExecuteTime(nowDate));
            fabosJsonDao.updateBatchById(alarmSettingList);
            return false;
        }
        historyDataDList.sort(Comparator.comparing(PSpaceHistoryDataDTO::getTimestamp).thenComparingLong(PSpaceHistoryDataDTO::getHistoryId));
        List<AlarmRecord> recordList=new ArrayList<>();
        for (PSpaceHistoryDataDTO historyDataDTO: historyDataDList) {
            for (AlarmSetting alarmSetting:alarmSettingList) {
                checkAlarmRule(alarmSetting,recordList,historyDataDTO);
            }
        }
        alarmSettingList.forEach(it->it.setLastExecuteTime(nowDate));
        fabosJsonDao.updateBatchById(alarmSettingList);
        fabosJsonDao.insertBatch(recordList);
        return true;
    }

    private boolean checkAlarmTime(Date nowDate,AlarmSetting setting){
        AlarmRule alarmRule=setting.getAlarmRule();
        if(alarmRule==null||CollectionUtils.isEmpty(alarmRule.getConditionList())){
            return false;
        }
        if(RuleTypeEnum.Enum.timer.name().equals(alarmRule.getRuleType())){
            int nowWeek=DateUtil.dayOfWeek(nowDate);
            if(alarmRule.getEffectiveTime().indexOf(String.valueOf(nowWeek))<0){
                return false;
            }
        }
        return true;
    }

    private void checkAlarmRule(AlarmSetting setting,List<AlarmRecord> recordList,PSpaceHistoryDataDTO dataDTO){
        try {
            AlarmRule alarmRule=setting.getAlarmRule();
            if(alarmRule==null||CollectionUtils.isEmpty(alarmRule.getConditionList())){
                return;
            }
            List<AlarmRuleCondition> conditionList= alarmRule.getConditionList();
            int alarmCount=0;
            boolean isAlarm=false;
            List<AlarmRuleCondition> alarmConditionList=new ArrayList<>();
            for (AlarmRuleCondition condition:conditionList) {
                if(condition.getEquipmentPoint()!=null&&equalsLongName(condition.getEquipmentPoint().getName(),dataDTO.getLongName())){
                    Boolean isOK = checkCondition(condition,dataDTO);
                    if(isOK){
                        alarmCount++;
                        alarmConditionList.add(condition);
                    }
                }
            }
            if(ConditionTypeEnum.Enum.all.name().equals(alarmRule.getConditionType())&&alarmCount>=conditionList.size()){
                isAlarm=true;
            }else if(ConditionTypeEnum.Enum.or.name().equals(alarmRule.getConditionType())&&alarmCount>=1){
                isAlarm=true;
            }
            if(isAlarm){
                log.info("发现点位报警,报警数量{}",alarmCount);
                List<AlarmRuleExecute> executeList = alarmRule.getExecuteList();
                if(CollectionUtils.isNotEmpty(executeList)){
                    String alarmEquipmentPoint = conditionList.get(0).getEquipmentPoint().getName();
                    String alarmEquipmentArchiveCode = conditionList.get(0).getEquipmentArchive().getGeneralCode();
                    executeTask(alarmEquipmentPoint,alarmEquipmentArchiveCode,dataDTO.getTimestamp(),setting,executeList,recordList);
                }else {
                    recordList.add(createAlarmRecord(setting,dataDTO.getTimestamp()));
                }
            }
        }catch (Exception e){
          log.error("告警检查失败! Exception={}",e);
        }
    }

    public boolean equalsLongName(String equipmentPointName,String longName){
        if(equipmentPointName==null||longName==null){
            return false;
        }
        longName=longName.replace("/","");
        equipmentPointName=equipmentPointName.replace("\\","");
        if(equipmentPointName.equals(longName)){
            return true;
        }
        return false;
    }

    public AlarmRecord createAlarmRecord(AlarmSetting setting,Date alarmTime){
        AlarmRecord alarmRecord=new AlarmRecord();
        alarmRecord.setEquipmentArchive(setting.getEquipmentArchive());
        alarmRecord.setAlarmSetting(setting);
        alarmRecord.setAlarmLevel(setting.getAlarmLevel());
        alarmRecord.setAlarmStatus(AlarmStatusEnum.Enum.untreated.name());
        alarmRecord.setAlarmContent(setting.getDescription());
        alarmRecord.setAlarmTime(alarmTime);
        return alarmRecord;
    }

    private Boolean checkCondition(AlarmRuleCondition condition,PSpaceHistoryDataDTO dataDTO){
        boolean isOk=false;
        try {
            switch (condition.getExprType()){
                case "equals":
                    if(StringUtils.equals(condition.getValue(),dataDTO.getValue())){
                        isOk=true;
                    }
                    break;
                case "less":
                    if(StringUtils.isNotBlank(condition.getValue())&&StringUtils.isNotBlank(dataDTO.getValue())){
                        if(Double.parseDouble(dataDTO.getValue())>Double.parseDouble(condition.getValue())){
                            isOk=true;
                        }
                    }
                    break;
                case "greater":
                    if(StringUtils.isNotBlank(condition.getValue())&&StringUtils.isNotBlank(dataDTO.getValue())){
                        if(Double.parseDouble(dataDTO.getValue())<Double.parseDouble(condition.getValue())){
                            isOk=true;
                        }
                    }
                    break;
                case "lessEquals":
                    if(StringUtils.isNotBlank(condition.getValue())&&StringUtils.isNotBlank(dataDTO.getValue())){
                        if(Double.parseDouble(dataDTO.getValue())>=Double.parseDouble(condition.getValue())){
                            isOk=true;
                        }
                    }
                    break;
                case "greaterEquals":
                    if(StringUtils.isNotBlank(condition.getValue())&&StringUtils.isNotBlank(dataDTO.getValue())){
                        if(Double.parseDouble(dataDTO.getValue())<=Double.parseDouble(condition.getValue())){
                            isOk=true;
                        }
                    }
                    break;
                case "isNotNull":
                    if(StringUtils.isNotBlank(dataDTO.getValue())){
                        isOk=true;
                    }
                    break;
                case "isNull":
                    if(StringUtils.isBlank(dataDTO.getValue())){
                        isOk=true;
                    }
                    break;
            }
        }catch (Exception e){
            log.error("字符转换错误,e={}",e);
        }
        return isOk;
    }

    private void executeTask(String alarmEquipmentPoint,String alarmEquipmentArchiveCode,Date alarmTime,AlarmSetting setting,List<AlarmRuleExecute> executeList,List<AlarmRecord> alarmRecordList){
        if(CollectionUtils.isEmpty(executeList)){
            return;
        }
        for (AlarmRuleExecute execute:executeList) {
            if(ExecuteTypeEnum.Enum.http.name().equals(execute.getExecuteType())){
                //调用HTTP接口
                callHttp(alarmEquipmentPoint,alarmEquipmentArchiveCode,alarmTime,execute);
            }else {
                alarmRecordList.add(createAlarmRecord(setting,alarmTime));
            }
        }
    }

    private void callHttp(String alarmEquipmentSign,String alarmEquipmentArchiveCode,Date alarmTime,AlarmRuleExecute execute){
        String url= execute.getAddress()+"/"+execute.getInterfaceUrl();
        Map<String,Object> paramMap=new HashMap<>();
        List<AlarmRuleExecuteParam> paramList=execute.getParamList();
        if(CollectionUtils.isNotEmpty(paramList)){
            paramList.forEach(it->{
                switch (it.getParamType()){
                    case "fixed":
                        paramMap.put(it.getParamName(),it.getParamValue());
                        break;
                    case "equipmentPointCode":
                        paramMap.put(it.getParamName(),alarmEquipmentSign);
                        break;
                    case "equipmentArchiveCode":
                        paramMap.put(it.getParamName(),alarmEquipmentArchiveCode);
                        break;
                    case "alarmDate":
                        paramMap.put(it.getParamName(),DateUtil.formatDateTime(alarmTime));
                        break;
                }
            });
            List<AlarmRuleExecuteParam> equipmentSignParamList=paramList.stream().filter(it->ParamTypeEnum.Enum.equipmentPoint.name().equals(it.getParamType())).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(equipmentSignParamList)){
                List<String> equipmentSign=equipmentSignParamList.stream().map(it->it.getEquipmentPoint().getName()).collect(Collectors.toList());
                List<PSpaceDataDTO> dataList= getEquipmentPointData(new HashSet<>(equipmentSign));
                equipmentSignParamList.forEach(it->{
                    PSpaceDataDTO dataDTO=dataList.stream().filter(da->equalsLongName(it.getEquipmentPoint().getName(),da.getLongName())).findFirst().orElse(null);
                    if(dataDTO!=null){
                        paramMap.put(it.getParamName(),dataDTO.getPv());
                    }
                });
            }
        }
        String result=null;
        if(HTTPTypeEnum.Enum.post.name().equals(execute.getMethod())){
            log.info("开始调用http接口,url={},paramMap={}",url,paramMap);
            result=HttpUtil.post(url,JSONObject.toJSONString(paramMap),12000);
        }else {
            result=HttpUtil.get(url,paramMap);
        }
        log.info("调用完成!result={}",result);
    }

    private List<PSpaceHistoryDataDTO> getEquipmentHistoryPointData(Date startTime, Date endTime,Set<String> pointList){
        Map<String,Object> paramMap=new HashMap<>();
        paramMap.put("user",pSpaceProp.user);
        paramMap.put("pwsd",pSpaceProp.password);
        paramMap.put("db",pSpaceProp.db);
        paramMap.put("opet","DBExecute");
        paramMap.put("data","select * from his where TIMESTAMP between '"+DateUtil.formatDateTime(startTime)+"' and '"+DateUtil.formatDateTime(endTime)+"' and longName in ("+getJoinString(pointList)+")");
        paramMap.put("mode",1);
        String param=JSONObject.toJSONString(paramMap);
        log.info("调用组态查询url={},paramMap={}",pSpaceProp.url,param);
        String resultStr= HttpUtil.post(pSpaceProp.url,param);
        PSpaceResultDTO resultDTO= JSONObject.parseObject(resultStr, PSpaceResultDTO.class);
        return PSpaceDataUtil.readDataList(resultDTO,PSpaceHistoryDataDTO.class);
    }


    private List<PSpaceDataDTO> getEquipmentPointData(Set<String> pointList){
        Map<String,Object> paramMap=new HashMap<>();
        paramMap.put("user",pSpaceProp.user);
        paramMap.put("pwsd",pSpaceProp.password);
        paramMap.put("db",pSpaceProp.db);
        paramMap.put("opet","DBExecute");
        paramMap.put("data","select * from real where longname in ("+getJoinString(pointList)+")");
        paramMap.put("mode",1);
        String resultStr= HttpUtil.post(pSpaceProp.url,JSONObject.toJSONString(paramMap));
        PSpaceResultDTO resultDTO= JSONObject.parseObject(resultStr, PSpaceResultDTO.class);
        return PSpaceDataUtil.readDataList(resultDTO,PSpaceDataDTO.class);
    }

    public String getJoinString(Set<String> pointList){
        if(CollectionUtils.isEmpty(pointList)){
            return "";
        }
        StringBuilder sb=new StringBuilder();
        for (String point:pointList){
            sb.append("'");
            sb.append(point);
            sb.append("',");
        }
        sb=sb.replace(sb.lastIndexOf(","),sb.length(),"");
        return sb.toString();
    }

    @Transient
    public void importExcel(MultipartFile file){
        try {
            List<EquipmentPointDTO> equipmentPointDTOList = importSheetByName(file, "设备点位", EquipmentPointDTO.class);
            List<AlarmRuleDTO> alarmRuleDTOList = importSheetByName(file, "报警规则", AlarmRuleDTO.class);
            List<AlarmRuleConditionDTO> ruleConditionDTOList = importSheetByName(file, "触发条件", AlarmRuleConditionDTO.class);
            List<AlarmRuleExecuteDTO> ruleExecuteDTOList = importSheetByName(file, "执行动作", AlarmRuleExecuteDTO.class);
            List<AlarmRuleExecuteParamDTO> ruleExecuteParamDTOList = importSheetByName(file, "调用参数", AlarmRuleExecuteParamDTO.class);
            List<cec.jiutian.bc.alarmSetting.domain.dto.AlarmSettingDTO> alarmSettingDTOList = importSheetByName(file, "告警设置", cec.jiutian.bc.alarmSetting.domain.dto.AlarmSettingDTO.class);
            //设备点位导入
            if(CollectionUtils.isNotEmpty(equipmentPointDTOList)){
              Map<String,List<EquipmentPointDTO>> equipmentPointMap =  equipmentPointDTOList.stream().collect(Collectors.groupingBy(EquipmentPointDTO::getEquipmentCode));
                for (String key:equipmentPointMap.keySet()) {
                    EquipmentArchive equipmentArchive=getEquipmentArchive(key);
                    if(equipmentArchive==null){
                        throw new FabosJsonApiErrorTip("设备不存在,设备编码:"+key);
                    }
                    List<EquipmentPointDTO> pointDTOList= equipmentPointMap.get(key);
                    List<EquipmentPoint> pointList= pointDTOList.stream().map(it->{
                        EquipmentPoint equipmentPoint=new EquipmentPoint();
                        equipmentPoint.setEquipmentArchive(equipmentArchive);
                        equipmentPoint.setName(it.getName());
                        equipmentPoint.setDescription(it.getDescription());
                        return equipmentPoint;
                    }).collect(Collectors.toList());
                    fabosJsonDao.insertBatch(pointList);
                }
            }
            //报警规则导入
            if(CollectionUtils.isNotEmpty(alarmRuleDTOList)){
               List<AlarmRule> alarmRuleList= alarmRuleDTOList.stream().map(it->{
                    AlarmRule alarmRule=new AlarmRule();
                    alarmRule.setRuleName(it.getRuleName());
                    alarmRule.setConditionType(it.getConditionType());
                    alarmRule.setDescription(it.getDescription());
                    alarmRule.setRuleType(it.getRuleType());
                    return alarmRule;
                }).collect(Collectors.toList());
                fabosJsonDao.insertBatch(alarmRuleList);
            }
            //触发条件导入
            if(CollectionUtils.isNotEmpty(ruleConditionDTOList)){
                for (AlarmRuleConditionDTO conditionDTO:ruleConditionDTOList) {
                    AlarmRuleCondition alarmRuleCondition=new AlarmRuleCondition();
                    AlarmRule alarmRule = getAlarmRule(conditionDTO.getRuleName());
                    alarmRuleCondition.setEquipmentArchive(getEquipmentArchive(conditionDTO.getEquipmentCode()));
                    alarmRuleCondition.setEquipmentPoint(getEquipmentPoint(conditionDTO.getEquipmentCode(),conditionDTO.getEquipmentPointName()));
                    alarmRuleCondition.setValue(conditionDTO.getValue());
                    alarmRuleCondition.setExprType(conditionDTO.getExprType());
                    List<AlarmRuleCondition> conditionList= alarmRule.getConditionList();
                    if(conditionList==null){
                        conditionList=new ArrayList<>();
                    }
                    conditionList.add(alarmRuleCondition);
                    alarmRule.setConditionList(conditionList);
                    fabosJsonDao.saveOrUpdate(alarmRule);
                }
            }
            //执行动作条件导入
            if(CollectionUtils.isNotEmpty(ruleExecuteDTOList)){
                for (AlarmRuleExecuteDTO executeDTO:ruleExecuteDTOList) {
                    AlarmRuleExecute alarmRuleExecute=new AlarmRuleExecute();
                    BeanUtils.copyProperties(executeDTO,alarmRuleExecute);
                    AlarmRule alarmRule = getAlarmRule(executeDTO.getRuleName());
                    alarmRuleExecute.setExecuteType(executeDTO.getExecuteType());
                    alarmRuleExecute.setAddress(executeDTO.getAddress());
                    alarmRuleExecute.setMethod(executeDTO.getMethod());
                    alarmRuleExecute.setInterfaceUrl(executeDTO.getInterfaceUrl());
                    List<AlarmRuleExecute> executeList= alarmRule.getExecuteList();
                    if(executeList==null){
                        executeList=new ArrayList<>();
                    }
                    executeList.add(alarmRuleExecute);
                    fabosJsonDao.saveOrUpdate(alarmRule);
                }
            }
            //调用参数导入
            if(CollectionUtils.isNotEmpty(ruleExecuteParamDTOList)){
                Map<String,List<AlarmRuleExecuteParamDTO>> ruleExecuteParamMap =  ruleExecuteParamDTOList.stream().collect(Collectors.groupingBy(AlarmRuleExecuteParamDTO::getRuleName));
                for (String key:ruleExecuteParamMap.keySet()) {
                    List<AlarmRuleExecuteParamDTO> executeParamDTOList= ruleExecuteParamMap.get(key);
                    AlarmRule alarmRule = getAlarmRule(key);
                    List<AlarmRuleExecute> executeList= alarmRule.getExecuteList();
                    if(CollectionUtils.isEmpty(executeList)){
                        throw new FabosJsonApiErrorTip("执行列表不能为空!");
                    }
                    AlarmRuleExecute alarmRuleExecute= executeList.get(0);
                    List<AlarmRuleExecuteParam> executeParamList=executeParamDTOList.stream().map(it->{
                        AlarmRuleExecuteParam executeParam=new AlarmRuleExecuteParam();
                        BeanUtils.copyProperties(it,executeParam);
                        if(StringUtils.isNotBlank(it.getEquipmentCode())){
                            executeParam.setEquipmentArchive(getEquipmentArchive(it.getEquipmentCode()));
                        }
                        if(StringUtils.isNotBlank(it.getEquipmentPointName())){
                            executeParam.setEquipmentPoint(getEquipmentPoint(it.getEquipmentCode(),it.getEquipmentPointName()));
                        }
                        executeParam.setParamName(it.getParamName());
                        executeParam.setParamType(it.getParamType());
                        executeParam.setParamValue(it.getParamValue());
                        return executeParam;
                    }).collect(Collectors.toList());
                    alarmRuleExecute.setParamList(executeParamList);
                    fabosJsonDao.saveOrUpdate(alarmRuleExecute);
                }
            }
            //告警设置导入
            if(CollectionUtils.isNotEmpty(alarmSettingDTOList)){
                List<AlarmSetting> alarmSettingList= alarmSettingDTOList.stream().map(it->{
                    AlarmSetting alarmSetting=new AlarmSetting();
                    alarmSetting.setAlarmName(it.getAlarmName());
                    alarmSetting.setAlarmLevel(it.getAlarmLevel());
                    alarmSetting.setState(it.getState());
                    alarmSetting.setEquipmentArchive(getEquipmentArchive(it.getEquipmentCode()));
                    alarmSetting.setAlarmRule(getAlarmRule(it.getRuleName()));
                    return alarmSetting;
                }).collect(Collectors.toList());
              fabosJsonDao.insertBatch(alarmSettingList);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }

    private EquipmentPoint getEquipmentPoint(String code,String point){
        if(StringUtils.isBlank(code)||StringUtils.isBlank(point)){
            return null;
        }
        EquipmentPoint equipmentPoint=new EquipmentPoint();
        EquipmentArchive equipmentArchive=new  EquipmentArchive();
        equipmentArchive.setCode(code);
        equipmentPoint.setName(point);
        equipmentPoint.setEquipmentArchive(equipmentArchive);
        EquipmentPoint equipmentPoint1= fabosJsonDao.selectOne(equipmentPoint);
        if(equipmentPoint1==null){
            throw new FabosJsonApiErrorTip("设备编码不存在 equipmentCode:"+code);
        }
        return equipmentPoint1;
    }


    private EquipmentArchive getEquipmentArchive(String code){
        if(StringUtils.isBlank(code)){
            return null;
        }
        EquipmentArchive equipmentArchive=new EquipmentArchive();
        equipmentArchive.setGeneralCode(code);
        EquipmentArchive equipmentArchive1= fabosJsonDao.selectOne(equipmentArchive);
        if(equipmentArchive==null){
            throw new FabosJsonApiErrorTip("设备编码不存在 equipmentCode:"+code);
        }
        return equipmentArchive1;
    }

    private AlarmRule getAlarmRule(String ruleName){
        AlarmRule alarmRule=new AlarmRule();
        alarmRule.setRuleName(ruleName);
        AlarmRule alarmRule1= fabosJsonDao.selectOne(alarmRule);
        if(alarmRule1==null){
            throw new FabosJsonApiErrorTip("报警规则不存在! ruleName:"+ruleName);
        }
        return alarmRule1;
    }


    public <T> List<T> importSheetByName(MultipartFile file, String sheetName, Class<T> clazz) throws IOException {
        try (InputStream inputStream = file.getInputStream()) {
            ExcelDataListener<T> listener = new ExcelDataListener<>();
            EasyExcel.read(inputStream, clazz, listener)
                    .sheet(sheetName)   // 指定sheet名称
                    .doRead();
            return listener.getDataList();
        }
    }
}
