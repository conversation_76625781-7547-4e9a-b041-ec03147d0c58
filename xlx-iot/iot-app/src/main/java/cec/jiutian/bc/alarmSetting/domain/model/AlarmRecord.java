package cec.jiutian.bc.alarmSetting.domain.model;

import cec.jiutian.bc.enums.AlarmLevelEnum;
import cec.jiutian.bc.enums.AlarmStatusEnum;
import cec.jiutian.bc.equipmentArchive.domain.equipmentArchive.model.EquipmentArchive;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/5/6
 * @description TODO
 */
@FabosJson(
        name = "告警记录",
        orderBy = "AlarmRecord.createTime desc"
)
@Table(name = "alarm_record")
@Entity
@Getter
@Setter
public class AlarmRecord extends MetaModel {


        @FabosJsonField(
                views = @View(title = "报警级别"),
                edit = @Edit(title = "报警级别",notNull = true,type = EditType.CHOICE,
                        choiceType = @ChoiceType(fetchHandler = AlarmLevelEnum.class),readonly = @Readonly,defaultVal = "general")
        )
        private String alarmLevel;

        @FabosJsonField(
                views = @View(title = "设备", column = "generalCode"),
                edit = @Edit(title = "设备",
                        type = EditType.REFERENCE_TABLE,
                        filter = @Filter(value = "EquipmentArchive.equipment.equipmentType.type = 'Equipment'"),
                        notNull = true, search = @Search(vague = true),readonly = @Readonly,
                        referenceTableType = @ReferenceTableType(id = "id", label = "generalCode")
                        , allowAddMultipleRows = false
                )
        )
        @ManyToOne
        @JoinColumn(name = "equipment_archive_id")
        private EquipmentArchive equipmentArchive;

        @FabosJsonField(
                views = @View(title = "状态"),
                edit = @Edit(title = "状态",readonly = @Readonly,notNull = true,type = EditType.CHOICE,choiceType = @ChoiceType(fetchHandler = AlarmStatusEnum.class))
        )
        private String alarmStatus;

        @FabosJsonField(
                views = @View(title = "报警时间", type =  ViewType.DATE_TIME),
                edit = @Edit(title = "报警时间",readonly = @Readonly)
        )
        private Date alarmTime;

        @FabosJsonField(
                views = @View(title = "报警内容"),
                edit = @Edit(title = "报警内容",
                        readonly = @Readonly,
                        type = EditType.TEXTAREA)
        )
        private String alarmContent;

        @ManyToOne
        @FabosJsonField(
                views = {
                        @View(title = "报警设置", column = "alarmName")
                },
                edit = @Edit(title = "报警设置",notNull = true, type = EditType.REFERENCE_TABLE,
                        readonly = @Readonly,
                        referenceTableType = @ReferenceTableType(id = "id", label = "alarmName")
                )
        )
        private AlarmSetting alarmSetting;
}
