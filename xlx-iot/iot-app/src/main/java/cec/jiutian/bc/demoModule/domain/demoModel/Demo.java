package cec.jiutian.bc.demoModule.domain.demoModel;



//import cec.jiutian.api.remoteCallLog.model.RemoteCallLog;

import cec.jiutian.api.remoteCallLog.model.RemoteCallLog;
import cec.jiutian.bc.demoModule.domain.proxy.DemoDataProxy;
import cec.jiutian.bc.demoModule.domain.proxy.DemoFlowProxy;
import cec.jiutian.bc.demoModule.handler.DemoOperationHandler;
import cec.jiutian.bc.demoModule.handler.DemoTest1Handler;
import cec.jiutian.bc.demoModule.handler.DemoTest2Handler;
import cec.jiutian.bc.flow.domain.model.entity.ExamineModel;
import cec.jiutian.bc.generalModeler.domain.warehouseArea.model.Warehouse;
import cec.jiutian.core.frame.constant.MenuTypeEnum;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.InheritStrategy;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.HideConditionByParent;
import cec.jiutian.view.InputGroup;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DependFieldDisplay;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.field.edit.TabTableReferType;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowBaseOperation;
import cec.jiutian.view.type.RowOperation;
import jakarta.persistence.CascadeType;
import jakarta.persistence.ConstraintMode;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.ForeignKey;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.annotations.FilterDef;
import org.hibernate.annotations.Filters;
import org.hibernate.annotations.ParamDef;

import java.util.List;


@FabosJson(
        dataProxy = DemoDataProxy.class,
        orderBy = "Demo.createTime desc",
        subTableDisplayType = FabosJson.SubTableDisplayTypeEnum.TAB,
        name = "demo", power = @Power(examine = true, examineDetails = true),
        flowProxy = DemoFlowProxy.class,
        flowCode = "DEMO5",
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "examine",
                        ifExpr = "examineStatus =='3' || examineStatus =='1'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "examineDetails",
                        ifExpr = "examineStatus =='0'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                )
        },
        rowOperation = {
                @RowOperation(
                        code = "Demo@RemoteCallDemo",
                        operationHandler = DemoOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        menuButtonType = RowOperation.MenuButtonTypeEnum.DEFAULT,
                        callHint = "推送数据到第三方？",
                        title = "推送数据到第三方1",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "Demo@RemoteCallDemo"
                        )
                ),
                @RowOperation(
                        title = "测试弹窗中选择",
                        code = "test1@Demo",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        operationHandler = DemoTest1Handler.class,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = DemoMto.class,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "test1@Demo"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
//                        ifExpr = "selectedItems[0].processCompleteFlag == 'Y' || selectedItems[0].isProcess != 'Y'",
//                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "测试弹窗修改其他模型",
                        code = "test2@Demo",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        operationHandler = DemoTest2Handler.class,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = DemoSimon.class,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "test2@Demo"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
//                        ifExpr = "selectedItems[0].processCompleteFlag == 'Y' || selectedItems[0].isProcess != 'Y'",
//                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                )
        }
)
@Setter
@Getter
@Entity
@Accessors(chain = true)
@Table(name = "mos_warehouse_demo")
@InheritStrategy(
        excludeParentRowBaseOperationCode = {},
        dataProxyFlag = true,
        excludeParentFields = {}
)
//@FilterDef(name = "demoFilter", defaultCondition = "1=1",
//        parameters = @ParamDef(name = "code", type = String.class))
//@org.hibernate.annotations.Filter(name = "demoFilter", condition = "code = :code")
//@FilterDef(name = "demoFilterCode",
//        parameters = @ParamDef(name = "code", type = String.class))
@FilterDef(name = "demoFilterChild",
        parameters = @ParamDef(name = "child_name", type = String.class))
//@org.hibernate.annotations.Filter(name = "demoFilterCode", condition = "code = :code")
public class Demo extends ExamineModel {

    @FabosJsonField(
            views = @View(title = "仓库编码2"),
            edit = @Edit(title = "仓库编码2", notNull = false, search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    private String code;

    @FabosJsonField(
            views = @View(title = "微信账号2", show = false, index = 99),
            edit = @Edit(title = "微信账号2", notNull = false)
    )
    private String wechatAccount;

    @FabosJsonField(
            views = @View(title = "仓库名称1"),
            edit = @Edit(title = "仓库名称1", notNull = false, search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    private String name;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "仓库", column = "name"),
            edit = @Edit(title = "仓库", search = @Search(vague = true),
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name", queryModelClass = "DemoQueryModel")
                    ,
//                    queryModelCondition = "{\"id\": \"abc\"}"
                    queryModelCondition = "{\"id\": \"${code}\"}"
            )
    )
    private Warehouse warehouse;

    @FabosJsonField(
            edit = @Edit(title = "数量", inputGroup = @InputGroup(postfix = "#{moduleTypeCode},#{anotherField},/周")),
            views = @View(title = "数量")

    )
    private Double quantity;

    @FabosJsonField(
            views = @View(title = "数量单位"),
            edit = @Edit(
                    title = "数量单位",
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(
                            fetchHandler = {MenuTypeEnum.ChoiceFetch.class}
                    ),
                    dependFieldDisplay = @DependFieldDisplay(

                    ),
                    inputGroup = @InputGroup(postfix = "/次") // 如果该字段作为其他字段的InputGroup,则忽略此处的配置
            )



    )
    private String moduleTypeCode;

    @ManyToOne(cascade = {}, fetch = FetchType.LAZY)
    @FabosJsonField(
            views = @View(title = "下发是否成功", column = "successFlagShowField", type = ViewType.TABLE_FORM, export = false),
            edit = @Edit(title = "下发是否成功", show = true, type = EditType.REFERENCE_TABLE, search = @Search(vague = true),
                    referenceTableType = @ReferenceTableType(id = "id", label = "successFlagShowField"))
    )
    @JoinColumn(name = "remote_call_log_id", referencedColumnName = "issuerDataId", foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT))
    private RemoteCallLog remoteCallLog;


    @ManyToMany
    @FabosJsonField(
            views = @View(title = "子表", column = "childName", type = ViewType.TABLE_VIEW),
            edit = @Edit(title = "子表", type = EditType.TAB_TABLE_REFER, index = 10000,
                    tabTableReferType = @TabTableReferType(id = "id", label = "childName")),
            hideConditionByParent = {
                    @HideConditionByParent(
                            expr = "${quantity > 1 && quantity < 10}",
                            fields = "childName1",
                            scope = HideConditionByParent.ScopeEnum.BOTH
                    ),
                    @HideConditionByParent(
                            expr = "${quantity >= 10}",
                            fields = "childName2",
                            scope = HideConditionByParent.ScopeEnum.FORM
                    ),
                    @HideConditionByParent(
                            expr = "${code == '01'}",
                            fields = "childName3,childName2",
                            scope = HideConditionByParent.ScopeEnum.TABLE
                    ),
            }
    )
    @JoinTable(
            name = "iot_rel_demo_demoChild",
            joinColumns = @JoinColumn(name = "demo_id", referencedColumnName = "id"),
            inverseJoinColumns = @JoinColumn(name = "child_id", referencedColumnName = "id", foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT)))
    private List<DemoChild> demoChildList;

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
    @JoinColumn(name = "demo_id", referencedColumnName = "id", foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT))
    @FabosJsonField(
            views = @View(title = "三次升级推送人员", column = "name", type = ViewType.TABLE_VIEW, exportAsSubTable = true),
            edit = @Edit(title = "三次升级推送人员", index = 2,
                    search = @Search(vague = true, subModelQueryField = "childName"),
                    type = EditType.TAB_TABLE_ADD
            ),
            hideConditionByParent = {
                    @HideConditionByParent(
                            expr = "${quantity < 10 || quantity > 100}",
                            fields = "childName1",
                            scope = HideConditionByParent.ScopeEnum.BOTH
                    ),
                    @HideConditionByParent(
                            expr = "${quantity >= 10}",
                            fields = "childName2",
                            scope = HideConditionByParent.ScopeEnum.BOTH
                    ),
                    @HideConditionByParent(
                            expr = "${code == '01'}",
                            fields = "childName3,childName2",
                            scope = HideConditionByParent.ScopeEnum.FORM
                    ),
            }
    )
    @Filters({
            @org.hibernate.annotations.Filter(name = "demoFilterChild", condition = "child_name=:child_name"),
    })
    private List<DemoChild2> demoChild2s;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "发货物资", column = "name"),
            edit = @Edit(title = "发货物资",
                    type = EditType.REFERENCE_TABLE,
                    filter = @Filter(value = "validateFlag = 'Y' and specificationCode = '03'"),
                    queryCondition = "{\"validateFlag\": \"Y\", \"specificationCode\": \"03\"}",
                    referenceTableType = @ReferenceTableType(id = "id", label = "name")
            )
    )
    private SpecificationManageMTO material;


}
