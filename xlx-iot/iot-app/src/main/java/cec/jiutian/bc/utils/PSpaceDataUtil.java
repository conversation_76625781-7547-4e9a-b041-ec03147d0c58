package cec.jiutian.bc.utils;

import cec.jiutian.bc.alarmSetting.annotation.PSpaceField;
import cec.jiutian.bc.alarmSetting.domain.dto.PSpaceColumnDTO;
import cec.jiutian.bc.alarmSetting.domain.dto.PSpaceResultDTO;
import cec.jiutian.bc.alarmSetting.domain.dto.PSpaceResultDataDTO;
import cec.jiutian.common.exception.ServiceException;
import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import java.lang.reflect.Field;
import java.util.*;

@Slf4j
public class PSpaceDataUtil {

    public static <T> List<T> readDataList(PSpaceResultDTO resultDTO, Class<T> aClass){
        if(resultDTO.getCode()==null||resultDTO.getCode()!=0){
            log.error("调用失败e={}",resultDTO);
            throw new ServiceException("调用失败,msg:"+resultDTO.getMesg());
        }
        List<PSpaceColumnDTO> columnDTOList = resultDTO.getData().getCols();
        if(CollectionUtils.isEmpty(columnDTOList)){
            throw new ServiceException("调用失败!cols is null");
        }
        if(resultDTO.getData().getRows().length==0){
            return new ArrayList<>();
        }
        return readDataList(resultDTO.getData(),aClass);
    }

    public static <T> List<T> readDataList(PSpaceResultDataDTO dataDTO, Class<T> aClass){
        Map<String,Integer> indexMap=new HashMap<>();
        if(CollectionUtils.isNotEmpty(dataDTO.getCols())){
            dataDTO.getCols().forEach(it->{
                indexMap.put(it.getName(),it.getIndex());
            });
        }
        try {
            while (aClass != null) {
                List<T> list=new ArrayList<>();
                for (String[] rows:dataDTO.getRows()) {
                    T clazz = aClass.newInstance();
                    Class<?> bClass=clazz.getClass();
                    for (Field field : bClass.getDeclaredFields()) {
                        if (field.isAnnotationPresent(PSpaceField.class)){
                            PSpaceField pSpaceField=field.getAnnotation(PSpaceField.class);
                            Class<?> fieldType = field.getType();
                            Integer index=indexMap.get(pSpaceField.name());
                            if(index==null){
                                continue;
                            }
                            if (!field.isAccessible()) {
                                field.setAccessible(true);
                            }
                            String value= rows[index.intValue()];
                            Object convertedValue = convertToType(value, fieldType);
                            field.set(clazz,convertedValue);
                        }
                    }
                    list.add(clazz);
                }
                return list;
            }
        } catch (InstantiationException e) {
            throw new ServiceException(e);
        } catch (IllegalAccessException e) {
            throw new ServiceException(e);
        }
        return null;

    }


    private static Object convertToType(Object value, Class<?> targetType) {
        if (value == null) return null;
        // 处理字符串到基本类型的转换
        if (value instanceof String) {
            String strValue = (String) value;
            if (targetType == int.class || targetType == Integer.class) {
                return Integer.parseInt(strValue);
            } else if (targetType == long.class || targetType == Long.class) {
                return Long.parseLong(strValue);
            } else if (targetType == boolean.class || targetType == Boolean.class) {
                return Boolean.parseBoolean(strValue);
            } else if (targetType == Date.class) {
                return DateUtil.parseDateTime(strValue);
            } else if (targetType == String.class) {
                return strValue;
            }
        }
        if (targetType.isAssignableFrom(value.getClass())) {
            return value;
        }
        throw new IllegalArgumentException("无法将 " + value.getClass() + " 转换为 " + targetType);
    }

}
