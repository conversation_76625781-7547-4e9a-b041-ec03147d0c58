package cec.jiutian.bc.alarmRule.domain.model;

import cec.jiutian.bc.alarmRule.domain.proxy.AlarmRuleExecuteParamProxy;
import cec.jiutian.bc.enums.ParamTypeEnum;
import cec.jiutian.bc.equipmentArchive.domain.equipment.model.Equipment;
import cec.jiutian.bc.equipmentArchive.domain.equipmentArchive.model.EquipmentArchive;
import cec.jiutian.bc.equipmentArchive.domain.equipmentArchive.model.EquipmentPoint;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DependFieldDisplay;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.RowBaseOperation;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025/5/6
 * @description TODO
 */
@FabosJson(
        name = "参数值",
        orderBy = "AlarmRuleExecuteParam.createTime desc",
        dataProxy = AlarmRuleExecuteParamProxy.class,
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "edit"
                )
        }
)
@Table(name = "alarm_rule_execute_param")
@Entity
@Getter
@Setter
public class AlarmRuleExecuteParam extends MetaModel {

        @FabosJsonField(
                views = @View(title = "参数名"),
                edit = @Edit(title = "参数名",notNull = true)
        )
        private String paramName;

        @FabosJsonField(
                views = @View(title = "参数类型"),
                edit = @Edit(title = "参数类型",notNull = true,type = EditType.CHOICE,
                        choiceType = @ChoiceType(fetchHandler = ParamTypeEnum.class))
        )
        private String paramType;

        @FabosJsonField(
                views = @View(title = "设备", column = "generalCode"),
                edit = @Edit(title = "设备",
                        type = EditType.REFERENCE_TABLE,
                        notNull = true,
                        filter = @Filter(value = "EquipmentArchive.equipment.equipmentType.type = 'Equipment'"),
                        dependFieldDisplay = @DependFieldDisplay(showOrHide = "paramType == 'equipmentPoint'"),
                        referenceTableType = @ReferenceTableType(id = "id", label = "generalCode")
                        , allowAddMultipleRows = false
                )
        )
        @ManyToOne
        @JoinColumn(name = "equipment_archive_id")
        private EquipmentArchive equipmentArchive;


        @FabosJsonField(
                views = @View(title = "参数值"),
                edit = @Edit(title = "参数值",
                    notNull = true,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "paramType == 'fixed'"))
        )
        private String paramValue;


        @ManyToOne
        @FabosJsonField(
                views = {
                        @View(title = "参数", column = "name")
                },
                edit = @Edit(title = "参数",type = EditType.REFERENCE_TABLE,
                        notNull = true,
                        queryCondition = "{\"equipmentArchive.id\": \"${equipmentArchive.id}\"}",
                        dependFieldDisplay = @DependFieldDisplay(showOrHide = "paramType == 'equipmentPoint'"),
                        referenceTableType = @ReferenceTableType(id = "id", label = "name")
                )
        )
        @JoinColumn(name = "equipment_point_id")
        private EquipmentPoint equipmentPoint;
}
