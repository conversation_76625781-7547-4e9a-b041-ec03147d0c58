package cec.jiutian.bc.modeler.domain.dto;

import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.meta.SkipMetadataScanning;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.View;
import cec.jiutian.view.type.Power;
import lombok.Getter;
import lombok.Setter;

import jakarta.persistence.*;

@Setter
@Getter
@Entity
@Table(name = "fr_unt")
@FabosJson(
        name = "MES单位",
        orderBy = "id ASC",
        power = @Power(add = false,delete = false,edit = false,importable = false,export = false)
)
@SkipMetadataScanning
public class MesUnit {

    @FabosJsonField(
            edit = @Edit(title = "id", show = false)
    )
    @Id
    @jakarta.persistence.Column(name = "id")
    private String id;

    @FabosJsonField(
            views = @View(title = "", show = false),
            edit = @Edit(title = "", show = false)
    )
    @Column(name = "oid", length = 64, nullable = false)
    private String oid;

    @FabosJsonField(
            views = @View(title = "单位编码 "),
            edit = @Edit(title = "单位编码 ", notNull = true)
    )
    @Column(name = "unt_cd", length = 40, nullable = false)
    private String code;

    @FabosJsonField(
            views = @View(title = "单位名称 "),
            edit = @Edit(title = "单位名称 ", notNull = true)
    )
    @Column(name = "unt_nm", length = 40, nullable = false)
    private String name;

    @FabosJsonField(
            views = @View(title = "精度"),
            edit = @Edit(title = "精度")
    )
    @Column(name = "prcsn_nb", length = 16)
    private Integer precision;

    @FabosJsonField(
            views = @View(title = "单位组编码"),
            edit = @Edit(title = "单位组编码", notNull = true)
    )
    @Column(name = "unt_grp_cd", length = 40, nullable = false)
    private String groupCode;


}
