package cec.jiutian.bc.modeler.vo;

import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.DateType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.Search;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @description: cec.jiutian.bc.questionPaperManagement.domain.questionPaper.model.QuestionPaper
 */
@Data
public class QuestionPaperVO {
    @FabosJsonField(
            views = @View(title = "问题简述"),
            edit = @Edit(title = "问题简述", notNull = true, search = @Search(vague = true))
    )
    private String questionDescription;

    @FabosJsonField(
            views = @View(title = "问题等级"),
            edit = @Edit(title = "问题等级", notNull = true/*, search = @Search,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = InspectionResultEnum.class)*/)
    )
    private String level;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "发生日期", type = ViewType.DATE),
            edit = @Edit(title = "发生日期", notNull = true, dateType = @DateType(type = DateType.Type.DATE))
    )
    private LocalDate occurDate;

    @FabosJsonField(
            views = @View(title = "事件描述（5W2H）"),
            edit = @Edit(title = "事件描述（5W2H）", notNull = true,
                    type = EditType.TEXTAREA, inputType = @InputType(length = 400))
    )
    private String eventDescription;



    @FabosJsonField(
            views = @View(title = "责任部门id", show = false),
            edit = @Edit(title = "责任部门id", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "responsibleDepartmentMTO", beFilledBy = "id"))
    )
    private String responsibleDepartmentId;

    @FabosJsonField(
            views = @View(title = "责任部门"),
            edit = @Edit(title = "责任部门", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "responsibleDepartmentMTO", beFilledBy = "name"))
    )
    private String responsibleDepartmentName;



    @FabosJsonField(
            views = @View(title = "责任人ID", show = false),
            edit = @Edit(title = "责任人ID", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "responsiblePersonMTO", beFilledBy = "id"))
    )
    private String responsiblePersonId;

    @FabosJsonField(
            views = @View(title = "责任人"),
            edit = @Edit(title = "责任人", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "responsiblePersonMTO", beFilledBy = "name"))
    )
    private String responsiblePersonName;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "预计纳期", type = ViewType.DATE),
            edit = @Edit(title = "预计纳期", notNull = true, dateType = @DateType(type = DateType.Type.DATE))
    )
    private LocalDate expectedDeliveryDate;
}
