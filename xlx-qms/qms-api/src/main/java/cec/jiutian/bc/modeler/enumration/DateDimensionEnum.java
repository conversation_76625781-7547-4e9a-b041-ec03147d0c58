package cec.jiutian.bc.modeler.enumration;

/**
 * <AUTHOR>
 * @date 2025/7/7
 * @description TODO
 */
public enum DateDimensionEnum {

    YEAR("1", "年"),
    QUARTER("2", "季"),
    MONTH("3", "月"),
    WEEK("4", "周"),
    DAY("5", "日");

    private final String code;
    private final String name;

    DateDimensionEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
