package cec.jiutian.bc.modeler.enumration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/10
 * @description TODO
 */
public class TaskBusinessStateEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum data : Enum.values()) {
            list.add(new VLModel(data.name(), data.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        BE_INSPECT("待检验"),
        INSPECTING("检验中"),
        INSPECT_FINISH("检验完成"),
        BE_SAMPLING("待取样"),
        SAMPLING_FINISH("已取样"),
        SEND_SAMPLE("已送样"),
        RECEIVED_SAMPLE("已收样"),
        TERMINATION("终止检验"),
        EXCEPTION_STOP("产品异常处置"),

        ;

        private final String value;

    }
}
