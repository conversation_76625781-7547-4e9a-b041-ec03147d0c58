package cec.jiutian.bc.modeler.domain.inspectionMethod.handler;

import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.modeler.domain.inspectionMethod.model.InspectionMethod;
import cec.jiutian.bc.modeler.enumration.NamingRuleCodeEnum;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/2/27
 * @description TODO
 */
@Component
public class CodeGenerateDynamicHandler implements DependFiled.DynamicHandler<InspectionMethod> {

    @Resource
    private NamingRuleService namingRuleService;

    @Override
    public Map<String, Object> handle(InspectionMethod inspectionMethod) {
        Map<String, Object> map = new HashMap<>();
        map.put("generalCode",String.valueOf(namingRuleService.getNameCode(NamingRuleCodeEnum.InspectionMethodCode.name(), 1, null).get(0)));
        return map;
    }
}
