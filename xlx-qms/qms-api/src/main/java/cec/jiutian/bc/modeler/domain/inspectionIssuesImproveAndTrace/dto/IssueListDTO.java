package cec.jiutian.bc.modeler.domain.inspectionIssuesImproveAndTrace.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import javax.persistence.Entity;
import java.util.Date;

@Data
@Entity
public class IssueListDTO {

    /**
     * 问题清单编号
     */
    private String generalCode;

    /**
     * 任务编号
     */
    private String taskGeneralCode;

    /**
     * 任务名称
     */
    private String name;

    /**
     * 任务描述
     */
    private String description;

    /**
     * 巡检标准
     */
    private String inspectionStandard;

    /**
     * 巡检项目
     */
    private String inspectionProject;

    /**
     * 巡检内容
     */
    private String content;

    /**
     * 巡检结果 NO_EXCEPTION:无异常,EXCEPTION:异常,NOT_RELATED:不涉及
     */
    private String insResult;

    /**
     * 问题描述
     */
    private String issueDescription;

    /**
     * 严重程度 SLIGHT:轻微,SOSO:一般,SEVERE:严重
     */
    private String severityLevel;

    /**
     * 责任部门编码
     */
    private String orgCode;

    /**
     * 责任人
     */
    private String metaAccount;

    /**
     * 分析对策
     */
    private String correctiveAction;

    /**
     * 关联单据号
     */
    private String relatedDocumentNumber;

    /**
     * 措施纳期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date measuresDeadline;

    /**
     * 问题重复次数
     */
    private Integer issueRepeatedTimes;

    /**
     * 问题附件
     */
    private String attachment;
}
