package cec.jiutian.bc.modeler.vo;

import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.*;
import lombok.Data;

/**
 * <AUTHOR>
 * @description: cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.mto.CorrectPreventMeasureCreateMTO
 */
@Data
public class CorrectPreventMeasureVO {

    @FabosJsonField(
            views = @View(title = "关联单据类型"),
            edit = @Edit(title = "关联单据类型",
                    type = EditType.CHOICE,
                    notNull = true
            )
    )
    private String relatedDocumentType;
    @FabosJsonField(
            views = @View(title = "关联单据编码",show = false),
            edit = @Edit(title = "关联单据编码",
                    show = false
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "relatedDocumentMTO", beFilledBy = "code"))
    )
    private String relatedDocumentMTOCode;

    @FabosJsonField(
            views = @View(title = "关联单据"),
            edit = @Edit(title = "关联单据",
                    readonly = @Readonly,
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "relatedDocumentMTO", beFilledBy = "name"))
    )
    private String relatedDocumentMTOName;

    @FabosJsonField(
            views = @View(title = "不合格（潜在不合格）陈述"),
            edit = @Edit(title = "不合格（潜在不合格）陈述",
                    type = EditType.TEXTAREA)
    )
    private String nonConformanceStatement;

    @FabosJsonField(
            views = @View(title = "不合格（潜在不合格）情况核实（初步原因分析）"),
            edit = @Edit(title = "不合格（潜在不合格）情况核实（初步原因分析）",
                    type = EditType.TEXTAREA)
    )
    private String verificationNonConformity;

}
