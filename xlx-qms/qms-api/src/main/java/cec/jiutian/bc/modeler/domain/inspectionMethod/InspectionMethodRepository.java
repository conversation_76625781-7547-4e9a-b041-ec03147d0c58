package cec.jiutian.bc.modeler.domain.inspectionMethod;

import cec.jiutian.bc.modeler.domain.inspectionMethod.model.InspectionMethod;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface InspectionMethodRepository extends JpaRepository<InspectionMethod, String> {

    List<InspectionMethod> findByName(String name);

}
