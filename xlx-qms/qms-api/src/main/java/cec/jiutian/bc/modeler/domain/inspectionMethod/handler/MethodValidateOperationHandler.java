package cec.jiutian.bc.modeler.domain.inspectionMethod.handler;

import cec.jiutian.bc.modeler.domain.inspectionMethod.model.InspectionMethod;
import cec.jiutian.bc.modeler.enumration.StatusEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/28
 * @description TODO
 */
@Component
public class MethodValidateOperationHandler implements OperationHandler<InspectionMethod,Void> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<InspectionMethod> data, Void modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            InspectionMethod inspectionMethod = data.get(0);
            inspectionMethod.setStatus(StatusEnum.Enum.Effective.name());
            fabosJsonDao.mergeAndFlush(inspectionMethod);
        }
        return "msg.success('操作成功')";
    }
}
