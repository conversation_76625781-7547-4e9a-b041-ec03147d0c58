package cec.jiutian.bc.modeler.domain.inspectionItem.proxy;

import cec.jiutian.bc.generalModeler.util.StringUtils;
import cec.jiutian.bc.modeler.domain.inspectionItem.model.InspectionItem;
import cec.jiutian.bc.modeler.domain.inspectionItem.model.InspectionItemTarget;
import cec.jiutian.bc.modeler.enumration.ComparisonMethodEnum;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @date 2025/3/4
 * @description TODO
 */
@Component
public class InspectionItemProxy implements DataProxy<InspectionItem> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public void beforeAdd(InspectionItem inspectionItem) {
        DataProxy.super.beforeAdd(inspectionItem);
        checkParam(inspectionItem);
        checkName(inspectionItem);
    }

    @Override
    public void beforeUpdate(InspectionItem inspectionItem) {
        DataProxy.super.beforeUpdate(inspectionItem);
        checkParam(inspectionItem);
        checkName(inspectionItem);
    }

    private void checkParam(InspectionItem inspectionItem) {
        if (CollectionUtils.isEmpty(inspectionItem.getInspectionItemTargetList())) {
            return;
        }
        for (InspectionItemTarget inspectionItemTarget : inspectionItem.getInspectionItemTargetList()) {
            checkItem(inspectionItemTarget);
        }
    }

    private void checkItem(InspectionItemTarget inspectionItemTarget) {
        if (!ComparisonMethodEnum.Enum.range.name().equals(inspectionItemTarget.getComparisonMethod())) {
            return;
        }
        if (inspectionItemTarget.getUpperValue() <= inspectionItemTarget.getLowerValue()) {
            throw new FabosJsonApiErrorTip("上限值必须高于下限值，请确认");
        }
    }

    private void checkName(InspectionItem inspectionItem) {
        InspectionItem condition = new InspectionItem();
        condition.setName(inspectionItem.getName());
        InspectionItem data = fabosJsonDao.selectOne(condition);
        if (data != null) {
            if (data.getName().equals(inspectionItem.getName())) {
                if (StringUtils.isEmpty(inspectionItem.getId())) {
                    throw new FabosJsonApiErrorTip("检验项名称不可重复，请确认");
                }else {
                    if (!inspectionItem.getId().equals(data.getId())) {
                        throw new FabosJsonApiErrorTip("检验项名称不可重复，请确认");
                    }
                }
            }
        }
    }
}
