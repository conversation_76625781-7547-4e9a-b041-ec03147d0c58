package cec.jiutian.bc.modeler.domain.samplingPlan.model;

import cec.jiutian.bc.modeler.domain.dto.MesUnit;
import cec.jiutian.bc.modeler.domain.samplingPlan.handler.PlanInValidateOperationHandler;
import cec.jiutian.bc.modeler.domain.samplingPlan.handler.PlanValidateOperationHandler;
import cec.jiutian.bc.modeler.domain.samplingPlan.handler.SamplingPlanCodeGenerateDynamicHandler;
import cec.jiutian.bc.modeler.domain.samplingPlan.proxy.SamplingPlanProxy;
import cec.jiutian.bc.modeler.enumration.*;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.*;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.RowBaseOperation;
import cec.jiutian.view.type.RowOperation;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/28
 * @description TODO
 */
@FabosJson(
        name = "抽样方案",
        orderBy = "SamplingPlan.createTime desc",
        dataProxy = SamplingPlanProxy.class,
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "status == 'Effective'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "status == 'Effective'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                )
        },
        rowOperation = {
                @RowOperation(
                        title = "生效",
                        code = "SamplingPlan@EFFECTIVE",
                        operationHandler = PlanValidateOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "是否确定操作？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "SamplingPlan@EFFECTIVE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "status == 'Effective'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "失效",
                        code = "SamplingPlan@INVALID",
                        operationHandler = PlanInValidateOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "是否确定操作？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "SamplingPlan@INVALID"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "status == 'Invalid'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                )
        }
)
@Table(name = "bd_sampling_plan",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Getter
@Setter
public class SamplingPlan extends MetaModel {

    @FabosJsonField(
            views = @View(title = "编号"),
            edit = @Edit(title = "编号", notNull = true),
            dynamicField = @DynamicField(dependFiled = @DependFiled(buttonName = "生成", changeBy = "id",
                    dynamicHandler = SamplingPlanCodeGenerateDynamicHandler.class))
    )
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "抽样方案名称"),
            edit = @Edit(title = "抽样方案名称", search = @Search(vague = true),notNull = true,
                    inputType = @InputType(length = 40))
    )
    private String name;

    @FabosJsonField(
            views = @View(title = "适用范围"),
            edit = @Edit(title = "适用范围",type = EditType.CHOICE,search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = ItemTypeEnum.class))
    )
    private String applyRange;

    @FabosJsonField(
            views = @View(title = "抽样方案标准"),
            edit = @Edit(title = "抽样方案标准",type = EditType.CHOICE,search = @Search(),
                    notNull = true,
                    choiceType = @ChoiceType(fetchHandler = SamplingStandardEnum.class))
    )
    private String samplingStandard;

//    @FabosJsonField(
//            views = @View(title = "件次"),
//            edit = @Edit(title = "件次",
//                    dependFieldDisplay = @DependFieldDisplay(notNull = "samplingStandard == 'fullSampling'", showOrHide = "samplingStandard == 'fullSampling'"))
//    )
//    private String pieceItem;

    @FabosJsonField(
            views = @View(title = "抽样比例"),
            edit = @Edit(title = "抽样比例",inputGroup = @InputGroup(postfix = "%"),
                    numberType = @NumberType(min = 0,max = 100,precision = 2),
                    dependFieldDisplay = @DependFieldDisplay(notNull = "samplingStandard == 'proportionSampling'", showOrHide = "samplingStandard == 'proportionSampling'"))
    )
    private Double proportion;

    @FabosJsonField(
            views = @View(title = "固定抽样数量"),
            edit = @Edit(title = "固定抽样数量",
                    numberType = @NumberType(min = 0,max = 100,precision = 2),
                    dependFieldDisplay = @DependFieldDisplay(notNull = "samplingStandard == 'fixedSampling'", showOrHide = "samplingStandard == 'fixedSampling'"))
    )
    private Double fixedCount;

    @Transient
    @FabosJsonField(
            views = @View(title = "单位", show = false, column = "name"),
            edit = @Edit(title = "单位",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "samplingStandard != 'fullSampling'")
            )
    )
    private MesUnit unit;

    @FabosJsonField(
            views = @View(title = "单位编码",show = false),
            edit = @Edit(title = "单位编码",readonly = @Readonly,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "samplingStandard != 'fullSampling'")),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(beFilledBy = "code",changeBy = "unit"))
    )
    private String unitCode;

    @FabosJsonField(
            views = @View(title = "单位id",show = false),
            edit = @Edit(title = "单位id", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "unit", beFilledBy = "id"))
    )
    private String unitId;

    @FabosJsonField(
            views = @View(title = "AQL值"),
            edit = @Edit(title = "AQL值",type = EditType.CHOICE,search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = AQLValueEnum.class),
                    dependFieldDisplay = @DependFieldDisplay(notNull = "samplingStandard == 'AQLSampling'", showOrHide = "samplingStandard == 'AQLSampling'"))
    )
    private String aqlValue;

    @FabosJsonField(
            views = @View(title = "单位"),
            edit = @Edit(title = "单位",
                    dependFieldDisplay = @DependFieldDisplay(notNull = "samplingStandard == 'AQLSampling'", showOrHide = "samplingStandard == 'AQLSampling'"))
    )
    private String aqlValueUnit;

    @FabosJsonField(
            views = @View(title = "可接收缺陷数", show = false),
            edit = @Edit(title = "可接收缺陷数", show = false,
                    numberType = @NumberType(min = 0,max = 100,precision = 2))
    )
    private Double acValue;

//    @FabosJsonField(
//            views = @View(title = "可接收缺陷数"),
//            edit = @Edit(title = "可接收缺陷数",type = EditType.CHOICE,search = @Search(),
//                    numberType = @NumberType(min = 0,max = 100,precision = 2),
//                    choiceType = @ChoiceType(fetchHandler = GeneralLevelValueEnum.class),
//                    dependFieldDisplay = @DependFieldDisplay(notNull = "samplingStandard == 'AQLSampling'", showOrHide = "samplingStandard == 'AQLSampling'"))
//    )
//    private Double acValue;
//
//    @FabosJsonField(
//            views = @View(title = "拒收缺陷数"),
//            edit = @Edit(title = "拒收缺陷数",type = EditType.CHOICE,search = @Search(),
//                    numberType = @NumberType(min = 0,max = 100,precision = 2),
//                    choiceType = @ChoiceType(fetchHandler = GeneralLevelValueEnum.class),
//                    dependFieldDisplay = @DependFieldDisplay(notNull = "samplingStandard == 'AQLSampling'", showOrHide = "samplingStandard == 'AQLSampling'"))
//    )
//    private Double reValue;

    @FabosJsonField(
            views = @View(title = "检验水平类型"),
            edit = @Edit(title = "检验水平类型",type = EditType.CHOICE,search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = AQLInspectionTypeEnum.class),
                    dependFieldDisplay = @DependFieldDisplay(notNull = "samplingStandard == 'AQLSampling'", showOrHide = "samplingStandard == 'AQLSampling'"))
    )
    private String AQLInspectionType;

    @FabosJsonField(
            views = @View(title = "一般检验水平值"),
            edit = @Edit(title = "一般检验水平值",type = EditType.CHOICE,search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = GeneralLevelValueEnum.class),
                    dependFieldDisplay = @DependFieldDisplay(notNull = "samplingStandard == 'AQLSampling' && AQLInspectionType == 'General'", showOrHide = "samplingStandard == 'AQLSampling' && AQLInspectionType == 'General'"))
    )
    private String generalLevelValue;

    @FabosJsonField(
            views = @View(title = "特殊检验水平值"),
            edit = @Edit(title = "特殊检验水平值",type = EditType.CHOICE,search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = SpecialLevelValue.class),
                    dependFieldDisplay = @DependFieldDisplay(notNull = "samplingStandard == 'AQLSampling' && AQLInspectionType == 'Special'", showOrHide = "samplingStandard == 'AQLSampling' && AQLInspectionType == 'Special'"))
    )
    private String specialLevelValue;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态", type = EditType.CHOICE,search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = StatusEnum.class))
    )
    private String status;

    @FabosJsonField(
            views = @View(title = "备注"),
            edit = @Edit(title = "备注",
                    type = EditType.TEXTAREA)
    )
    private String remark;

    @OneToMany(cascade = {CascadeType.MERGE,CascadeType.DETACH}, orphanRemoval = true)
    @JoinColumn(name = "sampling_plan_id")
    @FabosJsonField(
            views = @View(title = "自定义抽样规则", type= ViewType.TABLE_VIEW),
            edit = @Edit(title = "自定义抽样规则", type = EditType.TAB_TABLE_ADD,
                    dependFieldDisplay = @DependFieldDisplay(notNull = "samplingStandard == 'customSampling'", showOrHide = "samplingStandard == 'customSampling'"))
    )
    private List<CustomSamplingPlan> customSamplingPlanList;
}
