package cec.jiutian.bc.modeler.enumration;

import cec.jiutian.bc.urm.domain.dictionary.handler.DictChoiceFetchHandler;
import cec.jiutian.bc.urm.domain.dictionary.service.DictionaryService;
import cec.jiutian.bc.urm.dto.MetaDictItem;
import cec.jiutian.core.frame.cache.FabosJsonCache;
import cec.jiutian.core.frame.cache.FabosJsonCacheLRU;
import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class LabViewTypeChoiceFetchHandler implements ChoiceFetchHandler {
    @Resource
    private DictionaryService dictionaryService;

    private final FabosJsonCache<List<VLModel>> dictCache = new FabosJsonCacheLRU<>(500);

    public LabViewTypeChoiceFetchHandler() {
    }

    @Override
    public List<VLModel> fetch(String[] params) {
        // 缓存Key的格式：类全限定名:字典编码
        String key = DictChoiceFetchHandler.class.getName() + ":" + params[0];
        long ttl = params.length >= 2 ? Long.parseLong(params[1]) : 30000L;
        // 固定生成的枚举
        List<VLModel> vlModelList = new ArrayList<>();
        VLModel vlModel1 = new VLModel("LM_TESTING_CENTER", "检测中心");
        VLModel vlModel2 = new VLModel("LM_PROCESS_LAB", "过程实验室");
        vlModelList.add(vlModel1);
        vlModelList.add(vlModel2);

        List<MetaDictItem> dictCode = dictionaryService.getDictItems(params[0]);
        if (CollectionUtils.isEmpty(dictCode)) {
            return vlModelList;
        }
        List<VLModel> newList = this.dictCache.getAndSet(key, ttl, () -> dictCode.stream()
                .map((item) -> new VLModel(item.getCode(), item.getName()))
                .toList());
        vlModelList.addAll(newList);
        return vlModelList;
    }

    @Override
    public boolean isDataBaseQuery() {
        return ChoiceFetchHandler.super.isDataBaseQuery();
    }
}
