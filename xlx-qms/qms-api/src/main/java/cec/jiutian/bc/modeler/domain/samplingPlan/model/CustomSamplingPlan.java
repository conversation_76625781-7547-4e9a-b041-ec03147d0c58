package cec.jiutian.bc.modeler.domain.samplingPlan.model;

import cec.jiutian.bc.modeler.domain.samplingPlan.proxy.CustomSamplingPlanProxy;
import cec.jiutian.bc.modeler.enumration.CustomSamplingTypeEnum;
import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.InputGroup;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.*;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025/3/14
 * @description TODO
 */
@Table(name = "bd_custom_sampling_plan_detail")
@FabosJson(
        name = "自定义抽样规则",
        orderBy = "CustomSamplingPlan.createTime desc",
        dataProxy = CustomSamplingPlanProxy.class
)
@Entity
@Getter
@Setter
public class CustomSamplingPlan extends BaseModel {

    @FabosJsonField(
            views = {
                    @View(title = "抽样方案编码", column = "generalCode")
            },
            edit = @Edit(title = "抽样方案", type = EditType.REFERENCE_TABLE,show = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode"))
    )
    @ManyToOne
    @JsonIgnoreProperties("customSamplingPlanList")
    private SamplingPlan samplingPlan;

    @FabosJsonField(
            views = @View(title = "取样方式"),
            edit = @Edit(title = "取样方式",type = EditType.CHOICE,search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = CustomSamplingTypeEnum.class))
    )
    private String customSamplingType;

    @FabosJsonField(
            views = @View(title = "固定抽样数量"),
            edit = @Edit(title = "固定抽样数量",
                    numberType = @NumberType(min = 0,max = 100,precision = 2),
                    dependFieldDisplay = @DependFieldDisplay(notNull = "customSamplingType == 'fixedSampling'", showOrHide = "customSamplingType == 'fixedSampling'"))
    )
    private Double fixedCount;

    @FabosJsonField(
            views = @View(title = "单位"),
            edit = @Edit(title = "单位",
                    dependFieldDisplay = @DependFieldDisplay(notNull = "customSamplingType == 'fixedSampling'", showOrHide = "customSamplingType == 'fixedSampling'"))
    )
    private String fixedCountUnit;

    @FabosJsonField(
            views = @View(title = "抽样比例"),
            edit = @Edit(title = "抽样比例",inputGroup = @InputGroup(postfix = "%"),
                    numberType = @NumberType(min = 0,max = 100,precision = 2),
                    dependFieldDisplay = @DependFieldDisplay(notNull = "customSamplingType == 'proportionSampling'", showOrHide = "customSamplingType == 'proportionSampling'"))
    )
    private Double proportion;

    @FabosJsonField(
            views = @View(title = "起始批数"),
            edit = @Edit(title = "起始批数",numberType = @NumberType(min = 0,max = 999999))
    )
    private Integer startLot;

    @FabosJsonField(
            views = @View(title = "终止批数"),
            edit = @Edit(title = "终止批数", numberType = @NumberType(min = 0,max = 999999))
    )
    private Integer endLot;
}
