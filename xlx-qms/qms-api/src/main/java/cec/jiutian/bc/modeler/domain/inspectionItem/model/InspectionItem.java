package cec.jiutian.bc.modeler.domain.inspectionItem.model;

import cec.jiutian.bc.generalModeler.domain.measureUnit.model.MeasureUnit;
import cec.jiutian.bc.modeler.domain.dto.SendPointMTO;
import cec.jiutian.bc.modeler.domain.inspectionItem.handler.ItemCodeGenerateDynamicHandler;
import cec.jiutian.bc.modeler.domain.inspectionItem.handler.ItemInValidateOperationHandler;
import cec.jiutian.bc.modeler.domain.inspectionItem.handler.ItemValidateOperationHandler;
import cec.jiutian.bc.modeler.domain.inspectionItem.proxy.InspectionItemProxy;
import cec.jiutian.bc.modeler.domain.inspectionMethod.model.InspectionMethod;
import cec.jiutian.bc.modeler.domain.samplingPlan.model.SamplingPlan;
import cec.jiutian.bc.modeler.enumration.ItemTypeEnum;
import cec.jiutian.bc.modeler.enumration.SendPointTypeEnum;
import cec.jiutian.bc.modeler.enumration.StatusEnum;
import cec.jiutian.bc.urm.domain.dictionary.handler.DictChoiceFetchHandler;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.*;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.RowBaseOperation;
import cec.jiutian.view.type.RowOperation;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.HashSet;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/4
 * @description TODO
 */
@FabosJson(
        name = "检验项目",
        orderBy = "InspectionItem.createTime desc",
        dataProxy = InspectionItemProxy.class,
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "status == 'Effective'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "status == 'Effective'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                )
        },
        rowOperation = {
                @RowOperation(
                        title = "生效",
                        code = "InspectionItem@EFFECTIVE",
                        operationHandler = ItemValidateOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "是否确定操作？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "InspectionItem@EFFECTIVE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "status == 'Effective'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "失效",
                        code = "InspectionItem@INVALID",
                        operationHandler = ItemInValidateOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "是否确定操作？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "InspectionItem@INVALID"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "status == 'Invalid'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                )
        }
)
@Table(name = "bd_inspection_item",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Getter
@Setter
@TemplateType(type = "multiTable")
public class InspectionItem extends MetaModel {

    @FabosJsonField(
            views = @View(title = "编号"),
            edit = @Edit(title = "编号", notNull = true,search = @Search),
            dynamicField = @DynamicField(dependFiled = @DependFiled(buttonName = "生成", changeBy = "id",
                    dynamicHandler = ItemCodeGenerateDynamicHandler.class))
    )
    @SubTableField
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "检验项目名称"),
            edit = @Edit(title = "检验项目名称", notNull = true, search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    @SubTableField
    private String name;

    @FabosJsonField(
            views = @View(title = "特性"),
            edit = @Edit(title = "特性",
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = DictChoiceFetchHandler.class,
                            fetchHandlerParams = "InspectionItemFeature")
            )
    )
    private String feature;

    @FabosJsonField(
            views = @View(title = "检验项类型"),
            edit = @Edit(title = "检验项类型", type = EditType.CHOICE, notNull = true,
                    inputType = @InputType(length = 40),
                    choiceType = @ChoiceType(fetchHandler = ItemTypeEnum.class))
    )
    @SubTableField
    private String itemType;

    @ManyToOne
    //@JoinColumn(foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT, name = "none"))
    @FabosJsonField(
            views = @View(title = "检验方法",
//                    show = false,
                    column = "name", type = ViewType.TABLE_FORM),
            edit = @Edit(title = "检验方法",
                    filter = @Filter(value = "InspectionMethod.status = 'Effective'"),
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    search = @Search()
            )
    )
    @SubTableField
    private InspectionMethod inspectionMethod;

    @FabosJsonField(
            views = @View(title = "管理方案"),
            edit = @Edit(title = "管理方案", search = @Search(vague = true))
    )
    @SubTableField
    private String manageMethod;

    @FabosJsonField(
            views = @View(title = "检验方法", show = false),
            edit = @Edit(title = "检验方法",
                    readonly = @Readonly(edit = true, add = true),
                    type = EditType.TEXTAREA),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(
                    changeBy = "inspectionMethod", beFilledBy = "name"))
    )
    @SubTableField
    private String method;

    @FabosJsonField(
            views = @View(title = "是否自动判断"),
            edit = @Edit(title = "是否自动判断", defaultVal = "false"
            )
    )
    private Boolean isAutoJudged;

    @FabosJsonField(
            views = @View(title = "包装方式"),
            edit = @Edit(title = "包装方式", notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = DictChoiceFetchHandler.class,
                            fetchHandlerParams = "packageType"),
                    search = @Search(vague = true)
            )
    )
    private String packageType;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "抽样方案",
//                    show = false,
                    column = "name",
                    type = ViewType.TABLE_FORM),
            edit = @Edit(title = "抽样方案",
                    type = EditType.REFERENCE_TABLE,
                    filter = @Filter(value = "SamplingPlan.status = 'Effective'"),
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    search = @Search()
            )
    )
    private SamplingPlan samplingPlan;

    @FabosJsonField(
            views = @View(title = "抽样方案", show = false),
            edit = @Edit(title = "抽样方案",
                    type = EditType.TEXTAREA,
                    readonly = @Readonly(edit = true, add = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(
                    changeBy = "samplingPlan", beFilledBy = "name"))
    )
    private String plan;

    @FabosJsonField(
            views = @View(title = "检验频次"),
            edit = @Edit(title = "检验频次", notNull = true,
                   inputGroup = @InputGroup(postfix = "#{accountUnit},/次"),
                   numberType = @NumberType(min = 1, max = 10))
    )
    private Integer inspectFrequency;

    @ManyToOne(cascade = CascadeType.DETACH)
    @FabosJsonField(
            views = @View(title = "检验频次单位", column = "unitChnName"),
            edit = @Edit(title = "检验频次单位",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "unitChnName",
                            type = ReferenceTableType.SelectShowTypeMTO.LIST, groupField = "unitType")
            )
    )
    private MeasureUnit accountUnit;

    @FabosJsonField(
            views = @View(title = "是否开线首检"),
            edit = @Edit(title = "是否开线首检", defaultVal = "false",notNull = true
            )
    )
    private Boolean isOpenLine;

    @FabosJsonField(
            views = @View(title = "是否开机首检"),
            edit = @Edit(title = "是否开机首检", defaultVal = "false",notNull = true
            )
    )
    private Boolean isOpenDevice;

    @FabosJsonField(
            views = @View(title = "是否原材料首检"),
            edit = @Edit(title = "是否原材料首检", defaultVal = "false",notNull = true
            )
    )
    private Boolean isRawMaterial;

    @FabosJsonField(
            views = @View(title = "是否结果等待"),
            edit = @Edit(title = "是否结果等待", defaultVal = "false"
            )
    )
    private Boolean isResultWaiting;

    @FabosJsonField(
            views = @View(title = "取样点"),
            edit = @Edit(title = "取样点", notNull = true,
                    inputType = @InputType(length = 40))
    )
    private String samplingPoint;

    @FabosJsonField(
            views = @View(title = "送检点类型"),
            edit = @Edit(title = "送检点类型",notNull = true, type = EditType.CHOICE, search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = SendPointTypeEnum.class))
    )
    private String sendPointType;

    @FabosJsonField(
            views = @View(title = "送检点",  column = "name"),
            edit = @Edit(title = "送检点",notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    queryCondition = "{\"type\":\"${sendPointType}\"}",
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType(type = TabTableReferType.SelectShowTypeMTM.LIST)
            )
    )
    @ManyToOne
    @JoinColumn(name = "send_point_mto_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private SendPointMTO sendPointMTO;

    @FabosJsonField(
            views = @View(title = "SIP编号"),
            edit = @Edit(title = "SIP编号", show = false)
    )
    @SubTableField
    private String sipName;

    @FabosJsonField(
            views = @View(title = "SIP版本"),
            edit = @Edit(title = "SIP版本", show = false)
    )
    @SubTableField
    private String sipVersion;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态", type = EditType.CHOICE, search = @Search(),
                    readonly = @Readonly(),defaultVal = "Effective",
                    choiceType = @ChoiceType(fetchHandler = StatusEnum.class)),
            dynamicField = @DynamicField(passive = true)
    )
    private String status;

    @FabosJsonField(
            views = @View(title = "备注"),
            edit = @Edit(title = "备注",
                    type = EditType.TEXTAREA)
    )
    private String remark;

    @FabosJsonField(
            views = @View(title = "所属检验组", show = false),
            edit = @Edit(title = "所属检验组", show = false)
    )
    private String groupId;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = false,fetch = FetchType.EAGER)
    @JoinColumn(name = "inspection_item_id")
    @FabosJsonField(
            views = @View(title = "检验项指标", type = ViewType.TABLE_VIEW),
            edit = @Edit(title = "检验项指标", type = EditType.TAB_TABLE_ADD)
    )
    private List<InspectionItemTarget> inspectionItemTargetList;



    @Transient
    private final static String separator = ",";

    /**
     * 添加组id
     * 以逗号分割
     * @param oldData
     * @param addData
     * @return
     */
    public static String addGroupId(String oldData, String addData) {
        if (StringUtils.isBlank(addData)){
            return oldData;
        }
        HashSet<String> ideSet = new HashSet<>();
        if (StringUtils.isBlank(oldData)) {
            return addData;
        } else {
            //去重
            String[] split = StringUtils.split(oldData, separator);
            for (String s : split) {
                ideSet.add(s);
            }
            ideSet.add(addData);
        }
        return StringUtils.join(ideSet, separator);
    }
}
