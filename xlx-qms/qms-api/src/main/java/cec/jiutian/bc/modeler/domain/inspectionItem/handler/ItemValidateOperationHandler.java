package cec.jiutian.bc.modeler.domain.inspectionItem.handler;

import cec.jiutian.bc.modeler.domain.inspectionItem.model.InspectionItem;
import cec.jiutian.bc.modeler.enumration.StatusEnum;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/28
 * @description TODO
 */
@Component
public class ItemValidateOperationHandler implements OperationHandler<InspectionItem,Void> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<InspectionItem> data, Void modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            InspectionItem inspectionItem = data.get(0);
            if (CollectionUtils.isEmpty(inspectionItem.getInspectionItemTargetList())) {
                throw new FabosJsonApiErrorTip("请先选择检验项指标");
            }
            inspectionItem.setStatus(StatusEnum.Enum.Effective.name());
            fabosJsonDao.mergeAndFlush(inspectionItem);
        }
        return "msg.success('操作成功')";
    }
}
