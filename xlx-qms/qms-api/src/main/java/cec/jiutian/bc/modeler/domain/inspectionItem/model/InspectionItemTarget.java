package cec.jiutian.bc.modeler.domain.inspectionItem.model;

import cec.jiutian.bc.modeler.domain.inspectionItem.handler.ComparisonMethodDynamicHandler;
import cec.jiutian.bc.modeler.domain.inspectionItem.proxy.InspectionItemTargetProxy;
import cec.jiutian.bc.modeler.enumration.ComparisonMethodEnum;
import cec.jiutian.bc.modeler.enumration.InspectionValueTypeEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.*;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025/3/4
 * @description TODO
 */
@Table(name = "bd_inspection_item_target")
@FabosJson(
        name = "检验项指标",
        orderBy = "InspectionItemTarget.createTime desc",
        dataProxy = InspectionItemTargetProxy.class
)
@Entity
@Getter
@Setter
public class InspectionItemTarget extends MetaModel {

    @FabosJsonField(
            views = {
                    @View(title = "检验项编码", column = "generalCode")
            },
            edit = @Edit(title = "检验项", type = EditType.REFERENCE_TABLE,show = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode"))
    )
    @ManyToOne
    @JsonIgnoreProperties("inspectionItemTargetList")
    private InspectionItem inspectionItem;

    @FabosJsonField(
            views = @View(title = "检验项指标名称"),
            edit = @Edit(title = "检验项指标名称",notNull = true, search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    private String name;

    @FabosJsonField(
            views = @View(title = "值类型"),
            edit = @Edit(title = "值类型",
                    type = EditType.CHOICE,
                    search = @Search(),
                    notNull = true,
                    choiceType = @ChoiceType(fetchHandler = InspectionValueTypeEnum.class))
    )
    private String inspectionValueType;

    @FabosJsonField(
            views = @View(title = "比较方式"),
            edit = @Edit(title = "比较方式",
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "inspectionValueType == 'number' || inspectionValueType == 'percentage'"),
                    search = @Search(),
                    type = EditType.CHOICE,
                    notNull = true,
                    choiceType = @ChoiceType(fetchHandler = ComparisonMethodEnum.class)),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "inspectionValueType",dynamicHandler = ComparisonMethodDynamicHandler.class))
    )
    private String comparisonMethod;

    @FabosJsonField(
            views = @View(title = "单位"),
            edit = @Edit(title = "单位",
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "inspectionValueType == 'number'"),
                    notNull = true,
                    inputType = @InputType(length = 40))
    )
    private String unit;

    @FabosJsonField(
            views = @View(title = "标准值"),
            edit = @Edit(title = "标准值",
                    notNull = true,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "inspectionValueType == 'text' || comparisonMethod == 'equal'"))
    )
    private String standardValue;

    @FabosJsonField(
            views = @View(title = "上限值"),
            edit = @Edit(title = "上限值",
                    notNull = true,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "comparisonMethod == 'range' || comparisonMethod == 'upperLimit'"),
                    numberType = @NumberType(min=0,max = 9999999,precision = 2))
    )
    private Double upperValue;

    @FabosJsonField(
            views = @View(title = "是否包含上限值"),
            edit = @Edit(title = "是否包含上限值", defaultVal = "true", show = false
            )
    )
    private Boolean isContainUpper;

    @FabosJsonField(
            views = @View(title = "下限值"),
            edit = @Edit(title = "下限值",
                    notNull = true,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "comparisonMethod == 'range' || comparisonMethod == 'lowerLimit'"),
                    numberType = @NumberType(min=0,max = 9999999,precision = 2))
    )
    private Double lowerValue;

    @FabosJsonField(
            views = @View(title = "是否包含下限值"),
            edit = @Edit(title = "是否包含下限值", defaultVal = "true", show = false
            )
    )
    private Boolean isContainLower;

    @FabosJsonField(
            views = @View(title = "描述"),
            edit = @Edit(title = "描述",
                    type = EditType.TEXTAREA)
    )
    private String description;

}
