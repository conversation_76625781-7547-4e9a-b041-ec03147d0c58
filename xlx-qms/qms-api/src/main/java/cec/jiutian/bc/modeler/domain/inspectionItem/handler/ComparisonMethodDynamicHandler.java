package cec.jiutian.bc.modeler.domain.inspectionItem.handler;

import cec.jiutian.bc.modeler.domain.inspectionItem.model.InspectionItemTarget;
import cec.jiutian.view.DependFiled;

import java.util.HashMap;
import java.util.Map;

public class ComparisonMethodDynamicHandler implements DependFiled.DynamicHandler<InspectionItemTarget> {
    @Override
    public Map<String, Object> handle(InspectionItemTarget inspectionItemTarget) {
        Map<String, Object> result = new HashMap<>();
        result.put("comparisonMethod","");
        return result;
    }
}
