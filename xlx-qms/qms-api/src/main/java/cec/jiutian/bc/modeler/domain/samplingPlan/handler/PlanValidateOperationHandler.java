package cec.jiutian.bc.modeler.domain.samplingPlan.handler;

import cec.jiutian.bc.modeler.domain.samplingPlan.model.SamplingPlan;
import cec.jiutian.bc.modeler.enumration.StatusEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/28
 * @description TODO
 */
@Component
public class PlanValidateOperationHandler implements OperationHandler<SamplingPlan,Void> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<SamplingPlan> data, Void modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            SamplingPlan samplingPlan = data.get(0);
            samplingPlan.setStatus(StatusEnum.Enum.Effective.name());
            fabosJsonDao.mergeAndFlush(samplingPlan);
        }
        return "msg.success('操作成功')";
    }
}
