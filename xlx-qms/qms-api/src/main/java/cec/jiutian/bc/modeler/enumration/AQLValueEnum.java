package cec.jiutian.bc.modeler.enumration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/14
 * @description TODO
 */
public class AQLValueEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum data : Enum.values()) {
            list.add(new VLModel(data.name(), data.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        first("0.010"),
        second("0.015"),
        third("0.025"),
        fourth("0.040"),
        fifth("0.065"),
        sixth("0.10"),
        seventh("0.15"),
        eighth("0.25"),
        ninth("0.40"),
        tenth("0.65"),
        eleventh("1.0"),
        twelfth("1.5"),
        thirteenth("2.5"),
        fourteenth("4.0"),
        fifteenth("6.5"),
        sixteenth("10"),
        seventeenth("15"),
        ;

        private final String value;

    }
}
