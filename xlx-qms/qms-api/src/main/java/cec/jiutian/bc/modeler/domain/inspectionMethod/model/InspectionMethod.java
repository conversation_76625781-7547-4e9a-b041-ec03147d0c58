package cec.jiutian.bc.modeler.domain.inspectionMethod.model;


import cec.jiutian.bc.modeler.domain.inspectionInstrument.model.InspectionInstrument;
import cec.jiutian.bc.modeler.domain.inspectionMethod.handler.CodeGenerateDynamicHandler;
import cec.jiutian.bc.modeler.domain.inspectionMethod.handler.MethodInValidateOperationHandler;
import cec.jiutian.bc.modeler.domain.inspectionMethod.handler.MethodValidateOperationHandler;
import cec.jiutian.bc.modeler.domain.inspectionMethod.proxy.InspectionMethodProxy;
import cec.jiutian.bc.modeler.enumration.StatusEnum;
import cec.jiutian.bc.urm.domain.dictionary.handler.DictChoiceFetchHandler;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.field.edit.TabTableReferType;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.RowBaseOperation;
import cec.jiutian.view.type.RowOperation;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/27
 * @description TODO
 */
@FabosJson(
        name = "检验方法",
        orderBy = "InspectionMethod.createTime desc",
        dataProxy = InspectionMethodProxy.class,
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "status == 'Effective'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "status == 'Effective'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                )
        },
        rowOperation = {
                @RowOperation(
                        title = "生效",
                        code = "InspectionMethod@EFFECTIVE",
                        operationHandler = MethodValidateOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "是否确定操作？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "InspectionMethod@EFFECTIVE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "status == 'Effective'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE
                ),
                @RowOperation(
                        title = "失效",
                        code = "InspectionMethod@INVALID",
                        operationHandler = MethodInValidateOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "是否确定操作？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "InspectionMethod@INVALID"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "status == 'Invalid'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE
                )
        }
)
@Table(name = "bd_inspection_method",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Getter
@Setter
public class InspectionMethod extends MetaModel {

    @FabosJsonField(
            views = @View(title = "方法编码"),
            edit = @Edit(title = "方法编码", search = @Search(vague = true), notNull = true),
            dynamicField = @DynamicField(dependFiled = @DependFiled(buttonName = "生成", changeBy = "id",
            dynamicHandler = CodeGenerateDynamicHandler.class))
    )
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "检验方法名称"),
            edit = @Edit(title = "检验方法名称", search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    private String name;

    @FabosJsonField(
            views = @View(title = "类型"),
            edit = @Edit(title = "类型",
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = DictChoiceFetchHandler.class,
                            fetchHandlerParams = "DetectionTaskCreationType"),
                    search = @Search(vague = true)
            )
    )
    private String type;

    @ManyToMany
    @JoinTable(
            name = "e_method_instrument", //中间表表名
            inverseForeignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT),
            joinColumns = @JoinColumn(name = "inspection_method_id", referencedColumnName = "id"),
            inverseJoinColumns = @JoinColumn(name = "inspection_instrument_id", referencedColumnName = "id"))
    @FabosJsonField(
            views = @View(title = "检验仪器",type = ViewType.TABLE_VIEW),
            edit = @Edit(title = "检验仪器", type = EditType.TAB_TABLE_REFER, search = @Search(vague = true),
                    tabTableReferType = @TabTableReferType(type = TabTableReferType.SelectShowTypeMTM.LIST,
                            label = "name"),
                    filter = @Filter(value = "InspectionInstrument.status = 'Effective'")
            )
    )
    private List<InspectionInstrument> inspectionInstrumentList;

    @FabosJsonField(
            views = @View(title = "是否为破坏性检验",type = ViewType.BOOLEAN),
            edit = @Edit(title = "是否为破坏性检验",notNull = true,search = @Search(vague = true))
    )
    private Boolean isDestructiveTest;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态", type = EditType.CHOICE, search = @Search(),
                    readonly = @Readonly(),defaultVal = "Effective",
                    choiceType = @ChoiceType(fetchHandler = StatusEnum.class)),
            dynamicField = @DynamicField(passive = true)
    )
    private String status;

    @FabosJsonField(
            views = @View(title = "检验方法步骤描述"),
            edit = @Edit(title = "检验方法步骤描述",
                    type = EditType.TEXTAREA)
    )
    private String inspectStepDescription;

    @FabosJsonField(
            views = @View(title = "备注"),
            edit = @Edit(title = "备注",
                    type = EditType.TEXTAREA)
    )
    private String remark;
}
