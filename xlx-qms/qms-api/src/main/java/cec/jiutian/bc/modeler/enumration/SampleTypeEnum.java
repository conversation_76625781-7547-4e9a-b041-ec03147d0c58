package cec.jiutian.bc.modeler.enumration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 样品类型  和lims的需要保持一致 cec.jiutian.bc.process.enumeration.SampleTypeEnum
 * <AUTHOR>
 * @date 2025/2/27 17:43
 */
public class SampleTypeEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum value : Enum.values()) {
            list.add(new VLModel(value.name(), value.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        PRO_SAMPLING("生产取样"),
        DEV_SAMPLING("来料取样"),
        PRODUCT_SAMPLING("成品取样"),
        INVENTORY_SAMPLING("库存取样"),
        REFUND_SAMPLING("成品退货取样"),
        //以下类型来自lims 如果lims添加了新类型，请自行添加。还需要处理OtherSamplingTask模型的@Filter
        QC_SAMPLING("质控生产取样"),
        QC_INVENTORY_SAMPLING("质控库存取样"),
        QC_KEEP_SAMPLING("质控留样取样"),

        BENCH_SAMPLING("对标生产取样"),
        BENCH_INVENTORY_SAMPLING("对标库存取样"),
        KEEP_SAMPLING("对标留样取样"),
        EXT_SAMPLING("对标外部样品取样"),
        // 样品发货只创建样品任务 不创建检验任务
        SAMP_SHIPMENT("对标样品发货取样"),


        MSA_SAMPLING("MSA生产取样"),
        MSA_INVENTORY_SAMPLING("MSA库存取样"),
        MSA_KEEP_SAMPLING("MSA留样取样"),

        ;
        private final String value;

    }
}

