package cec.jiutian.bc.modeler.enumration;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/2/27
 * @description TODO
 */
@AllArgsConstructor
@Getter
public enum NamingRuleCodeEnum {
    InspectionMethodCode("检验方法"),
    InspectionInstrument("检验仪器"),
    SamplingPlan("抽样方案"),
    PatrolInspectionItem("巡检项"),
    InspectionItem("检验项目"),
    QuantityPreparationPlan("质量准备计划"),
    InspectionItemGroup("检验项目组"),
    InspectionTask("来料检验任务"),
    SamplingTask("取样任务"),
    InspectionStandard("质检标准"),
    OnSiteInspectionStandard("巡检标准"),
    OnSiteInspectionPlan("巡检计划"),
    IssueList("问题清单"),
    MRBUnqualifiedReview("MRB不合格评审"),
    UNQUALIFIED_REVIEW_TASK("MRB不合格任务"),
    AuditItemManage("审核项管理"),
    SystemAuditTemplate("体系审核模板"),
    AuditAnnualPlan("审核年度计划"),
    ExamineImplementPlan("产品审核实施计划"),
    ProcessAuditManage("过程审核管理"),
    LayeredAuditManage("分层审核项管理"),
    SpecialMaterialHandleRequest("特殊物料处理申请单"),
    Report8D("8D"),
    CMKPlan("CMK计划"),
    CMKInspectionTask("CMK检验任务"),
    CMKSampleTask("CMK取样任务"),
    PPKPlan("PPK计划"),
    PPKInspectionTask("PPK检验任务"),
    PPKSampleTask("PPK取样任务"),
    ProductExamineInspectionTask("产品审核检验任务"),
    ExamineReport("审核报告"),
    CustomerReviewTask("客户评审任务"),
    ABNORMAL_FEEDBACK_HANDLING("异常反馈处理"),
    ;

    private final String value;
}
