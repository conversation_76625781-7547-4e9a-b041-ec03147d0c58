package cec.jiutian.bc.modeler.domain.inspectionItem.handler;

import cec.jiutian.bc.modeler.domain.inspectionItem.model.InspectionItem;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cec.jiutian.bc.modeler.enumration.StatusEnum;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/28
 * @description TODO
 */
@Component
public class ItemInValidateOperationHandler implements OperationHandler<InspectionItem, Void> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<InspectionItem> data, Void modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            InspectionItem inspectionItem = data.get(0);
            inspectionItem.setStatus(StatusEnum.Enum.Invalid.name());
            fabosJsonDao.mergeAndFlush(inspectionItem);
        }
        return "msg.success('操作成功')";
    }
}
