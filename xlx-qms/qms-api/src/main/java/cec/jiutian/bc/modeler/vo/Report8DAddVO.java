package cec.jiutian.bc.modeler.vo;

import cec.jiutian.bc.urm.domain.dictionary.handler.DictChoiceFetchHandler;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class Report8DAddVO{

    // 题目
    @FabosJsonField(
            views = @View(title = "题目"),
            edit = @Edit(
                    title = "题目",
                    inputType = @InputType(length = 20),
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    private String title;

    // 投诉类型
    // 投诉类型
    @FabosJsonField(
            views = @View(title = "投诉类型"),
            edit = @Edit(
                    title = "投诉类型",
                    inputType = @InputType(length = 20),
                    notNull = true,
                    search = @Search,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(
                            fetchHandler = {DictChoiceFetchHandler.class},
                            fetchHandlerParams = {"ComplaintType"}
                    )
            )
    )
    private String complaintType;

    // 发生时间
    @FabosJsonField(
            views = @View(title = "发生时间", type = ViewType.DATE_TIME),
            edit = @Edit(
                    title = "发生时间",
                    notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date occurrenceTime;


    @FabosJsonField(
            views = @View(title = "产品批号"),
            edit = @Edit(
                    title = "产品批号",
                    inputType = @InputType(length = 20),
                    notNull = true,
                    readonly = @Readonly,
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "productMTO", beFilledBy = "lotSerialId"))
    )
    private String productCode;


    // 产品名称
    @FabosJsonField(
            views = @View(title = "产品名称"),
            edit = @Edit(
                    title = "产品名称",
                    inputType = @InputType(length = 20),
                    notNull = true,
                    readonly = @Readonly,
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "productMTO", beFilledBy = "materialName"))
    )
    private String productName;

    // 客户
    @FabosJsonField(
            views = @View(title = "客户"),
            edit = @Edit(
                    title = "客户",
                    inputType = @InputType(length = 20),
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    private String customer;

    // 总批量数
    @FabosJsonField(
            views = @View(title = "总批量数"),
            edit = @Edit(
                    title = "总批量数",
                    numberType = @NumberType(min = 0),
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    private Integer totalBatchQuantity;

    // 提出日期
    @FabosJsonField(
            views = @View(title = "提出日期", type = ViewType.DATE_TIME),
            edit = @Edit(
                    title = "提出日期",
                    notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date raisedDate;

    // 供应商
    @FabosJsonField(
            views = @View(title = "供应商"),
            edit = @Edit(
                    title = "供应商",
                    inputType = @InputType(length = 20),
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    private String supplier;

    // 抽样数
    @FabosJsonField(
            views = @View(title = "抽样数"),
            edit = @Edit(
                    title = "抽样数",
                    numberType = @NumberType(min = 0),
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    private Integer sampleQuantity;


    @FabosJsonField(
            views = @View(title = "提出人"),
            edit = @Edit(
                    title = "提出人",
                    inputType = @InputType(length = 20),
                    readonly = @Readonly(add = true, edit = true),
                    notNull = true,
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "reporterUserMTO", beFilledBy = "name"))
    )
    private String reporter;

    @FabosJsonField(
            views = @View(title = "提出人ID", show = false),
            edit = @Edit(
                    show = false,
                    title = "提出人ID",
                    inputType = @InputType(length = 50)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "reporterUserMTO", beFilledBy = "id"))
    )
    private String reporterId;



    @FabosJsonField(
            views = @View(title = "联络人"),
            edit = @Edit(
                    title = "联络人",
                    inputType = @InputType(length = 20),
                    readonly = @Readonly(add = true, edit = true),
                    notNull = true,
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "contactUserMTO", beFilledBy = "name"))
    )
    private String contactPerson;

    @FabosJsonField(
            views = @View(title = "联络人ID", show = false),
            edit = @Edit(
                    show = false,
                    title = "联络人ID",
                    inputType = @InputType(length = 50)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "contactUserMTO", beFilledBy = "id"))
    )
    private String contactPersonId;

    // 不良数量
    @FabosJsonField(
            views = @View(title = "不良数量"),
            edit = @Edit(
                    title = "不良数量",
                    numberType = @NumberType(min = 0),
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    private Integer defectiveCount;

    // 要求完成日期
    @FabosJsonField(
            views = @View(title = "要求完成日期", type = ViewType.DATE_TIME),
            edit = @Edit(
                    title = "要求完成日期",
                    notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date requiredCompletionDate;

    // 发生地点
    @FabosJsonField(
            views = @View(title = "发生地点"),
            edit = @Edit(
                    title = "发生地点",
                    inputType = @InputType(length = 20),
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    private String occurrenceLocation;

    @FabosJsonField(
            views = @View(title = "8D附件"),
            edit = @Edit(title = "8D附件",
                    inputType = @InputType(length = 300),
                    type = EditType.ATTACHMENT,
                    tips = "最大支持100M文件",
                    attachmentType = @AttachmentType(
                            size = 100 * 1024,
                            maxLimit = 1,
                            type = AttachmentType.Type.BASE)
            )
    )
    private String d8Attachment;
}
