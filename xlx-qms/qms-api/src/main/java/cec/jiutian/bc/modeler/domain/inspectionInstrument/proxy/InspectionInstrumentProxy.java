package cec.jiutian.bc.modeler.domain.inspectionInstrument.proxy;

import cec.jiutian.bc.generalModeler.util.StringUtils;
import cec.jiutian.bc.modeler.domain.inspectionInstrument.model.InspectionInstrument;
import cec.jiutian.bc.modeler.enumration.StatusEnum;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/2/27
 * @description TODO
 */
@Component
public class InspectionInstrumentProxy implements DataProxy<InspectionInstrument> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public void beforeAdd(InspectionInstrument inspectionInstrument) {
        checkName(inspectionInstrument);
        inspectionInstrument.setStatus(StatusEnum.Enum.Effective.name());
    }

    @Override
    public void beforeUpdate(InspectionInstrument inspectionInstrument) {
        if (StatusEnum.Enum.Effective.name().equals(inspectionInstrument.getStatus())) {
            throw new FabosJsonApiErrorTip("生效状态不可执行该操作");
        }
        checkName(inspectionInstrument);
    }

    @Override
    public void beforeDelete(InspectionInstrument inspectionInstrument) {
        if (StatusEnum.Enum.Effective.name().equals(inspectionInstrument.getStatus())) {
            throw new FabosJsonApiErrorTip("生效状态不可执行该操作");
        }
    }

    private void checkName(InspectionInstrument inspectionInstrument) {
        InspectionInstrument condition = new InspectionInstrument();
        condition.setName(inspectionInstrument.getName());
        InspectionInstrument data = fabosJsonDao.selectOne(condition);
        if (data != null) {
            if (data.getName().equals(inspectionInstrument.getName())) {
                if (StringUtils.isEmpty(inspectionInstrument.getId())) {
                    throw new FabosJsonApiErrorTip("检验仪器名称不可重复，请确认");
                }else {
                    if (!inspectionInstrument.getId().equals(data.getId())) {
                        throw new FabosJsonApiErrorTip("检验仪器名称不可重复，请确认");
                    }
                }
            }
        }
    }
}
