package cec.jiutian.bc.modeler.domain.samplingPlan.repository;

import cec.jiutian.bc.modeler.domain.samplingPlan.model.SamplingPlan;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SamplingPlanRepository extends JpaRepository<SamplingPlan, String> {

    List<SamplingPlan> findByName(String name);
}
