//package cec.jiutian.bc.modeler.domain.dto;
//
//import cec.jiutian.core.view.fabosJson.FabosJson;
//import cec.jiutian.view.type.Power;
//import jakarta.persistence.Entity;
//import jakarta.persistence.Table;
//import lombok.Getter;
//
///**
// * <AUTHOR>
// * @date 2025/3/25
// * @description TODO
// */
//@Entity
//@Table(name = "ms_spcfcn")
//@Getter
//@FabosJson(
//        name = "物资编码",
//        orderBy = "createTime desc",
//        power = @Power(add = false, edit = false, delete = false, print = false, importable = false)
//)
//public class SpecificationManage {
//}
