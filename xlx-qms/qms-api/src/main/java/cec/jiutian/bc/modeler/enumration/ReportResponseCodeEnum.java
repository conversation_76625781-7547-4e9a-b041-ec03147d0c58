package cec.jiutian.bc.modeler.enumration;

/**
 * <AUTHOR>
 * @date 2025/7/7
 * @description TODO
 */
public enum ReportResponseCodeEnum {

    SUCCESS("0", "调用成功");

    private final String code;
    private final String description;

    ReportResponseCodeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
