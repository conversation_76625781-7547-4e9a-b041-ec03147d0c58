package cec.jiutian.bc.modeler.domain.samplingPlan.proxy;

import cec.jiutian.bc.generalModeler.util.StringUtils;
import cec.jiutian.bc.modeler.domain.dto.MesUnit;
import cec.jiutian.bc.modeler.domain.samplingPlan.model.SamplingPlan;
import cec.jiutian.bc.modeler.enumration.StatusEnum;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/2/28
 * @description TODO
 */
@Component
public class SamplingPlanProxy implements DataProxy<SamplingPlan> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public void beforeAdd(SamplingPlan samplingPlan) {
        checkName(samplingPlan);
    }

    @Override
    public void beforeUpdate(SamplingPlan samplingPlan) {
        if (StatusEnum.Enum.Effective.name().equals(samplingPlan.getStatus())) {
            throw new FabosJsonApiErrorTip("生效状态不可执行该操作");
        }
        checkName(samplingPlan);
    }

    @Override
    public void beforeDelete(SamplingPlan samplingPlan) {
        if (StatusEnum.Enum.Effective.name().equals(samplingPlan.getStatus())) {
            throw new FabosJsonApiErrorTip("生效状态不可执行该操作");
        }
    }

    @Override
    public void afterSingleFetch(Map<String, Object> map) {
        if (map.get("unitId") != null) {
            String unitId = map.get("unitId").toString();
            MesUnit mesUnit = fabosJsonDao.findById(MesUnit.class, unitId);
            if (mesUnit != null) {
                map.put("unit", mesUnit);
                map.put("unit_name", mesUnit.getName());
            }
        }
    }

    private void checkName(SamplingPlan samplingPlan) {
        SamplingPlan condition = new SamplingPlan();
        condition.setName(samplingPlan.getName());
        SamplingPlan data = fabosJsonDao.selectOne(condition);
        if (data != null) {
            if (data.getName().equals(samplingPlan.getName())) {
                if (StringUtils.isEmpty(samplingPlan.getId())) {
                    throw new FabosJsonApiErrorTip("抽样方案名称不可重复，请确认");
                }else {
                    if (!samplingPlan.getId().equals(data.getId())) {
                        throw new FabosJsonApiErrorTip("抽样方案名称不可重复，请确认");
                    }
                }
            }
        }
    }
}
