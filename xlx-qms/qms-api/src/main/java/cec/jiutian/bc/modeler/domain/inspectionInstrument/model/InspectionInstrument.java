package cec.jiutian.bc.modeler.domain.inspectionInstrument.model;

import cec.jiutian.bc.generalModeler.domain.material.model.MaterialCategory;
import cec.jiutian.bc.modeler.domain.inspectionInstrument.handler.InstrumentCodeGenerateDynamicHandler;
import cec.jiutian.bc.modeler.domain.inspectionInstrument.handler.InstrumentInValidateOperationHandler;
import cec.jiutian.bc.modeler.domain.inspectionInstrument.handler.InstrumentValidateOperationHandler;
import cec.jiutian.bc.modeler.domain.inspectionInstrument.proxy.InspectionInstrumentProxy;
import cec.jiutian.bc.modeler.enumration.StatusEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.*;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.LinkTree;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowBaseOperation;
import cec.jiutian.view.type.RowOperation;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025/2/27
 * @description TODO
 */
@FabosJson(
        name = "检验仪器",
        orderBy = "InspectionInstrument.createTime desc",
        linkTree = @LinkTree(field = "materialCategory"),
        dataProxy = InspectionInstrumentProxy.class,
        power = @Power(importable = true, export = true),
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "status == 'Effective'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "status == 'Effective'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                )
        },
        rowOperation = {
                @RowOperation(
                        title = "生效",
                        code = "InspectionInstrument@EFFECTIVE",
                        operationHandler = InstrumentValidateOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "是否确定操作？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "InspectionInstrument@EFFECTIVE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "status == 'Effective'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE
                ),
                @RowOperation(
                        title = "失效",
                        code = "InspectionInstrument@INVALID",
                        operationHandler = InstrumentInValidateOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "是否确定操作？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "InspectionInstrument@INVALID"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "status == 'Invalid'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE
                )
        }
)
@Table(name = "bd_inspection_instrument",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Getter
@Setter
public class InspectionInstrument extends MetaModel {

        @FabosJsonField(
                views = @View(title = "检验仪器编号"),
                edit = @Edit(title = "检验仪器编号", notNull = true, search = @Search(vague = true)),
                dynamicField = @DynamicField(dependFiled = @DependFiled(buttonName = "生成", changeBy = "id",
                        dynamicHandler = InstrumentCodeGenerateDynamicHandler.class))
        )
        private String generalCode;

        @FabosJsonField(
                views = @View(title = "检验仪器名称"),
                edit = @Edit(title = "检验仪器名称",notNull = true, search = @Search(vague = true),
                        inputType = @InputType(length = 40))
        )
        private String name;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "物资分类", column = "name"),
            edit = @Edit(title = "物资分类", readonly = @Readonly(add = false, edit = true), notNull = true,
                    type = EditType.REFERENCE_TREE,
                    referenceTreeType = @ReferenceTreeType(pid = "parentCategory.id"),
                    search = @Search())
    )
    private MaterialCategory materialCategory;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态", type = EditType.CHOICE, search = @Search(),
                    readonly = @Readonly(),defaultVal = "Effective",
                    choiceType = @ChoiceType(fetchHandler = StatusEnum.class)),
            dynamicField = @DynamicField(passive = true)
    )
    private String status;

    @FabosJsonField(
            views = @View(title = "使用方法"),
            edit = @Edit(title = "使用方法",
                    type = EditType.TEXTAREA)
    )
    private String usage;
}
