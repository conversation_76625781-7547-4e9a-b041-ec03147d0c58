package cec.jiutian.bc.modeler.domain.inspectionInstrument.handler;

import cec.jiutian.bc.modeler.domain.inspectionInstrument.model.InspectionInstrument;
import cec.jiutian.bc.modeler.enumration.StatusEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/28
 * @description TODO
 */
@Component
public class InstrumentInValidateOperationHandler implements OperationHandler<InspectionInstrument, Void> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<InspectionInstrument> data, Void modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            InspectionInstrument inspectionInstrument = data.get(0);
            inspectionInstrument.setStatus(StatusEnum.Enum.Invalid.name());
            fabosJsonDao.mergeAndFlush(inspectionInstrument);
        }
        return "msg.success('操作成功')";
    }
}
