package cec.jiutian.bc.modeler.domain.dto;

import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.View;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025/4/25
 * @description TODO
 */
@FabosJson(
        name = "库存台账MTO"
)
@Entity
@Getter
@Setter
@Table(name = "lab_view")
public class SendPointMTO {

    @Id
    @FabosJsonField(
            edit = @Edit(title = "", show = false)
    )
    private String id;

    @FabosJsonField(
            views = @View(title = "送检点编码"),
            edit = @Edit(title = "送检点编码")
    )
    private String code;

    @FabosJsonField(
            views = @View(title = "送检点名称"),
            edit = @Edit(title = "送检点名称")
    )
    private String name;

    @FabosJsonField(
            views = @View(title = "送检点类型",show = false),
            edit = @Edit(title = "送检点类型", show = false)
    )
    private String type;
}
