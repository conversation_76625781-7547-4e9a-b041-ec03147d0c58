package cec.jiutian.bc.modeler.domain.inspectionMethod.proxy;

import cec.jiutian.bc.generalModeler.util.StringUtils;
import cec.jiutian.bc.modeler.domain.inspectionMethod.model.InspectionMethod;
import cec.jiutian.bc.modeler.enumration.StatusEnum;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/2/27
 * @description TODO
 */
@Component
public class InspectionMethodProxy implements DataProxy<InspectionMethod> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public void beforeAdd(InspectionMethod inspectionMethod) {
        checkName(inspectionMethod);
    }

    @Override
    public void beforeUpdate(InspectionMethod inspectionMethod) {
        if (StatusEnum.Enum.Effective.name().equals(inspectionMethod.getStatus())) {
            throw new FabosJsonApiErrorTip("生效状态不可执行该操作");
        }
        checkName(inspectionMethod);
    }

    @Override
    public void beforeDelete(InspectionMethod inspectionMethod) {
        if (StatusEnum.Enum.Effective.name().equals(inspectionMethod.getStatus())) {
            throw new FabosJsonApiErrorTip("生效状态不可执行该操作");
        }
    }

    private void checkName(InspectionMethod inspectionMethod) {
        InspectionMethod condition = new InspectionMethod();
        condition.setName(inspectionMethod.getName());
        InspectionMethod data = fabosJsonDao.selectOne(condition);
        if (data != null) {
            if (data.getName().equals(inspectionMethod.getName())) {
                if (StringUtils.isEmpty(inspectionMethod.getId())) {
                    throw new FabosJsonApiErrorTip("检验方法名称不可重复，请确认");
                }else {
                    if (!inspectionMethod.getId().equals(data.getId())) {
                        throw new FabosJsonApiErrorTip("检验方法名称不可重复，请确认");
                    }
                }
            }
        }
    }
}
