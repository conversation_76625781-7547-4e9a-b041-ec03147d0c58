<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cec.jiutian</groupId>
        <artifactId>xlx-qms</artifactId>
        <version>3.2.0</version>
    </parent>
    <version>3.2.0</version>
    <artifactId>qms-app</artifactId>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>cec.jiutian</groupId>
            <artifactId>fabos-component-manage</artifactId>
            <version>${fabos.framework.version}</version>
        </dependency>
        <dependency>
            <groupId>cec.jiutian</groupId>
            <artifactId>fabos-cmp-urm-biz</artifactId>
            <version>${fabos.framework.version}</version>
        </dependency>
        <dependency>
            <groupId>cec.jiutian</groupId>
            <artifactId>fabos-cmp-job-biz</artifactId>
            <version>${fabos.framework.version}</version>
        </dependency>
        <dependency>
            <groupId>cec.jiutian</groupId>
            <artifactId>fabos-cmp-lcp-biz</artifactId>
            <version>${fabos.framework.version}</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>cec.jiutian</groupId>-->
<!--            <artifactId>general-modeler-biz</artifactId>-->
<!--            <version>${fabos.framework.version}</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>cec.jiutian</groupId>
            <artifactId>fabos-cmp-workflow-biz</artifactId>
            <version>${fabos.framework.version}</version>
        </dependency>

        <dependency>
            <groupId>cec.jiutian</groupId>
            <artifactId>fabos-ie</artifactId>
            <version>${fabos.framework.version}</version>
        </dependency>

        <dependency>
            <groupId>cec.jiutian</groupId>
            <artifactId>qms-api</artifactId>
            <version>3.2.0</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>cec.jiutian</groupId>
            <artifactId>fabos-cmp-ecs-biz-api</artifactId>
            <version>${fabos.framework.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>5.2.3</version>
        </dependency>

        <!-- Apache POI OOXML支持 -->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>5.2.3</version>
        </dependency>

        <dependency>
            <groupId>commons-beanutils</groupId>
            <artifactId>commons-beanutils</artifactId>
            <version>1.9.4</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>repackage</id>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                        <configuration>
                            <!--	fix https://github.com/koupleless/koupleless/issues/161		-->
                            <loaderImplementation>CLASSIC</loaderImplementation>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <!--            为了让三方依赖和 koupleless 模式适配，需要引入以下构建插件-->
            <plugin>
                <groupId>com.alipay.sofa.koupleless</groupId>
                <artifactId>koupleless-base-build-plugin</artifactId>
                <version>2.1.1</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>add-patch</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
