CREATE OR REPLACE  VIEW "sample_task_view" AS  SELECT mi_sampling_task.id,
    mi_sampling_task.general_code,
    mi_sampling_task.material_name,
    mi_sampling_task.material_code,
    mi_sampling_task.material_specification AS model_spec,
    mi_sampling_task.send_sample_date AS sample_time,
    mi_sampling_task.all_inspection_item_quantity AS amount,
    mi_sampling_task.inspection_task_code,
    'WAREHOUSE'::character varying AS form_type
   FROM mi_sampling_task
  WHERE mi_sampling_task.business_state::text = 'RECEIVED_SAMPLE'::text
UNION ALL
 SELECT pi_sample_task.id,
    pi_sample_task.general_code,
    pi_sample_task.product_name AS material_name,
    pi_sample_task.product_code AS material_code,
    '-'::character varying AS model_spec,
    pi_sample_task.send_sample_date AS sample_time,
    pi_sample_task.sample_quantity AS amount,
    '-'::character varying AS inspection_task_code,
    'LINE_SIDE_WAREHOUSE'::character varying AS form_type
   FROM pi_sample_task
  WHERE pi_sample_task.business_state::text = 'RECEIVED_SAMPLE'::text;
ALTER TABLE "sample_task_view" OWNER TO "xlx_qms";

CREATE OR REPLACE  VIEW "lab_view" AS  SELECT fdi.id,
    fdi.code,
    fdi.name,
    'OTHER'::character varying AS type
   FROM fd_dict_item fdi
     LEFT JOIN fd_dict fd ON fd.id::text = fdi.dict_id::text
  WHERE fd.code::text = 'OtherSendPoint'::text
UNION ALL
 SELECT lm_lab.id,
    lm_lab.lab_code AS code,
    lm_lab.lab_name AS name,
    lm_lab.type
   FROM lm_lab;
ALTER TABLE "lab_view" OWNER TO "xlx_qms";

CREATE OR REPLACE  VIEW "special_material_source" AS  SELECT bsi.id::character varying(255) AS id,
    '线边库物资'::text AS source,
    ''::character varying AS order_code,
    bsi.lot_srlzd_id AS lot_serial_id,
    bsi.spcfcn_cd AS material_code,
    bsi.spcfcn_nm AS material_name,
    bsi.vld_stk_qt AS inventory_quantity,
    fu.unt_nm AS unit,
    ms.incoming_materials_level AS material_level
   FROM bm_stk_ivntry bsi
     LEFT JOIN fr_unt fu ON fu.unt_cd::text = bsi.ivntry_unt_cd::text
     LEFT JOIN ms_spcfcn ms ON ms.spcfcn_cd::text = bsi.spcfcn_cd::text AND ms.spcfcn_nm::text = bsi.spcfcn_nm::text
UNION ALL
 SELECT lsr.id,
    '实验室归还样品'::text AS source,
    lsr.general_code AS order_code,
    lsr.material_batch_no AS lot_serial_id,
    lsr.material_code,
    lsr.material_name,
    lsr.return_amount AS inventory_quantity,
    lsr.unit,
    ms.incoming_materials_level AS material_level
   FROM lm_sample_return lsr
     LEFT JOIN ms_spcfcn ms ON ms.spcfcn_cd::text = lsr.material_code::text AND ms.spcfcn_nm::text = lsr.material_name::text;
ALTER TABLE "special_material_source" OWNER TO "xlx_qms";

CREATE OR REPLACE  VIEW "cpm_related_document_view" AS  SELECT fdi.id,
    fdi.code,
    fdi.name,
    'ACTIVE'::character varying AS status,
    'OTHER'::character varying AS type
   FROM fd_dict_item fdi
     LEFT JOIN fd_dict fd ON fd.id::text = fdi.dict_id::text
  WHERE fd.code::text = 'OtherRelatedDocument'::text
UNION ALL
 SELECT spr.id,
    spr.general_code AS code,
    spr.general_code AS name,
    'no_status'::character varying AS status,
    'ProblemRectification'::character varying AS type
   FROM qms_supplier_problem_rectification spr
UNION ALL
 SELECT pei.id,
    pei.general_code AS code,
    pei.general_code AS name,
    pei.business_state AS status,
    'ExamineImplementPlan'::character varying AS type
   FROM qms_pem_product_examine_implement_plan pei
  WHERE pei.business_state::text = 'EXECUTE'::text
UNION ALL
 SELECT loi.id,
    loi.list_of_issue_form_number AS code,
    loi.list_of_issue_form_number AS name,
    loi.business_status AS status,
    'ProcessAuditListOfIssue'::character varying AS type
   FROM qms_pam_list_of_issue loi
  WHERE loi.business_status::text = 'PROCESSING'::text
UNION ALL
 SELECT rph.id,
    rph.general_code AS code,
    rph.general_code AS name,
    rph.analysis_method AS status,
    'ReturnProductHandling'::character varying AS type
   FROM qem_return_product_handling rph
  WHERE rph.analysis_method::text = 'Measure'::text
UNION ALL
 SELECT abfd.id,
    abfd.abnormal_feedback_handling_form_number AS code,
    abfd.abnormal_feedback_handling_form_number AS name,
    abfd.business_state AS status,
    'ABNORMAL_FEEDBACK_HANDLING'::character varying AS type
   FROM qms_qe_abnormal_feedback_handling abfd
  WHERE abfd.business_state::text = 'TO_BE_VERIFIED'::text
UNION ALL
 SELECT qmur.id,
    qmur.general_code AS code,
    qmur.general_code AS name,
    qmur.business_status AS status,
    'MRBUnqualifiedReview'::character varying AS type
   FROM qms_mrb_unqualified_review qmur
  WHERE qmur.business_status::text = 'PENDING'::text
UNION ALL
 SELECT ccf.id,
    ccf.general_code AS code,
    ccf.general_code AS name,
    ccf.business_status AS status,
    'CLIENT_COMPLAINT_FEEDBACK'::character varying AS type
   FROM qms_ccm_client_complaint_feedback ccf
  WHERE ccf.business_status::text = 'TO_BE_CORRECTED'::text
UNION ALL
 SELECT eiq.id,
    eiq.general_code AS code,
    eiq.general_code AS name,
    eiq.current_status AS status,
    'EhsQuestion'::character varying AS type
   FROM ehs_ins_question eiq
  WHERE eiq.current_status::text <> 'WAIT_PUBLISH'::text;
ALTER TABLE "cpm_related_document_view" OWNER TO "xlx_qms";

CREATE OR REPLACE  VIEW "inspection_task_view" AS  SELECT mi_inspection_task.id,
    mi_inspection_task.general_code,
    'null'::character varying AS operation_name,
    mi_inspection_task.inspection_type,
    mi_inspection_task.material_code,
    mi_inspection_task.material_name,
    mi_inspection_task.origin_lot_id AS lot_serial_id,
    mi_inspection_task.material_level,
    mi_inspection_task.user_name,
    mi_inspection_task.unqualified_level,
    mi_inspection_task.arrival_quantity AS quantity,
    mi_inspection_task.detect_complete_time
   FROM mi_inspection_task
  WHERE mi_inspection_task.inspection_result::text = 'UNQUALIFIED'::text
UNION ALL
 SELECT pi_inspection_task.id,
    pi_inspection_task.general_code,
    pi_inspection_task.operation_name,
    'processInspect'::character varying(255) AS inspection_type,
    pi_inspection_task.product_code AS material_code,
    pi_inspection_task.product_name AS material_name,
    pi_inspection_task.actual_lot_serial_id AS lot_serial_id,
    'null'::character varying AS material_level,
    pi_inspection_task.user_name,
    pi_inspection_task.unqualified_level,
    pi_inspection_task.sample_quantity AS quantity,
    pi_inspection_task.detect_complete_time
   FROM pi_inspection_task
  WHERE pi_inspection_task.result::text = 'UNQUALIFIED'::text;
ALTER TABLE "inspection_task_view" OWNER TO "xlx_qms";

CREATE OR REPLACE  VIEW "inventory_view" AS  SELECT eam_inventory_ledger.id,
    'line'::text AS inventory_type,
    eam_inventory_ledger.inventory_lot_id,
    eam_inventory_ledger.lot_serial_id,
    eam_inventory_ledger.supplier_lot_serial_id,
    eam_inventory_ledger.supplier_id,
    eam_inventory_ledger.supplier_name,
    eam_inventory_ledger.warehouse_id::character varying(255) AS warehouse_id,
    eam_inventory_ledger.warehouse_name,
    eam_inventory_ledger.block_id::character varying(255) AS block_id,
    eam_inventory_ledger.block_name,
    eam_inventory_ledger.shelf_id::character varying(255) AS shelf_id,
    eam_inventory_ledger.shelf_name,
    'null'::character varying AS material_id,
    eam_inventory_ledger.material_code,
    eam_inventory_ledger.material_name,
    eam_inventory_ledger.material_specification,
    eam_inventory_ledger.material_type,
    eam_inventory_ledger.accounting_unit_quantity,
    eam_inventory_ledger.available_quantity,
    eam_inventory_ledger.accounting_unit,
    eam_inventory_ledger.business_state AS current_state
   FROM eam_inventory_ledger
UNION ALL
 SELECT bwi_inventory.id,
    'warehouse'::text AS inventory_type,
    bwi_inventory.inventory_lot_id,
    bwi_inventory.lot_serial_id,
    bwi_inventory.supplier_lot_serial_id,
    bwi_inventory.supplier_id,
    bwi_inventory.supplier_name,
    bwi_inventory.warehouse_id,
    bwi_inventory.warehouse_name,
    bwi_inventory.block_id,
    bwi_inventory.block_name,
    bwi_inventory.shelf_id,
    bwi_inventory.shelf_name,
    bwi_inventory.material_id,
    bwi_inventory.material_code,
    bwi_inventory.material_name,
    bwi_inventory.material_specification,
    bwi_inventory.stock_type AS material_type,
    bwi_inventory.accounting_unit_quantity,
    bwi_inventory.available_quantity,
    bwi_inventory.accounting_unit,
    bwi_inventory.current_state
   FROM bwi_inventory;
ALTER TABLE "inventory_view" OWNER TO "xlx_qms";

CREATE OR REPLACE  VIEW "spc_inspection_target_view" AS  SELECT biit.id,
    concat('QMS_', bii.general_code, '_', biit.name) AS name
   FROM bd_inspection_item_target biit
     LEFT JOIN bd_inspection_item bii ON bii.id::text = biit.inspection_item_id::text
UNION ALL
 SELECT d.id,
    concat('LIMS_', e.name, '_', d.name) AS name
   FROM lm_exp_method_detail d
     JOIN lm_exp_method e ON e.id::text = d.exp_method_id::text;
ALTER TABLE "spc_inspection_target_view" OWNER TO "xlx_qms";


CREATE OR REPLACE VIEW xlx_qms.mf_prcs_oprtn_param
 AS
 SELECT mf_prcs_oprtn_param_xlx_mes_ext.id::character varying AS id,
    mf_prcs_oprtn_param_xlx_mes_ext.process_id,
    mf_prcs_oprtn_param_xlx_mes_ext.process_code,
    mf_prcs_oprtn_param_xlx_mes_ext.process_name,
    mf_prcs_oprtn_param_xlx_mes_ext.process_operation_id,
    mf_prcs_oprtn_param_xlx_mes_ext.process_operation_code,
    mf_prcs_oprtn_param_xlx_mes_ext.process_operation_name,
    mf_prcs_oprtn_param_xlx_mes_ext.standard_man_hour,
    mf_prcs_oprtn_param_xlx_mes_ext.process_flow_time,
    mf_prcs_oprtn_param_xlx_mes_ext.standard_output,
    mf_prcs_oprtn_param_xlx_mes_ext.process_flow_id,
    mf_prcs_oprtn_param_xlx_mes_ext.crte_tm,
    mf_prcs_oprtn_param_xlx_mes_ext.crte_usr,
    mf_prcs_oprtn_param_xlx_mes_ext.lst_evnt_nm,
    mf_prcs_oprtn_param_xlx_mes_ext.lst_evnt_tm,
    mf_prcs_oprtn_param_xlx_mes_ext.lst_evnt_usr,
    mf_prcs_oprtn_param_xlx_mes_ext.lst_evnt_cmnt,
    mf_prcs_oprtn_param_xlx_mes_ext.oid,
    mf_prcs_oprtn_param_xlx_mes_ext.single_output,
    mf_prcs_oprtn_param_xlx_mes_ext.standard_man_hour_unit,
    mf_prcs_oprtn_param_xlx_mes_ext.standard_oxygen_consumption,
    mf_prcs_oprtn_param_xlx_mes_ext.standard_water_energy_consumption,
    mf_prcs_oprtn_param_xlx_mes_ext.utility_standard_power_consumption,
    mf_prcs_oprtn_param_xlx_mes_ext.workshop_standard_power_consumption
   FROM mf_prcs_oprtn_param_xlx_mes_ext;

ALTER TABLE xlx_qms.mf_prcs_oprtn_param
    OWNER TO xlx_qms;

GRANT ALL ON TABLE xlx_qms.mf_prcs_oprtn_param TO xlx_qms;

CREATE OR REPLACE VIEW xlx_qms.batch_serial
 AS
 SELECT batch_serial_xlx_mes_ext.gid AS id,
    batch_serial_xlx_mes_ext.serial_number,
    batch_serial_xlx_mes_ext.process_code,
    batch_serial_xlx_mes_ext.process_name,
    batch_serial_xlx_mes_ext.input_quantity,
    batch_serial_xlx_mes_ext.input_time,
    batch_serial_xlx_mes_ext.output_quantity,
    batch_serial_xlx_mes_ext.output_time,
    batch_serial_xlx_mes_ext.production_line_id::character varying AS production_line_id,
    batch_serial_xlx_mes_ext.byproduct_flag,
    batch_serial_xlx_mes_ext.byproduct_weight,
    batch_serial_xlx_mes_ext.rated_byproduct_weight,
    batch_serial_xlx_mes_ext.remaining_weight,
    batch_serial_xlx_mes_ext.current_state,
    batch_serial_xlx_mes_ext.create_user,
    batch_serial_xlx_mes_ext.create_ts,
    batch_serial_xlx_mes_ext.last_trxn_user,
    batch_serial_xlx_mes_ext.last_trxn_ts,
    batch_serial_xlx_mes_ext.oid,
    batch_serial_xlx_mes_ext.production_line_name,
    batch_serial_xlx_mes_ext.workshop_id::character varying AS workshop_id,
    batch_serial_xlx_mes_ext.workshop_name,
    batch_serial_xlx_mes_ext.order_number,
    batch_serial_xlx_mes_ext.machine_id,
    batch_serial_xlx_mes_ext.machine_name,
    batch_serial_xlx_mes_ext.material_code,
    batch_serial_xlx_mes_ext.machine_code,
    batch_serial_xlx_mes_ext.box_number,
    batch_serial_xlx_mes_ext.additives_flag,
    batch_serial_xlx_mes_ext.material_name,
    batch_serial_xlx_mes_ext.net_weight1,
    batch_serial_xlx_mes_ext.net_weight2,
    batch_serial_xlx_mes_ext.feed_used_process_code,
    batch_serial_xlx_mes_ext.rework_flag,
    batch_serial_xlx_mes_ext.equipment_sign,
    batch_serial_xlx_mes_ext.qualified_flag
   FROM batch_serial_xlx_mes_ext;

ALTER TABLE xlx_qms.batch_serial
    OWNER TO xlx_qms;

GRANT ALL ON TABLE xlx_qms.batch_serial TO xlx_qms;

CREATE OR REPLACE VIEW xlx_qms.pqt_machine_startup_wire_change_view
 AS
  SELECT bos.gid as id,
    bos.ordr_nb as name,
    bos.workshop_id::character varying as workshop_id,
    bos.workshop_name,
    bos.production_line_id::character varying as production_line_id,
    bos.production_line_name,
    bos.spcfcn_cd as material_code,
    bos.spcfcn_nm as material_name,
    bos.spcfcn_dtl as material_type,
    'POWER_ON_TASK_LIST'::character varying AS type
   FROM bm_open_stop_line bos
  WHERE bos.open_type = 'OPEN' and bos.crnt_st != 'COMPLETE'
UNION ALL
 (WITH bts AS (
	  SELECT
	    bt.gid as id,
	    bt.transfer_task_order_number as name,
	    CASE WHEN bs.order_number IS NULL THEN bt.production_work_order
	    ELSE bs.order_number
		END AS order_number,
	    bs.workshop_id,
	    bs.workshop_name,
	    bs.production_line_id,
	    bs.production_line_name,
	    'LINE_CHANGE_TASK_ORDER'::varchar AS type
	  FROM batch_transfer_config bt
	  LEFT JOIN LATERAL (
	    SELECT *
	    FROM batch_serial
	    WHERE (bt.production_work_order IS NOT NULL AND order_number = bt.production_work_order)
	       OR (bt.production_work_order IS NULL AND bt.transfer_serial_number IS NOT NULL
	           AND serial_number = bt.transfer_serial_number)
	    ORDER BY CASE WHEN bt.production_work_order IS NOT NULL THEN order_number
	                 ELSE serial_number END
	    LIMIT 1
	  ) bs ON true
	  WHERE bt.production_work_order IS NOT NULL OR bt.transfer_serial_number IS NOT NULL
	),
	split_orders AS (
	  SELECT bts.*,
	         trim(unnest(string_to_array(bts.order_number, ','))) AS single_order_number
	  FROM bts
	),
	matched_orders AS (
	  SELECT DISTINCT ON (so.id) so.id, po.*
	  FROM split_orders so
	  LEFT JOIN production_order po ON po.order_number = so.single_order_number
	  ORDER BY so.id, po.order_number
	)
	SELECT bts.id,
	bts.name::character varying AS name,
	bts.workshop_id,
	bts.workshop_name,
	bts.production_line_id,
	bts.production_line_name,
	mo.material_code,
	mo.material_name,
	ms.spcfcn_dtl as material_type,
	bts.type
	FROM bts
	LEFT JOIN matched_orders mo ON mo.id = bts.id left JOIN ms_spcfcn ms on ms.spcfcn_cd = mo.material_code);
ALTER TABLE xlx_qms.pqt_machine_startup_wire_change_view
    OWNER TO xlx_qms;

