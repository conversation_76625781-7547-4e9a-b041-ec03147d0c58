server.port=8110
spring.application.name=${app.id}
#spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.liquibase.LiquibaseAutoConfiguration,\
#  org.flowable.spring.boot.actuate.info.FlowableInfoAutoConfiguration,\
#  org.flowable.spring.boot.app.AppEngineAutoConfiguration,\
#  org.flowable.spring.boot.app.AppEngineServicesAutoConfiguration,\
#  org.flowable.spring.boot.cmmn.CmmnEngineAutoConfiguration,\
#  org.flowable.spring.boot.cmmn.CmmnEngineServicesAutoConfiguration,\
#  org.flowable.spring.boot.dmn.DmnEngineAutoConfiguration,\
#  org.flowable.spring.boot.dmn.DmnEngineServicesAutoConfiguration,\
#  org.flowable.spring.boot.eventregistry.EventRegistryAutoConfiguration,\
#  org.flowable.spring.boot.eventregistry.EventRegistryServicesAutoConfiguration,\
#  org.flowable.spring.boot.ldap.FlowableLdapAutoConfiguration,\
#  org.flowable.spring.boot.EndpointAutoConfiguration,\
#  org.flowable.spring.boot.ProcessEngineAutoConfiguration,\
#  org.flowable.spring.boot.ProcessEngineServicesAutoConfiguration,\
#  org.flowable.spring.boot.RestApiAutoConfiguration,\
#  org.flowable.spring.boot.idm.IdmEngineAutoConfiguration,\
#  org.flowable.spring.boot.idm.IdmEngineServicesAutoConfiguration
##超级管理员帐号
#fabosjson.upms.super-admin-name=root
##加密后的密码
#fabosjson.upms.super-admin-password=7488E331B8B64E5794DA3FA4EB10AD5D
##数据源
#spring.datasource.driver-class-name=org.postgresql.Driver
#spring.datasource.url=**********************************************
#spring.datasource.username=xlx_qms
#spring.datasource.password=xlx_qms_0226
#spring.datasource.hikari.idleTimeout=300000
#spring.datasource.hikari.maxLifetime=600000
#spring.datasource.hikari.read-only=false
#spring.datasource.hikari.connection-timeout=60000
#spring.datasource.hikari.idle-timeout=1800000
#spring.datasource.hikari.validation-timeout=3000
#spring.datasource.hikari.max-lifetime=1800000
#spring.datasource.hikari.login-timeout=5
#spring.datasource.hikari.maximum-pool-size=500
#spring.datasource.hikari.minimum-idle=10
#spring.jmx.default-domain=${spring.application.name}
##jpa
#spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect
##spring.jpa.database-platform=cec.jiutian.data.jpa.dmAdaptation.DmDialectAdaptation
#spring.jpa.properties.hibernate.default_schema=${spring.datasource.username}
#spring.jpa.properties.hibernate.temp.use_jdbc_metadata_defaults=false
#spring.jpa.properties.hibernate.jdbc.batch_size=50
#spring.jpa.properties.hibernate.order_inserts=true
#spring.jpa.properties.hibernate.order_updates=true
#spring.jpa.show-sql=true
#logging.level.org.hibernate.SQL=debug
#logging.level.org.hibernate.orm.jdbc.bind=trace
##spring.jpa.generate-ddl=true
##spring.jpa.hibernate.ddl-auto=update
#spring.jpa.generate-ddl=false
#spring.jpa.hibernate.ddl-auto=none
#spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy
#spring.jpa.properties.hibernate.transaction.jta.platform=org.hibernate.service.jta.platform.internal.AtomikosJtaPlatform
##redis
##spring.data.redis.cluster.nodes = 172.16.200.85:6380,172.16.200.105:6380,172.16.200.104:6380
#spring.data.redis.cluster.nodes=172.16.200.85:6379,172.16.200.105:6379,172.16.200.104:6379
##spring.redis.password = Redisjt123
#spring.data.redis.cluster.max-redirects=3
## 连接池最大连接数（使用负值表示没有限制）
#spring.data.redis.lettuce.pool.max-active=30
## 连接池中的最大空闲连接
#spring.data.redis.lettuce.pool.max-idle=10
## 连接池中的最小空闲连接
#spring.data.redis.lettuce.pool.min-idle=8
## 连接池最大阻塞等待时间（使用负值表示没有限制）
#spring.data.redis.lettuce.pool.max-wait=-1ms
## 连接超时时间（毫秒）
#spring.data.redis.timeout=12000ms
## 单个文件大小为5MB
#spring.servlet.multipart.max-file-size=150MB
## 总上传的数据大小5MB
#spring.servlet.multipart.max-request-size=150MB
#fabosjson.uploadPath=D:\\biz
##组件存储路径
#fabos.arkBiz=/var/arkBizs
##fabos.arkBiz = D:\\biz
## logging
#logging.file.path=logging/log4j2/logs
#logging.level.com.alipay.sofa=DEBUG
#logging.level.root=INFO
#logging.level.com.alipay.sofa.arklet=INFO
#logging.level.net.sf.ehcache=DEBUG
#spring.cloud.stream.binders.rabbit.type=rabbit
#spring.cloud.stream.binders.rabbit.environment.spring.rabbitmq.addresses=**************:5672
#spring.cloud.stream.binders.rabbit.environment.spring.rabbitmq.username=wuhanalarm
#spring.cloud.stream.binders.rabbit.environment.spring.rabbitmq.password=alarm!123
#spring.cloud.stream.binders.rabbit.environment.spring.rabbitmq.virtual-host=/3.2.2
#
#faas.isSingle=true
#spring.application.version=1.0.0
#
#dubbo.registry.address= nacos://${nacos.addr}?username=nacos&password=nacos
#dubbo.registry.group= ${spring.profiles.active}
#dubbo.registry.parameters.data-id= ${app.id}
#dubbo.registry.parameters.namespace= fabos-xlx
#dubbo.protocol.name= tri
#dubbo.registry.port= 50556
#dubbo.application.logger= slf4j
#dubbo.application.name= ${app.id}
#dubbo.application.serialize-check-status=WARN
#management.health.rabbit.enabled=false
##ecs 配置
#aes.key=aWXoyC4UNb756984
#confirm.url=http://***********:8205/confirm?secret=
#sms.uid=test
#sms.key=test
#sms.url=url
#spring.mail.host=smtp.cecjiutian.com
#spring.mail.port=587
#spring.mail.username=<EMAIL>
#spring.mail.sendFrom=<EMAIL>
#spring.mail.password=Jiutian@!23456
#spring.mail.default-encoding=utf-8
#spring.mail.properties.timeout=5000
#spring.mail.properties.connection-timeout=5000
#spring.mail.properties.write-timeout=5000
##fabosjson.metadata-scan-level=scan_and_persist
#system.manage.super-account=superUser
#system.manage.super-password=e10adc3949ba59abbe56e057f20f883e
#system.manage.super-phone=***********
#system.manage.super-email=<EMAIL>
#system.manage.audit-permission=$rightManager,PermissionOperationLog,PermissionOperationLog@VIEW_DETAIL,WebApiLog,WebApiLog@VIEW_DETAIL,LoginLog,LoginLog@VIEW_DETAIL
#system.manage.system-permission=$rightManager,SystemBasicConfig,SystemBasicConfig@EDIT,SystemBasicConfig@VIEW_DETAIL,$system,\
#  Menu,Menu@ADD,Menu@EDIT,Menu@DELETE,Menu@VIEW_DETAIL,\
#  Role,Role@ADD,Role@EDIT,Role@DELETE,Role@VIEW_DETAIL,\
#  User,User@ADD,User@EDIT,User@DELETE,User@VIEW_DETAIL,User@RESET_PASSWORD,\
#  Org,Org@ADD,Org@EDIT,Org@DELETE,Org@VIEW_DETAIL
#system.manage.security-permission=$rightManager,RoleAuthDistribute,RoleAuthDistribute@VIEW_DETAIL,RoleAuthDistribute@EDIT,Role@AUTH,Role@AUTH,Role@User
#fabosjson.file-storage-bucket-name=test1029
#fabosjson.minio-end-point=http://172.16.200.90:9101
#fabosjson.minio-key=admin
#fabosjson.minio-secret=admin12345
#seata.enabled=false
#mq.queue.ecs=ecsMsgDest-dev
#mq.queue.group=ecsMsgGroup-dev
#mq.queue.person=personMsgDest-dev
#mq.queue.exchange.ecs=ecsMsgExchange-dev
#
#fabosjson.metadata-scan-level=scan_and_persist
