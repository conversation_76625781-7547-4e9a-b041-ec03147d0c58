package cec.jiutian.module;

import cec.jiutian.bc.urm.dto.InitDataDTO;
import cec.jiutian.bc.urm.dto.MetaDict;
import cec.jiutian.bc.urm.dto.MetaMenu;
import cec.jiutian.component.utils.ReadInitMenuDataFromCSVUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/2/28
 * @description TODO
 */
@Component("qms-business")
public class QmsInitProvider implements InitDataDTO.IInitDataProvider{
    @Value("${spring.application.name}")
    private String applicationName;
    @Override
    public List<MetaMenu> initData(InitDataDTO dto) {
        List<MetaMenu> menus = new ArrayList<>();
        Optional.ofNullable(ReadInitMenuDataFromCSVUtil.convertCsvToList(dto.getComponentName())).ifPresent(e -> menus.addAll(e));
        menus.forEach(menu -> { menu.setApplicationName(applicationName); });
        return menus;
    }

    @Override
    public List<MetaDict> initDict(InitDataDTO dto) {
        return null;
    }
}
