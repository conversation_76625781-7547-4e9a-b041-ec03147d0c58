package cec.jiutian.module;

import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleModel;
import cec.jiutian.core.frame.module.MetaDataProxy;
import cec.jiutian.core.view.fabosJson.PreDataProxy;
import jakarta.persistence.MappedSuperclass;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@MappedSuperclass
@PreDataProxy(MetaDataProxy.class)
public class QMSNamingRuleModel extends NamingRuleModel {
    @Override
    public String getNamingCode() {
        return this.getClass().getSimpleName();
    }
}
