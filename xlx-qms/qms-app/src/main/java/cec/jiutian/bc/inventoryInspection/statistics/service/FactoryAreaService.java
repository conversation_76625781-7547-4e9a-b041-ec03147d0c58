package cec.jiutian.bc.inventoryInspection.statistics.service;

import cec.jiutian.bc.inventoryInspection.statistics.repository.FactoryAreaRepository;
import cec.jiutian.bc.mto.FactoryArea;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class FactoryAreaService {


    @Resource
    private FactoryAreaRepository factoryAreaRepository;

    public List<FactoryArea> findByPids(List<Long> pids) {
        if (CollectionUtils.isEmpty(pids)) {
            return factoryAreaRepository.findByFactoryAreaTypeCode("01");
        }
        return factoryAreaRepository.findByPidInOrderById(pids);
    }
}
