package cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.handler;

import cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.enumration.TaskBusinessStateEnum;
import cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.model.AbnormalFeedbackHandling;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/27
 * @description TODO
 */
@Component
public class AbnormalFeedbackHandlingVerifySubmitHandler implements OperationHandler<AbnormalFeedbackHandling, Void> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Transactional
    @Override
    public String exec(List<AbnormalFeedbackHandling> data, Void modelObject, String[] param) {
        AbnormalFeedbackHandling myDeviceInsTask = data.get(0);
        myDeviceInsTask.setBusinessState(TaskBusinessStateEnum.Enum.TO_BE_ANALYZED.name());

        fabosJsonDao.update(myDeviceInsTask);
        return null;
    }
}
