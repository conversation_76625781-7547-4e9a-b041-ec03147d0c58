package cec.jiutian.bc.productReturnInspection.domain.samplingTask.handler;

import cec.jiutian.bc.modeler.enumration.SampleTypeEnum;
import cec.jiutian.bc.modeler.enumration.TaskBusinessStateEnum;
import cec.jiutian.bc.productReturnInspection.domain.samplingTask.model.ProductReturnSamplingTask;
import cec.jiutian.bc.productReturnInspection.domain.samplingTask.model.ProductReturnSamplingTaskOpr;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/12
 * @description TODO
 */
@Component
public class ProductReturnSendSampleOperationHandler implements OperationHandler<ProductReturnSamplingTask, ProductReturnSamplingTaskOpr> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<ProductReturnSamplingTask> data, ProductReturnSamplingTaskOpr modelObject, String[] param) {
        if (modelObject != null) {
            ProductReturnSamplingTask condition = new ProductReturnSamplingTask();
            condition.setGeneralCode(modelObject.getGeneralCode());
            condition.setInspectionType(SampleTypeEnum.Enum.REFUND_SAMPLING.name());
            ProductReturnSamplingTask productReturnSamplingTask = fabosJsonDao.selectOne(condition);
            if (productReturnSamplingTask == null) {
                throw new FabosJsonApiErrorTip("未查询到取样单，请确认");
            }
            if (!TaskBusinessStateEnum.Enum.SAMPLING_FINISH.name().equals(productReturnSamplingTask.getBusinessState())) {
                throw new FabosJsonApiErrorTip("取样任务单状态有误");
            }
            productReturnSamplingTask.setBusinessState(TaskBusinessStateEnum.Enum.SEND_SAMPLE.name());
            fabosJsonDao.mergeAndFlush(productReturnSamplingTask);
        }
        return "alert(操作成功)";
    }
}
