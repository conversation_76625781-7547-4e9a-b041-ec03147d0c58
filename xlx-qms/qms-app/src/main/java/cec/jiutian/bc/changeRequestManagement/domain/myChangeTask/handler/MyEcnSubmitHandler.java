package cec.jiutian.bc.changeRequestManagement.domain.myChangeTask.handler;

import cec.jiutian.bc.changeRequestManagement.domain.ecn.model.ChangeRequestExecute;
import cec.jiutian.bc.changeRequestManagement.domain.ecn.model.ECNItem;
import cec.jiutian.bc.changeRequestManagement.domain.ecn.model.MyECNItem;
import cec.jiutian.bc.changeRequestManagement.domain.myChangeTask.model.MyChangeTask;
import cec.jiutian.bc.changeRequestManagement.enums.ECRStatusEnum;
import cec.jiutian.bc.changeRequestManagement.enums.ProgressEnum;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component
public class MyEcnSubmitHandler implements OperationHandler<MyChangeTask, Void> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<MyChangeTask> data, Void modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            MyChangeTask myChangeTask = data.get(0);
            List<MyECNItem> updateList = new ArrayList<>();
            for (MyECNItem d : myChangeTask.getMyECNItems()) {
                if (!d.getResponsiblePersonId().equals(UserContext.getUserId())) {
                    //不是自己的任务不需要参与校验
                    continue;
                }
                if (!ProgressEnum.Enum.EXECUTION.name().equals(d.getProgress())) {
                    throw new FabosJsonApiErrorTip("提交失败：需要完成所有整改任务才可提交");
                }
                updateList.add(d);
            }

            updateList.forEach(d -> {
                d.setProgress(ProgressEnum.Enum.PENDING_VERIFICATION.name());
                fabosJsonDao.mergeAndFlush(d);
            });

            // 所有 变更事项 都是待验证 则主表改成待验证
            ChangeRequestExecute cre = fabosJsonDao.findById(ChangeRequestExecute.class, myChangeTask.getId());
            if (checkCurrentStatus(cre)) {
                cre.setStatus(ECRStatusEnum.Enum.SUPPLEMENT_WAIT_VERIFY.name());
                fabosJsonDao.mergeAndFlush(cre);
            }
        }

        return "msg.success('操作成功')";
    }

    private boolean checkCurrentStatus(ChangeRequestExecute cre) {
        for (ECNItem ecnItem : cre.getECNItems()) {
            if (!ProgressEnum.Enum.PENDING_VERIFICATION.name().equals(ecnItem.getProgress())) {
                return false;
            }
        }
        return true;
    }
}
