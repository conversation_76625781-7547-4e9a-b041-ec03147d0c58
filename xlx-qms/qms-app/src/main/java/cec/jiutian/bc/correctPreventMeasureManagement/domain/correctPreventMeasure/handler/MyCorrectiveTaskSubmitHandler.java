package cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.handler;

import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.enumration.CpmBusinessStateEnum;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.enumration.ImprovingProgressEnum;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.enumration.MyCorrectiveExecProgressEnum;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.enumration.ProgressEnum;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.model.CorrectPreventMeasure;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.model.CorrectiveAction;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.model.MyCorrectiveTask;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.model.PreventMeasure;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class MyCorrectiveTaskSubmitHandler implements OperationHandler<MyCorrectiveTask, Void> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<MyCorrectiveTask> data, Void modelObject, String[] param) {
        if (CollectionUtils.isEmpty(data)) {
            return "msg.success('操作成功')";
        }

        MyCorrectiveTask myCorrectiveTask = data.get(0);
        List<CorrectiveAction> updateCorrectiveAction = new ArrayList<>();
        for (CorrectiveAction d : myCorrectiveTask.getCorrectiveActionList()) {
            if (!d.getUserForInsTaskMTO().getId().equals(UserContext.getUserId())) {
                continue;
            }
            if (!MyCorrectiveExecProgressEnum.Enum.RUN_SUBMIT.name().equals(d.getImprovingProgress())) {
                throw new FabosJsonApiErrorTip("提交失败：需要完成所有整改任务才可提交");
            }
            updateCorrectiveAction.add(d);
        }

        updateCorrectiveAction.forEach(d -> {
            d.setImprovingProgress(ImprovingProgressEnum.Enum.TO_BE_VERIFIED.name());
            fabosJsonDao.mergeAndFlush(d);
        });

        CorrectPreventMeasure cpm = fabosJsonDao.findById(CorrectPreventMeasure.class, myCorrectiveTask.getId());
        if (checkCurrentStatus(cpm)) {
            // cpm.setBusinessState(CpmBusinessStateEnum.Enum.TO_BE_VERIFIED.name());
            cpm.setCorrectVeriState(ProgressEnum.Enum.WAIT_RUN.name());
            fabosJsonDao.mergeAndFlush(cpm);
        }

        // 判断纠正是否处理完 完毕-> 待验证TO_BE_VERIFIED
        if (checkOtherStatus(cpm)) {
            cpm.setBusinessState(CpmBusinessStateEnum.Enum.TO_BE_VERIFIED.name());
            cpm.setPreVeriState(ProgressEnum.Enum.WAIT_RUN.name());
            cpm.setCorrectVeriState(ProgressEnum.Enum.WAIT_RUN.name());
            fabosJsonDao.mergeAndFlush(cpm);
        }

        return "msg.success('操作成功')";
    }

    private boolean checkOtherStatus(CorrectPreventMeasure cpm) {
        for (PreventMeasure preventMeasure : cpm.getPreventMeasureList()) {
            if (!ImprovingProgressEnum.Enum.TO_BE_VERIFIED.name().equals(preventMeasure.getImprovingProgress())) {
                return false;
            }
        }
        return true;
    }

    private boolean checkCurrentStatus(CorrectPreventMeasure cpm) {
        for (CorrectiveAction correctiveAction : cpm.getCorrectiveActionList()) {
            if (!ImprovingProgressEnum.Enum.TO_BE_VERIFIED.name().equals(correctiveAction.getImprovingProgress())) {
                return false;
            }
        }
        return true;
    }
}
