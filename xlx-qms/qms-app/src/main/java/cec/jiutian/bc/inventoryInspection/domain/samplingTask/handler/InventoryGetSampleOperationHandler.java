package cec.jiutian.bc.inventoryInspection.domain.samplingTask.handler;

import cec.jiutian.bc.inventoryInspection.domain.samplingTask.model.InventorySamplingTask;
import cec.jiutian.bc.inventoryInspection.domain.samplingTask.model.InventorySamplingTaskOpr;
import cec.jiutian.bc.inventoryInspection.domain.samplingTask.model.InventorySamplingTaskOprDetail;
import cec.jiutian.bc.modeler.enumration.TaskBusinessStateEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/12
 * @description TODO
 */
@Component
public class InventoryGetSampleOperationHandler implements OperationHandler<InventorySamplingTask, InventorySamplingTaskOpr> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<InventorySamplingTask> data, InventorySamplingTaskOpr modelObject, String[] param) {
        if (modelObject != null) {
            InventorySamplingTask condition = new InventorySamplingTask();
            condition.setGeneralCode(modelObject.getGeneralCode());
            InventorySamplingTask samplingTask = fabosJsonDao.selectOne(condition);
            samplingTask.setBusinessState(TaskBusinessStateEnum.Enum.SAMPLING_FINISH.name());
            samplingTask.setSampleActualDate(new Date());
            fabosJsonDao.mergeAndFlush(samplingTask);
        }
        return "alert(操作成功)";
    }

    @Override
    public InventorySamplingTaskOpr fabosJsonFormValue(List<InventorySamplingTask> data, InventorySamplingTaskOpr fabosJsonForm, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            InventorySamplingTask samplingTask = data.get(0);
            fabosJsonForm.setGeneralCode(samplingTask.getGeneralCode());
            fabosJsonForm.setInspectionTaskCode(samplingTask.getInspectionTaskCode());
            fabosJsonForm.setSamplingTaskCode(samplingTask.getGeneralCode());
            fabosJsonForm.setMaterialCode(samplingTask.getMaterialCode());
            fabosJsonForm.setMaterialName(samplingTask.getMaterialName());
            fabosJsonForm.setOriginLotId(samplingTask.getOriginLotId());
            fabosJsonForm.setMaterialSpecification(samplingTask.getMaterialSpecification());
            fabosJsonForm.setUnit(samplingTask.getUnit());
            fabosJsonForm.setInspectionType(samplingTask.getInspectionType());
            fabosJsonForm.setAllInspectionItemQuantity(samplingTask.getAllInspectionItemQuantity());
            fabosJsonForm.setSamplingPoint(samplingTask.getSamplePoint());
            fabosJsonForm.setSendInspectPoint(samplingTask.getSendPoint());
            fabosJsonForm.setUnit(samplingTask.getUnit());
            fabosJsonForm.setPackageType(samplingTask.getPackageType());
            if (CollectionUtils.isNotEmpty(samplingTask.getDetailList())) {
                List<InventorySamplingTaskOprDetail> detailList = new ArrayList<>();
                samplingTask.getDetailList().forEach(samplingTaskDetail -> {
                    InventorySamplingTaskOprDetail samplingTaskOprDetail = new InventorySamplingTaskOprDetail();
                    samplingTaskOprDetail.setItemId(samplingTaskDetail.getItemId());
                    samplingTaskOprDetail.setCode(samplingTaskDetail.getCode());
                    samplingTaskOprDetail.setName(samplingTaskDetail.getName());
                    samplingTaskOprDetail.setSampleSize(samplingTaskDetail.getSampleSize());
                    detailList.add(samplingTaskOprDetail);
                });
                fabosJsonForm.setDetailList(detailList);
            }

        }
        return fabosJsonForm;
    }
}
