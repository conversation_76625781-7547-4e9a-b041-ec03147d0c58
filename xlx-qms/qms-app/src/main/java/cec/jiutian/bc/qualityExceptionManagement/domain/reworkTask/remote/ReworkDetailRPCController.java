package cec.jiutian.bc.qualityExceptionManagement.domain.reworkTask.remote;

import cec.jiutian.api.remoteCallLog.dto.RemoteCallResult;
import cec.jiutian.bc.qualityExceptionManagement.domain.reworkTask.dto.ReworkDetailDTO;
import cec.jiutian.bc.qualityExceptionManagement.domain.reworkTask.dto.ReworkTraceDTO;
import cec.jiutian.bc.qualityExceptionManagement.domain.reworkTask.service.ReworkDetailService;
import cec.jiutian.core.frame.constant.FabosJsonRestPath;
import com.google.gson.JsonObject;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/fabos-qms-app"+ FabosJsonRestPath.FABOS_REMOTE_API)
public class ReworkDetailRPCController {


    @Resource
    private ReworkDetailService reworkDetailService;

    /**
     * reworkTaskCode reworkQuantity
     * @param params
     * @return String detailId
     */
    @PostMapping("/createReworkDetail")
    public RemoteCallResult<Map> create(@RequestBody ReworkDetailDTO params) {
        String id = reworkDetailService.create(params);
        HashMap<String, String> res = new HashMap<>(4);
        res.put("detailId", id);
        return RemoteCallResult.success(res);
    }

    /**
     *
     * detailId productBatch
     * @param params
     * @return
     */
    @PostMapping("/synchronizeProductBatch")
    public RemoteCallResult<String> synchronizeProductBatch(@RequestBody JsonObject params) {
        reworkDetailService.synchronizeProductBatch(params);
        return RemoteCallResult.success("同步成功");
    }

    /**
     * serialNumber detailId reworkId weight
     * @param params
     * @return
     */
    @PostMapping("/createTrace")
    public RemoteCallResult<String> createTrace(@RequestBody ReworkTraceDTO params) {
        String traceId = reworkDetailService.createTrace(params);
        return RemoteCallResult.success(traceId);
    }
}
