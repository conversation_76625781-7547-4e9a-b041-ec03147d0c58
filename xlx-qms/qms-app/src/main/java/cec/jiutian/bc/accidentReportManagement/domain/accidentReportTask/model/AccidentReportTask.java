package cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.model;

import cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.enumration.*;
import cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.handler.*;
import cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.mto.*;
import cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.proxy.AccidentReportTaskDataProxy;
import cec.jiutian.bc.accidentReportManagement.enumeration.ArmMeasureStatusEnum;
import cec.jiutian.bc.accidentReportManagement.enumeration.ArmProgressEnum;
import cec.jiutian.bc.materialInspect.domain.publicInspectionTask.mto.UserForInsTaskMTO;
import cec.jiutian.bc.mto.OrgMTO;
import cec.jiutian.bc.mto.FactoryArea;
import cec.jiutian.bc.urm.domain.dictionary.handler.DictChoiceFetchHandler;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowBaseOperation;
import cec.jiutian.view.type.RowOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/29
 * @description TODO
 */
@FabosJson(
        name = "事故报告任务",
        orderBy = "AccidentReportTask.createTime desc",
        power = @Power(add = false, edit = false),
        dataProxy = AccidentReportTaskDataProxy.class,
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "businessStatus != 'TO_BE_CONFIRMED'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                )
        },
        rowOperation = {
                @RowOperation(
                        title = "创建",
                        code = "AccidentReportTask@CREATE",
                        mode = RowOperation.Mode.BUTTON,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.ADD,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = AccidentReportTaskCreateMTO.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "AccidentReportTask@CREATE"
                        )
                ),
                @RowOperation(
                        title = "编辑",
                        code = "AccidentReportTask@MODIFY",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = AccidentReportTaskCreateMTO.class,
                        operationHandler = AccidentReportTaskModifyHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "AccidentReportTask@MODIFY"
                        ),
                        ifExpr = "selectedItems[0].businessStatus != 'TO_BE_CONFIRMED'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "确认",
                        code = "AccidentReportTask@CONFIRM",
                        operationHandler = AccidentReportTaskConfirmHandler.class,
                        mode = RowOperation.Mode.HEADER,
                        callHint = "该操作不可撤回，确定提交吗？",
                        ifExpr = "selectedItems[0].businessStatus != 'TO_BE_CONFIRMED'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "AccidentReportTask@CONFIRM"
                        )
                ),
                @RowOperation(
                        title = "原因分析",
                        code = "AccidentReportTask@CAUSEANALYSIS",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = AccidentReportTaskCauseAnalyMTO.class,
                        operationHandler = AccidentReportTaskCauseAnalyHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "AccidentReportTask@CAUSEANALYSIS"
                        ),
                        ifExpr = "selectedItems[0].businessStatus != 'TO_BE_ANALYZED'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "分析提交",
                        code = "AccidentReportTask@ANALYSUBMIT",
                        operationHandler = AccidentReportTaskAnalySubmitHandler.class,
                        mode = RowOperation.Mode.HEADER,
                        callHint = "该操作不可撤回，确定提交吗？",
                        ifExpr = "selectedItems[0].businessStatus != 'TO_BE_ANALYZED' || selectedItems[0].analyticalStatus != 'RUN_SUBMIT'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "AccidentReportTask@ANALYSUBMIT"
                        )
                ),
                @RowOperation(
                        title = "临时措施",
                        code = "AccidentReportTask@TEMPORARY",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = AccidentReportTaskTemporaryMTO.class,
                        operationHandler = AccidentReportTaskTemporaryHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "AccidentReportTask@TEMPORARY"
                        ),
                        ifExpr = "selectedItems[0].businessStatus != 'UNDER_DISPOSAL' || selectedItems[0].temporaryMeasureStatus != 'ALREADY_FILLED_IN' && selectedItems[0].temporaryMeasureStatus != 'NOT_FILLED_IN'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "临时措施提交",
                        code = "AccidentReportTask@TEMSUBMIT",
                        operationHandler = AccidentReportTaskTemporarySubmitHandler.class,
                        mode = RowOperation.Mode.HEADER,
                        callHint = "该操作不可撤回，确定提交吗？",
                        ifExpr = "selectedItems[0].businessStatus != 'UNDER_DISPOSAL' || selectedItems[0].temporaryMeasureStatus != 'ALREADY_FILLED_IN'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "AccidentReportTask@TEMSUBMIT"
                        )
                ),
                @RowOperation(
                        title = "预防措施",
                        code = "AccidentReportTask@PREVNET",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = AccidentReportTaskPreventMTO.class,
                        operationHandler = AccidentReportTaskPreventHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "AccidentReportTask@PREVNET"
                        ),
                        ifExpr = "selectedItems[0].businessStatus != 'UNDER_DISPOSAL' || selectedItems[0].preventMeasureStatus != 'ALREADY_FILLED_IN' && selectedItems[0].preventMeasureStatus != 'NOT_FILLED_IN'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "预防措施提交",
                        code = "AccidentReportTask@PRESUBMIT",
                        operationHandler = AccidentReportTaskPreventSubmitHandler.class,
                        mode = RowOperation.Mode.HEADER,
                        callHint = "该操作不可撤回，确定提交吗？",
                        ifExpr = "selectedItems[0].businessStatus != 'UNDER_DISPOSAL' || selectedItems[0].preventMeasureStatus != 'ALREADY_FILLED_IN'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "AccidentReportTask@PRESUBMIT"
                        )
                ),
                @RowOperation(
                        title = "临时措施验证",
                        code = "AccidentReportTask@TEMVERI",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = AccidentReportTaskTemVeriMTO.class,
                        operationHandler = AccidentReportTaskTemVeriHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "AccidentReportTask@TEMVERI"
                        ),
                        ifExpr = "selectedItems[0].businessStatus != 'UNDER_DISPOSAL' || selectedItems[0].temporaryMeasureStatus != 'TO_BE_VERIFIED'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "预防措施验证",
                        code = "AccidentReportTask@PREVERI",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = AccidentReportTaskPreVeriMTO.class,
                        operationHandler = AccidentReportTaskPreVeriHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "AccidentReportTask@PREVERI"
                        ),
                        ifExpr = "selectedItems[0].businessStatus != 'UNDER_DISPOSAL' || selectedItems[0].preventMeasureStatus != 'TO_BE_VERIFIED'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "关闭",
                        code = "AccidentReportTask@CLOSE",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = AccidentReportTaskCloseMTO.class,
                        operationHandler = AccidentReportTaskCloseHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "AccidentReportTask@CLOSE"
                        ),
                        ifExpr = "selectedItems[0].temporaryMeasureStatus != 'VERIFIED' || selectedItems[0].preventMeasureStatus != 'VERIFIED'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
        }
)
@Table(name = "qms_arm_accident_report_task",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Getter
@Setter
public class AccidentReportTask extends MetaModel {
    //创建
    @FabosJsonField(
            views = @View(title = "任务单号"),
            edit = @Edit(title = "任务单号",
                    readonly = @Readonly(edit = false)
            )
    )
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "车间", column = "factoryAreaName"),
            edit = @Edit(title = "车间",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "factoryAreaName"),
                    allowAddMultipleRows = false,
                    filter = @Filter(value = "FactoryArea.factoryAreaTypeCode = '02'")
            )
    )
    @ManyToOne
    private FactoryArea factoryArea;

    @FabosJsonField(
            views = @View(title = "事故类型"),
            edit = @Edit(title = "事故类型",
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = DictChoiceFetchHandler.class,
                            fetchHandlerParams = "armAccidentType")
            )
    )
    private String armAccidentType;

    @FabosJsonField(
            views = @View(title = "事故等级"),
            edit = @Edit(title = "事故等级",
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = DictChoiceFetchHandler.class,
                            fetchHandlerParams = "armAccidentLevel")
            )
    )
    private String armAccidentLevel;

    @FabosJsonField(
            views = @View(title = "发生日期"),
            edit = @Edit(title = "发生日期",
                    notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date occurrenceDate;

    @FabosJsonField(
            views = @View(title = "事故描述"),
            edit = @Edit(title = "事故描述",
                    notNull = true,
                    type = EditType.TEXTAREA)
    )
    private String accidentDescription;

    @FabosJsonField(
            views = @View(title = "责任部门", column = "name"),
            edit = @Edit(title = "责任部门",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType(type = TabTableReferType.SelectShowTypeMTM.LIST)
            )
    )
    @ManyToOne
    @JoinColumn(name = "response_department_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private OrgMTO orgMTO;

    @FabosJsonField(
            views = @View(title = "责任人", column = "name"),
            edit = @Edit(title = "责任人",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType(type = TabTableReferType.SelectShowTypeMTM.LIST)
            )
    )
    @ManyToOne
    @JoinColumn(name = "person_responsible_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private UserForInsTaskMTO userForInsTaskMTO;

    @FabosJsonField(
            views = @View(title = "预计纳期"),
            edit = @Edit(title = "预计纳期",
                    notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date expectedDeliveryDate;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",
                    show = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ArmBusinessStatusEnum.class)
            )
    )
    private String businessStatus;

    //原因分析
    @FabosJsonField(
            views = @View(title = "事故原因（5why 分析））"),
            edit = @Edit(title = "事故原因（5why 分析）",
                    type = EditType.TEXTAREA)
    )
    private String accidentCause;

    @FabosJsonField(
            views = @View(title = "原因分析附件", show = false),
            edit = @Edit(title = "原因分析附件", type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType)
    )
    private String causeAnalysisAttachments;

    @FabosJsonField(
            views = @View(title = "分析状态", show = false),
            edit = @Edit(title = "分析状态",
                    readonly = @Readonly(),
                    type = EditType.CHOICE,
                    show = false,
                    choiceType = @ChoiceType(fetchHandler = ArmProgressEnum.class)
            )
    )
    private String analyticalStatus;

    //临时措施
    @FabosJsonField(
            edit = @Edit(title = "临时措施", type = EditType.TAB_TABLE_ADD),
            views = @View(title = "临时措施", type = ViewType.TABLE_VIEW)
    )
    @JoinColumn(name = "qms_arm_accident_report_task_id")
    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
    @OrderBy
    private List<ArmTemporaryMeasure> armTemporaryMeasureList;

    @FabosJsonField(
            views = @View(title = "临时措施状态", show = false),
            edit = @Edit(title = "临时措施状态",
                    readonly = @Readonly(),
                    type = EditType.CHOICE,
                    show = false,
                    choiceType = @ChoiceType(fetchHandler = ArmMeasureStatusEnum.class)
            )
    )
    private String temporaryMeasureStatus;

    @FabosJsonField(
            views = @View(title = "临时措施下所有责任人的id集合", show = false),
            edit = @Edit(title = "临时措施下所有责任人的id集合",show = false)
    )
    private String tempUserIds;

    //预防措施
    @FabosJsonField(
            edit = @Edit(title = "预防措施", type = EditType.TAB_TABLE_ADD),
            views = @View(title = "预防措施", type = ViewType.TABLE_VIEW)
    )
    @JoinColumn(name = "qms_arm_accident_report_task_id")
    @OneToMany(cascade = CascadeType.ALL)
    @OrderBy
    private List<ArmPreventMeasure> armPreventMeasureList;

    @FabosJsonField(
            views = @View(title = "预防措施状态", show = false),
            edit = @Edit(title = "预防措施状态",
                    readonly = @Readonly(),
                    type = EditType.CHOICE,
                    show = false,
                    choiceType = @ChoiceType(fetchHandler = ArmMeasureStatusEnum.class)
            )
    )
    private String preventMeasureStatus;

    @FabosJsonField(
            views = @View(title = "预防措施下所有责任人的id集合", show = false),
            edit = @Edit(title = "预防措施下所有责任人的id集合",show = false)
    )
    private String preventUserIds;

    //关闭
    @FabosJsonField(
            views = @View(title = "关闭审批意见", show = false),
            edit = @Edit(title = "关闭审批意见",
                    type = EditType.CHOICE,
                    notNull = true,
                    choiceType = @ChoiceType(fetchHandler = ArmVerificationResultEnum.class)
            )
    )
    private String closeApprovalComments;

    @FabosJsonField(
            views = @View(title = "意见说明", show = false),
            edit = @Edit(title = "意见说明",
                    type = EditType.TEXTAREA)
    )
    private String opinionExplanation;

    @FabosJsonField(
            views = @View(title = "附件", show = false),
            edit = @Edit(title = "附件", type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType)
    )
    private String closeAttachments;
}
