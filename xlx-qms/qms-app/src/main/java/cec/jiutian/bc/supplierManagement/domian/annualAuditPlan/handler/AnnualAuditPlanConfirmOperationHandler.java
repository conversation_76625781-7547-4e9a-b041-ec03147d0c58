package cec.jiutian.bc.supplierManagement.domian.annualAuditPlan.handler;

import cec.jiutian.bc.supplierManagement.domian.annualAuditPlan.model.SupplierAnnualAuditPlan;
import cec.jiutian.bc.supplierManagement.enums.AnnualAuditPlanStateEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class AnnualAuditPlanConfirmOperationHandler implements OperationHandler<SupplierAnnualAuditPlan, Void> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<SupplierAnnualAuditPlan> data, Void modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            SupplierAnnualAuditPlan entity = data.get(0);
            entity.setCurrentState(AnnualAuditPlanStateEnum.Enum.Confirmed.name());
            fabosJsonDao.mergeAndFlush(entity);
        }

        return "success";
    }
}
