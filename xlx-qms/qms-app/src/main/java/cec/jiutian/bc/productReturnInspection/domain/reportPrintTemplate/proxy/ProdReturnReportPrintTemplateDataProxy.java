package cec.jiutian.bc.productReturnInspection.domain.reportPrintTemplate.proxy;

import cec.jiutian.bc.productReturnInspection.domain.inspectionTask.model.ProductReturnInspectionStandardItemTarget;
import cec.jiutian.bc.productReturnInspection.domain.inspectionTask.model.ProductReturnInspectionTask;
import cec.jiutian.bc.productReturnInspection.domain.inspectionTask.model.ProductReturnQualityInspectionStandardDetail;
import cec.jiutian.bc.productReturnInspection.domain.reportPrintTemplate.model.ProdReturnReportPrintTemplate;
import cec.jiutian.bc.reportPrintTemplateSuper.InsItemAndIndex;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Component
public class ProdReturnReportPrintTemplateDataProxy implements DataProxy<ProdReturnReportPrintTemplate> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public void afterSingleFetch(Map<String, Object> map) {

        ProductReturnInspectionTask task = fabosJsonDao.findById(ProductReturnInspectionTask.class, map.get("id"));
        if (task == null) {
            throw new ServiceException("数据异常");
        }
        List<ProductReturnQualityInspectionStandardDetail> inspectionItems = task.getStandardDetailList();
        if (CollectionUtils.isEmpty(inspectionItems)) {
            throw new ServiceException("检验项为空，无法生成报告");
        }
        ArrayList<InsItemAndIndex> insItemAndIndices = new ArrayList<>();
        for (ProductReturnQualityInspectionStandardDetail inspectionItem : inspectionItems) {
            List<ProductReturnInspectionStandardItemTarget> insItemTargetList = inspectionItem.getProductReturnInspectionStandardItemTargetList();
            if (CollectionUtils.isEmpty(insItemTargetList)) {
                continue;
            }
            for (ProductReturnInspectionStandardItemTarget insItemTargetMTO : insItemTargetList) {
                InsItemAndIndex insItemAndIndex = new InsItemAndIndex();
                insItemAndIndex.setId(inspectionItem.getId());
                insItemAndIndex.setGeneralCode(inspectionItem.getCode());
                insItemAndIndex.setName(inspectionItem.getName());
                insItemAndIndex.setFeature(inspectionItem.getFeature());
                insItemAndIndex.setPackageType(inspectionItem.getPackageType());
                insItemAndIndex.setSampleSize(inspectionItem.getSampleSize());
                insItemAndIndex.setSamplingPoint(inspectionItem.getSamplingPoint());
                insItemAndIndex.setSendInspectPoint(inspectionItem.getSendInspectPoint());
                insItemAndIndex.setRemark(inspectionItem.getRemark());
                insItemAndIndex.setIndexName(insItemTargetMTO.getName());
                insItemAndIndex.setUnit(insItemTargetMTO.getUnit());
                insItemAndIndex.setStandardValue(insItemTargetMTO.getStandardValue());
                insItemAndIndex.setUpperValue(insItemTargetMTO.getUpperValue());
                insItemAndIndex.setLowerValue(insItemTargetMTO.getLowerValue());
                insItemAndIndex.setIsContainUpper(insItemTargetMTO.getIsContainUpper());
                insItemAndIndex.setIsContainLower(insItemTargetMTO.getIsContainLower());
                insItemAndIndex.setResultValue(insItemTargetMTO.getResultValue());
                insItemAndIndex.setDescription(insItemTargetMTO.getDescription());
                insItemAndIndices.add(insItemAndIndex);
            }
        }

        if (CollectionUtils.isEmpty(insItemAndIndices)) {
           throw new ServiceException("检验项指标为空，无法生成报告");
        }

        map.put("insItemAndIndexList", insItemAndIndices);
        DataProxy.super.afterSingleFetch(map);
    }
}
