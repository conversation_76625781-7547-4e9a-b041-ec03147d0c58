package cec.jiutian.bc.productionStartupRelease.domain.AproveTask.modle;

import cec.jiutian.bc.changeRequestManagement.enums.ApproveResultEnum;
import cec.jiutian.bc.changeRequestManagement.enums.ApproveTaskStatusEnum;
import cec.jiutian.bc.productionStartupRelease.domain.AproveTask.handler.PLSRApproveDataViewHandler;
import cec.jiutian.bc.productionStartupRelease.domain.AproveTask.handler.PLSRApproveHandler;
import cec.jiutian.bc.productionStartupRelease.domain.AproveTask.mto.PLSRApproveMTO;
import cec.jiutian.bc.productionStartupRelease.domain.AproveTask.mto.PLSRReportMTO;
import cec.jiutian.bc.productionStartupRelease.domain.AproveTask.proxy.PLSRApproveTaskDataProxy;
import cec.jiutian.bc.productionStartupRelease.enums.ApproveNodeEnum;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.PrePersist;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

@Getter
@Setter
@Entity
@Table(name = "qms_plsr_approve_task")
@FabosJson(
        name = "审批任务",
        orderBy = "createDate desc",
        dataProxy = PLSRApproveTaskDataProxy.class,
        power = @Power(add = false, edit = false, delete = false, export = false, print = false),
        rowOperation = {
                @RowOperation(
                        title = "审批",
                        code = "PLSRApproveTask@APPROVE",
                        ifExpr = "status !='WAIT_APPROVE'", //禁用按钮
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = PLSRApproveMTO.class,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        operationHandler = PLSRApproveHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "PLSRApproveTask@APPROVE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
                @RowOperation(
                        title = "查看表单",
                        code = "PLSRApproveTask@ViewData",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = PLSRReportMTO.class,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        operationHandler = PLSRApproveDataViewHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "PLSRApproveTask@ViewData"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
        }
)
public class PLSRApproveTask extends BaseModel {

    @FabosJsonField(
            views = @View(title = "审批任务名称"),
            edit = @Edit(title = "审批任务名称",
                    search = @Search(vague = true),
                    readonly = @Readonly,
                    inputType = @InputType(length = 30)
            )
    )
    @Column(length = 30, nullable = false)
    private String taskName;

    @FabosJsonField(
            views = @View(title = "审批意见"),
            edit = @Edit(
                    title = "审批意见",
                    inputType = @InputType(length = 10),
                    search = @Search,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ApproveResultEnum.class)
            )
    )
    @Column(length = 10)
    private String result;

    @FabosJsonField(
            views = @View(title = "意见说明"),
            edit = @Edit(
                    title = "意见说明",
                    type = EditType.TEXTAREA,
                    inputType = @InputType(length = 100)
            )
    )
    @Column(length = 100)
    private String explain;

    @FabosJsonField(
            views = @View(title = "审批人ID", show = false),
            edit = @Edit(
                    title = "审批人ID",
                    show = false,
                    inputType = @InputType(length = 20)
            )
    )
    private String assignee;

    @FabosJsonField(
            views = @View(title = "角色", show = false),
            edit = @Edit(title = "角色",
                    show = false
            )
    )
    private String role;

    @FabosJsonField(
            views = @View(title = "附件"),
            edit = @Edit(title = "附件",
                    readonly = @Readonly,
                    inputType = @InputType(length = 300),
                    type = EditType.ATTACHMENT,
                    tips = "最大支持5个大小为100M的文件",
                    attachmentType = @AttachmentType(
                            size = 100 * 1024,
                            maxLimit = 5,
                            type = AttachmentType.Type.BASE)
            )
    )
    @Column(length = 300)
    private String attachment;

    @FabosJsonField(
            views = @View(title = "变更请求ID", show = false),
            edit = @Edit(title = "变更请求ID",
                    show = false
            )
    )
    @Column(length = 40, nullable = false)
    private String businessKey;

    @FabosJsonField(
            views = @View(title = "审批任务编码", show = false),
            edit = @Edit(title = "审批任务编码",
                    show = false,
                    inputType = @InputType(length = 30)
            )
    )
    @Column(length = 30, nullable = false)
    private String code;

    @FabosJsonField(
            views = @View(title = "审批时间",
                    type = ViewType.DATE_TIME),
            edit = @Edit(title = "审批时间",
                    show = false,
                    search = @Search(vague = true),
                    dateType = @DateType(type = DateType.Type.DATE_TIME))
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date approveTime;

    @FabosJsonField(
            views = @View(title = "创建时间",
                    type = ViewType.DATE_TIME),
            edit = @Edit(title = "创建时间",
                    show = false,
                    search = @Search(vague = true),
                    dateType = @DateType(type = DateType.Type.DATE_TIME))
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date createDate;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",
                    defaultVal = "WAIT_APPROVE",
                    type = EditType.CHOICE,
                    search = @Search(defaultVal = "WAIT_APPROVE"),
                    choiceType = @ChoiceType(fetchHandler = ApproveTaskStatusEnum.class),
                    inputType = @InputType(length = 20)
            )
    )
    @Column(length = 20, nullable = false)
    private String status;

    @FabosJsonField(
            views = @View(title = "操作人", show = false),
            edit = @Edit(
                    title = "操作人",
                    show = false,
                    inputType = @InputType(length = 20)
            )
    )
    private String operator;

    @FabosJsonField(
            views = @View(title = "操作人ID", show = false),
            edit = @Edit(
                    title = "操作人ID",
                    show = false,
                    inputType = @InputType(length = 20)
            )
    )
    private String operatorId;


    /**
     * 节点存在被驳回后再次进入的情况，通过此字段区分
     */
    @FabosJsonField(
            views = @View(title = "节点审批轮数", show = false),
            edit = @Edit(
                    title = "节点审批轮数",
                    show = false,
                    inputType = @InputType(length = 20)
            )
    )
    @Column(nullable = false)
    private Integer turn;

    @PrePersist
    public void prePersist() {
        if (this.assignee == null && this.role == null) {
            throw new ServiceException("参数异常");
        }
        if (this.businessKey == null) {
            throw new ServiceException("表单id为空");
        }
        if (this.turn == null) {
            this.turn = 0;
        }
    }

    private static PLSRApproveTask create(String businessKsy, Integer turn) {
        PLSRApproveTask approveTask = new PLSRApproveTask();
        approveTask.setBusinessKey(businessKsy);
        approveTask.setCreateDate(new Date());
        approveTask.setTurn(turn);
        approveTask.setStatus(ApproveTaskStatusEnum.Enum.WAIT_APPROVE.name());
        return approveTask;
    }


    public static PLSRApproveTask createRoleTask(String businessKsy, String roleId, ApproveNodeEnum.Node node, Integer turn) {
        if (businessKsy == null || roleId == null) {
            throw new ServiceException("参数有误");
        }
        PLSRApproveTask approveTask = create(businessKsy, turn);
        approveTask.setRole(roleId);
        approveTask.setCode(node.name());
        approveTask.setTaskName(node.getValue());
        return approveTask;
    }

    public static PLSRApproveTask createUserTask(String businessKsy, String userId, ApproveNodeEnum.Node node, Integer turn) {
        if (StringUtils.isBlank(businessKsy) || StringUtils.isBlank(userId)) {
            throw new ServiceException("参数有误");
        }
        PLSRApproveTask approveTask = create(businessKsy, turn);
        approveTask.setAssignee(userId);
        approveTask.setCode(node.name());
        approveTask.setTaskName(node.getValue());
        return approveTask;
    }


}
