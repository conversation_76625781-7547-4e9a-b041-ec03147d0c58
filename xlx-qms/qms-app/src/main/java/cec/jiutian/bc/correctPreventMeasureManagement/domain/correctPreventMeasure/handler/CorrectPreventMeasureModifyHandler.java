package cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.handler;

import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.model.CorrectPreventMeasure;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.mto.CorrectPreventMeasureCreateMTO;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.mto.RelatedDocumentMTO;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/28
 * @description TODO
 */
@Component
public class CorrectPreventMeasureModifyHandler implements OperationHandler<CorrectPreventMeasure, CorrectPreventMeasureCreateMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<CorrectPreventMeasure> data, CorrectPreventMeasureCreateMTO modelObject, String[] param) {
        if (modelObject != null) {
            CorrectPreventMeasure model = data.get(0);
            BeanUtil.copyProperties(modelObject, model, "createBy", "createTime");
            fabosJsonDao.mergeAndFlush(model);
        }
        return "alert(操作成功)";
    }

    @Override
    public CorrectPreventMeasureCreateMTO fabosJsonFormValue(List<CorrectPreventMeasure> data, CorrectPreventMeasureCreateMTO fabosJsonForm, String[] param) {
        CorrectPreventMeasure model = data.get(0);
        BeanUtil.copyProperties(model, fabosJsonForm);
        if (fabosJsonForm.getRelatedDocumentMTOCode() != null) {
            RelatedDocumentMTO condition = new RelatedDocumentMTO();
            condition.setCode(fabosJsonForm.getRelatedDocumentMTOCode());
            RelatedDocumentMTO mto = fabosJsonDao.selectOne(condition);
            if (mto != null) {
                fabosJsonForm.setRelatedDocumentMTO(mto);
            }
        }
        return fabosJsonForm;
    }
}
