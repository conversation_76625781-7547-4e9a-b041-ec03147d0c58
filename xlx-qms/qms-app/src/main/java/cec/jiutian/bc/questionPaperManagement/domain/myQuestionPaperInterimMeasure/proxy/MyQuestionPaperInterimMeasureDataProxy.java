package cec.jiutian.bc.questionPaperManagement.domain.myQuestionPaperInterimMeasure.proxy;

import cec.jiutian.bc.questionPaperManagement.domain.myQuestionPaperInterimMeasure.model.MyQuestionPaperInterimMeasure;
import cec.jiutian.bc.questionPaperManagement.domain.questionPaper.model.QuestionPaper;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import cec.jiutian.view.query.Condition;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class MyQuestionPaperInterimMeasureDataProxy implements DataProxy<MyQuestionPaperInterimMeasure> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String beforeFetch(List<Condition> conditions) {
        String userId = UserContext.getUserId();
        if (StringUtils.isBlank(userId)) {
            throw new RuntimeException("当前用户未登录");
        }
        return "MyQuestionPaperInterimMeasure.responsiblePersonId = '" + userId + "'";
    }

    @Override
    public void afterFetch(Collection<Map<String, Object>> list) {
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(d -> {
                if (d.get("questionPaper") != null) {
                    HashMap<String, Object> map = (HashMap<String, Object>) d.get("questionPaper");
                    QuestionPaper questionPaper = fabosJsonDao.findById(QuestionPaper.class, map.get("id"));
                    d.put("questionNumber", questionPaper.getGeneralCode());
                    d.put("questionDescription", questionPaper.getQuestionDescription());
                    d.put("eventDescription", questionPaper.getEventDescription());
                    d.put("causeAnalysis", questionPaper.getCauseAnalysis());
                }
            });
        }
    }

}
