package cec.jiutian.bc.inventoryInspection.domain.publicInspectionTask.mto;

import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.Search;

//@Getter
//@Setter
//@Entity
//@Table(name = "mi_inspection_task")
//@FabosJson(
//        name = "任务单",
//        orderBy = "createTime desc"
//)
public class InventoryUserTaskResMTO extends MetaModel {

    @FabosJsonField(
            views = @View(title = "任务单号"),
            edit = @Edit(title = "任务单号",
                    search = @Search,
                    readonly = @Readonly(
                            add = true,
                            edit = true
                    )
            )
    )
   //@Column(name = "task_number")
    private String taskNumber; // 检验任务单号 (IQC-XXXXX)

    @FabosJsonField(
            views = @View(title = "到货单号"),
            edit = @Edit(title = "到货单号",
                    readonly = @Readonly(add = true, edit = true)
            )
    )
   //@Column(name = "delivery_note_number")
    private String deliveryNoteNumber; // 到货单号

    @FabosJsonField(
            views = @View(title = "物资名称"),
            edit = @Edit(title = "物资名称",
                    readonly = @Readonly(add = true, edit = true)
            )
    )
   //@Column(name = "material_name")
    private String materialName; // 物资名称

    @FabosJsonField(
            views = @View(title = "规格型号"),
            edit = @Edit(title = "规格型号",
                    readonly = @Readonly(add = true, edit = true)
            )
    )
   //@Column(name = "specification_model")
    private String specificationModel; // 规格型号

    @FabosJsonField(
            views = @View(title = "数量"),
            edit = @Edit(title = "数量",
                    readonly = @Readonly(add = true, edit = true)
            )
    )
   //@Column(name = "quantity")
    private Double quantity; // 数量

//    @FabosJsonField(
//            views = @View(title = "检验项目列表",column = "name"),
//            edit = @Edit(title = "检验项目列表",
//                    type = EditType.TAB_TABLE_ADD
//            )
//    )
//    @OneToMany(cascade = CascadeType.ALL)
//    @JoinColumn(name = "inventory_inspection_task_id")
//    private List<InventoryInsItemDetailEnterResMTO> inspectionItems; // 检验项目列表

}
