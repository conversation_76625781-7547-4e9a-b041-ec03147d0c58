package cec.jiutian.bc.inventoryInspection.statistics.service;

import cec.jiutian.bc.ao.QueryAO;
import cec.jiutian.bc.dto.ChartData;
import cec.jiutian.bc.enums.CharType;
import cec.jiutian.bc.enums.LineStyle;
import cec.jiutian.bc.inventoryInspection.statistics.dto.PassRateDTO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class ParetoService {

    @Resource
    private PassRateService passRateService;

    public ChartData getParetoOfInsItem(QueryAO queryAO) {
        // 1. 获取原始数据
        List<PassRateDTO> passRateDTOS = passRateService.queryInsItemPassRateData(queryAO);
        return getPareto(passRateDTOS);
    }

    public ChartData getParetoOfMaterialCode(QueryAO queryAO) {
        // 1. 获取原始数据
        List<PassRateDTO> passRateDTOS = passRateService.queryCodePassRateData(queryAO);
        return getPareto(passRateDTOS);
    }


    private ChartData getPareto(List<PassRateDTO> passRateDTOS) {
        // 2. 过滤掉不良数量为0的项（可选）并排序
        List<PassRateDTO> filteredList = passRateDTOS.stream()
                .filter(dto -> dto.getDefectiveCount() > 0)
                .sorted(Comparator.comparingDouble(PassRateDTO::getDefectiveCount).reversed())
                .collect(Collectors.toList());

        // 3. 计算总不良数量
        double totalDefects = filteredList.stream()
                .mapToDouble(PassRateDTO::getDefectiveCount)
                .sum();

        // 4. 准备图表数据
        List<String> itemNames = new ArrayList<>(10);
        List<Double> defectCounts = new ArrayList<>(10);
        List<Double> cumulativePercentages = new ArrayList<>(10);

        double cumulative = 0;

        //数量大于10，后边的归为其他
        if (filteredList.size() > 10) {
            for (int i = 0; i < 10; i++) {
                PassRateDTO dto = filteredList.get(i);
                double percentage = totalDefects > 0 ? (dto.getDefectiveCount() / totalDefects) : 0;
                cumulative += percentage;

                itemNames.add(dto.getName());
                defectCounts.add(dto.getDefectiveCount());
                cumulativePercentages.add(Math.round(cumulative * 1.0) / 100.0); // 保留两位小
            }
            itemNames.add("其他");
            defectCounts.add(totalDefects - filteredList.get(9).getDefectiveCount());
            cumulativePercentages.add(Math.round(cumulative * 1.0) / 100.0);

        }else {
            for (PassRateDTO dto : filteredList) {
                double percentage = totalDefects > 0 ? (dto.getDefectiveCount() / totalDefects) : 0;
                cumulative += percentage;

                itemNames.add(dto.getName());
                defectCounts.add(dto.getDefectiveCount());
                cumulativePercentages.add(Math.round(cumulative * 1.0) / 100.0); // 保留两位小数
            }
        }

        // 5. 构建ChartData对象
        ChartData chartData = new ChartData();

        // 设置X轴数据（检测项名称）
        ChartData.XAxis xAxis = new ChartData.XAxis();
        xAxis.setData(itemNames);
        xAxis.getAxisLabel().setRotate(270);
        chartData.setXAxis(xAxis);

        // 设置Y轴（左侧：不良数量，右侧：累计百分比）
        List<ChartData.YAxis> yAxes = new ArrayList<>();

        // 左侧Y轴（不良数量）
        ChartData.YAxis leftYAxis = new ChartData.YAxis();
        leftYAxis.setPosition("left");
        leftYAxis.setName("数量/重量");
        leftYAxis.setShow(true);

        // 右侧Y轴（累计百分比）
        ChartData.YAxis rightYAxis = new ChartData.YAxis();
        rightYAxis.setPosition("right");
        rightYAxis.setName("累计占比");
        rightYAxis.setShow(false);
        rightYAxis.setType("value");

        yAxes.add(leftYAxis);
        yAxes.add(rightYAxis);
        chartData.setYAxis(yAxes);

        // 设置数据系列
        List<ChartData.Series> seriesList = new ArrayList<>();

        // 柱状图系列（不良数量）
        ChartData.Series barSeries = ChartData.Series.createSeries(
                "不合格数量",
                CharType.bar.name(),
                0,
                defectCounts.stream().map(Object.class::cast).collect(Collectors.toList()),
                "#5470C6",
                false
        );

        // 折线图系列（累计百分比）
        ChartData.Series lineSeries = ChartData.Series.createSeries(
                "累计百分比",
                CharType.line.name(),
                1,
                cumulativePercentages.stream().map(Object.class::cast).collect(Collectors.toList()),
                "#EE6666",
                true
        );
        // 设置折线样式
        lineSeries.setIsPercent(true);
        ChartData.LineStyle lineStyle = new ChartData.LineStyle();
        lineStyle.setType(LineStyle.solid.name());
        lineSeries.setLineStyle(lineStyle);

        seriesList.add(barSeries);
        seriesList.add(lineSeries);
        chartData.setSeries(seriesList);

        return chartData;
    }
}