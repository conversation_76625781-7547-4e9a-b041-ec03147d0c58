package cec.jiutian.bc.productReturnInspection.domain.samplingTask.handler;

import cec.jiutian.bc.materialInspect.domain.samplingTask.model.SamplingTask;
import cec.jiutian.bc.modeler.enumration.TaskBusinessStateEnum;
import cec.jiutian.bc.productReturnInspection.domain.samplingTask.model.ProductReturnSamplingTaskOpr;
import cec.jiutian.bc.productReturnInspection.domain.samplingTask.model.ProductReturnSamplingTaskOprDetail;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.DependFiled;
import cn.hutool.core.bean.BeanUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：2025/3/20 14:35
 * @description：
 */
@Component
public class ProductReturnSamplingTaskOprDynamicHandler implements DependFiled.DynamicHandler<ProductReturnSamplingTaskOpr> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public Map<String, Object> handle(ProductReturnSamplingTaskOpr productReturnSamplingTaskOpr) {
        Map<String, Object> result = new HashMap<>();
        if (StringUtils.isNotEmpty(productReturnSamplingTaskOpr.getGeneralCode())) {
            SamplingTask condition = new SamplingTask();
            condition.setGeneralCode(productReturnSamplingTaskOpr.getGeneralCode());
            condition.setBusinessState(TaskBusinessStateEnum.Enum.SAMPLING_FINISH.name());
            SamplingTask samplingTask = fabosJsonDao.selectOne(condition);
            if (samplingTask == null) {
                throw new FabosJsonApiErrorTip("未查询到取样单，请确认");
            }
            result.put("inspectionTaskCode",samplingTask.getInspectionTaskCode());
            result.put("samplingTaskCode",samplingTask.getGeneralCode());
            result.put("materialCode", samplingTask.getMaterialCode());
            result.put("materialName", samplingTask.getMaterialName());
            result.put("originLotId", samplingTask.getOriginLotId());
            result.put("allInspectionItemQuantity", samplingTask.getAllInspectionItemQuantity());
            result.put("unit", samplingTask.getUnit());
            result.put("inspectionType", samplingTask.getInspectionType());
            result.put("samplingPoint",samplingTask.getSamplePoint());
            result.put("sendInspectPoint", samplingTask.getSendPoint());
            result.put("sendSamplePersonId", samplingTask.getSendSamplePersonId());
            result.put("sendSamplePerson", samplingTask.getSendSamplePerson());
            result.put("sendSampleDate", samplingTask.getSendSampleDate());
            result.put("businessState", samplingTask.getBusinessState());
            result.put("packageType",samplingTask.getPackageType());
            if (CollectionUtils.isNotEmpty(samplingTask.getDetailList())) {
                List<ProductReturnSamplingTaskOprDetail> detailList = new ArrayList<>();
                samplingTask.getDetailList().forEach(samplingTaskDetail -> {
                    ProductReturnSamplingTaskOprDetail oprDetail = new ProductReturnSamplingTaskOprDetail();
                    BeanUtil.copyProperties(samplingTaskDetail, oprDetail);
                    detailList.add(oprDetail);
                });
                result.put("detailList", detailList);
            }
        }
        return result;
    }
}
