package cec.jiutian.bc.productExamineManagement.domain.examineImplementPlan.handler;

import cec.jiutian.bc.productExamineManagement.domain.examineImplementPlan.mto.ProductExamineExecuteMTO;
import cec.jiutian.view.DependFiled;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/6/24
 * @description TODO
 */
@Component
public class UnqualifiedScoreDynamicHandler implements DependFiled.DynamicHandler<ProductExamineExecuteMTO> {
    @Override
    public Map<String, Object> handle(ProductExamineExecuteMTO productExamineExecuteMTO) {
        Map<String, Object> result = new HashMap<>();
        if (productExamineExecuteMTO.getWeightingFactor() != null && productExamineExecuteMTO.getUnqualifiedQuantity() != null) {
            Double unqualifiedScore = productExamineExecuteMTO.getWeightingFactor() * productExamineExecuteMTO.getUnqualifiedQuantity();
            result.put("unqualifiedScore",unqualifiedScore);
        }
        return result;
    }
}
