package cec.jiutian.bc.productionStartupRelease.domain.productionStartupRelease.handler;

import cec.jiutian.bc.mto.ProductProcessMTO;
import cec.jiutian.bc.productionStartupRelease.domain.productionStartupRelease.model.ProductProcess;
import cec.jiutian.bc.productionStartupRelease.domain.productionStartupRelease.mto.PLSRReportAddMTO;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.view.ReferenceAddType;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class ProductProcessAddHandler implements
        ReferenceAddType.ReferenceAddHandler<PLSRReportAddMTO, ProductProcessMTO> {
    @Override
    public Map<String, Object> handle(PLSRReportAddMTO plsrReportAddMTO, List<ProductProcessMTO> productProcessMTOS) {

        if (CollectionUtils.isEmpty(productProcessMTOS)) {
            throw new ServiceException("选择数据为空");
        }
        ArrayList<ProductProcess> productProcesses = new ArrayList<>(productProcessMTOS.size());
        for (ProductProcessMTO productProcessMTO : productProcessMTOS) {
            ProductProcess productProcess = new ProductProcess();
            productProcess.setCode(productProcessMTO.getCode());
            productProcess.setName(productProcessMTO.getName());
            productProcess.setOriginalId(productProcessMTO.getId());
            productProcess.setCreateTime(LocalDateTime.now());
            productProcesses.add(productProcess);
        }
        HashMap<String, Object> res = new HashMap<>(4);
        res.put("process", productProcesses);
        return res;
    }
}
