package cec.jiutian.bc.accidentReportManagement.domain.armMyTempMeasureTask.handler;

import cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.model.AccidentReportTask;
import cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.model.ArmTemporaryMeasure;
import cec.jiutian.bc.accidentReportManagement.domain.armMyTempMeasureTask.model.ArmMyTempMeasureTask;
import cec.jiutian.bc.accidentReportManagement.enumeration.ArmImprovingProgressEnum;
import cec.jiutian.bc.accidentReportManagement.enumeration.ArmMeasureStatusEnum;
import cec.jiutian.bc.accidentReportManagement.enumeration.ArmProgressEnum;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component
public class ArmMyTempMeasureTaskSubmitHandler implements OperationHandler<ArmMyTempMeasureTask, Void> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<ArmMyTempMeasureTask> data, Void modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            ArmMyTempMeasureTask armMyTempMeasureTask = data.get(0);
            List<ArmTemporaryMeasure> updateDetailList = new ArrayList<>();
            for (ArmTemporaryMeasure d : armMyTempMeasureTask.getArmTemporaryMeasureList()) {
                if(!d.getUserForInsTaskMTO().getId().equals(UserContext.getUserId())){
                    continue;
                }
                if(!ArmProgressEnum.Enum.RUN_SUBMIT.name().equals(d.getProgress())){
                    throw new FabosJsonApiErrorTip("提交失败：需要完成所有任务才可提交");
                }
                updateDetailList.add(d);
            }

            updateDetailList.forEach(d->{
                d.setImprovingProgress(ArmImprovingProgressEnum.Enum.TO_BE_VERIFIED.name());
                fabosJsonDao.mergeAndFlush(d);
            });

            AccidentReportTask accidentReportTask = fabosJsonDao.findById(AccidentReportTask.class, armMyTempMeasureTask.getId());
            if(checkCurrentStatus(accidentReportTask)){
                accidentReportTask.setTemporaryMeasureStatus(ArmMeasureStatusEnum.Enum.TO_BE_VERIFIED.name());
                fabosJsonDao.mergeAndFlush(accidentReportTask);
            }
        }
        return "msg.success('操作成功')";
    }

    private boolean checkCurrentStatus(AccidentReportTask accidentReportTask) {
        for (ArmTemporaryMeasure tempMeasure : accidentReportTask.getArmTemporaryMeasureList()) {
            if (!ArmImprovingProgressEnum.Enum.TO_BE_VERIFIED.name().equals(tempMeasure.getImprovingProgress())) {
                return false;
            }
        }
        return true;
    }
}
