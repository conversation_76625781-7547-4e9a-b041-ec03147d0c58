package cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.enumration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/25
 * @description TODO
 */
public class AnalysisMethodEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum data : Enum.values()) {
            list.add(new VLModel(data.name(), data.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        PREVENTIVE_AND_CORRECTIVE_ACTION_TABLE("预防纠正措施表"),
        EIGHT_D("8D"),
        ONE_PAGE_PAPER("一页纸"),
        ACCIDENT_REPORT("事故报告"),
        ;

        private final String value;

    }
}
