package cec.jiutian.bc.productionStartupRelease.domain.productionStartupRelease.model;

import cec.jiutian.bc.productionStartupRelease.enums.ReviewResultEnum;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.NumberType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.PrePersist;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 评审结果实体类
 */

@Entity
@Table(name = "qms_plsr_review")
@Setter
@Getter
@FabosJson(
        name = "生产放行",
        orderBy = "createTime desc"
)
public class ReviewResult extends BaseModel {

    @FabosJsonField(
            views = @View(title = "总数"),
            edit = @Edit(title = "总数",
                    notNull = true,
                    type = EditType.NUMBER,
                    numberType = @NumberType(min = 0, max = 10000000)
            )
    )
    @Column(length = 10,nullable = false)
    private Integer totalCount;

    @FabosJsonField(
            views = @View(title = "通过数"),
            edit = @Edit(title = "通过数",
                    notNull = true,
                    type = EditType.NUMBER,
                    numberType = @NumberType(min = 0, max = 10000000)
            )
    )
    @Column(length = 10,nullable = false)
    private Integer passCount;
    @FabosJsonField(
            views = @View(title = "让步通过数"),
            edit = @Edit(title = "让步通过数",
                    notNull = true,
                    type = EditType.NUMBER,
                    numberType = @NumberType(min = 0, max = 10000000)
            )
    )
    @Column(length = 10,nullable = false)
    private Integer concessionPassCount;

    @FabosJsonField(
            views = @View(title = "不通过数"),
            edit = @Edit(title = "不通过数",
                    notNull = true,
                    type = EditType.NUMBER,
                    numberType = @NumberType(min = 0, max = 10000000)
            )
    )
    @Column(length = 10,nullable = false)
    private Integer rejectCount;

    @FabosJsonField(
            views = @View(title = "评审结果"),
            edit = @Edit(title = "评审结果",
            notNull = true,
            type = EditType.CHOICE,
            inputType = @InputType(length = 10),
            choiceType = @ChoiceType(fetchHandler = ReviewResultEnum.class))
    )
    @Column(length = 10,nullable = false)
    private String reviewResult;

    @PrePersist
    public void prePersist() {
        if (getCreateTime() == null) {
            setCreateTime(LocalDateTime.now());
            setCreateBy(UserContext.getUserId());
        }
    }

}