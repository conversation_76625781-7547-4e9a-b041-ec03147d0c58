package cec.jiutian.bc.clientReviewManagement.enumeration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

public class CustomerReviewNatureEnum implements ChoiceFetchHandler {

    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum data : Enum.values()) {
            list.add(new VLModel(data.name(), data.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        Edit("开立"),
        WaitSubmit("待提交"),
        WaitExecute("待执行"),
        Execute("执行中"),
        WaitCheck("待验证"),
        Complete("完成"),
        ;

        private final String value;

    }
}
