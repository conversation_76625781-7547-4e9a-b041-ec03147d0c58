package cec.jiutian.bc.changeRequestManagement.domain.ecr.model;

import cec.jiutian.bc.changeRequestManagement.domain.bo.FlowNodeBO;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.dto.ECRSubmitDTO;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.handler.ApproveDataViewHandler;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.handler.ApproveHandler;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.mto.ApproveMTO;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.mto.ChangeRequestView;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.mto.RoleMTO;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.proxy.ApproveTaskDataProxy;
import cec.jiutian.bc.changeRequestManagement.enums.ApproveResultEnum;
import cec.jiutian.bc.changeRequestManagement.enums.ApproveTaskStatusEnum;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.PrePersist;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

@Getter
@Setter
@Entity
@Table(name = "qms_ecr_approve_task")
@FabosJson(
        name = "审批任务",
        orderBy = "createDate desc",
        dataProxy = ApproveTaskDataProxy.class,
        power = @Power(add = false, edit = false, delete = false, export = false, print = false),
        rowOperation = {
                @RowOperation(
                        title = "审批",
                        code = "ApproveTask@APPROVE",
                        ifExpr = "status !='WAIT_APPROVE'", //禁用按钮
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = ApproveMTO.class,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        operationHandler = ApproveHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "ApproveTask@APPROVE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
                @RowOperation(
                        title = "查看表单",
                        code = "ApproveTask@ViewData",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = ChangeRequestView.class,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        operationHandler = ApproveDataViewHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "ApproveTask@ViewData"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
        }
)
public class ApproveTask extends BaseModel {

    @FabosJsonField(
            views = @View(title = "审批任务名称"),
            edit = @Edit(title = "审批任务名称",
                    search = @Search(vague = true),
                    readonly = @Readonly,
                    inputType = @InputType(length = 20)
            )
    )
    @Column(length = 20, nullable = false)
    private String taskName;

    @FabosJsonField(
            views = @View(title = "审批意见"),
            edit = @Edit(
                    title = "审批意见",
                    inputType = @InputType(length = 10),
                    search = @Search,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ApproveResultEnum.class)
            )
    )
    @Column(length = 10)
    private String result;

    @FabosJsonField(
            views = @View(title = "意见说明"),
            edit = @Edit(
                    title = "意见说明",
                    type = EditType.TEXTAREA,
                    inputType = @InputType(length = 100)
            )
    )
    @Column(length = 100)
    private String explain;

    @FabosJsonField(
            views = @View(title = "审批人ID", show = false),
            edit = @Edit(
                    title = "审批人ID",
                    show = false,
                    inputType = @InputType(length = 20)
            )
    )
    private String assignee;

    @FabosJsonField(
            views = @View(title = "角色", show = false),
            edit = @Edit(title = "角色",
                    show = false
            )
    )
    private String role;

    @FabosJsonField(
            views = @View(title = "附件"),
            edit = @Edit(title = "附件",
                    readonly = @Readonly,
                    inputType = @InputType(length = 300),
                    type = EditType.ATTACHMENT,
                    tips = "最大支持5个大小为100M的文件",
                    attachmentType = @AttachmentType(
                            size = 100 * 1024,
                            maxLimit = 5,
                            type = AttachmentType.Type.BASE)
            )
    )
    @Column(length = 300)
    private String attachment;

    @FabosJsonField(
            views = @View(title = "变更请求ID", show = false),
            edit = @Edit(title = "变更请求ID",
                    show = false
            )
    )
    @Column(length = 40, nullable = false)
    private String businessKey;

    @FabosJsonField(
            views = @View(title = "审批任务编码", show = false),
            edit = @Edit(title = "审批任务编码",
                    show = false,
                    inputType = @InputType(length = 20)
            )
    )
    @Column(length = 20, nullable = false)
    private String code;

    @FabosJsonField(
            views = @View(title = "审批时间",
                    type = ViewType.DATE_TIME),
            edit = @Edit(title = "审批时间",
                    show = false,
                    search = @Search(vague = true),
                    dateType = @DateType(type = DateType.Type.DATE_TIME))
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date approveTime;

    @FabosJsonField(
            views = @View(title = "创建时间",
                    type = ViewType.DATE_TIME),
            edit = @Edit(title = "创建时间",
                    show = false,
                    search = @Search(vague = true),
                    dateType = @DateType(type = DateType.Type.DATE_TIME))
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date createDate;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",
                    defaultVal = "WAIT_APPROVE",
                    type = EditType.CHOICE,
                    search = @Search(defaultVal = "WAIT_APPROVE"),
                    choiceType = @ChoiceType(fetchHandler = ApproveTaskStatusEnum.class),
                    inputType = @InputType(length = 20)
            )
    )
    @Column(length = 20, nullable = false)
    private String status;

    @FabosJsonField(
            views = @View(title = "操作人", show = false),
            edit = @Edit(
                    title = "操作人",
                    show = false,
                    inputType = @InputType(length = 20)
            )
    )
    private String operator;

    @FabosJsonField(
            views = @View(title = "操作人ID", show = false),
            edit = @Edit(
                    title = "操作人ID",
                    show = false,
                    inputType = @InputType(length = 20)
            )
    )
    private String operatorId;


    /**
     * 节点存在被驳回后再次进入的情况，通过此字段区分
     */
    @FabosJsonField(
            views = @View(title = "节点审批轮数", show = false),
            edit = @Edit(
                    title = "节点审批轮数",
                    show = false,
                    inputType = @InputType(length = 20)
            )
    )
    private Integer turn;

    @PrePersist
    public void prePersist() {
        if (this.assignee == null && this.role == null) {
            throw new ServiceException("参数异常");
        }
        if (this.businessKey == null) {
            throw new ServiceException("表单id为空");
        }
        if (this.turn == null) {
            this.turn = 0;
        }
    }

    private static ApproveTask create(String ecrId,Integer turn) {
        ApproveTask approveTask = new ApproveTask();
        approveTask.setBusinessKey(ecrId);
        approveTask.setCreateDate(new Date());
        approveTask.setTurn(turn);
        approveTask.setStatus(ApproveTaskStatusEnum.Enum.WAIT_APPROVE.name());
        return approveTask;
    }

    public static ApproveTask createExamineTask(String ecrId, ECRSubmitDTO ecrSubmitDTO, Integer turn) {
        if (StringUtils.isBlank(ecrId) || ecrSubmitDTO == null) {
            throw new ServiceException("参数有误");
        }
        ApproveTask approveTask = create(ecrId, turn);
        approveTask.setAssignee(ecrSubmitDTO.getApproveMTO().getId());
        approveTask.setCode(FlowNodeBO.Node.EXAMINE.name());
        approveTask.setTaskName(FlowNodeBO.Node.EXAMINE.getValue());
        approveTask.setStatus(ApproveTaskStatusEnum.Enum.WAIT_APPROVE.name());
        return approveTask;
    }

    public static ApproveTask createRoleTask(String ecrId, String roleId,FlowNodeBO.Node node, Integer turn) {
        if (ecrId == null || roleId == null) {
            throw new ServiceException("参数有误");
        }
        ApproveTask approveTask = create(ecrId, turn);
        approveTask.setRole(roleId);
        approveTask.setCode(node.name());
        approveTask.setTaskName(node.getValue());
        return approveTask;
    }

    public static ApproveTask createUserTask(String ecrId, String userId, FlowNodeBO.Node node, Integer turn) {
        if (StringUtils.isBlank(ecrId) || StringUtils.isBlank(userId)) {
            throw new ServiceException("参数有误");
        }
        ApproveTask approveTask = create(ecrId, turn);
        approveTask.setAssignee(userId);
        approveTask.setCode(node.name());
        approveTask.setTaskName(node.getValue());
        return approveTask;
    }

    public static ApproveTask createReviewExamineTask(String ecrId, RoleMTO role, Integer turn) {
        return createRoleTask(ecrId, role.getId(),FlowNodeBO.Node.REVIEW,turn);
    }

    public static ApproveTask createApprove1ExamineTask(String ecrId, RoleMTO role, Integer turn) {
        return createRoleTask(ecrId, role.getId(),FlowNodeBO.Node.APPROVE_1, turn);
    }

    public static ApproveTask createApprove2ExamineTask(String ecrId, RoleMTO role, Integer turn) {
        return createRoleTask(ecrId, role.getId(),FlowNodeBO.Node.APPROVE_2, turn);
    }

    public static ApproveTask createVerifyTask(String ecnId, String roleId,Integer turn) {
        return createRoleTask(ecnId,roleId,FlowNodeBO.Node.VERIFY_REVIEW, turn);
    }

    public static ApproveTask createVerifyATask(String ecnId, String roleId, Integer turn) {
        return createRoleTask(ecnId,roleId,FlowNodeBO.Node.VERIFY_REVIEW_A, turn);
    }

    public static ApproveTask createVerifyApproveTask(String ecnId, String roleId, Integer turn) {
        return createRoleTask(ecnId,roleId,FlowNodeBO.Node.VERIFY_APPROVE, turn);
    }

    public static ApproveTask createCloseConfirmTask(String ecnId, String userId,Integer turn) {
        return createUserTask(ecnId, userId, FlowNodeBO.Node.WAIT_CLOSING,turn);
    }

    public static ApproveTask createSupplementVerifyTask(String ecnId, String roleId, Integer turn) {
        return createRoleTask(ecnId,roleId,FlowNodeBO.Node.SUPPLEMENT_VERIFY, turn);
    }

}
