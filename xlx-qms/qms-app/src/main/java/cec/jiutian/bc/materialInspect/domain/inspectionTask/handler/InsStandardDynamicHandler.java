package cec.jiutian.bc.materialInspect.domain.inspectionTask.handler;

import cec.jiutian.bc.basicData.domain.inspectionStandard.model.InspectionStandard;
import cec.jiutian.bc.materialInspect.domain.inspectionTask.model.IncomingInspectionTask;
import cec.jiutian.view.DependFiled;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class InsStandardDynamicHandler implements DependFiled.DynamicHandler<IncomingInspectionTask> {

    @Override
    public Map<String, Object> handle(IncomingInspectionTask incomingInspectionTask) {
        Map<String, Object> map = new HashMap<>();
        map.put("inspectionStandard",new InspectionStandard());
        return map;
    }
}
