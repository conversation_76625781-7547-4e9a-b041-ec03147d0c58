package cec.jiutian.bc.clientComplaintManagement.enumeration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/05/20
 * @description TODO
 */
public class UpgradeMechanismEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum value : Enum.values()) {
            list.add(new VLModel(value.name(), value.getValue()));
        }
        return list;
    }

    //TODO：这个升级机制的枚举量
    @AllArgsConstructor
    @Getter
    public enum Enum {
        WAIT_RUN("未完成"),
        RUN_SUBMIT("已完成"),
        ;

        private final String value;

    }

}
