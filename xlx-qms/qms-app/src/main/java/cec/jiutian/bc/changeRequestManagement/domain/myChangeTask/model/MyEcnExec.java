package cec.jiutian.bc.changeRequestManagement.domain.myChangeTask.model;

import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.ReferenceGenerateType;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.type.Power;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@Entity
@Table(name = "qms_change_request",
        indexes = {
                @Index(name = "idx_group_code", columnList = "general_code", unique = true)
        })
@FabosJson(
        name = "我的补充变更任务-执行",
        orderBy = "createTime desc",
        power = @Power(add = false, edit = false,delete = false, export = false)
)
public class MyEcnExec extends BaseModel {

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "ecn_id")
    @FabosJsonField(
            views = @View(title = "补充变更任务", column = "changeItem", type = ViewType.TABLE_VIEW),
            edit = @Edit(
                    title = "补充变更任务",
                    type = EditType.TAB_REFERENCE_GENERATE,
                    allowAddMultipleRows = true),
            referenceGenerateType = @ReferenceGenerateType(editable = {"progress", "supplementTaskCompletionDate", "attachment"}
            )
    )
    private List<MyExecECNItem> myExecECNItems;
}
