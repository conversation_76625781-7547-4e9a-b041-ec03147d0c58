package cec.jiutian.bc.processAuditManage.domain.processAuditImplementPlan.enumeration;

import cec.jiutian.bc.systemAuditManage.enumeration.AuditImplementPlanStatusEnum;
import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

public class ProcessAuditImplementPlanStatusEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (AuditImplementPlanStatusEnum.Enum value : AuditImplementPlanStatusEnum.Enum.values()) {
            list.add(new VLModel(value.name(), value.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        CREATED("新建"),
        PUBLICISED("已发布"),
        EXECUTING("执行中"),
        WAIT_AUDIT("待审批"),
        END("结束"),
        ;

        private final String value;

    }
}
