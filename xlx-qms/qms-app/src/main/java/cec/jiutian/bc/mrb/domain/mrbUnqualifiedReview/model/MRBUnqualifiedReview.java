package cec.jiutian.bc.mrb.domain.mrbUnqualifiedReview.model;

import cec.jiutian.bc.materialInspect.domain.publicInspectionTask.mto.UserForInsTaskMTO;
import cec.jiutian.bc.modeler.enumration.UnqualifiedLevelEnum;
import cec.jiutian.bc.mrb.domain.mrbUnqualifiedReview.enumration.MRBAnalysisMethodEnum;
import cec.jiutian.bc.mrb.domain.mrbUnqualifiedReview.enumration.MRBApplyRangeEnum;
import cec.jiutian.bc.modeler.enumration.MRBDisposalOpinionEnum;
import cec.jiutian.bc.mrb.domain.mrbUnqualifiedReview.handler.*;
import cec.jiutian.bc.mrb.domain.mrbUnqualifiedReview.mto.*;
import cec.jiutian.bc.mrb.enumeration.MRBUnqualifiedBusinessStatusEnum;
import cec.jiutian.bc.mrb.domain.mrbUnqualifiedReview.proxy.MRBUnqualifiedReviewProxy;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.*;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowBaseOperation;
import cec.jiutian.view.type.RowOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/5
 * @description TODO
 */
@FabosJson(
        name = "MRB不合格评审",
        orderBy = "MRBUnqualifiedReview.createTime desc",
        dataProxy = MRBUnqualifiedReviewProxy.class,
        power = @Power(add = false, edit = false, viewDetails = false),
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "businessStatus != 'TO_BE_RELEASED'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                )
        },
        rowOperation = {
                @RowOperation(
                        title = "创建",
                        code = "MRBUnqualifiedReview@CREATE",
                        mode = RowOperation.Mode.BUTTON,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = MRBUnqualifiedReviewCreateMTO.class,
                        operationHandler = MRBUnqualifiedReviewCreateHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "MRBUnqualifiedReview@CREATE"
                        )
                ),
                @RowOperation(
                        title = "编辑",
                        code = "MRBUnqualifiedReview@MODIFY",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = MRBUnqualifiedReviewCreateMTO.class,
                        operationHandler = MRBUnqualifiedReviewModifyHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "MRBUnqualifiedReview@MODIFY"
                        ),
                        ifExpr = "selectedItems[0].businessStatus != 'TO_BE_RELEASED'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "发布",
                        code = "MRBUnqualifiedReview@RELEASE",
                        operationHandler = MRBUnqualifiedReviewReleaseHandler.class,
                        mode = RowOperation.Mode.HEADER,
                        callHint = "是否确定操作？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "MRBUnqualifiedReview@RELEASE"
                        ),
                        ifExpr = "selectedItems[0].businessStatus != 'TO_BE_RELEASED'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "处置方案",
                        code = "MRBUnqualifiedReview@DISPOSALPLAN",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = MRBUnqualifiedReviewDisposalPlanMTO.class,
                        operationHandler = MRBUnqualifiedDisposalPlanHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "MRBUnqualifiedReview@DISPOSALPLAN"
                        ),
                        ifExpr = "selectedItems[0].businessStatus != 'PENDING'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "处置提交",
                        code = "MRBUnqualifiedReview@DISPOSALSUB",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = MRBUnqualifiedReviewDisposalSubMTO.class,
                        operationHandler = MRBUnqualifiedDisposalSubHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "MRBUnqualifiedReview@DISPOSALSUB"
                        ),
                        ifExpr = "selectedItems[0].businessStatus != 'TO_BE_SUBMITTED'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "查看",
                        code = "MRBUnqualifiedReview@EXAMINEVIEW",
                        mode = RowOperation.Mode.SINGLE,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.NONE,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = MRBUnqualifiedReviewExaminViewMTO.class,
                        operationHandler = MRBUnqualifiedReviewExaminViewHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "MRBUnqualifiedReview@EXAMINEVIEW"
                        )
                ),
        }
)
@Table(name = "qms_mrb_unqualified_review",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Getter
@Setter
@TemplateType(type = "multiTable")
public class MRBUnqualifiedReview extends MetaModel {
    //创建
    @FabosJsonField(
            views = @View(title = "不合格评审单号"),
            edit = @Edit(title = "不合格评审单号")
    )
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "检验任务类型"),
            edit = @Edit(title = "检验任务类型", type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = MRBApplyRangeEnum.class))
    )
    private String inspectionTaskType;

    @FabosJsonField(
            views = @View(title = "检验任务", column = "generalCode"),
            edit = @Edit(title = "检验任务", notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    queryCondition = "{\"inspectionType\":\"${inspectionTaskType}\"}",
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType(type = TabTableReferType.SelectShowTypeMTM.LIST, label = "generalCode")
            )
    )
    @ManyToOne
    @JoinColumn(name = "inspection_task_mto_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private InspectionTaskMTO inspectionTaskMTO;

    @FabosJsonField(
            views = @View(title = "不合格产品编码"),
            edit = @Edit(title = "不合格产品编码", notNull = true, search = @Search(vague = true), readonly = @Readonly,
                    inputType = @InputType(length = 40)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inspectionTaskMTO", beFilledBy = "materialCode"))

    )
    private String materialCode;

    @FabosJsonField(
            views = @View(title = "不合格产品"),
            edit = @Edit(title = "不合格产品", notNull = true, search = @Search(vague = true), readonly = @Readonly,
                    inputType = @InputType(length = 40)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inspectionTaskMTO", beFilledBy = "materialName"))

    )
    private String materialName;

    @FabosJsonField(
            views = @View(title = "产品批号"),
            edit = @Edit(title = "产品批号", notNull = true, search = @Search(vague = true), readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inspectionTaskMTO", beFilledBy = "lotSerialId"))

    )
    private String lotSerialId;

    @FabosJsonField(
            views = @View(title = "物资分级"),
            edit = @Edit(title = "物资分级", notNull = true, search = @Search(vague = true), readonly = @Readonly,
                    inputType = @InputType(length = 40)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inspectionTaskMTO", beFilledBy = "materialLevel"))

    )
    private String materialLevel;

    @FabosJsonField(
            views = @View(title = "数量"),
            edit = @Edit(title = "数量", notNull = true, search = @Search(vague = true), readonly = @Readonly,
                    inputType = @InputType(length = 40)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inspectionTaskMTO", beFilledBy = "quantity"))

    )
    private Double quantity;

    @FabosJsonField(
            views = @View(title = "工序"),
            edit = @Edit(title = "工序", notNull = true, search = @Search(vague = true), readonly = @Readonly,
                    inputType = @InputType(length = 40)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inspectionTaskMTO", beFilledBy = "operationName"))

    )
    private String operationName;

    @FabosJsonField(
            views = @View(title = "检验人"),
            edit = @Edit(title = "检验人", notNull = true, search = @Search(vague = true), readonly = @Readonly,
                    inputType = @InputType(length = 40)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inspectionTaskMTO", beFilledBy = "userName"))

    )
    private String userName;

    @FabosJsonField(
            views = @View(title = "不合格等级"),
            edit = @Edit(title = "不合格等级",
                    type = EditType.CHOICE,
                    notNull = true,
                    readonly = @Readonly(),
                    search = @Search(vague = true),
                    choiceType = @ChoiceType(fetchHandler = UnqualifiedLevelEnum.class)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inspectionTaskMTO", beFilledBy = "unqualifiedLevel"))

    )
    private String unqualifiedLevel;

    @FabosJsonField(
            views = @View(title = "检验完成时间"),
            edit = @Edit(title = "检验完成时间",
                    dateType = @DateType(type = DateType.Type.DATE_TIME),
                    readonly = @Readonly()
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date detectCompleteTime;

    @OneToMany(cascade = CascadeType.ALL)
    @FabosJsonField(
            views = @View(title = "检验结果", type = ViewType.TABLE_VIEW),
            edit = @Edit(title = "检验结果", type = EditType.TAB_REFERENCE_GENERATE),
            referenceGenerateType = @ReferenceGenerateType()
    )
    @JoinColumn(name = "mrb_unqualified_review_id")
    private List<UnqualifiedReviewItem> itemList;

    //处置方案：
    @FabosJsonField(
            views = @View(title = "处置意见"),
            edit = @Edit(title = "处置意见", notNull = true, type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = MRBDisposalOpinionEnum.class))
    )
    private String handlingSuggestion;

    @FabosJsonField(
            views = @View(title = "分析方法"),
            edit = @Edit(title = "分析方法", type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = MRBAnalysisMethodEnum.class))
    )
    private String analysisMethod;

    @FabosJsonField(
            views = @View(title = "作业指导书"),
            edit = @Edit(title = "作业指导书",
                    type = EditType.TEXTAREA, inputType = @InputType(length = 400),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "handlingSuggestion != 'REWORK'", enableOrDisable = "handlingSuggestion != 'REWORK'")
            )
    )
    private String workingInstruction;

    @FabosJsonField(
            views = @View(title = "附件"),
            edit = @Edit(title = "附件",
                    type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(size = 5120, maxLimit = 10, type = AttachmentType.Type.BASE)
            )
    )
    private String disposalAttachment;

    //处置方案创建的单号：
    @FabosJsonField(
            views = @View(title = "分析方法单号"),
            edit = @Edit(title = "分析方法单号", show = false)
    )
    private String analysisMethodDocument;

    //处置方案创建的单号：
    @FabosJsonField(
            views = @View(title = "处置意见单号"),
            edit = @Edit(title = "处置意见单号", show = false)
    )
    private String handlingSuggestionDocument;

    //评审
    @FabosJsonField(
            views = @View(title = "质量管理部评审人", column = "name"),
            edit = @Edit(title = "质量管理部评审人",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    readonly = @Readonly(),
                    referenceTableType = @ReferenceTableType(type = TabTableReferType.SelectShowTypeMTM.LIST)
            )
    )
    @ManyToOne
    @JoinColumn(name = "quality_depart_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private UserForInsTaskMTO qualityDepart;

    @FabosJsonField(
            views = @View(title = "分管领导评审人", column = "name"),
            edit = @Edit(title = "分管领导评审人",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    readonly = @Readonly(),
                    referenceTableType = @ReferenceTableType(type = TabTableReferType.SelectShowTypeMTM.LIST),
                    dependFieldDisplay = @DependFieldDisplay(notNull = "unqualifiedLevel == 'Slight'", showOrHide = "unqualifiedLevel == 'Slight'")
            )
    )
    @ManyToOne
    @JoinColumn(name = "response_leader_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private UserForInsTaskMTO responseLeader;

    @FabosJsonField(
            views = @View(title = "总经理评审人", column = "name"),
            edit = @Edit(title = "总经理评审人",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    readonly = @Readonly(),
                    referenceTableType = @ReferenceTableType(type = TabTableReferType.SelectShowTypeMTM.LIST),
                    dependFieldDisplay = @DependFieldDisplay(notNull = "unqualifiedLevel != 'Serious'", showOrHide = "unqualifiedLevel != 'Serious'")
            )
    )
    @ManyToOne
    @JoinColumn(name = "general_manager_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private UserForInsTaskMTO generalManager;

    //审核信息
    @OneToMany(cascade = CascadeType.ALL)
    @FabosJsonField(
            views = @View(title = "审核进度", type = ViewType.TABLE_VIEW, show = false),
            edit = @Edit(title = "审核进度", type = EditType.TAB_REFERENCE_GENERATE, show = false),
            referenceGenerateType = @ReferenceGenerateType()
    )
    @JoinColumn(name = "mrb_unqualified_review_id")
    private List<UnqualifiedReviewApprovalInfo> approvalInfoList;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",
                    show = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = MRBUnqualifiedBusinessStatusEnum.class)
            )
    )
    private String businessStatus;
}
