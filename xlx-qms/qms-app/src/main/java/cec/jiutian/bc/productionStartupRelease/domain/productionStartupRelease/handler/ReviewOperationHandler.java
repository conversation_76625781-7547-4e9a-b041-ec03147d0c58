package cec.jiutian.bc.productionStartupRelease.domain.productionStartupRelease.handler;

import cec.jiutian.bc.productionStartupRelease.domain.productionStartupRelease.model.PLSRReport;
import cec.jiutian.bc.productionStartupRelease.domain.productionStartupRelease.model.ReviewResult;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class ReviewOperationHandler implements OperationHandler<PLSRReport, ReviewResult> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<PLSRReport> data, ReviewResult modelObject, String[] param) {

        if (CollectionUtils.isEmpty(data)) {
            throw new ServiceException("选择数据为空");
        }
        PLSRReport plsrReport = data.get(0);
        List<ReviewResult> results = plsrReport.getResults();
        if (CollectionUtils.isEmpty(results)) {
            results.add(modelObject);
        } else {
            results.clear();
            results.add(modelObject);
        }
        plsrReport.setResults(results);
        fabosJsonDao.mergeAndFlush(plsrReport);
        return "";
    }

    @Override
    public ReviewResult fabosJsonFormValue(List<PLSRReport> data, ReviewResult fabosJsonForm, String[] param) {
        if (CollectionUtils.isEmpty(data)) {
            throw new ServiceException("请选择操作数据");
        }
        PLSRReport plsrReport = data.get(0);
        List<ReviewResult> results = plsrReport.getResults();
        if (CollectionUtils.isEmpty(results)) {
            return new ReviewResult();
        }
        return results.get(0);
    }
}
