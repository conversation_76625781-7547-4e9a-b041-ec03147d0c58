package cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.proxy;

import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.enumration.CpmBusinessStateEnum;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.mto.CorrectPreventMeasureCreateMTO;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.mto.RelatedDocumentMTO;
import cec.jiutian.bc.urm.domain.user.entity.User;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/4/28
 * @description TODO
 */
@Component
public class CorrectPreventMeasureCreateMTODataProxy implements DataProxy<CorrectPreventMeasureCreateMTO> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public void beforeAdd(CorrectPreventMeasureCreateMTO mto) {
        if (mto == null) {
            return;
        }
        User user = fabosJsonDao.getById(User.class, UserContext.getUserId());
        mto.setCreateUser(user);
        mto.setBusinessState(CpmBusinessStateEnum.Enum.TO_BE_RELEASED.name());
    }

    @Override
    public void afterSingleFetch(Map<String, Object> map) {
        if (map.get("relatedDocumentMTOCode") != null) {
            RelatedDocumentMTO mto = fabosJsonDao.findById(RelatedDocumentMTO.class, map.get("relatedDocumentMTOCode"));
            if (mto != null) {
                map.put("relatedDocumentMTO", mto);
            } else {
                throw new FabosJsonApiErrorTip("未查到关联单据，请确认");
            }
        }
    }

}
