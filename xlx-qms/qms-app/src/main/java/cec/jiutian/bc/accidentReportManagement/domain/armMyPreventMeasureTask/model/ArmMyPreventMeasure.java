package cec.jiutian.bc.accidentReportManagement.domain.armMyPreventMeasureTask.model;

import cec.jiutian.bc.accidentReportManagement.enumeration.ArmImprovingProgressEnum;
import cec.jiutian.bc.accidentReportManagement.enumeration.ArmProgressEnum;
import cec.jiutian.bc.materialInspect.domain.publicInspectionTask.mto.UserForInsTaskMTO;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@Entity
@Table(name = "qms_arm_prevent_measure")
@FabosJson(
        name = "我的预防措施",
        orderBy = "ArmMyPreventMeasure.createTime desc"
)
public class ArmMyPreventMeasure extends MetaModel {
    @FabosJsonField(
            views = @View(title = "预防措施"),
            edit = @Edit(title = "预防措施", notNull = true)
    )
    private String preventiveMeasure;;

    @FabosJsonField(
            views = @View(title = "责任人", column = "name"),
            edit = @Edit(title = "责任人",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    readonly = @Readonly(),
                    referenceTableType = @ReferenceTableType(type = TabTableReferType.SelectShowTypeMTM.LIST)
            )
    )
    @ManyToOne
    @JoinColumn(name = "responsible_person_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private UserForInsTaskMTO userForInsTaskMTO;

    @FabosJsonField(
            views = @View(title = "纳期"),
            edit = @Edit(title = "纳期",
                    dateType = @DateType(type = DateType.Type.DATE_TIME),
                    notNull = true
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date deliveryTime;

    @FabosJsonField(
            views = @View(title = "改善进度"),
            edit = @Edit(title = "改善进度",
                    readonly = @Readonly(),
                    type = EditType.CHOICE,
                    show = false,
                    choiceType = @ChoiceType(fetchHandler = ArmImprovingProgressEnum.class)
            )
    )
    private String improvingProgress;

    //是否完成
    @FabosJsonField(
            views = @View(title = "是否完成"),
            edit = @Edit(title = "是否完成",
                    inputType = @InputType(length = 20),
                    defaultVal = "WAIT_RUN",
                    type = EditType.CHOICE,
                    notNull = true,
                    choiceType = @ChoiceType(fetchHandler = ArmProgressEnum.class))
    )
    @Column(length = 20)
    private String progress;

    @FabosJsonField(
            views = @View(title = "完成日期"),
            edit = @Edit(title = "完成日期",
                    notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date completionDate;

    @FabosJsonField(
            views = @View(title = "完成佐证材料"),
            edit = @Edit(title = "完成佐证材料", type = EditType.ATTACHMENT,
                    readonly = @Readonly(),
                    attachmentType = @AttachmentType)
    )
    private String completeSupportMaterial;
}
