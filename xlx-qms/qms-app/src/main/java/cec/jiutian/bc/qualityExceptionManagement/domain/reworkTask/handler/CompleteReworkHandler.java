package cec.jiutian.bc.qualityExceptionManagement.domain.reworkTask.handler;

import cec.jiutian.bc.qualityExceptionManagement.domain.reworkTask.model.ReworkTask;
import cec.jiutian.bc.qualityExceptionManagement.enums.ReworkTaskStatusEnum;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class CompleteReworkHandler implements OperationHandler<ReworkTask, ReworkTask> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<ReworkTask> data, ReworkTask modelObject, String[] param) {
        if (CollectionUtils.isEmpty(data)) {
            throw new ServiceException("数据异常");
        }

        ReworkTask reworkTask = data.get(0);
        reworkTask = fabosJsonDao.findById(ReworkTask.class, reworkTask.getId());
        if (CollectionUtils.isEmpty(reworkTask.getReworkDetails())) {
            throw new ServiceException("该任务没有返工明细");
        }

        reworkTask.setStatus(ReworkTaskStatusEnum.Enum.COMPLETE.name());
        fabosJsonDao.merge(reworkTask);
        return "";
    }
}
