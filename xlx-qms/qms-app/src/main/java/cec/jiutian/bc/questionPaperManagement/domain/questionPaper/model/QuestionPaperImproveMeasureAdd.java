package cec.jiutian.bc.questionPaperManagement.domain.questionPaper.model;

import cec.jiutian.bc.processInspect.domain.publicInspectionTask.mto.ProcessUserForInsTaskMTO;
import cec.jiutian.bc.questionPaperManagement.enums.QuestionPaperMeasureProgressEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DateType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

@FabosJson(
        name = "问题一页纸-改善措施新增"
)
@Table(name = "qpm_question_paper_improve_measure")
@Entity
@Getter
@Setter
public class QuestionPaperImproveMeasureAdd extends MetaModel {

    @ManyToOne
    @FabosJsonField(
            views = {
                    @View(title = "问题一页纸", column = "generalCode", show = false)
            },
            edit = @Edit(title = "问题一页纸", type = EditType.REFERENCE_TABLE, show = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode")
            )
    )
    @JsonIgnoreProperties("improveMeasures")
    private QuestionPaperImprove questionPaper;

    @FabosJsonField(
            views = @View(title = "改善措施"),
            edit = @Edit(title = "改善措施", notNull = true)
    )
    private String measure;

    @FabosJsonField(
            views = @View(title = "突出/强调事项"),
            edit = @Edit(title = "突出/强调事项")
    )
    private String emphasizedMatter;

    @Transient
    @FabosJsonField(
            views = @View(title = "责任人", column = "name", show = false),
            edit = @Edit(title = "责任人", notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType()
            )
    )
    private ProcessUserForInsTaskMTO responsiblePersonMTO;

    @FabosJsonField(
            views = @View(title = "责任人ID", show = false),
            edit = @Edit(title = "责任人ID", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "responsiblePersonMTO", beFilledBy = "id"))
    )
    private String responsiblePersonId;

    @FabosJsonField(
            views = @View(title = "责任人"),
            edit = @Edit(title = "责任人", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "responsiblePersonMTO", beFilledBy = "name"))
    )
    private String responsiblePersonName;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "纳期", type = ViewType.DATE),
            edit = @Edit(title = "纳期", notNull = true, dateType = @DateType(type = DateType.Type.DATE))
    )
    private LocalDate deliveryDate;

    @FabosJsonField(
            views = @View(title = "进度", show = false),
            edit = @Edit(title = "进度", show = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = QuestionPaperMeasureProgressEnum.class))
    )
    private String progress;

}
