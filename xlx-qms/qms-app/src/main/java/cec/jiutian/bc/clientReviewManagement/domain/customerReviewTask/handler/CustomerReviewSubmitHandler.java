package cec.jiutian.bc.clientReviewManagement.domain.customerReviewTask.handler;

import cec.jiutian.bc.clientReviewManagement.domain.customerReviewTask.model.CustomerReviewTask;
import cec.jiutian.bc.clientReviewManagement.enumeration.CustomerReviewNatureEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class CustomerReviewSubmitHandler implements OperationHandler<CustomerReviewTask, Void> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<CustomerReviewTask> data, Void modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            CustomerReviewTask customerReviewTask = data.get(0);
            customerReviewTask.setCurrentState(CustomerReviewNatureEnum.Enum.WaitExecute.name());
            fabosJsonDao.mergeAndFlush(customerReviewTask);
        }
        return "msg.success('操作成功')";
    }
}
