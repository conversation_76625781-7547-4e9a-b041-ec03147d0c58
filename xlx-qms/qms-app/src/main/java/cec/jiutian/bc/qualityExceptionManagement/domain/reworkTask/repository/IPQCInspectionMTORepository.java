package cec.jiutian.bc.qualityExceptionManagement.domain.reworkTask.repository;

import cec.jiutian.bc.qualityExceptionManagement.domain.reworkTask.mto.IPQCInspectionMTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface IPQCInspectionMTORepository extends JpaRepository<IPQCInspectionMTO, String> {

    IPQCInspectionMTO findFirstByActualLotSerialIdOrderByCreateTimeDesc(@Param("actualLotSerialId") String actualLotSerialId);
}
