package cec.jiutian.bc.customerReturnProductHand.domain.productHand.model;


import cec.jiutian.bc.customerReturnProductHand.domain.productHand.proxy.TestRunProxy;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.View;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "test_run")
@FabosJson(
        name = "测试运行",
        orderBy = "createTime desc",
        dataProxy = TestRunProxy.class
)
public class TestRun extends MetaModel {


    @FabosJsonField(
            views = @View(title = "顾客名称"),
            edit = @Edit(title = "顾客名称"))
    private String name;
}
