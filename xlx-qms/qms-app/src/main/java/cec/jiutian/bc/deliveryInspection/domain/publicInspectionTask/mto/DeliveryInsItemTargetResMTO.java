package cec.jiutian.bc.deliveryInspection.domain.publicInspectionTask.mto;

import cec.jiutian.bc.basicData.enumeration.InspectionValueTypeEnum;
import cec.jiutian.bc.deliveryInspection.enumeration.InsItemResEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.Search;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Table(name = "mi_quality_inspection_standard_detail_target")
@FabosJson(
        name = "检验项指标",
        orderBy = "createTime desc"
)
@Entity
@Getter
@Setter
public class DeliveryInsItemTargetResMTO extends MetaModel {

    @FabosJsonField(
            views = @View(title = "检验项指标名称"),
            edit = @Edit(title = "检验项指标名称", search = @Search(vague = true),
                    readonly = @Readonly,
                    inputType = @InputType(length = 40))
    )
    private String name;

    @FabosJsonField(
            views = @View(title = "值类型"),
            edit = @Edit(title = "值类型", type = EditType.CHOICE,search = @Search(),
                    readonly = @Readonly,
                    choiceType = @ChoiceType(fetchHandler = InspectionValueTypeEnum.class))
    )
    private String inspectionValueType;

    @FabosJsonField(
            views = @View(title = "单位"),
            edit = @Edit(title = "单位",
                    readonly = @Readonly,
                    inputType = @InputType(length = 40))
    )
    private String unit;

    @FabosJsonField(
            views = @View(title = "标准值"),
            edit = @Edit(title = "标准值",notNull = true)
    )
    private String standardValue;

    @FabosJsonField(
            views = @View(title = "上限值"),
            edit = @Edit(title = "上限值",
                    readonly = @Readonly,
                    numberType = @NumberType(min=0,max = 9999999,precision = 2))
    )
    private Double UpperValue;

    @FabosJsonField(
            views = @View(title = "是否包含上限值"),
            edit = @Edit(title = "是否包含上限值",
                    readonly = @Readonly,
                    defaultVal = "true"
            )
    )
    private Boolean isContainUpper;

    @FabosJsonField(
            views = @View(title = "下限值"),
            edit = @Edit(title = "下限值",
                    readonly = @Readonly,
                    numberType = @NumberType(min=0,max = 9999999,precision = 2))
    )
    private Double lowerValue;

    @FabosJsonField(
            views = @View(title = "是否包含下限值"),
            edit = @Edit(title = "是否包含下限值",
                    readonly = @Readonly,
                    defaultVal = "true"
            )
    )
    private Boolean isContainLower;

    @FabosJsonField(
            views = @View(title = "检验值"),
            edit = @Edit(title = "检验值")
    )
    private String resultValue;

    @FabosJsonField(
            views = @View(title = "判定结果"),
            edit = @Edit(title = "判定结果",
                    readonly = @Readonly,
                    choiceType = @ChoiceType(fetchHandler = InsItemResEnum.ChoiceFetch.class))
    )
    private String judge;

    @FabosJsonField(
            views = @View(title = "描述"),
            edit = @Edit(title = "描述",
                    type = EditType.TEXTAREA)
    )
    private String description;


}
