package cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.issueList.proxy;

import cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.issueList.model.IssueListANACorrection;
import cec.jiutian.view.fun.DataProxy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;

@Slf4j
@Component
public class IssueListANACorrectionDataProxy implements DataProxy<IssueListANACorrection> {

    @Override
    public void afterSingleFetch(Map<String, Object> map) {
        log.info("IssueListANADataProxy afterSingleFetch"+map);
        DataProxy.super.afterSingleFetch(map);
    }

    @Override
    public void afterFetch(Collection<Map<String, Object>> list) {
        log.info("IssueListANADataProxy afterFetch"+list);
        DataProxy.super.afterFetch(list);
    }
}
