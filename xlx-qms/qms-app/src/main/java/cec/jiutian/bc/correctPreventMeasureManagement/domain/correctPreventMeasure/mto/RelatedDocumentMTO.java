package cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.mto;

import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.View;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025/4/28
 * @description TODO
 */
@FabosJson(
        name = "关联单据MTO"
)
@Entity
@Getter
@Setter
@Table(name = "cpm_related_document_view")
public class RelatedDocumentMTO {

    @Id
    @FabosJsonField(
            edit = @Edit(title = "", show = false)
    )
    private String id;

    @FabosJsonField(
            views = @View(title = "关联单据编码"),
            edit = @Edit(title = "关联单据编码")
    )
    private String code;

    @FabosJsonField(
            views = @View(title = "关联单据名称"),
            edit = @Edit(title = "关联单据名称")
    )
    private String name;

    @FabosJsonField(
            views = @View(title = "关联单据状态",show = false),
            edit = @Edit(title = "关联单据状态", show = false)
    )
    private String status;

    @FabosJsonField(
            views = @View(title = "关联单据类型",show = false),
            edit = @Edit(title = "关联单据类型", show = false)
    )
    private String type;
}
