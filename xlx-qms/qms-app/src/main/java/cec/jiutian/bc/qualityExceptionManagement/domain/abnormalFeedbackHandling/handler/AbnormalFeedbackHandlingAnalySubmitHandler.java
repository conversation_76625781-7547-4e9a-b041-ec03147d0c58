package cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.handler;

import cec.jiutian.api.remoteCallLog.dto.RemoteCallResult;
import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.enumration.MaterialDisposalOpinionEnum;
import cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.enumration.TaskBusinessStateEnum;
import cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.model.AbnormalFeedbackHandling;
import cec.jiutian.bc.qualityExceptionManagement.domain.otherDellTask.model.OtherDellTask;
import cec.jiutian.bc.qualityExceptionManagement.domain.reworkTask.model.ReworkTask;
import cec.jiutian.bc.qualityExceptionManagement.enums.ReworkTaskStatusEnum;
import cec.jiutian.bc.qualityExceptionManagement.enums.TaskTypeEnum;
import cec.jiutian.bc.qualityExceptionManagement.port.client.BatchSerialApiFeignClient;
import cec.jiutian.bc.qualityExceptionManagement.port.dto.ProductInfoDTO;
import cec.jiutian.bc.specialMaterialHandle.domain.model.SpecialMaterialHandleRequest;
import cec.jiutian.bc.urm.domain.org.entity.Org;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/4/27
 * @description TODO
 */
@Component
@Slf4j
public class AbnormalFeedbackHandlingAnalySubmitHandler implements OperationHandler<AbnormalFeedbackHandling, Void> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private BatchSerialApiFeignClient batchSerialApiFeignClient;

    @Resource
    private NamingRuleService namingRuleService;

    @Transactional
    @Override
    public String exec(List<AbnormalFeedbackHandling> data, Void modelObject, String[] param) {
        AbnormalFeedbackHandling myDeviceInsTask = data.get(0);
        createDisposalTaskDocument(myDeviceInsTask);
        myDeviceInsTask.setBusinessState(TaskBusinessStateEnum.Enum.PENDING_REVIEW.name());

        fabosJsonDao.update(myDeviceInsTask);
        return null;
    }

    private void createDisposalTaskDocument(AbnormalFeedbackHandling model) {
        String opinion = model.getMaterialDisposalOpinion();
        if (opinion.equals(MaterialDisposalOpinionEnum.Enum.REWORK.name())) {
            createReworkTaskByAfHandling(model);
        } else if (opinion.equals(MaterialDisposalOpinionEnum.Enum.SCRAPPED.name()) ||
                opinion.equals(MaterialDisposalOpinionEnum.Enum.DOWNGRADED_TO_CLASSB.name()) ||
                opinion.equals(MaterialDisposalOpinionEnum.Enum.DOWNGRADED_TO_CLASSC.name())) {
            createSpecialMaterialHandleRequestByAfHandling(model);
        } else {
            createOtherTaskByReturnHandling(model);
        }
    }

    public void createReworkTaskByAfHandling(AbnormalFeedbackHandling model) {
        String serialNumber = model.getSerialNumber();
        log.info("Get Data by getProductInfoBySerialNumber, the serial number is " + serialNumber);
        ProductInfoDTO productInfoDTO = null;
        try {
            productInfoDTO = batchSerialApiFeignClient.getProductInfoBySerialNumber(serialNumber);
        } catch (Exception e) {
            throw new ServiceException("远程接口调用失败:" + e.getMessage());
        }
        if (productInfoDTO != null) {
            log.info("What the interface returns is " + productInfoDTO.toString());
        } else {
            throw new FabosJsonApiErrorTip("根据工序流水号["+serialNumber+"]从批次系统查不到产品编码和名称");
        }
        ReworkTask reworkTask = new ReworkTask();
        reworkTask.setGeneralCode(namingRuleService.getNameCode(reworkTask.getNamingCode(), 1, reworkTask.getParameters()).get(0));
        reworkTask.setTaskType(TaskTypeEnum.Enum.ABNORMAL_FEEDBACK_HANDLING.name());
        reworkTask.setTaskCode(model.getAbnormalFeedbackHandlingFormNumber());
        if (productInfoDTO != null) {
            reworkTask.setProductCode(productInfoDTO.getMaterialCode());
            reworkTask.setProductName(productInfoDTO.getMaterialName());
        }
        reworkTask.setBatchCode(serialNumber);
        reworkTask.setStatus(ReworkTaskStatusEnum.Enum.CREATE.name());
        reworkTask.setReworkQuantity(model.getAffectsMaterialWeight());
        reworkTask.setExamineStatus("0");

        fabosJsonDao.saveOrUpdate(reworkTask);
        log.info("ReworkTask create success.");
    }

    private void createSpecialMaterialHandleRequestByAfHandling(AbnormalFeedbackHandling model) {
        log.info("Create SpecialMaterialHandleRequest");
        UserContext.CurrentUser currentUser = UserContext.get();
        SpecialMaterialHandleRequest materialHandleRequest = new SpecialMaterialHandleRequest();
        materialHandleRequest.setGeneralCode(namingRuleService.getNameCode(materialHandleRequest.getNamingCode(), 1, materialHandleRequest.getParameters()).get(0));
        materialHandleRequest.setApplicantId(currentUser.getUserId());
        materialHandleRequest.setApplicant(currentUser.getUserName());

        Org condition = new Org();
        condition.setId(currentUser.getOrgId());
        Org org = fabosJsonDao.selectOne(condition);
        materialHandleRequest.setDepartmentId(org.getId());
        materialHandleRequest.setApplyDepartment(org.getName());

        fabosJsonDao.mergeAndFlush(materialHandleRequest);
        log.info("SpecialMaterialHandleRequest create success.");
    }

    public void createOtherTaskByReturnHandling(AbnormalFeedbackHandling model) {
        String serialNumber = model.getSerialNumber();
        log.info("Get Data by getProductInfoBySerialNumber, the serial number is " + serialNumber);
        ProductInfoDTO productInfoDTO = batchSerialApiFeignClient.getProductInfoBySerialNumber(serialNumber);
        if (Objects.isNull(productInfoDTO)) {
            throw new FabosJsonApiErrorTip("根据工序流水号["+serialNumber+"]从批次系统查不到产品编码和名称");
        }
        log.info("What the interface returns is " + productInfoDTO.toString());

        OtherDellTask task = new OtherDellTask();
        task.setGeneralCode(namingRuleService.getNameCode(task.getNamingCode(), 1, task.getParameters()).get(0));
        task.setPlan(model.getMaterialDisposalOpinion());
        task.setTaskType(TaskTypeEnum.Enum.ABNORMAL_FEEDBACK_HANDLING.name());
        task.setTaskCode(model.getAbnormalFeedbackHandlingFormNumber());
        task.setProductCode(productInfoDTO.getMaterialCode());
        task.setProductName(productInfoDTO.getMaterialName());
        task.setBatchCode(serialNumber);
        task.setStatus(ReworkTaskStatusEnum.Enum.CREATE.name());
        task.setReworkQuantity(model.getAffectsMaterialWeight());
        task.setExamineStatus("0");

        fabosJsonDao.saveOrUpdate(task);
        log.info("OtherDellTask create success.");
    }
}
