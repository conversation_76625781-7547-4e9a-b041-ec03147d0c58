package cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.issueList.remote.provider;

import cec.jiutian.bc.ecs.dto.SendMsgGroupDTO;
import cec.jiutian.bc.ecs.provider.EcsMessageProvider;
import cec.jiutian.bc.job.provider.IJobProvider;
import cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.issueList.model.IssueList;
import cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.issueList.model.IssueListCorrection;
import cec.jiutian.bc.qualityExceptionManagement.enumeration.inspectionIssuesImproveAndTrace.ImproveProgressEnum;
import cec.jiutian.bc.qualityExceptionManagement.enumeration.inspectionIssuesImproveAndTrace.IssueListStatusEnum;
import cec.jiutian.core.frame.annotation.FabosCustomizedService;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.meta.FabosJob;
import cn.hutool.core.date.DateUtil;
import jakarta.annotation.Resource;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@FabosCustomizedService(value = IssueListCorrection.class)
@Slf4j
@Component
@Transactional
public class IssueListCorrectionScheduleProvider implements IJobProvider {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private EcsMessageProvider ecsMessageProvider;

    @FabosJob(comment = "问题清单整改措施定时任务")
    @Override
    public String exec(String code, String param) {
        IssueListCorrection condition = new IssueListCorrection();
        condition.setProgress(ImproveProgressEnum.Enum.WAIT_RUN.name());
        List<IssueListCorrection> corrections = fabosJsonDao.select(condition);
        for (IssueListCorrection correction : corrections) {
            if (checkDate(correction.getMeasureDate())) {
                sendQms(correction);
            }
        }
        return "";
    }

    private boolean checkDate(Date measureDate) {
        int compare = DateUtil.compare(measureDate, DateUtil.date(), "yyyy-MM-dd HH");
        return (compare <= 0);
    }

    private void sendQms(IssueListCorrection correction) {
        SendMsgGroupDTO sendMsgGroupDTO = new SendMsgGroupDTO();
        sendMsgGroupDTO.setMessageGroupCode("CorrectionOverTimeInfo");
        sendMsgGroupDTO.setContent("请尽快完成整改措施["+correction.getCorrection()+"]的处理, 纳期时间["+correction.getMeasureDate()+"]");
        //log.warn("请尽快完成整改措施["+correction.getCorrection()+"]的处理, 纳期时间["+correction.getMeasureDate()+"]");
        ecsMessageProvider.sendGroupMessage(sendMsgGroupDTO);
    }
}
