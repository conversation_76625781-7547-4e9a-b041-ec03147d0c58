package cec.jiutian.bc.qualityExceptionManagement.domain.returnProductHandling.proxy;

import cec.jiutian.bc.qualityExceptionManagement.domain.returnProductHandling.model.ReturnProductHandlingHandle;
import cec.jiutian.bc.qualityExceptionManagement.enums.ReturnProductHandlingStatusEnum;
import cec.jiutian.view.fun.DataProxy;

public class ReturnProductHandlingHandleDataProxy implements DataProxy<ReturnProductHandlingHandle> {

    @Override
    public void beforeUpdate(ReturnProductHandlingHandle entity) {
        entity.setStatus(ReturnProductHandlingStatusEnum.Enum.WaitReview.name());
    }

}
