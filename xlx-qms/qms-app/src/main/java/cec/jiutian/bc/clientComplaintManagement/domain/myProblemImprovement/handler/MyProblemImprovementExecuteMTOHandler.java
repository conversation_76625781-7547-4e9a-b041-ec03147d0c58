package cec.jiutian.bc.clientComplaintManagement.domain.myProblemImprovement.handler;

import cec.jiutian.bc.clientComplaintManagement.domain.myProblemImprovement.model.MyProblemImprovement;
import cec.jiutian.bc.clientComplaintManagement.domain.myProblemImprovement.mto.MyProblemImprovementExecuteMTO;
import cec.jiutian.bc.clientComplaintManagement.enumeration.ProblemImprovementStatusEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/28
 * @description TODO
 */
@Component
public class MyProblemImprovementExecuteMT<PERSON>and<PERSON> implements OperationHandler<MyProblemImprovement, MyProblemImprovementExecuteMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<MyProblemImprovement> data, MyProblemImprovementExecuteMTO modelObject, String[] param) {
        modelObject.setBusinessStatus(ProblemImprovementStatusEnum.Enum.EXECUTED.name());

        MyProblemImprovement model = data.get(0);
        BeanUtil.copyProperties(modelObject, model, "createBy", "createTime");
        fabosJsonDao.mergeAndFlush(model);
        return "alert(操作成功)";
    }

    @Override
    public MyProblemImprovementExecuteMTO fabosJsonFormValue(List<MyProblemImprovement> data, MyProblemImprovementExecuteMTO fabosJsonForm, String[] param) {
        MyProblemImprovement model = data.get(0);
        BeanUtil.copyProperties(model, fabosJsonForm);
        return fabosJsonForm;
    }
}
