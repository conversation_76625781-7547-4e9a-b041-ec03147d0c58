package cec.jiutian.bc.qualityExceptionManagement.domain.reworkTask.schedule;

import cec.jiutian.bc.changeRequestManagement.enums.ReqTraceResultEnum;
import cec.jiutian.bc.job.provider.IJobProvider;
import cec.jiutian.bc.qualityExceptionManagement.domain.reworkTask.model.ReworkDetail;
import cec.jiutian.bc.qualityExceptionManagement.domain.reworkTask.model.ReworkTrace;
import cec.jiutian.bc.qualityExceptionManagement.domain.reworkTask.mto.IPQCInspectionMTO;
import cec.jiutian.bc.qualityExceptionManagement.domain.reworkTask.repository.IPQCInspectionMTORepository;
import cec.jiutian.bc.qualityExceptionManagement.domain.reworkTask.repository.ReworkTraceRepository;
import cec.jiutian.core.frame.annotation.FabosCustomizedService;
import cec.jiutian.meta.FabosJob;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@FabosCustomizedService(value = ReworkDetail.class)
@Component
public class InspectionResultQueryJob implements IJobProvider {

    @Resource
    private ReworkTraceRepository reworkTraceRepository;

    @Resource
    private IPQCInspectionMTORepository IPQCInspectionMTORepository;

    @FabosJob(comment = "返工追溯检验结果查询")
    @Override
    public String exec(String code, String param) {

        List<ReworkTrace> reworkTraceList = reworkTraceRepository.findByInspectionResultOrderByCreateTime(ReqTraceResultEnum.Enum.WAITING.name());
        for (ReworkTrace reworkTrace : reworkTraceList) {
            IPQCInspectionMTO inspectionMTO = IPQCInspectionMTORepository.findFirstByActualLotSerialIdOrderByCreateTimeDesc(reworkTrace.getSerialNumber());
            if (inspectionMTO == null || StringUtils.isBlank(inspectionMTO.getResult())) {
                continue;
            }
            reworkTrace.setInspectionCode(inspectionMTO.getGeneralCode());
            reworkTrace.setInspectionResult(inspectionMTO.getResult());
            reworkTrace.setProcessOperationId(inspectionMTO.getProcessOperationId());
            reworkTrace.setProcessCode(inspectionMTO.getOperationCode());
            reworkTrace.setProcess(inspectionMTO.getOperationName());
            reworkTraceRepository.save(reworkTrace);
        }
        return "执行成功";
    }
}
