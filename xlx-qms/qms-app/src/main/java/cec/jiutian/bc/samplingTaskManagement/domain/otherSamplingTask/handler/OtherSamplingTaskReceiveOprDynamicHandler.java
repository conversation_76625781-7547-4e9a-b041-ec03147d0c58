package cec.jiutian.bc.samplingTaskManagement.domain.otherSamplingTask.handler;

import cec.jiutian.bc.processInspect.domain.publicInspectionTask.mto.ProcessUserForInsTaskMTO;
import cec.jiutian.bc.samplingTaskManagement.domain.otherSamplingTask.model.OtherSamplingTask;
import cec.jiutian.bc.samplingTaskManagement.domain.otherSamplingTask.model.OtherSamplingTaskReceiveOpr;
import cec.jiutian.bc.samplingTaskManagement.domain.otherSamplingTask.model.OtherSamplingTaskReceiveOprDetail;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.DependFiled;
import cn.hutool.core.bean.BeanUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/5/12
 * @description TODO
 */
@Component
public class OtherSamplingTaskReceiveOprDynamicHandler implements DependFiled.DynamicHandler<OtherSamplingTaskReceiveOpr> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public Map<String, Object> handle(OtherSamplingTaskReceiveOpr otherSamplingTaskReceiveOpr) {
        Map<String, Object> result = new HashMap<>();
        if (StringUtils.isNotEmpty(otherSamplingTaskReceiveOpr.getGeneralCode())) {
            OtherSamplingTask condition = new OtherSamplingTask();
            condition.setGeneralCode(otherSamplingTaskReceiveOpr.getGeneralCode());
            OtherSamplingTask otherSamplingTask = fabosJsonDao.selectOne(condition);
            if (otherSamplingTask == null) {
                throw new FabosJsonApiErrorTip("未查询到取样单，请确认");
            }
            result.put("id",otherSamplingTask.getId());
            result.put("inspectionTaskCode",otherSamplingTask.getInspectionTaskCode());
            result.put("otherSamplingTaskCode",otherSamplingTask.getGeneralCode());
            result.put("materialCode", otherSamplingTask.getMaterialCode());
            result.put("materialName", otherSamplingTask.getMaterialName());
            result.put("originLotId", otherSamplingTask.getOriginLotId());
            result.put("allInspectionItemQuantity", otherSamplingTask.getAllInspectionItemQuantity());
            result.put("unit", otherSamplingTask.getUnit());
            result.put("inspectionType", otherSamplingTask.getInspectionType());
            result.put("samplingPoint",otherSamplingTask.getSamplePoint());
            result.put("sendInspectPoint", otherSamplingTask.getSendPoint());
            result.put("receiveSamplePersonId", otherSamplingTask.getReceiveSamplePersonId());
            result.put("receiveSamplePerson", otherSamplingTask.getReceiveSamplePerson());
            result.put("receiveSampleDate", otherSamplingTask.getReceiveSampleDate());
            if (otherSamplingTask.getReceiveSamplePersonId()!= null) {
                ProcessUserForInsTaskMTO discoveredPerson = fabosJsonDao.findById(ProcessUserForInsTaskMTO.class, otherSamplingTask.getReceiveSamplePersonId());
                if (discoveredPerson != null) {
                    result.put("user", discoveredPerson);
                } else {
                    throw new FabosJsonApiErrorTip("未查到收样人数据，请确认");
                }
            }
            if (CollectionUtils.isNotEmpty(otherSamplingTask.getDetailList())) {
                List<OtherSamplingTaskReceiveOprDetail> detailList = new ArrayList<>();
                otherSamplingTask.getDetailList().forEach(otherSamplingTaskDetail -> {
                    OtherSamplingTaskReceiveOprDetail oprDetail = new OtherSamplingTaskReceiveOprDetail();
                    BeanUtil.copyProperties(otherSamplingTaskDetail, oprDetail);
                    detailList.add(oprDetail);
                });
                result.put("detailList", detailList);
            }
        }
        return result;
    }
}
