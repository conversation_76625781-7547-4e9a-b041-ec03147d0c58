package cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.model;

import cec.jiutian.bc.materialInspect.domain.publicInspectionTask.mto.UserForInsTaskMTO;
import cec.jiutian.bc.mto.OrgMTO;
import cec.jiutian.bc.mto.ProcessFlowMTO;
import cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.enumration.*;
import cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.handler.*;
import cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.mto.*;
import cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.proxy.AbnormalFeedbackHandlingDataProxy;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.*;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/25
 * @description TODO
 */
@FabosJson(
        name = "异常反馈处理",
        orderBy = "AbnormalFeedbackHandling.createTime desc",
        power = @Power(add = false, edit = false, delete = false),
        dataProxy = AbnormalFeedbackHandlingDataProxy.class,
        rowOperation = {
                @RowOperation(
                        title = "创建",
                        code = "AbnormalFeedbackHandling@CREATE",
                        mode = RowOperation.Mode.BUTTON,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.ADD,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = AbnormalFeedbackHandlingCreateMTO.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "AbnormalFeedbackHandling@CREATE"
                        )
                ),
                @RowOperation(
                        title = "编辑",
                        code = "AbnormalFeedbackHandling@MODIFY",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = AbnormalFeedbackHandlingCreateMTO.class,
                        operationHandler = AbnormalFeedbackHandlingEditHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "AbnormalFeedbackHandling@MODIFY"
                        ),
                        ifExpr = "selectedItems[0].businessState != 'TO_BE_SUBMITTED'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "提交",
                        code = "AbnormalFeedbackHandling@SUBMIT",
                        operationHandler = AbnormalFeedbackHandlingSubmitHandler.class,
                        mode = RowOperation.Mode.HEADER,
                        callHint = "该操作不可撤回，确定提交吗？",
                        ifExpr = "selectedItems[0].businessState != 'TO_BE_SUBMITTED'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "AbnormalFeedbackHandling@SUBMIT"
                        )
                ),
                @RowOperation(
                        title = "核实",
                        code = "AbnormalFeedbackHandling@VERIFIED",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = AbnormalFeedbackHandlingVerifyMTO.class,
                        operationHandler = AbnormalFeedbackHandlingVerifyHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "AbnormalFeedbackHandling@VERIFIED"
                        ),
                        ifExpr = "selectedItems[0].businessState != 'TO_BE_VERIFIED'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "核实提交",
                        code = "AbnormalFeedbackHandling@VERIFYSUBMIT",
                        operationHandler = AbnormalFeedbackHandlingVerifySubmitHandler.class,
                        mode = RowOperation.Mode.HEADER,
                        callHint = "该操作不可撤回，确定提交吗？",
                        ifExpr = "selectedItems[0].businessState != 'TO_BE_VERIFIED' || selectedItems[0].verificationState != 'RUN_SUBMIT'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "AbnormalFeedbackHandling@VERIFYSUBMIT"
                        )
                ),
                @RowOperation(
                        title = "物料方案",
                        code = "AbnormalFeedbackHandling@ANALY",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = AbnormalFeedbackHandlingAnalyMTO.class,
                        operationHandler = AbnormalFeedbackHandlingAnalyHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "AbnormalFeedbackHandling@ANALY"
                        ),
                        ifExpr = "selectedItems[0].businessState != 'TO_BE_ANALYZED'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "方案提交",
                        code = "AbnormalFeedbackHandling@ANALYSUBMIT",
                        operationHandler = AbnormalFeedbackHandlingAnalySubmitHandler.class,
                        mode = RowOperation.Mode.HEADER,
                        callHint = "该操作不可撤回，确定提交吗？",
                        ifExpr = "selectedItems[0].businessState != 'TO_BE_ANALYZED' || selectedItems[0].analysisState != 'RUN_SUBMIT'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "AbnormalFeedbackHandling@ANALYSUBMIT"
                        )
                ),
                @RowOperation(
                        title = "方案评审",
                        code = "AbnormalFeedbackHandling@REVIEW",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = AbnormalFeedbackHandlingReviewMTO.class,
                        operationHandler = AbnormalFeedbackHandlingReviewHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "AbnormalFeedbackHandling@REVIEW"
                        ),
                        ifExpr = "selectedItems[0].businessState != 'PENDING_REVIEW'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "效果验证",
                        code = "AbnormalFeedbackHandling@SCHEMEREVIEW",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = AbnormalFeedbackHandlingSchemeMTO.class,
                        operationHandler = AbnormalFeedbackHandlingSchemeHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "AbnormalFeedbackHandling@SCHEMEREVIEW"
                        ),
                        ifExpr = "selectedItems[0].businessState != 'PENDING_SCHEME_REVIEW'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "关闭",
                        code = "AbnormalFeedbackHandling@CLOSE",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = AbnormalFeedbackHandlingCloseMTO.class,
                        operationHandler = AbnormalFeedbackHandlingCloseHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "AbnormalFeedbackHandling@CLOSE"
                        ),
                        ifExpr = "selectedItems[0].businessState != 'COMPLETED'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
        }
)
@Table(name = "qms_qe_abnormal_feedback_handling",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"abnormalFeedbackHandlingFormNumber"})
        }
)
@Entity
@Getter
@Setter
public class AbnormalFeedbackHandling extends MetaModel {
    //创建
    @FabosJsonField(
            views = @View(title = "单据编号"),
            edit = @Edit(title = "单据编号",
                    readonly = @Readonly(add = false),
                    search = @Search(vague = true),
                    notNull = true),
            dynamicField = @DynamicField(dependFiled = @DependFiled(buttonName = "生成", changeBy = "id",
                    dynamicHandler = AbnormalFeedbackHandlingDynamicHandler.class))
    )
    private String abnormalFeedbackHandlingFormNumber;

    //TODO:如何关联，在哪儿关联
    @FabosJsonField(
            views = @View(title = "分析关联单据号"),
            edit = @Edit(title = "分析关联单据号",
                    search = @Search(vague = true),
                    readonly = @Readonly(add = false),
                    inputType = @InputType(length = 40))
    )
    private String analyzeAssociatedDocumentNumber;

    @FabosJsonField(
            views = @View(title = "创建时间"),
            edit = @Edit(title = "创建时间",
                    search = @Search(vague = true),
                    type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @CreationTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date createdTime;

    @FabosJsonField(
            views = @View(title = "异常描述"),
            edit = @Edit(title = "异常描述",
                    search = @Search(),
                    type = EditType.TEXTAREA)
    )
    private String abnormalDescription;

    @FabosJsonField(
            views = @View(title = "发现时间"),
            edit = @Edit(title = "发现时间",
                    dateType = @DateType(type = DateType.Type.DATE_TIME),
                    search = @Search(),
                    notNull = true
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date submissionTime;

    @Transient
    @FabosJsonField(
            views = @View(title = "选择发现人", show = false, column = "name"),
            edit = @Edit(title = "选择发现人",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    show = false,
                    referenceTableType = @ReferenceTableType()
            )
    )
    private UserForInsTaskMTO discoveredPerson;

    @FabosJsonField(
            views = @View(title = "发现人ID", show = false),
            edit = @Edit(title = "发现人ID",
                    readonly = @Readonly(),
                    show = false
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "discoveredPerson", beFilledBy = "id"))
    )
    private String discoveredPersonId;

    @FabosJsonField(
            views = @View(title = "发现人"),
            edit = @Edit(title = "发现人",
                    readonly = @Readonly(),
                    search = @Search()
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "discoveredPerson", beFilledBy = "name"))
    )
    private String discoveredPersonName;

    @FabosJsonField(
            views = @View(title = "异常附件"),
            edit = @Edit(title = "异常附件", type = EditType.ATTACHMENT,
                    search = @Search(),
                    attachmentType = @AttachmentType)
    )
    private String abnormalAttachments;

    //核实
    @FabosJsonField(
            views = @View(title = "情况核实（初步原因分析）/意见"),
            edit = @Edit(title = "情况核实（初步原因分析）/意见",
                    search = @Search(),
                    type = EditType.TEXTAREA)
    )
    private String verifyOpinions;

    @FabosJsonField(
            views = @View(title = "异常分类"),
            edit = @Edit(title = "异常分类",
                    readonly = @Readonly(),
                    type = EditType.CHOICE,
                    search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = AnomalyClassificationEnum.class))
    )
    private String anomalyClassification;

    @FabosJsonField(
            views = @View(title = "分析方法"),
            edit = @Edit(title = "分析方法",
                    readonly = @Readonly(),
                    type = EditType.CHOICE, search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = AnalysisMethodEnum.class))
    )
    private String analysisMethod;

    @FabosJsonField(
            views = @View(title = "不合格等级"),
            edit = @Edit(title = "不合格等级",
                    readonly = @Readonly(),
                    type = EditType.CHOICE, search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = UnqualifiedLevelEnum.class))
    )
    private String unqualifiedLevel;

    @FabosJsonField(
            views = @View(title = "问题分析牵头部门", column = "name"),
            edit = @Edit(title = "问题分析牵头部门",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(type = TabTableReferType.SelectShowTypeMTM.LIST)
            )
    )
    @ManyToOne
    @JoinColumn(name = "lead_department_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private OrgMTO leadDepartment;

    @FabosJsonField(
            views = @View(title = "物料方案输出部门", column = "name"),
            edit = @Edit(title = "物料方案输出部门",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(type = TabTableReferType.SelectShowTypeMTM.LIST)
            )
    )
    @ManyToOne
    @JoinColumn(name = "output_department_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private OrgMTO outputDepartment;

    @Transient
    @FabosJsonField(
            views = @View(title = "工序流水号", column = "serialNumber", show = false),
            edit = @Edit(title = "工序流水号",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(type = TabTableReferType.SelectShowTypeMTM.LIST,
                            label = "serialNumber")
            )
    )
    private ProcessFlowMTO processFlowMTO;

    @FabosJsonField(
            views = @View(title = "工序流水号ID", show = false),
            edit = @Edit(title = "工序流水号ID",
                    show = false
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "processFlowMTO", beFilledBy = "id"))
    )
    @JoinColumn(name = "process_flow_id")
    private String processFlowMTOID;

    @FabosJsonField(
            views = @View(title = "工序流水号"),
            edit = @Edit(title = "工序流水号",
                    search = @Search(vague = true),
                    notNull = true,
                    readonly = @Readonly()
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "processFlowMTO", beFilledBy = "serialNumber"))
    )
    private String serialNumber;

    @FabosJsonField(
            views = @View(title = "工序编码", show = false),
            edit = @Edit(title = "工序编码",
                    show = false
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "processFlowMTO", beFilledBy = "processCode"))
    )
    private String processCode;

    @FabosJsonField(
            views = @View(title = "工序名称"),
            edit = @Edit(title = "工序名称",
                    search = @Search(vague = true),
                    notNull = true,
                    readonly = @Readonly()
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "processFlowMTO", beFilledBy = "processName"))
    )
    private String processName;

    @FabosJsonField(
            views = @View(title = "车间ID", show = false),
            edit = @Edit(title = "车间ID",
                    show = false
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "processFlowMTO", beFilledBy = "workshopId"))
    )
    private String workshopId;

    @FabosJsonField(
            views = @View(title = "车间名称"),
            edit = @Edit(title = "车间名称",
                    search = @Search(vague = true),
                    notNull = true,
                    readonly = @Readonly()
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "processFlowMTO", beFilledBy = "workshopName"))
    )
    private String workshopName;

    @FabosJsonField(
            views = @View(title = "产线ID", show = false),
            edit = @Edit(title = "产线ID",
                    show = false
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "processFlowMTO", beFilledBy = "productionLineId"))
    )
    private String productionLineId;

    @FabosJsonField(
            views = @View(title = "产线名称"),
            edit = @Edit(title = "产线名称",
                    search = @Search(vague = true),
                    notNull = true,
                    readonly = @Readonly()
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "processFlowMTO", beFilledBy = "productionLineName"))
    )
    private String productionLineName;

    @FabosJsonField(
            views = @View(title = "影响物料重量"),
            edit = @Edit(title = "影响物料重量", notNull = true,
                    inputGroup = @InputGroup(postfix = "#{afhWeightUnit}"),
                    numberType = @NumberType(min = 0, max = 999999999, precision = 2))
    )
    private Double affectsMaterialWeight ;

    @FabosJsonField(
            views = @View(title = "重量单位"),
            edit = @Edit(title = "重量单位",
                    type = EditType.CHOICE,
                    notNull = true,
                    search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = AfhWeightUnitEnum.class))
    )
    private String afhWeightUnit;

    @FabosJsonField(
            views = @View(title = "核实附件"),
            edit = @Edit(title = "核实附件", type = EditType.ATTACHMENT,
                    search = @Search(),
                    attachmentType = @AttachmentType)
    )
    private String verificationAttachments;

    @FabosJsonField(
            views = @View(title = "核实状态", show = false),
            edit = @Edit(title = "核实状态",
                    readonly = @Readonly(),
                    type = EditType.CHOICE,
                    show = false,
                    choiceType = @ChoiceType(fetchHandler = ProgressEnum.class)
            )
    )
    private String verificationState;

    //方案提交
    @FabosJsonField(
            views = @View(title = "解决方案"),
            edit = @Edit(title = "解决方案",
                    notNull = true,
                    type = EditType.TEXTAREA)
    )
    private String solution;

    @ManyToMany
    @JoinTable(
            name = "qms_qe_afh_org", //中间表表名
            inverseForeignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT),
            joinColumns = @JoinColumn(name = "qms_qe_afh_org_id", referencedColumnName = "id"),
            inverseJoinColumns = @JoinColumn(name = "org_id", referencedColumnName = "id"))
    @FabosJsonField(
            views = @View(title = "物料处置责任部门", type = ViewType.TABLE_VIEW, column = "name"),
            edit = @Edit(title = "物料处置责任部门", type = EditType.TAB_TABLE_REFER, search = @Search(vague = true),
                    tabTableReferType = @TabTableReferType(type = TabTableReferType.SelectShowTypeMTM.LIST,
                            label = "name")
            )
    )
    private List<OrgMTO> materialDisposalDepartmentList;

    @FabosJsonField(
            views = @View(title = "物料处置意见"),
            edit = @Edit(title = "物料处置意见",
                    type = EditType.CHOICE,
                    notNull = true,
                    search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = MaterialDisposalOpinionEnum.class))
    )
    private String materialDisposalOpinion;

    @FabosJsonField(
            views = @View(title = "分析人", column = "name"),
            edit = @Edit(title = "分析人",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType(type = TabTableReferType.SelectShowTypeMTM.LIST)
            )
    )
    @ManyToOne
    @JoinColumn(name = "analyst_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private UserForInsTaskMTO analyst;

    @FabosJsonField(
            views = @View(title = "分析时间"),
            edit = @Edit(title = "分析时间",
                    dateType = @DateType(type = DateType.Type.DATE_TIME),
                    search = @Search(),
                    notNull = true
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date analysisTime;

    @FabosJsonField(
            views = @View(title = "解决方案附件"),
            edit = @Edit(title = "解决方案附件", type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType)
    )
    private String solutionAttachments;

    @FabosJsonField(
            views = @View(title = "分析状态", show = false),
            edit = @Edit(title = "分析状态",
                    readonly = @Readonly(),
                    type = EditType.CHOICE,
                    search = @Search(),
                    show = false,
                    choiceType = @ChoiceType(fetchHandler = ProgressEnum.class)
            )
    )
    private String analysisState;

    //评审
    @FabosJsonField(
            views = @View(title = "评审意见"),
            edit = @Edit(title = "评审意见",
                    readonly = @Readonly(),
                    type = EditType.CHOICE, search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = AuditStatusEnum.class))
    )
    private String reviewComments;

    @FabosJsonField(
            views = @View(title = "意见说明"),
            edit = @Edit(title = "意见说明",
                    search = @Search(),
                    type = EditType.TEXTAREA)
    )
    private String opinionExplanation;

    @FabosJsonField(
            views = @View(title = "评审附件"),
            edit = @Edit(title = "评审附件", type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType)
    )
    private String reviewCommentsAttachments;

    //验证
    @FabosJsonField(
            views = @View(title = "效果验证"),
            edit = @Edit(title = "效果验证",
                    readonly = @Readonly(),
                    type = EditType.CHOICE, search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = AuditStatusEnum.class))
    )
    private String effectVerification;

    @Transient
    @FabosJsonField(
            views = @View(title = "选择验证人", show = false, column = "name"),
            edit = @Edit(title = "选择验证人",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    show = false,
                    referenceTableType = @ReferenceTableType()
            )
    )
    private UserForInsTaskMTO verifier;

    @FabosJsonField(
            views = @View(title = "验证人ID", show = false),
            edit = @Edit(title = "验证人ID",
                    readonly = @Readonly(),
                    show = false
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "verifier", beFilledBy = "id"))
    )
    private String verifierId;

    @FabosJsonField(
            views = @View(title = "验证人"),
            edit = @Edit(title = "验证人",
                    readonly = @Readonly(),
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "verifier", beFilledBy = "name"))
    )
    private String verifierName;

    @FabosJsonField(
            views = @View(title = "验证时间"),
            edit = @Edit(title = "验证时间",
                    dateType = @DateType(type = DateType.Type.DATE_TIME),
                    search = @Search(),
                    notNull = true
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date verifyTime;

    @FabosJsonField(
            views = @View(title = "效果验证附件"),
            edit = @Edit(title = "效果验证附件", type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType)
    )
    private String effectVeriAttachments;

    //关闭
    @Transient
    @FabosJsonField(
            views = @View(title = "选择关闭人", show = false, column = "name"),
            edit = @Edit(title = "选择关闭人",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    show = false,
                    referenceTableType = @ReferenceTableType()
            )
    )
    private UserForInsTaskMTO closePeople;

    @FabosJsonField(
            views = @View(title = "关闭人ID", show = false),
            edit = @Edit(title = "关闭人ID",
                    readonly = @Readonly(),
                    show = false
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "closePeople", beFilledBy = "id"))
    )
    private String closePeopleId;

    @FabosJsonField(
            views = @View(title = "关闭人"),
            edit = @Edit(title = "关闭人",
                    readonly = @Readonly(),
                    search = @Search()
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "closePeople", beFilledBy = "name"))
    )
    private String closePeopleName;

    @FabosJsonField(
            views = @View(title = "关闭时间"),
            edit = @Edit(title = "关闭时间",
                    dateType = @DateType(type = DateType.Type.DATE_TIME),
                    search = @Search(),
                    notNull = true
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date closeTime;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",
                    readonly = @Readonly(),
                    search = @Search(),
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = TaskBusinessStateEnum.class)
            )
    )
    private String businessState;
}
