package cec.jiutian.bc.qualityExceptionManagement.domain.otherDellTask.proxy;

import cec.jiutian.bc.flow.enums.ExamineStatusEnum;
import cec.jiutian.bc.qualityExceptionManagement.domain.otherDellTask.model.OtherDellTask;
import cec.jiutian.bc.qualityExceptionManagement.domain.reworkTask.model.ReworkTask;
import cec.jiutian.bc.qualityExceptionManagement.enums.ReworkTaskStatusEnum;
import cec.jiutian.view.fun.FlowProxy;
import org.springframework.stereotype.Component;

@Component
public class OtherDellTaskFlowProxy extends FlowProxy {

    @Override
    public void onEvent(Object event, Object entity) {
        if (entity instanceof OtherDellTask otherDellTask) {
            if (otherDellTask.getExamineStatus().equals(ExamineStatusEnum.AUDITED.getCode())) {
                // todo 预留
                otherDellTask.setStatus(ReworkTaskStatusEnum.Enum.EXECUTING.name());
            }
            if (otherDellTask.getExamineStatus().equals(ExamineStatusEnum.REJECTED.getCode())) {
                otherDellTask.setStatus(ReworkTaskStatusEnum.Enum.CREATE.name());
            }
        }
    }

    @Override
    public void afterSubmit(Object entity) {
        if (entity instanceof OtherDellTask otherDellTask) {
            otherDellTask.setStatus(ReworkTaskStatusEnum.Enum.EXECUTING.name());
        }
    }
}
