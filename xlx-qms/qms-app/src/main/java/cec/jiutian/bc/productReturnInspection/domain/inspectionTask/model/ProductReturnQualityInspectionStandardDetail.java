package cec.jiutian.bc.productReturnInspection.domain.inspectionTask.model;

import cec.jiutian.bc.modeler.domain.inspectionMethod.model.InspectionMethod;
import cec.jiutian.bc.modeler.domain.samplingPlan.model.SamplingPlan;
import cec.jiutian.bc.productReturnInspection.enumeration.InspectionResultEnum;
import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.InheritStrategy;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.*;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/10
 * @description TODO
 */
@FabosJson(
        name = "检验任务质检标准明细"
)
@Table(name = "mi_Quality_inspection_standard_detail")
@Entity
@Getter
@Setter
@InheritStrategy(
        excludeParentFields = {"isAutoJudged","resultWait","inspectionItemTargetList"}
)
public class ProductReturnQualityInspectionStandardDetail extends BaseModel {

    @ManyToOne
    @FabosJsonField(
            views = {
                    @View(title = "检验任务", column = "generalCode", show = false)
            },
            edit = @Edit(title = "检验任务", type = EditType.REFERENCE_TABLE, show = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode")
            )
    )
    @JsonIgnoreProperties("inspectionTaskDetailList")
    private ProductReturnInspectionTask productReturnInspectionTask;

    @FabosJsonField(
            views = @View(title = "检验项目id",show = false),
            edit = @Edit(title = "检验项目id",show = false)
    )
    private String itemId;

    @FabosJsonField(
            views = @View(title = "检验项目编码"),
            edit = @Edit(title = "检验项目编码", search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    private String code;

    @FabosJsonField(
            views = @View(title = "检验项目名称"),
            edit = @Edit(title = "检验项目名称", search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    private String name;

    @FabosJsonField(
            views = @View(title = "特性"),
            edit = @Edit(title = "特性", search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    private String feature;

    @FabosJsonField(
            views = @View(title = "检验项类型"),
            edit = @Edit(title = "检验项类型")
    )
    private String itemType;

    @FabosJsonField(
            views = @View(title = "取样点"),
            edit = @Edit(title = "取样点",
                    inputType = @InputType(length = 40))
    )
    private String samplingPoint;

    @FabosJsonField(
            views = @View(title = "送检点"),
            edit = @Edit(title = "送检点",
                    inputType = @InputType(length = 40))
    )
    private String sendInspectPoint;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "检验方法", column = "name", type = ViewType.TABLE_FORM),
            edit = @Edit(title = "检验方法",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    search = @Search()
            )
    )
    private InspectionMethod inspectionMethod;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "抽样方案", column = "name", type = ViewType.TABLE_FORM),
            edit = @Edit(title = "抽样方案",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    search = @Search()
            )
    )
    private SamplingPlan samplingPlan;

    @FabosJsonField(
            views = @View(title = "样本量"),
            edit = @Edit(title = "样本量",show = false,
                    numberType = @NumberType(min=0,max = 9999999,precision = 2))
    )
    private Double sampleSize;

    @FabosJsonField(
            views = @View(title = "包装方式"),
            edit = @Edit(title = "包装方式")
    )
    private String packageType;

    @FabosJsonField(
            views = @View(title = "检验结果"),
            edit = @Edit(title = "检验结果", type = EditType.CHOICE,search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = InspectionResultEnum.class))
    )
    private String inspectionResult;

    @FabosJsonField(
            views = @View(title = "检验员"),
            edit = @Edit(title = "检验员")
    )
    private String inspectPerson;

    @FabosJsonField(
            views = @View(title = "备注"),
            edit = @Edit(title = "备注",
                    type = EditType.TEXTAREA)
    )
    private String remark;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = false)
    @JoinColumn(name = "inspection_item_id")
    @FabosJsonField(
            views = @View(title = "检验项指标", type = ViewType.TABLE_VIEW),
            edit = @Edit(title = "检验项指标", type = EditType.TAB_TABLE_ADD)
    )
    private List<ProductReturnInspectionStandardItemTarget> productReturnInspectionStandardItemTargetList;

    @FabosJsonField(
            views = @View(title = "所属检验组",show = false),
            edit = @Edit(title = "所属检验组",show = false)
    )
    private String groupId;

}
