package cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.issueList.remote.feign;

import cec.jiutian.bc.modeler.domain.inspectionIssuesImproveAndTrace.dto.IssueListDTO;
import cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.issueList.service.IssueListService;
import cec.jiutian.core.frame.constant.FabosJsonRestPath;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2025/6/11
 * @description
 */
@RestController
@RequestMapping("/fabos-qms-app"+ FabosJsonRestPath.FABOS_REMOTE_API)
public class IssueListFeign {

    @Resource
    private IssueListService issueListService;

    @PostMapping("/createIssueList")
    public String createIssueList(@RequestBody IssueListDTO issueListDTO) {
        return issueListService.createIssueList(issueListDTO);
    }
}
