package cec.jiutian.bc.materialInspect.domain.samplingTask.handler;

import cec.jiutian.bc.materialInspect.domain.samplingTask.model.SamplingTask;
import cec.jiutian.bc.materialInspect.domain.samplingTask.model.SamplingTaskOpr;
import cec.jiutian.bc.modeler.enumration.TaskBusinessStateEnum;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/12
 * @description TODO
 */
@Component
public class SendSampleOperationHandler implements OperationHandler<SamplingTask, SamplingTaskOpr> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<SamplingTask> data, SamplingTaskOpr modelObject, String[] param) {
        if (modelObject != null) {
            SamplingTask condition = new SamplingTask();
            condition.setGeneralCode(modelObject.getGeneralCode());
            SamplingTask samplingTask = fabosJsonDao.selectOne(condition);
            if (samplingTask == null) {
                throw new FabosJsonApiErrorTip("未查询到取样单，请确认");
            }
            if (!TaskBusinessStateEnum.Enum.SAMPLING_FINISH.name().equals(samplingTask.getBusinessState())) {
                throw new FabosJsonApiErrorTip("取样任务单状态有误");
            }
            samplingTask.setBusinessState(TaskBusinessStateEnum.Enum.SEND_SAMPLE.name());
            samplingTask.setSendSamplePersonId(modelObject.getSendSamplePersonId());
            samplingTask.setSendSamplePerson(modelObject.getSendSamplePerson());
            samplingTask.setSendSampleDate(modelObject.getSendSampleDate());
            fabosJsonDao.mergeAndFlush(samplingTask);
        }
        return "alert(操作成功)";
    }
}
