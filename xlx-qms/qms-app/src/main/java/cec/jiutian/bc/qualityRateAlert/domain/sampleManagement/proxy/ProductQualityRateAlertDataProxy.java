package cec.jiutian.bc.qualityRateAlert.domain.sampleManagement.proxy;

import cec.jiutian.bc.qualityRateAlert.domain.sampleManagement.model.ProductQualityRateAlert;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.view.fun.DataProxy;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/06/23
 * @description
 */
@Component
public class ProductQualityRateAlertDataProxy implements DataProxy<ProductQualityRateAlert> {

    @Override
    public void beforeAdd(ProductQualityRateAlert productQualityRateAlert) {
        check(productQualityRateAlert);
    }

    private void check(ProductQualityRateAlert productQualityRateAlert) {
        if (productQualityRateAlert.getMaxAlterRate() == null && productQualityRateAlert.getMinAlterRate() == null) {
            throw new FabosJsonApiErrorTip("预警上下限不可同时为空，请确认");
        }

        if (productQualityRateAlert.getMinAlterRate() == null) {
            productQualityRateAlert.setMinAlterRate(0.0);
        }

        if (productQualityRateAlert.getMaxAlterRate() == null) {
            productQualityRateAlert.setMaxAlterRate(1.0);
        }

        if (productQualityRateAlert.getMaxAlterRate() <= productQualityRateAlert.getMinAlterRate()) {
            throw new FabosJsonApiErrorTip("预警上限值需要大于下限，请确认");
        }
    }
}
