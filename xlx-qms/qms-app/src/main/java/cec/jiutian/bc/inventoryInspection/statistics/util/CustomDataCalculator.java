package cec.jiutian.bc.inventoryInspection.statistics.util;

import cec.jiutian.bc.inventoryInspection.statistics.vo.YieldRateVO;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.Month;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

public class CustomDataCalculator {
    private static final DateTimeFormatter DATE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    // 定义各种时间格式的正则表达式
    private static final Pattern YEAR_PATTERN = Pattern.compile("^(\\d{4})年$");
    private static final Pattern QUARTER_PATTERN = Pattern.compile("^(\\d{4})年第([1-4])季度$");
    private static final Pattern MONTH_PATTERN = Pattern.compile("^(\\d{4})年(0[1-9]|1[0-2])月$");
    private static final Pattern WEEK_PATTERN = Pattern.compile("^(\\d{4})年第(0[1-9]|[1-4][0-9]|5[0-3])周$");
    private static final Pattern DAY_PATTERN = Pattern.compile("^(\\d{4})年(0[1-9]|1[0-2])月(0[1-9]|[12][0-9]|3[01])日$");

    public static Map<String, YieldRateVO.Period> calculateRanges(String[] timeStrings) {
        Map<String, YieldRateVO.Period> result = new HashMap<>();

        for (String timeStr : timeStrings) {
            YieldRateVO.Period period = new YieldRateVO.Period();

            var yearMatcher = YEAR_PATTERN.matcher(timeStr);
            var quarterMatcher = QUARTER_PATTERN.matcher(timeStr);
            var monthMatcher = MONTH_PATTERN.matcher(timeStr);
            var weekMatcher = WEEK_PATTERN.matcher(timeStr);
            var dayMatcher = DAY_PATTERN.matcher(timeStr);

            if (yearMatcher.matches()) {
                int year = Integer.parseInt(yearMatcher.group(1));
                period.setStartTime(LocalDate.of(year, 1, 1).format(DATE_FORMAT));
                period.setEndTime(LocalDate.of(year, 12, 31).format(DATE_FORMAT));
            } else if (quarterMatcher.matches()) {
                int year = Integer.parseInt(quarterMatcher.group(1));
                int quarter = Integer.parseInt(quarterMatcher.group(2));

                Month startMonth = Month.of((quarter - 1) * 3 + 1);
                Month endMonth = startMonth.plus(2);

                period.setStartTime(LocalDate.of(year, startMonth, 1).format(DATE_FORMAT));
                period.setEndTime(LocalDate.of(year, endMonth, endMonth.maxLength()).format(DATE_FORMAT));
            } else if (monthMatcher.matches()) {
                int year = Integer.parseInt(monthMatcher.group(1));
                int month = Integer.parseInt(monthMatcher.group(2));

                period.setStartTime(LocalDate.of(year, month, 1).format(DATE_FORMAT));
                period.setEndTime(LocalDate.of(year, month, Month.of(month).maxLength()).format(DATE_FORMAT));
            } else if (weekMatcher.matches()) {
                int year = Integer.parseInt(weekMatcher.group(1));
                int week = Integer.parseInt(weekMatcher.group(2));

                LocalDate start = LocalDate.of(year, 1, 1)
                        .with(TemporalAdjusters.firstInMonth(DayOfWeek.MONDAY));
                start = start.plusWeeks(week - 1);

                period.setStartTime(start.format(DATE_FORMAT));
                period.setEndTime(start.plusDays(6).format(DATE_FORMAT));
            } else if (dayMatcher.matches()) {
                int year = Integer.parseInt(dayMatcher.group(1));
                int month = Integer.parseInt(dayMatcher.group(2));
                int day = Integer.parseInt(dayMatcher.group(3));

                LocalDate date = LocalDate.of(year, month, day);
                period.setStartTime( date.format(DATE_FORMAT));
                period.setEndTime(date.format(DATE_FORMAT));
            } else {
                throw new IllegalArgumentException("无效的时间格式: " + timeStr);
            }

            result.put(timeStr, period);
        }

        return result;
    }

}
