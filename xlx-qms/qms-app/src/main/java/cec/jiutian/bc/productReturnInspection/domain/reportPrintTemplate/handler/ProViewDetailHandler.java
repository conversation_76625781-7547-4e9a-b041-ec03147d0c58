package cec.jiutian.bc.productReturnInspection.domain.reportPrintTemplate.handler;

import cec.jiutian.bc.productReturnInspection.domain.reportPrintTemplate.model.ProdReturnReportPrintTemplate;
import cec.jiutian.bc.productReturnInspection.domain.reportPrintTemplate.mto.ProReturnInspectionTaskMTO;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class ProViewDetailHandler implements OperationHandler<ProdReturnReportPrintTemplate, ProReturnInspectionTaskMTO> {
    @Resource
    private FabosJsonDao fabosJsonDao;


    @Override
    public ProReturnInspectionTaskMTO fabosJsonFormValue(List<ProdReturnReportPrintTemplate> data, ProReturnInspectionTaskMTO fabosJsonForm, String[] param) {
        ProdReturnReportPrintTemplate reportPrintTemplate = data.get(0);
        ProReturnInspectionTaskMTO task = fabosJsonDao.findById(ProReturnInspectionTaskMTO.class, reportPrintTemplate.getId());
        return task;
    }

}
