package cec.jiutian.bc.qualityExceptionManagement.domain.abnormalStopProduction.proxy;

import cec.jiutian.bc.flow.enums.ExamineStatusEnum;
import cec.jiutian.bc.qualityExceptionManagement.domain.abnormalStopProduction.model.AbnormalStopProduction;
import cec.jiutian.bc.qualityExceptionManagement.enums.AbnormalStopProductionStateEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.FlowProxy;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class AbnormalStopProductionFlowProxy extends FlowProxy {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public void onEvent(Object event, Object data) {
        if (data instanceof AbnormalStopProduction entity) {
            if (entity.getExamineStatus().equals(ExamineStatusEnum.AUDITED.getCode())) {
                entity.setCurrentState(AbnormalStopProductionStateEnum.Enum.Complete.name());

                // todo 通知生产经营部 发出停产指令

            } else if (entity.getExamineStatus().equals(ExamineStatusEnum.REJECTED.getCode())) {
                entity.setCurrentState(AbnormalStopProductionStateEnum.Enum.Edit.name());

            }
            fabosJsonDao.mergeAndFlush(entity);
        }
        log.info("异常停产单审批回调成功");
    }
}
