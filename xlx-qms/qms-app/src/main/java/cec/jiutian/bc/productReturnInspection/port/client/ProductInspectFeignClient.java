package cec.jiutian.bc.productReturnInspection.port.client;

import cec.jiutian.core.frame.constant.FabosJsonRestPath;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/3/25
 * @description TODO
 */
@Component
@FeignClient(name = "xlx-wms")
public interface ProductInspectFeignClient {

    @PostMapping("/fabos-wms-app" + FabosJsonRestPath.FABOS_REMOTE_API+"/createInspectMaterialApplyByQms")
    void createOutboundRequest(@RequestBody Map<String, Object> params);
}
