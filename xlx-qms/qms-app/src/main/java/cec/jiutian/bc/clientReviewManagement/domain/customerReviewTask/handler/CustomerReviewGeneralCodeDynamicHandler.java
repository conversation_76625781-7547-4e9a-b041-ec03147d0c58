package cec.jiutian.bc.clientReviewManagement.domain.customerReviewTask.handler;

import cec.jiutian.bc.clientReviewManagement.domain.customerReviewTask.model.CustomerReviewTask;
import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.modeler.enumration.NamingRuleCodeEnum;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class CustomerReviewGeneralCodeDynamicHandler implements DependFiled.DynamicHandler<CustomerReviewTask> {

    @Resource
    private NamingRuleService namingRuleService;

    @Override
    public Map<String, Object> handle(CustomerReviewTask customerReviewTask) {
        Map<String, Object> map = new HashMap<>();
        map.put("generalCode", String.valueOf(namingRuleService.getNameCode(NamingRuleCodeEnum.CustomerReviewTask.name(), 1, null).get(0)));
        return map;
    }
}
