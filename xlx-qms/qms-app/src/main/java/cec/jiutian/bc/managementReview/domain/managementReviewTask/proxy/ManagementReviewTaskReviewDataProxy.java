package cec.jiutian.bc.managementReview.domain.managementReviewTask.proxy;

import cec.jiutian.bc.managementReview.domain.managementReviewTask.model.ManagementReviewTaskReview;
import cec.jiutian.bc.managementReview.enumeration.ManagementReviewTaskStateEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
public class ManagementReviewTaskReviewDataProxy implements DataProxy<ManagementReviewTaskReview> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public void beforeUpdate(ManagementReviewTaskReview entity) {
        entity.setCurrentState(ManagementReviewTaskStateEnum.Enum.REVIEWED.name());
    }

}
