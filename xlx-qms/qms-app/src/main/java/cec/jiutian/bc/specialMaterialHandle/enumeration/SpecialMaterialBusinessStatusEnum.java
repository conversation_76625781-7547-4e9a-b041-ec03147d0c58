package cec.jiutian.bc.specialMaterialHandle.enumeration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/24
 * @description
 */
public class SpecialMaterialBusinessStatusEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum data : Enum.values()) {
            list.add(new VLModel(data.name(), data.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        WAIT_RELEASE("待发布"),
        WAIT_AUDIT("待审核"),
        WAIT_VERIFY("待验证"),
        COMPLETED("关闭"),
        ;
        private final String value;
    }
}
