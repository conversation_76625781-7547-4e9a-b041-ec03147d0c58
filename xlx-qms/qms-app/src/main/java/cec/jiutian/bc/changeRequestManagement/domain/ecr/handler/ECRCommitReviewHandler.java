package cec.jiutian.bc.changeRequestManagement.domain.ecr.handler;

import cec.jiutian.bc.changeRequestManagement.domain.bo.FlowNodeBO;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.model.ApproveTask;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.model.ChangeRequest;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.mto.ApproveViewMTO;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.mto.RoleMTO;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.repository.ApproveTaskRepository;
import cec.jiutian.bc.changeRequestManagement.enums.ECRStatusEnum;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Component
public class ECRCommitReviewHandler implements OperationHandler<ChangeRequest, ApproveViewMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private ApproveTaskRepository approveTaskRepository;

    @Transactional
    @Override
    public String exec(List<ChangeRequest> data, ApproveViewMTO modelObject, String[] param) {
        if (CollectionUtils.isEmpty(data) || data.get(0).getId() == null) {
            throw new ServiceException("数据异常");
        }
        if (modelObject.getInvolveCustomer() && modelObject.getMarketRole() == null) {
            throw new ServiceException("涉及客户，请选择市场部负责人");
        }
        ChangeRequest changeRequest = data.get(0);
        changeRequest.setStatus(ECRStatusEnum.Enum.WAIT_REVIEW_APPROVE.name());
        changeRequest.setInvolveCustomer(modelObject.getInvolveCustomer());
        fabosJsonDao.mergeAndFlush(changeRequest);

        List<RoleMTO> roles = modelObject.getRoles();
        if (CollectionUtils.isEmpty(roles)) {
            throw new ServiceException("请选择候选角色");
        }
        if (modelObject.getInvolveCustomer()) {
            roles.add(modelObject.getMarketRole());
        }

        Integer maxTurn = approveTaskRepository.findMaxTurnByBusinessKeyAndCode(changeRequest.getId(), FlowNodeBO.Node.REVIEW.name());
        if (maxTurn == null) {
            maxTurn = 0;
        }else {
            maxTurn++;
        }

        for (RoleMTO role : roles) {
            ApproveTask approveTask = ApproveTask.createReviewExamineTask(changeRequest.getId(), role,maxTurn);
            fabosJsonDao.persistAndFlush(approveTask);
        }
        return "提交成功";
    }

}
