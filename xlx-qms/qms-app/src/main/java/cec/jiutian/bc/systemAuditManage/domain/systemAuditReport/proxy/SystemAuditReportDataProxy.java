package cec.jiutian.bc.systemAuditManage.domain.systemAuditReport.proxy;

import cec.jiutian.bc.systemAuditManage.domain.systemAuditReport.model.SystemAuditReport;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/5/12
 * @description TODO
 */
@Component
public class SystemAuditReportDataProxy implements DataProxy<SystemAuditReport> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public void afterSingleFetch(Map<String, Object> map) {

    }
}
