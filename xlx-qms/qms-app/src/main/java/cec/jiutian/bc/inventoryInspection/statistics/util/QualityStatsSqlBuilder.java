package cec.jiutian.bc.inventoryInspection.statistics.util;

import cec.jiutian.bc.inventoryInspection.statistics.ao.StatisticsCommonParam;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Map;

public class QualityStatsSqlBuilder {
    private static final Map<String, String> DATE_TRUNC_MAP = Map.of(
            "1", "'year'",
            "2", "'quarter'",
            "3", "'month'",
            "4", "'week'",
            "5", "'day'"
    );

    private static final Map<String, String> INTERVAL_MAP = Map.of(
            "1", "INTERVAL '1 year'",
            "2", "INTERVAL '3 month'",
            "3", "INTERVAL '1 month'",
            "4", "INTERVAL '1 week'",
            "5", "INTERVAL '1 day'"
    );

    public static String buildSql(StatisticsCommonParam param) {
        return "WITH date_range AS (" + buildDateRangeCte(param) + "), \n" +
                "time_periods AS (" + buildTimePeriodsCte(param) + "), \n" +
                "quantity_stat AS (" + buildQuantityStatCte(param) + ") \n" +
                buildFinalSelect(param);
    }

    private static String buildDateRangeCte(StatisticsCommonParam param) {
        StringBuilder sb = new StringBuilder("SELECT ");

        if ("1".equals(param.getIsDiyTime()) && param.getStartTime() != null && param.getEndTime() != null) {
            // 自定义时间范围
            sb.append("TO_DATE(:startTime, 'YYYY-MM-DD') AS min_time, ")
                    .append("TO_DATE(:endTime, 'YYYY-MM-DD') AS max_time");
        } else {
            // 预设时间范围
            switch (param.getQuery()) {
                case "1": // 本年
                    sb.append("DATE_TRUNC('year', CURRENT_DATE) AS min_time, ")
                            .append("DATE_TRUNC('year', CURRENT_DATE) + INTERVAL '1 year - 1 second' AS max_time");
                    break;
                case "2": // 本季
                    sb.append("DATE_TRUNC('quarter', CURRENT_DATE) AS min_time, ")
                            .append("DATE_TRUNC('quarter', CURRENT_DATE) + INTERVAL '3 months - 1 second' AS max_time");
                    break;
                case "3": // 本月
                    sb.append("DATE_TRUNC('month', CURRENT_DATE) AS min_time, ")
                            .append("DATE_TRUNC('month', CURRENT_DATE) + INTERVAL '1 month - 1 second' AS max_time");
                    break;
                case "4": // 本周
                    sb.append("DATE_TRUNC('week', CURRENT_DATE) AS min_time, ")
                            .append("DATE_TRUNC('week', CURRENT_DATE) + INTERVAL '7 days - 1 second' AS max_time");
                    break;
                case "5": // 当天
                    sb.append("CURRENT_DATE AS min_time, ")
                            .append("CURRENT_DATE + INTERVAL '1 days - 1 second' AS max_time");
                    break;
                default: // 全部时间
                    sb.append("(SELECT MIN(output_time) FROM batch_serial) AS min_time, ")
                            .append("(SELECT MAX(output_time) FROM batch_serial) AS max_time");
            }
        }
        return sb.toString();
    }

    private static String buildTimePeriodsCte(StatisticsCommonParam param) {
        String dateTrunc = DATE_TRUNC_MAP.getOrDefault(param.getDimension(), "'year'");
        String interval = INTERVAL_MAP.getOrDefault(param.getDimension(), "INTERVAL '1 year'");

        return "SELECT generate_series( \n" +
                " date_trunc(" + dateTrunc + ", date_range.min_time), \n" +
                " date_trunc(" + dateTrunc + ", date_range.max_time), \n" +
                " " + interval + " \n" +
                ") AS time_period \n" +
                "FROM date_range";
    }

    private static String buildQuantityStatCte(StatisticsCommonParam param) {
        String dateTrunc = DATE_TRUNC_MAP.getOrDefault(param.getDimension(), "'year'");

        StringBuilder sb = new StringBuilder("SELECT \n");
        sb.append("  process_name, \n")
                .append(" output_time, \n")
                .append(" output_quantity, \n")
                .append(" bs.serial_number, \n")
                .append(" CASE qualified_flag WHEN 'N' THEN output_quantity ELSE NULL END AS unqualified_output_quantity, \n")
                .append(" date_trunc(").append(dateTrunc).append(", output_time) AS time_dimension \n")
                .append("FROM date_range, batch_serial bs \n")
                .append("LEFT JOIN ms_spcfcn fcn ON bs.material_code = fcn.spcfcn_cd \n")
                .append("LEFT JOIN ms_spcdct dct ON fcn.spcdct_cd = dct.spcdct_cd \n")
                .append("WHERE output_time BETWEEN date_range.min_time AND date_range.max_time \n")
                .append(" AND process_code = 'CP' \n");

        if (StringUtils.hasText(param.getProduceType())) {
            sb.append(" AND bs.rework_flag = :produceType \n");
        }
        // 添加工厂/车间/产线筛选条件
        if (CollectionUtils.isNotEmpty(param.getLineIds())) {
            sb.append(" AND bs.production_line_id in (");
            param.getLineIds().forEach(lineId -> sb.append("'").append(lineId).append("',"));
            sb.replace(sb.length() - 1, sb.length(), ")");

        } else if (CollectionUtils.isNotEmpty(param.getWorkshopIds())) {
            sb.append(" AND bs.workshop_id in (");
            param.getWorkshopIds().forEach(workshopId -> sb.append("'").append(workshopId).append("',"));
            sb.replace(sb.length() - 1, sb.length(), ")");
        } else if (CollectionUtils.isNotEmpty(param.getFactoryIds())) {
            //按工厂筛选,车间的上级是工厂,先根据工厂筛选出车间
            sb.append(" AND bs.workshop_id in (SELECT fc.id FROM mo_fctry_ara fc WHERE fc.pid in (");
            param.getFactoryIds().forEach(factoryId -> sb.append("'").append(factoryId).append("',"));
            sb.replace(sb.length() - 1, sb.length(), "))");
        }

        if (CollectionUtils.isNotEmpty(param.getSpcdctCodes())) {
            sb.append(" AND dct.spcdct_cd IN (");
            param.getSpcdctCodes().forEach(spcdctCode -> sb.append("'").append(spcdctCode).append("',"));
            sb.replace(sb.length() - 1, sb.length(), ") \n");
        }
        if (CollectionUtils.isNotEmpty(param.getMaterialCodes())) {
            sb.append(" AND fcn.spcdct_cd IN (");
            param.getMaterialCodes().forEach(materialCode -> sb.append("'").append(materialCode).append("',"));
            sb.replace(sb.length() - 1, sb.length(), ") \n");
        }

        // 处理检测项目和特性类型
        if (CollectionUtils.isNotEmpty(param.getFeatures()) ||
                CollectionUtils.isNotEmpty(param.getItemIds())) {

            sb.append("  AND bs.serial_number IN ( \n")
                    .append(" SELECT pt.actual_lot_serial_id \n")
                    .append(" FROM pi_inspection_task pt \n")
                    .append(" WHERE 1=1 \n");

            if (CollectionUtils.isNotEmpty(param.getItemIds())) {
                sb.append(" AND pt.id IN ( \n")
                        .append("  SELECT inspection_task_id \n")
                        .append("  FROM pi_inspection_task_detail \n")
                        .append("  WHERE item_id in (");
                param.getItemIds().forEach(itemId -> sb.append("'").append(itemId).append("',"));
                sb.replace(sb.length() - 1, sb.length(), ") \n");
            }

            if (CollectionUtils.isNotEmpty(param.getFeatures())) {
                sb.append("  AND pt.id IN ( \n")
                        .append("  SELECT ptd.inspection_task_id \n")
                        .append("  FROM pi_inspection_task_detail ptd \n")
                        .append("  LEFT JOIN bd_inspection_item bi ON ptd.item_id = bi.id \n")
                        .append("  WHERE bi.feature in (");
                param.getFeatures().forEach(feature -> sb.append("'").append(feature).append("',"));
                sb.replace(sb.length() - 1, sb.length(), ") \n");
            }
            sb.append(") \n");
            sb.append(") \n");
            sb.append(") \n");
        }
        return sb.toString();
    }

    private static String buildFinalSelect(StatisticsCommonParam param) {
        return "SELECT \n" +
                "    " + buildTimePeriodCase(param) + " AS 时间段, \n" +
                "    COALESCE(process_name, '所有工序') AS 工序名称, \n" +
                "    " + buildOutputQuantityCalc(param) + " AS 生产数量, \n" +
                "    COALESCE(SUM(qs.unqualified_output_quantity), 0) AS 不良数量, \n" +
                "    " + buildQualifiedRateCalc(param) + " AS 合格率, \n" +
                "    0.5 AS 目标 \n" +
                "FROM time_periods tp \n" +
                "LEFT JOIN quantity_stat qs ON tp.time_period = qs.time_dimension \n" +
                "GROUP BY tp.time_period, process_name \n" +
                "ORDER BY tp.time_period ASC";
    }

    private static String buildTimePeriodCase(StatisticsCommonParam param) {
        String dimension = param.getDimension();
        StringBuilder sb = new StringBuilder("CASE \n");

        if ("1".equals(dimension)) { // 年维度
            sb.append(" WHEN EXTRACT(YEAR FROM tp.time_period) IS NULL THEN '无数据' \n")
                    .append(" ELSE TO_CHAR(tp.time_period, 'YYYY\"年\"') \n");
        } else if ("2".equals(dimension)) { // 季维度
            sb.append("  WHEN EXTRACT(QUARTER FROM tp.time_period) IS NULL THEN '无数据' \n")
                    .append(" ELSE '第' || EXTRACT(QUARTER FROM tp.time_period) || '季度' \n");
        } else if ("3".equals(dimension)) { // 月维度
            sb.append("  WHEN EXTRACT(MONTH FROM tp.time_period) IS NULL THEN '无数据' \n")
                    .append("    ELSE TO_CHAR(tp.time_period, 'YYYY\"年\"') || EXTRACT(MONTH FROM tp.time_period) || '月' \n");
        } else if ("4".equals(dimension)) { // 周维度
            sb.append("  WHEN EXTRACT(WEEK FROM tp.time_period) IS NULL THEN '无数据' \n")
                    .append("  ELSE TO_CHAR(tp.time_period, 'YYYY\"年\"') || '第' || EXTRACT(WEEK FROM tp.time_period) || '周' \n");
        } else if ("5".equals(dimension)) { // 日维度
            sb.append(" WHEN tp.time_period IS NULL THEN '无数据' \n")
                    .append(" ELSE TO_CHAR(tp.time_period, 'MM\"月\"DD\"日\"') \n");
        } else { // 默认年维度
            sb.append("    WHEN EXTRACT(YEAR FROM tp.time_period) IS NULL THEN '无数据' \n")
                    .append(" ELSE TO_CHAR(tp.time_period, 'YYYY\"年\"') \n");
        }

        sb.append("END");
        return sb.toString();
    }

    private static String buildOutputQuantityCalc(StatisticsCommonParam param) {
        return "0".equals(param.getCalculateDimension())
                ? "COUNT(qs.serial_number)"
                : "COALESCE(SUM(qs.output_quantity), 0)";
    }

    private static String buildQualifiedRateCalc(StatisticsCommonParam param) {
        if ("1".equals(param.getCalculateDimension())) {
            return "CASE WHEN COALESCE(SUM(qs.output_quantity), 0) = 0 THEN 1 \n" +
                    "  ELSE 1 - COALESCE(SUM(qs.unqualified_output_quantity), 0) / SUM(qs.output_quantity) \n" +
                    "END";
        } else {
            return "CASE WHEN COUNT(*) = 0 THEN 1 \n" +
                    "  ELSE 1 - CAST(COUNT(qs.unqualified_output_quantity) AS numeric) / COUNT(*) \n" +
                    "END";
        }
    }
}