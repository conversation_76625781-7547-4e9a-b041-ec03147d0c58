package cec.jiutian.bc.inventoryInspection.remote.rpc;

import cec.jiutian.bc.inventoryInspection.domain.inspectionRequest.model.InventoryInspectionRequest;
import cec.jiutian.bc.inventoryInspection.domain.inspectionRequest.model.InventoryInspectionRequestDetail;
import cec.jiutian.bc.inventoryInspection.service.InventoryMaterialInspectService;
import cec.jiutian.bc.mto.OrgMTO;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import jakarta.annotation.Resource;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;

@Component
public class InventoryMaterialInspectRpc {

    @Resource
    private InventoryMaterialInspectService inventoryMaterialInspectService;

    @Resource
    private FabosJsonDao fabosJsonDao;

    /**
     * WMS发布检验通知 todo
     * 1.创建QMS检验通知
     * 2.创建检验任务
     *
     * @param jsonObject
     * @return
     */
    public String materialInspectByWMS(JSONObject jsonObject) throws ParseException {

        JSONArray detailList = jsonObject.getJSONArray("detailList");

        InventoryInspectionRequest request = new InventoryInspectionRequest();
        request.setMaterialCode(jsonObject.getString("materialCode"));
        request.setMaterialName(jsonObject.getString("materialName"));
        setDep(jsonObject, request);
        request.setType(jsonObject.getString("type"));
        request.setGeneralCode(jsonObject.getString("generalCode"));
        request.setSupplierName(jsonObject.getString("supplierName"));
        request.setMeasureUnit(jsonObject.getString("measureUnit"));
        request.setMaterialSpecification(jsonObject.getString("materialSpecification"));
        request.setMaterialSpecification(jsonObject.getString("materialSpecification"));
        request.setOriginLotId(jsonObject.getString("originLotId"));
        request.setInspector(jsonObject.getString("inspector"));
        request.setInspectorName(jsonObject.getString("inspectorName"));
        request.setArrivalQuantity(Double.parseDouble(jsonObject.getString("arrivalQuantity")));
        request.setOriginLotId(jsonObject.getString("originLotId"));


        // 处理详情
        List<InventoryInspectionRequestDetail> requestDetails = new ArrayList<>();
        for (int i = 0; i < detailList.size(); i++) {
            JSONObject detailJson = detailList.getJSONObject(i);
            InventoryInspectionRequestDetail requestDetail = new InventoryInspectionRequestDetail();
            requestDetail.setCOAFlag(detailJson.getString("COAFlag"));
            requestDetail.setOriginLotId(detailJson.getString("originLotId"));
            requestDetail.setLotSerialId(detailJson.getString("lotSerialId"));
            requestDetail.setMaterialCode(detailJson.getString("materialCode"));
            requestDetail.setMaterialName(detailJson.getString("materialName"));
            requestDetail.setMaterialSpecification(detailJson.getString("materialSpecification"));
            requestDetail.setMeasureUnit(detailJson.getString("measureUnit"));
            requestDetail.setSampleFlag(detailJson.getString("sampleFlag"));
            if (StringUtils.isNotBlank(detailJson.getString("stockInQuantity"))) {
                requestDetail.setLotQuantity(Double.parseDouble(detailJson.getString("stockInQuantity")));
            }
            requestDetail.setInventoryId(detailJson.getString("inventoryId"));

            requestDetails.add(requestDetail);
        }
        request.setDetails(requestDetails);
        InventoryInspectionRequest inventoryInspectionRequest = inventoryMaterialInspectService.createInspectionRequest(request);
        return inventoryInspectionRequest.getGeneralCode();

    }

    private static final String orgQuery = "from OrgMTO where name = '质量部' order by sort asc";

    private void setDep(JSONObject jsonObject, InventoryInspectionRequest request) {
        String materialLevel = jsonObject.getString("materialLevel");
        if (StringUtils.isBlank(materialLevel)) materialLevel = "A";
        request.setMaterialLevel(materialLevel);
        switch (materialLevel) {
            case "A":
            case "B":
                request.setInspectionDepartmentName("质量部");
                OrgMTO orgMTO = fabosJsonDao.getEntityManager().createQuery(orgQuery, OrgMTO.class).setMaxResults(1).getSingleResult();
                if (orgMTO == null) {
                    throw new ServiceException("未查询到质量部，请联系管理员");
                }
                request.setInspectionDepartment(orgMTO.getId());
                break;
            default:
                request.setInspectionDepartment(jsonObject.getString("inspectionDepartment"));
                request.setInspectionDepartmentName(jsonObject.getString("inspectionDepartmentName"));
                break;
        }

    }
}
