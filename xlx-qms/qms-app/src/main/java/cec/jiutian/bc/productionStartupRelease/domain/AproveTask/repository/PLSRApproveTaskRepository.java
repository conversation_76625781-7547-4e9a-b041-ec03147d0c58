package cec.jiutian.bc.productionStartupRelease.domain.AproveTask.repository;


import cec.jiutian.bc.productionStartupRelease.domain.AproveTask.modle.PLSRApproveTask;
import jakarta.persistence.LockModeType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PLSRApproveTaskRepository extends JpaRepository<PLSRApproveTask, String> {

    List<PLSRApproveTask> findByBusinessKeyOrderByCreateDate(String businessKey);


    @Query("select MAX (turn) from PLSRApproveTask where businessKey = :businessKey and code = :code")
    Integer findByBOrderByBusinessKeyAndCode(@Param("businessKey") String businessKey, @Param("code") String Code);


    @Lock(LockModeType.PESSIMISTIC_WRITE)
    List<PLSRApproveTask> findByBusinessKeyAndStatus(@Param("businessKey") String businessKey, @Param("status") String status);

    @Lock(LockModeType.PESSIMISTIC_WRITE)
    @Query("select t from PLSRApproveTask t where t.businessKey = :businessKey and t.status = :status and t.code = :code and t.turn = (select Max(p.turn) from PLSRApproveTask p where p.businessKey = :businessKey and p.status = :status and p.code = :code)")
    PLSRApproveTask findByBusinessKeyAndStatusAndCode(@Param("businessKey") String businessKey, @Param("status") String status, @Param("code") String code);

}
