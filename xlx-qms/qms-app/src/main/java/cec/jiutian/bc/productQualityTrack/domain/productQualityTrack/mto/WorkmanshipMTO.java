package cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.mto;

import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/07/18
 * @description TODO
 */
@Entity
@Table(name = "mf_prcs")
@Getter
@Setter
@FabosJson(
        name = "工艺",
        orderBy = "createTime desc",
        power = @Power(add = false, edit = false, delete = false, print = false, importable = false)
)
public class WorkmanshipMTO {
    @Id
    @FabosJsonField(
            edit = @Edit(title = "", show = false)
    )
    private String id;

    @FabosJsonField(
            views = @View(title = "工艺编码"),
            edit = @Edit(title = "工艺编码",
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "prcs_cd", length = 40)
    private String processCode;

    @FabosJsonField(
            views = @View(title = "工艺名称"),
            edit = @Edit(title = "工艺名称",
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "prcs_nm", length = 40)
    private String processName;

    @Column(name = "crte_tm")
    private Date createTime;
}
