package cec.jiutian.bc.questionPaperManagement.domain.questionPaper.model;

import cec.jiutian.bc.basicData.enumeration.NamingRuleCodeEnum;
import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleBaseModel;
import cec.jiutian.bc.mto.OrgMTO;
import cec.jiutian.bc.processInspect.domain.publicInspectionTask.mto.ProcessUserForInsTaskMTO;
import cec.jiutian.bc.questionPaperManagement.domain.questionPaper.handler.QuestionPaperCompleteOperationHandler;
import cec.jiutian.bc.questionPaperManagement.domain.questionPaper.handler.QuestionPaperReleaseOperationHandler;
import cec.jiutian.bc.questionPaperManagement.domain.questionPaper.proxy.QuestionPaperDataProxy;
import cec.jiutian.bc.questionPaperManagement.enums.QuestionPaperStateEnum;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.AttachmentType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DateType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowBaseOperation;
import cec.jiutian.view.type.RowOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OrderBy;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.util.List;

@Entity
@Table(name = "qpm_question_paper",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        })
@Getter
@Setter
@FabosJson(
        name = "问题一页纸",
        dataProxy = QuestionPaperDataProxy.class,
        power = @Power(export = false),
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "currentState !='Edit'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "currentState !='Edit'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
        },
        rowOperation = {
                @RowOperation(
                        title = "确认",
                        code = "QuestionPaper@RELEASE",
                        operationHandler = QuestionPaperReleaseOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "是否确定操作？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "QuestionPaper@RELEASE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "currentState != 'Edit'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "原因分析",
                        code = "QuestionPaper@ANALYSE",
                        mode = RowOperation.Mode.SINGLE,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.UPDATE,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = QuestionPaperAnalyse.class,
                        ifExpr = "currentState != 'WaitAnalyse'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "QuestionPaper@ANALYSE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
                @RowOperation(
                        title = "临时措施",
                        code = "QuestionPaper@INTERIM",
                        mode = RowOperation.Mode.SINGLE,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.UPDATE,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = QuestionPaperInterim.class,
//                        operationHandler = QuestionPaperInterimOperationHandler.class,
                        ifExpr = "currentState != 'Execute' || interimFlag",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "QuestionPaper@INTERIM"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
                @RowOperation(
                        title = "改善措施",
                        code = "QuestionPaper@IMPROVE",
                        mode = RowOperation.Mode.SINGLE,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.UPDATE,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = QuestionPaperImprove.class,
                        ifExpr = "currentState != 'Execute' || improveFlag",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "QuestionPaper@IMPROVE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
                @RowOperation(
                        title = "临时措施验证",
                        code = "QuestionPaper@INTERIMCHECK",
                        mode = RowOperation.Mode.SINGLE,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.UPDATE,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = QuestionPaperInterimCheck.class,
                        ifExpr = "currentState != 'Execute' || !interimFlag",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "QuestionPaper@INTERIMCHECK"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
                @RowOperation(
                        title = "改善措施验证",
                        code = "QuestionPaper@IMPROVECHECK",
                        mode = RowOperation.Mode.SINGLE,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.UPDATE,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = QuestionPaperImproveCheck.class,
                        ifExpr = "currentState != 'Execute' || !improveFlag",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "QuestionPaper@IMPROVECHECK"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
                @RowOperation(
                        title = "关闭",
                        code = "QuestionPaper@COMPLETE",
                        operationHandler = QuestionPaperCompleteOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "是否确定操作？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "QuestionPaper@COMPLETE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "currentState != 'Execute'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
        }
)
@TemplateType(type = "multiTable")
public class QuestionPaper extends NamingRuleBaseModel {

    @Override
    public String getNamingCode() {
        return NamingRuleCodeEnum.QuestionPaper.name();
    }

    @FabosJsonField(
            views = @View(title = "问题简述"),
            edit = @Edit(title = "问题简述", notNull = true, search = @Search(vague = true))
    )
    private String questionDescription;

    @FabosJsonField(
            views = @View(title = "问题等级"),
            edit = @Edit(title = "问题等级", notNull = true/*, search = @Search,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = InspectionResultEnum.class)*/)
    )
    private String level;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "发生日期", type = ViewType.DATE),
            edit = @Edit(title = "发生日期", notNull = true, dateType = @DateType(type = DateType.Type.DATE))
    )
    private LocalDate occurDate;

    @FabosJsonField(
            views = @View(title = "事件描述（5W2H）"),
            edit = @Edit(title = "事件描述（5W2H）", notNull = true,
                    type = EditType.TEXTAREA, inputType = @InputType(length = 400))
    )
    private String eventDescription;

    @Transient
    @FabosJsonField(
            views = @View(title = "责任部门", column = "name", show = false),
            edit = @Edit(title = "责任部门", notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false
            )
    )
    private OrgMTO responsibleDepartmentMTO;

    @FabosJsonField(
            views = @View(title = "责任部门id", show = false),
            edit = @Edit(title = "责任部门id", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "responsibleDepartmentMTO", beFilledBy = "id"))
    )
    private String responsibleDepartmentId;

    @FabosJsonField(
            views = @View(title = "责任部门"),
            edit = @Edit(title = "责任部门", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "responsibleDepartmentMTO", beFilledBy = "name"))
    )
    private String responsibleDepartmentName;

    @Transient
    @FabosJsonField(
            views = @View(title = "责任人", column = "name", show = false),
            edit = @Edit(title = "责任人", notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType()
            )
    )
    private ProcessUserForInsTaskMTO responsiblePersonMTO;

    @FabosJsonField(
            views = @View(title = "责任人ID", show = false),
            edit = @Edit(title = "责任人ID", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "responsiblePersonMTO", beFilledBy = "id"))
    )
    private String responsiblePersonId;

    @FabosJsonField(
            views = @View(title = "责任人"),
            edit = @Edit(title = "责任人", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "responsiblePersonMTO", beFilledBy = "name"))
    )
    private String responsiblePersonName;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "预计纳期", type = ViewType.DATE),
            edit = @Edit(title = "预计纳期", notNull = true, dateType = @DateType(type = DateType.Type.DATE))
    )
    private LocalDate expectedDeliveryDate;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态", show = false, search = @Search,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = QuestionPaperStateEnum.class))
    )
    private String currentState;

    @FabosJsonField(
            views = @View(title = "原因分析（5why）"),
            edit = @Edit(title = "原因分析（5why）", show = false)
    )
    private String causeAnalysis;

    @FabosJsonField(
            views = @View(title = "附件"),
            edit = @Edit(title = "附件", show = false, type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(maxLimit = 10))
    )
    private String attachment;

    @FabosJsonField(
            views = @View(title = "临时措施标记", show = false),
            edit = @Edit(title = "临时措施标记", show = false)
    )
    private Boolean interimFlag;

    @FabosJsonField(
            views = @View(title = "改善措施标记", show = false),
            edit = @Edit(title = "改善措施标记", show = false)
    )
    private Boolean improveFlag;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "question_paper_id")
    @OrderBy
    @FabosJsonField(
            edit = @Edit(title = "临时措施", type = EditType.TAB_REFERENCE_GENERATE),
            views = @View(title = "临时措施", type = ViewType.TABLE_VIEW)
    )
    private List<QuestionPaperInterimMeasure> interimMeasures;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "question_paper_id")
    @OrderBy
    @FabosJsonField(
            edit = @Edit(title = "改善措施", type = EditType.TAB_REFERENCE_GENERATE),
            views = @View(title = "改善措施", type = ViewType.TABLE_VIEW)
    )
    private List<QuestionPaperImproveMeasure> improveMeasures;

}
