package cec.jiutian.bc.accidentReportManagement.remote.provider;

import cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.enumration.ArmBusinessStatusEnum;
import cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.model.AccidentReportTask;
import cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.model.ArmPreventMeasure;
import cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.model.ArmTemporaryMeasure;
import cec.jiutian.bc.accidentReportManagement.enumeration.ArmImprovingProgressEnum;
import cec.jiutian.bc.accidentReportManagement.enumeration.ArmMeasureStatusEnum;
import cec.jiutian.bc.ecs.dto.SendMsgGroupDTO;
import cec.jiutian.bc.ecs.provider.EcsMessageProvider;
import cec.jiutian.bc.job.provider.IJobProvider;
import cec.jiutian.core.frame.annotation.FabosCustomizedService;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.meta.FabosJob;
import jakarta.annotation.Resource;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/30
 * @description 事故报告临期提醒定时任务
 */
@FabosCustomizedService(value = AccidentReportTask.class)
@Slf4j
@Component
@Transactional
public class AccidentReportScheduleProvider implements IJobProvider {

    private String groupCode = "AccidentReportTaskInfo";
    private String analyzeMessage = "事故报告任务分析超期，请尽快在处理，编号：";

    private String temMeasureMessage = "事故报告任务临时措施执行超期，请尽快在处理，编号：";

    private String preMeasureMessage = "事故报告任务预防措施执行超期，请尽快在处理，编号：";

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private EcsMessageProvider ecsMessageProvider;

    @FabosJob(comment = "临期提醒")
    @Override
    public String exec(String code, String param) {
        log.info("Timed task initiation for accident reporting task.");
        //待分析
        AccidentReportTask condition = new AccidentReportTask();
        condition.setBusinessStatus(ArmBusinessStatusEnum.Enum.TO_BE_ANALYZED.name());
        List<AccidentReportTask> modelList = fabosJsonDao.select(condition);
        analyzeOverdueReminder(modelList);

        //临时措施-已提交状态
        condition = new AccidentReportTask();
        condition.setTemporaryMeasureStatus(ArmMeasureStatusEnum.Enum.SUBMITTED.name());
        modelList = fabosJsonDao.select(condition);
        temporaryMeasureOverdueReminder(modelList);

        //预防措施-已提交状态
        condition = new AccidentReportTask();
        condition.setPreventMeasureStatus(ArmMeasureStatusEnum.Enum.SUBMITTED.name());
        modelList = fabosJsonDao.select(condition);
        preventMeasureOverdueReminder(modelList);
        return null;
    }

    //分析超期提醒
    private void analyzeOverdueReminder(List<AccidentReportTask> modelList) {
        if (modelList == null) {
            return;
        }
        log.info("In the state of pending analysis of AccidentReportTask list size is :" + modelList.size());

        for (AccidentReportTask model : modelList) {
            if (isOverdue(model.getExpectedDeliveryDate().getTime())) {
                sendQms(groupCode, analyzeMessage + model.getGeneralCode());
            }
        }
    }

    //临时措施超期提醒
    private void temporaryMeasureOverdueReminder(List<AccidentReportTask> modelList) {
        if (modelList == null) {
            return;
        }
        log.info("The number of temporary measures submitted is:" + modelList.size());
        for (AccidentReportTask model : modelList) {
            boolean flag = false;
            List<ArmTemporaryMeasure> measureList = model.getArmTemporaryMeasureList();
            for (ArmTemporaryMeasure measure : measureList) {
                if (measure.getImprovingProgress().equals(ArmImprovingProgressEnum.Enum.PENDING_EXECUTION.name()) && isOverdue(measure.getDeliveryTime().getTime())) {
                    flag = true;
                    break;
                }
            }
            if (flag) {
                sendQms(groupCode, temMeasureMessage + model.getGeneralCode());
            }
        }
    }

    //预防措施超期提醒
    private void preventMeasureOverdueReminder(List<AccidentReportTask> modelList) {
        if (modelList == null) {
            return;
        }
        log.info("The number of preventive measures submitted is:" + modelList.size());
        for (AccidentReportTask model : modelList) {
            boolean flag = false;
            List<ArmPreventMeasure> measureList = model.getArmPreventMeasureList();
            for (ArmPreventMeasure measure : measureList) {
                if (measure.getImprovingProgress().equals(ArmImprovingProgressEnum.Enum.PENDING_EXECUTION.name()) && isOverdue(measure.getDeliveryTime().getTime())) {
                    flag = true;
                    break;
                }
            }
            if (flag) {
                sendQms(groupCode, preMeasureMessage + model.getGeneralCode());
            }
        }
    }

    private boolean isOverdue(long deliveryTime) {
        long nowTime = (new Date()).getTime();

        if (nowTime >= deliveryTime) {
            return true;
        }
        return false;
    }


    private void sendQms(String groupCode, String message) {
        SendMsgGroupDTO sendMsgGroupDTO = new SendMsgGroupDTO();
        sendMsgGroupDTO.setMessageGroupCode(groupCode);
        sendMsgGroupDTO.setContent(message);
        ecsMessageProvider.sendGroupMessage(sendMsgGroupDTO);
        log.info(message);
    }
}
