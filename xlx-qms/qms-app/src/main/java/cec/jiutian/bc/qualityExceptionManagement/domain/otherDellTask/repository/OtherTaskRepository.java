package cec.jiutian.bc.qualityExceptionManagement.domain.otherDellTask.repository;

import cec.jiutian.bc.qualityExceptionManagement.domain.otherDellTask.model.OtherDellTask;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface OtherTaskRepository extends JpaRepository<OtherDellTask, String> {


    List<OtherDellTask> findByCreationTimeBetween(LocalDateTime startTime, LocalDateTime endTime);

}
