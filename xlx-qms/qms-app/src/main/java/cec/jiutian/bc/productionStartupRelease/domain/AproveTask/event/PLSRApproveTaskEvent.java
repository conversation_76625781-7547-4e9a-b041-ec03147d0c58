package cec.jiutian.bc.productionStartupRelease.domain.AproveTask.event;

import cec.jiutian.bc.changeRequestManagement.enums.ApproveResultEnum;
import cec.jiutian.bc.changeRequestManagement.enums.ApproveTaskStatusEnum;
import cec.jiutian.bc.productionStartupRelease.domain.AproveTask.config.ApproveNodeConfig;
import cec.jiutian.bc.productionStartupRelease.domain.AproveTask.modle.PLSRApproveTask;
import cec.jiutian.bc.productionStartupRelease.domain.AproveTask.repository.PLSRApproveTaskRepository;
import cec.jiutian.bc.productionStartupRelease.enums.ApproveNodeEnum;
import cec.jiutian.bc.productionStartupRelease.enums.StatusEnum;
import cec.jiutian.bc.productionStartupRelease.domain.productionStartupRelease.model.PLSRReport;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Component
public class PLSRApproveTaskEvent {

    @Resource
    private PLSRApproveTaskRepository plsrApproveTaskRepository;

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Transactional
    public void onEvent(PLSRApproveTask approveTask) {
        switch (approveTask.getResult()) {
            case "PASS" -> {
                onPassEvent(approveTask);
                break;
            }
            case "REJECT" -> {
                onRejectEvent(approveTask);
                break;
            }
            default -> {
                throw new ServiceException("无效的操作");
            }
        }
    }

    private void onPassEvent(PLSRApproveTask approveTask) {

        ApproveNodeEnum.Node node = ApproveNodeEnum.getNode(approveTask.getCode());
        switch (node) {
            case WORKSHOP_PROCESS: {
                onLevel1Pass(approveTask, ApproveNodeEnum.Node.PROCESS_TECHNOLOGY);
                break;
            }
            case WORKSHOP_PRODUCTION: {
                onLevel1Pass(approveTask, ApproveNodeEnum.Node.CATHODE_MATERIAL_PRODUCTION);
                break;
            }

            case WORKSHOP_EQUIPMENT: {
                onLevel1Pass(approveTask, ApproveNodeEnum.Node.EQUIPMENT_POWER);
                break;
            }
            case WORKSHOP_QUALITY: {
                onLevel1Pass(approveTask, ApproveNodeEnum.Node.QUALITY_MANAGEMENT);
                break;
            }

            case PROCESS_TECHNOLOGY:
            case CATHODE_MATERIAL_PRODUCTION:
            case EQUIPMENT_POWER:
            case QUALITY_MANAGEMENT: {
                onLevel2Pass(approveTask);
                break;
            }
            default: {
                throw new ServiceException("无效的操作");
            }
        }

    }

    private void onLevel2Pass(PLSRApproveTask approveTask) {
        boolean isPass = true;
        if (!ApproveNodeEnum.Node.PROCESS_TECHNOLOGY.name().equals(approveTask.getCode())) {
            isPass = isPass && judgePass(approveTask.getBusinessKey(), ApproveNodeEnum.Node.PROCESS_TECHNOLOGY);
        }
        if (!ApproveNodeEnum.Node.CATHODE_MATERIAL_PRODUCTION.name().equals(approveTask.getCode())) {
            isPass = isPass && judgePass(approveTask.getBusinessKey(), ApproveNodeEnum.Node.CATHODE_MATERIAL_PRODUCTION);
        }
        if (!ApproveNodeEnum.Node.EQUIPMENT_POWER.name().equals(approveTask.getCode())) {
            isPass = isPass && judgePass(approveTask.getBusinessKey(), ApproveNodeEnum.Node.EQUIPMENT_POWER);
        }
        if (!ApproveNodeEnum.Node.QUALITY_MANAGEMENT.name().equals(approveTask.getCode())) {
            isPass = isPass && judgePass(approveTask.getBusinessKey(), ApproveNodeEnum.Node.QUALITY_MANAGEMENT);
        }
        PLSRReport plsrReport = fabosJsonDao.findById(PLSRReport.class, approveTask.getBusinessKey());
        if (isPass) {
            plsrReport.setStatus(StatusEnum.Enum.PASS.name());
        } else {
            plsrReport.setStatus(StatusEnum.Enum.NOT_PASS.name());
        }
        fabosJsonDao.mergeAndFlush(plsrReport);
    }

    private boolean judgePass(String businessKey, ApproveNodeEnum.Node node) {
        PLSRApproveTask task = plsrApproveTaskRepository.findByBusinessKeyAndStatusAndCode(businessKey, ApproveTaskStatusEnum.Enum.COMPLETE.name(), node.name());
        if (task != null && task.getResult() != null
                && ApproveResultEnum.Enum.PASS.name().equals(task.getResult())) {
            return true;
        }
        return false;
    }

    private void onLevel1Pass(PLSRApproveTask approveTask, ApproveNodeEnum.Node nextNode) {
        List<String> assigneeIds = ApproveNodeConfig.getAssigneeIds(nextNode.name());
        if (CollectionUtils.isEmpty(assigneeIds)) {
            throw new ServiceException("Nacos 未配置审批角色，请联系系统管理员。");
        }

        Integer turn = plsrApproveTaskRepository.findByBOrderByBusinessKeyAndCode(approveTask.getBusinessKey(), nextNode.name());
        if (turn == null) {
            turn = -1;
        }
        PLSRApproveTask task = PLSRApproveTask.createRoleTask(approveTask.getBusinessKey(), assigneeIds.get(0), nextNode, turn + 1);
        plsrApproveTaskRepository.saveAndFlush(task);
    }

    private void onRejectEvent(PLSRApproveTask approveTask) {
        ApproveNodeEnum.Node node = ApproveNodeEnum.getNode(approveTask.getCode());
        switch (node) {
            case WORKSHOP_PROCESS:
            case PROCESS_TECHNOLOGY:
            case WORKSHOP_PRODUCTION:
            case CATHODE_MATERIAL_PRODUCTION:
            case WORKSHOP_EQUIPMENT:
            case EQUIPMENT_POWER:
            case WORKSHOP_QUALITY:
            case QUALITY_MANAGEMENT:
                doReject(approveTask);
                break;
            default:
                throw new ServiceException("无效的操作");
        }
    }

    private void doReject(PLSRApproveTask approveTask) {
        List<PLSRApproveTask> undoTasks = plsrApproveTaskRepository.findByBusinessKeyAndStatus(approveTask.getBusinessKey(), ApproveTaskStatusEnum.Enum.WAIT_APPROVE.name());
        for (PLSRApproveTask task : undoTasks) {
            task.setStatus(ApproveTaskStatusEnum.Enum.SKIPPED.name());
            String userName = UserContext.getUserName();
            task.setExplain(userName + " 审批不通过。");
            plsrApproveTaskRepository.saveAndFlush(task);
        }
        PLSRReport plsrReport = fabosJsonDao.findById(PLSRReport.class, approveTask.getBusinessKey());
        plsrReport.setStatus(StatusEnum.Enum.NOT_PASS.name());
        fabosJsonDao.mergeAndFlush(plsrReport);
    }

}
