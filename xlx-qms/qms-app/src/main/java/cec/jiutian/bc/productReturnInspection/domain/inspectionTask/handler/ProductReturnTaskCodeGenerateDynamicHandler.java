package cec.jiutian.bc.productReturnInspection.domain.inspectionTask.handler;

import cec.jiutian.bc.basicData.enumeration.NamingRuleCodeEnum;
import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.productReturnInspection.domain.inspectionTask.model.ProductReturnInspectionTask;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/2/27
 * @description 编号生成handler
 */
@Component
public class ProductReturnTaskCodeGenerateDynamicHandler implements DependFiled.DynamicHandler<ProductReturnInspectionTask> {

    @Resource
    private NamingRuleService namingRuleService;

    @Override
    public Map<String, Object> handle(ProductReturnInspectionTask productReturnInspectionTask) {
        Map<String, Object> map = new HashMap<>();
        map.put("generalCode",String.valueOf(namingRuleService.getNameCode(NamingRuleCodeEnum.InspectionTask.name(), 1, null).get(0)));
        return map;
    }
}
