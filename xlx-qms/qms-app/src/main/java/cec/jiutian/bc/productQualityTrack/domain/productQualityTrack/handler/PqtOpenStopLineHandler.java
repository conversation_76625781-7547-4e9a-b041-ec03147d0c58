package cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.handler;

import cec.jiutian.bc.mto.FactoryArea;
import cec.jiutian.bc.mto.SpecificationManageMTO;
import cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.mto.MachineStartupWireChangeMTO;
import cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.mto.PqtProcessOperationCreateMTO;
import cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.mto.ProductQualityTrackCreateMTO;
import cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.service.ProductQualityTrackDealService;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class PqtOpenStopLineHandler implements DependFiled.DynamicHandler<ProductQualityTrackCreateMTO> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private ProductQualityTrackDealService dealService;

    @Override
    public Map<String, Object> handle(ProductQualityTrackCreateMTO model) {
        HashMap<String, Object> resultMap = new HashMap<>();
        List<PqtProcessOperationCreateMTO> pqtProcessOperationList = new ArrayList<>();
        resultMap.put("pqtProcessOperationList", pqtProcessOperationList);

        if (model != null && model.getMachineStartupWireChangeMTO() != null) {
            MachineStartupWireChangeMTO machineStartupWireChangeMTO = model.getMachineStartupWireChangeMTO();
            resultMap.put("mwGid", machineStartupWireChangeMTO.getId());
            resultMap.put("mwName", machineStartupWireChangeMTO.getName());

            if (machineStartupWireChangeMTO.getWorkshopId() != null) {
                FactoryArea faCondition = new FactoryArea();
                faCondition.setId(Long.parseLong(machineStartupWireChangeMTO.getWorkshopId()));
                FactoryArea result = fabosJsonDao.selectOne(faCondition);
                if (result != null) {
                    resultMap.put("factoryArea", result);
                    resultMap.put("factoryArea_factoryAreaName", result.getFactoryAreaName());
                }
            }
            resultMap.put("factoryAreaId", machineStartupWireChangeMTO.getWorkshopId());
            resultMap.put("factoryAreaName", machineStartupWireChangeMTO.getWorkshopName());

            if (machineStartupWireChangeMTO.getProductionLineId() != null) {
                FactoryArea faCondition = new FactoryArea();
                faCondition.setId(Long.parseLong(machineStartupWireChangeMTO.getProductionLineId()));
                FactoryArea result = fabosJsonDao.selectOne(faCondition);
                if (result != null) {
                    resultMap.put("factoryLine", result);
                    resultMap.put("factoryLine_factoryAreaName", result.getFactoryAreaName());

                    //填充list
                    ProductQualityTrackCreateMTO tempMTO = new ProductQualityTrackCreateMTO();
                    tempMTO.setFactoryArea((FactoryArea) resultMap.get("factoryArea"));
                    tempMTO.setFactoryLine(result);
                    dealService.disposeProcessOperationList(tempMTO, pqtProcessOperationList);
                }
            }
            resultMap.put("factoryLineId", machineStartupWireChangeMTO.getProductionLineId());
            resultMap.put("factoryLineName", machineStartupWireChangeMTO.getProductionLineName());

            if (machineStartupWireChangeMTO.getMaterialCode() != null) {
                SpecificationManageMTO condition = new SpecificationManageMTO();
                condition.setCode(machineStartupWireChangeMTO.getMaterialCode());
                SpecificationManageMTO result = fabosJsonDao.selectOne(condition);
                if (result != null) {
                    resultMap.put("material", result);
                    resultMap.put("material_name", result.getName());
                    resultMap.put("materialId", result.getId());
                }
            }
            resultMap.put("materialCode", machineStartupWireChangeMTO.getMaterialCode());
            resultMap.put("materialName", machineStartupWireChangeMTO.getMaterialName());
            resultMap.put("materialType", machineStartupWireChangeMTO.getMaterialType());

        } else {
            resultMap.put("mwGid", null);
            resultMap.put("mwName", null);
            resultMap.put("factoryArea", null);
            resultMap.put("factoryAreaId", null);
            resultMap.put("factoryAreaName", null);
            resultMap.put("factoryLine", null);
            resultMap.put("factoryLineId", null);
            resultMap.put("factoryLineName", null);
            resultMap.put("material", null);
            resultMap.put("materialCode", null);
            resultMap.put("materialName", null);
            resultMap.put("materialType", null);
        }
        return resultMap;
    }
}
