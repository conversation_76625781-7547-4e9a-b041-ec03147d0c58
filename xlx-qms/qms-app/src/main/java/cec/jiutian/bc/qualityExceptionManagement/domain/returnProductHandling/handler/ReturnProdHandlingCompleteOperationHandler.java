package cec.jiutian.bc.qualityExceptionManagement.domain.returnProductHandling.handler;

import cec.jiutian.bc.qualityExceptionManagement.domain.returnProductHandling.model.ReturnProductHandling;
import cec.jiutian.bc.qualityExceptionManagement.enums.ReturnProductHandlingStatusEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class ReturnProdHandlingCompleteOperationHandler implements OperationHandler<ReturnProductHandling, Void> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<ReturnProductHandling> data, Void modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            ReturnProductHandling entity = data.get(0);
            entity.setStatus(ReturnProductHandlingStatusEnum.Enum.Complete.name());
            fabosJsonDao.mergeAndFlush(entity);
        }
        return "msg.success('操作成功')";
    }
}
