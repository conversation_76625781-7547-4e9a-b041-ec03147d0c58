package cec.jiutian.bc.clientReviewManagement.domain.customerReviewTask.model;

import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.AttachmentType;
import cec.jiutian.view.field.edit.DateType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

@FabosJson(
        name = "客户评审任务详情"
)
@Table(name = "qms_client_review_task_detail")
@Entity
@Getter
@Setter
public class CustomerReviewTaskDetail extends MetaModel {

    @ManyToOne
    @FabosJsonField(
            views = {
                    @View(title = "客户评审任务", column = "generalCode", show = false)
            },
            edit = @Edit(title = "客户评审任务", type = EditType.REFERENCE_TABLE, show = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode")
            )
    )
    @JsonIgnoreProperties("details")
    private CustomerReviewTask customerReviewTask;

    @FabosJsonField(
            views = @View(title = "部门id", show = false),
            edit = @Edit(title = "部门id", show = false)
    )
    private String departmentId;

    @FabosJsonField(
            views = @View(title = "部门"),
            edit = @Edit(title = "部门")
    )
    private String departmentName;

    @FabosJsonField(
            views = @View(title = "待准备资料"),
            edit = @Edit(title = "待准备资料",notNull = true)
    )
    private String informationRequirement;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "资料上传纳期", type = ViewType.DATE),
            edit = @Edit(title = "资料上传纳期",notNull = true, dateType = @DateType(type = DateType.Type.DATE))
    )
    private LocalDate deliveryTime;

    @FabosJsonField(
            views = @View(title = "资料"),
            edit = @Edit(title = "资料",
                    readonly = @Readonly,
                    type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(maxLimit = 10))
    )
    private String attachment;

}
