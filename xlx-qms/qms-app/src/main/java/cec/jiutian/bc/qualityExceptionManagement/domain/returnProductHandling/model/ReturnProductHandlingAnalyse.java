package cec.jiutian.bc.qualityExceptionManagement.domain.returnProductHandling.model;

import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleBaseModel;
import cec.jiutian.bc.generalModeler.domain.supplier.model.Supplier;
import cec.jiutian.bc.generalModeler.enumeration.YesOrNoEnum;
import cec.jiutian.bc.mto.OrgMTO;
import cec.jiutian.bc.processInspect.domain.publicInspectionTask.mto.ProcessUserForInsTaskMTO;
import cec.jiutian.bc.qualityExceptionManagement.domain.returnProductHandling.proxy.ReturnProductHandlingAnalyseDataProxy;
import cec.jiutian.bc.qualityExceptionManagement.enums.ReturnProductAnalysisMethodEnum;
import cec.jiutian.bc.qualityExceptionManagement.enums.ReturnProductHandlingStatusEnum;
import cec.jiutian.bc.qualityExceptionManagement.enums.ReturnProductLiabilityJudgmentEnum;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.AttachmentType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DateType;
import cec.jiutian.view.field.edit.DependFieldDisplay;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

@Entity
@Table(name = "qem_return_product_handling",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        })
@Getter
@Setter
@FabosJson(
        name = "退货产品处理单-原因分析",
        dataProxy = ReturnProductHandlingAnalyseDataProxy.class
)
public class ReturnProductHandlingAnalyse extends NamingRuleBaseModel {

    @FabosJsonField(
            views = @View(title = "原因分析"),
            edit = @Edit(title = "原因分析", notNull = true,
                    type = EditType.TEXTAREA, inputType = @InputType(length = 400))
    )
    private String causeAnalysis;

    @FabosJsonField(
            views = @View(title = "责任判定"),
            edit = @Edit(title = "责任判定", notNull = true, type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ReturnProductLiabilityJudgmentEnum.class))
    )
    private String liabilityJudgment;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "供应商", column = "supplierName"),
            edit = @Edit(title = "供应商", notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "supplierName"),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "liabilityJudgment == 'Supplier'")
            )
    )
    private Supplier supplier;

    @FabosJsonField(
            views = @View(title = "分析方法"),
            edit = @Edit(title = "分析方法", notNull = true, type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ReturnProductAnalysisMethodEnum.class))
    )
    private String analysisMethod;

    @Transient
    @FabosJsonField(
            views = @View(title = "问题解决责任部门", column = "name", show = false),
            edit = @Edit(title = "问题解决责任部门",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false
            )
    )
    private OrgMTO responsibleDepartmentMTO;

    @FabosJsonField(
            views = @View(title = "问题解决责任部门id", show = false),
            edit = @Edit(title = "问题解决责任部门id", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "responsibleDepartmentMTO", beFilledBy = "id"))
    )
    private String responsibleDepartmentId;

    @FabosJsonField(
            views = @View(title = "问题解决责任部门"),
            edit = @Edit(title = "问题解决责任部门",notNull = true, show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "responsibleDepartmentMTO", beFilledBy = "name"))
    )
    private String responsibleDepartmentName;

    @FabosJsonField(
            views = @View(title = "是否需要处置"),
            edit = @Edit(title = "是否需要处置", notNull = true, type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = YesOrNoEnum.class))
    )
    private String handleFlag;

    @Transient
    @FabosJsonField(
            views = @View(title = "方案输出部门", column = "name", show = false),
            edit = @Edit(title = "方案输出部门",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false
            )
    )
    private OrgMTO suggestionDepartmentMTO;

    @FabosJsonField(
            views = @View(title = "方案输出部门id", show = false),
            edit = @Edit(title = "方案输出部门id", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "suggestionDepartmentMTO", beFilledBy = "id"))
    )
    private String suggestionDepartmentId;

    @FabosJsonField(
            views = @View(title = "方案输出部门"),
            edit = @Edit(title = "方案输出部门",notNull = true, show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "suggestionDepartmentMTO", beFilledBy = "name"))
    )
    private String suggestionDepartmentName;

    @Transient
    @FabosJsonField(
            views = @View(title = "分析人", column = "name"),
            edit = @Edit(title = "分析人", notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType()
            )
    )
    private ProcessUserForInsTaskMTO analyst;

    @FabosJsonField(
            views = @View(title = "分析人ID"),
            edit = @Edit(title = "分析人ID", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "analyst", beFilledBy = "id"))
    )
    private String analystId;

    @FabosJsonField(
            views = @View(title = "分析人姓名"),
            edit = @Edit(title = "分析人姓名", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "analyst", beFilledBy = "name"))
    )
    private String analystName;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "分析时间", type = ViewType.DATE),
            edit = @Edit(title = "分析时间", notNull = true, dateType = @DateType(type = DateType.Type.DATE))
    )
    private LocalDate productionDate;

    @FabosJsonField(
            views = @View(title = "附件"),
            edit = @Edit(title = "附件", type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(maxLimit = 10))
    )
    private String attachment;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态", show = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ReturnProductHandlingStatusEnum.class))
    )
    private String status;

}
