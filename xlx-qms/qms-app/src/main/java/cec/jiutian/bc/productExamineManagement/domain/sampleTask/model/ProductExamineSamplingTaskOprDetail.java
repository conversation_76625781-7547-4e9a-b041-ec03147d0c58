package cec.jiutian.bc.productExamineManagement.domain.sampleTask.model;

import cec.jiutian.bc.modeler.domain.inspectionMethod.model.InspectionMethod;
import cec.jiutian.bc.modeler.domain.samplingPlan.model.SamplingPlan;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/3/19
 * @description TODO
 */
@FabosJson(
        name = "取样任务自定义按钮模型详情",
        power = @Power(add = false,edit = false,delete = false)
)
@Table(name = "qms_pem_product_examine_sampling_task_detail")
@Entity
@Data
public class ProductExamineSamplingTaskOprDetail extends MetaModel {

    @ManyToOne
    @FabosJsonField(
            views = {
                    @View(title = "取样任务编号", column = "generalCode", show = false)
            },
            edit = @Edit(title = "取样任务", type = EditType.REFERENCE_TABLE, show = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode")
            )
    )
    @JsonIgnoreProperties("detailList")
    private ProductExamineSamplingTaskOpr samplingTaskOpr;

    @FabosJsonField(
            views = @View(title = "检验项目id",show = false),
            edit = @Edit(title = "检验项目id",show = false)
    )
    private String itemId;

    @FabosJsonField(
            views = @View(title = "检验项目编码"),
            edit = @Edit(title = "检验项目编码", search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    private String code;

    @FabosJsonField(
            views = @View(title = "检验项目名称"),
            edit = @Edit(title = "检验项目名称", search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    private String name;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "抽样方案", column = "name", type = ViewType.TABLE_FORM),
            edit = @Edit(title = "抽样方案",
                    type = EditType.REFERENCE_TABLE,
                    readonly = @Readonly,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    search = @Search()
            )
    )
    private SamplingPlan samplingPlan;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "检验方法", column = "name", type = ViewType.TABLE_FORM),
            edit = @Edit(title = "检验方法",
                    type = EditType.REFERENCE_TABLE,
                    readonly = @Readonly,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    search = @Search()
            )
    )
    private InspectionMethod inspectionMethod;

    @FabosJsonField(
            views = @View(title = "样本量"),
            edit = @Edit(title = "样本量",show = false,
                    numberType = @NumberType(min=0,max = 9999999,precision = 2))
    )
    private Double sampleSize;
}
