package cec.jiutian.bc.clientReviewManagement.domain.problemImprovement.handler;

import cec.jiutian.bc.basicData.enumeration.NamingRuleCodeEnum;
import cec.jiutian.bc.clientReviewManagement.domain.problemImprovement.model.ReviewProblemImprovement;
import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/06/11
 * @description
 */
@Component
public class ReviewProblemImprovementDynamicHandler implements DependFiled.DynamicHandler<ReviewProblemImprovement> {

    @Resource
    private NamingRuleService namingRuleService;

    @Override
    public Map<String, Object> handle(ReviewProblemImprovement reviewProblemImprovement) {
        Map<String, Object> map = new HashMap<>();
        List<String> result = namingRuleService.getNameCode(NamingRuleCodeEnum.REVIEW_PROBLEM_IMPROVEMENT.name(), 1, null);
        map.put("generalCode", result.get(0));
        return map;
    }
}
