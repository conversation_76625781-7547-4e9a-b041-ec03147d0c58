package cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.mto;

import cec.jiutian.bc.materialInspect.domain.publicInspectionTask.mto.UserForInsTaskMTO;
import cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.enumration.AuditStatusEnum;
import cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.enumration.TaskBusinessStateEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.Entity;
import jakarta.persistence.Transient;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/4/27
 * @description TODO
 */
@FabosJson(
        name = "验证"
)
@Entity
@Getter
@Setter
public class AbnormalFeedbackHandlingSchemeMTO extends MetaModel {

    @FabosJsonField(
            views = @View(title = "效果验证"),
            edit = @Edit(title = "效果验证",
                    type = EditType.CHOICE,
                    notNull = true,
                    search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = AuditStatusEnum.class))
    )
    private String effectVerification;

    @Transient
    @FabosJsonField(
            views = @View(title = "选择验证人", show = false, column = "name"),
            edit = @Edit(title = "选择验证人",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType()
            )
    )
    private UserForInsTaskMTO verifier;

    @FabosJsonField(
            views = @View(title = "验证人ID", show = false),
            edit = @Edit(title = "验证人ID",
                    readonly = @Readonly(),
                    show = false,
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "verifier", beFilledBy = "id"))
    )
    private String verifierId;

    @FabosJsonField(
            views = @View(title = "验证人"),
            edit = @Edit(title = "验证人",
                    readonly = @Readonly(),
                    notNull = true,
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "verifier", beFilledBy = "name"))
    )
    private String verifierName;

    @FabosJsonField(
            views = @View(title = "验证时间"),
            edit = @Edit(title = "验证时间",
                    dateType = @DateType(type = DateType.Type.DATE_TIME),
                    notNull = true
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date verifyTime;

    @FabosJsonField(
            views = @View(title = "效果验证附件"),
            edit = @Edit(title = "效果验证附件", type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType)
    )
    private String effectVeriAttachments;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",
                    show = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = TaskBusinessStateEnum.class)
            )
    )
    private String businessState;
}
