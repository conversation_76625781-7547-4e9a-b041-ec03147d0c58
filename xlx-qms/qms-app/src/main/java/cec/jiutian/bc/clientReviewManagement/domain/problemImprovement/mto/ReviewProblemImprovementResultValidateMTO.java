package cec.jiutian.bc.clientReviewManagement.domain.problemImprovement.mto;

import cec.jiutian.bc.clientComplaintManagement.enumeration.ReviewProblemImproveStatusEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.AttachmentType;
import cec.jiutian.view.field.edit.ChoiceType;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025/06/11
 * @description
 */
@FabosJson(
        name = "原因分析"

)
@Table(name = "qms_client_review_problem_improvement")
@Entity
@Getter
@Setter
public class ReviewProblemImprovementResultValidateMTO extends MetaModel {
    @FabosJsonField(
            views = @View(title = "验收情况"),
            edit = @Edit(title = "验收情况")
    )
    private String acceptanceDescription;

    @FabosJsonField(
            views = @View(title = "佐证材料"),
            edit = @Edit(title = "佐证材料",type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(size = 5120, maxLimit = 10, type = AttachmentType.Type.BASE))
    )
    private String supportProof;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",
                    show = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ReviewProblemImproveStatusEnum.class)
            )
    )
    private String businessStatus;
}
