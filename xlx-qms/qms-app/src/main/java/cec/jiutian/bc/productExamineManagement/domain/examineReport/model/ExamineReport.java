package cec.jiutian.bc.productExamineManagement.domain.examineReport.model;

import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.model.CorrectPreventMeasure;
import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleModel;
import cec.jiutian.bc.modeler.enumration.NamingRuleCodeEnum;
import cec.jiutian.bc.productExamineManagement.domain.examineReport.proxy.ExamineReportFlowProxy;
import cec.jiutian.bc.productExamineManagement.enumration.ImplementPlanHandleResultEnum;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowBaseOperation;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025/6/13
 * @description TODO
 */
@FabosJson(
        name = "审核报告",
        orderBy = "ExamineReport.createTime desc",
        power = @Power(add = false,examine = true, examineDetails = true),
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "businessState !='EDIT'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "businessState !='EDIT'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "examine",
                        ifExpr = "examineStatus == '3' || examineStatus == '1'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "examineDetails",
                        ifExpr = "examineStatus == '0'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
        },
        flowCode = "ExamineReport",
        flowProxy = ExamineReportFlowProxy.class
)
@Table(name = "qms_pem_product_examine_report"
)
@Entity
@Getter
@Setter
public class ExamineReport extends NamingRuleModel {

    @Override
    public String getNamingCode() {
        return NamingRuleCodeEnum.ExamineImplementPlan.name();
    }

    @FabosJsonField(
            views = @View(title = "审核结果描述"),
            edit = @Edit(title = "审核结果描述",notNull = true)
    )
    private String examineDescription;

    @FabosJsonField(
            views = @View(title = "处置结果"),
            edit = @Edit(title = "处置结果",type = EditType.CHOICE,choiceType = @ChoiceType(fetchHandler = ImplementPlanHandleResultEnum.class))
    )
    private String handleResult;

    @FabosJsonField(
            views = @View(title = "是否创建纠正预防措施",show = false),
            edit = @Edit(title = "是否创建纠正预防措施",show = false
            )
    )
    private Boolean isCorrectPreventMeasure;

    @FabosJsonField(
            views = @View(title = "纠正措施单", column = "correctPreventMeasureFormNumber"),
            edit = @Edit(title = "纠正措施单", show = false,
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType(type = TabTableReferType.SelectShowTypeMTM.LIST,label = "correctPreventMeasureFormNumber"),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "handleResult == Correct")
            )
    )
    @ManyToOne
    @JoinColumn(name = "correct_prevent_measure_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private CorrectPreventMeasure correctPreventMeasure;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态", readonly = @Readonly, search = @Search,
                    type = EditType.CHOICE,
                    defaultVal = "EDIT",
                    choiceType = @ChoiceType(fetchHandler = OrderCurrentStateEnum.class)),
            dynamicField = @DynamicField(passive = true)
    )
    private String currentState;
}
