package cec.jiutian.bc.inventoryInspection.statistics.ao;

import cec.jiutian.bc.ao.QueryAO;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Set;

@Data
public class StatisticsCommonParam {
    // 时间维度: 1=年,2=季,3=月,4=周,5=日
    private String dimension = "3";

    // 是否自定义时间: 1=是
    private String isDiyTime = "0";

    // 时间范围参数
    private String startTime;
    private String endTime;

    // 预设时间范围:0=全部时间, 1=本年,2=本季,3=本月,4=本周,5=当天
    private String query = "0";

    /**
     * 工厂ids
     */
    private List<String> factoryIds;


    /**
     * 车间ids
     */
    private  List<String>  workshopIds;

    /**
     * 产线ids
     */
    private List<String>  lineIds;


    /**
     * 物料类型编码
     */
    private List<String> spcdctCodes;

    /**
     * 物料编码
     */
    private List<String> materialCodes;

    /**
     * 特性类型
     */
    private List<String> features;

    /**
     * 检测项目
     */
    private List<String> itemIds;

    // 计算维度:
    //0 按批次数量计算 1 按批次重量计算
    private String calculateDimension = "0";

    /**
     * 产出类型：N=一次合格,Y=返工
     */
    private String produceType;

    public static StatisticsCommonParam createByQueryAO(QueryAO queryAO) {
        StatisticsCommonParam param = new StatisticsCommonParam();
        param.setIsDiyTime(queryAO.getIsDiyTime());
        param.setQuery(queryAO.getQuery());
        param.setFeatures(queryAO.getFeatures());
        param.setSpcdctCodes(queryAO.getSpcdctCodes());
        param.setMaterialCodes(queryAO.getMaterialCodes());
        param.setWorkshopIds(queryAO.getWorkshopIds());
        param.setFactoryIds(queryAO.getFactoryIds());
        param.setLineIds(queryAO.getLineIds());
        param.setItemIds(queryAO.getItemIds());
        param.setStartTime(queryAO.getStartTime());
        param.setEndTime(queryAO.getEndTime());
        param.setCalculateDimension(queryAO.getCalculateDimension() == null ? "0" : queryAO.getCalculateDimension() + "");
        if (StringUtils.isNotBlank(queryAO.getProduceType()) && !"0".equals(queryAO.getProduceType())) {
            switch (queryAO.getProduceType()) {
                case "1":
                    param.setProduceType("N");
                case "2":
                    param.setProduceType("Y");
                    break;
            }
        }
        return param;
    }

    // 参数校验方法
    public void validate() {
        // 验证维度值是否合法
        if (!Set.of("1", "2", "3", "4", "5").contains(dimension)) {
            throw new IllegalArgumentException("非法时间维度参数: " + dimension);
        }

        // 验证预设查询类型
        if (!Set.of("0", "1", "2", "3", "4", "5").contains(query)) {
            throw new IllegalArgumentException("非法查询类型参数: " + query);
        }

        // 验证自定义时间
        if ("1".equals(isDiyTime)) {
            if (startTime == null || endTime == null) {
                throw new IllegalArgumentException("自定义时间需指定起止日期");
            }
            if (startTime.compareTo(endTime) > 0) {
                throw new IllegalArgumentException("自定义时间起止日期错误");
            }
        }

        // 验证计算维度
        if (!Set.of("0", "1").contains(calculateDimension)) {
            throw new IllegalArgumentException("非法计算维度参数: " + calculateDimension);
        }
    }
}
