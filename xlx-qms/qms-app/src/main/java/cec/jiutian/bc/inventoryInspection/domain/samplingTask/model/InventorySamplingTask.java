package cec.jiutian.bc.inventoryInspection.domain.samplingTask.model;

import cec.jiutian.bc.inventoryInspection.domain.samplingTask.handler.InventoryGetSampleOperationHandler;
import cec.jiutian.bc.inventoryInspection.domain.samplingTask.handler.InventoryReceiveSampleOperationHandler;
import cec.jiutian.bc.inventoryInspection.domain.samplingTask.handler.InventorySamplingCodeGenerateDynamicHandler;
import cec.jiutian.bc.inventoryInspection.domain.samplingTask.handler.InventorySendSampleOperationHandler;
import cec.jiutian.bc.inventoryInspection.domain.samplingTask.proxy.InventorySamplingTaskProxy;
import cec.jiutian.bc.modeler.enumration.SampleTypeEnum;
import cec.jiutian.bc.modeler.enumration.TaskBusinessStateEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DateType;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.RowOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/10
 * @description TODO
 */
@FabosJson(
        name = "取样任务",
        orderBy = "InventorySamplingTask.createTime desc",
        dataProxy = InventorySamplingTaskProxy.class,
        filter = @Filter(value = "inspectionType = 'INVENTORY_SAMPLING'" ),
        rowOperation = {
                @RowOperation(
                        title = "取样",
                        code = "InventorySamplingTask@GET",
                        operationHandler = InventoryGetSampleOperationHandler.class,
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        popupType = RowOperation.PopupType.FORM,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        fabosJsonClass = InventorySamplingTaskOpr.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "InventorySamplingTask@PUBLISH"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "selectedItems[0].businessState != 'BE_SAMPLING'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "送样",
                        code = "InventorySamplingTask@SEND",
                        operationHandler = InventorySendSampleOperationHandler.class,
                        mode = RowOperation.Mode.BUTTON,
                        type = RowOperation.Type.POPUP,
                        popupType = RowOperation.PopupType.FORM,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        fabosJsonClass = InventorySamplingTaskOpr.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "InventorySamplingTask@PUBLISH"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
                @RowOperation(
                        title = "收样",
                        code = "InventorySamplingTask@RECEIVE",
                        operationHandler = InventoryReceiveSampleOperationHandler.class,
                        mode = RowOperation.Mode.BUTTON,
                        type = RowOperation.Type.POPUP,
                        popupType = RowOperation.PopupType.FORM,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        fabosJsonClass = InventorySamplingTaskReceiveOpr.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "InventorySamplingTask@PUBLISH"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
        }
)
@Table(name = "mi_sampling_task",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Getter
@Setter
@TemplateType(type = "multiTable")
public class InventorySamplingTask extends MetaModel {

        @FabosJsonField(
                views = @View(title = "编号"),
                edit = @Edit(title = "编号", notNull = true),
                dynamicField = @DynamicField(dependFiled = @DependFiled(buttonName = "生成", changeBy = "id",
                        dynamicHandler = InventorySamplingCodeGenerateDynamicHandler.class))
        )
        private String generalCode;

        @FabosJsonField(
                views = @View(title = "检验任务单号"),
                edit = @Edit(title = "检验任务单号", notNull = true)
        )
        private String inspectionTaskCode;

        @FabosJsonField(
                views = @View(title = "是否加急"),
                edit = @Edit(title = "是否加急", defaultVal = "false"
                )
        )
        private Boolean isUrgent;

        @FabosJsonField(
                views = @View(title = "是否留样"),
                edit = @Edit(title = "是否留样", defaultVal = "false"
                )
        )
        private Boolean isSaveSample;

        @FabosJsonField(
                views = @View(title = "供应商名称"),
                edit = @Edit(title = "供应商名称", search = @Search(vague = true))
        )
        private String supplierName;

        @FabosJsonField(
                views = @View(title = "检验状态"),
                edit = @Edit(title = "检验状态", type = EditType.CHOICE,search = @Search(),
                        choiceType = @ChoiceType(fetchHandler = TaskBusinessStateEnum.class))
        )
        private String businessState;

        @FabosJsonField(
                views = @View(title = "物资编码"),
                edit = @Edit(title = "物资编码", notNull = true)
        )
        private String materialCode;

        @FabosJsonField(
                views = @View(title = "物资名称"),
                edit = @Edit(title = "物资名称", notNull = true)
        )
        private String materialName;

        @FabosJsonField(
                views = @View(title = "批次号"),
                edit = @Edit(title = "批次号", notNull = true)
        )
        private String originLotId;

        @FabosJsonField(
                views = @View(title = "规格"),
                edit = @Edit(title = "规格", notNull = true)
        )
        private String materialSpecification;

        @FabosJsonField(
                views = @View(title = "单位"),
                edit = @Edit(title = "单位", notNull = true)
        )
        private String unit;

        @FabosJsonField(
                views = @View(title = "取样类型"),
                edit = @Edit(title = "取样类型", type = EditType.CHOICE,search = @Search(),
                        choiceType = @ChoiceType(fetchHandler = SampleTypeEnum.class))
        )
        private String inspectionType;

        @FabosJsonField(
                views = @View(title = "检验项目组"),
                edit = @Edit(title = "检验项目组", search = @Search(vague = true))
        )
        private String inspectionItemGroupName;

        @FabosJsonField(
                views = @View(title = "包装方式"),
                edit = @Edit(title = "包装方式", readonly = @Readonly)
        )
        private String packageType;

        @FabosJsonField(
                views = @View(title = "取样点"),
                edit = @Edit(title = "取样点",
                        search = @Search(vague = true)
                )
        )
        private String samplePoint;

        @FabosJsonField(
                views = @View(title = "送样点"),
                edit = @Edit(title = "送样点",
                        search = @Search(vague = true)
                )
        )
        private String sendPoint;
        @FabosJsonField(
                views = @View(title = "取样数量"),
                edit = @Edit(title = "取样数量", readonly = @Readonly,
                        numberType = @NumberType(precision = 2))
        )
        private Double allInspectionItemQuantity;

        @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
        @FabosJsonField(
                views = @View(title = "计划取样时间", type = ViewType.DATE),
                edit = @Edit(title = "计划取样时间", show = false,
                        dateType = @DateType(type = DateType.Type.DATE))
        )
        private Date samplePlanDate;

        @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
        @FabosJsonField(
                views = @View(title = "实际取样时间", type = ViewType.DATE),
                edit = @Edit(title = "实际取样时间", show = false,
                        dateType = @DateType(type = DateType.Type.DATE))
        )
        private Date sampleActualDate;

        @FabosJsonField(
                views = @View(title = "送样人"),
                edit = @Edit(title = "送样人", search = @Search(vague = true))
        )
        private String sendSamplePerson;

        @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
        @FabosJsonField(
                views = @View(title = "送样日期", type = ViewType.DATE),
                edit = @Edit(title = "送样日期", notNull = true,
                        dateType = @DateType(type = DateType.Type.DATE))
        )
        private Date sendSampleDate;

        @FabosJsonField(
                views = @View(title = "收样人id",show = false),
                edit = @Edit(title = "收样人id", show = false)
        )
        private String receiveSamplePersonId;

        @FabosJsonField(
                views = @View(title = "收样人"),
                edit = @Edit(title = "收样人", search = @Search(vague = true))
        )
        private String receiveSamplePerson;

        @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
        @FabosJsonField(
                views = @View(title = "收样日期", type = ViewType.DATE),
                edit = @Edit(title = "收样日期", notNull = true,
                        dateType = @DateType(type = DateType.Type.DATE))
        )
        private Date receiveSampleDate;

        @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
        @JoinColumn(name = "sampling_task_id")
        @OrderBy
        @FabosJsonField(
                edit = @Edit(title = "取样任务明细", type = EditType.TAB_TABLE_ADD),
                views = @View(title = "取样任务明细", type = ViewType.TABLE_VIEW)
        )
        private List<InventorySamplingTaskDetail> detailList;
}
