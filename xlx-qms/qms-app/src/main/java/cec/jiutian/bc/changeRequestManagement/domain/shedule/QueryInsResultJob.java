package cec.jiutian.bc.changeRequestManagement.domain.shedule;

import cec.jiutian.bc.changeRequestManagement.domain.ecr.model.ChangeTrace;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.service.ChangeTraceService;
import cec.jiutian.bc.job.provider.IJobProvider;
import cec.jiutian.core.frame.annotation.FabosCustomizedService;
import cec.jiutian.meta.FabosJob;
import jakarta.annotation.Resource;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@FabosCustomizedService(value = ChangeTrace.class)
@Component
@Transactional
@Slf4j
public class QueryInsResultJob implements IJobProvider {

    @Resource
    private ChangeTraceService changeTraceService;

    @Override
    @FabosJob(comment = "ecr获取过程检验结果")
    public String exec(String code, String param) {
        changeTraceService.getInsResult();
        return "执行成功";
    }
}
