package cec.jiutian.bc.materialInspect.domain.samplingTask.handler;

import cec.jiutian.bc.materialInspect.domain.samplingTask.model.SamplingTaskReceiveOpr;
import cec.jiutian.bc.materialInspect.enumeration.InspectionResultEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.DependFiled;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：2025/7/4
 * @description：
 */
@Component
public class SamplingTaskDiscoveredPersonDynamicHandler implements DependFiled.DynamicHandler<SamplingTaskReceiveOpr> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public Map<String, Object> handle(SamplingTaskReceiveOpr samplingTaskReceiveOpr) {
        Map<String, Object> result = new HashMap<>();
        if (StringUtils.isNotEmpty(samplingTaskReceiveOpr.getAppearanceInspect())) {
            if (samplingTaskReceiveOpr.getAppearanceInspect().equals(InspectionResultEnum.Enum.UNQUALIFIED.name())
                    && samplingTaskReceiveOpr.getDiscoveredPerson() == null) {
                result.put("discoveredPersonName", "");
            }
        }
        return result;
    }
}
