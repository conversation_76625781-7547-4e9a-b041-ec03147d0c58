package cec.jiutian.bc.inventoryInspection.statistics.util;

import cec.jiutian.bc.inventoryInspection.statistics.ao.StatisticsCommonParam;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.time.Month;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;

public class QualifiedRateQuery {

    public static final String mineTimeSql = "SELECT TO_CHAR(MIN(output_time), 'YYYY-MM-DD HH24:MI:SS') AS formatted_time FROM batch_serial";

    private static final String slqPart1 = "(SELECT fap.ID pId,fap.fctry_ara_nm pNm,fas.ID sId FROM mo_fctry_ara fas LEFT JOIN mo_fctry_ara fap ON fap.ID = fas.pid ) fa ";

    private static String buildBaseSQL(StatisticsCommonParam param){
        StringBuilder sql = new StringBuilder(2000);
        sql.append(" (SELECT \n")
                .append(" output_time,")
                .append(" serial_number,")
                .append(" output_quantity,")
                .append("CASE")
                .append(" ( qualified_flag ) ")
                .append(" WHEN 'N' THEN")
                .append(" output_quantity ELSE 0 ")
                .append(" END AS unqualified_quantity,")
                .append("CASE")
                .append(" ( qualified_flag ) ")
                .append(" WHEN 'Y' THEN")
                .append(" output_quantity ELSE 0 ")
                .append(" END AS qualified_quantity,")
                .append(" qualified_flag ")
                .append(" FROM batch_serial bs \n")
                .append(" LEFT JOIN ms_spcfcn fcn ON bs.material_code = fcn.spcfcn_cd \n")
                .append(" LEFT JOIN ms_spcdct dct ON fcn.spcdct_cd = dct.spcdct_cd \n")
                .append(" LEFT JOIN ").append(slqPart1).append("ON bs.workshop_id = fa.sId \n")
                .append(" WHERE output_quantity > 0 \n")
                .append(" AND output_time IS NOT NULL \n")
                .append(" AND process_code = 'CP' \n");


        // 1. 产出类型条件
        if (StringUtils.isNotBlank(param.getProduceType())) {
            sql.append(" AND bs.rework_flag = '").append(param.getProduceType()).append("' \n");
        }
        // 2. 时间范围条件（动态处理预设/自定义时间）
        if ("1".equals(param.getIsDiyTime())) {
            TimeRangeCalculator.TimeRange range = TimeRangeCalculator.calculateTimeRange(param.getIsDiyTime(), param.getStartTime(), param.getEndTime(), param.getQuery());
            sql.append(" AND output_time BETWEEN TO_TIMESTAMP('" + range.getFormattedStart() + "', 'YYYY-MM-DD HH24:MI:SS') AND TO_TIMESTAMP('" + range.getFormattedEnd() + "', 'YYYY-MM-DD HH24:MI:SS') \n");
        }

        // 4. 物料类型条件
        if (CollectionUtils.isNotEmpty(param.getSpcdctCodes())) {
            sql.append(" AND dct.spcdct_cd IN (");
            param.getSpcdctCodes().forEach(code -> sql.append("'").append(code).append("',"));
            sql.setLength(sql.length() - 1);
            sql.append(") \n");
        } else if (CollectionUtils.isNotEmpty(param.getMaterialCodes())) {
            // 3. 物料编码条件
            sql.append(" AND material_code IN (");
            param.getMaterialCodes().forEach(code -> sql.append("'").append(code).append("',"));
            sql.setLength(sql.length() - 1); // 移除最后一个逗号
            sql.append(") \n");
        }

        // 5. 组织架构条件（产线>车间>工厂的优先级）
        if (CollectionUtils.isNotEmpty(param.getLineIds())) {
            sql.append(" AND production_line_id IN (");
            param.getLineIds().forEach(id -> sql.append("'").append(id).append("',"));
            sql.deleteCharAt(sql.length() - 1);
            sql.append(") \n");
        } else if (CollectionUtils.isNotEmpty(param.getWorkshopIds())) {
            sql.append(" AND workshop_id IN (");
            param.getWorkshopIds().forEach(id -> sql.append("'").append(id).append("',"));
            sql.deleteCharAt(sql.length() - 1);
            sql.append(") \n");
        } else if (CollectionUtils.isNotEmpty(param.getFactoryIds())) {
            sql.append(" AND workshop_id IN ( \n");
            sql.append(" SELECT id FROM mo_fctry_ara WHERE pid IN (");
            param.getFactoryIds().forEach(id -> sql.append("'").append(id).append("',"));
            sql.deleteCharAt(sql.length() - 1);
            sql.append(") \n  ) \n");
        }
        // 6. 检测任务条件（动态拼接检测项目和特性）
        if (CollectionUtils.isNotEmpty(param.getItemIds()) ||
                CollectionUtils.isNotEmpty(param.getFeatures())) {

            sql.append(" AND bs.serial_number IN ( \n");
            sql.append(" SELECT pt.actual_lot_serial_id \n");
            sql.append(" FROM pi_inspection_task pt \n");
            sql.append(" WHERE 1=1 \n");

            // 6.1 检测项目条件 / 特性条件
            if (CollectionUtils.isNotEmpty(param.getItemIds())) {
                sql.append(" AND pt.id IN ( \n");
                sql.append(" SELECT inspection_task_id \n");
                sql.append(" FROM pi_inspection_task_detail \n");
                sql.append(" WHERE item_id IN (");
                param.getItemIds().forEach(id -> sql.append("'").append(id).append("',"));
                sql.setLength(sql.length() - 1);
                sql.append(") \n ) \n)");
            } else if (CollectionUtils.isNotEmpty(param.getFeatures())) {
                sql.append(" AND pt.id IN ( \n");
                sql.append(" SELECT ptd.inspection_task_id \n");
                sql.append(" FROM pi_inspection_task_detail ptd \n");
                sql.append(" LEFT JOIN bd_inspection_item bi ON ptd.item_id = bi.id \n");
                sql.append(" WHERE bi.feature IN (");
                param.getFeatures().forEach(feature -> sql.append("'").append(feature).append("',"));
                sql.setLength(sql.length() - 1);
                sql.append(") \n ) \n )\n )");
            }
        }
        sql.append(") AS base_data \n");
        return sql.toString();
    }

    public static String buildSql(StatisticsCommonParam param) {

        // 1. 获取原始时间范围
        LocalDateTime[] timeRange = getTimeRange(param.getIsDiyTime(), param.getQuery(),param.getStartTime(), param.getEndTime());
        LocalDateTime startDateTime = timeRange[0];
        LocalDateTime endDateTime = timeRange[1];  // 确保不超过当前时间

        // 2. 根据时间维度调整时间范围边界
        Object[] adjusted = adjustTimeRangeForDimension(startDateTime, endDateTime, param.getDimension());
        LocalDateTime seriesStart = (LocalDateTime) adjusted[0];
        LocalDateTime seriesEnd = (LocalDateTime) adjusted[1];
        String interval = (String) adjusted[2];

        // 3. 构建时间维度表达式
        String timeDimExpr = getTimeDimensionExpression(param.getDimension(), "output_time");
        String seriesTimeDimExpr = getTimeDimensionExpression(param.getDimension(), "time_point");

        // 4. 构建计算表达式
        String[] calcExprs = getCalculationExpressions(param.getCalculateDimension());
        String totalOutputExpr = calcExprs[0];
        String qualifiedOutputExpr = calcExprs[1];

        // 5. 格式化日期字符串用于SQL
        DateTimeFormatter sqlFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String seriesStartStr = seriesStart.format(sqlFormatter);
        String seriesEndStr = seriesEnd.format(sqlFormatter);
        String startDateTimeStr = startDateTime.format(sqlFormatter);
        String endDateTimeStr = endDateTime.format(sqlFormatter);

        // 6. 构建完整SQL
        return "WITH time_series AS (\n" +
                "  SELECT generate_series(\n" +
                "    TIMESTAMP '" + seriesStartStr + "',\n" +
                "    TIMESTAMP '" + seriesEndStr + "',\n" +
                "    INTERVAL '" + interval + "'\n" +
                "  ) AS time_point\n" +
                ")\n" +
                "SELECT \n" +
                "  ts.time_formatted AS time_period,\n" +
                "  COALESCE(prod.total_output, 0) AS total_output,\n" +
                "  COALESCE(prod.qualified_output, 0) AS qualified_output,\n" +
                "  CASE WHEN COALESCE(prod.total_output, 0) > 0\n" +
                "       THEN ROUND(COALESCE(prod.qualified_output, 0)::::NUMERIC / prod.total_output, 4)\n" +
                "       ELSE 0 END AS qualified_rate\n" +
                "FROM (\n" +
                "  SELECT \n" +
                "    " + seriesTimeDimExpr + " AS time_formatted\n" +
                "  FROM time_series\n" +
                ") ts\n" +
                "LEFT JOIN (\n" +
                "  SELECT \n" +
                "    " + timeDimExpr + " AS time_period,\n" +
                "    " + totalOutputExpr + " AS total_output,\n" +
                "    " + qualifiedOutputExpr + " AS qualified_output\n" +
                "  FROM "+ buildBaseSQL(param) + "\n" +
                "  WHERE output_time BETWEEN TIMESTAMP '" + startDateTimeStr + "' AND TIMESTAMP '" + endDateTimeStr + "'\n" +
                "  GROUP BY " + timeDimExpr + "\n" +
                ") prod ON ts.time_formatted = prod.time_period\n" +
                "ORDER BY ts.time_formatted";
    }

    private static String getTimeDimensionExpression(String dimension, String column) {
        switch (dimension) {
            case "1": // 年
                return "TO_CHAR(" + column + ", 'YYYY\"年\"')";
            case "2": // 季度
                return "TO_CHAR(" + column + ", 'YYYY\"年\"') || " +
                        "CASE EXTRACT(QUARTER FROM " + column + ") " +
                        "  WHEN 1 THEN '第1季度' " +
                        "  WHEN 2 THEN '第2季度' " +
                        "  WHEN 3 THEN '第3季度' " +
                        "  WHEN 4 THEN '第4季度' " +
                        "END";
            case "3": // 月
                return "TO_CHAR(" + column + ", 'YYYY\"年\"MM\"月\"')";
            case "4": // 周
                return "EXTRACT(ISOYEAR FROM " + column + ") || '年第' || " +
                        "LPAD(EXTRACT(WEEK FROM " + column + ")::::text, 2, '0') || '周'";
            case "5": // 日
                return "TO_CHAR(" + column + ", 'YYYY\"年\"MM\"月\"DD\"日\"')";
            default:
                throw new IllegalArgumentException("无效的时间维度");
        }
    }

    private static String[] getCalculationExpressions(String calculateDimension) {
        if ("0".equals(calculateDimension)) {
            return new String[] {
                    "COUNT(serial_number)",
                    "COUNT(CASE WHEN qualified_flag = 'Y' THEN 1 END)"
            };
        } else {
            return new String[] {
                    "SUM(output_quantity)",
                    "SUM(CASE WHEN qualified_flag = 'Y' THEN output_quantity ELSE 0 END)"
            };
        }
    }

    private static LocalDateTime[] getTimeRange(String isDiyTime, String query, String diyStart, String diyEnd) {
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        if ( "1".equals(isDiyTime)) {
            LocalDateTime start = parseDateTime(diyStart, formatter);
            LocalDateTime end = parseDateTime(diyEnd, formatter);
            return new LocalDateTime[]{start, end.isAfter(now) ? now : end};
        }

        LocalDateTime start;
        switch (query) {

            case "0":
                start = LocalDateTime.parse(diyStart, formatter);
                break;
            case "1": // 本年
                start = LocalDateTime.of(now.getYear(), 1, 1, 0, 0);
                break;
            case "2": // 本季
                int quarter = (now.getMonthValue() - 1) / 3;
                Month firstMonth = Month.of(quarter * 3 + 1);
                start = LocalDateTime.of(now.getYear(), firstMonth, 1, 0, 0);
                break;
            case "3": // 本月
                start = LocalDateTime.of(now.getYear(), now.getMonth(), 1, 0, 0);
                break;
            case "4": // 本周
                start = now.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY))
                        .withHour(0).withMinute(0).withSecond(0);
                break;
            case "5": // 当天
                start = now.withHour(0).withMinute(0).withSecond(0);
                break;
            default:
                start = LocalDateTime.of(now.getYear(), 1, 1, 0, 0);
                break;
        }

        return new LocalDateTime[]{start, now};
    }

    private static Object[] adjustTimeRangeForDimension(LocalDateTime start, LocalDateTime end, String dimension) {
        LocalDateTime seriesStart = start;
        LocalDateTime seriesEnd = end;
        String interval;
        switch (dimension) {
            case "1": // 年
                seriesStart = start.withMonth(1).withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0);
                seriesEnd = end.plusYears(1).withMonth(1).withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).minusSeconds(1);
                interval = "1 year";
                break;
            case "2": // 季
                int startQuarter = (start.getMonthValue() - 1) / 3;
                int startQuarterMonth = startQuarter * 3 + 1;
                seriesStart = start.withMonth(startQuarterMonth).withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0);

                int endQuarter = (end.getMonthValue() - 1) / 3;
                int endQuarterMonth = endQuarter * 3 + 1;
                seriesEnd = end.withMonth(endQuarterMonth).withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0)
                        .plusMonths(3).minusSeconds(1);
                interval = "3 months";
                break;
            case "3": // 月
                seriesStart = start.withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0);
                seriesEnd = end.withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).plusMonths(1).minusSeconds(1);
                interval = "1 month";
                break;
            case "4": // 周
                seriesStart = start.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY))
                        .withHour(0).withMinute(0).withSecond(0);
                seriesEnd = end.with(TemporalAdjusters.next(DayOfWeek.MONDAY))
                        .withHour(0).withMinute(0).withSecond(0).minusSeconds(1);
                interval = "1 week";
                break;
            case "5": // 日
                seriesStart = start.toLocalDate().atStartOfDay();
                seriesEnd = end.toLocalDate().plusDays(1).atStartOfDay().minusSeconds(1);
                interval = "1 day";
                break;
            default:
                throw new IllegalArgumentException("无效的时间范围参数");
        }
        return new Object[]{seriesStart, seriesEnd, interval};
    }

    private static LocalDateTime parseDateTime(String datetime, DateTimeFormatter formatter) {
        if (datetime == null || datetime.trim().isEmpty()) {
            throw new IllegalArgumentException("时间参数不能为空");
        }
        try {
            return LocalDateTime.parse(datetime, formatter);
        } catch (Exception e) {
            throw new IllegalArgumentException("时间格式错误，请使用 yyyy-MM-dd HH:mm:ss 格式");
        }
    }
}