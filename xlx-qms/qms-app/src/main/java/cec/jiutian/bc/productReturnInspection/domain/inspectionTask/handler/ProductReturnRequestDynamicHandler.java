package cec.jiutian.bc.productReturnInspection.domain.inspectionTask.handler;

import cec.jiutian.bc.productReturnInspection.domain.inspectionRequest.model.ProductReturnInspectionRequest;
import cec.jiutian.bc.productReturnInspection.domain.inspectionTask.model.ProductReturnInspectionTask;
import cec.jiutian.bc.productReturnInspection.domain.inspectionTask.model.ProductReturnInspectionTaskDetail;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/3/10
 * @description dynamic
 */
@Component
public class ProductReturnRequestDynamicHandler implements DependFiled.DynamicHandler<ProductReturnInspectionTask> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public Map<String, Object> handle(ProductReturnInspectionTask productReturnInspectionTask) {
        Map<String, Object> result = new HashMap<>();
        List<ProductReturnInspectionTaskDetail> detailList = new ArrayList<>();
        if (productReturnInspectionTask.getInspectionRequest() != null) {
            ProductReturnInspectionRequest inspectionRequest = fabosJsonDao.findById(ProductReturnInspectionRequest.class, productReturnInspectionTask.getInspectionRequest().getId());
            if (CollectionUtils.isNotEmpty(inspectionRequest.getDetails())) {
                inspectionRequest.getDetails().forEach(inspectionRequestDetail -> {
                    ProductReturnInspectionTaskDetail inspectionTaskDetail = new ProductReturnInspectionTaskDetail();
                    inspectionTaskDetail.setInventoryId(inspectionTaskDetail.getInventoryId());
                    inspectionTaskDetail.setMaterialCode(inspectionRequestDetail.getMaterialCode());
                    inspectionTaskDetail.setMaterialName(inspectionRequestDetail.getMaterialName());
                    inspectionTaskDetail.setLotSerialId(inspectionRequestDetail.getLotSerialId());
                    inspectionTaskDetail.setLotQuantity(inspectionRequestDetail.getLotQuantity());
                    inspectionTaskDetail.setMeasureUnit(inspectionRequestDetail.getMeasureUnit());
                    inspectionTaskDetail.setSampleFlag(inspectionTaskDetail.getSampleFlag());
                    detailList.add(inspectionTaskDetail);
                });
            }
        }
        result.put("productReturnInspectionTaskDetailList",detailList);
        return result;
    }
}
