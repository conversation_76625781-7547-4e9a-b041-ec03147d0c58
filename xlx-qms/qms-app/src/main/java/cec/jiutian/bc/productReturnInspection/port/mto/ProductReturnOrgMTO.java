package cec.jiutian.bc.productReturnInspection.port.mto;

import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.FabosJsonI18n;
import cec.jiutian.view.constant.AnnotationConst;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.Search;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "fd_org")
@FabosJson(
        name = "部门",
        orderBy = "OrgMTO.sort asc"
)
@FabosJsonI18n
@Getter
@Setter
@NoArgsConstructor
public class ProductReturnOrgMTO extends MetaModel {

    @Column(length = AnnotationConst.CODE_LENGTH, unique = true)
    @FabosJsonField(
            views = @View(title = "部门编码", sortable = true),
            edit = @Edit(title = "部门编码", notNull = true, search = @Search(vague = true), readonly = @Readonly(add = false, edit = true))
    )
    private String code;

    @FabosJsonField(
            views = @View(title = "部门名称", sortable = true),
            edit = @Edit(title = "部门名称", notNull = true, search = @Search(vague = true))
    )
    private String name;

    @FabosJsonField(
            views = @View(title = "部门职能", sortable = true),
            edit = @Edit(title = "部门职能", notNull = false, type = EditType.TEXTAREA, inputType = @InputType(length = 400))
    )
    private String departmentFunction;

    private Integer sort;

}
