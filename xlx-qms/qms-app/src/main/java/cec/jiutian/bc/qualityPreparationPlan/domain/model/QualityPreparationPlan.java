package cec.jiutian.bc.qualityPreparationPlan.domain.model;

import cec.jiutian.bc.mto.FactoryArea;
import cec.jiutian.bc.qualityPreparationPlan.domain.handler.PlanGeneralCodeDynamicHandler;
import cec.jiutian.bc.qualityPreparationPlan.domain.handler.QualityPreparationPlanDynamicHandler;
import cec.jiutian.bc.qualityPreparationPlan.domain.proxy.QualityPreparationPlanProxy;
import cec.jiutian.bc.qualityPreparationPlan.enums.QualityPreparationPlanStatusEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Power;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Table(name = "qms_quality_preparation_plan")
@Entity
@Getter
@Setter
@FabosJson(
        name = "质量准备计划",
        orderBy = "QualityPreparationPlan.createTime desc",
        power = @Power(edit = false, export = false, importable = false),
        dataProxy = QualityPreparationPlanProxy.class
)
public class QualityPreparationPlan extends MetaModel {

    @FabosJsonField(
            views = @View(title = "编号"),
            edit = @Edit(title = "编号", notNull = true),
            dynamicField = @DynamicField(dependFiled = @DependFiled(buttonName = "生成", changeBy = "id",
                    dynamicHandler = PlanGeneralCodeDynamicHandler.class))
    )
    private String generalCode;

    //产线名称
    @Transient
    @FabosJsonField(
            views = @View(title = "请选择产线", column = "factoryAreaName", show = false),
            edit = @Edit(
                    title = "请选择产线",
                    notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType(label = "factoryAreaName")
            )
    )
    private FactoryArea line;

    @FabosJsonField(
            views = @View(title = "产线名称"),
            edit = @Edit(
                    title = "产线名称",
                    readonly = @Readonly(add = true, edit = true),
                    notNull = true
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "line", beFilledBy = "factoryAreaName"))
    )
    private String lineName;

    @FabosJsonField(
            views = @View(title = "产线ID", show = false),
            edit = @Edit(
                    show = false,
                    title = "产线ID"
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "line", beFilledBy = "id"))
    )
    private String lineId;

    // 准备计划月份
    @FabosJsonField(
            views = @View(title = "准备计划年月"),
            edit = @Edit(title = "准备计划年月", notNull = true, search = @Search(),
                    type = EditType.DATE,dependFieldDisplay = @DependFieldDisplay(showOrHide = "line != null"),
                    dateType = @DateType(type = DateType.Type.MONTH))
    )
    private String PlanYearAndMonth;

    // 状态
    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态", type = EditType.CHOICE, readonly = @Readonly, defaultVal = "EFFECTIVE",
                    show = false, choiceType = @ChoiceType(fetchHandler = QualityPreparationPlanStatusEnum.class))
    )
    private String status;

    //计划详情
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @FabosJsonField(
            views = @View(title = "质量准备计划详情", type = ViewType.TABLE_VIEW),
            edit = @Edit(title = "质量准备计划详情", type = EditType.TAB_REFERENCE_GENERATE),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = {"lineId", "PlanYearAndMonth"},
                    dynamicHandler = QualityPreparationPlanDynamicHandler.class))
    )
    @JoinColumn(name = "quality_preparation_plan_id")
    private List<QualityPreparationPlanDetail> qualityPreparationPlanDetailList;
}
