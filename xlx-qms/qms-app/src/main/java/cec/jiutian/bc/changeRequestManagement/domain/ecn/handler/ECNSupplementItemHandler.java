package cec.jiutian.bc.changeRequestManagement.domain.ecn.handler;

import cec.jiutian.bc.changeRequestManagement.domain.ecn.model.ChangeRequestExecute;
import cec.jiutian.bc.changeRequestManagement.domain.ecn.model.ECNItem;
import cec.jiutian.bc.changeRequestManagement.domain.ecn.mto.ECNSupplementItemMTO;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import jakarta.persistence.LockModeType;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Component
public class ECNSupplementItemHandler implements OperationHandler<ChangeRequestExecute, ECNSupplementItemMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Transactional
    @Override
    public String exec(List<ChangeRequestExecute> data, ECNSupplementItemMTO modelObject, String[] param) {
        if (CollectionUtils.isEmpty(data) || data.get(0).getId() == null) {
            throw new ServiceException("数据异常");
        }
        ChangeRequestExecute changeRequest = fabosJsonDao.getEntityManager().find(ChangeRequestExecute.class, data.get(0).getId(), LockModeType.PESSIMISTIC_WRITE);
       List<ECNItem> ecrItems = modelObject.getECNItems();
        if (CollectionUtils.isEmpty(ecrItems)) {
            throw new ServiceException("请添加变更明细");
        }
        for (ECNItem ecrItem : ecrItems) {
            ecrItem.setEcn(changeRequest);
        }
        fabosJsonDao.mergeAndFlush(modelObject);
        return "提交成功";
    }

    @Override
    public ECNSupplementItemMTO fabosJsonFormValue(List<ChangeRequestExecute> data, ECNSupplementItemMTO fabosJsonForm, String[] param) {
        if (CollectionUtils.isEmpty(data)) {
            throw new ServiceException("数据异常");
        }
        ChangeRequestExecute changeRequest = data.get(0);
        BeanUtils.copyProperties(changeRequest, fabosJsonForm);
        return fabosJsonForm;
    }
}
