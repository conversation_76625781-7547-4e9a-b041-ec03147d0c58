package cec.jiutian.bc.qualityExceptionManagement.domain.reworkTask.service;

import cec.jiutian.bc.changeRequestManagement.enums.ReqTraceResultEnum;
import cec.jiutian.bc.processInspect.domain.processInspectionTask.model.ProcessInspectionTask;
import cec.jiutian.bc.qualityExceptionManagement.domain.reworkTask.dto.ReworkDetailDTO;
import cec.jiutian.bc.qualityExceptionManagement.domain.reworkTask.dto.ReworkTraceDTO;
import cec.jiutian.bc.qualityExceptionManagement.domain.reworkTask.model.ReworkDetail;
import cec.jiutian.bc.qualityExceptionManagement.domain.reworkTask.model.ReworkTask;
import cec.jiutian.bc.qualityExceptionManagement.domain.reworkTask.model.ReworkTrace;
import cec.jiutian.bc.qualityExceptionManagement.domain.reworkTask.repository.ReworkDetailRepository;
import cec.jiutian.bc.qualityExceptionManagement.domain.reworkTask.repository.ReworkTaskRepository;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import com.google.gson.JsonObject;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.Objects;
import java.util.Optional;

@Service
public class ReworkDetailService {

    private static final Logger log = LoggerFactory.getLogger(ReworkDetailService.class);
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private ReworkTaskRepository repository;

    @Resource
    private ReworkDetailRepository reworkDetailRepository;

    public String create(ReworkDetailDTO params) {
        if (params == null) {
            throw new ServiceException("请求参数为空，创建失败");
        }
        String reworkTaskCode = params.getReworkTaskCode();
        if (StringUtils.isBlank(reworkTaskCode)) {
            throw new ServiceException("返工任务单号不能为空");
        }
        Double reworkQuantity = params.getReworkQuantity();
        if (reworkQuantity == null || reworkQuantity <= 0) {
            throw new ServiceException("返工数量不能为空");
        }
        ReworkTask reworkTask = repository.findByGeneralCode(reworkTaskCode);

        if (reworkTask == null) {
            log.error("返工工单不存在：{}", reworkTaskCode);
            throw new ServiceException("返工工单不存在");
        }

        ReworkDetail reworkDetail = new ReworkDetail();
        reworkDetail.setReworkDate(new Date());
        reworkDetail.setReworkTaskCode(reworkTaskCode);
        reworkDetail.setReworkQuantity(reworkQuantity);
        reworkDetail.setProductBatch(params.getProductBatch());
        fabosJsonDao.persistAndFlush(reworkDetail);

        reworkTask.getReworkDetails().add(reworkDetail);
        repository.saveAndFlush(reworkTask);
        return reworkDetail.getId();
    }


    public void synchronizeProductBatch(JsonObject params) {
        if (params == null) {
            throw new ServiceException("请求参数为空，同步失败");
        }
        String detailId = params.get("detailId").getAsString();
        if (StringUtils.isBlank(detailId)) {
            throw new ServiceException("返工明细id为空");
        }
        String productBatch = params.get("productBatch").getAsString();
        if (StringUtils.isBlank(productBatch)) {
            throw new ServiceException("成品批次号不能为空");
        }

        Optional<ReworkDetail> optional = reworkDetailRepository.findById(detailId);
        if (!optional.isPresent()) {
            throw new ServiceException("返工明细不存在");
        }
        ReworkDetail detail = optional.get();
        detail.setProductBatch(productBatch);
        reworkDetailRepository.save(detail);
    }

    public String createTrace(ReworkTraceDTO params) {
        if (params == null) {
            throw new ServiceException("请求参数为空");
        }
        String detailId = params.getDetailId();
        //if (StringUtils.isBlank(detailId)) {
        //    throw new ServiceException("detailId不能为空");
        //}
        String serialNumber = params.getSerialNumber();
        if (StringUtils.isBlank(serialNumber)) {
            throw new ServiceException("serialNumber不能为空");
        }
        String reworkTaskCode = params.getReworkTaskCode();
        if (StringUtils.isBlank(reworkTaskCode)) {
            throw new ServiceException("返工任务单号不能为空");
        }
        Double weight = params.getWeight();
        if (weight == null || weight <= 0) {
            throw new ServiceException("重量不能为空");
        }
        //reworkDetailRepository.findById(detailId).get();

        ReworkTask reworkTask = repository.findByGeneralCode(reworkTaskCode);
        if (reworkTask == null) {
            log.error("返工工单不存在：{}", reworkTaskCode);
            throw new ServiceException("返工工单不存在");
        }
        params.setReworkTaskId(reworkTask.getId());

        ProcessInspectionTask processInspectionTask = new ProcessInspectionTask();
        processInspectionTask.setActualLotSerialId(serialNumber);
        ProcessInspectionTask task = fabosJsonDao.selectOne(processInspectionTask);
        String code = Objects.isNull(task) ? "" : task.getGeneralCode();
        String result = (Objects.isNull(task) || StringUtils.isBlank(task.getResult())) ? ReqTraceResultEnum.Enum.WAITING.name() : task.getResult();

        ReworkTrace reworkTrace = ReworkTrace.create(serialNumber, params.getProcess(), detailId, reworkTask.getId(), weight, code, result);
        //reworkTrace.setInspectionResult(ReqTraceResultEnum.Enum.WAITING.name());
        fabosJsonDao.persistAndFlush(reworkTrace);
        return reworkTrace.getId();
    }


}
