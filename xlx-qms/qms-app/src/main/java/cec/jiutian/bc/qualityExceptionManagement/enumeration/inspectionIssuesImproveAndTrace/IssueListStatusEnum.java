package cec.jiutian.bc.qualityExceptionManagement.enumeration.inspectionIssuesImproveAndTrace;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

public class IssueListStatusEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum value : Enum.values()) {
            list.add(new VLModel(value.name(), value.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        WAIT_PUBLISH("待发布"),
        WAIT_RUN("待执行"),
        RUNNING("执行中"),
        VERIFIED("待验证"),
        FINISH("已完成"),
        ;

        private final String value;
    }
}
