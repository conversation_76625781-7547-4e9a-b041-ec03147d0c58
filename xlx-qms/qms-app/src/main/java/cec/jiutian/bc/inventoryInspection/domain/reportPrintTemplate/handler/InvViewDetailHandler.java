package cec.jiutian.bc.inventoryInspection.domain.reportPrintTemplate.handler;

import cec.jiutian.bc.inventoryInspection.domain.reportPrintTemplate.model.InvInsReportPrintTemplate;
import cec.jiutian.bc.inventoryInspection.domain.reportPrintTemplate.mto.InventoryInspectionTaskMTO;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class InvViewDetailHandler implements OperationHandler<InvInsReportPrintTemplate, InventoryInspectionTaskMTO> {
    @Resource
    private FabosJsonDao fabosJsonDao;


    @Override
    public InventoryInspectionTaskMTO fabosJsonFormValue(List<InvInsReportPrintTemplate> data, InventoryInspectionTaskMTO fabosJsonForm, String[] param) {
        InvInsReportPrintTemplate reportPrintTemplate = data.get(0);
        InventoryInspectionTaskMTO task = fabosJsonDao.findById(InventoryInspectionTaskMTO.class, reportPrintTemplate.getId());
        return task;
    }

}
