package cec.jiutian.bc.productExamineManagement.domain.examineImplementPlan.handler;

import cec.jiutian.bc.mto.SpecificationManageMTO;
import cec.jiutian.bc.productExamineManagement.domain.examineImplementPlan.model.ExamineImplementPlan;
import cec.jiutian.bc.productExamineManagement.domain.examineYearlyPlan.model.ProductExamineYearlyPlan;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/6/24
 * @description TODO
 */
@Component
public class ExamineMaterialDynamicHandler implements DependFiled.DynamicHandler<ExamineImplementPlan> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public Map<String, Object> handle(ExamineImplementPlan examineImplementPlan) {
        Map<String, Object> result = new HashMap<>();
        ProductExamineYearlyPlan productExamineYearlyPlan = fabosJsonDao.getById(ProductExamineYearlyPlan.class, examineImplementPlan.getProductExamineYearlyPlan().getId());
        if (productExamineYearlyPlan != null) {
            SpecificationManageMTO manageMTO = fabosJsonDao.getById(SpecificationManageMTO.class, productExamineYearlyPlan.getMaterialId());
            if (manageMTO != null) {
                result.put("material",manageMTO);
                result.put("materialId",manageMTO.getId());
                result.put("materialName",manageMTO.getName());
            }
        }
        return result;
    }
}
