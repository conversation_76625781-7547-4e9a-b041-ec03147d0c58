package cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.issueList.handler;

import cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.issueList.model.IssueList;
import cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.issueList.model.IssueListANA;
import cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.issueList.model.IssueListANACorrection;
import cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.issueList.model.IssueListCorrection;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.common.util.BeanUtils;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.core.frame.module.MetaUser;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class IssueListANAHandler implements OperationHandler<IssueList, IssueListANA> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<IssueList> data, IssueListANA modelObject, String[] param) {
        if (CollectionUtils.isEmpty(data)) {
            return "msg.success('操作成功')";
        }
        IssueList issueList = data.get(0);
        if (CollectionUtils.isNotEmpty(modelObject.getIssueListANACorrections())) {
            List<IssueListCorrection> corrections = new ArrayList<>();

            modelObject.getIssueListANACorrections().forEach(d -> {
                IssueListCorrection correction = new IssueListCorrection();
                BeanUtils.copyProperties(d, correction);

                corrections.add(correction);
            });

            issueList.setIssueListCorrections(corrections);
        }
        issueList.setAllUserIds(modelObject.getIssueListANACorrections().stream()
                .map(IssueListANACorrection::getMetaUser)
                .map(MetaUser::getId)
                .distinct()
                .collect(Collectors.joining(",")));
        issueList.setCauseAnalysis(modelObject.getCauseAnalysis());
        fabosJsonDao.mergeAndFlush(issueList);

        return "msg.success('操作成功')";
    }

    @Override
    public IssueListANA fabosJsonFormValue(List<IssueList> data, IssueListANA fabosJsonForm, String[] param) {
        // data为空 直接返回
        if (CollectionUtils.isEmpty(data)) {
            return fabosJsonForm;
        }

        String userId = UserContext.getUserId();
        IssueList issueList = data.get(0);
        fabosJsonForm.setCauseAnalysis(issueList.getCauseAnalysis());

        //如果list为空 直接返回
        if (CollectionUtils.isEmpty(issueList.getIssueListCorrections())) {
            return fabosJsonForm;
        }

        List<IssueListANACorrection> anaCorrections = new ArrayList<>();
        issueList.getIssueListCorrections().forEach(d->{
            IssueListANACorrection correction = new IssueListANACorrection();
            BeanUtils.copyProperties(d, correction);
            anaCorrections.add(correction);
        });

        fabosJsonForm.setIssueListANACorrections(anaCorrections);
        return fabosJsonForm;
    }
}
