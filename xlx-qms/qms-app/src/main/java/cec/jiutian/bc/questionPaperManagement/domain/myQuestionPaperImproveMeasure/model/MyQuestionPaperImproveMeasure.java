package cec.jiutian.bc.questionPaperManagement.domain.myQuestionPaperImproveMeasure.model;

import cec.jiutian.bc.questionPaperManagement.domain.myQuestionPaperImproveMeasure.handler.MyQuestionImproveMeasureExecuteOperationHandler;
import cec.jiutian.bc.questionPaperManagement.domain.myQuestionPaperImproveMeasure.proxy.MyQuestionPaperImproveMeasureDataProxy;
import cec.jiutian.bc.questionPaperManagement.domain.questionPaper.model.QuestionPaper;
import cec.jiutian.bc.questionPaperManagement.enums.QuestionPaperMeasureCheckEnum;
import cec.jiutian.bc.questionPaperManagement.enums.QuestionPaperMeasureProgressEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.AttachmentType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DateType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

@FabosJson(
        name = "我的改善措施",
        orderBy = "MyQuestionPaperImproveMeasure.questionPaper.id desc",
        dataProxy = MyQuestionPaperImproveMeasureDataProxy.class,
        power = @Power(add = false, edit = false, delete = false, export = false),
        rowOperation = {
                @RowOperation(
                        title = "执行",
                        code = "MyQuestionPaperImproveMeasure@EXECUTE",
                        operationHandler = MyQuestionImproveMeasureExecuteOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "是否确定操作？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "MyQuestionPaperImproveMeasure@EXECUTE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "progress != 'WaitExecute'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "提交",
                        code = "MyQuestionPaperImproveMeasure@SUBMIT",
                        mode = RowOperation.Mode.SINGLE,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.UPDATE,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = MyQuestionPaperImproveMeasureSubmit.class,
                        ifExpr = "progress != 'Execute'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "MyQuestionPaperImproveMeasure@SUBMIT"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
        }
)
@Table(name = "qpm_question_paper_improve_measure")
@Entity
@Getter
@Setter
public class MyQuestionPaperImproveMeasure extends MetaModel {

    @ManyToOne
    @FabosJsonField(
            views = {
                    @View(title = "问题一页纸", column = "generalCode", show = false)
            },
            edit = @Edit(title = "问题一页纸", type = EditType.REFERENCE_TABLE, show = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode")
            )
    )
    @JsonIgnoreProperties("improveMeasures")
    private QuestionPaper questionPaper;

    @Transient
    @FabosJsonField(
            views = @View(title = "问题编号"),
            edit = @Edit(title = "问题编号")
    )
    private String questionNumber;

    @Transient
    @FabosJsonField(
            views = @View(title = "问题简述"),
            edit = @Edit(title = "问题简述")
    )
    private String questionDescription;

    @Transient
    @FabosJsonField(
            views = @View(title = "事件描述（5W2H）"),
            edit = @Edit(title = "事件描述（5W2H）")
    )
    private String eventDescription;

    @Transient
    @FabosJsonField(
            views = @View(title = "原因分析（5why）"),
            edit = @Edit(title = "原因分析（5why）")
    )
    private String causeAnalysis;

    @FabosJsonField(
            views = @View(title = "改善措施"),
            edit = @Edit(title = "改善措施", search = @Search(vague = true))
    )
    private String measure;

    @FabosJsonField(
            views = @View(title = "突出/强调事项"),
            edit = @Edit(title = "突出/强调事项")
    )
    private String emphasizedMatter;

    @FabosJsonField(
            views = @View(title = "责任人ID", show = false),
            edit = @Edit(title = "责任人ID", show = false)
    )
    private String responsiblePersonId;

    @FabosJsonField(
            views = @View(title = "责任人"),
            edit = @Edit(title = "责任人")
    )
    private String responsiblePersonName;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "纳期", type = ViewType.DATE),
            edit = @Edit(title = "纳期", dateType = @DateType(type = DateType.Type.DATE))
    )
    private LocalDate deliveryDate;

    @FabosJsonField(
            views = @View(title = "进度"),
            edit = @Edit(title = "进度", search = @Search,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = QuestionPaperMeasureProgressEnum.class))
    )
    private String progress;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "完成日期", type = ViewType.DATE),
            edit = @Edit(title = "完成日期", dateType = @DateType(type = DateType.Type.DATE))
    )
    private LocalDate completeDate;

    @FabosJsonField(
            views = @View(title = "完成佐证材料"),
            edit = @Edit(title = "完成佐证材料", type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(maxLimit = 10))
    )
    private String completeAttachment;

    @FabosJsonField(
            views = @View(title = "验证结果"),
            edit = @Edit(title = "验证结果",
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = QuestionPaperMeasureCheckEnum.class))
    )
    private String checkResult;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "验证日期", type = ViewType.DATE),
            edit = @Edit(title = "验证日期", dateType = @DateType(type = DateType.Type.DATE))
    )
    private LocalDate checkDate;

    @FabosJsonField(
            views = @View(title = "验证佐证材料"),
            edit = @Edit(title = "验证佐证材料", type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(maxLimit = 10))
    )
    private String checkAttachment;


}
