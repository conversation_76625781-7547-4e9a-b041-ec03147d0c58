package cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.mto;

import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.enumration.ProgressEnum;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.handler.CorrectPreventMeasureDynamicHandler;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.proxy.CorrectPreventMeasureCauseAnalyMTOProxy;
import cec.jiutian.bc.urm.domain.user.entity.User;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/28
 * @description TODO
 */
@FabosJson(
        name = "原因分析",
        dataProxy = CorrectPreventMeasureCauseAnalyMTOProxy.class
)
@Table(name = "qms_cpm_correct_prevent_measure")
@Entity
@Getter
@Setter
public class CorrectPreventMeasureCauseAnalyMTO extends MetaModel {
    @FabosJsonField(
            views = @View(title = "单据编号", show = false),
            edit = @Edit(title = "单据编号",
                    readonly = @Readonly(edit = false),
                    show = false
            ),
            dynamicField = @DynamicField(dependFiled = @DependFiled(buttonName = "生成", changeBy = "id",
                    dynamicHandler = CorrectPreventMeasureDynamicHandler.class))
    )
    private String correctPreventMeasureFormNumber;

    @FabosJsonField(
            views = @View(title = "不合格（潜在）根本原因分析（5why分析法）"),
            edit = @Edit(title = "不合格（潜在）根本原因分析（5why分析法）",
                    type = EditType.TEXTAREA)
    )
    private String rootCauseAnalysis;

    @FabosJsonField(
            views = @View(title = "原因分析附件"),
            edit = @Edit(title = "原因分析附件", type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType)
    )
    private String causeAnalysisAttachments;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "分析人", column = "name"),
            edit = @Edit(title = "分析人",
                    type = EditType.REFERENCE_TABLE,
                    search = @Search(),
                    show = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name")
            )
    )
    private User analyticalUser;

    @FabosJsonField(
            views = @View(title = "分析时间"),
            edit = @Edit(title = "分析时间",
                    dateType = @DateType(type = DateType.Type.DATE_TIME),
                    show = false
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date analyticalTime;

    @FabosJsonField(
            views = @View(title = "整改任务下所有责任人的id集合,我的整改任务查询数据使用", show = false),
            edit = @Edit(title = "整改任务下所有责任人的id集合,我的整改任务查询数据使用",show = false)
    )
    private String preventAllUserIds;

    @FabosJsonField(
            edit = @Edit(title = "预防措施", type = EditType.TAB_TABLE_ADD),
            views = @View(title = "预防措施", type= ViewType.TABLE_VIEW)
    )
    @JoinColumn(name = "qms_cpm_correct_prevent_measure_id")
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy
    private List<PreventMeasureCreationMTO> preventMeasureList;

    @FabosJsonField(
            views = @View(title = "分析状态", show = false),
            edit = @Edit(title = "分析状态",
                    readonly = @Readonly(),
                    type = EditType.CHOICE,
                    show = false,
                    choiceType = @ChoiceType(fetchHandler = ProgressEnum.class)
            )
    )
    private String analyticalState;

}
