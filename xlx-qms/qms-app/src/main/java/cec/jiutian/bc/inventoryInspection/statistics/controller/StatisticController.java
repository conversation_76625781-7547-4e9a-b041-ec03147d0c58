package cec.jiutian.bc.inventoryInspection.statistics.controller;


import cec.jiutian.bc.ao.QueryAO;
import cec.jiutian.bc.dto.ChartData;
import cec.jiutian.bc.inventoryInspection.statistics.service.ParetoService;
import cec.jiutian.bc.inventoryInspection.statistics.service.PassRateService;
import cec.jiutian.bc.inventoryInspection.statistics.service.YieldRateService;
import cec.jiutian.bc.inventoryInspection.statistics.vo.YieldRateVO;
import cec.jiutian.core.frame.module.R;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/fabos-qms/data-provider")
public class StatisticController {

    @Resource
    private  YieldRateService yieldRateService;

    @Resource
    private PassRateService passRateService;

    @Resource
    private ParetoService paretoService;


    @RequestMapping("/yield-rate")
    public R<YieldRateVO> getQualityStats(@RequestBody QueryAO queryAO) {
        YieldRateVO yieldRateVO = yieldRateService.getQualityChartData(queryAO);
        yieldRateVO.setCustomData();
        return R.ok(yieldRateVO);
    }

    @RequestMapping("/line-pass-rate")
    public R<ChartData> queryLinePassRateData(@RequestBody QueryAO queryAO) {
        ChartData chartData = passRateService.getLinePassRateChartData(queryAO);
        return R.ok(chartData);
    }

    @RequestMapping("/workshop-pass-rate")
    public R<ChartData> queryWorkshopPassRateData(@RequestBody QueryAO queryAO) {
        ChartData chartData = passRateService.getWorkshopPassRateChartData(queryAO);
        return R.ok(chartData);
    }

    @RequestMapping("/factory-pass-rate")
    public R<ChartData> queryFactoryPassRateData(@RequestBody QueryAO queryAO) {
        ChartData chartData = passRateService.getFactoryPassRateChartData(queryAO);
        return R.ok(chartData);
    }

    @RequestMapping("/category-pass-rate")
    public R<ChartData> queryCategoryPassRateData(@RequestBody QueryAO queryAO) {
        ChartData chartData = passRateService.getCategoryPassRateChartData(queryAO);
        return R.ok(chartData);
    }

    @RequestMapping("/code-pass-rate")
    public R<ChartData> queryCodePassRateData(@RequestBody QueryAO queryAO) {
        ChartData chartData = passRateService.getCodePassRateChartData(queryAO);
        return R.ok(chartData);
    }
    @RequestMapping("/ins-item-pass-rate")
    public R<ChartData> queryInsItemPassRateData(@RequestBody QueryAO queryAO) {
        ChartData chartData = passRateService.getInsItemPassRateChartData(queryAO);
        return R.ok(chartData);
    }

    @RequestMapping("/ins-item-pareto")
    public R<ChartData> queryInsItemPareto(@RequestBody QueryAO queryAO) {
        ChartData chartData = paretoService.getParetoOfInsItem(queryAO);
        return R.ok(chartData);
    }
    @RequestMapping("/material-code-pareto")
    public R<ChartData> queryMaterialCodePareto(@RequestBody QueryAO queryAO) {
        ChartData chartData = paretoService.getParetoOfMaterialCode(queryAO);
        return R.ok(chartData);
    }


}
