package cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.mto;

import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.Search;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025/7/16
 * @description TODO
 */
@FabosJson(
        name = "开线任务单MTO"
)
@Entity
@Getter
@Setter
@Table(name = "pqt_machine_startup_wire_change_view")
public class MachineStartupWireChangeMTO {
    @Id
    @FabosJsonField(
            edit = @Edit(title = "", show = false)
    )
    private String id;

    @FabosJsonField(
            views = @View(title = "单据名称"),
            edit = @Edit(title = "单据名称")
    )
    private String name;

    @FabosJsonField(
            views = @View(title = "车间ID", show = false),
            edit = @Edit(title = "车间ID", show = false)
    )
    private String workshopId;

    @FabosJsonField(
            views = @View(title = "车间名称", show = false),
            edit = @Edit(title = "车间名称", show = false)
    )
    private String workshopName;

    @FabosJsonField(
            views = @View(title = "产线Id", show = false),
            edit = @Edit(title = "产线Id", show = false)
    )
    private String productionLineId;

    @FabosJsonField(
            views = @View(title = "产线名称", show = false),
            edit = @Edit(title = "产线名称", show = false)
    )
    private String productionLineName;

    @FabosJsonField(
            views = @View(title = "物料编码"),
            edit = @Edit(title = "物料编码", notNull = true, search = @Search(vague = true))
    )
    private String materialCode;

    @FabosJsonField(
            views = @View(title = "物料名称"),
            edit = @Edit(title = "物料名称", notNull = true,search = @Search(vague = true))
    )
    private String materialName;

    @FabosJsonField(
            views = @View(title = "物料类型"),
            edit = @Edit(title = "物料类型", readonly = @Readonly)
    )
    private String materialType;

    @FabosJsonField(
            views = @View(title = "单据类型", show = false),
            edit = @Edit(title = "单据类型", show = false)
    )
    private String type;
}
