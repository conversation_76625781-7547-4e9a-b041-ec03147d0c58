package cec.jiutian.bc.questionPaperManagement.domain.myQuestionPaperInterimMeasure.proxy;

import cec.jiutian.bc.questionPaperManagement.domain.myQuestionPaperInterimMeasure.model.MyQuestionPaperInterimMeasureSubmit;
import cec.jiutian.bc.questionPaperManagement.enums.QuestionPaperMeasureProgressEnum;
import cec.jiutian.view.fun.DataProxy;
import org.springframework.stereotype.Component;

@Component
public class MyQuestionPaperInterimMeasureSubmitDataProxy implements DataProxy<MyQuestionPaperInterimMeasureSubmit> {

    @Override
    public void beforeUpdate(MyQuestionPaperInterimMeasureSubmit entity) {
        entity.setProgress(QuestionPaperMeasureProgressEnum.Enum.WaitCheck.name());
    }

}
