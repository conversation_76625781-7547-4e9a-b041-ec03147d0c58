package cec.jiutian.bc.productExamineManagement.enumration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/16
 * @description TODO
 */
public class ExamineImplementStateEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum data : Enum.values()) {
            list.add(new VLModel(data.name(), data.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        Edit("新建"),
        Publish("发布"),
        Cancel("取消"),
        Inspecting("检验中"),
        Examine("待审核"),
        Examined("审核完成"),
        ;

        private final String value;

    }
}
