package cec.jiutian.bc.qualityExceptionManagement.domain.returnProductHandling.model;

import cec.jiutian.bc.modeler.enumration.InspectionValueTypeEnum;
import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@FabosJson(
        name = "退货产品处理-检验项指标"
)
@Table(name = "qem_return_product_handling_item")
@Entity
@Getter
@Setter
public class ReturnProductHandlingItem extends BaseModel {

    @FabosJsonField(
            views = {
                    @View(title = "检验项编码", column = "name")
            },
            edit = @Edit(title = "检验项", type = EditType.REFERENCE_TABLE, show = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"))
    )
    @ManyToOne
    @JsonIgnoreProperties("items")
    private ReturnProductHandlingDetail detail;

    @FabosJsonField(
            views = @View(title = "检验项指标名称"),
            edit = @Edit(title = "检验项指标名称", search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    private String name;

    @FabosJsonField(
            views = @View(title = "值类型"),
            edit = @Edit(title = "值类型", type = EditType.CHOICE, search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = InspectionValueTypeEnum.class))
    )
    private String inspectionValueType;

    @FabosJsonField(
            views = @View(title = "单位"),
            edit = @Edit(title = "单位",
                    inputType = @InputType(length = 40))
    )
    private String unit;

    @FabosJsonField(
            views = @View(title = "标准值"),
            edit = @Edit(title = "标准值", notNull = true)
    )
    private String standardValue;

    @FabosJsonField(
            views = @View(title = "上限值"),
            edit = @Edit(title = "上限值", notNull = true,
                    numberType = @NumberType(min = 0, max = 9999999, precision = 2))
    )
    private Double UpperValue;

    @FabosJsonField(
            views = @View(title = "是否包含上限值"),
            edit = @Edit(title = "是否包含上限值", defaultVal = "true"
            )
    )
    private Boolean isContainUpper;

    @FabosJsonField(
            views = @View(title = "下限值"),
            edit = @Edit(title = "下限值", notNull = true,
                    numberType = @NumberType(min = 0, max = 9999999, precision = 2))
    )
    private Double lowerValue;

    @FabosJsonField(
            views = @View(title = "是否包含下限值"),
            edit = @Edit(title = "是否包含下限值", defaultVal = "true"
            )
    )
    private Boolean isContainLower;

    @FabosJsonField(
            views = @View(title = "检验结果值"),
            edit = @Edit(title = "检验结果值")
    )
    private String resultValue;

    @FabosJsonField(
            views = @View(title = "描述"),
            edit = @Edit(title = "描述",
                    type = EditType.TEXTAREA)
    )
    private String description;
}
