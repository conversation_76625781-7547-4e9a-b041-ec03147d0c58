package cec.jiutian.bc.accidentReportManagement.domain.armMyTempMeasureTask.proxy;

import cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.model.ArmTemporaryMeasure;
import cec.jiutian.bc.accidentReportManagement.domain.armMyTempMeasureTask.model.ArmMyTempMeasureTask;
import cec.jiutian.bc.accidentReportManagement.enumeration.ArmImprovingProgressEnum;
import cec.jiutian.bc.materialInspect.domain.publicInspectionTask.mto.UserForInsTaskMTO;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import cec.jiutian.view.query.Condition;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

@Component
public class ArmMyTempMeasureTaskProxy implements DataProxy<ArmMyTempMeasureTask> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String beforeFetch(List<Condition> conditions) {
        // 查询当前用户
        String userId = UserContext.getUserId();
        return "ArmMyTempMeasureTask.tempUserIds like '%"+userId+"%'";
    }

    @Override
    public void afterFetch(Collection<Map<String, Object>> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        list.forEach(d -> {
            ArmMyTempMeasureTask temp = fabosJsonDao.findById(ArmMyTempMeasureTask.class, d.get("id").toString());
            int operateFlag = 0;
            for (ArmTemporaryMeasure t : temp.getArmTemporaryMeasureList()) {
                // 不是当前用户 跳过
                if (!UserContext.getUserId().equals(t.getUserForInsTaskMTO().getId())) {
                    continue;
                }
                // 只要前用户的状态 出现待执行 则显示按钮
                if (!ArmImprovingProgressEnum.Enum.TO_BE_VERIFIED.name().equals(t.getImprovingProgress())) {
                    operateFlag = 1;
                    break;
                }
            }
            d.put("rowOperationAuthFlag", operateFlag);
        });
    }

    @Override
    public void afterSingleFetch(Map<String, Object> map) {
        List<HashMap<String,Object>> list = (List<HashMap<String,Object>>) map.get("armTemporaryMeasureList");
        List<HashMap<String,Object>> result = new ArrayList<>();
        for (HashMap<String, Object> data : list) {
            String responsiblePersonId = ((HashMap) data.get("userForInsTaskMTO")).get("id").toString();
            if (responsiblePersonId.equals(UserContext.getUserId())) {
                result.add(data);
            }
        }
        map.put("armTemporaryMeasureList", result);
    }
}
