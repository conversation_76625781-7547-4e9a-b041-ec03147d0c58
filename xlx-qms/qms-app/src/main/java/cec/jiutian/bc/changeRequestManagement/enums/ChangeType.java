package cec.jiutian.bc.changeRequestManagement.enums;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

public class ChangeType implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum data : Enum.values()) {
            list.add(new VLModel(data.name(), data.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {

        MATERIAL("原材料变更"),
        LINE("产线变更"),
        TECHNOLOGY("工艺变更"),
        EQUIPMENT("设备变更"),
        TEST("测试变更"),
        SITE("场地变更"),
        PERSON("人员变更")
        ;

        private final String value;

    }
}
