package cec.jiutian.bc.changeRequestManagement.domain.ecn.handler;

import cec.jiutian.bc.changeRequestManagement.domain.bo.FlowAssigneeConfig;
import cec.jiutian.bc.changeRequestManagement.domain.bo.FlowNodeBO;
import cec.jiutian.bc.changeRequestManagement.domain.ecn.model.ChangeRequestExecute;
import cec.jiutian.bc.changeRequestManagement.domain.ecn.model.ECNItem;
import cec.jiutian.bc.changeRequestManagement.domain.ecn.model.VerificationIDetail;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.model.ApproveTask;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.repository.ApproveTaskRepository;
import cec.jiutian.bc.changeRequestManagement.enums.ECRStatusEnum;
import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import jakarta.persistence.LockModeType;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Component
public class ECNVerifyHandler implements OperationHandler<ChangeRequestExecute, VerificationIDetail> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private FlowAssigneeConfig flowAssigneeConfig;

    @Resource
    private ApproveTaskRepository approveTaskRepository;

    @Transactional
    @Override
    public String exec(List<ChangeRequestExecute> data, VerificationIDetail modelObject, String[] param) {
        if (CollectionUtils.isEmpty(data) || data.get(0).getId() == null) {
            throw new ServiceException("数据异常");
        }
        Date changeDate = modelObject.getChangeDate();
        Date executeDate = modelObject.getExecuteDate();
        if (changeDate == null || executeDate == null) {
            throw new ServiceException("请选择变更切换日期和计划变更执行日期");
        }
        if (changeDate.after(executeDate)) {
            throw new ServiceException("变更切换日期不能晚于计划变更执行日期");
        }
        ChangeRequestExecute ecn = fabosJsonDao.getEntityManager().find(ChangeRequestExecute.class, data.get(0).getId(), LockModeType.PESSIMISTIC_WRITE);
        if (ECRStatusEnum.Enum.WAIT_CHANGE_EXECUTE.name().equals(ecn.getStatus())) {
            throw new ServiceException("该请求已提交批准，请刷新页面");
        }
        ecn.setStatus(ECRStatusEnum.Enum.WAIT_CHANGE_EXECUTE.name());
        ecn.setVerificationIDetail(modelObject);
        fabosJsonDao.mergeAndFlush(ecn);

        //创建审批任务
        Integer maxTurn = approveTaskRepository.findMaxTurnByBusinessKeyAndCode(ecn.getId(), FlowNodeBO.Node.VERIFY_REVIEW.name());
        if (maxTurn == null) {
            maxTurn = 0;
        }else {
            maxTurn++;
        }

        List<String> roleIds = flowAssigneeConfig.getAssigneeIds(FlowNodeBO.Node.VERIFY_REVIEW.name());
        for (String roleId : roleIds) {
            ApproveTask approveTask = ApproveTask.createVerifyTask(ecn.getId(), roleId, maxTurn);
            fabosJsonDao.persistAndFlush(approveTask);
        }

        // 将所有 变更事项 负责人id 保持
        ChangeRequestExecute cre = data.get(0);
        if (CollectionUtils.isNotEmpty(cre.getECNItems())) {
            String supplementUserIds = ecn.getECNItems().stream().map(ECNItem::getResponsiblePersonId).distinct()
                    .collect(Collectors.joining(","));
            String orgAllUserIds = cre.getAllUserIds();
            String allUserIds = Stream.concat(Arrays.stream(orgAllUserIds.split(",")),
                    ecn.getECNItems().stream().map(ECNItem::getResponsiblePersonId).distinct())
                    .distinct()
                    .collect(Collectors.joining(","));
            ecn.setAllUserIds(allUserIds);
            fabosJsonDao.mergeAndFlush(ecn);
        }
        return "提交成功";
    }

    @Resource
    private NamingRuleService namingRuleService;

    @Override
    public VerificationIDetail fabosJsonFormValue(List<ChangeRequestExecute> data, VerificationIDetail fabosJsonForm, String[] param) {
        if (CollectionUtils.isEmpty(data) || StringUtils.isEmpty(data.get(0).getId())) {
            throw new ServiceException("选择数据为空");
        }
        fabosJsonForm.setEcnId(data.get(0).getId());
        fabosJsonForm.setGeneralCode(namingRuleService.getNameCode(VerificationIDetail.class.getSimpleName(), 1, null).get(0));
        fabosJsonForm.setChangeDate(new Date());
        fabosJsonForm.setExecuteDate(new Date());
        return fabosJsonForm;
    }
}
