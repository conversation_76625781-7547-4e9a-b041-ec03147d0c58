package cec.jiutian.bc.inventoryInspection.statistics.repository;

import cec.jiutian.bc.mto.FactoryArea;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface FactoryAreaRepository extends JpaRepository<FactoryArea, Long> {

    List<FactoryArea> findByFactoryAreaTypeCode(String factoryAreaTypeCode);

    List<FactoryArea> findByPidInOrderById(List<Long> pids);
}