package cec.jiutian.bc.systemAuditManage.domain.auditAnnualPlan.remote.provider;

import cec.jiutian.bc.ecs.dto.SendMsgGroupDTO;
import cec.jiutian.bc.ecs.provider.EcsMessageProvider;
import cec.jiutian.bc.job.provider.IJobProvider;
import cec.jiutian.bc.systemAuditManage.domain.auditAnnualPlan.model.AuditAnnualPlan;
import cec.jiutian.bc.systemAuditManage.enumeration.AuditAnnualPlanStatusEnum;
import cec.jiutian.core.frame.annotation.FabosCustomizedService;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.meta.FabosJob;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import jakarta.annotation.Resource;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@FabosCustomizedService(value = AuditAnnualPlan.class)
@Slf4j
@Component
@Transactional
public class AuditAnnualPlanScheduleProvider implements IJobProvider {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private EcsMessageProvider ecsMessageProvider;

    @FabosJob(comment = "审核年度计划定时任务")
    @Override
    public String exec(String code, String param) {
        AuditAnnualPlan auditAnnualPlan = new AuditAnnualPlan();
        auditAnnualPlan.setStatus(AuditAnnualPlanStatusEnum.Enum.VERIFY.name());

        List<AuditAnnualPlan> planList = fabosJsonDao.select(auditAnnualPlan);
        planList.forEach(plan -> {
            if (checkDate(plan.getPlanDate())) {
                sendQms(plan);
            }
        });
        return "";
    }

    private boolean checkDate(Date planDate) {
        DateTime nowDate = DateUtil.date();
        long betweenDays = DateUtil.between(planDate, nowDate, DateUnit.DAY, false);
        if (betweenDays <=30 && nowDate.before(planDate)) {
            return true;
        }
        return false;
    }

    private void sendQms(AuditAnnualPlan plan) {
        SendMsgGroupDTO sendMsgGroupDTO = new SendMsgGroupDTO();
        sendMsgGroupDTO.setMessageGroupCode("AuditAnnualPlanOverTimeInfo");
        sendMsgGroupDTO.setContent("请尽快完成审核年度计划["+plan.getAuditPlanName()+"]的处理, 计划时间是["+plan.getPlanDate()+"]");
        ecsMessageProvider.sendGroupMessage(sendMsgGroupDTO);
    }
}
