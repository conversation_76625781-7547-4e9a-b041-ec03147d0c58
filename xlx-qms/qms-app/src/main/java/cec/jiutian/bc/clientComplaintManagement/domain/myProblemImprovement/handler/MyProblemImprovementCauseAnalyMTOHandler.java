package cec.jiutian.bc.clientComplaintManagement.domain.myProblemImprovement.handler;

import cec.jiutian.bc.clientComplaintManagement.domain.myProblemImprovement.model.MyProblemImprovement;
import cec.jiutian.bc.clientComplaintManagement.domain.myProblemImprovement.mto.MyProblemImprovementCauseAnalyMTO;
import cec.jiutian.bc.clientComplaintManagement.enumeration.ProblemImprovementStatusEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/28
 * @description TODO
 */
@Component
public class MyProblemImprovementCauseAnalyMTOHand<PERSON> implements OperationHandler<MyProblemImprovement, MyProblemImprovementCauseAnalyMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<MyProblemImprovement> data, MyProblemImprovementCauseAnalyMTO modelObject, String[] param) {
        if (CollectionUtils.isEmpty(data) || data.get(0) == null) {
            return "alert(行数据选择异常)";
        }
        modelObject.setBusinessStatus(ProblemImprovementStatusEnum.Enum.EXECUTING.name());

        MyProblemImprovement model = data.get(0);
        BeanUtil.copyProperties(modelObject, model, "createBy", "createTime");
        fabosJsonDao.mergeAndFlush(model);
        return "alert(操作成功)";
    }

    @Override
    public MyProblemImprovementCauseAnalyMTO fabosJsonFormValue(List<MyProblemImprovement> data, MyProblemImprovementCauseAnalyMTO fabosJsonForm, String[] param) {
        MyProblemImprovement model = data.get(0);
        BeanUtil.copyProperties(model, fabosJsonForm);
        return fabosJsonForm;
    }
}
