package cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.model;

import cec.jiutian.bc.accidentReportManagement.enumeration.ArmImprovingProgressEnum;
import cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.enumration.ArmVerificationResultEnum;
import cec.jiutian.bc.accidentReportManagement.enumeration.ArmProgressEnum;
import cec.jiutian.bc.materialInspect.domain.publicInspectionTask.mto.UserForInsTaskMTO;
import cec.jiutian.bc.urm.domain.user.entity.User;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/5/29
 * @description
 */
@FabosJson(
        name = "预防措施",
        orderBy = "PreventMeasure.createTime desc"
)
@Table(name = "qms_arm_prevent_measure")
@Entity
@Data
public class ArmPreventMeasure extends MetaModel {
//    @ManyToOne
//    @FabosJsonField(
//            views = {
//                    @View(title = "事故报告任务", column = "generalCode", show = false)
//            },
//            edit = @Edit(title = "事故报告任务", type = EditType.REFERENCE_TABLE, show = false,
//                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode")
//            )
//    )
//    @JsonIgnoreProperties("armPreventMeasureList")
//    private AccidentReportTask accidentReportTask;

    @FabosJsonField(
            views = @View(title = "预防措施"),
            edit = @Edit(title = "预防措施", notNull = true)
    )
    private String preventiveMeasure;;

    @FabosJsonField(
            views = @View(title = "责任人", column = "name"),
            edit = @Edit(title = "责任人",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType(type = TabTableReferType.SelectShowTypeMTM.LIST)
            )
    )
    @ManyToOne
    @JoinColumn(name = "responsible_person_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private UserForInsTaskMTO userForInsTaskMTO;

    @FabosJsonField(
            views = @View(title = "纳期"),
            edit = @Edit(title = "纳期",
                    dateType = @DateType(type = DateType.Type.DATE_TIME),
                    notNull = true
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date deliveryTime;

    @FabosJsonField(
            views = @View(title = "改善进度"),
            edit = @Edit(title = "改善进度",
                    readonly = @Readonly(),
                    type = EditType.CHOICE,
                    show = false,
                    choiceType = @ChoiceType(fetchHandler = ArmImprovingProgressEnum.class)
            )
    )
    private String improvingProgress;

    //是否完成
    @FabosJsonField(
            views = @View(title = "是否完成"),
            edit = @Edit(title = "是否完成",
                    inputType = @InputType(length = 20),
                    defaultVal = "WAIT_RUN",
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ArmProgressEnum.class))
    )
    @Column(length = 20)
    private String progress;

    @FabosJsonField(
            views = @View(title = "完成日期"),
            edit = @Edit(title = "完成日期",
                    readonly = @Readonly(),
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date completionDate;

    @FabosJsonField(
            views = @View(title = "完成佐证材料"),
            edit = @Edit(title = "完成佐证材料", type = EditType.ATTACHMENT,
                    readonly = @Readonly(),
                    attachmentType = @AttachmentType)
    )
    private String completeSupportMaterial;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "验证人", column = "name"),
            edit = @Edit(title = "验证人",
                    type = EditType.REFERENCE_TABLE,
                    search = @Search(),
                    show = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name")
            )
    )
    private User verifiedUser;

    @FabosJsonField(
            views = @View(title = "验证结果"),
            edit = @Edit(title = "验证结果",
                    type = EditType.CHOICE,
                    notNull = true,
                    choiceType = @ChoiceType(fetchHandler = ArmVerificationResultEnum.class)
            )
    )
    private String verificationResult;

    @FabosJsonField(
            views = @View(title = "验证日期"),
            edit = @Edit(title = "验证日期",
                    notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date verificationDate;

    @FabosJsonField(
            views = @View(title = "验证佐证材料"),
            edit = @Edit(title = "验证佐证材料",
                    type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType)
    )
    private String verificationSupportMaterial;
}
