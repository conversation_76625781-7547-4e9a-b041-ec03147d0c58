package cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.handler;

import cec.jiutian.bc.basicData.enumeration.NamingRuleCodeEnum;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.mto.CorrectPreventMeasureCreateMTO;
import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/4/28
 * @description TODO
 */
@Component
public class CorrectPreventMeasureDynamicHandler implements DependFiled.DynamicHandler<CorrectPreventMeasureCreateMTO> {

    @Resource
    private NamingRuleService namingRuleService;

    @Override
    public Map<String, Object> handle(CorrectPreventMeasureCreateMTO inspectionInstrument) {
        Map<String, Object> map = new HashMap<>();
        List<String> result = namingRuleService.getNameCode(NamingRuleCodeEnum.CORRECT_PREVENT_MEASURE.name(), 1, null);
        map.put("correctPreventMeasureFormNumber", result.get(0));
        return map;
    }
}
