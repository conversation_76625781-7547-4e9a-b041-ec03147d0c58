package cec.jiutian.bc.qualityExceptionManagement.enumeration.inspectionIssuesImproveAndTrace;

import cec.jiutian.bc.onSiteInspecton.enumeration.OnSiteInsResultEnum;
import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

public class InsResultEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        java.util.List<cec.jiutian.view.fun.VLModel> list = new ArrayList<>();
        for (Enum data : Enum.values()) {
            list.add(new cec.jiutian.view.fun.VLModel(data.name(), data.getValue()));
        }
        return list;
    }
    @Getter
    public enum Enum {
        //无异常、异常、不涉及
        NO_EXCEPTION("无异常"),
        EXCEPTION("异常"),
        NOT_RELATED("不涉及")
        ;

        private final String value;

        Enum(String value) {
            this.value = value;
        }
    }
}
