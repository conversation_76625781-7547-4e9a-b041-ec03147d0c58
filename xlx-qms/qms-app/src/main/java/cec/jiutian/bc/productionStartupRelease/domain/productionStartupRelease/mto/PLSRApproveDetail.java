package cec.jiutian.bc.productionStartupRelease.domain.productionStartupRelease.mto;

import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowBaseOperation;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@FabosJson(
        name = "审批详情",
        power = @Power(add = false, edit = false, delete = false, importable = false, export = false),
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "1 == 1",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.HIDE
                ),
                @RowBaseOperation(
                        code = "add",
                        ifExpr = "1 == 1",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.HIDE
                ),
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "1 == 1",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.HIDE
                )
        }
)
@TemplateType(type = "multiTable")
public class PLSRApproveDetail extends BaseModel {

    @FabosJsonField(
            views = @View(title = "生产放行单号", index = 0),
            edit = @Edit(title = "生产放行单号", readonly = @Readonly(),
                    notNull = true, search = @Search(vague = true), inputType = @InputType(length = 40), index = 0)
    )
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "审批进度", column = "taskName", type = ViewType.TABLE_VIEW),
            edit = @Edit(
                    readonly = @Readonly,
                    title = "审批进度",
                    type = EditType.TAB_TABLE_ADD,
                    allowAddMultipleRows = true,
                    referenceTableType = @ReferenceTableType(label = "taskName")
            )
    )
    private List<ApproveTaskMTO> approveTaskDetails;


}
