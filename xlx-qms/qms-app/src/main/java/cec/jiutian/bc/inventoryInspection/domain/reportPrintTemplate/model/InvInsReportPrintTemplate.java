package cec.jiutian.bc.inventoryInspection.domain.reportPrintTemplate.model;

import cec.jiutian.bc.inventoryInspection.domain.reportPrintTemplate.handler.InvViewDetailHandler;
import cec.jiutian.bc.inventoryInspection.domain.reportPrintTemplate.mto.InventoryInspectionTaskMTO;
import cec.jiutian.bc.inventoryInspection.domain.reportPrintTemplate.proxy.InvInsReportPrintTemplateDataProxy;
import cec.jiutian.bc.reportPrintTemplateSuper.ReportPrintTemplateSuper;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowOperation;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "mi_inspection_task")
@FabosJson(
        name = "库存检验报告",
        orderBy = "createTime desc",
        dataProxy = InvInsReportPrintTemplateDataProxy.class,
        rowOperation = {
                @RowOperation(
                        code = "InvInsReportPrintTemplate@LOOK",
                        title = "查看",
                        type = RowOperation.Type.POPUP,
                        mode = RowOperation.Mode.HEADER,
                        ifExpr = "selectedItems[0].businessState !='INSPECT_FINISH'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = InventoryInspectionTaskMTO.class,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        operationHandler = InvViewDetailHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "InvInsReportPrintTemplate@LOOK"
                        )
                )
        },
        filter = @Filter(value = "businessState = 'INSPECT_FINISH' and inspectionType = 'inventoryInspect'"),
        power = @Power(add = false, edit = false, delete = false, print = true, viewDetails = false)
)
public class InvInsReportPrintTemplate extends ReportPrintTemplateSuper {

}
