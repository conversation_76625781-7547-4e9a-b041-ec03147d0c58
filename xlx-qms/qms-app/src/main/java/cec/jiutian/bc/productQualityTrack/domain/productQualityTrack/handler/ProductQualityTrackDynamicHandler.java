package cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.handler;

import cec.jiutian.bc.basicData.enumeration.NamingRuleCodeEnum;
import cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.mto.ProductQualityTrackCreateMTO;
import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/7/15
 * @description
 */
@Component
public class ProductQualityTrackDynamicHandler implements DependFiled.DynamicHandler<ProductQualityTrackCreateMTO> {

    @Resource
    private NamingRuleService namingRuleService;

    @Override
    public Map<String, Object> handle(ProductQualityTrackCreateMTO inspectionInstrument) {
        Map<String, Object> map = new HashMap<>();
        List<String> result = namingRuleService.getNameCode(NamingRuleCodeEnum.PRODUCT_QUALITY_TRACK.name(), 1, null);
        map.put("generalCode", result.get(0));
        return map;
    }
}
