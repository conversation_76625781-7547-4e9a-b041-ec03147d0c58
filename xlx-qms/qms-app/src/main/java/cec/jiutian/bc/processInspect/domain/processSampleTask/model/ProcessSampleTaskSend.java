package cec.jiutian.bc.processInspect.domain.processSampleTask.model;

import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleBaseModel;
import cec.jiutian.bc.modeler.enumration.PackageTypeEnum;
import cec.jiutian.bc.modeler.enumration.SampleTypeEnum;
import cec.jiutian.bc.modeler.enumration.TaskBusinessStateEnum;
import cec.jiutian.bc.processInspect.domain.processSampleTask.handler.IPQCSampleTaskSendInfoDynamicHandler;
import cec.jiutian.bc.processInspect.domain.publicInspectionTask.mto.ProcessUserForInsTaskMTO;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DateType;
import cec.jiutian.view.field.edit.DependFieldDisplay;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OrderBy;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@FabosJson(
        name = "过程取样任务-送样（检验样）"
)
@Table(name = "pi_sample_task",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Getter
@Setter
public class ProcessSampleTaskSend extends NamingRuleBaseModel {

    @FabosJsonField(
            views = @View(title = "检验任务单号"),
            edit = @Edit(title = "检验任务单号", readonly = @Readonly),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "generalCode", dynamicHandler = IPQCSampleTaskSendInfoDynamicHandler.class))
    )
    private String inspectionTaskCode;

    @FabosJsonField(
            views = @View(title = "取样类型"),
            edit = @Edit(title = "取样类型", readonly = @Readonly, type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = SampleTypeEnum.class))
    )
    private String processInspectionType;

    @FabosJsonField(
            views = @View(title = "检验状态"),
            edit = @Edit(title = "检验状态", readonly = @Readonly, type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = TaskBusinessStateEnum.class))
    )
    private String businessState;

    @FabosJsonField(
            views = @View(title = "产品编码"),
            edit = @Edit(title = "产品编码", readonly = @Readonly)
    )
    private String productCode;

    @FabosJsonField(
            views = @View(title = "产品名称"),
            edit = @Edit(title = "产品名称", readonly = @Readonly)
    )
    private String productName;

    @FabosJsonField(
            views = @View(title = "工艺编码"),
            edit = @Edit(title = "工艺编码", readonly = @Readonly)
    )
    private String processCode;

    @FabosJsonField(
            views = @View(title = "工序编码"),
            edit = @Edit(title = "工序编码", readonly = @Readonly)
    )
    private String operationCode;

    @FabosJsonField(
            views = @View(title = "工序名称"),
            edit = @Edit(title = "工序名称", readonly = @Readonly)
    )
    private String operationName;

    @FabosJsonField(
            views = @View(title = "实际取样批号"),
            edit = @Edit(title = "实际取样批号", readonly = @Readonly)
    )
    private String actualLotSerialId;

    @FabosJsonField(
            views = @View(title = "取样数量"),
            edit = @Edit(title = "取样数量", readonly = @Readonly,
                    numberType = @NumberType(min = 0, max = 999999999, precision = 2))
    )
    private Double sampleQuantity;

    @FabosJsonField(
            views = @View(title = "样品单位"),
            edit = @Edit(title = "样品单位", readonly = @Readonly)
    )
    private String unit;

    @FabosJsonField(
            views = @View(title = "取样点"),
            edit = @Edit(title = "取样点",
                    readonly = @Readonly
            )
    )
    private String samplePoint;

    @FabosJsonField(
            views = @View(title = "送样点"),
            edit = @Edit(title = "送样点",
                    readonly = @Readonly
            )
    )
    private String sendPoint;

    @Transient
    @FabosJsonField(
            views = @View(title = "送样人", show = false, column = "name"),
            edit = @Edit(title = "送样人", notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType(),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "businessState != 'BE_SAMPLING'")
            )
    )
    private ProcessUserForInsTaskMTO user;

    @FabosJsonField(
            views = @View(title = "送样人id", show = false),
            edit = @Edit(title = "送样人id", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "user", beFilledBy = "id"))
    )
    private String sendSamplePersonId;

    @FabosJsonField(
            views = @View(title = "送样人"),
            edit = @Edit(title = "送样人", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "user", beFilledBy = "name"))
    )
    private String sendSamplePerson;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "送样日期", type = ViewType.DATE),
            edit = @Edit(title = "送样日期", notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE))
    )
    private Date sendSampleDate;

    @FabosJsonField(
            views = @View(title = "包装方式"),
            edit = @Edit(title = "包装方式", type = EditType.CHOICE, readonly = @Readonly,
                    choiceType = @ChoiceType(fetchHandler = PackageTypeEnum.class))
    )
    private String packageType;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "sample_task_id")
    @OrderBy
    @FabosJsonField(
            edit = @Edit(title = "项目详情", type = EditType.TAB_REFERENCE_GENERATE),
            views = @View(title = "项目详情", type = ViewType.TABLE_VIEW)
    )
    private List<ProcessSampleTaskDetail> details;
}
