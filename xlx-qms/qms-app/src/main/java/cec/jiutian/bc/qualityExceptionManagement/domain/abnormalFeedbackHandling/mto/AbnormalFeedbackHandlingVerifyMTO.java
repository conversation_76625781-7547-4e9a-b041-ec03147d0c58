package cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.mto;

import cec.jiutian.bc.mto.OrgMTO;
import cec.jiutian.bc.mto.ProcessFlowMTO;
import cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.enumration.*;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.*;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.*;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025/4/27
 * @description TODO
 */
@FabosJson(
        name = "核实"
)
@Entity
@Getter
@Setter
public class AbnormalFeedbackHandlingVerifyMTO extends MetaModel {

    @FabosJsonField(
            views = @View(title = "情况核实（初步原因分析）/意见"),
            edit = @Edit(title = "情况核实（初步原因分析）/意见",
                    notNull = true,
                    type = EditType.TEXTAREA)
    )
    private String verifyOpinions;

    @FabosJsonField(
            views = @View(title = "异常分类"),
            edit = @Edit(title = "异常分类",
                    type = EditType.CHOICE,
                    notNull = true,
                    search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = AnomalyClassificationEnum.class))
    )
    private String anomalyClassification;

    @FabosJsonField(
            views = @View(title = "分析方法"),
            edit = @Edit(title = "分析方法",
                    type = EditType.CHOICE,
                    search = @Search(),
                    notNull = true,
                    choiceType = @ChoiceType(fetchHandler = AnalysisMethodEnum.class))
    )
    private String analysisMethod;

    @FabosJsonField(
            views = @View(title = "不合格等级"),
            edit = @Edit(title = "不合格等级",
                    type = EditType.CHOICE,
                    search = @Search(),
                    notNull = true,
                    choiceType = @ChoiceType(fetchHandler = UnqualifiedLevelEnum.class))
    )
    private String unqualifiedLevel;

    @FabosJsonField(
            views = @View(title = "问题分析牵头部门", column = "name"),
            edit = @Edit(title = "问题分析牵头部门",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(type = TabTableReferType.SelectShowTypeMTM.LIST)
            )
    )
    @ManyToOne
    @JoinColumn(name = "lead_department_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private OrgMTO leadDepartment;

    @FabosJsonField(
            views = @View(title = "物料方案输出部门", column = "name"),
            edit = @Edit(title = "物料方案输出部门",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(type = TabTableReferType.SelectShowTypeMTM.LIST)
            )
    )
    @ManyToOne
    @JoinColumn(name = "output_department_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private OrgMTO outputDepartment;

    @Transient
    @FabosJsonField(
            views = @View(title = "工序流水号", column = "serialNumber", show = false),
            edit = @Edit(title = "工序流水号",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(type = TabTableReferType.SelectShowTypeMTM.LIST,
                            label = "serialNumber")
            )
    )
    private ProcessFlowMTO processFlowMTO;

    @FabosJsonField(
            views = @View(title = "工序流水号ID", show = false),
            edit = @Edit(title = "工序流水号ID",
                    show = false
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "processFlowMTO", beFilledBy = "id"))
    )
    @JoinColumn(name = "process_flow_id")
    private String processFlowMTOID;

    @FabosJsonField(
            views = @View(title = "工序流水号"),
            edit = @Edit(title = "工序流水号",
                    search = @Search(vague = true),
                    notNull = true,
                    readonly = @Readonly()
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "processFlowMTO", beFilledBy = "serialNumber"))
    )
    private String serialNumber;

    @FabosJsonField(
            views = @View(title = "工序编码", show = false),
            edit = @Edit(title = "工序编码",
                    show = false
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "processFlowMTO", beFilledBy = "processCode"))
    )
    private String processCode;

    @FabosJsonField(
            views = @View(title = "工序名称"),
            edit = @Edit(title = "工序名称",
                    search = @Search(vague = true),
                    notNull = true,
                    readonly = @Readonly()
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "processFlowMTO", beFilledBy = "processName"))
    )
    private String processName;

    @FabosJsonField(
            views = @View(title = "车间ID", show = false),
            edit = @Edit(title = "车间ID",
                    show = false
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "processFlowMTO", beFilledBy = "workshopId"))
    )
    private String workshopId;

    @FabosJsonField(
            views = @View(title = "车间名称"),
            edit = @Edit(title = "车间名称",
                    search = @Search(vague = true),
                    notNull = true,
                    readonly = @Readonly()
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "processFlowMTO", beFilledBy = "workshopName"))
    )
    private String workshopName;

    @FabosJsonField(
            views = @View(title = "产线ID", show = false),
            edit = @Edit(title = "产线ID",
                    show = false
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "processFlowMTO", beFilledBy = "productionLineId"))
    )
    private String productionLineId;

    @FabosJsonField(
            views = @View(title = "产线名称"),
            edit = @Edit(title = "产线名称",
                    search = @Search(vague = true),
                    notNull = true,
                    readonly = @Readonly()
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "processFlowMTO", beFilledBy = "productionLineName"))
    )
    private String productionLineName;

    @FabosJsonField(
            views = @View(title = "影响物料重量"),
            edit = @Edit(title = "影响物料重量",
                    notNull = true,
                    inputGroup = @InputGroup(postfix = "#{afhWeightUnit}"),
                    numberType = @NumberType(min = 0, max = 999999999, precision = 2))
    )
    private Double affectsMaterialWeight;

    @FabosJsonField(
            views = @View(title = "重量单位"),
            edit = @Edit(title = "重量单位",
                    type = EditType.CHOICE,
                    notNull = true,
                    search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = AfhWeightUnitEnum.class))
    )
    private String afhWeightUnit;

    @FabosJsonField(
            views = @View(title = "核实附件"),
            edit = @Edit(title = "核实附件", type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType)
    )
    private String verificationAttachments;

//    @Transient
//    @FabosJsonField(
//            views = @View(title = "选择直接责任部门", show = false, column = "name"),
//            edit = @Edit(title = "选择直接责任部门",
//                    type = EditType.REFERENCE_TABLE,
//                    allowAddMultipleRows = false,
//                    referenceTableType = @ReferenceTableType()
//            )
//    )
//    private OrgMTO orgMTO;
//
//    @FabosJsonField(
//            views = @View(title = "直接责任部门id", show = false),
//            edit = @Edit(title = "直接责任部门id", show = false),
//            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "orgMTO", beFilledBy = "id"))
//    )
//    private String directlyDepartmentID;
//
//    @FabosJsonField(
//            views = @View(title = "直接责任部门"),
//            edit = @Edit(title = "直接责任部门",
//                    notNull = true,
//                    readonly = @Readonly(),
//                    search = @Search(vague = true)),
//            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "orgMTO", beFilledBy = "name"))
//    )
//    private String directlyDepartmentName;
//
//    @Transient
//    @FabosJsonField(
//            views = @View(title = "选择核实人", show = false, column = "name"),
//            edit = @Edit(title = "选择核实人",
//                    type = EditType.REFERENCE_TABLE,
//                    allowAddMultipleRows = false,
//                    referenceTableType = @ReferenceTableType()
//            )
//    )
//    private UserForInsTaskMTO verificationPerson;
//
//    @FabosJsonField(
//            views = @View(title = "核实人ID", show = false),
//            edit = @Edit(title = "核实人ID",
//                    show = false,
//                    readonly = @Readonly(),
//                    search = @Search(vague = true)
//            ),
//            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "verificationPerson", beFilledBy = "id"))
//    )
//    private String verificationPersonId;
//
//    @FabosJsonField(
//            views = @View(title = "核实人"),
//            edit = @Edit(title = "核实人",
//                    readonly = @Readonly(),
//                    notNull = true,
//                    search = @Search(vague = true)
//            ),
//            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "verificationPerson", beFilledBy = "name"))
//    )
//    private String verificationPersonName;
//
//    @FabosJsonField(
//            views = @View(title = "核实时间"),
//            edit = @Edit(title = "核实时间",
//                    dateType = @DateType(type = DateType.Type.DATE_TIME),
//                    notNull = true
//            )
//    )
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
//    private Date verificationTime;
}
