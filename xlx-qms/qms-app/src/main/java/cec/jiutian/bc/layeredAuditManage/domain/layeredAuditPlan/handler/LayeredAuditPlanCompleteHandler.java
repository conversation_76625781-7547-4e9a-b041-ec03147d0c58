package cec.jiutian.bc.layeredAuditManage.domain.layeredAuditPlan.handler;

import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.layeredAuditManage.domain.issueImprove.model.IssueItemDetail;
import cec.jiutian.bc.layeredAuditManage.domain.issueImprove.model.LayeredIssueImprove;
import cec.jiutian.bc.layeredAuditManage.domain.layeredAuditPlan.model.ExecPlanDetail;
import cec.jiutian.bc.layeredAuditManage.domain.layeredAuditPlan.model.LayeredAuditPlan;
import cec.jiutian.bc.layeredAuditManage.domain.layeredAuditTemplate.model.LayeredAuditTemplate;
import cec.jiutian.bc.layeredAuditManage.enumeration.LayeredAuditPlanStatusEnum;
import cec.jiutian.bc.layeredAuditManage.enumeration.LayeredIssueImproveStatusEnum;
import cec.jiutian.bc.modeler.enumration.NamingRuleCodeEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component
public class LayeredAuditPlanCompleteHandler implements OperationHandler<LayeredAuditPlan, Void> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private NamingRuleService namingRuleService;

    @Override
    public String exec(List<LayeredAuditPlan> data, Void modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            LayeredAuditPlan layeredAuditPlan = data.get(0);
            //生成改善问题单
            createIssueImprove(layeredAuditPlan);

            //修改状态
            layeredAuditPlan.setStatus(LayeredAuditPlanStatusEnum.Enum.APPROVED.name());
            fabosJsonDao.mergeAndFlush(layeredAuditPlan);
        }
        return "msg.success('操作成功')";
    }

    private void createIssueImprove(LayeredAuditPlan layeredAuditPlan) {
        if (CollectionUtils.isNotEmpty(layeredAuditPlan.getExecPlanDetailList())) {
            for (ExecPlanDetail execPlanDetail : layeredAuditPlan.getExecPlanDetailList()) {
                LayeredIssueImprove layeredIssueImprove = new LayeredIssueImprove();
                layeredIssueImprove.setGeneralCode(namingRuleService.getNameCode(NamingRuleCodeEnum.LayeredAuditManage.name(), 1, null).get(0));
                layeredIssueImprove.setLayeredAuditTemplate(createLayeredAuditTemplate(layeredAuditPlan.getLayeredAuditTemplate()));
                layeredIssueImprove.setAuditLevel(layeredAuditPlan.getAuditLevel());
                layeredIssueImprove.setStatus(LayeredIssueImproveStatusEnum.Enum.PENDING_EXECUTION.name());
                layeredIssueImprove.setIssueItemDetailList(createIssueItemDetail(execPlanDetail));
                fabosJsonDao.mergeAndFlush(layeredIssueImprove);
            }
        }
    }

    private List<IssueItemDetail> createIssueItemDetail(ExecPlanDetail execPlanDetail) {
        IssueItemDetail issueItemDetail = new IssueItemDetail();
        BeanUtil.copyProperties(execPlanDetail, issueItemDetail);
        List<IssueItemDetail> issueItemDetailList = new ArrayList<>();
        issueItemDetailList.add(issueItemDetail);
        return issueItemDetailList;
    }

    private LayeredAuditTemplate createLayeredAuditTemplate(LayeredAuditTemplate layeredAuditTemplate) {
        LayeredAuditTemplate template = new LayeredAuditTemplate();
        BeanUtil.copyProperties(layeredAuditTemplate, template);
        return template;
    }
}
