package cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.mto;

import cec.jiutian.bc.materialInspect.domain.publicInspectionTask.mto.UserForInsTaskMTO;
import cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.enumration.TaskBusinessStateEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.Entity;
import jakarta.persistence.Transient;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/4/27
 * @description TODO
 */
@FabosJson(
        name = "关闭"
)
@Entity
@Getter
@Setter
public class AbnormalFeedbackHandlingCloseMTO extends MetaModel {
    @Transient
    @FabosJsonField(
            views = @View(title = "选择关闭人", show = false, column = "name"),
            edit = @Edit(title = "选择关闭人",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType()
            )
    )
    private UserForInsTaskMTO closePeople;

    @FabosJsonField(
            views = @View(title = "关闭人ID", show = false),
            edit = @Edit(title = "关闭人ID",
                    readonly = @Readonly(),
                    show = false,
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "closePeople", beFilledBy = "id"))
    )
    private String closePeopleId;

    @FabosJsonField(
            views = @View(title = "关闭人"),
            edit = @Edit(title = "关闭人",
                    readonly = @Readonly(),
                    notNull = true,
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "closePeople", beFilledBy = "name"))
    )
    private String closePeopleName;

    @FabosJsonField(
            views = @View(title = "关闭时间"),
            edit = @Edit(title = "关闭时间",
                    dateType = @DateType(type = DateType.Type.DATE_TIME),
                    notNull = true
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date closeTime;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",
                    show = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = TaskBusinessStateEnum.class)
            )
    )
    private String businessState;
}
