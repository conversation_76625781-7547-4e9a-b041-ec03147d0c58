package cec.jiutian.bc.clientComplaintManagement.domain.problemImprovement.mto;

import cec.jiutian.bc.clientComplaintManagement.domain.clientComplaintFeedback.model.ClientComplaintFeedback;
import cec.jiutian.bc.clientComplaintManagement.enumeration.ProblemImprovementStatusEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.frame.module.MetaUser;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/05/20
 * @description TODO
 */
@FabosJson(
        name = "原因分析"

)
@Table(name = "qms_ccm_problem_improvement")
@Entity
@Getter
@Setter
public class ProblemImprovementResultValidateMTO extends MetaModel {
    @ManyToOne
    @FabosJsonField(
            views = {
                    @View(title = "客诉反馈", column = "generalCode")
            },
            edit = @Edit(title = "客诉反馈",
                    type = EditType.REFERENCE_TABLE,
                    show = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode")
            )
    )
    private ClientComplaintFeedback clientComplaintFeedback;

    @FabosJsonField(
            views = @View(title = "验收情况"),
            edit = @Edit(title = "验收情况",
                    search = @Search(vague = true))
    )
    private String acceptanceStatus;

    @FabosJsonField(
            views = @View(title = "验证附件"),
            edit = @Edit(title = "验证附件",
                    type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType)
    )
    private String verifyAttachment;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",
                    show = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ProblemImprovementStatusEnum.class)
            )
    )
    private String businessStatus;
}
