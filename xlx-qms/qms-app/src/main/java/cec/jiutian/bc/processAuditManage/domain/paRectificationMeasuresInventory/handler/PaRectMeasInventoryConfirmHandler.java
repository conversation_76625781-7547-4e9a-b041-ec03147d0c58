package cec.jiutian.bc.processAuditManage.domain.paRectificationMeasuresInventory.handler;

import cec.jiutian.bc.processAuditManage.domain.paRectificationMeasuresInventory.enumration.PaRectMeasuresInventoryStatusEnum;
import cec.jiutian.bc.processAuditManage.domain.paRectificationMeasuresInventory.model.PaRectificationMeasuresInventory;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/15
 * @description TODO
 */
@Component
public class PaRectMeasInventoryConfirmHandler implements OperationHandler<PaRectificationMeasuresInventory, Void> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Transactional
    @Override
    public String exec(List<PaRectificationMeasuresInventory> data, Void modelObject, String[] param) {
        PaRectificationMeasuresInventory model = data.get(0);
        model.setBusinessStatus(PaRectMeasuresInventoryStatusEnum.Enum.PENDING.name());

        fabosJsonDao.update(model);
        return null;
    }
}
