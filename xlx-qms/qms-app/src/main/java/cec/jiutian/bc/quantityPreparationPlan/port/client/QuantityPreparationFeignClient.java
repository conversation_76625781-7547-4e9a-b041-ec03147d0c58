package cec.jiutian.bc.quantityPreparationPlan.port.client;

import cec.jiutian.bc.basicData.dto.MsSipResultDTO;
import cec.jiutian.core.frame.constant.FabosJsonRestPath;
import com.alibaba.fastjson2.JSONObject;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/3/25
 * @description TODO
 */
@Component
@FeignClient(name = "api-business-mes")
public interface QuantityPreparationFeignClient {

    @PostMapping(FabosJsonRestPath.FABOS_REMOTE_API_OLD+"/getSipById")
    MsSipResultDTO getSipByIdFromMES(@RequestParam String sipId);

    @PostMapping("/errorRule/list")
    JSONObject getErrorRule(@RequestBody Map<String,Object> params);

    @PostMapping("/productionPlan/scheduleList")
    JSONObject scheduleListFromMES(@RequestBody Map<String,Object> params);
}
