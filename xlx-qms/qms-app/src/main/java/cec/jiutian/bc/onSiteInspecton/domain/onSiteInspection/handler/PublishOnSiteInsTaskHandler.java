package cec.jiutian.bc.onSiteInspecton.domain.onSiteInspection.handler;

import cec.jiutian.bc.onSiteInspecton.domain.onSiteInspection.model.OnSiteInspectionTask;
import cec.jiutian.bc.onSiteInspecton.enumeration.InspectionTaskStatusEnum;
import cec.jiutian.bc.urm.domain.user.entity.User;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import jakarta.persistence.OneToMany;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@RefreshScope
public class PublishOnSiteInsTaskHandler implements OperationHandler<OnSiteInspectionTask, OnSiteInspectionTask> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Value("${qms.debug}")
    private boolean debug;


    private static final String orgCode = "IQC";

    @Override
    public String exec(List<OnSiteInspectionTask> data, OnSiteInspectionTask modelObject, String[] param) {
        if (CollectionUtils.isEmpty(data)) {
            return null;
        }
        String userId = UserContext.getUserId();
        User userMTO = fabosJsonDao.findById(User.class, userId);
        if (userMTO == null) {
            throw new ServiceException("用户不存在");
        }
        if (!debug && (userMTO.getOrg() != null && !userMTO.getOrg().getCode().equals(orgCode))) {
            throw new ServiceException("用户不是IQC人员");
        }
        OnSiteInspectionTask task = data.get(0);
        if (!InspectionTaskStatusEnum.Enum.CREATED.name().equals(task.getState())
                && !InspectionTaskStatusEnum.Enum.EDIT.name().equals(task.getState())) {
            throw new ServiceException("任务状态不正确");
        }
        task = fabosJsonDao.findById(OnSiteInspectionTask.class, task.getId());
        if (CollectionUtils.isEmpty(task.getStandardItems())) {
            throw new ServiceException("请录入巡检项");
        }
        task.setState(InspectionTaskStatusEnum.Enum.PUBLISHED.name());
        fabosJsonDao.mergeAndFlush(task);
        return "操作成功";
    }
}
