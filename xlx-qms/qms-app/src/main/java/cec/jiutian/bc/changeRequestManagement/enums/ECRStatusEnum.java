package cec.jiutian.bc.changeRequestManagement.enums;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

public class ECRStatusEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum data : Enum.values()) {
            list.add(new VLModel(data.name(), data.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {

        //待提交
        WAIT_SUBMIT("待提交"),
        //待审批
        WAIT_EXAMINE("待审批"),
        //待发起评审
        WAIT_REVIEW("待发起评审"),
        //待评审
        WAIT_REVIEW_APPROVE("待评审"),
        //待计划
        WAIT_PLAN("待计划"),
        //待发起批准
        WAIT_APPROVE("待发起批准"),
        //待批准
        APPROVING("待批准"),
        //执行中
        EXECUTING("执行中"),
        //待验证
        WAIT_VERIFY("待验证"),
        //待变更实施审核
        WAIT_CHANGE_EXECUTE("待变更实施审核"),
        //待变更实施批准
        WAIT_CHANGE_EXECUTE_APPROVE("待变更实施批准"),
        //待闭环确认
        WAIT_CLOSING("待闭环确认"),
        //完成
        COMPLETE("完成"),
        //待补充
        WAIT_SUPPLEMENT("待补充"),
        SUPPLEMENT_EXECUTING("补充任务执行中"),
        SUPPLEMENT_WAIT_VERIFY("补充任务待验证"),
        SUPPLEMENT_VERIFYING("补充任务验证中")
        ;

        private final String value;

    }
}
