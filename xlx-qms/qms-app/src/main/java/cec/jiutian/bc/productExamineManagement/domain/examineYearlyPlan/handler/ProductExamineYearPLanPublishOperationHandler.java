package cec.jiutian.bc.productExamineManagement.domain.examineYearlyPlan.handler;

import cec.jiutian.bc.productExamineManagement.domain.examineYearlyPlan.model.ProductExamineYearlyPlan;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/27
 * @description TODO
 */
@Component
public class ProductExamineYearPLanPublishOperationHandler implements OperationHandler<ProductExamineYearlyPlan,Void> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<ProductExamineYearlyPlan> data, Void modelObject, String[] param) {
        ProductExamineYearlyPlan productExamineYearlyPlan = data.get(0);
        productExamineYearlyPlan.setBusinessState(OrderCurrentStateEnum.Enum.EXECUTE.name());
        fabosJsonDao.mergeAndFlush(productExamineYearlyPlan);

        return "alert('操作成功')";
    }
}
