package cec.jiutian.bc.clientComplaintManagement.enumeration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/12
 * @description TODO
 */
public class ProblemImprovementStatusEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum data : Enum.values()) {
            list.add(new VLModel(data.name(), data.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        TO_BE_DISTRIBUTED("待分发"),
        TO_BE_EXECUTE("待执行"),
        EXECUTING("执行中"),
        EXECUTED("已执行"),
        TO_BE_VERIFIED("待验证"),
        COMPLETE("已完成"),

        CLOSED("已关闭"),
        ;

        private final String value;

    }
}
