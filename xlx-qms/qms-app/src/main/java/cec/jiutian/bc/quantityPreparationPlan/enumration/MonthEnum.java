package cec.jiutian.bc.quantityPreparationPlan.enumration;

import cec.jiutian.bc.basicData.enumeration.InitialInspectionTypeEnum;
import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/5
 * @description TODO
 */
public class MonthEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum data : Enum.values()) {
            list.add(new VLModel(data.name(), data.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        january("1月"),
        february("2月"),
        march("3月"),
        april("4月"),
        may("5月"),
        june("6月"),
        july("7月"),
        august("8月"),
        september("9月"),
        october("10月"),
        november("11月"),
        december("12月"),
        ;

        private final String value;

    }
}
