package cec.jiutian.bc.clientComplaintManagement.domain.clientComplaintFeedback.enumration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/12
 * @description TODO
 */
public class ClientComplaintFeedbackStatusEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum data : Enum.values()) {
            list.add(new VLModel(data.name(), data.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        TO_BE_SUBMITTED("待提交"),
        TO_BE_CONFIRMED("待确认"),
        TO_BE_CORRECTED("待执行"),
        COMPLETED("完成"),

        CLOSED("已关闭")
        ;

        private final String value;

    }
}
