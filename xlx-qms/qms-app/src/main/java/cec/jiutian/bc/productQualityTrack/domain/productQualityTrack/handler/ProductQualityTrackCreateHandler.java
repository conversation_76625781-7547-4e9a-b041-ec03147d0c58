package cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.handler;

import cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.enumration.ProductQualityTrackBusinessStatusEnum;
import cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.model.ProductQualityTrack;
import cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.mto.ProductQualityTrackCreateMTO;
import cec.jiutian.bc.flow.enums.ExamineStatusEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/6
 * @description
 */
@Component
public class ProductQualityTrackCreateHandler implements OperationHandler<ProductQualityTrack, ProductQualityTrackCreateMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<ProductQualityTrack> data, ProductQualityTrackCreateMTO modelObject, String[] param) {
        if (modelObject != null) {
            modelObject.setDocumentCreateDate(new Date());
            modelObject.setBusinessStatus(ProductQualityTrackBusinessStatusEnum.Enum.OPENING.name());
            modelObject.setExamineStatus(ExamineStatusEnum.UNAUDITED.getCode());
            fabosJsonDao.mergeAndFlush(modelObject);
        } else {
            return "alert(数据异常)";
        }
        return "alert(操作成功)";
    }
}
