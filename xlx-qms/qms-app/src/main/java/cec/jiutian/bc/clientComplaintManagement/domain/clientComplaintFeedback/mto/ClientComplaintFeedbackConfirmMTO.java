package cec.jiutian.bc.clientComplaintManagement.domain.clientComplaintFeedback.mto;

import cec.jiutian.bc.clientComplaintManagement.domain.clientComplaintFeedback.enumration.ClientComplaintFeedbackStatusEnum;
import cec.jiutian.bc.clientComplaintManagement.domain.problemImprovement.model.ProblemImprovement;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.*;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/05/20
 * @description TODO
 */
@FabosJson(
        name = "确认"
)
@Table(name = "eam_ccm_client_complaint_feedback",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Getter
@Setter
public class ClientComplaintFeedbackConfirmMTO extends MetaModel {
    @FabosJsonField(
            edit = @Edit(title = "问题改善", type = EditType.TAB_TABLE_ADD),
            views = @View(title = "问题改善", type = ViewType.TABLE_VIEW)
    )
    @JoinColumn(name = "eam_ccm_client_complaint_feedback_id")
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy
    private List<ProblemImprovementMTO> problemImprovementList;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",
                    show = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ClientComplaintFeedbackStatusEnum.class)
            )
    )
    private String businessStatus;
}
