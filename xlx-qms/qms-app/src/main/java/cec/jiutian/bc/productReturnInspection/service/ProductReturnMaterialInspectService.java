package cec.jiutian.bc.productReturnInspection.service;

import cec.jiutian.bc.productReturnInspection.domain.inspectionRequest.model.ProductReturnInspectionRequest;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

@Service
@Component
public class ProductReturnMaterialInspectService {

    @Resource
    private FabosJsonDao fabosJsonDao;

    /**
     * 自动创建检验通知单
     * 1.WMS系统检验通知发布时调用
     */
    public ProductReturnInspectionRequest createInspectionRequest(ProductReturnInspectionRequest productReturnInspectionRequest) {
        if (null != productReturnInspectionRequest) {
            productReturnInspectionRequest.setCurrentState(OrderCurrentStateEnum.Enum.EDIT.name());
            fabosJsonDao.insert(productReturnInspectionRequest);
        }
        return productReturnInspectionRequest;
    }

}
