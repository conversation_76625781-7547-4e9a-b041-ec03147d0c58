package cec.jiutian.bc.supplierManagement.domian.supplierAuditTemplate.proxy;

import cec.jiutian.bc.modeler.enumration.StatusEnum;
import cec.jiutian.bc.supplierManagement.domian.supplierAuditTemplate.model.SupplierAuditTemplate;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
public class SupplierAuditTemplateDataProxy implements DataProxy<SupplierAuditTemplate> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public void beforeAdd(SupplierAuditTemplate entity) {
        entity.setStatus(StatusEnum.Enum.Invalid.name());
    }

}
