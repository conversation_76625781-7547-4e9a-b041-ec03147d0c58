package cec.jiutian.bc.processInspect.port.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class BmSampleTaskCreateDTO {

    @ApiModelProperty("取样任务单号")
    private String sampleOrderNumber;

    @ApiModelProperty(name = "Specification Code", notes = "产品编码")
    private String specificationCode;

    @ApiModelProperty(name = "Specification Name", notes = "产品名称")
    private String specificationName;

    @ApiModelProperty("产线ID")
    private Long productionLineId;

    @ApiModelProperty("产线名称")
    private String productionLineName;

    @ApiModelProperty(value = "工序编码")
    private String processOperationCode;

    @ApiModelProperty(value = "工序名称")
    private String processOperationName;

    @ApiModelProperty(name = "Plan Sample Weight", notes = "计划取样重量")
    private Double planSampleWeight;

    @ApiModelProperty(name = "Plan Sample Weight", notes = "实际取样重量")
    private Double sampleWeight;

    @ApiModelProperty(name = "Sample Scheme", notes = "抽样方案")
    private String sampleScheme;

    @ApiModelProperty(name = "Operate Account Identifier", notes = "操作人员ID")
    private String operateAccountIdentifier;

    @ApiModelProperty(name = "Operate Account Name", notes = "操作人员姓名")
    private String operateAccountName;

}
