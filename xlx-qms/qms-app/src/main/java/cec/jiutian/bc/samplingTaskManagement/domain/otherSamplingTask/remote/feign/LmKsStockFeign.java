package cec.jiutian.bc.samplingTaskManagement.domain.otherSamplingTask.remote.feign;

import cec.jiutian.api.remoteCallLog.dto.RemoteCallResult;
import cec.jiutian.core.frame.constant.FabosJsonRestPath;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@Component
@FeignClient(name = "xlx-lims")
public interface LmKsStockFeign {
    /**
     * 创建留样出库单
     * <AUTHOR>
     * @date 2025/6/9 9:38
     * @param sampleTaskNo 样品任务单号
     * @param type 留样类型
     * @param quantity 出库数量
     * @return
     */
    @PostMapping("/fabos-lims-app"+ FabosJsonRestPath.FABOS_REMOTE_API + "/createLmKsStockOut")
    public RemoteCallResult<Void> createLmKsStockOut(@RequestParam(value = "sampleTaskNo") String sampleTaskNo,
                                                     @RequestParam(value = "type") String type,
                                                     @RequestParam(value = "quantity") Double quantity);
}
