package cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.enumration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/15
 * @description
 */
public class ProductQualityTrackBusinessStatusEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum data : Enum.values()) {
            list.add(new VLModel(data.name(), data.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        OPENING("开立"),
        PENDING_DECISION("待判定"),
        PENDING_APPROVAL("待审批"),
        IN_APPROVAL("审批中"),
        APPROVED("审批通过"),
        ;

        private final String value;

    }
}
