package cec.jiutian.bc.inventoryInspection.statistics.repository;

import cec.jiutian.bc.inventoryInspection.statistics.ao.StatisticsCommonParam;
import cec.jiutian.bc.inventoryInspection.statistics.util.*;
import cec.jiutian.common.exception.ServiceException;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class StatisticsRepositoryImpl implements StatisticsRepository {
    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public List<Object[]> queryPassTrendData(StatisticsCommonParam param) {
        // 参数校验
        param.validate();

        if ("0".equals(param.getQuery())) {
            String mineTimeSql = QualifiedRateQuery.mineTimeSql;
            Query nativeQuery = entityManager.createNativeQuery(mineTimeSql);
            List<Object> resultList = nativeQuery.getResultList();
            if (CollectionUtils.isEmpty(resultList)) {
                throw new ServiceException("未找到任何数据");
            }
            param.setStartTime(String.valueOf(resultList.get(0)));
        }

        // 构建SQL
        String sql = QualifiedRateQuery.buildSql(param);

        // 创建原生查询
        Query query = entityManager.createNativeQuery(sql);

        // 绑定参数
        bindParameters(query, param);

        return query.getResultList();
    }

    @Override
    public List<Object[]> queryLinePassRateData(StatisticsCommonParam param) {

        // 参数校验
        param.validate();

        // 构建SQL
        String sql = LinePassRateSqlBuilder.buildSql(param);

        // 创建原生查询
        Query query = entityManager.createNativeQuery(sql);

        // 绑定参数
        bindParameters(query, param);

        return query.getResultList();
    }

    @Override
    public List<Object[]> queryWorkshopPassRate(StatisticsCommonParam param) {
        param.validate();
        String sql = WorkshopPassRateSqlBuilder.buildWorkshopStatsSql(param);
        Query query = entityManager.createNativeQuery(sql);

        // 绑定参数
        if ("1".equals(param.getIsDiyTime())) {
            query.setParameter("startTime", param.getStartTime());
            query.setParameter("endTime", param.getEndTime());
        }
        if (StringUtils.isNotBlank(param.getProduceType())) {
            query.setParameter("produceType", param.getProduceType());
        }

        return query.getResultList();
    }

    @Override
    public List<Object[]> queryFactoryPassRate(StatisticsCommonParam param) {
        param.validate();
        String sql = FactoryPassRateSqlBuilder.buildFactoryStatsSql(param);
        Query query = entityManager.createNativeQuery(sql);

        // 绑定参数
        if ("1".equals(param.getIsDiyTime())) {
            query.setParameter("startTime", param.getStartTime());
            query.setParameter("endTime", param.getEndTime());
        }
        if (StringUtils.isNotBlank(param.getProduceType())) {
            query.setParameter("produceType", param.getProduceType());
        }

        return query.getResultList();
    }

    @Override
    public List<Object[]> queryCategoryPassRate(StatisticsCommonParam param) {
        param.validate();
        String sql = CategoryPassRateSqlBuilder.buildCategoryStatsSql(param);
        Query query = entityManager.createNativeQuery(sql);

        // 绑定参数
        if ("1".equals(param.getIsDiyTime())) {
            query.setParameter("startTime", param.getStartTime());
            query.setParameter("endTime", param.getEndTime());
        }
        if (StringUtils.isNotBlank(param.getProduceType())) {
            query.setParameter("produceType", param.getProduceType());
        }

        return query.getResultList();
    }

    @Override
    public List<Object[]> queryCodePassRate(StatisticsCommonParam param) {
        param.validate();
        String sql = MaterialCodePassRateSqlBuilder.buildMaterialCodeStatsSql(param);
        Query query = entityManager.createNativeQuery(sql);
        // 绑定参数
        if ("1".equals(param.getIsDiyTime())) {
            query.setParameter("startTime", param.getStartTime());
            query.setParameter("endTime", param.getEndTime());
        }
        if (StringUtils.isNotBlank(param.getProduceType())) {
            query.setParameter("produceType", param.getProduceType());
        }
        return query.getResultList();
    }

    @Override
    public List<Object[]> queryInsItemPassRate(StatisticsCommonParam param) {
        param.validate();
        String sql = ItemPassRateSqlBuilder.buildSql(param);
        Query query = entityManager.createNativeQuery(sql);
        // 绑定参数
        if ("1".equals(param.getIsDiyTime())) {
            query.setParameter("startTime", param.getStartTime());
            query.setParameter("endTime", param.getEndTime());
        }
        if (StringUtils.isNotBlank(param.getProduceType())) {
            query.setParameter("produceType", param.getProduceType());
        }
        return query.getResultList();
    }


    private void bindParameters(Query query, StatisticsCommonParam param) {
        // 绑定时间参数
        if ("1".equals(param.getIsDiyTime())) {
            if (param.getStartTime() != null) {
                query.setParameter("startTime", param.getStartTime().toString());
            }
            if (param.getEndTime() != null) {
                query.setParameter("endTime", param.getEndTime().toString());
            }
        }
        if (StringUtils.isNotBlank(param.getProduceType())) {
            query.setParameter("produceType", param.getProduceType());
        }
    }


    
}
