package cec.jiutian.bc.changeRequestManagement.domain.bo;

import cec.jiutian.common.exception.ServiceException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * key -> <node Code>
 * value -> Role:<roleId1>，Role:<RoleId2>
 *       -> User:<userId>
 */

@Component
@ConfigurationProperties(prefix = "qms.flow.ecr")
public class FlowAssigneeConfig {
    private Map<String, String> config = new HashMap<>();


    public String getAssignee(String nodeCode) {
        return config.get(nodeCode);
    }

    public List<String> getAssigneeIds(String nodeCode) {
        String assignee = getAssignee(nodeCode);
        if (StringUtils.isBlank(assignee)) {
            throw new ServiceException("请前往Nacos配置节点审批人");
        }
        String[] assignees = assignee.replaceAll("Role:", "")
                .replaceAll("User:", "")
                .split(",");
        return Arrays.asList(assignees);
    }

    public Map<String, String> getConfig() {
        return config;
    }

    public void setConfig(Map<String, String> config) {
        this.config = config;
    }
}
