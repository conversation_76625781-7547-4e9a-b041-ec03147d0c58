package cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.mto;

import cec.jiutian.bc.accidentReportManagement.enumeration.ArmMeasureStatusEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.ChoiceType;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/29
 * @description
 */
@FabosJson(
        name = "预防措施"
)
@Table(name = "qms_arm_accident_report_task")
@Entity
@Getter
@Setter
public class AccidentReportTaskPreventMTO extends MetaModel {
    @FabosJsonField(
            edit = @Edit(title = "预防措施", type = EditType.TAB_TABLE_ADD),
            views = @View(title = "预防措施", type = ViewType.TABLE_VIEW)
    )
    @JoinColumn(name = "qms_arm_accident_report_task_id")
    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
    @OrderBy
    private List<ArmPreventMeasureCreateMTO> armPreventMeasureList;

    @FabosJsonField(
            views = @View(title = "预防措施状态", show = false),
            edit = @Edit(title = "预防措施状态",
                    readonly = @Readonly(),
                    type = EditType.CHOICE,
                    show = false,
                    choiceType = @ChoiceType(fetchHandler = ArmMeasureStatusEnum.class)
            )
    )
    private String preventMeasureStatus;
}
