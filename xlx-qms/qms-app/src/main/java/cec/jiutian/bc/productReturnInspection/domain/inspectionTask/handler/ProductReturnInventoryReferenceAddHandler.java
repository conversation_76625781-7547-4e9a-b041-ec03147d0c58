package cec.jiutian.bc.productReturnInspection.domain.inspectionTask.handler;

import cec.jiutian.bc.productReturnInspection.domain.inspectionTask.model.ProductReturnInspectionTask;
import cec.jiutian.bc.productReturnInspection.domain.inspectionTask.model.ProductReturnInspectionTaskDetail;
import cec.jiutian.bc.productReturnInspection.port.dto.ProductReturnInventoryMTO;
import cec.jiutian.view.ReferenceAddType;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/3/10
 * @description TODO
 */
@Component
public class ProductReturnInventoryReferenceAddHandler implements ReferenceAddType.ReferenceAddHandler<ProductReturnInspectionTask, ProductReturnInventoryMTO> {


    @Override
    public Map<String, Object> handle(ProductReturnInspectionTask productReturnInspectionTask, List<ProductReturnInventoryMTO> productReturnInventoryMTOS) {
        Map<String, Object> result = new HashMap<>();
        List<ProductReturnInspectionTaskDetail> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(productReturnInventoryMTOS)) {
            productReturnInventoryMTOS.forEach(i -> {
//                ProductReturnInspectionTaskDetail productReturnInspectionTaskDetail = new ProductReturnInspectionTaskDetail();
//                productReturnInspectionTaskDetail.setInventoryId(i.getId());
//                productReturnInspectionTaskDetail.setInventoryLotId(i.getInventoryLotId());
//                productReturnInspectionTaskDetail.setSerialLotId(i.getLotSerialId());
//                productReturnInspectionTaskDetail.setInventoryQuantity(i.getAvailableQuantity());
            });
        }
        result.put("inspectionTaskDetailList",list);
        return result;
    }
}
