package cec.jiutian.bc.productExamineManagement.domain.examineImplementPlan.handler;

import cec.jiutian.bc.basicData.enumeration.ApplyRangeEnum;
import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.modeler.enumration.NamingRuleCodeEnum;
import cec.jiutian.bc.mto.SpecificationManageMTO;
import cec.jiutian.bc.productExamineManagement.domain.examineImplementPlan.model.ExamineImplementPlan;
import cec.jiutian.bc.productExamineManagement.domain.inspectionTask.model.ProductExamineInspectionTask;
import cec.jiutian.bc.productExamineManagement.enumration.ExamineImplementStateEnum;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/24
 * @description TODO
 */
@Component
public class ProductExamineInspectTaskGenerateOprHandler implements OperationHandler<ExamineImplementPlan, ProductExamineInspectionTask> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private NamingRuleService namingRuleService;

    @Override
    public String exec(List<ExamineImplementPlan> data, ProductExamineInspectionTask modelObject, String[] param) {
        ExamineImplementPlan examineImplementPlan = data.get(0);
        modelObject.setId(null);
        modelObject.setSampleQuantity(examineImplementPlan.getSampleQuantity());
        fabosJsonDao.mergeAndFlush(modelObject);
        examineImplementPlan.setBusinessState(ExamineImplementStateEnum.Enum.Inspecting.name());
        fabosJsonDao.mergeAndFlush(examineImplementPlan);
        return "alert(操作成功)";
    }

    @Override
    public ProductExamineInspectionTask fabosJsonFormValue(List<ExamineImplementPlan> data, ProductExamineInspectionTask fabosJsonForm, String[] param) {
        ExamineImplementPlan examineImplementPlan = data.get(0);
        SpecificationManageMTO manageMTO = fabosJsonDao.getById(SpecificationManageMTO.class, examineImplementPlan.getMaterialId());
        fabosJsonForm.setSpecificationManageMTO(manageMTO);
        fabosJsonForm.setGeneralCode(namingRuleService.getNameCode(NamingRuleCodeEnum.ProductExamineInspectionTask.name(), 1, null).get(0));
        fabosJsonForm.setExamineImplementPlan(examineImplementPlan);
        fabosJsonForm.setCurrentState(OrderCurrentStateEnum.Enum.EDIT.name());
        fabosJsonForm.setInspectionType(ApplyRangeEnum.Enum.ProductExamineInspect.name());
        return fabosJsonForm;
    }
}
