package cec.jiutian.bc.productReturnInspection.domain.inspectionTask.mto;

import cec.jiutian.bc.basicData.domain.inspectionStandard.model.InspectionStandard;
import cec.jiutian.bc.basicData.enumeration.ApplyRangeEnum;
import cec.jiutian.bc.inventoryInspection.enumeration.InspectionResultEnum;
import cec.jiutian.bc.modeler.enumration.TaskBusinessStateEnum;
import cec.jiutian.bc.modeler.enumration.UnqualifiedLevelEnum;
import cec.jiutian.bc.processInspect.domain.processInspectionTask.mto.TaskResultEnterDetailMTO;
import cec.jiutian.bc.productReturnInspection.domain.inspectionTask.handler.ProductReturnInspectionItemResultHandler;
import cec.jiutian.bc.productReturnInspection.domain.inspectionTask.handler.ProInsItemResultHandler;
import cec.jiutian.bc.productReturnInspection.enumeration.TaskBuildTypeEnum;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DependFieldDisplay;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/9
 * @description TODO
 */
@FabosJson(
        name = "检验任务结果录入MTO",
        power = @Power(add = false,edit = false,delete = false)
)
@Entity
@Getter
@Setter
public class MyProTaskResultEnterMTO extends MetaModel {

    @FabosJsonField(
            views = @View(title = "编号"),
            edit = @Edit(title = "编号", readonly = @Readonly)
    )
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "供应商名称"),
            edit = @Edit(title = "供应商名称",
                    readonly = @Readonly(),
                    search = @Search(vague = true))
    )
    private String supplierName;

    @FabosJsonField(
            views = @View(title = "检验部门"),
            edit = @Edit(title = "检验部门",
                    readonly = @Readonly(),
                    search = @Search(vague = true))
    )
    private String inspectionDepartmentName;

    @FabosJsonField(
            views = @View(title = "检验人"),
            edit = @Edit(title = "检验人",
                    readonly = @Readonly(),
                    search = @Search(vague = true))
    )
    private String userName;

    @FabosJsonField(
            views = @View(title = "检验状态"),
            edit = @Edit(title = "检验状态", readonly = @Readonly(),
                    type = EditType.CHOICE, search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = TaskBusinessStateEnum.class))
    )
    private String businessState;

    @FabosJsonField(
            views = @View(title = "当前状态"),
            edit = @Edit(title = "当前状态",readonly = @Readonly(),
                    type = EditType.CHOICE, search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = OrderCurrentStateEnum.class))
    )
    private String currentState;


    @FabosJsonField(
            views = @View(title = "质检标准", column = "generalCode"),
            edit = @Edit(title = "质检标准",
                    readonly = @Readonly(add = false), allowAddMultipleRows = false,
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode")
            )
    )
    @ManyToOne
    @JoinColumn(name = "inspection_standard_id")
    private InspectionStandard inspectionStandard;

    @FabosJsonField(
            views = @View(title = "创建类型"),
            edit = @Edit(title = "创建类型", type = EditType.CHOICE, search = @Search(),
                    readonly = @Readonly(),
                    choiceType = @ChoiceType(fetchHandler = TaskBuildTypeEnum.class))
    )
    private String buildType;

    @FabosJsonField(
            views = @View(title = "检验类型"),
            edit = @Edit(title = "检验类型", readonly = @Readonly()
                    , type = EditType.CHOICE, search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = ApplyRangeEnum.class))
    )
    private String inspectionType;

    @FabosJsonField(
            views = @View(title = "是否加急"),
            edit = @Edit(title = "是否加急",
                    readonly = @Readonly(),
                    defaultVal = "false"
            )
    )
    private Boolean isUrgent;

    @FabosJsonField(
            views = @View(title = "检验结果"),
            edit = @Edit(title = "检验结果", type = EditType.CHOICE, choiceType = @ChoiceType(fetchHandler = InspectionResultEnum.class)),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "detailList",
                    dynamicHandler = ProInsItemResultHandler.class))
    )
    private String inspectionResult;

    @FabosJsonField(
            views = @View(title = "不合格等级"),
            edit = @Edit(title = "不合格等级", notNull = true,type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = UnqualifiedLevelEnum.class),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "inspectionResult == 'UNQUALIFIED'"))
    )
    private String unqualifiedLevel;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = false)
    @JoinColumn(name = "inspection_task_id")
    @FabosJsonField(
            views = @View(title = "检验项指标", type = ViewType.TABLE_VIEW),
            edit = @Edit(title = "检验项指标", type = EditType.TAB_TABLE_ADD, readonly = @Readonly(edit = false)),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "detailList",
                    dynamicHandler = ProductReturnInspectionItemResultHandler.class))
    )
    private List<TaskResultEnterDetailMTO> detailList;
}
