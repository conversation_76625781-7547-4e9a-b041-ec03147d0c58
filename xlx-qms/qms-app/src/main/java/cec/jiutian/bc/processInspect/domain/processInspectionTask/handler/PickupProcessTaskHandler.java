package cec.jiutian.bc.processInspect.domain.processInspectionTask.handler;

import cec.jiutian.bc.deliveryInspection.port.mto.DeliveryUserMTO;
import cec.jiutian.bc.modeler.enumration.TaskBusinessStateEnum;
import cec.jiutian.bc.processInspect.domain.processInspectionTask.model.ProcessInspectionTask;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import jakarta.persistence.LockModeType;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Component
@RefreshScope
public class PickupProcessTask<PERSON>and<PERSON> implements OperationHandler<ProcessInspectionTask, ProcessInspectionTask> {

    @Resource
    private FabosJsonDao fabosJsonDao;


    @Override
    @Transactional
    public String exec(List<ProcessInspectionTask> data, ProcessInspectionTask modelObject, String[] param) {
        if (CollectionUtils.isEmpty(data)) {
            return null;
        }
        String userId = UserContext.getUserId();
        DeliveryUserMTO deliveryUserMTO = fabosJsonDao.findById(DeliveryUserMTO.class, userId);
        if (deliveryUserMTO == null) {
            throw new ServiceException("用户不存在");
        }
        ProcessInspectionTask task = data.get(0);
        task = fabosJsonDao.getEntityManager().find(ProcessInspectionTask.class, task.getId(), LockModeType.PESSIMISTIC_WRITE);
        if (task.getUserId() != null || task.getUserName() != null) {
            throw new ServiceException("该任务已被领取");
        }
        task.setUpdateBy(UserContext.getUserName());
        task.setUserName(UserContext.getUserName());
        task.setUserId(deliveryUserMTO.getId());
        task.setBusinessState(TaskBusinessStateEnum.Enum.INSPECTING.name());
        fabosJsonDao.mergeAndFlush(task);
        return "操作成功";
    }
}
