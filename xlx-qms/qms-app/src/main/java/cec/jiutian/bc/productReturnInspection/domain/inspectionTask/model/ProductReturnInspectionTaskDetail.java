package cec.jiutian.bc.productReturnInspection.domain.inspectionTask.model;

import cec.jiutian.bc.generalModeler.enumeration.YesOrNoEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/3/10
 * @description TODO
 */
@FabosJson(
        name = "检验任务物资详情"
)
@Table(name = "mi_inspection_task_material_detail")
@Entity
@Data
public class ProductReturnInspectionTaskDetail extends MetaModel {

    @FabosJsonField(
            views = @View(title = "编号"),
            edit = @Edit(title = "编号", show = false)
    )
    private String generalCode;

    @ManyToOne
    @FabosJsonField(
            views = {
                    @View(title = "检验任务", column = "generalCode", show = false)
            },
            edit = @Edit(title = "检验任务", type = EditType.REFERENCE_TABLE, show = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode")
            )
    )
    @JsonIgnoreProperties("inspectionTaskDetailList")
    private ProductReturnInspectionTask productReturnInspectionTask;

    @FabosJsonField(
            views = @View(title = "本厂批号/件次号库存Id", show = false),
            edit = @Edit(title = "本厂批号/件次号库存Id",show = false)
    )
    private String inventoryId;

    @FabosJsonField(
            views = @View(title = "本厂批号/件次号"),
            edit = @Edit(title = "本厂批号/件次号",readonly = @Readonly)
    )
    private String lotSerialId;

    @FabosJsonField(
            views = @View(title = "批次数量"),
            edit = @Edit(title = "批次数量",readonly = @Readonly)
    )
    private Double lotQuantity;

    @FabosJsonField(
            views = @View(title = "物料编码"),
            edit = @Edit(title = "物料编码",readonly = @Readonly)
    )
    private String materialCode;

    @FabosJsonField(
            views = @View(title = "物料名称"),
            edit = @Edit(title = "物料名称",readonly = @Readonly)
    )
    private String materialName;

    @FabosJsonField(
            views = @View(title = "单位"),
            edit = @Edit(title = "单位")
    )
    private String measureUnit;

    @FabosJsonField(
            views = @View(title = "是否随样"),
            edit = @Edit(title = "是否随样", type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = YesOrNoEnum.class))
    )
    private String sampleFlag;

    @FabosJsonField(
            views = @View(title = "取样数量"),
            edit = @Edit(title = "取样数量", notNull = true, numberType = @NumberType(min = 0, precision = 2, maxExpr = "${lotQuantity}"))
    )
    private Double requestQuantity;
}
