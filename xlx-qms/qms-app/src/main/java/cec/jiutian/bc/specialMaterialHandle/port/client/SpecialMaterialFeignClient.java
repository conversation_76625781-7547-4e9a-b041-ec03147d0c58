package cec.jiutian.bc.specialMaterialHandle.port.client;

import cec.jiutian.core.frame.constant.FabosJsonRestPath;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/5/26
 * @description TODO
 */
@Component
@FeignClient(name = "xlx-lims")
public interface SpecialMaterialFeignClient {

    @PostMapping("/fabos-lims-app"+ FabosJsonRestPath.FABOS_REMOTE_API+"/sampleReturn/callback")
    void specialMaterialHandle(@RequestBody Map<String, Object> params);
}
