package cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.handler;

import cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.enumration.ProgressEnum;
import cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.model.AbnormalFeedbackHandling;
import cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.mto.AbnormalFeedbackHandlingAnalyMTO;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/27
 * @description TODO
 */
@Component
public class AbnormalFeedbackHandlingAnalyHandler implements OperationHandler<AbnormalFeedbackHandling, AbnormalFeedbackHandlingAnalyMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<AbnormalFeedbackHandling> data, AbnormalFeedbackHandlingAnalyMTO modelObject, String[] param) {
        if (modelObject != null) {
            AbnormalFeedbackHandling model = data.get(0);
            BeanUtil.copyProperties(modelObject, model, "createBy", "createTime");
            model.setAnalysisState(ProgressEnum.Enum.RUN_SUBMIT.name());
            fabosJsonDao.mergeAndFlush(model);
        }
        return "alert(操作成功)";
    }

    @Override
    public AbnormalFeedbackHandlingAnalyMTO fabosJsonFormValue(List<AbnormalFeedbackHandling> data, AbnormalFeedbackHandlingAnalyMTO fabosJsonForm, String[] param) {
        AbnormalFeedbackHandling abnormalFeedbackHandling = data.get(0);
        BeanUtil.copyProperties(abnormalFeedbackHandling, fabosJsonForm);

        return fabosJsonForm;
    }

}
