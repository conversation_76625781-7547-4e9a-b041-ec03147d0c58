package cec.jiutian.bc.clientComplaintManagement.domain.clientComplaintFeedback.handler;

import cec.jiutian.bc.clientComplaintManagement.domain.clientComplaintFeedback.enumration.ClientComplaintFeedbackStatusEnum;
import cec.jiutian.bc.clientComplaintManagement.domain.clientComplaintFeedback.model.ClientComplaintFeedback;
import cec.jiutian.bc.clientComplaintManagement.domain.clientComplaintFeedback.mto.ClientComplaintFeedbackConfirmMTO;
import cec.jiutian.bc.clientComplaintManagement.domain.clientComplaintFeedback.mto.ProblemImprovementMTO;
import cec.jiutian.bc.clientComplaintManagement.domain.problemImprovement.model.ProblemImprovement;
import cec.jiutian.bc.clientComplaintManagement.enumeration.ProblemImprovementStatusEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/05/20
 * @description TODO
 */
@Component
public class ClientComplaintFeedbackConfirmHandler implements OperationHandler<ClientComplaintFeedback, ClientComplaintFeedbackConfirmMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<ClientComplaintFeedback> data, ClientComplaintFeedbackConfirmMTO modelObject, String[] param) {
        if (modelObject != null && data != null) {
            ClientComplaintFeedback model = data.get(0);
            if (model == null) {
                return "alert(选择数据异常)";
            }
            List<ProblemImprovementMTO> problemImprovementList = modelObject.getProblemImprovementList();
            for (ProblemImprovementMTO problemImprovement : problemImprovementList) {
                problemImprovement.setClientComplaintFeedback(model);
                problemImprovement.setBusinessStatus(ProblemImprovementStatusEnum.Enum.TO_BE_DISTRIBUTED.name());
                fabosJsonDao.mergeAndFlush(problemImprovement);
            }

            BeanUtil.copyProperties(modelObject, model, "createBy", "createTime");
            model.setBusinessStatus(ClientComplaintFeedbackStatusEnum.Enum.TO_BE_CORRECTED.name());
            fabosJsonDao.mergeAndFlush(model);
        }
        return "alert(操作成功)";
    }

    @Override
    public ClientComplaintFeedbackConfirmMTO fabosJsonFormValue(List<ClientComplaintFeedback> data, ClientComplaintFeedbackConfirmMTO fabosJsonForm, String[] param) {
        ClientComplaintFeedback model = data.get(0);
        BeanUtil.copyProperties(model, fabosJsonForm);
        return fabosJsonForm;
    }
}
