package cec.jiutian.bc.processInspect.domain.processInspectionPlan.handler;

import cec.jiutian.bc.basicData.domain.inspectionStandard.model.InspectionStandard;
import cec.jiutian.bc.basicData.enumeration.NamingRuleCodeEnum;
import cec.jiutian.bc.generalModeler.enumeration.YesOrNoEnum;
import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.modeler.domain.inspectionItem.model.InspectionItem;
import cec.jiutian.bc.modeler.enumration.TaskBusinessStateEnum;
import cec.jiutian.bc.processInspect.domain.processInspectionPlan.model.ProcessInspectionPlan;
import cec.jiutian.bc.processInspect.domain.processInspectionPlan.model.ProcessInspectionPlanDetail;
import cec.jiutian.bc.processInspect.domain.processInspectionPlan.model.ProcessInspectionPlanItem;
import cec.jiutian.bc.processInspect.domain.processInspectionPlan.mto.IPQCPlanExecuteTheFirstMTO;
import cec.jiutian.bc.processInspect.domain.processInspectionPlan.mto.ProcessInspectionPlanDetailMTO;
import cec.jiutian.bc.processInspect.domain.processInspectionTask.model.ProcessInspectionItemTarget;
import cec.jiutian.bc.processInspect.domain.processInspectionTask.model.ProcessInspectionTask;
import cec.jiutian.bc.processInspect.domain.processInspectionTask.model.ProcessInspectionTaskDetail;
import cec.jiutian.bc.processInspect.service.ProcessInspectService;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.common.util.BeanUtils;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Component
public class IPQCPlanExecuteTheFirstOperationHandler implements OperationHandler<ProcessInspectionPlan, IPQCPlanExecuteTheFirstMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private NamingRuleService namingRuleService;

    @Resource
    private ProcessInspectService processInspectService;

    @Override
    @Transactional
    public String exec(List<ProcessInspectionPlan> data, IPQCPlanExecuteTheFirstMTO modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data) && CollectionUtils.isNotEmpty(modelObject.getDetailList())) {
            ProcessInspectionPlan entity = data.get(0);
            // 校验：详情和检验项目不为空
            if (CollectionUtils.isEmpty(entity.getDetails())) {
                throw new FabosJsonApiErrorTip("检验计划详情不能为空");
            } else {
                for (ProcessInspectionPlanDetail detail : entity.getDetails()) {
                    if (CollectionUtils.isEmpty(detail.getItems())) {
                        throw new FabosJsonApiErrorTip("检验项目不能为空，请检查");
                    } else {
                        for (ProcessInspectionPlanItem item : detail.getItems()) {
                            if (null == item.getInspectionItemGroup()) {
                                throw new FabosJsonApiErrorTip("检验项目组不能为空，请检查");
                            }
                        }
                    }
                }
            }

            InspectionStandard inspectionStandard = entity.getInspectionStandard();

            for (ProcessInspectionPlanDetailMTO detailMTO : modelObject.getDetailList()) {
                entity.getDetails().stream().filter(x -> x.getId().equals(detailMTO.getId())).findFirst()
                        .ifPresent(p -> {
                            p.setCurrentState(OrderCurrentStateEnum.Enum.EXECUTE.name());

                            // 工序首检任务
                            ProcessInspectionTask task = new ProcessInspectionTask();
                            task.setGeneralCode(namingRuleService.getNameCode(NamingRuleCodeEnum.ProcessInspectionTask.name(), 1, null).get(0));
                            task.setInspectionPlanId(entity.getId());
                            task.setInspectionPlanNumber(entity.getGeneralCode());
                            task.setInspectionPlanDetailId(p.getId());
                            task.setTechnologyFlowId(entity.getTechnologyFlowId());
                            task.setOperationCode(p.getOperationCode());
                            task.setOperationName(p.getOperationName());
                            task.setProcessCode(entity.getProcessCode());
                            task.setProcessName(entity.getProcessName());
                            task.setProductCode(entity.getProductCode());
                            task.setProductName(entity.getProductName());
                            task.setProcessInspectionType(p.getType());
                            task.setKeepSampleFlag(inspectionStandard.getKeepSampleFlag());
                            task.setKeepSampleQuantity(inspectionStandard.getSampleQuantity());
                            task.setKeepSampleUnit(inspectionStandard.getSampleUnit());
                            task.setSequenceNumber(1);
                            String batchNumber = processInspectService.generateSerialNumber(p.getOperationCode(), Long.valueOf(entity.getFactoryLineId()));
                            task.setPreLotSerialId(batchNumber +"-"+ String.format("%04d", 1)); // 工序批次流水生成规则
                            task.setCurrentState(OrderCurrentStateEnum.Enum.EDIT.name());
                            task.setBusinessState(TaskBusinessStateEnum.Enum.BE_INSPECT.name());
                            task.setExtraFlag(YesOrNoEnum.Enum.N.name());
                            task.setFactoryAreaId(entity.getFactoryAreaId());
                            task.setFactoryAreaName(entity.getFactoryAreaName());
                            task.setFactoryLineId(entity.getFactoryLineId());
                            task.setFactoryLineName(entity.getFactoryLineName());

                            List<ProcessInspectionTaskDetail> taskDetails = new ArrayList<>();
                            p.getItems().forEach(i -> {
                                ProcessInspectionTaskDetail taskDetail = new ProcessInspectionTaskDetail();
                                BeanUtils.copyProperties(i, taskDetail);
                                taskDetail.setId(null);
                                taskDetail.setItemGroupId(i.getInspectionItemGroup().getId());
                                InspectionItem item = fabosJsonDao.getById(InspectionItem.class, i.getItemId());
                                taskDetail.setItemName(item.getName());
                                taskDetail.setUrgentFlag(YesOrNoEnum.Enum.N.name());

                                if (CollectionUtils.isNotEmpty(item.getInspectionItemTargetList())) {
                                    List<ProcessInspectionItemTarget> targetList = new ArrayList<>();
                                    item.getInspectionItemTargetList().forEach(t -> {
                                        ProcessInspectionItemTarget target = new ProcessInspectionItemTarget();
                                        BeanUtils.copyProperties(t, target);
                                        target.setTargetId(t.getId());
                                        target.setId(null);
                                        targetList.add(target);
                                    });
                                    taskDetail.setTargetList(targetList);
                                }

                                taskDetails.add(taskDetail);
                            });
                            task.setDetails(taskDetails);

                            fabosJsonDao.saveOrUpdate(task);
                        });
            }

            entity.setCurrentState(OrderCurrentStateEnum.Enum.EXECUTE.name());
            fabosJsonDao.mergeAndFlush(entity);

        }

        return "msg.success('操作成功')";
    }
}
