package cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.model;

import cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.enumration.*;
import cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.handler.*;
import cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.mto.*;
import cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.proxy.ProductQualityTrackDataProxy;
import cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.proxy.ProductQualityTrackFlowProxy;
import cec.jiutian.bc.flow.domain.model.entity.ExamineModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.*;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowBaseOperation;
import cec.jiutian.view.type.RowOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/15
 * @description
 */
@FabosJson(
        name = "产品品质跟踪",
        orderBy = "ProductQualityTrack.createTime desc",
        power = @Power(add = false, edit = false, examine= true, examineDetails = true),
        dataProxy = ProductQualityTrackDataProxy.class,
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "businessStatus != 'OPENING'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "examine",
                        ifExpr = "examineStatus == '3' || examineStatus == '1' || businessStatus != 'PENDING_APPROVAL'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "examineDetails",
                        ifExpr = "examineStatus == '0'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                )
        },
        rowOperation = {
                @RowOperation(
                        title = "创建",
                        code = "ProductQualityTrack@CREATE",
                        mode = RowOperation.Mode.BUTTON,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = ProductQualityTrackCreateMTO.class,
                        operationHandler = ProductQualityTrackCreateHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "ProductQualityTrack@CREATE"
                        )
                ),
                @RowOperation(
                        title = "编辑",
                        code = "ProductQualityTrack@MODIFY",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = ProductQualityTrackCreateMTO.class,
                        operationHandler = ProductQualityTrackModifyHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "ProductQualityTrack@MODIFY"
                        ),
                        ifExpr = "selectedItems[0].businessStatus != 'OPENING'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "结果录入",
                        code = "ProductQualityTrack@RESULTINPUT",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = ProductQualityTrackResultInputMTO.class,
                        operationHandler = ProductQualityTrackResultInputHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "ProductQualityTrack@RESULTINPUT"
                        ),
                        ifExpr = "selectedItems[0].businessStatus != 'OPENING'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "结果审批",
                        code = "ProductQualityTrack@APPROVAL",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = ProductQualityTrackApprovalMTO.class,
                        operationHandler = ProductQualityTrackApprovalHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "ProductQualityTrack@APPROVAL"
                        ),
                        ifExpr = "selectedItems[0].businessStatus != 'PENDING_DECISION'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                )
        },
        flowProxy = {ProductQualityTrackFlowProxy.class},
        flowCode = "ProductQualityTrack"
)
@Table(name = "qms_pqt_product_quality_track",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Getter
@Setter
public class ProductQualityTrack extends ExamineModel {
    //创建
    @FabosJsonField(
            views = @View(title = "编号"),
            edit = @Edit(title = "编号", search = @Search(vague = true))
    )
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "单据类型"),
            edit = @Edit(title = "单据类型",
                    type = EditType.CHOICE, search = @Search(vague = true),
                    choiceType = @ChoiceType(fetchHandler = PqtDocumentTypeEnum.class))
    )
    private String documentType;

    @FabosJsonField(
            views = @View(title = "开机/换线任务单编码",show = false),
            edit = @Edit(title = "开机/换线任务单编码",
                    show = false
            )
    )
    private String mwGid;

    @FabosJsonField(
            views = @View(title = "开机/换线任务单名称"),
            edit = @Edit(title = "开机/换线任务单名称", search = @Search(vague = true))
    )
    private String mwName;

    @FabosJsonField(
            views = @View(title = "车间ID",show = false),
            edit = @Edit(title = "车间ID", show = false)
    )
    private String factoryAreaId;

    @FabosJsonField(
            views = @View(title = "车间名称"),
            edit = @Edit(title = "车间名称",
                    search = @Search(vague = true)
            )
    )
    private String factoryAreaName;

    @FabosJsonField(
            views = @View(title = "产线Id", show = false),
            edit = @Edit(title = "产线Id", show = false)
    )
    private String factoryLineId;

    @FabosJsonField(
            views = @View(title = "产线名称"),
            edit = @Edit(title = "产线名称", search = @Search(vague = true))
    )
    private String factoryLineName;

    @FabosJsonField(
            views = @View(title = "产品id", show = false),
            edit = @Edit(title = "产品id", show = false)
    )
    private String materialId;

    @FabosJsonField(
            views = @View(title = "产品编码"),
            edit = @Edit(title = "产品编码", search = @Search(vague = true))
    )
    private String materialCode;

    @FabosJsonField(
            views = @View(title = "产品名称"),
            edit = @Edit(title = "产品名称", search = @Search(vague = true))
    )
    private String materialName;

    @FabosJsonField(
            views = @View(title = "产品型号"),
            edit = @Edit(title = "产品型号", search = @Search(vague = true))
    )
    private String materialType;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",
                    search = @Search(vague = true),
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ProductQualityTrackBusinessStatusEnum.class)
            )
    )
    private String businessStatus;

    @FabosJsonField(
            views = @View(title = "日期"),
            edit = @Edit(title = "日期",
                    search = @Search(vague = true),
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date documentCreateDate;

    @FabosJsonField(
            views = @View(title = "工序列表",
                    column = "processOperationName",
                    type = ViewType.TABLE_VIEW),
            edit = @Edit(title = "工序列表",
                    type = EditType.TAB_REFERENCE_GENERATE
            ),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "materialId", dynamicHandler = PqtProcessOperationListHandler.class))
    )
    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumn(name = "qms_pqt_product_quality_track_id")
    private List<PqtProcessOperation> pqtProcessOperationList;
}
