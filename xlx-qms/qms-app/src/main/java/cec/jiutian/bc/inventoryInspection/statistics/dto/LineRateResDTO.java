package cec.jiutian.bc.inventoryInspection.statistics.dto;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class LineRateResDTO {
    /**
     * 产线ID
     */
    private String productionLineId;

    /**
     * 产线名称
     */
    private String productionLineName;

    /**
     * 生产数量
     * 根据计算维度不同，可能是批次数量或重量
     */
    private Double outputQuantity;

    /**
     * 不良数量
     * 根据计算维度不同，可能是批次数量或重量
     */
    private Double unqualifiedQuantity;

    /**
     * 合格数量
     * 根据计算维度不同，可能是批次数量或重量
     */
    private Double qualifiedQuantity;

    /**
     * 成品合格率（保留4位小数）
     * 计算公式：合格数量 / 生产数量
     */
    private Double passRate;

    /**
     * 目标合格率（固定值0.5）
     */
    private final Double target = Double.valueOf(0.5);

    public static LineRateResDTO mapToDto(Object[] row) {
        LineRateResDTO lineRateResDTO = new LineRateResDTO();
        return lineRateResDTO
                .setProductionLineId(safeToString(row[0]))
                .setProductionLineName(safeToString(row[1]))
                .setOutputQuantity(convertToDouble(row[2]))
                .setUnqualifiedQuantity(convertToDouble(row[3]))
                .setQualifiedQuantity(convertToDouble(row[4]))
                .setPassRate(convertToDouble(row[5]));
    }

    private static String safeToString(Object obj) {
        return obj == null ? null : obj.toString();
    }

    private static Double convertToDouble(Object value) {
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        return 0D;
    }
}