package cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.handler;

import cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.enumration.ArmBusinessStatusEnum;
import cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.model.ArmPreventMeasure;
import cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.model.ArmTemporaryMeasure;
import cec.jiutian.bc.accidentReportManagement.enumeration.ArmImprovingProgressEnum;
import cec.jiutian.bc.accidentReportManagement.enumeration.ArmMeasureStatusEnum;
import cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.enumration.ArmVerificationResultEnum;
import cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.model.AccidentReportTask;
import cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.mto.AccidentReportTaskCloseMTO;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/5/30
 * @description TODO
 */
@Component
@Slf4j
public class AccidentReportTaskCloseHandler implements OperationHandler<AccidentReportTask, AccidentReportTaskCloseMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<AccidentReportTask> data, AccidentReportTaskCloseMTO modelObject, String[] param) {
        if (modelObject != null) {
            AccidentReportTask model = data.get(0);
            BeanUtil.copyProperties(modelObject, model, "createBy", "createTime");

            String closeApprovalComments = model.getCloseApprovalComments();
            if (closeApprovalComments.equals(ArmVerificationResultEnum.Enum.PASSED.name())) {
                model.setBusinessStatus(ArmBusinessStatusEnum.Enum.COMPLETED.name());
                model.setTemporaryMeasureStatus(ArmMeasureStatusEnum.Enum.CLOSED.name());
                model.setPreventMeasureStatus(ArmMeasureStatusEnum.Enum.CLOSED.name());
            } else {
                List<ArmPreventMeasure> preList = model.getArmPreventMeasureList();
                if (preList == null || preList.isEmpty()) {
                    model.setPreventMeasureStatus(ArmMeasureStatusEnum.Enum.TO_BE_VERIFIED.name());
                } else {
                    model.setPreventMeasureStatus(ArmMeasureStatusEnum.Enum.SUBMITTED.name());
                }

                List<ArmTemporaryMeasure> temList = model.getArmTemporaryMeasureList();
                if (temList == null || temList.isEmpty()) {
                    model.setTemporaryMeasureStatus(ArmMeasureStatusEnum.Enum.TO_BE_VERIFIED.name());
                } else {
                    model.setTemporaryMeasureStatus(ArmMeasureStatusEnum.Enum.SUBMITTED.name());
                }
                setImprovingProgressToPendingExecution(model);
            }
            fabosJsonDao.mergeAndFlush(model);
        }
        return "alert(操作成功)";
    }

    private void setImprovingProgressToPendingExecution(AccidentReportTask model) {
        Optional.ofNullable(model.getArmTemporaryMeasureList())
                .ifPresent(list -> list.stream()
                        .forEach(mto -> mto.setImprovingProgress(
                                ArmImprovingProgressEnum.Enum.PENDING_EXECUTION.name()
                        ))
                );
        Optional.ofNullable(model.getArmPreventMeasureList())
                .ifPresent(list -> list.stream()
                        .forEach(mto -> mto.setImprovingProgress(
                                ArmImprovingProgressEnum.Enum.PENDING_EXECUTION.name()
                        ))
                );
    }

    @Override
    public AccidentReportTaskCloseMTO fabosJsonFormValue(List<AccidentReportTask> data, AccidentReportTaskCloseMTO fabosJsonForm, String[] param) {
        AccidentReportTask model = data.get(0);
        BeanUtil.copyProperties(model, fabosJsonForm);
        return fabosJsonForm;
    }
}
