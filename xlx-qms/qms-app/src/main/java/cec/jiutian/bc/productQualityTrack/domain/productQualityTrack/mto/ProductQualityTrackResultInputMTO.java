package cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.mto;

import cec.jiutian.bc.flow.domain.model.entity.ExamineModel;
import cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.enumration.ProductQualityTrackBusinessStatusEnum;;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.ReferenceGenerateType;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import jakarta.persistence.*;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/15
 * @description
 */
@FabosJson(
        name = "结果录入"
)
@Table(name = "qms_pqt_product_quality_track",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Data
public class ProductQualityTrackResultInputMTO extends ExamineModel {
    @FabosJsonField(
            views = @View(title = "编号"),
            edit = @Edit(title = "编号",
                    readonly = @Readonly()
            )
    )
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",
                    show = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ProductQualityTrackBusinessStatusEnum.class)
            )
    )
    private String businessStatus;

    @FabosJsonField(
            views = @View(title = "工序列表",
                    column = "processOperationName",
                    type = ViewType.TABLE_VIEW),
            edit = @Edit(title = "工序列表",
                    type = EditType.TAB_REFERENCE_GENERATE
            ),
            referenceGenerateType = @ReferenceGenerateType(editable = {"pqtProductBatchNumberList"})
    )
    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumn(name = "qms_pqt_product_quality_track_id")
    private List<PqtProcessOperationResultInputMTO> pqtProcessOperationList;
}
