package cec.jiutian.bc.samplingTaskManagement.domain.otherSamplingTask.proxy;

import cec.jiutian.bc.samplingTaskManagement.domain.otherSamplingTask.model.OtherSamplingTask;
import cec.jiutian.view.fun.DataProxy;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/5/12
 * @description TODO
 */
@Component
public class OtherSamplingTaskProxy implements DataProxy<OtherSamplingTask> {

    @Override
    public void beforeAdd(OtherSamplingTask otherSamplingTask) {
        DataProxy.super.beforeAdd(otherSamplingTask);
    }
}
