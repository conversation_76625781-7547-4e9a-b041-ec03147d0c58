package cec.jiutian.bc.questionPaperManagement.domain.questionPaper.model;

import cec.jiutian.bc.questionPaperManagement.enums.QuestionPaperMeasureCheckEnum;
import cec.jiutian.bc.questionPaperManagement.enums.QuestionPaperMeasureProgressEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.AttachmentType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DateType;
import cec.jiutian.view.field.edit.DependFieldDisplay;
import cec.jiutian.view.field.edit.ReferenceTableType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

@FabosJson(
        name = "问题一页纸-临时措施"
)
@Table(name = "qpm_question_paper_interim_measure")
@Entity
@Getter
@Setter
public class QuestionPaperInterimMeasure extends MetaModel {

    @ManyToOne
    @FabosJsonField(
            views = {
                    @View(title = "问题一页纸", column = "generalCode", show = false)
            },
            edit = @Edit(title = "问题一页纸", type = EditType.REFERENCE_TABLE, show = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode")
            )
    )
    @JsonIgnoreProperties("interimMeasures")
    private QuestionPaper questionPaper;

    @FabosJsonField(
            views = @View(title = "临时措施"),
            edit = @Edit(title = "临时措施")
    )
    private String measure;

    @FabosJsonField(
            views = @View(title = "责任人ID", show = false),
            edit = @Edit(title = "责任人ID", show = false)
    )
    private String responsiblePersonId;

    @FabosJsonField(
            views = @View(title = "责任人"),
            edit = @Edit(title = "责任人")
    )
    private String responsiblePersonName;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "纳期", type = ViewType.DATE),
            edit = @Edit(title = "纳期", dateType = @DateType(type = DateType.Type.DATE))
    )
    private LocalDate deliveryDate;

    @FabosJsonField(
            views = @View(title = "进度"),
            edit = @Edit(title = "进度",
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = QuestionPaperMeasureProgressEnum.class))
    )
    private String progress;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "完成日期", type = ViewType.DATE),
            edit = @Edit(title = "完成日期", dateType = @DateType(type = DateType.Type.DATE))
    )
    private LocalDate completeDate;

    @FabosJsonField(
            views = @View(title = "完成佐证材料"),
            edit = @Edit(title = "完成佐证材料", type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(maxLimit = 10))
    )
    private String completeAttachment;

    @FabosJsonField(
            views = @View(title = "验证结果"),
            edit = @Edit(title = "验证结果", notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = QuestionPaperMeasureCheckEnum.class),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "progress == 'WaitCheck'"))
    )
    private String checkResult;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "验证日期", type = ViewType.DATE),
            edit = @Edit(title = "验证日期", notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "progress == 'WaitCheck'"))
    )
    private LocalDate checkDate;

    @FabosJsonField(
            views = @View(title = "验证佐证材料"),
            edit = @Edit(title = "验证佐证材料", type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(maxLimit = 10),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "progress == 'WaitCheck'"))
    )
    private String checkAttachment;


}
