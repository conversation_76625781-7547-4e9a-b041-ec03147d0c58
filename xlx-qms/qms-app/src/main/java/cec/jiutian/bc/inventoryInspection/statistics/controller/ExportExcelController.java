package cec.jiutian.bc.inventoryInspection.statistics.controller;

import cec.jiutian.bc.dto.ChartData;
import cec.jiutian.bc.inventoryInspection.statistics.ao.ExportDataAO;
import cec.jiutian.bc.inventoryInspection.statistics.service.ExcelExportService;
import cec.jiutian.bc.inventoryInspection.statistics.service.YieldRateService;
import cec.jiutian.core.frame.module.R;
import jakarta.annotation.Resource;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.net.URLEncoder;


@RestController
@RequestMapping("/fabos-qms/data-export")
public class ExportExcelController {

    @Resource
    private ExcelExportService excelExportService;

    @Resource
    private YieldRateService yieldRateService;

    private static final String fileName = "成品合格率趋势图.xlsx";

    @PostMapping("/trend")
    public R<Void> trend(HttpServletResponse response, @RequestBody ExportDataAO queryAO) {

            ChartData chartData = yieldRateService.getQualityChartData(queryAO);
        if (queryAO.getIndex() > chartData.getSeries().size()) {
            return R.errorWithTypedParam("参数错误");
        }

        try( ServletOutputStream outputStream = response.getOutputStream();) {
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" +
                    URLEncoder.encode(fileName, "UTF-8"));

            excelExportService.exportChartDataToExcel(chartData,outputStream,queryAO.getIndex());


        } catch (Exception e) {
            return  R.error(e.getMessage());
        }

        return R.ok();
    }

}
