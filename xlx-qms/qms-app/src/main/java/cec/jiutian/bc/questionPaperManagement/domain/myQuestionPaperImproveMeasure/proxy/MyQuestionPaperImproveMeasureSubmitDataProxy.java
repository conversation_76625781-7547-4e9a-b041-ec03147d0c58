package cec.jiutian.bc.questionPaperManagement.domain.myQuestionPaperImproveMeasure.proxy;

import cec.jiutian.bc.questionPaperManagement.domain.myQuestionPaperImproveMeasure.model.MyQuestionPaperImproveMeasureSubmit;
import cec.jiutian.bc.questionPaperManagement.enums.QuestionPaperMeasureProgressEnum;
import cec.jiutian.view.fun.DataProxy;
import org.springframework.stereotype.Component;

@Component
public class MyQuestionPaperImproveMeasureSubmitDataProxy implements DataProxy<MyQuestionPaperImproveMeasureSubmit> {

    @Override
    public void beforeUpdate(MyQuestionPaperImproveMeasureSubmit entity) {
        entity.setProgress(QuestionPaperMeasureProgressEnum.Enum.WaitCheck.name());
    }

}
