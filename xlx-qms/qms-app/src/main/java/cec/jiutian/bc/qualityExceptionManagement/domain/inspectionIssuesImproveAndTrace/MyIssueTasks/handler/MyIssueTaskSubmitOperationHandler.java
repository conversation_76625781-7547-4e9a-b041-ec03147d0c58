package cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.MyIssueTasks.handler;

import cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.MyIssueTasks.model.MyIssueTask;
import cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.issueList.model.IssueList;
import cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.issueList.model.IssueListCorrection;
import cec.jiutian.bc.qualityExceptionManagement.enumeration.inspectionIssuesImproveAndTrace.IssueListStatusEnum;
import cec.jiutian.bc.qualityExceptionManagement.enumeration.inspectionIssuesImproveAndTrace.ProgressEnum;
import cec.jiutian.bc.qualityExceptionManagement.enumeration.inspectionIssuesImproveAndTrace.ProgressRUNEnum;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component
public class MyIssueTaskSubmitOperationHandler implements OperationHandler<MyIssueTask, Void> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<MyIssueTask> data, Void modelObject, String[] param) {
        //这里有个问题  data传过来的参数  子表会包括所有的数据。但是在我的整改任务中
        //子表一般只处理 子表责任人是当前登录人的数据。因此需要通过过滤来处理
        //子表过滤过后的主表模型不能用于更新  更新过后会把过滤的数据从数据库层面去掉
        if (CollectionUtils.isNotEmpty(data)) {
            MyIssueTask myIssueTask = data.get(0);
            List<IssueListCorrection> updateDetailList = new ArrayList<>();
            for (IssueListCorrection d : myIssueTask.getIssueListCorrections()) {
                if(!d.getMetaUser().getId().equals(UserContext.getUserId())){
                    //不是自己的任务不需要参与校验
                    continue;
                }
                if(!ProgressRUNEnum.Enum.RUN_SUBMIT.name().equals(d.getProgress())){
                    throw new FabosJsonApiErrorTip("提交失败：需要完成所有整改任务才可提交");
                }
                //保存自己的整改任务  如果校验通过需要更新这部分数据的状态
                updateDetailList.add(d);

            }

            updateDetailList.forEach(d->{
                d.setProgress(ProgressEnum.Enum.VERIFIED.name());
                fabosJsonDao.mergeAndFlush(d);
            });
            //需要判断  如果所有的明细都是待验证  那么整个单据需要调整状态为：待验证
            IssueList issueList = fabosJsonDao.findById(IssueList.class, myIssueTask.getId());
            if(checkCurrentStatus(issueList)){
                issueList.setStatus(IssueListStatusEnum.Enum.VERIFIED.name());
                fabosJsonDao.mergeAndFlush(issueList);
            }
        }
        return "msg.success('操作成功')";
    }

    private boolean checkCurrentStatus(IssueList issueList) {
        for (IssueListCorrection correction : issueList.getIssueListCorrections()) {
            if (!ProgressEnum.Enum.VERIFIED.name().equals(correction.getProgress())) {
                return false;
            }
        }
        return true;
    }
}
