package cec.jiutian.bc.inventoryInspection.domain.inspectionTask.handler;

import cec.jiutian.bc.basicData.domain.inspectionItemGroup.model.InspectionItemGroup;
import cec.jiutian.bc.basicData.domain.inspectionStandard.model.InspectionStandard;
import cec.jiutian.bc.basicData.enumeration.NamingRuleCodeEnum;
import cec.jiutian.bc.generalModeler.enumeration.YesOrNoEnum;
import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.generalModeler.util.StringUtils;
import cec.jiutian.bc.inventoryInspection.domain.inspectionRequest.model.InventoryInspectionRequest;
import cec.jiutian.bc.inventoryInspection.domain.inspectionTask.model.InventoryInspectionStandardDetail;
import cec.jiutian.bc.inventoryInspection.domain.inspectionTask.model.InventoryInspectionTask;
import cec.jiutian.bc.inventoryInspection.domain.inspectionTask.model.InventoryInspectionTaskDetail;
import cec.jiutian.bc.inventoryInspection.domain.samplingTask.model.InventorySamplingTask;
import cec.jiutian.bc.inventoryInspection.domain.samplingTask.model.InventorySamplingTaskDetail;
import cec.jiutian.bc.materialInspect.port.client.MaterialInspectFeignClient;
import cec.jiutian.bc.modeler.enumration.SampleTypeEnum;
import cec.jiutian.bc.modeler.enumration.TaskBusinessStateEnum;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/10
 * @description TODO
 */
@Component
public class InventoryPublishOperationHandler implements OperationHandler<InventoryInspectionTask, Void> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private NamingRuleService namingRuleService;

    @Resource
    private MaterialInspectFeignClient materialInspectFeignClient;

    @Override
    @Transactional
    public String exec(List<InventoryInspectionTask> data, Void modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            InventoryInspectionTask inventoryInspectionTask = data.get(0);
            if (CollectionUtils.isEmpty(inventoryInspectionTask.getInspectionTaskDetailList())) {
                throw new FabosJsonApiErrorTip("未选择检验物资批次，无法发布，请确认");
            }else {
                Double inventoryAllQuantity = inventoryInspectionTask.getInspectionTaskDetailList().stream().mapToDouble(InventoryInspectionTaskDetail::getRequestQuantity).sum();
                if (!Objects.equals(inventoryInspectionTask.getAllInspectionItemQuantity(), inventoryAllQuantity)) {
                    throw new FabosJsonApiErrorTip("检验物资批次总数量与取样数量不符，无法发布，请确认");
                }
            }

            inventoryInspectionTask.setCurrentState(OrderCurrentStateEnum.Enum.EXECUTE.name());
            fabosJsonDao.mergeAndFlush(inventoryInspectionTask);

            InventoryInspectionRequest inspectionRequest = fabosJsonDao.findById(InventoryInspectionRequest.class,inventoryInspectionTask.getInspectionRequest().getId());
            if (inspectionRequest != null) {
                inspectionRequest.setBusinessState(TaskBusinessStateEnum.Enum.INSPECTING.name());
                fabosJsonDao.mergeAndFlush(inspectionRequest);
            }

            Map<String, Object> outboundRequest = createOutboundRequest(inventoryInspectionTask);

            try {
                materialInspectFeignClient.createOutboundRequest(outboundRequest);
            } catch (Exception e) {
                throw new ServiceException("远程接口调用失败:" + e.getMessage());
            }

            // 根据检验任务创建取样任务，一个检验组对应一个取样任务
            if (CollectionUtils.isNotEmpty(inventoryInspectionTask.getStandardDetailList())) {
                List<InventoryInspectionStandardDetail> groupList = inventoryInspectionTask.getStandardDetailList().stream().filter(q -> StringUtils.isNotEmpty(q.getGroupId())).toList();
                if (CollectionUtils.isNotEmpty(groupList)) {
                    Map<String, List<InventoryInspectionStandardDetail>> map = groupList.stream()
                            .collect(Collectors.groupingBy(InventoryInspectionStandardDetail::getGroupId));
                    for (String key : map.keySet()) {
                        if (inventoryInspectionTask.getAllInspectionItemQuantity() != null && inventoryInspectionTask.getAllInspectionItemQuantity() > 0D
                                && hasNonZeroSampleSize(map.get(key))) {
                            createSamplingTask(false,key, map.get(key), inventoryInspectionTask);
                        }
                    }
                }

                List<InventoryInspectionStandardDetail> nullGroupList = inventoryInspectionTask.getStandardDetailList().stream().filter(q -> StringUtils.isEmpty(q.getGroupId())).toList();
                if (CollectionUtils.isNotEmpty(nullGroupList)) {
                    nullGroupList.forEach(detail -> {
                        List<InventoryInspectionStandardDetail> list = new ArrayList<>();
                        list.add(detail);
                        if (inventoryInspectionTask.getAllInspectionItemQuantity() != null && inventoryInspectionTask.getAllInspectionItemQuantity() > 0D) {
                            createSamplingTask(false,null, list, inventoryInspectionTask);
                        }
                    });
                }

                // 根据质检标准判断是否创建留样类型取样单
                InspectionStandard inspectionStandard = fabosJsonDao.getById(InspectionStandard.class, inventoryInspectionTask.getInspectionStandard().getId());
                if (YesOrNoEnum.Enum.Y.name().equals(inspectionStandard.getKeepSampleFlag())) {
                    createSamplingTask(true,null, null, inventoryInspectionTask);
                }
            }
        }
        return "alert(操作成功)";
    }

    private boolean hasNonZeroSampleSize(List<InventoryInspectionStandardDetail> detailList) {
        if (detailList == null || detailList.isEmpty()) {
            return false;
        }
        double sum = 0.0;
        for (InventoryInspectionStandardDetail detail : detailList) {
            if (detail != null) {
                sum += detail.getSampleSize() != null ? detail.getSampleSize() : 0.0;
            }
        }
        return Math.abs(sum) > 1e-10;
    }

    private void createSamplingTask(Boolean isKeepSample, String groupId, List<InventoryInspectionStandardDetail> list, InventoryInspectionTask inventoryInspectionTask) {
        InventorySamplingTask inventorySamplingTask = new InventorySamplingTask();
        if (isKeepSample) {
            inventorySamplingTask.setIsSaveSample(true);
            InspectionStandard inspectionStandard = fabosJsonDao.getById(InspectionStandard.class, inventoryInspectionTask.getInspectionStandard().getId());
            inventorySamplingTask.setSamplePoint(inspectionStandard.getSamplePoint());
            inventorySamplingTask.setSendPoint(inspectionStandard.getSendPointMTO().getName());
            inventorySamplingTask.setUnit(inspectionStandard.getSampleUnit());
            inventorySamplingTask.setAllInspectionItemQuantity(inspectionStandard.getSampleQuantity());
        }
        if (StringUtils.isNotEmpty(groupId)) {
            InspectionItemGroup inspectionItemGroup = fabosJsonDao.getById(InspectionItemGroup.class, groupId);
            inventorySamplingTask.setInspectionItemGroupName(inspectionItemGroup.getGroupName());
            inventorySamplingTask.setSamplePoint(inspectionItemGroup.getSamplePoint());
            inventorySamplingTask.setSendPoint(inspectionItemGroup.getSendPointMTO().getName());
            inventorySamplingTask.setUnit(inspectionItemGroup.getUsualUnit());
            inventorySamplingTask.setPackageType(inspectionItemGroup.getPackageType());
        }

        inventorySamplingTask.setSamplePlanDate(new Date());
        inventorySamplingTask.setGeneralCode(String.valueOf(namingRuleService.getNameCode(NamingRuleCodeEnum.SamplingTask.name(), 1, null).get(0)));
        inventorySamplingTask.setInspectionTaskCode(inventoryInspectionTask.getGeneralCode());
        inventorySamplingTask.setInspectionType(SampleTypeEnum.Enum.INVENTORY_SAMPLING.name());
        inventorySamplingTask.setIsUrgent(inventoryInspectionTask.getIsUrgent());
        inventorySamplingTask.setBusinessState(TaskBusinessStateEnum.Enum.BE_SAMPLING.name());
        inventorySamplingTask.setMaterialCode(inventoryInspectionTask.getInspectionRequest().getMaterialCode());
        inventorySamplingTask.setMaterialName(inventoryInspectionTask.getMaterialName());
        inventorySamplingTask.setSupplierName(inventoryInspectionTask.getSupplierName());
        inventorySamplingTask.setMaterialSpecification(inventoryInspectionTask.getMaterialSpecification());
        inventorySamplingTask.setUnit(inventoryInspectionTask.getUnit());
        inventorySamplingTask.setOriginLotId(inventoryInspectionTask.getOriginLotId());

        if (CollectionUtils.isNotEmpty(list)) {
            List<InventorySamplingTaskDetail> detailList = new ArrayList<>();
            list.forEach(standardDetail -> {
                InventorySamplingTaskDetail inventorySamplingTaskDetail = new InventorySamplingTaskDetail();
                inventorySamplingTaskDetail.setInventorySamplingTask(inventorySamplingTask);
                inventorySamplingTaskDetail.setName(standardDetail.getName());
                inventorySamplingTaskDetail.setSamplingPlan(standardDetail.getSamplingPlan());
                inventorySamplingTaskDetail.setInspectionMethod(standardDetail.getInspectionMethod());
                inventorySamplingTaskDetail.setItemId(standardDetail.getItemId());
                inventorySamplingTaskDetail.setCode(standardDetail.getCode());
                inventorySamplingTaskDetail.setSampleSize(standardDetail.getSampleSize());
                inventorySamplingTask.setAllInspectionItemQuantity((inventorySamplingTask.getAllInspectionItemQuantity() == null ? 0.0 : inventorySamplingTask.getAllInspectionItemQuantity())
                        + standardDetail.getSampleSize());
                detailList.add(inventorySamplingTaskDetail);
            });
            inventorySamplingTask.setDetailList(detailList);
        }
        fabosJsonDao.mergeAndFlush(inventorySamplingTask);
    }

    private Map<String, Object> createOutboundRequest(InventoryInspectionTask inventoryInspectionTask) {
        Map<String, Object> result = new HashMap<>();
        UserContext.CurrentUser currentUser = UserContext.get();
        if (inventoryInspectionTask != null && currentUser != null) {
            result.put("inspectionTaskNumber",inventoryInspectionTask.getGeneralCode());
            result.put("inspectionRequestNumber",inventoryInspectionTask.getInspectionRequest().getGeneralCode());
            result.put("inspectItemType",inventoryInspectionTask.getInspectionType());
            result.put("username",currentUser.getUserName());
            result.put("org_id",currentUser.getOrgId());
            result.put("applyDate",new Date());
            if (CollectionUtils.isNotEmpty(inventoryInspectionTask.getInspectionTaskDetailList())) {
                List<Map<String, Object>> list = new ArrayList<>();
                inventoryInspectionTask.getInspectionTaskDetailList().forEach(detail -> {
                    Map<String, Object> detailMap = new HashMap<>();
                    detailMap.put("inspectMaterialApplyDetailNumber",detail.getGeneralCode());
                    detailMap.put("materialName", inventoryInspectionTask.getMaterialName());
                    detailMap.put("materialCode", inventoryInspectionTask.getMaterialCode());
                    detailMap.put("measureUnit", detail.getMeasureUnit());
                    detailMap.put("originLotId", inventoryInspectionTask.getOriginLotId());
                    detailMap.put("serialLotId",detail.getLotSerialId());
                    detailMap.put("inventoryId",detail.getInventoryId());
                    detailMap.put("requestQuantity", detail.getRequestQuantity());
                    list.add(detailMap);
                    result.put("details",list);
                });
            }
        }
        return result;
    }
}
