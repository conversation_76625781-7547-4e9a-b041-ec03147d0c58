package cec.jiutian.bc.productionStartupRelease.domain.productionStartupRelease.handler;

import cec.jiutian.bc.productionStartupRelease.domain.AproveTask.config.ApproveNodeConfig;
import cec.jiutian.bc.productionStartupRelease.domain.AproveTask.modle.PLSRApproveTask;
import cec.jiutian.bc.productionStartupRelease.domain.AproveTask.repository.PLSRApproveTaskRepository;
import cec.jiutian.bc.productionStartupRelease.enums.ApproveNodeEnum;
import cec.jiutian.bc.productionStartupRelease.enums.StatusEnum;
import cec.jiutian.bc.productionStartupRelease.domain.productionStartupRelease.model.PLSRReport;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Component

public class SubmitApproveOperationHandler implements OperationHandler<PLSRReport, PLSRReport> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private PLSRApproveTaskRepository plsrApproveTaskRepository;

    @Override
    @Transactional
    public String exec(List<PLSRReport> data, PLSRReport modelObject, String[] param) {

        if (CollectionUtils.isEmpty(data)) {
            throw new ServiceException("选择数据为空");
        }

        PLSRReport plsrReport = data.get(0);
        // 创建审批任务
        // 车间工艺负责人
        createApproveTask(ApproveNodeEnum.Node.WORKSHOP_PROCESS, plsrReport.getId());
        //车间生产负责人
        createApproveTask(ApproveNodeEnum.Node.WORKSHOP_PRODUCTION, plsrReport.getId());
        //车间设备负责人
        createApproveTask(ApproveNodeEnum.Node.WORKSHOP_EQUIPMENT, plsrReport.getId());
        //车间品质负责人
        createApproveTask(ApproveNodeEnum.Node.WORKSHOP_QUALITY, plsrReport.getId());
        plsrReport.setStatus(StatusEnum.Enum.APPROVE_ING.name());

        fabosJsonDao.mergeAndFlush(plsrReport);
        return "";
    }

    private void createApproveTask(ApproveNodeEnum.Node node, String businessId) {
        List<String> assigneeIds = ApproveNodeConfig.getAssigneeIds(node.name());
        if (CollectionUtils.isEmpty(assigneeIds)) {
            throw new ServiceException("Nacos 未配置审批角色，请联系系统管理员。");
        }
        Integer turn = plsrApproveTaskRepository.findByBOrderByBusinessKeyAndCode(businessId, node.name());
        if (turn == null) {
            turn = -1;
        }
        for (String roleId : assigneeIds) {
            PLSRApproveTask roleTask = PLSRApproveTask.createRoleTask(businessId, roleId, node, turn+1);
            fabosJsonDao.mergeAndFlush(roleTask);
        }
    }


}
