package cec.jiutian.bc.qualityExceptionManagement.domain.reworkTask.model;

import cec.jiutian.bc.inventoryInspection.enumeration.InspectionResultEnum;
import cec.jiutian.bc.modeler.enumration.UnqualifiedLevelEnum;
import cec.jiutian.bc.qualityExceptionManagement.domain.reworkTask.handler.CompleteReworkHandler;
import cec.jiutian.bc.qualityExceptionManagement.domain.reworkTask.proxy.ReworkTaskDataProxy;
import cec.jiutian.bc.qualityExceptionManagement.domain.reworkTask.proxy.ReworkTaskFlowProxy;
import cec.jiutian.bc.qualityExceptionManagement.enums.ReworkTaskStatusEnum;
import cec.jiutian.bc.qualityExceptionManagement.enums.TaskTypeEnum;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.module.QMSNamingRuleModel;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.ReferenceGenerateType;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowBaseOperation;
import cec.jiutian.view.type.RowOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;

import java.util.Date;
import java.util.List;

@Entity
@Table(name = "qms_rework_task")
@Getter
@Setter
@FabosJson(
        name = "返工处置任务",
        dataProxy = ReworkTaskDataProxy.class,
        orderBy = "ReworkTask.createTime desc",
        power = @Power(add = false, examine = true,examineDetails = true, export = false),
        flowCode = "ReworkTask",
        flowProxy = ReworkTaskFlowProxy.class,
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "examine",
                        ifExpr = "!(status =='CREATE' && examineStatus =='0')",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "examineDetails",
                        ifExpr = "examineStatus =='0'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "status !='CREATE'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "status !='CREATE'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
        },
        rowOperation = {
                @RowOperation(
                        code = "ReworkDetail@COMPLETE",
                        title = "完成",
                        mode = RowOperation.Mode.SINGLE,
                        ifExpr = "status !='EXECUTING' || examineStatus != '1'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        operationHandler = CompleteReworkHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "ReworkDetail@COMPLETE"
                        )
                )
        }
)
@TemplateType(type = "multiTable")
public class ReworkTask extends QMSNamingRuleModel {

    @FabosJsonField(
            views = @View(title = "关联单据"),
            edit = @Edit(title = "关联单据", notNull = true,
                    search = @Search,
                    inputType = @InputType(length = 40),
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = TaskTypeEnum.class)
            )
    )
    private String taskType;

    @FabosJsonField(
            views = @View(title = "关联单据单号"),
            edit = @Edit(title = "关联单据单号",
                    notNull = true,
                    search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    private String taskCode;

    @FabosJsonField(
            views = @View(title = "产品名称"),
            edit = @Edit(title = "产品名称", notNull = true,
                    search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    private String productName;

    @FabosJsonField(
            views = @View(title = "产品编号"),
            edit = @Edit(title = "产品编号", notNull = true,
                    search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    private String productCode;

    @FabosJsonField(
            views = @View(title = "批次号"),
            edit = @Edit(title = "批次号", notNull = true, search = @Search(vague = true))
    )
    private String batchCode;

    @FabosJsonField(
            views = @View(title = "返工数量"),
            edit = @Edit(title = "返工数量", type = EditType.NUMBER,
                    inputType = @InputType(type = "number"),
                    numberType = @NumberType(min = 0, max = Integer.MAX_VALUE, precision = 2)
            )
    )
    private Double reworkQuantity;

    @FabosJsonField(
            views = @View(title = "是否合格"),
            edit = @Edit(title = "是否合格",
                    type = EditType.BOOLEAN,
                    search = @Search(vague = true)
            )
    )
    private Boolean isQualified;

    @FabosJsonField(
            views = @View(title = "不合格等级"),
            edit = @Edit(title = "不合格等级",
                    type = EditType.CHOICE,
                    search = @Search,
                    choiceType = @ChoiceType(fetchHandler = UnqualifiedLevelEnum.class)
            )
    )
    private String unqualifiedLevel;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",
                    readonly = @Readonly(add = true,edit = true),
                    type = EditType.CHOICE,
                    search = @Search,
                    defaultVal = "CREATE",
                    choiceType = @ChoiceType(fetchHandler = ReworkTaskStatusEnum.class)
            ),
            dynamicField = @DynamicField(passive = true)
    )
    private String status;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "创建时间", type = ViewType.DATE_TIME),
            edit = @Edit(title = "创建时间",
                    show = false,
                    dateType = @DateType(type = DateType.Type.DATE_TIME))
    )
    @CreationTimestamp
    private Date creationTime;

    @FabosJsonField(
            views = @View(title = "再检验结果"),
            edit = @Edit(title = "再检验结果",
                    type = EditType.CHOICE,
                    search = @Search,
                    choiceType = @ChoiceType(fetchHandler = InspectionResultEnum.class)
            )
    )
    private String recheckResult;

    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumn(name = "rework_task_id")
    @FabosJsonField(
            views = @View(title = "返工明细",
                    type = ViewType.TABLE_VIEW,
                    column = "currentBatch"),
            edit = @Edit(title = "返工明细",
//                    readonly = @Readonly(add = true, edit = true),
                    referenceTableType = @ReferenceTableType,
                    type = EditType.TAB_REFERENCE_GENERATE
            ),
            referenceGenerateType = @ReferenceGenerateType(editable = {"remark","attachment"})
    )
    private List<ReworkDetail> reworkDetails;

    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumn(name = "rework_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @FabosJsonField(
            views = @View(title = "返工追溯", column = "serialNumber", type = ViewType.TABLE_VIEW),
            edit = @Edit(
                    show = false,
                    title = "返工追溯",
                    type = EditType.TAB_TABLE_ADD,
                    allowAddMultipleRows = true,
                    referenceTableType = @ReferenceTableType(label = "serialNumber")
            )
    )
    private List<ReworkTrace> changeTraces;

}
