package cec.jiutian.bc.changeRequestManagement.domain.ecr.service;

import cec.jiutian.bc.changeRequestManagement.domain.ecn.model.ChangeRequestExecute;
import cec.jiutian.bc.changeRequestManagement.domain.ecn.model.VerificationIDetail;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.model.ChangeTrace;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.mto.ProcessInspectionMTO;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.mto.ProcessMTO;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.repository.ChangeTraceRepository;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.repository.ProcessInspectionMTORepository;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.repository.ProcessMTORepository;
import cec.jiutian.bc.changeRequestManagement.enums.ReqTraceResultEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class ChangeTraceService {


    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private ProcessMTORepository processMTORepository;

    @Resource
    private ChangeTraceRepository changeTraceRepository;

    @Resource
    private ProcessInspectionMTORepository processInspectionMTORepository;

    @Transactional
    public void pullData(String ecrId) {

        ChangeRequestExecute changeRequestExecute = fabosJsonDao.findById(ChangeRequestExecute.class, ecrId);
        VerificationIDetail verificationIDetail = changeRequestExecute.getVerificationIDetail();
        if (verificationIDetail == null) {
            log.error("验证信息为空：{}", ecrId);
            return;
        }
        Long factoryAreaId = changeRequestExecute.getFactoryAreaId();
        Long factoryLineId = changeRequestExecute.getFactoryLineId();
        Date endDate = changeRequestExecute.getEndDate();
        Date executeDate = verificationIDetail.getExecuteDate();
        if (factoryAreaId == null || factoryLineId == null || endDate == null || executeDate == null) {
            log.error("参数异常：{}", ecrId);
            return;
        }
        List<ProcessMTO> processMTOS = processMTORepository
                .findByOutputTimeBetweenAndWorkshopIdAndProductionLineId(executeDate, endDate,
                        factoryAreaId, factoryLineId);
        if (CollectionUtils.isEmpty(processMTOS)) {
            return;
        }
        for (ProcessMTO processMTO : processMTOS) {
            String serialNumber = processMTO.getSerialNumber();
            ChangeTrace changeTrace = new ChangeTrace();
            changeTrace.setEcrId(ecrId);
            changeTrace.setSerialNumber(serialNumber);
            changeTrace.setProcess(processMTO.getProcessName());
            changeTrace.setProcessCode(processMTO.getProcessCode());
            changeTrace.setWeight(processMTO.getByproductWeight());

            ProcessInspectionMTO processInspectionMTO = processInspectionMTORepository
                    .findFirstByActualLotSerialIdOrderByCreateTimeDesc(serialNumber);
            if (processInspectionMTO != null) {
                changeTrace.setInspectionCode(processInspectionMTO.getGeneralCode());
                if (StringUtils.isBlank(processInspectionMTO.getResult())) {
                    changeTrace.setInspectionResult(ReqTraceResultEnum.Enum.WAITING.name());
                } else {
                    changeTrace.setInspectionResult(processInspectionMTO.getResult());
                }
            } else {
                changeTrace.setInspectionResult(ReqTraceResultEnum.Enum.WAITING.name());
            }
            changeTrace.setCreateTime(LocalDateTime.now());
            changeTraceRepository.save(changeTrace);
        }
    }

    @Transactional
    public void getInsResult() {
        List<ChangeTrace> changeTraceList = changeTraceRepository
                .findByInspectionResultOrderByCreateTimeDesc(ReqTraceResultEnum.Enum.WAITING.name());
        if (CollectionUtils.isEmpty(changeTraceList)) {
            return;
        }
        for (ChangeTrace changeTrace : changeTraceList) {
            ProcessInspectionMTO processInspectionMTO = processInspectionMTORepository
                    .findFirstByActualLotSerialIdOrderByCreateTimeDesc(changeTrace.getSerialNumber());
            if (processInspectionMTO != null) {
                changeTrace.setInspectionCode(processInspectionMTO.getGeneralCode());
                changeTrace.setInspectionResult(processInspectionMTO.getResult());
                changeTraceRepository.save(changeTrace);
            }
        }
    }
}
