package cec.jiutian.bc.questionPaperManagement.domain.myQuestionPaperImproveMeasure.model;

import cec.jiutian.bc.questionPaperManagement.domain.myQuestionPaperImproveMeasure.proxy.MyQuestionPaperImproveMeasureSubmitDataProxy;
import cec.jiutian.bc.questionPaperManagement.enums.QuestionPaperMeasureProgressEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.AttachmentType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DateType;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

@FabosJson(
        name = "我的改善措施-提交",
        dataProxy = MyQuestionPaperImproveMeasureSubmitDataProxy.class
)
@Table(name = "qpm_question_paper_improve_measure")
@Entity
@Getter
@Setter
public class MyQuestionPaperImproveMeasureSubmit extends MetaModel {

    @FabosJsonField(
            views = @View(title = "改善措施"),
            edit = @Edit(title = "改善措施", readonly = @Readonly)
    )
    private String measure;

    @FabosJsonField(
            views = @View(title = "突出/强调事项"),
            edit = @Edit(title = "突出/强调事项")
    )
    private String emphasizedMatter;

    @FabosJsonField(
            views = @View(title = "责任人ID", show = false),
            edit = @Edit(title = "责任人ID", show = false)
    )
    private String responsiblePersonId;

    @FabosJsonField(
            views = @View(title = "责任人"),
            edit = @Edit(title = "责任人", readonly = @Readonly)
    )
    private String responsiblePersonName;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "纳期", type = ViewType.DATE),
            edit = @Edit(title = "纳期", readonly = @Readonly, dateType = @DateType(type = DateType.Type.DATE))
    )
    private LocalDate deliveryDate;

    @FabosJsonField(
            views = @View(title = "进度"),
            edit = @Edit(title = "进度", show = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = QuestionPaperMeasureProgressEnum.class))
    )
    private String progress;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "完成日期", type = ViewType.DATE),
            edit = @Edit(title = "完成日期", notNull = true, dateType = @DateType(type = DateType.Type.DATE))
    )
    private LocalDate completeDate;

    @FabosJsonField(
            views = @View(title = "完成佐证材料"),
            edit = @Edit(title = "完成佐证材料", type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(maxLimit = 10))
    )
    private String completeAttachment;

}
