package cec.jiutian.bc.changeRequestManagement.domain.ecn.model;

import cec.jiutian.bc.changeRequestManagement.enums.VerificationContentEnum;
import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Power;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@Entity
@Table(name = "qms_change_request_verify")
@FabosJson(
        name = "变更事项",
        orderBy = "createTime asc",
        power = @Power(add = false, edit = false, delete = false, export = false)

)
public class VerificationIDetail extends BaseModel {

    @Column(unique = true,length = 40)
    @FabosJsonField(
            views = @View(title = "单号", index = 0),
            edit = @Edit(title = "单号",
                    readonly = @Readonly(add = true, edit = true),
                    notNull = true,
                    search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "变更切换日期", type = ViewType.DATE_TIME),
            edit = @Edit(
                    title = "变更切换日期",
                    notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @Column(nullable = false)
    private Date changeDate;

    @FabosJsonField(
            views = @View(title = "计划变更执行日期", type = ViewType.DATE_TIME),
            edit = @Edit(
                    title = "计划变更执行日期",
                    notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @Column(nullable = false)
    private Date executeDate;

    @FabosJsonField(
            views = @View(title = "方案验证内容"),
            edit = @Edit(
                    title = "方案验证内容",
                    notNull = true,
                    type = EditType.multiple_select,
                    choiceType = @ChoiceType(fetchHandler = VerificationContentEnum.class)
            )
    )
    private String verificationContent;

    @FabosJsonField(
            views = @View(title = "附件"),
            edit = @Edit(title = "附件",
                    inputType = @InputType(length = 400),
                    type = EditType.ATTACHMENT,
                    tips = "最大支持5个100M文件",
                    attachmentType = @AttachmentType(
                            size = 100 * 1024,
                            maxLimit = 5,
                            type = AttachmentType.Type.BASE)
            )
    )
    @Column(length = 400)
    private String files;

    @FabosJsonField(
            views = @View(title = "ECN_ID", show = false),
            edit = @Edit(
                    show = false,
                    title = "ECN_ID"
            )
    )
    @Column(name = "ecn_id", length = 40, nullable = false)
    private String ecnId;
}
