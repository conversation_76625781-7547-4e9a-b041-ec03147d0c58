package cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.MyIssueTasks.model;

import cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.MyIssueTasks.handler.MyIssueTaskRUNOperationHandler;
import cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.MyIssueTasks.handler.MyIssueTaskSubmitOperationHandler;
import cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.MyIssueTasks.proxy.MyIssueTaskDataProxy;
import cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.issueList.model.IssueListCorrection;
import cec.jiutian.bc.qualityExceptionManagement.enumeration.inspectionIssuesImproveAndTrace.CorrectiveActionEnum;
import cec.jiutian.bc.qualityExceptionManagement.enumeration.inspectionIssuesImproveAndTrace.InsResultEnum;
import cec.jiutian.bc.qualityExceptionManagement.enumeration.inspectionIssuesImproveAndTrace.IssueListStatusEnum;
import cec.jiutian.bc.qualityExceptionManagement.enumeration.inspectionIssuesImproveAndTrace.SeverityLevelEnum;
import cec.jiutian.bc.urm.domain.org.entity.Org;
import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.frame.module.MetaUser;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@FabosJson(
        name = "我的整改任务",
        orderBy = "MyIssueTask.createTime desc",
        power = @Power(add = false, delete = false, edit = false, viewDetails = false),
        filter = @Filter("status = 'RUNNING'"),
        dataProxy = MyIssueTaskDataProxy.class,
        rowOperation = {
                @RowOperation(
                        title = "执行",
                        code = "MyIssueTask@RUN",
                        mode = RowOperation.Mode.SINGLE,
                        type = RowOperation.Type.POPUP,
                        operationHandler = MyIssueTaskRUNOperationHandler.class,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = MyIssueTaskRUN.class,
                        ifExpr = "rowOperationAuthFlag == 0",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "MyIssueTask@RUN"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
                @RowOperation(
                        title = "提交",
                        code = "MyIssueTask@SUBMIT",
                        operationParam={"VERIFIED"},
                        mode = RowOperation.Mode.SINGLE,
                        operationHandler = MyIssueTaskSubmitOperationHandler.class,
                        callHint = "请确认是否提交？",
                        ifExpr = "rowOperationAuthFlag == 0",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "MyIssueTask@SUBMIT"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                )
        }

)
@Table(name = "qms_qem_issue_list")
@Entity
@Getter
@Setter
public class MyIssueTask extends BaseModel {

    @FabosJsonField(
            views = @View(title = "编号"),
            edit = @Edit(title = "编号", search = @Search(vague = true))
    )
    private String generalCode;


    @FabosJsonField(
            views = @View(title = "任务名称"),
            edit = @Edit(title = "任务名称", search = @Search(vague = true), readonly = @Readonly)
    )
    private String name;

    @FabosJsonField(
            views = @View(title = "任务描述"),
            edit = @Edit(title = "任务描述", readonly = @Readonly)
    )
    private String description;

    @FabosJsonField(
            views = @View(title = "巡检标准"),
            edit = @Edit(title = "巡检标准", readonly = @Readonly)
    )
    private String inspectionStandard;

    @FabosJsonField(
            views = @View(title = "巡检项目"),
            edit = @Edit(title = "巡检项目", readonly = @Readonly)
    )
    private String inspectionProject;

    @FabosJsonField(
            views = @View(title = "巡检内容", toolTip = true),
            edit = @Edit(title = "巡检内容", search = @Search(vague = true), readonly = @Readonly)
    )
    private String content;

    @FabosJsonField(
            views = @View(title = "巡检结果", toolTip = true),
            edit = @Edit(title = "巡检结果",readonly = @Readonly,type= EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler =  InsResultEnum.class))
    )
    private String insResult;

    @FabosJsonField(
            views = @View(title = "问题描述"),
            edit = @Edit(title = "问题描述")
    )
    private String issueDescription;

    //严重程度  轻微 一般 严重
    @FabosJsonField(
            views = @View(title = "严重程度"),
            edit = @Edit(title = "严重程度",type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = SeverityLevelEnum.class))
    )
    private String severityLevel;

    // 待发布 待执行 执行中 待验证 已完成
    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态", type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = IssueListStatusEnum.class))
    )
    private String status;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "责任部门", column = "name"),
            edit = @Edit(title = "责任部门",notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"))
    )
    @JoinColumn(foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private Org org;
    @ManyToOne
    @FabosJsonField(
            views = @View(title = "责任人", column = "name"),
            edit = @Edit(title = "责任人",notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"))
    )
    @JoinColumn(foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private MetaUser metaUser;

    // todo 关联单据后 回显
    @FabosJsonField(
            views = @View(title = "分析对策"),
            edit = @Edit(title = "分析对策",type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = CorrectiveActionEnum.class))
    )
    private String correctiveAction;

    // todo 先留白
    @FabosJsonField(
            views = @View(title = "关联单据号"),
            edit = @Edit(title = "关联单据号")
    )
    private String relatedDocumentNumber;

    @FabosJsonField(
            views = @View(title = "措施纳期"),
            edit = @Edit(title = "措施纳期", dateType = @DateType(type = DateType.Type.DATE_TIME))
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date measuresDeadline;

    @FabosJsonField(
            views = @View(title = "问题重复次数"),
            edit = @Edit(title = "问题重复次数", numberType = @NumberType(min = 0), notNull = true)
    )
    private Integer issueRepeatedTimes;

    @FabosJsonField(
            views = @View(title = "问题附件"),
            edit = @Edit(title = "问题附件")
    )
    private String attachment;

    @FabosJsonField(
            views = @View(title = "原因分析", toolTip = true),
            edit = @Edit(title = "原因分析",type = EditType.TEXTAREA)
    )
    private String causeAnalysis;

    @FabosJsonField(
            views = @View(title = "整改任务下所有责任人的id集合,问题分析时写入", show = false),
            edit = @Edit(title = "整改任务下所有责任人的id集合,问题分析时写入",show = false)
    )
    private String allUserIds;

    // 子表
    @FabosJsonField(
            edit = @Edit(title = "整改措施", type = EditType.TAB_TABLE_ADD),
            views = @View(title = "整改措施", column = "correction", type= ViewType.TABLE_VIEW)
    )
    @JoinColumn(name = "issue_list_id")
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    private List<IssueListCorrection> issueListCorrections;

}
