package cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.mto;

import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.enumration.CpmBusinessStateEnum;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.enumration.ProgressEnum;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.handler.CorrectPreventMeasureDynamicHandler;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.proxy.CorrectPreventMeasureCorrectMTOProxy;
import cec.jiutian.bc.mto.OrgMTO;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/28
 * @description TODO
 */
@FabosJson(
        name = "纠正",
        dataProxy = CorrectPreventMeasureCorrectMTOProxy.class

)
@Table(name = "qms_cpm_correct_prevent_measure")
@Entity
@Getter
@Setter
public class CorrectPreventMeasureCorrectMTO extends MetaModel {
    @FabosJsonField(
            views = @View(title = "单据编号", show = false),
            edit = @Edit(title = "单据编号",
                    readonly = @Readonly(edit = false),
                    show = false
            ),
            dynamicField = @DynamicField(dependFiled = @DependFiled(buttonName = "生成", changeBy = "id",
                    dynamicHandler = CorrectPreventMeasureDynamicHandler.class))
    )
    private String correctPreventMeasureFormNumber;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",
                    show = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = CpmBusinessStateEnum.class)
            )
    )
    private String businessState;

    @FabosJsonField(
            views = @View(title = "责任部门", column = "name"),
            edit = @Edit(title = "责任部门",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "id")
            )
    )
    @ManyToOne
    @JoinColumn(name = "response_department_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private OrgMTO orgMTO;

    //TODO: 这个框到底是什么
    @FabosJsonField(
            views = @View(title = "原因分析纳期"),
            edit = @Edit(title = "原因分析纳期",
                    dateType = @DateType(type = DateType.Type.DATE_TIME),
                    notNull = true
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date reasonAnalysisDeliveryTime;

    @FabosJsonField(
            views = @View(title = "整改任务下所有责任人的id集合,我的整改任务查询数据使用", show = false),
            edit = @Edit(title = "整改任务下所有责任人的id集合,我的整改任务查询数据使用",show = false)
    )
    private String correctAllUserIds;

    @FabosJsonField(
            edit = @Edit(title = "纠正措施", type = EditType.TAB_TABLE_ADD),
            views = @View(title = "纠正措施", type= ViewType.TABLE_VIEW)
    )
    @JoinColumn(name = "qms_cpm_correct_prevent_measure_id")
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy
    private List<CorrectiveActionCreationMTO> correctiveActionList;

    @FabosJsonField(
            views = @View(title = "纠正状态", show = false),
            edit = @Edit(title = "纠正状态",
                    readonly = @Readonly(),
                    type = EditType.CHOICE,
                    show = false,
                    choiceType = @ChoiceType(fetchHandler = ProgressEnum.class)
            )
    )
    private String correctiveState;
}
