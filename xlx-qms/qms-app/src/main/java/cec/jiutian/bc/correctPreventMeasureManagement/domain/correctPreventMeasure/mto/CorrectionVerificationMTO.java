package cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.mto;

import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.enumration.ImprovingProgressEnum;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.enumration.VerificationResultEnum;
import cec.jiutian.bc.materialInspect.domain.publicInspectionTask.mto.UserForInsTaskMTO;
import cec.jiutian.bc.mto.OrgMTO;
import cec.jiutian.bc.urm.domain.user.entity.User;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.RowBaseOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/4/29
 * @description TODO
 */
@FabosJson(
        name = "纠正验证",
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "1==1",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.HIDE

                ),
                @RowBaseOperation(
                        code = "add",
                        ifExpr = "1==1",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.HIDE
                )
        }
)
@Table(name = "qms_cpm_corrective_action")
@Entity
@Data
public class CorrectionVerificationMTO extends MetaModel {
    @ManyToOne
    @FabosJsonField(
            views = {
                    @View(title = "预防纠正措施", column = "correctPreventMeasureFormNumber", show = false)
            },
            edit = @Edit(title = "预防纠正措施", type = EditType.REFERENCE_TABLE, show = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "correctPreventMeasureFormNumber")
            )
    )
    @JsonIgnoreProperties("correctiveActionList")
    private CorrectPreventMeasureCorrectVeriMTO correctPreventMeasureCorrectVeriMTO;

    @FabosJsonField(
            views = @View(title = "纠正"),
            edit = @Edit(title = "纠正", readonly = @Readonly())
    )
    private String correct;

    @FabosJsonField(
            views = @View(title = "选择责任部门", column = "name"),
            edit = @Edit(title = "选择责任部门",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    readonly = @Readonly(),
                    referenceTableType = @ReferenceTableType(type = TabTableReferType.SelectShowTypeMTM.LIST)
            )
    )
    @ManyToOne
    @JoinColumn(name = "response_department_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private OrgMTO orgMTO;

    @FabosJsonField(
            views = @View(title = "责任人", column = "name"),
            edit = @Edit(title = "责任人",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    readonly = @Readonly(),
                    referenceTableType = @ReferenceTableType(type = TabTableReferType.SelectShowTypeMTM.LIST)
            )
    )
    @ManyToOne
    @JoinColumn(name = "person_responsible_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private UserForInsTaskMTO userForInsTaskMTO;

    @FabosJsonField(
            views = @View(title = "纳期"),
            edit = @Edit(title = "纳期",
                    readonly = @Readonly(),
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date deliveryTime;

    @FabosJsonField(
            views = @View(title = "改善进度"),
            edit = @Edit(title = "改善进度",
                    type = EditType.CHOICE,
                    readonly = @Readonly(),
                    choiceType = @ChoiceType(fetchHandler = ImprovingProgressEnum.class)
            )
    )
    private String improvingProgress;

    @FabosJsonField(
            views = @View(title = "完成日期"),
            edit = @Edit(title = "完成日期",
                    readonly = @Readonly(),
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date completionDate;

    @FabosJsonField(
            views = @View(title = "完成佐证材料"),
            edit = @Edit(title = "完成佐证材料", type = EditType.ATTACHMENT,
                    readonly = @Readonly(),
                    attachmentType = @AttachmentType)
    )
    private String completeSupportMaterial;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "验证人", column = "name"),
            edit = @Edit(title = "验证人",
                    type = EditType.REFERENCE_TABLE,
                    search = @Search(),
                    show = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name")
            )
    )
    private User verifiedUser;

    @FabosJsonField(
            views = @View(title = "验证结果"),
            edit = @Edit(title = "验证结果",
                    type = EditType.CHOICE,
                    notNull = true,
                    choiceType = @ChoiceType(fetchHandler = VerificationResultEnum.class)
            )
    )
    private String verificationResult;

    @FabosJsonField(
            views = @View(title = "验证日期"),
            edit = @Edit(title = "验证日期",
                    notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date verificationDate;

    @FabosJsonField(
            views = @View(title = "验证佐证材料"),
            edit = @Edit(title = "验证佐证材料",
                    type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType)
    )
    private String verificationSupportMaterial;
}
