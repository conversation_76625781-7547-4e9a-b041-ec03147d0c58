package cec.jiutian.bc.qualityPreparationPlan.domain.model;

import cec.jiutian.bc.quantityPreparationPlan.model.QuantityPreparationDetailItem;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.DateType;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowBaseOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;
import java.util.Objects;

@Table(name = "qms_quality_preparation_plan_detail")
@Entity
@Getter
@Setter
@FabosJson(
        name = "质量准备计划详情",
        orderBy = "QualityPreparationPlanDetail.date desc",
        power = @Power(add = false, edit = false, delete = false),
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "1 == 1",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                )
        }
)
public class QualityPreparationPlanDetail extends MetaModel {
    // 日期
    @FabosJsonField(
            views = @View(title = "日期"),
            edit = @Edit(title = "日期", notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE))
    )
    private Date date;

    // 检验任务总数
    @FabosJsonField(
            views = @View(title = "检验任务总数", rowEdit = true),
            edit = @Edit(title = "检验任务总数", notNull = true,
                    numberType = @NumberType(min = 0))
    )
    private Integer totalDetectionTasks;

    // 检验项明细
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "qms_quality_preparation_plan_detail_id")
    @FabosJsonField(
            edit = @Edit(title = "检验项明细", type = EditType.TAB_TABLE_ADD),
            views = @View(title = "检验项明细", type = ViewType.TABLE_VIEW)
    )
    private List<QualityPreparationDetailItem> qualityPreparationDetailItemList;
}
