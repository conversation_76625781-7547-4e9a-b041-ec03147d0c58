package cec.jiutian.bc.inventoryInspection.statistics.util;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.Optional;

public class TimeRangeCalculator {
//
//    public static void main(String[] args) {
//        // 示例使用
//        TimeRange range = calculateTimeRange(
//                "1",              // IS_DIY_TIME
//                "2025-01-01",     // START_TIME
//                "2025-12-31",     // END_TIME
//                null              // QUERY (自定义时间时忽略)
//        );
//        System.out.println("自定义时间范围: " + range.getFormattedRange());
//
//        range = calculateTimeRange("0", null, null, "1");
//        System.out.println("本年范围: " + range.getFormattedRange());
//
//        range = calculateTimeRange("0", null, null, "2");
//        System.out.println("本季度范围: " + range.getFormattedRange());
//
//        range = calculateTimeRange("0", null, null, "3");
//        System.out.println("本月范围: " + range.getFormattedRange());
//
//        range = calculateTimeRange("0", null, null, "4");
//        System.out.println("本周范围: " + range.getFormattedRange());
//
//        range = calculateTimeRange("0", null, null, "5");
//        System.out.println("当天范围: " + range.getFormattedRange());
//
//        range = calculateTimeRange("0", null, null, "6");
//        System.out.println("全部时间: " + range.getFormattedRange());
//    }

    public static TimeRange calculateTimeRange(
            String isDiyTime,
            String startTime,
            String endTime,
            String query
    ) {
        // 处理自定义时间范围
        if ("1".equals(isDiyTime)) {
            return parseCustomTimeRange(startTime, endTime);
        }

        // 处理预定义查询类型
        if (query == null) {
            return TimeRange.allTime(); // 默认全部时间
        }

        return switch (query) {
            case "1" -> getYearRange();
            case "2" -> getQuarterRange();
            case "3" -> getMonthRange();
            case "4" -> getWeekRange();
            case "5" -> getDayRange();
            default -> TimeRange.allTime(); // 其他情况返回全部时间
        };
    }

    // 解析自定义时间范围
    private static TimeRange parseCustomTimeRange(String start, String end) {
        LocalDateTime startTime = parseDateTime(start).orElseThrow(
                () -> new IllegalArgumentException("Invalid START_TIME format")
        );
        LocalDateTime endTime = parseDateTime(end).orElseThrow(
                () -> new IllegalArgumentException("Invalid END_TIME format")
        );
        return new TimeRange(startTime, endTime);
    }

    // 解析日期时间字符串（支持多种格式）
    private static Optional<LocalDateTime> parseDateTime(String datetime) {
        if (datetime == null || datetime.trim().isEmpty()) {
            return Optional.empty();
        }

        // 尝试解析为完整时间格式（yyyy-MM-dd HH:mm:ss）
        try {
            return Optional.of(LocalDateTime.parse(datetime,
                    DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        } catch (Exception ignore) {
        }

        // 尝试解析为日期格式（yyyy-MM-dd）
        try {
            return Optional.of(LocalDate.parse(datetime,
                    DateTimeFormatter.ISO_LOCAL_DATE).atStartOfDay());
        } catch (Exception ignore) {
        }

        return Optional.empty();
    }

    // 本年范围（年初 00:00:00 到年末 23:59:59）
    private static TimeRange getYearRange() {
        LocalDate now = LocalDate.now();
        LocalDateTime start = now.withDayOfYear(1).atStartOfDay();
        LocalDateTime end = now.with(TemporalAdjusters.lastDayOfYear())
                .atTime(LocalTime.MAX).withNano(0);
        return new TimeRange(start, end);
    }

    // 本季度范围
    private static TimeRange getQuarterRange() {
        LocalDate now = LocalDate.now();
        int quarter = (now.getMonthValue() - 1) / 3 + 1;
        Month startMonth = Month.of((quarter - 1) * 3 + 1);

        LocalDate startDate = LocalDate.of(now.getYear(), startMonth, 1);
        LocalDate endDate = startDate.plusMonths(2)
                .with(TemporalAdjusters.lastDayOfMonth());

        return new TimeRange(
                startDate.atStartOfDay(),
                endDate.atTime(LocalTime.MAX).withNano(0)
        );
    }

    // 本月范围
    private static TimeRange getMonthRange() {
        LocalDate now = LocalDate.now();
        LocalDateTime start = now.withDayOfMonth(1).atStartOfDay();
        LocalDateTime end = now.with(TemporalAdjusters.lastDayOfMonth())
                .atTime(LocalTime.MAX).withNano(0);
        return new TimeRange(start, end);
    }

    // 本周范围（周一 00:00:00 到周日 23:59:59）
    private static TimeRange getWeekRange() {
        LocalDate now = LocalDate.now();
        LocalDate monday = now.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        LocalDate sunday = monday.plusDays(6);
        return new TimeRange(
                monday.atStartOfDay(),
                sunday.atTime(LocalTime.MAX).withNano(0)
        );
    }

    // 当天范围（00:00:00 到 23:59:59）
    private static TimeRange getDayRange() {
        LocalDateTime today = LocalDateTime.now();
        return new TimeRange(
                today.toLocalDate().atStartOfDay(),
                today.toLocalDate().atTime(LocalTime.MAX).withNano(0)
        );
    }

    // 时间范围值对象
    static class TimeRange {
        private static final DateTimeFormatter FORMATTER =
                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        private final LocalDateTime start;
        private final LocalDateTime end;
        private final boolean isAllTime;

        TimeRange(LocalDateTime start, LocalDateTime end) {
            this.start = start;
            this.end = end;
            this.isAllTime = false;
        }

        // 全部时间构造函数
        private TimeRange() {
            this.start = null;
            this.end = null;
            this.isAllTime = true;
        }

        static TimeRange allTime() {
            return new TimeRange();
        }

        public boolean isAllTime() {
            return isAllTime;
        }

        // 获取格式化后的时间范围字符串
        public String getFormattedRange() {
            if (isAllTime) {
                return "ALL_TIME";
            }
            return String.format("%s ~ %s",
                    start.format(FORMATTER),
                    end.format(FORMATTER)
            );
        }

        // 单独获取开始时间字符串
        public String getFormattedStart() {
            return isAllTime ? "" : start.format(FORMATTER);
        }

        // 单独获取结束时间字符串
        public String getFormattedEnd() {
            return isAllTime ? "" : end.format(FORMATTER);
        }
    }
}