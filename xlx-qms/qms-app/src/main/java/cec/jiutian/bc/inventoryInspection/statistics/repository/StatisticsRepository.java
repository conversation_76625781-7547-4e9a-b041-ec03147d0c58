package cec.jiutian.bc.inventoryInspection.statistics.repository;

import cec.jiutian.bc.inventoryInspection.statistics.ao.StatisticsCommonParam;

import java.util.List;

public interface StatisticsRepository {
    List<Object[]> queryPassTrendData(StatisticsCommonParam param);

    List<Object[]> queryLinePassRateData(StatisticsCommonParam param);

    List<Object[]> queryWorkshopPassRate(StatisticsCommonParam param);

    List<Object[]> queryFactoryPassRate(StatisticsCommonParam param);

    List<Object[]> queryCategoryPassRate(StatisticsCommonParam param);

    List<Object[]> queryCodePassRate(StatisticsCommonParam param);

    List<Object[]> queryInsItemPassRate(StatisticsCommonParam param);
}
