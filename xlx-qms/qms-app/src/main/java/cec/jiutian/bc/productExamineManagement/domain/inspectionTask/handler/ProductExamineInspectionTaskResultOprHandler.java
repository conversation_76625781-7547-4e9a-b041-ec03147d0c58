package cec.jiutian.bc.productExamineManagement.domain.inspectionTask.handler;

import cec.jiutian.bc.productExamineManagement.domain.inspectionTask.model.ProductExamineInspectionItemDetail;
import cec.jiutian.bc.productExamineManagement.domain.inspectionTask.model.ProductExamineInspectionTask;
import cec.jiutian.bc.productExamineManagement.domain.mto.ProductExamineInspectionItemDetailResultMTO;
import cec.jiutian.bc.productExamineManagement.domain.mto.ProductExamineInspectionTaskResultMTO;
import cec.jiutian.bc.productReturnInspection.enumeration.InspectionResultEnum;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/6/24
 * @description TODO
 */
@Component
public class ProductExamineInspectionTaskResultOprHandler implements OperationHandler<ProductExamineInspectionTask, ProductExamineInspectionTaskResultMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<ProductExamineInspectionTask> data, ProductExamineInspectionTaskResultMTO modelObject, String[] param) {
        ProductExamineInspectionTask productExamineInspectionTask = data.get(0);
        String currentState = OrderCurrentStateEnum.Enum.COMPLETE.name();
        for (ProductExamineInspectionItemDetail detail : productExamineInspectionTask.getStandardDetailList()) {
            Optional<ProductExamineInspectionItemDetailResultMTO> optional = modelObject.getDetailResultMTOList().stream().filter(m -> detail.getId().equals(detail.getId())).findAny();
            if (optional.isPresent()) {
                if (InspectionResultEnum.Enum.UNQUALIFIED.name().equals(detail.getInspectionResult())) {
                    currentState = OrderCurrentStateEnum.Enum.EDIT.name();
                }
                detail.setInspectionResult(optional.get().getInspectionResult());
                detail.setUnQualifiedLevel(optional.get().getUnQualifiedLevel());
            }
        }
        productExamineInspectionTask.setCurrentState(currentState);
        fabosJsonDao.mergeAndFlush(productExamineInspectionTask);
        return "alert(操作成功)";
    }

    @Override
    public ProductExamineInspectionTaskResultMTO fabosJsonFormValue(List<ProductExamineInspectionTask> data, ProductExamineInspectionTaskResultMTO fabosJsonForm, String[] param) {
        ProductExamineInspectionTask productExamineInspectionTask = data.get(0);
        if (CollectionUtils.isNotEmpty(productExamineInspectionTask.getStandardDetailList())) {
            fabosJsonForm.setProductName(productExamineInspectionTask.getSpecificationManageMTO().getName());
            fabosJsonForm.setLotSerialId(productExamineInspectionTask.getInventoryMTO().getLotSerialId());
            fabosJsonForm.setWorkShopName(productExamineInspectionTask.getWorkshop().getFactoryAreaName());
            fabosJsonForm.setProductLineName(productExamineInspectionTask.getProductionLine().getFactoryAreaName());
            List<ProductExamineInspectionItemDetailResultMTO> detailResultMTOList = new ArrayList<>();
            productExamineInspectionTask.getStandardDetailList().forEach(detail -> {
                ProductExamineInspectionItemDetailResultMTO detailResultMTO = new ProductExamineInspectionItemDetailResultMTO();
                detailResultMTO.setId(detail.getId());
                detailResultMTO.setName(detail.getName());
                detailResultMTO.setFeature(detail.getFeature());
                detailResultMTO.setItemType(detail.getItemType());
                detailResultMTO.setSamplingPoint(detail.getSamplingPoint());
                detailResultMTO.setSendInspectPoint(detail.getSendInspectPoint());
                detailResultMTOList.add(detailResultMTO);
            });
            fabosJsonForm.setDetailResultMTOList(detailResultMTOList);
        }
        return fabosJsonForm;
    }
}
