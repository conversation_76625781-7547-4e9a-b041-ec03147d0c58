package cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.handler;

import cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.enumration.AuditStatusEnum;
import cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.enumration.TaskBusinessStateEnum;
import cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.model.AbnormalFeedbackHandling;
import cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.mto.AbnormalFeedbackHandlingSchemeMTO;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/27
 * @description TODO
 */
@Component
public class AbnormalFeedbackHandlingSchemeHandler implements OperationHandler<AbnormalFeedbackHandling, AbnormalFeedbackHandlingSchemeMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<AbnormalFeedbackHandling> data, AbnormalFeedbackHandlingSchemeMTO modelObject, String[] param) {
        if (modelObject != null) {
            AbnormalFeedbackHandling model = data.get(0);
            BeanUtil.copyProperties(modelObject, model, "createBy", "createTime");
            if (modelObject.getEffectVerification().equals(AuditStatusEnum.Enum.PASS.name())) {
                model.setBusinessState(TaskBusinessStateEnum.Enum.COMPLETED.name());
            } else {
                model.setBusinessState(TaskBusinessStateEnum.Enum.PENDING_REVIEW.name());
            }
            fabosJsonDao.mergeAndFlush(model);
        }
        return "alert(操作成功)";
    }

    @Override
    public AbnormalFeedbackHandlingSchemeMTO fabosJsonFormValue(List<AbnormalFeedbackHandling> data, AbnormalFeedbackHandlingSchemeMTO fabosJsonForm, String[] param) {
        AbnormalFeedbackHandling abnormalFeedbackHandling = data.get(0);
        BeanUtil.copyProperties(abnormalFeedbackHandling, fabosJsonForm);

        return fabosJsonForm;
    }
}
