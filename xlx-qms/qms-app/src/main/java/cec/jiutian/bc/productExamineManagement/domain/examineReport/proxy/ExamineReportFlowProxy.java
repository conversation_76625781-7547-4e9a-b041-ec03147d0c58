package cec.jiutian.bc.productExamineManagement.domain.examineReport.proxy;

import cec.jiutian.bc.flow.enums.ExamineStatusEnum;
import cec.jiutian.bc.productExamineManagement.domain.examineReport.model.ExamineReport;
import cec.jiutian.bc.productExamineManagement.enumration.ImplementPlanHandleResultEnum;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.FlowProxy;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class ExamineReportFlowProxy extends FlowProxy {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public void onEvent(Object event, Object data) {
        if (data instanceof ExamineReport entity) {
            if (entity.getExamineStatus().equals(ExamineStatusEnum.AUDITED.getCode())) {
                entity.setCurrentState(OrderCurrentStateEnum.Enum.COMPLETE.name());
            }
            fabosJsonDao.mergeAndFlush(entity);
        }
    }

    @Override
    public void beforeSubmit(Object data) {
        if (data instanceof ExamineReport entity) {
            if (ImplementPlanHandleResultEnum.Enum.Correct.name().equals(entity.getHandleResult())) {
                if (entity.getCorrectPreventMeasure() == null) {
                    throw new FabosJsonApiErrorTip("未录入预防措施，不可发起审批");
                }
            }
        }
    }

    @Override
    public void afterSubmit(Object data) {
        if (data instanceof ExamineReport entity) {
            entity.setCurrentState(OrderCurrentStateEnum.Enum.EXECUTE.name());
            fabosJsonDao.mergeAndFlush(entity);
        }
    }

}
