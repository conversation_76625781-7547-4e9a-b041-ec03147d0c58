package cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.issueList.proxy;

import cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.issueList.model.IssueListADD;
import cec.jiutian.bc.qualityExceptionManagement.enumeration.inspectionIssuesImproveAndTrace.IssueListStatusEnum;
import cec.jiutian.view.fun.DataProxy;
import org.springframework.stereotype.Component;

@Component
public class IssueListADDProxy implements DataProxy<IssueListADD> {

    @Override
    public void beforeAdd(IssueListADD issueListADD) {
        issueListADD.setStatus(IssueListStatusEnum.Enum.WAIT_PUBLISH.name());
    }
}
