package cec.jiutian.bc.correctPreventMeasureManagement.remote.provider;

import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.enumration.ImprovingProgressEnum;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.model.PreventMeasure;
import cec.jiutian.bc.ecs.dto.SendMsgGroupDTO;
import cec.jiutian.bc.ecs.provider.EcsMessageProvider;
import cec.jiutian.bc.job.provider.IJobProvider;
import cec.jiutian.core.frame.annotation.FabosCustomizedService;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.meta.FabosJob;
import cn.hutool.core.date.DateUtil;
import jakarta.annotation.Resource;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@FabosCustomizedService(value = PreventMeasure.class)
@Slf4j
@Component
@Transactional
public class PreventOverDateScheduleProvider implements IJobProvider {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private EcsMessageProvider ecsMessageProvider;

    @FabosJob(comment = "预防措施定时任务")
    @Override
    public String exec(String code, String param) {
        PreventMeasure condition = new PreventMeasure();
        condition.setImprovingProgress(ImprovingProgressEnum.Enum.TO_BE_CORRECTED.name());
        List<PreventMeasure> pm = fabosJsonDao.select(condition);
        for (PreventMeasure preventMeasure : pm) {
            if (checkDate(preventMeasure.getDeliveryTime())) {
                sendQms(preventMeasure);
            }
        }
        return "";
    }

    private boolean checkDate(Date deliveryTime) {
        int compare = DateUtil.compare(deliveryTime, DateUtil.date(), "yyyy-MM-dd HH");
        return (compare <=0 );
    }

    private void sendQms(PreventMeasure preventMeasure) {
        SendMsgGroupDTO sendMsgGroupDTO = new SendMsgGroupDTO();
        sendMsgGroupDTO.setMessageGroupCode("PreventOverTimeInfo");
        sendMsgGroupDTO.setContent("请尽快完成预防措施["+preventMeasure.getSystematicMeasures()+"]的处理,纳期时间["+preventMeasure.getDeliveryTime()+"]");
        //log.warn("请尽快完成预防措施["+preventMeasure.getSystematicMeasures()+"]的处理,纳期时间["+preventMeasure.getDeliveryTime()+"]");
        ecsMessageProvider.sendGroupMessage(sendMsgGroupDTO);
    }
}
