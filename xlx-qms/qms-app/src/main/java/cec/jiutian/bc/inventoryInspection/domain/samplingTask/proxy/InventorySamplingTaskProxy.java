package cec.jiutian.bc.inventoryInspection.domain.samplingTask.proxy;

import cec.jiutian.bc.inventoryInspection.domain.samplingTask.model.InventorySamplingTask;
import cec.jiutian.view.fun.DataProxy;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/3/10
 * @description TODO
 */
@Component
public class InventorySamplingTaskProxy implements DataProxy<InventorySamplingTask> {

    @Override
    public void beforeAdd(InventorySamplingTask inventorySamplingTask) {
        DataProxy.super.beforeAdd(inventorySamplingTask);
    }
}
