package cec.jiutian.bc.deliveryInspection.domain.inspectionTask.handler;

import cec.jiutian.bc.deliveryInspection.domain.inspectionTask.model.DeliveryInspectionItemDetail;
import cec.jiutian.bc.deliveryInspection.domain.inspectionTask.model.DeliveryInspectionTask;
import cec.jiutian.bc.modeler.domain.inspectionItem.model.InspectionItem;
import cec.jiutian.view.ReferenceAddType;
import cn.hutool.core.bean.BeanUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/3/26
 * @description TODO
 */
@Component
public class DeliveryInspectionItemReferHandler implements ReferenceAddType.ReferenceAddHandler<DeliveryInspectionTask, InspectionItem> {
    @Override
    public Map<String, Object> handle(DeliveryInspectionTask deliveryInspectionTask, List<InspectionItem> inspectionItems) {
        Map<String, Object> result = new HashMap<>();
        if (CollectionUtils.isNotEmpty(inspectionItems)) {
            List<DeliveryInspectionItemDetail> list = new ArrayList<>();
            inspectionItems.forEach(inspectionItem -> {
                DeliveryInspectionItemDetail deliveryInspectionItemDetail = new DeliveryInspectionItemDetail();
                BeanUtil.copyProperties(inspectionItem, deliveryInspectionItemDetail);
                deliveryInspectionItemDetail.setId(null);
                deliveryInspectionItemDetail.setItemId(inspectionItem.getId());
                list.add(deliveryInspectionItemDetail);
            });
            result.put("standardDetailList", list);
        }
        return result;
    }
}
