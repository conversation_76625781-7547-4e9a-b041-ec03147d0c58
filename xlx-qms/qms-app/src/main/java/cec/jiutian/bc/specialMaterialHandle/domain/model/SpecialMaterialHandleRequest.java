package cec.jiutian.bc.specialMaterialHandle.domain.model;

import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleModel;
import cec.jiutian.bc.materialInspect.domain.publicInspectionTask.mto.UserForInsTaskMTO;
import cec.jiutian.bc.mto.OrgMTO;
import cec.jiutian.bc.modeler.enumration.NamingRuleCodeEnum;
import cec.jiutian.bc.specialMaterialHandle.domain.handler.HandleRequestVerifyOperationHandler;
import cec.jiutian.bc.specialMaterialHandle.domain.handler.MaterialDetailReferenceAddHandler;
import cec.jiutian.bc.specialMaterialHandle.domain.handler.SpecialMaterialHandlePublishHandler;
import cec.jiutian.bc.specialMaterialHandle.domain.proxy.SpecialMaterialHandleRequestFlowProxy;
import cec.jiutian.bc.specialMaterialHandle.domain.proxy.SpecialMaterialHandleRequestProxy;
import cec.jiutian.bc.specialMaterialHandle.enumeration.SpecialMaterialBusinessStatusEnum;
import cec.jiutian.bc.specialMaterialHandle.mto.HandleRequestVerifyMTO;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.*;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowBaseOperation;
import cec.jiutian.view.type.RowOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/22
 * @description TODO
 */
@FabosJson(
        name = "特殊物料处理申请",
        orderBy = "SpecialMaterialHandleRequest.createTime desc",
        dataProxy = SpecialMaterialHandleRequestProxy.class,
        power = @Power(examine = true, examineDetails = true),
        flowCode = "SpecialMaterialHandleRequest",
        flowProxy = SpecialMaterialHandleRequestFlowProxy.class,
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "examine",
                        ifExpr = "!(businessState =='WAIT_AUDIT' && examineStatus =='0')",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "examineDetails",
                        ifExpr = "examineStatus =='0'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "businessState !='WAIT_RELEASE'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "businessState !='WAIT_RELEASE'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
        },
        rowOperation = {
                @RowOperation(
                        title = "发布",
                        code = "SpecialMaterialHandleRequest@PUBLISH",
                        operationHandler = SpecialMaterialHandlePublishHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "确定发布吗？",
                        ifExpr = "businessState != 'WAIT_RELEASE'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "SpecialMaterialHandleRequest@PUBLISH"
                        )
                ),
                @RowOperation(
                        title = "验证",
                        code = "SpecialMaterialHandleRequest@VERIFY",
                        operationHandler = HandleRequestVerifyOperationHandler.class,
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = HandleRequestVerifyMTO.class,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "SpecialMaterialHandleRequest@VERIFY"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "selectedItems[0].examineStatus !='1' || selectedItems[0].businessState !='WAIT_VERIFY'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
        }

)
@Table(name = "qms_special_material_handle_request",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Getter
@Setter
@TemplateType(type = "multiTable")
public class SpecialMaterialHandleRequest extends NamingRuleModel {

    @Override
    public String getNamingCode() {
        return NamingRuleCodeEnum.SpecialMaterialHandleRequest.name();
    }

    @FabosJsonField(
            views = @View(title = "物料指标"),
            edit = @Edit(title = "物料指标")
    )
    private String materialTarget;

    @FabosJsonField(
            views = @View(title = "使用风险"),
            edit = @Edit(title = "使用风险")
    )
    private String useRisk;

    @Transient
    @FabosJsonField(
            views = @View(title = "选择申请人", column = "name", show = false),
            edit = @Edit(
                    title = "选择申请人",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType()
            )
    )
    private UserForInsTaskMTO user;

    @FabosJsonField(
            views = @View(title = "申请人"),
            edit = @Edit(
                    title = "申请人",
                    inputType = @InputType(length = 20),
                    notNull = true,
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "user", beFilledBy = "name"))
    )
    private String applicant;

    @FabosJsonField(
            views = @View(title = "申请人ID", show = false),
            edit = @Edit(
                    show = false,
                    title = "申请人ID",
                    inputType = @InputType(length = 50)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "user", beFilledBy = "id"))
    )
    private String applicantId;

    @Transient
    @FabosJsonField(
            views = @View(title = "选择申请部门", column = "name", show = false),
            edit = @Edit(
                    title = "选择申请部门",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType()
            )
    )
    private OrgMTO org;

    @FabosJsonField(
            views = @View(title = "申请部门"),
            edit = @Edit(
                    title = "申请部门",
                    inputType = @InputType(length = 20),
                    notNull = true,
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "org", beFilledBy = "name"))
    )
    @Column(name = "apply_department", length = 20)
    private String applyDepartment;

    @FabosJsonField(
            views = @View(title = "申请部门ID", show = false),
            edit = @Edit(
                    show = false,
                    title = "申请部门ID"
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "org", beFilledBy = "id"))
    )
    @Column(name = "department_id", length = 50)
    private String departmentId;

    @FabosJsonField(
            views = @View(title = "申请日期"),
            edit = @Edit(title = "申请日期",
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date requestDate;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",readonly = @Readonly,type = EditType.CHOICE,
                    defaultVal = "WAIT_RELEASE",
                    choiceType = @ChoiceType(fetchHandler = SpecialMaterialBusinessStatusEnum.class)),
            dynamicField = @DynamicField(passive = true)
    )
    private String businessState;

    @FabosJsonField(
            views = @View(title = "备注"),
            edit = @Edit(title = "备注",
                    type = EditType.TEXTAREA)
    )
    private String remark;

    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumn(name = "special_material_handle_request_id")
    @OrderBy
    @FabosJsonField(
            edit = @Edit(title = "特殊物料处理申请明细", type = EditType.TAB_REFER_ADD),
            referenceAddType = @ReferenceAddType(referenceClass = "SpecialMaterialSourceMTO",
                    //filter = "currentState = 'normal' and materialCode = '${materialCode}' and materialName = '${materialName}'",
                    editable = {"storageLocation","handleType","chargePerson","remark"},
                    referenceAddHandler = MaterialDetailReferenceAddHandler.class),
            views = @View(title = "特殊物料处理申请明细", type = ViewType.TABLE_VIEW, extraPK = "inventoryId", column = "lotSerialId")
    )
    private List<SpecialMaterialHandleRequestDetail> detailList;
}
