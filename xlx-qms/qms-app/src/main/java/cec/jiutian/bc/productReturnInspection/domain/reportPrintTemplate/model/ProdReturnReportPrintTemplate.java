package cec.jiutian.bc.productReturnInspection.domain.reportPrintTemplate.model;

import cec.jiutian.bc.productReturnInspection.domain.reportPrintTemplate.handler.ProViewDetailHandler;
import cec.jiutian.bc.productReturnInspection.domain.reportPrintTemplate.mto.ProReturnInspectionTaskMTO;
import cec.jiutian.bc.productReturnInspection.domain.reportPrintTemplate.proxy.ProdReturnReportPrintTemplateDataProxy;
import cec.jiutian.bc.reportPrintTemplateSuper.ReportPrintTemplateSuper;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowOperation;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "mi_inspection_task")
@FabosJson(
        name = "成品退货检验报告",
        orderBy = "createTime desc",
        dataProxy = ProdReturnReportPrintTemplateDataProxy.class,
        filter = @Filter(value = "businessState = 'INSPECT_FINISH' and inspectionType = 'productReturnInspect'"),
        rowOperation = {
                @RowOperation(
                        code = "ProdReturnReportPrintTemplate@LOOK",
                        title = "查看",
                        type = RowOperation.Type.POPUP,
                        mode = RowOperation.Mode.HEADER,
                        ifExpr = "selectedItems[0].businessState !='INSPECT_FINISH'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = ProReturnInspectionTaskMTO.class,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        operationHandler = ProViewDetailHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "ProdReturnReportPrintTemplate@LOOK"
                        )
                )
        },
        power = @Power(add = false,edit = false,viewDetails = false, delete = false, print = true)
)
public class ProdReturnReportPrintTemplate extends ReportPrintTemplateSuper {

}
