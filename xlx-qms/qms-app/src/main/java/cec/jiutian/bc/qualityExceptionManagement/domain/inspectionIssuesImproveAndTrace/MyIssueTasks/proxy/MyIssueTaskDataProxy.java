package cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.MyIssueTasks.proxy;

import cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.MyIssueTasks.model.MyIssueTask;
import cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.issueList.model.IssueListCorrection;
import cec.jiutian.bc.qualityExceptionManagement.enumeration.inspectionIssuesImproveAndTrace.ProgressEnum;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import cec.jiutian.view.query.Condition;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

@Component
public class MyIssueTaskDataProxy implements DataProxy<MyIssueTask> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String beforeFetch(List<Condition> conditions) {
        // 查询当前用户
        String userId = UserContext.getUserId();
        return " MyIssueTask.allUserIds like '%"+userId+"%'";
    }

    @Override
    public void afterFetch(Collection<Map<String, Object>> list) {
        if(CollectionUtils.isNotEmpty(list)){
            list.forEach(d->{
                //判断权限标识  判断明细中整改任务的状态 progress 如果为 待验证   那么就不需要显示操作按钮
                String id = d.get("id").toString();
                MyIssueTask myIssueTask = fabosJsonDao.findById(MyIssueTask.class, id);
                for (IssueListCorrection correction : myIssueTask.getIssueListCorrections()) {
                    if (ProgressEnum.Enum.VERIFIED.name().equals(correction.getProgress())
                            && UserContext.getUserId().equals(correction.getMetaUser().getId())) {
                        d.put("rowOperationAuthFlag",0);
                        break;
                    }
                }
            });
        }
    }
    @Override
    public void afterSingleFetch(Map<String, Object> map) {
        List<HashMap<String,Object>> list = (List<HashMap<String,Object>>) map.get("issueListCorrections");
        List<HashMap<String,Object>> result = new ArrayList<>();
        for(HashMap<String,Object> data: list){
            Map<String,Object> userMap = (Map<String, Object>) data.get("metaUser");
            String id = userMap.get("id").toString();
            if(id.equals(UserContext.getUserId())){
                result.add(data);
            }
        }
        map.put("issueListCorrections",result);
    }
}
