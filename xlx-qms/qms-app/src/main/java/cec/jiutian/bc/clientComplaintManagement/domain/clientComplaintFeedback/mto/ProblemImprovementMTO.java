package cec.jiutian.bc.clientComplaintManagement.domain.clientComplaintFeedback.mto;

import cec.jiutian.bc.clientComplaintManagement.domain.clientComplaintFeedback.handler.ProblemImprovementMTODynamicHandler;
import cec.jiutian.bc.clientComplaintManagement.domain.clientComplaintFeedback.model.ClientComplaintFeedback;
import cec.jiutian.bc.clientComplaintManagement.enumeration.ProblemImprovementStatusEnum;
import cec.jiutian.bc.mto.OrgMTO;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/05/20
 * @description TODO
 */
@FabosJson(
        name = "问题改善",
        orderBy = "ProblemImprovementMTO.createTime desc"
)
@Table(name = "qms_ccm_problem_improvement",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Data
public class ProblemImprovementMTO extends MetaModel {
    @FabosJsonField(
            views = @View(title = "问题改善任务单号"),
            edit = @Edit(title = "问题改善任务单号",
                    readonly = @Readonly(add = false),
                    search = @Search(vague = true),
                    notNull = true),
            dynamicField = @DynamicField(
                    dependFiled = @DependFiled(buttonName = "生成", changeBy = "id",
                            dynamicHandler = ProblemImprovementMTODynamicHandler.class))
    )
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "问题描述"),
            edit = @Edit(title = "问题描述",
                    notNull = true,
                    search = @Search(vague = true))

    )
    private String problemDescription;

    @ManyToOne
    @FabosJsonField(
            views = {
                    @View(title = "客诉反馈", column = "generalCode", show = false)
            },
            edit = @Edit(title = "客诉反馈",
                    type = EditType.REFERENCE_TABLE,
                    readonly = @Readonly(),
                    show = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode")
            )
    )
    private ClientComplaintFeedback clientComplaintFeedback;

    @FabosJsonField(
            views = @View(title = "责任部门", column = "name"),
            edit = @Edit(title = "责任部门",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(type = TabTableReferType.SelectShowTypeMTM.LIST)
            )
    )
    @ManyToOne
    @JoinColumn(name = "response_department_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private OrgMTO orgMTO;

    @FabosJsonField(
            views = @View(title = "分析方法"),
            edit = @Edit(title = "分析方法",
                    notNull = true,
                    search = @Search(vague = true))

    )
    private String method;

    @FabosJsonField(
            views = @View(title = "纳期"),
            edit = @Edit(title = "纳期",
                    dateType = @DateType(type = DateType.Type.DATE_TIME),
                    notNull = true
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date deliveryTime;

    @FabosJsonField(
            views = @View(title = "状态", show = false),
            edit = @Edit(title = "状态",
                    show = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ProblemImprovementStatusEnum.class)
            )
    )
    private String businessStatus;
}
