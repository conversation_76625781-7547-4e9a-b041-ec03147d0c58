package cec.jiutian.bc.changeRequestManagement.domain.ecn.mto;

import cec.jiutian.bc.changeRequestManagement.domain.ecn.model.ECNItem;
import cec.jiutian.bc.changeRequestManagement.enums.ChangeLevel;
import cec.jiutian.bc.changeRequestManagement.enums.ChangeType;
import cec.jiutian.bc.changeRequestManagement.enums.ECRStatusEnum;
import cec.jiutian.bc.changeRequestManagement.enums.FileStandardEnum;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.module.QMSBaseNamingRuleModel;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Power;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@Entity
@Table(name = "qms_change_request",
        indexes = {
                @Index(name = "idx_group_code", columnList = "general_code", unique = true)
        })
@FabosJson(
        name = "变更申请",
        orderBy = "createTime desc",
        power = @Power(examine = false, examineDetails = false, export = false, importable = false)
)
@TemplateType(type = "multiTable")
public class ECNSupplementItemMTO extends QMSBaseNamingRuleModel {

    @FabosJsonField(
            views = @View(title = "变更主题"),
            edit = @Edit(
                    readonly = @Readonly(add = true,edit = true),
                    title = "变更主题",
                    inputType = @InputType(length = 20),
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "change_subject", length = 20)
    private String changeSubject;


    @FabosJsonField(
            views = @View(title = "变更类型"),
            edit = @Edit(
                    readonly = @Readonly(add = true,edit = true),
                    title = "变更类型",
                    inputType = @InputType(length = 20),
                    notNull = true,
                    search = @Search,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ChangeType.class)
            )
    )
    @Column(name = "change_type", length = 20)
    private String changeType;

    @FabosJsonField(
            views = @View(title = "变更级别"),
            edit = @Edit(
                    readonly = @Readonly(add = true,edit = true),
                    title = "变更级别",
                    inputType = @InputType(length = 20),
                    notNull = true,
                    search = @Search,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ChangeLevel.class)
            )
    )
    @Column(name = "change_level", length = 20)
    private String changeLevel;

    @FabosJsonField(
            views = @View(title = "变更原因"),
            edit = @Edit(
                    readonly = @Readonly(add = true,edit = true),
                    type = EditType.TEXTAREA,
                    title = "变更原因",
                    inputType = @InputType(length = 100),
                    notNull = true
            )
    )
    @Column(name = "change_reason", length = 100)
    private String changeReason;

    @FabosJsonField(
            views = @View(title = "变更前"),
            edit = @Edit(
                    readonly = @Readonly(add = true,edit = true),
                    title = "变更前",
                    type = EditType.TEXTAREA,
                    inputType = @InputType(length = 100),
                    notNull = true
            )
    )
    @Column(name = "before_change")
    private String beforeChange;

    @FabosJsonField(
            views = @View(title = "变更后"),
            edit = @Edit(
                    readonly = @Readonly(add = true,edit = true),
                    title = "变更后",
                    type = EditType.TEXTAREA,
                    inputType = @InputType(length = 100)
            )
    )
    @Column(name = "after_change", length = 100)
    private String afterChange;

    @FabosJsonField(
            views = @View(title = "预计变更切换时间", type = ViewType.DATE_TIME),
            edit = @Edit(
                    readonly = @Readonly(add = true,edit = true),
                    title = "预计变更切换时间",
                    notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @Column(name = "estimated_switch_time")
    private Date estimatedSwitchTime;


    @FabosJsonField(
            views = @View(title = "风险影响"),
            edit = @Edit(
                    readonly = @Readonly(add = true,edit = true),
                    title = "风险影响",
                    type = EditType.TEXTAREA,
                    inputType = @InputType(length = 100),
                    notNull = true
            )
    )
    @Column(name = "risk_impact", length = 100)
    private String riskImpact;

    @FabosJsonField(
            views = @View(title = "文件标准化"),
            edit = @Edit(
                    readonly = @Readonly(add = true,edit = true),
                    title = "文件标准化",
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = FileStandardEnum.class),
                    inputType = @InputType(length = 100)
            )
    )
    @Column(name = "file_standardization", length = 100)
    private String fileStandardization;

    @FabosJsonField(
            views = @View(title = "附件"),
            edit = @Edit(title = "附件",
                    readonly = @Readonly(add = true,edit = true),
                    inputType = @InputType(length = 300),
                    type = EditType.ATTACHMENT,
                    tips = "最大支持100M文件",
                    attachmentType = @AttachmentType(
                            size = 100 * 1024,
                            maxLimit = 1,
                            type = AttachmentType.Type.BASE)
            )
    )
    @Column(length = 300)
    private String attachment;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(
                    title = "状态",
                    inputType = @InputType(length = 30),
                    notNull = true,
                    search = @Search,
                    readonly = @Readonly(add = true, edit = true),
                    defaultVal = "WAIT_SUBMIT",
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ECRStatusEnum.class)
            ),
            dynamicField = @DynamicField(passive = true)
    )
    @Column(name = "status", length = 30)
    private String status;

    @FabosJsonField(
            views = @View(title = "申请人"),
            edit = @Edit(
                    title = "申请人",
                    inputType = @InputType(length = 20),
                    readonly = @Readonly(add = true, edit = true),
                    notNull = true,
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "user", beFilledBy = "name"))
    )
    @Column(name = "applicant", length = 20)
    private String applicant;

    @FabosJsonField(
            views = @View(title = "申请人ID", show = false),
            edit = @Edit(
                    show = false,
                    title = "申请人ID",
                    inputType = @InputType(length = 50)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "user", beFilledBy = "id"))
    )
    @Column(name = "applicant_id", length = 50)
    private String applicantId;

    @FabosJsonField(
            views = @View(title = "申请时间", type = ViewType.DATE_TIME),
            edit = @Edit(
                    readonly = @Readonly(add = true,edit = true),
                    title = "申请时间",
                    notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @Column(name = "apply_time", nullable = false)
    private Date applyTime;

    @FabosJsonField(
            views = @View(title = "申请部门"),
            edit = @Edit(
                    readonly = @Readonly(add = true,edit = true),
                    title = "申请部门",
                    inputType = @InputType(length = 20),
                    notNull = true,
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "org", beFilledBy = "name"))
    )
    @Column(name = "apply_department", length = 20)
    private String applyDepartment;

    @FabosJsonField(
            views = @View(title = "申请部门ID", show = false),
            edit = @Edit(
                    show = false,
                    title = "申请部门ID"
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "org", beFilledBy = "id"))
    )
    @Column(name = "department_id", length = 50)
    private String departmentId;

    @FabosJsonField(
            views = @View(title = "变更切换日期", type = ViewType.DATE_TIME),
            edit = @Edit(
                    readonly = @Readonly(add = true,edit = true),
                    title = "变更切换日期",
                    notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @Column(name = "change_switch_date")
    private Date changeSwitchDate;

    @FabosJsonField(
            views = @View(title = "计划变更执行日期", type = ViewType.DATE_TIME),
            edit = @Edit(
                    readonly = @Readonly(add = true,edit = true),
                    title = "计划变更执行日期",
                    notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @Column(name = "planned_execution_date")
    private Date plannedExecutionDate;

    @FabosJsonField(
            views = @View(title = "是否涉及客户", show = false),
            edit = @Edit(title = "是否涉及客户",
                    show = false,
                    type = EditType.BOOLEAN,
                    boolType = @BoolType(trueText = "是", falseText = "否")
            )
    )
    private Boolean involveCustomer;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "ecn_id")
    @FabosJsonField(
            views = @View(title = "补充变更任务", column = "changeItem", type = ViewType.TABLE_VIEW),
            edit = @Edit(
                    title = "补充变更任务",
                    type = EditType.TAB_TABLE_ADD,
                    allowAddMultipleRows = true,
                    referenceTableType = @ReferenceTableType(label = "changeItem")
            )
    )
    private List<ECNItem> ECNItems;

}