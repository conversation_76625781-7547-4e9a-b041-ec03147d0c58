package cec.jiutian.bc.inventoryInspection.domain.inspectionTask.model;

import cec.jiutian.bc.basicData.domain.inspectionStandard.model.InspectionStandard;
import cec.jiutian.bc.basicData.enumeration.ApplyRangeEnum;
import cec.jiutian.bc.inventoryInspection.domain.inspectionRequest.model.InventoryInspectionRequest;
import cec.jiutian.bc.inventoryInspection.domain.inspectionTask.handler.InvRequestDynamicHandler;
import cec.jiutian.bc.inventoryInspection.domain.inspectionTask.handler.InventoryInspectionItemDetailDynamicHandler;
import cec.jiutian.bc.inventoryInspection.domain.inspectionTask.handler.InventoryInspectionResultCommitOperationHandler;
import cec.jiutian.bc.inventoryInspection.domain.inspectionTask.handler.MyInvTaskResultEnterHandler;
import cec.jiutian.bc.inventoryInspection.domain.inspectionTask.mto.MyInvTaskResultEnterMTO;
import cec.jiutian.bc.inventoryInspection.domain.inspectionTask.proxy.MyInvInsTaskDataProxy;
import cec.jiutian.bc.inventoryInspection.enumeration.InspectionResultEnum;
import cec.jiutian.bc.inventoryInspection.enumeration.TaskBuildTypeEnum;
import cec.jiutian.bc.modeler.enumration.TaskBusinessStateEnum;
import cec.jiutian.bc.modeler.enumration.UnqualifiedLevelEnum;
import cec.jiutian.bc.processInspect.domain.publicInspectionTask.mto.ProcessUserForInsTaskMTO;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.*;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DependFieldDisplay;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowOperation;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/10
 * @description TODO
 */
@FabosJson(
        name = "我的库存检验任务",
        orderBy = "createTime desc",
        filter = @Filter(value = "inspectionType = 'inventoryInspect'"),
        dataProxy = MyInvInsTaskDataProxy.class,
        power = @Power(add = false,edit = false,delete = false),
        rowOperation = {
                @RowOperation(
                        title = "结果录入",
                        code = "MyInventoryInspectionTask@RESLTENTER",
                        type = RowOperation.Type.POPUP,
                        mode = RowOperation.Mode.HEADER,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = MyInvTaskResultEnterMTO.class,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        operationHandler = MyInvTaskResultEnterHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "MyInventoryInspectionTask@RESLTENTER"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "selectedItems[0].businessState != 'INSPECTING'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "结果提交",
                        code = "MyInventoryInspectionTask@RESLTCOMMIT",
                        operationHandler = InventoryInspectionResultCommitOperationHandler.class,
                        mode = RowOperation.Mode.HEADER,
                        callHint = "是否确定提交？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "MyInventoryInspectionTask@RESLTCOMMIT"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "selectedItems[0].businessState != 'INSPECTING'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
        }
)
@Table(name = "mi_inspection_task",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Getter
@Setter
@TemplateType(type = "multiTable")
public class MyInventoryInspectionTask extends MetaModel {


    @FabosJsonField(
            views = @View(title = "检验任务编号"),
            edit = @Edit(title = "检验任务编号", show = false,search = @Search)
    )
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "检验状态"),
            edit = @Edit(title = "检验状态",show = false, type = EditType.CHOICE,search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = TaskBusinessStateEnum.class))
    )
    private String businessState;

    @FabosJsonField(
            views = @View(title = "当前状态"),
            edit = @Edit(title = "当前状态",show = false, type = EditType.CHOICE,search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = OrderCurrentStateEnum.class))
    )
    private String currentState;

    @FabosJsonField(
            views = @View(title = "供应商名称"),
            edit = @Edit(title = "供应商名称", search = @Search(vague = true))
    )
    private String supplierName;

    @FabosJsonField(
            views = @View(title = "检验部门"),
            edit = @Edit(title = "检验部门", search = @Search(vague = true))
    )
    private String inspectionDepartmentName;

    @FabosJsonField(
            views = @View(title = "检验人"),
            edit = @Edit(title = "检验人", search = @Search(vague = true))
    )
    private String inspectorName;

    @FabosJsonField(
            views = @View(title = "质检标准", column = "generalCode"),
            edit = @Edit(title = "质检标准",
                    readonly = @Readonly(add = false), allowAddMultipleRows = false,
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode")
            )
    )
    @ManyToOne
    @JoinColumn(name = "inspection_standard_id")
    private InspectionStandard inspectionStandard;

    @FabosJsonField(
            views = @View(title = "创建类型"),
            edit = @Edit(title = "创建类型", type = EditType.CHOICE, search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = TaskBuildTypeEnum.class))
    )
    private String buildType;

    @FabosJsonField(
            views = @View(title = "检验类型"),
            edit = @Edit(title = "检验类型", show = false, type = EditType.CHOICE, search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = ApplyRangeEnum.class))
    )
    private String inspectionType;

    @FabosJsonField(
            views = @View(title = "是否加急"),
            edit = @Edit(title = "是否加急", defaultVal = "false"
            )
    )
    private Boolean isUrgent;

    @FabosJsonField(
            views = @View(title = "检验结果"),
            edit = @Edit(title = "检验结果", type = EditType.CHOICE, search = @Search(), show = false,
                    choiceType = @ChoiceType(fetchHandler = InspectionResultEnum.class))
    )
    private String inspectionResult;

    @FabosJsonField(
            views = @View(title = "不合格等级"),
            edit = @Edit(title = "不合格等级", notNull = true,type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = UnqualifiedLevelEnum.class),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "inspectionResult == 'UNQUALIFIED'"))
    )
    private String unqualifiedLevel;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "检验通知",column = "generalCode", type = ViewType.TABLE_FORM),
            edit = @Edit(title = "检验通知",
                    type = EditType.REFERENCE_TABLE,allowAddMultipleRows = false,
                    notNull = true,
                    filter = @Filter(value = "InventoryInspectionRequest.type = 'Overdue'"),
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode")
            )
    )
    private InventoryInspectionRequest inventoryInspectionRequest;

    @FabosJsonField(
            views = @View(title = "检验物资名称"),
            edit = @Edit(title = "检验物资名称", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inventoryInspectionRequest", beFilledBy = "materialName"))
    )
    private String materialName;

    @FabosJsonField(
            views = @View(title = "单位"),
            edit = @Edit(title = "单位", notNull = true),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inventoryInspectionRequest", beFilledBy = "measureUnit"))
    )
    private String unit;

    @FabosJsonField(
            views = @View(title = "批次号",show = false),
            edit = @Edit(title = "批次号",show = false, readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inventoryInspectionRequest", beFilledBy = "originLotId"))
    )
    private String originLotId;

    @FabosJsonField(
            views = @View(title = "规格"),
            edit = @Edit(title = "规格"),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inventoryInspectionRequest", beFilledBy = "materialSpecification"))
    )
    private String materialSpecification;

    @FabosJsonField(
            views = @View(title = "物资分级"),
            edit = @Edit(title = "物资分级"),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inventoryInspectionRequest", beFilledBy = "materialLevel"))
    )
    private String materialLevel;

    @FabosJsonField(
            views = @View(title = "来料数量"),
            edit = @Edit(title = "来料数量", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inventoryInspectionRequest", beFilledBy = "lotQuantity"))
    )
    private Double arrivalQuantity;

    @FabosJsonField(
            views = @View(title = "样品数量"),
            edit = @Edit(title = "样品数量", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inventoryInspectionRequest", beFilledBy = "originSampleQuantity"))
    )
    private Double sampleQuantity;

    @FabosJsonField(
            views = @View(title = "取样数量"),
            edit = @Edit(title = "取样数量", readonly = @Readonly)
    )
    private Double allInspectionItemQuantity;

    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumn(name = "inspection_task_id")
    @OrderBy
    @FabosJsonField(
            views = @View(title = "检验物资明细", type= ViewType.TABLE_VIEW,index = 8),
            edit = @Edit(title = "检验物资明细",type = EditType.TAB_REFERENCE_GENERATE),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "inspectionRequest", dynamicHandler = InvRequestDynamicHandler.class)),
            referenceGenerateType = @ReferenceGenerateType()
    )
    private List<InventoryInspectionTaskDetail> inventoryInspectionTaskDetailList;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = false)
    @FabosJsonField(
            views = @View(title = "检验项目明细", type = ViewType.TABLE_VIEW, index = 8),
            edit = @Edit(title = "检验项目明细", type = EditType.TAB_REFERENCE_GENERATE),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "inspectionStandard", dynamicHandler = InventoryInspectionItemDetailDynamicHandler.class)),
            referenceGenerateType = @ReferenceGenerateType()
    )
    @JoinColumn(name = "inspection_task_id")
    private List<InventoryInspectionStandardDetail> standardDetailList;

    @Transient
    @FabosJsonField(
            views = @View(title = "检验员", show = false, column = "name"),
            edit = @Edit(title = "检验员",
                    show = false,
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType()
            )
    )
    private ProcessUserForInsTaskMTO user;

    @FabosJsonField(
            views = @View(title = "检验员ID", show = false),
            edit = @Edit(title = "检验员ID",
                    show = false
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "user", beFilledBy = "id"))
    )
    private String userId;

    @FabosJsonField(
            views = @View(title = "检验员姓名"),
            edit = @Edit(title = "检验员姓名",
                    show = false,
                    readonly = @Readonly(add = false, edit = false),
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "user", beFilledBy = "name"))
    )
    private String userName;

    @FabosJsonField(
            views = @View(title = "备注"),
            edit = @Edit(title = "备注",
                    type = EditType.TEXTAREA)
    )
    private String remark;
}
