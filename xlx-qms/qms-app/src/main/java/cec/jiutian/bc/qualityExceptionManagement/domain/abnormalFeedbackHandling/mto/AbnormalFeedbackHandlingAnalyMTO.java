package cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.mto;

import cec.jiutian.bc.materialInspect.domain.publicInspectionTask.mto.UserForInsTaskMTO;
import cec.jiutian.bc.mto.OrgMTO;
import cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.enumration.*;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/27
 * @description TODO
 */
@FabosJson(
        name = "物料方案"
)
@Table(name = "qms_qe_abnormal_feedback_handling")
@Entity
@Getter
@Setter
public class AbnormalFeedbackHandlingAnalyMTO extends MetaModel {

    @FabosJsonField(
            views = @View(title = "解决方案"),
            edit = @Edit(title = "解决方案",
                    notNull = true,
                    type = EditType.TEXTAREA)
    )
    private String solution;

    @ManyToMany
    @JoinTable(
            name = "qms_qe_afh_org", //中间表表名
            inverseForeignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT),
            joinColumns = @JoinColumn(name = "qms_qe_afh_org_id", referencedColumnName = "id"),
            inverseJoinColumns = @JoinColumn(name = "org_id", referencedColumnName = "id"))
    @FabosJsonField(
            views = @View(title = "物料处置责任部门", type = ViewType.TABLE_VIEW),
            edit = @Edit(title = "物料处置责任部门", type = EditType.TAB_TABLE_REFER, search = @Search(vague = true),
                    tabTableReferType = @TabTableReferType(type = TabTableReferType.SelectShowTypeMTM.LIST,
                            label = "name")
            )
    )
    private List<OrgMTO> materialDisposalDepartmentList;

    @FabosJsonField(
            views = @View(title = "物料处置意见"),
            edit = @Edit(title = "物料处置意见",
                    type = EditType.CHOICE,
                    notNull = true,
                    search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = MaterialDisposalOpinionEnum.class))
    )
    private String materialDisposalOpinion;

    @FabosJsonField(
            views = @View(title = "分析人", column = "name"),
            edit = @Edit(title = "分析人",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(type = TabTableReferType.SelectShowTypeMTM.LIST)
            )
    )
    @ManyToOne
    @JoinColumn(name = "analyst_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private UserForInsTaskMTO analyst;

    @FabosJsonField(
            views = @View(title = "分析时间"),
            edit = @Edit(title = "分析时间",
                    dateType = @DateType(type = DateType.Type.DATE_TIME),
                    notNull = true
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date analysisTime;

    @FabosJsonField(
            views = @View(title = "解决方案附件"),
            edit = @Edit(title = "解决方案附件", type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType)
    )
    private String solutionAttachments;
}
