package cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.MyIssueTasks.model;

import cec.jiutian.bc.qualityExceptionManagement.enumeration.inspectionIssuesImproveAndTrace.IssueListStatusEnum;
import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.ReferenceGenerateType;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Table(name = "qms_qem_issue_list")
@Entity
@Getter
@Setter
@FabosJson(name = "我的整改任务",
        power = @Power(add = false,delete = false,edit = false),
        orderBy = "MyIssueTaskRUN.createTime desc",
        filter = @Filter("status = 'RUNNING'")
)
public class MyIssueTaskRUN extends BaseModel {
    @FabosJsonField(
            views = @View(title = "状态", show = false),
            edit = @Edit(title = "状态", show = false, type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = IssueListStatusEnum.class))
    )
    private String status;

    // todo 还需动态绑定plan?
    @FabosJsonField(
            edit = @Edit(title = "整改措施", type = EditType.TAB_REFERENCE_GENERATE),
            views = @View(title = "整改措施", column = "correction", type= ViewType.TABLE_VIEW),
            referenceGenerateType = @ReferenceGenerateType(editable = {"progress", "completionDate", "completionEvidence"})
    )
    @JoinColumn(name = "issue_list_id")
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    private List<MyIssueListCorrection> issueListCorrections;
}
