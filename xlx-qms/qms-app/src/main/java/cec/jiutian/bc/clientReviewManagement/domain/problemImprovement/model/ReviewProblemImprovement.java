package cec.jiutian.bc.clientReviewManagement.domain.problemImprovement.model;

import cec.jiutian.bc.clientComplaintManagement.enumeration.ReviewProblemImproveStatusEnum;
import cec.jiutian.bc.clientReviewManagement.domain.problemImprovement.handler.*;
import cec.jiutian.bc.clientReviewManagement.domain.problemImprovement.mto.ReviewProblemImprovementAssignMTO;
import cec.jiutian.bc.clientReviewManagement.domain.problemImprovement.mto.ReviewProblemImprovementResultValidateMTO;
import cec.jiutian.bc.clientReviewManagement.domain.problemImprovement.proxy.ReviewProblemImprovementDataProxy;
import cec.jiutian.bc.materialInspect.domain.publicInspectionTask.mto.UserForInsTaskMTO;
import cec.jiutian.bc.mto.OrgMTO;
import cec.jiutian.bc.urm.domain.user.entity.User;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/06/11
 * @description
 */
@FabosJson(
        name = "问题改善任务",
        orderBy = "ReviewProblemImprovement.createTime desc",
        dataProxy = ReviewProblemImprovementDataProxy.class,
        power = @Power(add = false,edit = false,delete = false),
        rowOperation = {
                @RowOperation(
                        title = "分发",
                        code = "ReviewProblemImprovement@ASSIGN",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = ReviewProblemImprovementAssignMTO.class,
                        operationHandler = ReviewProblemImprovementAssignMTOHandler.class,
                        ifExpr = "selectedItems[0].businessStatus != 'DISTRIBUTING'", //禁用按钮
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "ReviewProblemImprovement@ASSIGN"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
                @RowOperation(
                        title = "验证",
                        code = "ReviewProblemImprovement@RESULTVALIDATE",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = ReviewProblemImprovementResultValidateMTO.class,
                        operationHandler = ReviewProblemImprovementResultValidateMTOHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "ReviewProblemImprovement@RESULTVALIDATE"
                        ),
                        ifExpr = "selectedItems[0].businessStatus != 'VERIFYING'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
        }
)
@Table(name = "qms_client_review_problem_improvement",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Data
public class ReviewProblemImprovement extends MetaModel {
    @FabosJsonField(
            views = @View(title = "评审任务单号"),
            edit = @Edit(title = "评审任务单号",
                    readonly = @Readonly(add = false),
                    search = @Search(vague = true),
                    notNull = true),
            dynamicField = @DynamicField(
                    dependFiled = @DependFiled(buttonName = "生成", changeBy = "id",
                            dynamicHandler = ReviewProblemImprovementDynamicHandler.class))
    )
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "序号"),
            edit = @Edit(title = "序号", show = false)
    )
    private Integer sequenceNumber;

    @FabosJsonField(
            views = @View(title = "问题描述"),
            edit = @Edit(title = "问题描述",
                    notNull = true,
                    search = @Search(vague = true))

    )
    private String problemDescription;

    @FabosJsonField(
            views = @View(title = "计划完成日期", type = ViewType.DATE_TIME),
            edit = @Edit(
                    title = "计划完成日期",
                    notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @Column(name = "planned_completion_date")
    private Date plannedCompletionDate;

    @FabosJsonField(
            views = @View(title = "主责部门", column = "name"),
            edit = @Edit(title = "主责部门",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType(type = TabTableReferType.SelectShowTypeMTM.LIST)
            )
    )
    @ManyToOne
    @JoinColumn(name = "primary_response_department_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private OrgMTO primaryRespOrgMTO;

    @FabosJsonField(
            views = @View(title = "次责部门", column = "name"),
            edit = @Edit(title = "次责部门",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType(type = TabTableReferType.SelectShowTypeMTM.LIST)
            )
    )
    @ManyToOne
    @JoinColumn(name = "secondary_response_department_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private OrgMTO secondaryRespOrgMTO;

    @Transient
    @FabosJsonField(
            views = @View(title = "选择责任人", show = false, column = "name"),
            edit = @Edit(title = "选择责任人",
                    notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType()
            )
    )
    private UserForInsTaskMTO responsiblePersonMTO;

    @FabosJsonField(
            views = @View(title = "责任人id", show = false),
            edit = @Edit(title = "责任人id",
                    show = false,
                    notNull = true,
                    inputType = @InputType(length = 30)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "responsiblePersonMTO",
                    beFilledBy = "id"))
    )
    private String responsiblePersonId;

    @FabosJsonField(
            views = @View(title = "责任人"),
            edit = @Edit(title = "责任人",
                    search = @Search(vague = true),
                    notNull = true,
                    readonly = @Readonly(),
                    inputType = @InputType(length = 30)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "responsiblePersonMTO",
                    beFilledBy = "name"))
    )
    @Column(length = 30)
    private String responsiblePerson;

    @FabosJsonField(
            views = @View(title = "原因"),
            edit = @Edit(title = "原因", inputType = @InputType(length = 200))
    )
    private String reason;

    @FabosJsonField(
            views = @View(title = "整改措施"),
            edit = @Edit(title = "整改措施")
    )
    private String correction;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "验证人", column = "name"),
            edit = @Edit(title = "验证人",
                    type = EditType.REFERENCE_TABLE,
                    search = @Search(),
                    show = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name")
            )
    )
    private User verifiedUser;

    @FabosJsonField(
            views = @View(title = "实际完成日期", type = ViewType.DATE_TIME),
            edit = @Edit(
                    title = "实际完成日期",
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date actualFinishTime;

    @FabosJsonField(
            views = @View(title = "执行情况"),
            edit = @Edit(title = "执行情况",
                    search = @Search(vague = true))

    )
    private String executionStatus;

    @FabosJsonField(
            views = @View(title = "改善证据"),
            edit = @Edit(title = "改善证据", type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(maxLimit = 10))
    )
    private String improvementEvidence;

    @FabosJsonField(
            views = @View(title = "验收情况"),
            edit = @Edit(title = "验收情况")
    )
    private String acceptanceDescription;

    @FabosJsonField(
            views = @View(title = "佐证材料"),
            edit = @Edit(title = "佐证材料",type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(size = 5120, maxLimit = 10, type = AttachmentType.Type.BASE))
    )
    private String supportProof;

    @FabosJsonField(
            views = @View(title = "完成状态"),
            edit = @Edit(title = "完成状态",
                    show = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ReviewProblemImproveStatusEnum.class),
                    search = @Search(vague = true)
            )
    )
    private String businessStatus;
}
