package cec.jiutian.bc.qualityExceptionManagement.domain.otherDellTask.handler;

import cec.jiutian.bc.qualityExceptionManagement.domain.otherDellTask.model.OtherDellTask;
import cec.jiutian.bc.qualityExceptionManagement.enums.ReworkTaskStatusEnum;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class CompleteOtherDellTaskHandler implements OperationHandler<OtherDellTask, OtherDellTask> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<OtherDellTask> data, OtherDellTask modelObject, String[] param) {
        if (CollectionUtils.isEmpty(data)) {
            throw new ServiceException("数据异常");
        }

        OtherDellTask otherDellTask = data.get(0);

        otherDellTask.setStatus(ReworkTaskStatusEnum.Enum.COMPLETE.name());
        fabosJsonDao.merge(otherDellTask);
        return "";
    }
}
