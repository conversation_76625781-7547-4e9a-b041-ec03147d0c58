package cec.jiutian.bc.clientReviewManagement.domain.problemImprovement.handler;

import cec.jiutian.bc.clientComplaintManagement.enumeration.ReviewProblemImproveStatusEnum;
import cec.jiutian.bc.clientReviewManagement.domain.problemImprovement.model.MyReviewProblemImprovement;
import cec.jiutian.bc.clientReviewManagement.domain.problemImprovement.mto.MyReviewProblemImprovementAnalyMTO;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/06/11
 * @description
 */
@Component
public class MyReviewProblemImprovementAnalyMTOHandler implements OperationHandler<MyReviewProblemImprovement, MyReviewProblemImprovementAnalyMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<MyReviewProblemImprovement> data, MyReviewProblemImprovementAnalyMTO modelObject, String[] param) {
        if (CollectionUtils.isEmpty(data) || data.get(0) == null) {
            return "alert(行数据选择异常)";
        }
        modelObject.setBusinessStatus(ReviewProblemImproveStatusEnum.Enum.EXECUTING.name());

        MyReviewProblemImprovement model = data.get(0);
        BeanUtil.copyProperties(modelObject, model, "createBy", "createTime");
        fabosJsonDao.mergeAndFlush(model);
        return "alert(操作成功)";
    }

    @Override
    public MyReviewProblemImprovementAnalyMTO fabosJsonFormValue(List<MyReviewProblemImprovement> data, MyReviewProblemImprovementAnalyMTO fabosJsonForm, String[] param) {
        MyReviewProblemImprovement model = data.get(0);
        BeanUtil.copyProperties(model, fabosJsonForm);
        return fabosJsonForm;
    }
}