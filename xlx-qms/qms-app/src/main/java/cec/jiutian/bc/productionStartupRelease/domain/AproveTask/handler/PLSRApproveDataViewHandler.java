package cec.jiutian.bc.productionStartupRelease.domain.AproveTask.handler;

import cec.jiutian.bc.productionStartupRelease.domain.AproveTask.modle.PLSRApproveTask;
import cec.jiutian.bc.productionStartupRelease.domain.AproveTask.mto.PLSRReportMTO;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class PLSRApproveDataViewHandler implements OperationHandler<PLSRApproveTask, PLSRReportMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;


    @Override
    public PLSRReportMTO fabosJsonFormValue(List<PLSRApproveTask> data, PLSRReportMTO fabosJsonForm, String[] param) {
        if (CollectionUtils.isEmpty(data)) {
            throw new ServiceException("数据异常");
        }
        PLSRReportMTO plsrReportMTO = fabosJsonDao.findById(PLSRReportMTO.class, data.get(0).getBusinessKey());
        return plsrReportMTO;
    }
}
