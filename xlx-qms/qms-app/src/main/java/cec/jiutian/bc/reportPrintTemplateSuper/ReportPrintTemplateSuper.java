package cec.jiutian.bc.reportPrintTemplateSuper;

import cec.jiutian.bc.basicData.enumeration.ApplyRangeEnum;
import cec.jiutian.bc.materialInspect.enumeration.InspectionResultEnum;
import cec.jiutian.bc.modeler.enumration.TaskBusinessStateEnum;
import cec.jiutian.bc.modeler.enumration.UnqualifiedLevelEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DependFieldDisplay;
import cec.jiutian.view.field.edit.Search;
import jakarta.persistence.CascadeType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.MappedSuperclass;
import jakarta.persistence.OneToMany;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Setter
@Getter
@MappedSuperclass
public class ReportPrintTemplateSuper extends MetaModel {
    @FabosJsonField(
            views = @View(title = "任务单号"),
            edit = @Edit(title = "任务单号",
                    search = @Search
            )
    )
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "检验类型", show = false),
            edit = @Edit(title = "检验类型", show = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ApplyRangeEnum.class))
    )
    private String inspectionType;

    @FabosJsonField(
            views = @View(title = "物资名称"),
            edit = @Edit(title = "物资名称",
                    search = @Search(vague = true)
            )
    )
    private String materialName; // 物资名称

    @FabosJsonField(
            views = @View(title = "规格型号"),
            edit = @Edit(title = "规格型号",
                    search = @Search
            )
    )
    private String specificationModel; // 规格型号

    @FabosJsonField(
            views = @View(title = "数量"),
            edit = @Edit(title = "数量"
            )
    )
    private Double sampleQuantity; // 数量

    @FabosJsonField(
            views = @View(title = "供应商名称"),
            edit = @Edit(title = "供应商名称",
                    search = @Search
            )
    )
    private String supplierName; // 供应商名称

    @FabosJsonField(
            views = @View(title = "供应商批次号"),
            edit = @Edit(title = "供应商批次号",
                    search = @Search
            )
    )
    private String supplierBatchNumber; // 供应商批次号

    @FabosJsonField(
            views = @View(title = "二次编码号"),
            edit = @Edit(title = "二次编码号",
                    search = @Search
            )
    )
    private String secondaryCodingNumber; // 二次编码号

    @FabosJsonField(
            views = @View(title = "质检标准模板"),
            edit = @Edit(title = "质检标准模板",
                    search = @Search
            )
    )
    private String qualityStandardTemplate; // 质检标准模板

    @FabosJsonField(
            views = @View(title = "是否加急"),
            edit = @Edit(title = "是否加急",
                    search = @Search,
                    notNull = true,
                    type = EditType.BOOLEAN
            )
    )
    private Boolean isUrgent;


    @FabosJsonField(
            views = @View(title = "创建日期"),
            edit = @Edit(title = "创建日期")
    )
    private Date creationDate; // 创建日期

    @FabosJsonField(
            views = @View(title = "任务状态"),
            edit = @Edit(title = "任务状态",
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = TaskBusinessStateEnum.class),
                    defaultVal = "BE_INSPECT"
            )
    )
    private String businessState;

    @FabosJsonField(
            views = @View(title = "检验结果"),
            edit = @Edit(title = "检验结果", type = EditType.CHOICE, search = @Search(), show = false,
                    choiceType = @ChoiceType(fetchHandler = InspectionResultEnum.class))
    )
    private String inspectionResult;

    @FabosJsonField(
            views = @View(title = "不合格等级"),
            edit = @Edit(title = "不合格等级", notNull = true,type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = UnqualifiedLevelEnum.class),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "inspectionResult == 'UNQUALIFIED'"))
    )
    private String unqualifiedLevel;

    @FabosJsonField(
            views = @View(title = "检验员"),
            edit = @Edit(title = "检验员",
                    show = false,
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "user", beFilledBy = "name"))
    )
    private String userName;

    @OneToMany(cascade = CascadeType.DETACH)
    @JoinColumn(name = "task_id")
    @FabosJsonField(
            views = @View(title = "检验项目",
                    column = "name",
                    type = ViewType.TABLE_FORM),
            edit = @Edit(title = "检验项目",
                    show = false,
                    type = EditType.TAB_TABLE_ADD
            )
    )
    private List<InsItemAndIndex> insItemAndIndexList;

}
