package cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.proxy;

import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.enumration.ProgressEnum;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.mto.CorrectPreventMeasureCauseAnalyMTO;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.mto.PreventMeasureCreationMTO;
import cec.jiutian.bc.urm.domain.user.entity.User;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.view.fun.DataProxy;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.stream.Collectors;

@Component
public class CorrectPreventMeasureCauseAnalyMTOProxy implements DataProxy<CorrectPreventMeasureCauseAnalyMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public void beforeUpdate(CorrectPreventMeasureCauseAnalyMTO mto) {
        if (CollectionUtils.isNotEmpty(mto.getPreventMeasureList())) {
            mto.setPreventAllUserIds(mto.getPreventMeasureList().stream().map(PreventMeasureCreationMTO::getUserForInsTaskMTO)
                    .map(BaseModel::getId).distinct().collect(Collectors.joining(",")));
            // handler移过来的内容
            User user = fabosJsonDao.getById(User.class, UserContext.getUserId());
            mto.setAnalyticalUser(user);
            mto.setAnalyticalTime(new Date());
            mto.setAnalyticalState(ProgressEnum.Enum.RUN_SUBMIT.name());
        }
    }
}
