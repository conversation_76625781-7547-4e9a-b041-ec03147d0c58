package cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.handler;

import cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.enumration.ProductQualityTrackBusinessStatusEnum;
import cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.model.ProductQualityTrack;
import cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.mto.ProductQualityTrackResultInputMTO;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/05
 * @description TODO
 */
@Component
@Slf4j
public class ProductQualityTrackResultInputHandler implements OperationHandler<ProductQualityTrack, ProductQualityTrackResultInputMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<ProductQualityTrack> data, ProductQualityTrackResultInputMTO modelObject, String[] param) {
        if (modelObject != null) {
            ProductQualityTrack model = data.get(0);
            modelObject.getPqtProcessOperationList().forEach(pq -> {
                pq.getPqtProductBatchNumberList().forEach(pbn -> {
                    pbn.setTrackerId(UserContext.getUserId());
                    pbn.setTracker(UserContext.getUserName());
                });
            });

            BeanUtil.copyProperties(modelObject, model, "createBy", "createTime");
            model.setBusinessStatus(ProductQualityTrackBusinessStatusEnum.Enum.PENDING_DECISION.name());
            fabosJsonDao.mergeAndFlush(model);
        }
        return "alert(操作成功)";
    }

    @Override
    public ProductQualityTrackResultInputMTO fabosJsonFormValue(List<ProductQualityTrack> data, ProductQualityTrackResultInputMTO fabosJsonForm, String[] param) {
        ProductQualityTrack model = data.get(0);
        BeanUtil.copyProperties(model, fabosJsonForm);
        return fabosJsonForm;
    }
}
