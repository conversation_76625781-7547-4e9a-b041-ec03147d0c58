package cec.jiutian.bc.inventoryInspection.statistics.util;

import cec.jiutian.bc.inventoryInspection.statistics.ao.StatisticsCommonParam;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

public class WorkshopPassRateSqlBuilder {



    public static String buildBatchSerialSql(StatisticsCommonParam param) {
        StringBuilder sql = new StringBuilder(
                "SELECT \n" +
                        "  workshop_id, \n" +
                        "  workshop_name, \n" +
                        "  production_line_id, \n" +
                        "  production_line_name, \n" +
                        "  serial_number, \n" +
                        "  output_quantity, \n" +
                        "  output_time, \n" +
                        "  qualified_flag, \n" +
                        "  material_code, \n" +
                        "  process_name \n" +
                        "FROM batch_serial bs \n" +
                        "LEFT JOIN ms_spcfcn fcn ON bs.material_code = fcn.spcfcn_cd \n" +
                        "LEFT JOIN ms_spcdct dct ON fcn.spcdct_cd = dct.spcdct_cd \n" +
                        "WHERE output_quantity > 0 \n" +
                        "  AND output_time IS NOT NULL \n" +
                        "  AND process_code = 'CP' \n"
        );

        // 1. 产出类型条件
        if (StringUtils.isNotBlank(param.getProduceType())) {
            sql.append(" AND bs.rework_flag = '")
                    .append(param.getProduceType()).append("' \n");
        }

        // 2. 时间范围条件（动态处理预设/自定义时间）
        TimeRangeCalculator.TimeRange range = TimeRangeCalculator.calculateTimeRange(param.getIsDiyTime(), param.getStartTime(), param.getEndTime(), param.getQuery());
        if (range != null) {
            sql.append("  AND output_time BETWEEN TO_TIMESTAMP(:startTime, 'yyyy-MM-dd HH:mm:ss') AND TO_TIMESTAMP(:endTime, 'yyyy-MM-dd HH:mm:ss') \n");
        }

        // 4. 物料类型条件
        if (CollectionUtils.isNotEmpty(param.getSpcdctCodes())) {
            sql.append("  AND dct.spcdct_cd IN (");
            param.getSpcdctCodes().forEach(code -> sql.append("'").append(code).append("',"));
            sql.setLength(sql.length() - 1);
            sql.append(") \n");
        } else if (CollectionUtils.isNotEmpty(param.getMaterialCodes())) {
            // 3. 物料编码条件
            sql.append("  AND material_code IN (");
            param.getMaterialCodes().forEach(code -> sql.append("'").append(code).append("',"));
            sql.setLength(sql.length() - 1); // 移除最后一个逗号
            sql.append(") \n");
        }

        // 5. 组织架构条件（产线>车间>工厂的优先级）
        if (CollectionUtils.isNotEmpty(param.getLineIds())) {
            sql.append("  AND production_line_id IN (");
            param.getLineIds().forEach(id -> sql.append("'").append(id).append("',"));
            sql.setLength(sql.length() - 1);
            sql.append(") \n");
        } else if (CollectionUtils.isNotEmpty(param.getWorkshopIds())) {
            sql.append("  AND workshop_id IN (");
            param.getWorkshopIds().forEach(id -> sql.append("'").append(id).append("',"));
            sql.setLength(sql.length() - 1);
            sql.append(") \n");
        } else if (CollectionUtils.isNotEmpty(param.getFactoryIds())) {
            sql.append("  AND workshop_id IN ( \n");
            sql.append("  SELECT id FROM mo_fctry_ara WHERE pid IN (");
            param.getFactoryIds().forEach(id -> sql.append("'").append(id).append("',"));
            sql.setLength(sql.length() - 1);
            sql.append(") \n  ) \n");
        }

        // 6. 检测任务条件（动态拼接检测项目和特性）
        if (CollectionUtils.isNotEmpty(param.getItemIds()) ||
                CollectionUtils.isNotEmpty(param.getFeatures())) {

            sql.append("  AND bs.serial_number IN ( \n");
            sql.append("  SELECT pt.actual_lot_serial_id \n");
            sql.append("  FROM pi_inspection_task pt \n");
            sql.append("  WHERE 1=1 \n");

            // 6.1 检测项目条件 / 特性条件
            if (CollectionUtils.isNotEmpty(param.getItemIds())) {
                sql.append("  AND pt.id IN ( \n");
                sql.append("  SELECT inspection_task_id \n");
                sql.append("  FROM pi_inspection_task_detail \n");
                sql.append("  WHERE item_id IN (");
                param.getItemIds().forEach(id -> sql.append("'").append(id).append("',"));
                sql.setLength(sql.length() - 1);
                sql.append(") \n      ) \n");
            } else if (CollectionUtils.isNotEmpty(param.getFeatures())) {
                sql.append("  AND pt.id IN ( \n");
                sql.append("  SELECT ptd.inspection_task_id \n");
                sql.append("  FROM pi_inspection_task_detail ptd \n");
                sql.append("  LEFT JOIN bd_inspection_item bi ON ptd.item_id = bi.id \n");
                sql.append("  WHERE bi.feature IN (");
                param.getFeatures().forEach(feature -> sql.append("'").append(feature).append("',"));
                sql.setLength(sql.length() - 1);
                sql.append(") \n      ) \n");
            }

            sql.append("  ) \n");
        }

        return sql.toString();
    }


    public static String buildWorkshopStatsSql(StatisticsCommonParam param) {
        StringBuilder sql = new StringBuilder(
                "SELECT * FROM ( SELECT \n" +
                        "  workshop_id AS \"车间ID\", \n" +
                        "  workshop_name AS \"车间名称\", \n"
        );

        // 根据计算维度动态选择统计方式
        if ("0".equals(param.getCalculateDimension())) {
            sql.append("  COUNT(serial_number) AS \"生产数量\", \n")
                    .append("  COUNT(CASE WHEN qualified_flag = 'N' THEN serial_number END) AS \"不良数量\", \n")
                    .append("  COUNT(CASE WHEN qualified_flag = 'Y' THEN serial_number END) AS \"合格数量\" \n");
        } else {
            sql.append("  COALESCE(SUM(output_quantity), 0) AS \"生产重量\", \n")
                    .append("  COALESCE(SUM(CASE WHEN qualified_flag = 'N' THEN output_quantity END), 0) AS \"不良重量\", \n")
                    .append("  COALESCE(SUM(CASE WHEN qualified_flag = 'Y' THEN output_quantity END), 0) AS \"合格重量\" \n");
        }

        // 成品合格率计算（动态处理计算维度）
        sql.append(", ROUND( \n")
                .append("  COALESCE( \n")
                .append("  COALESCE(SUM(CASE WHEN qualified_flag = 'Y' THEN ");

        if ("0".equals(param.getCalculateDimension())) {
            sql.append("1");
        } else {
            sql.append("output_quantity");
        }

        sql.append(" END) ::::NUMERIC, 0) \n")
                .append("  / NULLIF(");

        if ("0".equals(param.getCalculateDimension())) {
            sql.append("COUNT(serial_number)");
        } else {
            sql.append("COALESCE(SUM(output_quantity), 0)");
        }

        sql.append(", 0), \n")
                .append(" 0 \n")
                .append(" ), 4 \n")
                .append(") AS \"qualified_rate\", \n")
                .append("0.5 AS \"目标\" \n")
                .append("FROM ( \n")
                .append(" SELECT \n")
                .append(" workshop_id, \n")
                .append(" workshop_name, \n")
                .append(" serial_number, \n")
                .append(" output_quantity, \n")
                .append(" qualified_flag \n")
                .append(" FROM batch_serial bs \n")
                .append(" LEFT JOIN ms_spcfcn fcn ON bs.material_code = fcn.spcfcn_cd \n")
                .append(" LEFT JOIN ms_spcdct dct ON fcn.spcdct_cd = dct.spcdct_cd \n")
                .append(" WHERE output_quantity > 0 \n")
                .append(" AND output_time IS NOT NULL \n")
                .append(" AND process_code = 'CP' \n");

        // 1. 产出类型条件
        if (StringUtils.isNotBlank(param.getProduceType())) {
            sql.append("  AND bs.rework_flag = '").append(param.getProduceType()).append("' \n");
        }
        // 2. 时间范围条件（动态处理预设/自定义时间）
        if ("1".equals(param.getIsDiyTime())) {
            TimeRangeCalculator.TimeRange range = TimeRangeCalculator.calculateTimeRange(param.getIsDiyTime(), param.getStartTime(), param.getEndTime(), param.getQuery());
            sql.append(" AND output_time BETWEEN TO_TIMESTAMP('" + range.getFormattedStart() + "', 'YYYY-MM-DD HH24:MI:SS') AND TO_TIMESTAMP('" + range.getFormattedEnd() + "', 'YYYY-MM-DD HH24:MI:SS') \n");
        }

        // 4. 物料类型条件
        if (CollectionUtils.isNotEmpty(param.getSpcdctCodes())) {
            sql.append(" AND dct.spcdct_cd IN (");
            param.getSpcdctCodes().forEach(code -> sql.append("'").append(code).append("',"));
            sql.setLength(sql.length() - 1);
            sql.append(") \n");
        } else if (CollectionUtils.isNotEmpty(param.getMaterialCodes())) {
            // 3. 物料编码条件
            sql.append(" AND material_code IN (");
            param.getMaterialCodes().forEach(code -> sql.append("'").append(code).append("',"));
            sql.setLength(sql.length() - 1); // 移除最后一个逗号
            sql.append(") \n");
        }

        // 5. 组织架构条件（产线>车间>工厂的优先级）
        if (CollectionUtils.isNotEmpty(param.getLineIds())) {
            sql.append(" AND production_line_id IN (");
            param.getLineIds().forEach(id -> sql.append("'").append(id).append("',"));
            sql.deleteCharAt(sql.length() - 1);
            sql.append(") \n");
        } else if (CollectionUtils.isNotEmpty(param.getWorkshopIds())) {
            sql.append(" AND workshop_id IN (");
            param.getWorkshopIds().forEach(id -> sql.append("'").append(id).append("',"));
            sql.deleteCharAt(sql.length() - 1);
            sql.append(") \n");
        } else if (CollectionUtils.isNotEmpty(param.getFactoryIds())) {
            sql.append(" AND workshop_id IN ( \n");
            sql.append(" SELECT id FROM mo_fctry_ara WHERE pid IN (");
            param.getFactoryIds().forEach(id -> sql.append("'").append(id).append("',"));
            sql.deleteCharAt(sql.length() - 1);
            sql.append(") \n  ) \n");
        }
        // 6. 检测任务条件（动态拼接检测项目和特性）
        if (CollectionUtils.isNotEmpty(param.getItemIds()) ||
                CollectionUtils.isNotEmpty(param.getFeatures())) {

            sql.append("  AND bs.serial_number IN ( \n");
            sql.append("  SELECT pt.actual_lot_serial_id \n");
            sql.append("  FROM pi_inspection_task pt \n");
            sql.append("  WHERE 1=1 \n");

            // 6.1 检测项目条件 / 特性条件
            if (CollectionUtils.isNotEmpty(param.getItemIds())) {
                sql.append("  AND pt.id IN ( \n");
                sql.append("  SELECT inspection_task_id \n");
                sql.append("  FROM pi_inspection_task_detail \n");
                sql.append("  WHERE item_id IN (");
                param.getItemIds().forEach(id -> sql.append("'").append(id).append("',"));
                sql.setLength(sql.length() - 1);
                sql.append(") \n  ) \n)");
            } else if (CollectionUtils.isNotEmpty(param.getFeatures())) {
                sql.append("  AND pt.id IN ( \n");
                sql.append("  SELECT ptd.inspection_task_id \n");
                sql.append("  FROM pi_inspection_task_detail ptd \n");
                sql.append("  LEFT JOIN bd_inspection_item bi ON ptd.item_id = bi.id \n");
                sql.append("  WHERE bi.feature IN (");
                param.getFeatures().forEach(feature -> sql.append("'").append(feature).append("',"));
                sql.setLength(sql.length() - 1);
                sql.append(") \n   ) \n)");
            }
        }
        sql.append(") AS workshop_base_data \n") // 关闭子查询
                .append("GROUP BY workshop_id, workshop_name \n")
                .append("ORDER BY workshop_id ASC")
                .append(") res ORDER BY qualified_rate");

        return sql.toString();
    }

}