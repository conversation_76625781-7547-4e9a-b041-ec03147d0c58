package cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.enumration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/27
 * @description TODO
 */
public class PqtDocumentTypeEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum data : Enum.values()) {
            list.add(new VLModel(data.name(), data.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        POWER_ON_TASK_LIST("开机任务单"),
        LINE_CHANGE_TASK_ORDER("换线任务单"),
        ;

        private final String value;

    }
}
