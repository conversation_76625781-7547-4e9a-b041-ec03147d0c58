package cec.jiutian.bc.qualityExceptionManagement.domain.returnProductHandling.proxy;

import cec.jiutian.bc.generalModeler.util.StringUtils;
import cec.jiutian.bc.qualityExceptionManagement.domain.returnProductHandling.model.ReturnProductHandlingReview;
import cec.jiutian.bc.qualityExceptionManagement.enums.ReturnProductHandlingStatusEnum;
import cec.jiutian.bc.qualityExceptionManagement.enums.ReturnProductReviewCommentEnum;
import cec.jiutian.bc.qualityExceptionManagement.service.QualityExceptionManagementService;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
public class ReturnProductHandlingReviewDataProxy implements DataProxy<ReturnProductHandlingReview> {

    @Resource
    private QualityExceptionManagementService qualityExceptionManagementService;

    @Override
    public void beforeUpdate(ReturnProductHandlingReview entity) {
        if (StringUtils.equals(entity.getReviewComment(), ReturnProductReviewCommentEnum.Enum.Pass.name())) {
            entity.setStatus(ReturnProductHandlingStatusEnum.Enum.Handling.name());

            String analysisMethodOrder = qualityExceptionManagementService.returnProductHandlingReviewPass(entity.getId());
            entity.setAnalysisMethodOrder(analysisMethodOrder);

        } else {
            entity.setStatus(ReturnProductHandlingStatusEnum.Enum.WaitAnalyse.name());
        }
    }

}
