package cec.jiutian.bc.materialInspect.domain.inspectionTask.model;

import cec.jiutian.bc.materialInspect.domain.inspectionRequest.model.InspectionRequest;
import cec.jiutian.bc.materialInspect.domain.inspectionTask.handler.RequestDynamicHandler;
import cec.jiutian.bc.materialInspect.domain.inspectionTask.proxy.TerminationInspectionTaskProxy;
import cec.jiutian.bc.materialInspect.enumeration.InspectionResultEnum;
import cec.jiutian.bc.materialInspect.enumeration.TaskBuildTypeEnum;
import cec.jiutian.bc.mto.OrgMTO;
import cec.jiutian.bc.modeler.enumration.TaskBusinessStateEnum;
import cec.jiutian.bc.modeler.enumration.UnqualifiedLevelEnum;
import cec.jiutian.bc.quantityPreparationPlan.handler.PlanCodeGenerateDynamicHandler;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.*;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Filter;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/10
 * @description TODO
 */
@FabosJson(
        name = "检验任务-终止检验按钮",
        dataProxy = TerminationInspectionTaskProxy.class
)
@Table(name = "mi_inspection_task",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Getter
@Setter
public class TerminationInspectionTask extends MetaModel {

    @FabosJsonField(
            views = @View(title = "编号"),
            edit = @Edit(title = "编号", notNull = true,readonly = @Readonly),
            dynamicField = @DynamicField(dependFiled = @DependFiled(buttonName = "生成", changeBy = "id",
                    dynamicHandler = PlanCodeGenerateDynamicHandler.class))
    )
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "检验状态"),
            edit = @Edit(title = "检验状态", type = EditType.CHOICE,search = @Search(),readonly = @Readonly,
                    choiceType = @ChoiceType(fetchHandler = TaskBusinessStateEnum.class))
    )
    private String businessState;

    @FabosJsonField(
            views = @View(title = "当前状态"),
            edit = @Edit(title = "当前状态", type = EditType.CHOICE,search = @Search(),readonly = @Readonly,
                    choiceType = @ChoiceType(fetchHandler = OrderCurrentStateEnum.class))
    )
    private String currentState;

    @FabosJsonField(
            views = @View(title = "创建类型"),
            edit = @Edit(title = "创建类型", type = EditType.CHOICE,search = @Search(),readonly = @Readonly,
                    choiceType = @ChoiceType(fetchHandler = TaskBuildTypeEnum.class))
    )
    private String buildType;

    @FabosJsonField(
            views = @View(title = "是否加急"),
            edit = @Edit(title = "是否加急", defaultVal = "false",readonly = @Readonly
            )
    )
    private Boolean isUrgent;

    @FabosJsonField(
            views = @View(title = "是否留样"),
            edit = @Edit(title = "是否留样", defaultVal = "false",readonly = @Readonly
            )
    )
    private Boolean isKeepSample;

    @FabosJsonField(
            views = @View(title = "标准值"),
            edit = @Edit(title = "标准值",notNull = true,
                    numberType = @NumberType(min=0,max = 9999999,precision = 2),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "isKeepSample == true"))
    )
    private Double keepSampleQuantity;

    @FabosJsonField(
            views = @View(title = "检验结果"),
            edit = @Edit(title = "检验结果", type = EditType.CHOICE,search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = InspectionResultEnum.class))
    )
    private String inspectionResult;

    @FabosJsonField(
            views = @View(title = "不合格等级"),
            edit = @Edit(title = "不合格等级", notNull = true,type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = UnqualifiedLevelEnum.class),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "inspectionResult == 'UNQUALIFIED'"))
    )
    private String unqualifiedLevel;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "检验通知物资",column = "generalCode", type = ViewType.TABLE_FORM),
            edit = @Edit(title = "检验通知物资",
                    type = EditType.REFERENCE_TABLE,allowAddMultipleRows = false,
                    notNull = true,
                    filter = @Filter(value = "InspectionRequest.type = 'Purchase'"),
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode")
            )
    )
    private InspectionRequest inspectionRequest;

    @FabosJsonField(
            views = @View(title = "供应商名称"),
            edit = @Edit(title = "供应商名称", search = @Search(vague = true)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inspectionRequest", beFilledBy = "supplierName"))
    )
    private String supplierName;

    @Transient
    @FabosJsonField(
            views = @View(title = "检验部门", show = false, column = "name"),
            edit = @Edit(title = "检验部门",notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType()
            )
    )
    private OrgMTO orgMTO;

    @FabosJsonField(
            views = @View(title = "检验部门id",show = false),
            edit = @Edit(title = "检验部门id", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "orgMTO", beFilledBy = "id"))
    )
    private String inspectionDepartment;

    @FabosJsonField(
            views = @View(title = "检验部门"),
            edit = @Edit(title = "检验部门", search = @Search(vague = true)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "orgMTO", beFilledBy = "name"))
    )
    private String inspectionDepartmentName;

    @FabosJsonField(
            views = @View(title = "检验人id",show = false),
            edit = @Edit(title = "检验人id", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inspectionRequest", beFilledBy = "inspector"))
    )
    private String inspector;

    @FabosJsonField(
            views = @View(title = "检验人"),
            edit = @Edit(title = "检验人", search = @Search(vague = true)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inspectionRequest", beFilledBy = "inspectorName"))
    )
    private String inspectorName;

    @FabosJsonField(
            views = @View(title = "检验物资编码"),
            edit = @Edit(title = "检验物资编码", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inspectionRequest", beFilledBy = "materialCode"))
    )
    private String materialCode;

    @FabosJsonField(
            views = @View(title = "检验物资名称"),
            edit = @Edit(title = "检验物资名称", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inspectionRequest", beFilledBy = "materialName"))
    )
    private String materialName;

    @FabosJsonField(
            views = @View(title = "来料批号"),
            edit = @Edit(title = "来料批号"),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inspectionRequest", beFilledBy = "originLotId"))
    )
    private String originLotId;

    @FabosJsonField(
            views = @View(title = "来料数量"),
            edit = @Edit(title = "来料数量", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inspectionRequest", beFilledBy = "arrivalQuantity"))
    )
    private Double arrivalQuantity;

    @FabosJsonField(
            views = @View(title = "样品数量"),
            edit = @Edit(title = "样品数量", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inspectionRequest", beFilledBy = "sampleQuantity"))
    )
    private Double sampleQuantity;

    @FabosJsonField(
            views = @View(title = "单位"),
            edit = @Edit(title = "单位", notNull = true),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inspectionRequest", beFilledBy = "measureUnit"))
    )
    private String unit;

    @FabosJsonField(
            views = @View(title = "规格"),
            edit = @Edit(title = "规格"),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inspectionRequest", beFilledBy = "materialSpecification"))
    )
    private String materialSpecification;

    @FabosJsonField(
            views = @View(title = "物资分级"),
            edit = @Edit(title = "物资分级"),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inspectionRequest", beFilledBy = "materialLevel"))
    )
    private String materialLevel;

    @FabosJsonField(
            views = @View(title = "取样数量"),
            edit = @Edit(title = "取样数量", readonly = @Readonly)
    )
    private Double allInspectionItemQuantity;

    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumn(name = "inspection_task_id")
    @OrderBy
    @FabosJsonField(
            views = @View(title = "检验物资明细", type= ViewType.TABLE_VIEW,index = 8),
            edit = @Edit(title = "检验物资明细",type = EditType.TAB_REFERENCE_GENERATE),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "inspectionRequest", dynamicHandler = RequestDynamicHandler.class)),
            referenceGenerateType = @ReferenceGenerateType()
    )
    private List<InspectionTaskDetail> inspectionTaskDetailList;

}
