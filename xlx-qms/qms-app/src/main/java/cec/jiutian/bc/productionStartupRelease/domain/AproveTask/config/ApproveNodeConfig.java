package cec.jiutian.bc.productionStartupRelease.domain.AproveTask.config;


import cec.jiutian.common.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
@ConfigurationProperties(prefix = "qms.flow.production-release")
public class ApproveNodeConfig {

    private static Map<String, String> config = new HashMap<>();

    public static String getAssignee(String nodeCode) {
        return config.get(nodeCode);
    }

    public static List<String> getAssigneeIds(String nodeCode) {
        String assignee = getAssignee(nodeCode);
        if (StringUtils.isBlank(assignee)) {
            throw new ServiceException("请前往Nacos配置节点审批人");
        }
        String[] assignees = assignee.replaceAll("Role:", "")
                .replaceAll("User:", "")
                .split(",");
        return Arrays.asList(assignees);
    }

    public Map<String, String> getConfig() {
        return config;
    }

    public void setConfig(Map<String, String> config) {
        log.info("production-release刷新配置：{}", config);
        this.config = config;
    }

}
