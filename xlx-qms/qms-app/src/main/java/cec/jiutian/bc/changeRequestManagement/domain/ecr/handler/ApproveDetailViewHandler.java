package cec.jiutian.bc.changeRequestManagement.domain.ecr.handler;

import cec.jiutian.bc.changeRequestManagement.domain.ecr.model.ApproveTask;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.model.ChangeRequest;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.mto.ApproveDetail;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.mto.ApproveTaskDetail;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.repository.ApproveTaskRepository;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class ApproveDetailViewHandler implements OperationHandler<ChangeRequest, ApproveDetail> {

    @Resource
    private ApproveTaskRepository approveTaskRepository;

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public ApproveDetail fabosJsonFormValue(List<ChangeRequest> data, ApproveDetail fabosJsonForm, String[] param) {

        if (CollectionUtils.isEmpty(data) || data.get(0).getId() == null) {
            throw new ServiceException("数据异常");
        }
        BeanUtils.copyProperties(data.get(0), fabosJsonForm);
        List<ApproveTask> approveTasks = approveTaskRepository.findByBusinessKeyOrderByCreateDate(data.get(0).getId());
        if (CollectionUtils.isEmpty(approveTasks)) {
            throw new ServiceException("未查询到审批信息");
        }
        ArrayList<ApproveTaskDetail> details = new ArrayList<>(approveTasks.size());
        for (ApproveTask approveTask : approveTasks) {
            details.add(ApproveTaskDetail.create(approveTask));
        }
        fabosJsonForm.setApproveTaskDetails(details);
        return fabosJsonForm;
    }
}
