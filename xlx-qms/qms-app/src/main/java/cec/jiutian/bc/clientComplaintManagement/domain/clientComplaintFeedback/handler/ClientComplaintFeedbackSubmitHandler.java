package cec.jiutian.bc.clientComplaintManagement.domain.clientComplaintFeedback.handler;

import cec.jiutian.bc.clientComplaintManagement.domain.clientComplaintFeedback.enumration.ClientComplaintFeedbackStatusEnum;
import cec.jiutian.bc.clientComplaintManagement.domain.clientComplaintFeedback.model.ClientComplaintFeedback;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.enumration.CpmBusinessStateEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/05/20
 * @description TODO
 */
@Component
public class ClientComplaintFeedbackSubmitHandler implements OperationHandler<ClientComplaintFeedback, Void> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Transactional
    @Override
    public String exec(List<ClientComplaintFeedback> data, Void modelObject, String[] param) {
        ClientComplaintFeedback model = data.get(0);
        model.setBusinessStatus(ClientComplaintFeedbackStatusEnum.Enum.TO_BE_CONFIRMED.name());

        fabosJsonDao.update(model);
        return null;
    }
}
