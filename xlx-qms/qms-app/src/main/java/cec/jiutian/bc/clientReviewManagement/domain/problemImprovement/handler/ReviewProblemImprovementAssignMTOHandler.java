package cec.jiutian.bc.clientReviewManagement.domain.problemImprovement.handler;

import cec.jiutian.bc.clientComplaintManagement.enumeration.ReviewProblemImproveStatusEnum;
import cec.jiutian.bc.clientReviewManagement.domain.problemImprovement.model.ReviewProblemImprovement;
import cec.jiutian.bc.clientReviewManagement.domain.problemImprovement.mto.ReviewProblemImprovementAssignMTO;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/06/11
 * @description
 */
@Component
public class ReviewProblemImprovementAssignMTOHandler implements OperationHandler<ReviewProblemImprovement, ReviewProblemImprovementAssignMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<ReviewProblemImprovement> data, ReviewProblemImprovementAssignMTO modelObject, String[] param) {
        modelObject.setBusinessStatus(ReviewProblemImproveStatusEnum.Enum.ANALYZING.name());
        ReviewProblemImprovement model = data.get(0);
        BeanUtil.copyProperties(modelObject, model, "createBy", "createTime");
        fabosJsonDao.mergeAndFlush(model);
        return "alert(操作成功)";
    }

    @Override
    public ReviewProblemImprovementAssignMTO fabosJsonFormValue(List<ReviewProblemImprovement> data, ReviewProblemImprovementAssignMTO fabosJsonForm, String[] param) {
        ReviewProblemImprovement model = data.get(0);
        BeanUtil.copyProperties(model, fabosJsonForm);
        return fabosJsonForm;
    }
}
