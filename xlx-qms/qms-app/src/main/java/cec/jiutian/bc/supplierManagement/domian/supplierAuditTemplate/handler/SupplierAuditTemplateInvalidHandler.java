package cec.jiutian.bc.supplierManagement.domian.supplierAuditTemplate.handler;

import cec.jiutian.bc.modeler.enumration.StatusEnum;
import cec.jiutian.bc.supplierManagement.domian.supplierAuditTemplate.model.SupplierAuditTemplate;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class SupplierAuditTemplateInvalidHandler implements OperationHandler<SupplierAuditTemplate, Void> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<SupplierAuditTemplate> data, Void modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            SupplierAuditTemplate entity = data.get(0);
            entity.setStatus(StatusEnum.Enum.Invalid.name());
            fabosJsonDao.mergeAndFlush(entity);
        }

        return "success";
    }
}
