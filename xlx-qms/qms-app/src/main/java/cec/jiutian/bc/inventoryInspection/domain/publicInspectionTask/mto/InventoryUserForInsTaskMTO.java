package cec.jiutian.bc.inventoryInspection.domain.publicInspectionTask.mto;

import cec.jiutian.core.data.annotation.LinkTable;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.OperationFixed;
import cec.jiutian.view.SubTableField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;


@Entity
@LinkTable
@Table(name = "fd_meta_user")
@Getter
@Setter
@FabosJson(
        name = "用户",
        orderBy = "createTime desc",
        power = @Power(importable = false, print = false)
)
@OperationFixed()
public class InventoryUserForInsTaskMTO extends MetaModel {
    @FabosJsonField(
            views = @View(title = "账号", index = -9),
            edit = @Edit(title = "账号",
                    search = @Search(vague = true)
            )
    )
    @SubTableField
    private String account;

    @FabosJsonField(
            views = @View(title = "工号"),
            edit = @Edit(title = "工号",
                    search = @Search(vague = true))
    )
    @SubTableField
    private String employeeNumber;

    @Column(unique = true)
    @FabosJsonField(
            views = @View(title = "电话号码"),
            edit = @Edit(title = "电话号码",
                    search = @Search(vague = true)
            ))
    @SubTableField
    private String phoneNumber;

    @FabosJsonField(
            views = @View(title = "姓名"),
            edit = @Edit(title = "姓名", search = @Search(vague = true))
    )
    @SubTableField
    private String name;

}
