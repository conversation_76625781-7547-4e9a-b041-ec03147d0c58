package cec.jiutian.bc.qualityExceptionManagement.domain.reworkTask.mto;

import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleBaseModel;
import cec.jiutian.bc.materialInspect.enumeration.InspectionResultEnum;
import cec.jiutian.bc.processInspect.enumeration.InspectionPlanDetailTypeEnum;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.Search;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

@Table(name = "pi_inspection_task",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Getter
@Setter
public class IPQCInspectionMTO extends NamingRuleBaseModel {


        @FabosJsonField(
                views = @View(title = "实际取样批号"),
                edit = @Edit(title = "实际取样批号", show = false)
        )
        private String actualLotSerialId;

        @FabosJsonField(
                views = @View(title = "过程检类型"),
                edit = @Edit(title = "过程检类型", notNull = true, type = EditType.CHOICE,
                        choiceType = @ChoiceType(fetchHandler = InspectionPlanDetailTypeEnum.class))
        )
        private String processInspectionType;

        @FabosJsonField(
                views = @View(title = "工艺工序id", show = false),
                edit = @Edit(title = "工艺工序id", show = false)
        )
        private Long processOperationId;

        @FabosJsonField(
                views = @View(title = "工序编码", show = false),
                edit = @Edit(title = "工序编码", show = false)
        )
        private String operationCode;

        @FabosJsonField(
                views = @View(title = "工序名称"),
                edit = @Edit(title = "工序名称", readonly = @Readonly)
        )
        private String operationName;

        @FabosJsonField(
                views = @View(title = "检验结果"),
                edit = @Edit(title = "检验结果", show = false, type = EditType.CHOICE, search = @Search(),
                        choiceType = @ChoiceType(fetchHandler = InspectionResultEnum.class))
        )
        private String result;


}
