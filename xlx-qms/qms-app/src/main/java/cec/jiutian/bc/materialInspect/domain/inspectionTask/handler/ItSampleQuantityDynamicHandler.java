package cec.jiutian.bc.materialInspect.domain.inspectionTask.handler;

import cec.jiutian.bc.basicData.service.BasicDataService;
import cec.jiutian.bc.generalModeler.enumeration.YesOrNoEnum;
import cec.jiutian.bc.materialInspect.domain.inspectionTask.model.IncomingInspectionTask;
import cec.jiutian.bc.materialInspect.domain.inspectionTask.model.InspectionTaskDetail;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/4/25
 * @description TODO
 */
@Component
public class ItSampleQuantityDynamicHandler implements DependFiled.DynamicHandler<IncomingInspectionTask> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private BasicDataService basicDataService;

    @Override
    public Map<String, Object> handle(IncomingInspectionTask incomingInspectionTask) {
        Map<String, Object> result = new HashMap<>();
        double sampleQuantity = 0D;
        boolean flag = false;
        if (CollectionUtils.isNotEmpty(incomingInspectionTask.getInspectionTaskDetailList())) {
            for (InspectionTaskDetail taskDetail : incomingInspectionTask.getInspectionTaskDetailList()) {
                if (taskDetail.getSampleFlag().equals(YesOrNoEnum.Enum.Y.name())) {
                    sampleQuantity = sampleQuantity + taskDetail.getLotQuantity();
                    flag = true;
                }
            }
        }

        if (flag) {
            result.put("sampleQuantity",sampleQuantity);
        }
        return result;
    }
}
