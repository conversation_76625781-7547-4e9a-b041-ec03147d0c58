package cec.jiutian.bc.systemAuditManage.domain.systemAuditListOfIssue.mto;

import cec.jiutian.bc.flow.domain.model.entity.ExamineModel;
import cec.jiutian.bc.materialInspect.domain.publicInspectionTask.mto.UserForInsTaskMTO;
import cec.jiutian.bc.mto.OrgMTO;
import cec.jiutian.bc.systemAuditManage.domain.saRectificationMeasuresInventory.enumration.SaRectMeasuresInventoryStatusEnum;
import cec.jiutian.bc.systemAuditManage.domain.systemAuditListOfIssue.model.SystemAuditListOfIssue;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Filter;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
/**
 * <AUTHOR>
 * @date 2025/5/13
 * @description TODO
 */
@FabosJson(
        name = "处理"
)
@Table(name = "qms_sam_rectification_measures_inventory")
@Entity
@Getter
@Setter
public class SystemAuditListOfIssueDealMTO extends ExamineModel {
    @ManyToOne
    @FabosJsonField(
            views = @View(title = "问题清单编号",column = "listOfIssueFormNumber", type = ViewType.TABLE_FORM),
            edit = @Edit(title = "问题清单编号",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    show = false,
                    filter = @Filter(value = "SystemAuditListOfIssue.businessStatus = 'RELEASE'"),
                    referenceTableType = @ReferenceTableType(id = "id", label = "listOfIssueFormNumber")
            )
    )
    private SystemAuditListOfIssue systemAuditListOfIssue;

    @FabosJsonField(
            views = @View(title = "不合格原因"),
            edit = @Edit(title = "不合格原因",
                    type = EditType.TEXTAREA
            )
    )
    private String nonConformanceReason;

    @FabosJsonField(
            views = @View(title = "整改措施"),
            edit = @Edit(title = "整改措施",
                    type = EditType.TEXTAREA
            )
    )
    private String rectificationMeasures;

    @FabosJsonField(
            views = @View(title = "责任部门", column = "name"),
            edit = @Edit(title = "责任部门",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "id")
            )
    )
    @ManyToOne
    @JoinColumn(name = "response_department_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private OrgMTO orgMTO;


    @FabosJsonField(
            views = @View(title = "责任人", column = "name"),
            edit = @Edit(title = "责任人",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "id")
            )
    )
    @ManyToOne
    @JoinColumn(name = "response_user_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private UserForInsTaskMTO userForInsTaskMTO;

    @FabosJsonField(
            views = @View(title = "措施纳期"),
            edit = @Edit(title = "措施纳期",
                    dateType = @DateType(type = DateType.Type.DATE_TIME),
                    notNull = true
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date measureDeliveryTime;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",
                    show = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = SaRectMeasuresInventoryStatusEnum.class)
            )
    )
    private String businessStatus;
}
