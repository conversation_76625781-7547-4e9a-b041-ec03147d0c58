package cec.jiutian.bc.changeRequestManagement.domain.ecr.mto;

import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.BoolType;
import cec.jiutian.view.field.edit.DependFieldDisplay;
import cec.jiutian.view.field.edit.ReferenceTableType;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@FabosJson(name = "候选角色")
@Getter
@Setter
public class ApproveViewMTO extends BaseModel {

    @FabosJsonField(
            views = @View(title = "是否涉及客户"),
            edit = @Edit(title = "是否涉及客户",
                    type = EditType.BOOLEAN,
                    boolType = @BoolType(trueText = "是", falseText = "否"),
                    notNull = true
            )
    )
    private Boolean involveCustomer;

    @FabosJsonField(
            views = @View(title = "候选角色",column = "name"),
            edit = @Edit(title = "候选角色", notNull = true,
                    type = EditType.TAB_TABLE_REFER,
                    referenceTableType = @ReferenceTableType
            )
    )
    private List<RoleMTO> roles;

    @FabosJsonField(
            views = @View(title = "市场部负责人",column = "name"),
            edit = @Edit(title = "市场部负责人",
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "involveCustomer == true"),
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    notNull = true,
                    referenceTableType = @ReferenceTableType()
            )

    )
    private RoleMTO marketRole;

}
