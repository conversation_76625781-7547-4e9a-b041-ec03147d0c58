package cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.mto;

import cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.enumration.ArmVerificationResultEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.AttachmentType;
import cec.jiutian.view.field.edit.ChoiceType;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;


/**
 * <AUTHOR>
 * @date 2025/5/30
 * @description
 */
@FabosJson(
        name = "关闭"
)
@Table(name = "qms_arm_accident_report_task")
@Entity
@Getter
@Setter
public class AccidentReportTaskCloseMTO extends MetaModel {
    @FabosJsonField(
            views = @View(title = "关闭审批意见", show = false),
            edit = @Edit(title = "关闭审批意见",
                    type = EditType.CHOICE,
                    notNull = true,
                    choiceType = @ChoiceType(fetchHandler = ArmVerificationResultEnum.class)
            )
    )
    private String closeApprovalComments;

    @FabosJsonField(
            views = @View(title = "意见说明"),
            edit = @Edit(title = "意见说明",
                    type = EditType.TEXTAREA)
    )
    private String opinionExplanation;

    @FabosJsonField(
            views = @View(title = "附件"),
            edit = @Edit(title = "附件", type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType)
    )
    private String closeAttachments;
}
