package cec.jiutian.bc.productionStartupRelease.domain.productionStartupRelease.model;

import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "qms_prcs_oprtn")
@Setter
@Getter
@FabosJson(
        name = "工序",
        orderBy = "createTime desc",
        power = @Power(add = false, edit = false, delete = false, print = false, importable = false)
)
public class ProductProcess extends MetaModel {

    @FabosJsonField(
            views = @View(title = "原ID",show = false),
            edit = @Edit(title = "原ID",show = false)
    )
    @Column(columnDefinition = "int8")
    private Long originalId;


    @FabosJsonField(
            views = @View(title = "工序编码"),
            edit = @Edit(title = "工序编码",
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "prcs_oprtn_cd", length = 40)
    private String code;

    @FabosJsonField(
            views = @View(title = "工序名称"),
            edit = @Edit(title = "工序名称",
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "prcs_oprtn_nm", length = 40)
    private String name;

}
