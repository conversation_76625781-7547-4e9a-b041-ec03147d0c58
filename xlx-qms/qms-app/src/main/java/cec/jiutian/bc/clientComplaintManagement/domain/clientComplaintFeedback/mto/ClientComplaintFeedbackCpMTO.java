package cec.jiutian.bc.clientComplaintManagement.domain.clientComplaintFeedback.mto;

import cec.jiutian.bc.clientComplaintManagement.domain.clientComplaintFeedback.handler.ClientComplaintFeedbackCpDynamicHandler;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.enumration.CpmBusinessStateEnum;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.enumration.RelatedDocumentTypeEnum;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.handler.CorrectPreventMeasureDynamicHandler;
import cec.jiutian.bc.urm.domain.user.entity.User;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/5/27
 * @description TODO
 */
@FabosJson(
        name = "创建预防纠正措施"
)
@Table(name = "qms_cpm_correct_prevent_measure",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"correctPreventMeasureFormNumber"})
        }
)
@Entity
@Getter
@Setter
public class ClientComplaintFeedbackCpMTO extends MetaModel {

    @FabosJsonField(
            views = @View(title = "单据编号"),
            edit = @Edit(title = "单据编号",
                    notNull = true
            ),
            dynamicField = @DynamicField(dependFiled = @DependFiled(buttonName = "生成", changeBy = "id",
                    dynamicHandler = ClientComplaintFeedbackCpDynamicHandler.class))
    )
    private String correctPreventMeasureFormNumber;

    @FabosJsonField(
            views = @View(title = "创建日期"),
            edit = @Edit(title = "创建日期",
                    show = false,
                    type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @CreationTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date createdTime;

    @FabosJsonField(
            views = @View(title = "关联单据类型"),
            edit = @Edit(title = "关联单据类型",
                    type = EditType.CHOICE,
                    notNull = true,
                    readonly = @Readonly(),
                    choiceType = @ChoiceType(fetchHandler = RelatedDocumentTypeEnum.class)
            )
    )
    private String relatedDocumentType;

    @FabosJsonField(
            views = @View(title = "关联单据编码",show = false),
            edit = @Edit(title = "关联单据编码",
                    show = false
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "relatedDocumentMTO", beFilledBy = "code"))
    )
    private String relatedDocumentMTOCode;

    @FabosJsonField(
            views = @View(title = "关联单据"),
            edit = @Edit(title = "关联单据",
                    readonly = @Readonly,
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "relatedDocumentMTO", beFilledBy = "name"))
    )
    private String relatedDocumentMTOName;

    @FabosJsonField(
            views = @View(title = "不合格（潜在不合格）陈述"),
            edit = @Edit(title = "不合格（潜在不合格）陈述",
                    type = EditType.TEXTAREA)
    )
    private String nonConformanceStatement;

    @FabosJsonField(
            views = @View(title = "不合格（潜在不合格）情况核实（初步原因分析）"),
            edit = @Edit(title = "不合格（潜在不合格）情况核实（初步原因分析）",
                    type = EditType.TEXTAREA)
    )
    private String verificationNonConformity;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "提出人", column = "name"),
            edit = @Edit(title = "提出人", type = EditType.REFERENCE_TABLE, show = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name")
            )
    )
    private User createUser;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",
                    show = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = CpmBusinessStateEnum.class)
            )
    )
    private String businessState;
}
