package cec.jiutian.bc.samplingTaskManagement.domain.otherSamplingTask.handler;

import cec.jiutian.bc.materialInspect.port.client.MaterialInspectFeignClient;
import cec.jiutian.bc.modeler.enumration.SampleTypeEnum;
import cec.jiutian.bc.modeler.enumration.TaskBusinessStateEnum;
import cec.jiutian.bc.samplingTaskManagement.domain.otherSamplingTask.model.OtherSamplingTask;
import cec.jiutian.bc.samplingTaskManagement.domain.otherSamplingTask.model.OtherSamplingTaskOpr;
import cec.jiutian.bc.samplingTaskManagement.domain.otherSamplingTask.model.OtherSamplingTaskOprDetail;
import cec.jiutian.bc.samplingTaskManagement.domain.otherSamplingTask.remote.feign.LmKsStockFeign;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2025/5/12
 * @description TODO
 */
@Slf4j
@Component
public class GetOtherSampleOperationHandler implements OperationHandler<OtherSamplingTask, OtherSamplingTaskOpr> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private LmKsStockFeign lmKsStockFeign;

    @Resource
    private MaterialInspectFeignClient materialInspectFeignClient;

    @Override
    @Transactional
    public String exec(List<OtherSamplingTask> data, OtherSamplingTaskOpr modelObject, String[] param) {
        if (modelObject != null) {
            OtherSamplingTask condition = new OtherSamplingTask();
            condition.setGeneralCode(modelObject.getGeneralCode());
            OtherSamplingTask otherSamplingTask = fabosJsonDao.selectOne(condition);
            otherSamplingTask.setBusinessState(TaskBusinessStateEnum.Enum.SAMPLING_FINISH.name());
            //调用lims接口
            doLimsFeign(modelObject);
            fabosJsonDao.mergeAndFlush(otherSamplingTask);

            // 库存检验类型调用wms出库申请创建接口
            if (SampleTypeEnum.Enum.BENCH_INVENTORY_SAMPLING.name().equals(otherSamplingTask.getInspectionType()) ||
                    SampleTypeEnum.Enum.MSA_INVENTORY_SAMPLING.name().equals(otherSamplingTask.getInspectionType()) ||
                    SampleTypeEnum.Enum.QC_INVENTORY_SAMPLING.name().equals(otherSamplingTask.getInspectionType())) {
                Map<String, Object> outboundRequest = createOutboundRequest(otherSamplingTask);
                materialInspectFeignClient.createOutboundRequest(outboundRequest);
            }

        }
        return "alert(操作成功)";
    }

    private void doLimsFeign(OtherSamplingTaskOpr modelObject) {
        log.info("-----------------开始执行lims留样取样-----------------");
        try {
            lmKsStockFeign.createLmKsStockOut(modelObject.getOtherSamplingTaskCode(), modelObject.getInspectionType(), modelObject.getAllInspectionItemQuantity());
        } catch (Exception e) {
            log.error("调用lims接口出错", e);
            throw new FabosJsonApiErrorTip("调用lims接口,创建留样出库单出错");
        }
    }

    @Override
    public OtherSamplingTaskOpr fabosJsonFormValue(List<OtherSamplingTask> data, OtherSamplingTaskOpr fabosJsonForm, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            OtherSamplingTask otherSamplingTask = data.get(0);
            fabosJsonForm.setGeneralCode(otherSamplingTask.getGeneralCode());
            fabosJsonForm.setInspectionTaskCode(otherSamplingTask.getInspectionTaskCode());
            fabosJsonForm.setOtherSamplingTaskCode(otherSamplingTask.getGeneralCode());
            fabosJsonForm.setMaterialCode(otherSamplingTask.getMaterialCode());
            fabosJsonForm.setMaterialName(otherSamplingTask.getMaterialName());
            fabosJsonForm.setOriginLotId(otherSamplingTask.getOriginLotId());
            fabosJsonForm.setMaterialSpecification(otherSamplingTask.getMaterialSpecification());
            fabosJsonForm.setUnit(otherSamplingTask.getUnit());
            fabosJsonForm.setInspectionType(otherSamplingTask.getInspectionType());
            fabosJsonForm.setAllInspectionItemQuantity(otherSamplingTask.getAllInspectionItemQuantity());
            fabosJsonForm.setSamplingPoint(otherSamplingTask.getSamplePoint());
            fabosJsonForm.setSendInspectPoint(otherSamplingTask.getSendPoint());
            fabosJsonForm.setBusinessState(otherSamplingTask.getBusinessState());
            if (CollectionUtils.isNotEmpty(otherSamplingTask.getDetailList())) {
                List<OtherSamplingTaskOprDetail> detailList = new ArrayList<>();
                otherSamplingTask.getDetailList().forEach(otherSamplingTaskDetail -> {
                    OtherSamplingTaskOprDetail otherSamplingTaskOprDetail = new OtherSamplingTaskOprDetail();
                    otherSamplingTaskOprDetail.setItemId(otherSamplingTaskDetail.getItemId());
                    otherSamplingTaskOprDetail.setCode(otherSamplingTaskDetail.getCode());
                    otherSamplingTaskOprDetail.setName(otherSamplingTaskDetail.getName());
                    otherSamplingTaskOprDetail.setPackageType(otherSamplingTaskDetail.getPackageType());
                    otherSamplingTaskOprDetail.setSampleSize(otherSamplingTaskDetail.getSampleSize());
                    detailList.add(otherSamplingTaskOprDetail);
                });
                fabosJsonForm.setDetailList(detailList);
            }

        }
        return fabosJsonForm;
    }

    private Map<String, Object> createOutboundRequest(OtherSamplingTask otherSamplingTask) {
        Map<String, Object> result = new HashMap<>();
        UserContext.CurrentUser currentUser = UserContext.get();
        if (otherSamplingTask != null && currentUser != null) {
            result.put("inspectionTaskNumber", otherSamplingTask.getInspectionTaskCode());
            result.put("inspectItemType", otherSamplingTask.getInspectionType());
            result.put("username", currentUser.getUserName());
            result.put("org_id", otherSamplingTask.getOrgId());
            result.put("applyDate", new Date());
            List<Map<String, Object>> list = new ArrayList<>();
            Map<String, Object> detailMap = new HashMap<>();
            detailMap.put("inspectMaterialApplyDetailNumber", otherSamplingTask.getGeneralCode());
            detailMap.put("materialName", otherSamplingTask.getMaterialName());
            detailMap.put("materialCode", otherSamplingTask.getMaterialCode());
            detailMap.put("measureUnit", otherSamplingTask.getUnit());
            detailMap.put("serialLotId", otherSamplingTask.getOriginLotId());
            detailMap.put("inventoryId", otherSamplingTask.getWmsInventoryId());
            detailMap.put("requestQuantity", otherSamplingTask.getAllInspectionItemQuantity());
            list.add(detailMap);
            result.put("details", list);
        }
        return result;
    }
}
