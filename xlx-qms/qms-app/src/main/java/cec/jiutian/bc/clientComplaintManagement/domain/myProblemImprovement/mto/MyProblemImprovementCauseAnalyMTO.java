package cec.jiutian.bc.clientComplaintManagement.domain.myProblemImprovement.mto;

import cec.jiutian.bc.clientComplaintManagement.enumeration.ProblemImprovementStatusEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.frame.module.MetaUser;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DateType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/06/13
 * @description TODO
 */
@FabosJson(
        name = "原因分析"

)
@Table(name = "qms_ccm_problem_improvement")
@Entity
@Getter
@Setter
public class MyProblemImprovementCauseAnalyMTO extends MetaModel {
    @FabosJsonField(
            views = @View(title = "原因分析"),
            edit = @Edit(title = "原因分析",
                    notNull = true,
                    search = @Search(vague = true))
    )
    private String causeAnalysis;

    @FabosJsonField(
            views = @View(title = "整改措施"),
            edit = @Edit(title = "整改措施",
                    search = @Search(vague = true))
    )
    private String rectificationMeasures;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "负责人", column = "name"),
            edit = @Edit(title = "负责人",
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"))
    )
    private MetaUser director;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "验证人", column = "name"),
            edit = @Edit(title = "验证人",
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"))
    )
    private MetaUser verifier;

    @FabosJsonField(
            views = @View(title = "预防纠正措施"),
            edit = @Edit(title = "预防纠正措施",
                    search = @Search(vague = true))
    )
    private String preCorMeasure;

    @FabosJsonField(
            views = @View(title = "纳期"),
            edit = @Edit(title = "纳期",
                    dateType = @DateType(type = DateType.Type.DATE_TIME),
                    notNull = true,
                    readonly = @Readonly()
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date deliveryTime;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",
                    show = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ProblemImprovementStatusEnum.class)
            )
    )
    private String businessStatus;
}
