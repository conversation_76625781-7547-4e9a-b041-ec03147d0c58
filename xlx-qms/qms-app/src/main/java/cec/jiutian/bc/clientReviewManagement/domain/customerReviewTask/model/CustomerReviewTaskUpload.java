package cec.jiutian.bc.clientReviewManagement.domain.customerReviewTask.model;

import cec.jiutian.bc.clientReviewManagement.enumeration.CustomerReviewNatureEnum;
import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.ReferenceGenerateType;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.ChoiceType;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Entity
@Table(name = "qms_client_review_task",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        })
@Getter
@Setter
@FabosJson(
        name = "客户评审任务-资料上传"
)
@TemplateType(type = "multiTable")
public class CustomerReviewTaskUpload extends NamingRuleModel {

    @FabosJsonField(
            views = @View(title = "任务名称"),
            edit = @Edit(title = "任务名称", readonly = @Readonly)
    )
    private String name;


    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态", show = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = CustomerReviewNatureEnum.class))
    )
    private String currentState;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy
    @FabosJsonField(
            edit = @Edit(title = "资料详情", type = EditType.TAB_REFERENCE_GENERATE),
            views = @View(title = "资料详情", type = ViewType.TABLE_VIEW),
            referenceGenerateType = @ReferenceGenerateType(editable = {"attachment"})
    )
    private List<CustomerReviewTaskDetailUpload> details;

}
