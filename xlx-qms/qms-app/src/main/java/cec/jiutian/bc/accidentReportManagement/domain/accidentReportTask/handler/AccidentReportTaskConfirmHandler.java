package cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.handler;

import cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.enumration.ArmBusinessStatusEnum;
import cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.model.AccidentReportTask;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/30
 * @description
 */
@Component
public class AccidentReportTaskConfirmHandler implements OperationHandler<AccidentReportTask, Void> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Transactional
    @Override
    public String exec(List<AccidentReportTask> data, Void modelObject, String[] param) {
        AccidentReportTask model = data.get(0);
        model.setBusinessStatus(ArmBusinessStatusEnum.Enum.TO_BE_ANALYZED.name());

        fabosJsonDao.update(model);
        return null;
    }
}
