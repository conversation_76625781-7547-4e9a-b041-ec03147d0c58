package cec.jiutian.bc.productExamineManagement.domain.examineImplementPlan.mto;

import cec.jiutian.bc.productExamineManagement.enumration.ItemUnQualifiedLevelEnum;
import cec.jiutian.bc.productReturnInspection.enumeration.InspectionResultEnum;
import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DependFieldDisplay;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.Search;
import jakarta.persistence.Entity;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/6/24
 * @description TODO
 */
@Entity
@Data
@FabosJson(
        name = "产品审核实施计划执行详情MTO"
)
public class ProductExamineExecuteDetailMTO extends BaseModel {
    @FabosJsonField(
            views = @View(title = "检验项目名称"),
            edit = @Edit(title = "检验项目名称", search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    private String name;

    @FabosJsonField(
            views = @View(title = "检验结果"),
            edit = @Edit(title = "检验结果", type = EditType.CHOICE,search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = InspectionResultEnum.class))
    )
    private String inspectionResult;

    @FabosJsonField(
            views = @View(title = "不合格类型"),
            edit = @Edit(title = "不合格类型",notNull = true,type = EditType.CHOICE,choiceType = @ChoiceType(fetchHandler = ItemUnQualifiedLevelEnum.class),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "inspectionResult == 'UNQUALIFIED'"))
    )
    private String unQualifiedLevel;
}
