package cec.jiutian.bc.clientComplaintManagement.domain.clientComplaintFeedback.handler;

import cec.jiutian.bc.clientComplaintManagement.domain.clientComplaintFeedback.enumration.ClientComplaintFeedbackStatusEnum;
import cec.jiutian.bc.clientComplaintManagement.domain.clientComplaintFeedback.model.ClientComplaintFeedback;
import cec.jiutian.bc.clientComplaintManagement.domain.problemImprovement.model.ProblemImprovement;
import cec.jiutian.bc.clientComplaintManagement.enumeration.ProblemImprovementStatusEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/27
 * @description TODO
 */
@Component
public class ClientComplaintFeedbackCloseHandler implements OperationHandler<ClientComplaintFeedback, Void> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Transactional
    @Override
    public String exec(List<ClientComplaintFeedback> data, Void modelObject, String[] param) {
        ClientComplaintFeedback model = data.get(0);
        //关闭所有的问题改善
        ProblemImprovement condition = new ProblemImprovement();
        condition.setClientComplaintFeedback(model);
        List<ProblemImprovement> problemImprovementList = fabosJsonDao.select(condition);
        for (ProblemImprovement problemImprovement : problemImprovementList) {
            problemImprovement.setBusinessStatus(ProblemImprovementStatusEnum.Enum.CLOSED.name());
            fabosJsonDao.mergeAndFlush(problemImprovement);
        }

        model.setBusinessStatus(ClientComplaintFeedbackStatusEnum.Enum.CLOSED.name());
        fabosJsonDao.update(model);
        return null;
    }
}
