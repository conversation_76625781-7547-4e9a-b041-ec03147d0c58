package cec.jiutian.bc.productionStartupRelease.enums;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

public class ReleaseLevelEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum data : Enum.values()) {
            list.add(new VLModel(data.getValue(), data.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {

        l1("1"),
        l2("2"),
        l3("3"),
        l4("4"),
        ;

        private final String value;

    }

}
