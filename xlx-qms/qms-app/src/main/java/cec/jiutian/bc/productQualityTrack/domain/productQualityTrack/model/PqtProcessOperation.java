package cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.model;

import cec.jiutian.bc.modeler.domain.inspectionItem.model.InspectionItem;
import cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.enumration.ProcessOperationStatusEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Filter;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/15
 * @description
 */
@FabosJson(
        name = "工序列表",
        orderBy = "PqtProcessOperation.createTime desc"
)
@Table(name = "qms_pqt_process_operation")
@Entity
@Getter
@Setter
public class PqtProcessOperation extends MetaModel {
    @FabosJsonField(
            views = @View(title = "工序编码", show = false),
            edit = @Edit(title = "工序编码", show = false)
    )
    private String processOperationCode;

    @FabosJsonField(
            views = @View(title = "工序名称"),
            edit = @Edit(title = "工序名称",
                    search = @Search(vague = true)
            )
    )
    private String processOperationName;

    @FabosJsonField(
            views = @View(title = "物料"),
            edit = @Edit(title = "物料",
                    search = @Search(vague = true))
    )
    private String pqtMaterial;

    @ManyToMany
    @JoinTable(
            name = "e_pqt_inspection_item",
            inverseForeignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT),
            joinColumns = @JoinColumn(name = "process_operation_list_id", referencedColumnName = "id"),
            inverseJoinColumns = @JoinColumn(name = "inspection_item_id", referencedColumnName = "id"))
    @FabosJsonField(
            views = @View(title = "检验项目",type = ViewType.TABLE_VIEW),
            edit = @Edit(title = "检验项目", type = EditType.TAB_TABLE_REFER, search = @Search(vague = true),
                    tabTableReferType = @TabTableReferType(type = TabTableReferType.SelectShowTypeMTM.LIST,
                            label = "name"),
                    filter = @Filter(value = "InspectionItem.status = 'Effective'")
            )
    )
    private List<InspectionItem> inspectionInstrumentList;

    @FabosJsonField(
            views = @View(title = "产品列表",
                    column = "processOperationName",
                    type = ViewType.TABLE_VIEW),
            edit = @Edit(title = "产品列表",
                    type = EditType.TAB_REFERENCE_GENERATE
            )
    )
    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumn(name = "qms_pqt_process_operation_id")
    private List<PqtProductBatchNumber> pqtProductBatchNumberList;

    @FabosJsonField(
            views = @View(title = "综合判断"),
            edit = @Edit(title = "综合判断",
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ProcessOperationStatusEnum.class)
            )
    )
    private String comprehensiveJudgment;

    @FabosJsonField(
            views = @View(title = "判定人id", show = false),
            edit = @Edit(title = "判定人id", show = false)
    )
    private String judgingPersonId;

    @FabosJsonField(
            views = @View(title = "判定人"),
            edit = @Edit(title = "判定人")
    )
    private String judgingPerson;

    @FabosJsonField(
            views = @View(title = "备注"),
            edit = @Edit(title = "备注",
                    type = EditType.TEXTAREA)
    )
    private String remark;
}
