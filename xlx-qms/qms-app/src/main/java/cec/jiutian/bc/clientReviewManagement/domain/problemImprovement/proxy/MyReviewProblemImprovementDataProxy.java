package cec.jiutian.bc.clientReviewManagement.domain.problemImprovement.proxy;

import cec.jiutian.bc.clientReviewManagement.domain.problemImprovement.model.MyReviewProblemImprovement;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.view.fun.DataProxy;
import cec.jiutian.view.query.Condition;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/05/20
 * @description TODO
 */
@Component
public class MyReviewProblemImprovementDataProxy implements DataProxy<MyReviewProblemImprovement> {
    @Override
    public String beforeFetch(List<Condition> conditions) {
        String userId = UserContext.getUserId();
        if (StringUtils.isBlank(userId)) {
            throw new RuntimeException("当前用户未登录");
        }
        return "MyReviewProblemImprovement.responsiblePersonId = '" + userId + "'";
    }
}
