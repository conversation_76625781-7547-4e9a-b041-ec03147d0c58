package cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.mto;

import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.enumration.ProgressEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.ReferenceGenerateType;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.ChoiceType;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@FabosJson(
        name = "执行",
        orderBy = "MyCorrectiveTaskExecMTO.createTime desc"
)
@Table(name = "qms_cpm_correct_prevent_measure")
@Entity
@Getter
@Setter
public class MyPreventTaskExecMTO extends MetaModel {
    @FabosJsonField(
            views = @View(title = "分析状态", show = false),
            edit = @Edit(title = "分析状态",
                    readonly = @Readonly(),
                    type = EditType.CHOICE,
                    show = false,
                    choiceType = @ChoiceType(fetchHandler = ProgressEnum.class)
            )
    )
    private String analyticalState;

    @FabosJsonField(
            edit = @Edit(title = "预防措施", type = EditType.TAB_REFERENCE_GENERATE),
            views = @View(title = "预防措施", type = ViewType.TABLE_VIEW),
            referenceGenerateType = @ReferenceGenerateType(editable = {"improvingProgress","completionDate","completeSupportMaterial"})
    )
    @JoinColumn(name = "qms_cpm_correct_prevent_measure_id")
    @OneToMany(cascade = CascadeType.ALL)
    @OrderBy
    private List<MyPreventMeasure> preventMeasureList;
}
