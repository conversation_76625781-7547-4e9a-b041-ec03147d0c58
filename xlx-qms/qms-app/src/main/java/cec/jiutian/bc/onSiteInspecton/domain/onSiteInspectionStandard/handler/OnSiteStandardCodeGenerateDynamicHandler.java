package cec.jiutian.bc.onSiteInspecton.domain.onSiteInspectionStandard.handler;

import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.modeler.enumration.NamingRuleCodeEnum;
import cec.jiutian.bc.onSiteInspecton.domain.onSiteInspectionStandard.model.OnSiteInspectionStandard;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/2/27
 * @description 编号生成handler
 */
@Component
public class OnSiteStandardCodeGenerateDynamicHandler implements DependFiled.DynamicHandler<OnSiteInspectionStandard> {

    @Resource
    private NamingRuleService namingRuleService;

    @Override
    public Map<String, Object> handle(OnSiteInspectionStandard onSiteInspectionStandard) {
        Map<String, Object> map = new HashMap<>();
        map.put("generalCode",String.valueOf(namingRuleService.getNameCode(NamingRuleCodeEnum.OnSiteInspectionStandard.name(), 1, null).get(0)));
        return map;
    }
}
