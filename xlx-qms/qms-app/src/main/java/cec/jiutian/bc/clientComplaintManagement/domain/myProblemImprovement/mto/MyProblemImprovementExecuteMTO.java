package cec.jiutian.bc.clientComplaintManagement.domain.myProblemImprovement.mto;

import cec.jiutian.bc.clientComplaintManagement.enumeration.ProblemImprovementStatusEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.AttachmentType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DateType;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/06/13
 * @description TODO
 */
@FabosJson(
        name = "执行"

)
@Table(name = "qms_ccm_problem_improvement")
@Entity
@Getter
@Setter
public class MyProblemImprovementExecuteMTO extends MetaModel {
    @FabosJsonField(
            views = @View(title = "实际完成日期"),
            edit = @Edit(title = "实际完成日期",
                    dateType = @DateType(type = DateType.Type.DATE_TIME),
                    notNull = true
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date actualFinishTime;

    @FabosJsonField(
            views = @View(title = "执行附件"),
            edit = @Edit(title = "执行附件",
                    type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType)
    )
    private String executeDocument;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",
                    show = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ProblemImprovementStatusEnum.class)
            )
    )
    private String businessStatus;
}
