package cec.jiutian.bc.supplierManagement.domian.auditImplementPlan.model;

import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleBaseModel;
import cec.jiutian.bc.supplierManagement.domian.annualAuditPlan.model.SupplierAnnualAuditPlan;
import cec.jiutian.bc.supplierManagement.domian.auditImplementPlan.proxy.SupplierAuditImplementPlanScoreDataProxy;
import cec.jiutian.bc.supplierManagement.domian.supplierAuditTemplate.model.SupplierAuditTemplate;
import cec.jiutian.bc.supplierManagement.domian.supplierInventory.model.SupplierInventory;
import cec.jiutian.bc.supplierManagement.enums.AuditImplementPlanStateEnum;
import cec.jiutian.bc.supplierManagement.enums.SupplierAuditTypeEnum;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.ReferenceGenerateType;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.AttachmentType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OrderBy;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Entity
@Table(name = "sm_audit_implement_plan",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        })
@Getter
@Setter
@FabosJson(
        name = "审核实施计划-打分",
        dataProxy = SupplierAuditImplementPlanScoreDataProxy.class
)
@TemplateType(type = "multiTable")
public class SupplierAuditImplementPlanScore extends NamingRuleBaseModel {

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "供应商", column = "supplierName"),
            edit = @Edit(title = "供应商", readonly = @Readonly,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "supplierName")
            )
    )
    private SupplierInventory supplierInventory;

    @FabosJsonField(
            views = @View(title = "审核类型"),
            edit = @Edit(title = "审核类型", readonly = @Readonly,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = SupplierAuditTypeEnum.class))
    )
    private String auditType;

    @FabosJsonField(
            views = @View(title = "审核组组长"),
            edit = @Edit(title = "审核组组长", readonly = @Readonly)
    )
    private String leaderName;

    @FabosJsonField(
            views = @View(title = "审核成员"),
            edit = @Edit(title = "审核成员", readonly = @Readonly)
    )
    private String memberName;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "年度审核计划", column = "generalCode"),
            edit = @Edit(title = "年度审核计划", readonly = @Readonly,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode")
            )
    )
    private SupplierAnnualAuditPlan annualAuditPlan;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "审核模板", column = "generalCode"),
            edit = @Edit(title = "审核模板", readonly = @Readonly,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode"),
                    queryCondition = "{\"type\":\"${auditType}\"}"
            )
    )
    private SupplierAuditTemplate auditTemplate;

    @FabosJsonField(
            views = @View(title = "附件"),
            edit = @Edit(title = "附件", readonly = @Readonly, type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(maxLimit = 10))
    )
    private String attachment;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态", show = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = AuditImplementPlanStateEnum.class))
    )
    private String currentState;

    @FabosJsonField(
            views = @View(title = "评分"),
            edit = @Edit(title = "评分", show = false)
    )
    private Double score;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "plan_id")
    @OrderBy
    @FabosJsonField(
            edit = @Edit(title = "审核项目详情", type = EditType.TAB_REFERENCE_GENERATE),
            views = @View(title = "审核项目详情", type = ViewType.TABLE_VIEW),
            referenceGenerateType = @ReferenceGenerateType(editable = {"score", "resultRecord", "qualifiedFlag", "unqualifiedDescription"})
    )
    private List<SupplierAuditImplementPlanDetailScore> details;

}
