package cec.jiutian.bc.changeRequestManagement.domain.bo;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.HashMap;

public class FlowNodeBO {

    private static HashMap<String, Node> approveChainMap = new HashMap<>();

    private static ArrayList<Node> approveChain  = new ArrayList<>();
    static {
        Node[] values = Node.values();
        for (Node node : values) {
            approveChain.add(node);
            approveChainMap.put(node.name(), node);
        }
    }

    public static Node getNode(String status) {
        return approveChainMap.get(status);
    }

    @Getter
    @AllArgsConstructor
    public enum Node {

        EXAMINE("审批"),

        REVIEW("评审"),

        APPROVE_1("变更批准"),

        APPROVE_2("变更批准"),

        VERIFY_REVIEW("验证审核"),

        VERIFY_REVIEW_A("A类验证审核"),

        VERIFY_APPROVE("验证批准"),

        WAIT_CLOSING("待闭环确认"),

        SUPPLEMENT_VERIFY("补充任务验证"),
        ;

        private String value;

    }
}
