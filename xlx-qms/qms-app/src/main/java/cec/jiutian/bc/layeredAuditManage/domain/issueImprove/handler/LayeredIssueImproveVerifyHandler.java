package cec.jiutian.bc.layeredAuditManage.domain.issueImprove.handler;

import cec.jiutian.bc.layeredAuditManage.domain.issueImprove.model.LayeredIssueImprove;
import cec.jiutian.bc.layeredAuditManage.domain.issueImprove.model.LayeredIssueImproveVerify;
import cec.jiutian.bc.layeredAuditManage.enumeration.LayeredIssueImproveStatusEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component
public class LayeredIssueImproveVerifyHandler implements OperationHandler<LayeredIssueImprove, LayeredIssueImproveVerify> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<LayeredIssueImprove> data, LayeredIssueImproveVerify modelObject, String[] param) {

        if (CollectionUtils.isNotEmpty(data)) {
            LayeredIssueImprove layeredIssueImprove = data.get(0);
            List<LayeredIssueImproveVerify> verifyList = new ArrayList<>();
            LayeredIssueImproveVerify verify = new LayeredIssueImproveVerify();
            BeanUtil.copyProperties(modelObject, verify);
            verifyList.add(verify);
            layeredIssueImprove.setLayeredIssueImproveVerifyList(verifyList);
            layeredIssueImprove.setStatus(LayeredIssueImproveStatusEnum.Enum.COMPLETED.name());
            fabosJsonDao.mergeAndFlush(layeredIssueImprove);
        }
        return "msg.success('操作成功')";
    }
}
