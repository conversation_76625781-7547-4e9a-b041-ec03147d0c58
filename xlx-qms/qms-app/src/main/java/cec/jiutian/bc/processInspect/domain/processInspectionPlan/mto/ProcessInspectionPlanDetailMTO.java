package cec.jiutian.bc.processInspect.domain.processInspectionPlan.mto;

import cec.jiutian.bc.processInspect.enumeration.InspectionPlanDetailTypeEnum;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import jakarta.persistence.Entity;
import lombok.Getter;
import lombok.Setter;

@FabosJson(
        name = "过程检验计划详情-执行末检"
)
@Entity
@Getter
@Setter
public class ProcessInspectionPlanDetailMTO extends MetaModel {

    @FabosJsonField(
            views = @View(title = "工序编码"),
            edit = @Edit(title = "工序编码", readonly = @Readonly)
    )
    private String operationCode;

    @FabosJsonField(
            views = @View(title = "工序名称"),
            edit = @Edit(title = "工序名称", readonly = @Readonly)
    )
    private String operationName;

    @FabosJsonField(
            views = @View(title = "类型"),
            edit = @Edit(title = "类型", readonly = @Readonly, type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = InspectionPlanDetailTypeEnum.class))
    )
    private String type;

    @FabosJsonField(
            views = @View(title = "当前状态"),
            edit = @Edit(title = "当前状态", readonly = @Readonly,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = OrderCurrentStateEnum.class))
    )
    private String currentState;

}
