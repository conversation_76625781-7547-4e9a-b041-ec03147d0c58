package cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.handler;

import cec.jiutian.bc.mto.ProcessFlowMTO;
import cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.enumration.ProgressEnum;
import cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.model.AbnormalFeedbackHandling;
import cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.mto.AbnormalFeedbackHandlingVerifyMTO;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/27
 * @description TODO
 */
@Component
public class AbnormalFeedbackHandlingVerifyHandler implements OperationHandler<AbnormalFeedbackHandling, AbnormalFeedbackHandlingVerifyMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<AbnormalFeedbackHandling> data, AbnormalFeedbackHandlingVerifyMTO modelObject, String[] param) {
        if (modelObject != null) {
            AbnormalFeedbackHandling model = data.get(0);
            BeanUtil.copyProperties(modelObject, model, "createBy", "createTime");
            model.setVerificationState(ProgressEnum.Enum.RUN_SUBMIT.name());

            fabosJsonDao.mergeAndFlush(model);
        }
        return "alert(操作成功)";
    }

    @Override
    public AbnormalFeedbackHandlingVerifyMTO fabosJsonFormValue(List<AbnormalFeedbackHandling> data, AbnormalFeedbackHandlingVerifyMTO fabosJsonForm, String[] param) {
        AbnormalFeedbackHandling abnormalFeedbackHandling = data.get(0);
        BeanUtil.copyProperties(abnormalFeedbackHandling, fabosJsonForm);
        if (fabosJsonForm.getProcessFlowMTOID() != null) {
            ProcessFlowMTO processFlowMTO = fabosJsonDao.findById(ProcessFlowMTO.class, fabosJsonForm.getProcessFlowMTOID());
            if (processFlowMTO != null) {
                fabosJsonForm.setProcessFlowMTO(processFlowMTO);
            } else {
                throw new FabosJsonApiErrorTip("未查工序流水号的数据，请确认");
            }
        }
        return fabosJsonForm;
    }
}
