package cec.jiutian.bc.clientComplaintManagement.domain.clientComplaintFeedback.model;

import cec.jiutian.bc.clientComplaintManagement.domain.clientComplaintFeedback.enumration.CfbCorrectPreventProgressEnum;
import cec.jiutian.bc.clientComplaintManagement.domain.clientComplaintFeedback.enumration.ClientComplaintFeedbackStatusEnum;
import cec.jiutian.bc.clientComplaintManagement.domain.clientComplaintFeedback.handler.*;
import cec.jiutian.bc.clientComplaintManagement.domain.clientComplaintFeedback.mto.ClientComplaintFeedbackConfirmMTO;
import cec.jiutian.bc.clientComplaintManagement.domain.clientComplaintFeedback.mto.ClientComplaintFeedbackCpMTO;
import cec.jiutian.bc.clientComplaintManagement.domain.clientComplaintMechanism.model.ClientComplaintMechanism;
import cec.jiutian.bc.clientComplaintManagement.enumeration.CustomerComplaintLevelEnum;
import cec.jiutian.bc.clientComplaintManagement.domain.clientComplaintFeedback.proxy.ClientComplaintFeedbackDataProxy;
import cec.jiutian.bc.mto.SpecificationManageMTO;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.frame.module.MetaUser;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.RowBaseOperation;
import cec.jiutian.view.type.RowOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/05/20
 * @description TODO
 */
@FabosJson(
        name = "客诉反馈",
        orderBy = "ClientComplaintFeedback.createTime desc",
        dataProxy = ClientComplaintFeedbackDataProxy.class,
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "businessStatus != 'TO_BE_SUBMITTED'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "businessStatus != 'TO_BE_SUBMITTED'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
        },
        rowOperation = {
                @RowOperation(
                        title = "提交",
                        code = "ClientComplaintFeedback@SUBMIT",
                        operationHandler = ClientComplaintFeedbackSubmitHandler.class,
                        mode = RowOperation.Mode.HEADER,
                        callHint = "该操作不可撤回，确定提交吗？",
                        ifExpr = "selectedItems[0].businessStatus != 'TO_BE_SUBMITTED'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "ClientComplaintFeedback@SUBMIT"
                        )
                ),
                @RowOperation(
                        title = "确认",
                        code = "ClientComplaintFeedback@CONFIRM",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = ClientComplaintFeedbackConfirmMTO.class,
                        operationHandler = ClientComplaintFeedbackConfirmHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "ClientComplaintFeedback@CONFIRM"
                        ),
                        ifExpr = "selectedItems[0].businessStatus != 'TO_BE_CONFIRMED'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "预防纠正",
                        code = "ClientComplaintFeedback@PRECOR",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = ClientComplaintFeedbackCpMTO.class,
                        operationHandler = ClientComplaintFeedbackCpHandler.class,
                        ifExpr = "selectedItems[0].businessStatus != 'TO_BE_CORRECTED' || selectedItems[0].corPreStatus != 'WAIT_RUN'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "ClientComplaintFeedback@PRECOR"
                        )
                ),
                @RowOperation(
                        title = "关闭",
                        code = "ClientComplaintFeedback@CLOSE",
                        operationHandler = ClientComplaintFeedbackCloseHandler.class,
                        mode = RowOperation.Mode.HEADER,
                        callHint = "该操作不可撤回，确定提交吗？",
                        ifExpr = "selectedItems[0].businessStatus == 'CLOSED' || electedItems[0].businessStatus == 'COMPLETED'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "ClientComplaintFeedback@CLOSE"
                        )
                ),
        }
)
@Table(name = "qms_ccm_client_complaint_feedback",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Data
public class ClientComplaintFeedback extends MetaModel {
    @FabosJsonField(
            views = @View(title = "客诉反馈任务单号"),
            edit = @Edit(title = "客诉反馈任务单号",
                    readonly = @Readonly(add = false),
                    search = @Search(vague = true),
                    notNull = true),
            dynamicField = @DynamicField(
                    dependFiled = @DependFiled(buttonName = "生成", changeBy = "id",
                            dynamicHandler = ClientComplaintFeedbackDynamicHandler.class))
    )
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "公司"),
            edit = @Edit(title = "公司",
                    search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    private String company;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "客诉机制", column = "generalCode"),
            edit = @Edit(title = "客诉机制", notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode"))
    )
    private ClientComplaintMechanism clientComplaintMechanism;

    @FabosJsonField(
            views = @View(title = "客诉等级"),
            edit = @Edit(title = "客诉等级",
                    type = EditType.CHOICE,
                    notNull = true,
                    search = @Search(),
                    readonly = @Readonly(),
                    choiceType = @ChoiceType(fetchHandler = CustomerComplaintLevelEnum.class)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "clientComplaintMechanism", beFilledBy = "clientComplaintLevel"))
    )
    private String clientComplaintLevel;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "投诉人", column = "name"),
            edit = @Edit(title = "投诉人", notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"))
    )
    private MetaUser complainant;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "产品型号", column = "name"),
            edit = @Edit(title = "产品型号",
                    type = EditType.REFERENCE_TABLE,
                    filter = @Filter(value = "validateFlag = 'Y' and specificationCode like '02%'"),
                    referenceTableType = @ReferenceTableType(id = "id", label = "name")
            )
    )
    private SpecificationManageMTO productType;

    @FabosJsonField(
            views = @View(title = "发货批次号"),
            edit = @Edit(title = "发货批次号",
                    search = @Search(vague = true))
    )
    private String shipmentBatchNumber;

    @FabosJsonField(
            views = @View(title = "重量"),
            edit = @Edit(title = "重量"))
    private Double weight;

    @FabosJsonField(
            views = @View(title = "单位"),
            edit = @Edit(title = "单位",
                    inputType = @InputType(length = 40))
    )
    private String unit;

    @FabosJsonField(
            views = @View(title = "投诉原因"),
            edit = @Edit(title = "投诉原因",
                    type = EditType.TEXTAREA)
    )
    private String complaintReason;

    @FabosJsonField(
            views = @View(title = "CQE核实内容"),
            edit = @Edit(title = "CQE核实内容",
                    type = EditType.TEXTAREA)
    )
    private String cqeVeriContent;

    @FabosJsonField(
            views = @View(title = "要求完成时间"),
            edit = @Edit(title = "要求完成时间",
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date requiredCompletionTime;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",
                    show = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ClientComplaintFeedbackStatusEnum.class)
            )
    )
    private String businessStatus;

    @FabosJsonField(
            views = @View(title = "预防纠正创建状态", show = false),
            edit = @Edit(title = "预防纠正创建状态",
                    show = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = CfbCorrectPreventProgressEnum.class)
            )
    )
    private String corPreStatus;

    @PrePersist
    public void onCreate() {
        if (this.businessStatus == null) {
            this.setBusinessStatus(ClientComplaintFeedbackStatusEnum.Enum.TO_BE_SUBMITTED.name());
        }

        if (this.corPreStatus == null) {
            this.setCorPreStatus(CfbCorrectPreventProgressEnum.Enum.WAIT_RUN.name());
        }
    }
}
