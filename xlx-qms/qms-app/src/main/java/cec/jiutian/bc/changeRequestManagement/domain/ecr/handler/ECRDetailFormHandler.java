package cec.jiutian.bc.changeRequestManagement.domain.ecr.handler;

import cec.jiutian.bc.changeRequestManagement.domain.bo.FlowNodeBO;
import cec.jiutian.bc.changeRequestManagement.domain.ecn.model.ECNItem;
import cec.jiutian.bc.changeRequestManagement.domain.ecn.model.VerificationIDetail;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.model.ApproveTask;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.model.ChangeRequest;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.model.ConfirmRequest;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.mto.RoleMTO;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.mto.formDetail.ApproveTaskMTO;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.mto.formDetail.ECNItemMTO;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.mto.formDetail.ECRDetailFormMTO;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.repository.ApproveTaskRepository;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Component
public class ECRDetailFormHandler implements OperationHandler<ChangeRequest, ECRDetailFormMTO> {

    @Resource
    private ApproveTaskRepository approveTaskRepository;

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Transactional
    @Override
    public ECRDetailFormMTO fabosJsonFormValue(List<ChangeRequest> data, ECRDetailFormMTO fabosJsonForm, String[] param) {
        ECRDetailFormMTO ecrDetailFormMTO = new ECRDetailFormMTO();
        ChangeRequest changeRequest = data.get(0);
        ecrDetailFormMTO.init(changeRequest);

        List<ApproveTask> reviewTasks = approveTaskRepository.findByBusinessKeyAndCode(changeRequest.getId(), FlowNodeBO.Node.REVIEW.name());
        if (CollectionUtils.isNotEmpty(reviewTasks)) {
            ArrayList<ApproveTaskMTO> approveTaskMTOS = new ArrayList<>(reviewTasks.size());
            for (ApproveTask approveTask : reviewTasks) {
                ApproveTaskMTO approveTaskMTO = ApproveTaskMTO.createByApproveTask(approveTask);
                RoleMTO roleMTO = fabosJsonDao.findById(RoleMTO.class, approveTask.getRole());
                if (roleMTO != null) {
                    approveTaskMTO.setNodeName(roleMTO.getName());
                }
                approveTaskMTOS.add(approveTaskMTO);
            }
            ecrDetailFormMTO.setApproveTaskMTO(approveTaskMTOS);
        }
        changeApprove(changeRequest, ecrDetailFormMTO);
        executeAndVerify(changeRequest, ecrDetailFormMTO);
        supplement(changeRequest, ecrDetailFormMTO);
        traceAndClose(changeRequest, ecrDetailFormMTO);

        return ecrDetailFormMTO;
    }

    private void changeApprove(ChangeRequest changeRequest, ECRDetailFormMTO ecrDetailFormMTO) {
        ConfirmRequest confirmRequest = changeRequest.getConfirmRequest();
        if (confirmRequest != null) {
            ecrDetailFormMTO.setInnerAdvice(confirmRequest.getInnerAdvice());
            ecrDetailFormMTO.setCustomerAdvice(confirmRequest.getCustomerAdvice());
            ecrDetailFormMTO.setAdviceAttachment(confirmRequest.getAdviceAttachment());
            ApproveTask approve1Task = approveTaskRepository.findFirstByBusinessKeyAndCodeAndRoleOrderByCreateDateDesc(changeRequest.getId(),
                    FlowNodeBO.Node.APPROVE_1.name(),
                    confirmRequest.getApproveBy().getId());
            if (approve1Task != null) {
                ecrDetailFormMTO.setApprover(approve1Task.getOperator());
            }
            ApproveTask approve2Task = approveTaskRepository.findFirstByBusinessKeyAndCodeAndRoleOrderByCreateDateDesc(changeRequest.getId(),
                    FlowNodeBO.Node.APPROVE_2.name(),
                    confirmRequest.getApproveBy().getId());
            if (approve2Task != null) {
                ecrDetailFormMTO.setConformer(approve2Task.getOperator());
            }
        }
    }

    private void executeAndVerify(ChangeRequest changeRequest, ECRDetailFormMTO ecrDetailFormMTO) {
        ecrDetailFormMTO.setChangeSwitchDate(changeRequest.getChangeSwitchDate());
        ecrDetailFormMTO.setPlannedExecutionDate(changeRequest.getPlannedExecutionDate());
        VerificationIDetail verificationIDetail = changeRequest.getVerificationIDetail();
        if (verificationIDetail != null) {
            ecrDetailFormMTO.setVerificationContent(verificationIDetail.getVerificationContent());
            ecrDetailFormMTO.setFiles(verificationIDetail.getFiles());
            List<ApproveTask> tasks1 = approveTaskRepository.findByBusinessKeyAndCode(changeRequest.getId(), FlowNodeBO.Node.VERIFY_REVIEW.name());
            List<ApproveTask> tasks2 = approveTaskRepository.findByBusinessKeyAndCode(changeRequest.getId(), FlowNodeBO.Node.VERIFY_REVIEW_A.name());
            List<ApproveTask> tasks3 = approveTaskRepository.findByBusinessKeyAndCode(changeRequest.getId(), FlowNodeBO.Node.VERIFY_APPROVE.name());
            ArrayList<ApproveTask> approveTasks = new ArrayList<>();
            approveTasks.addAll(tasks1);
            approveTasks.addAll(tasks2);
            approveTasks.addAll(tasks3);
            if (CollectionUtils.isNotEmpty(approveTasks)) {
                ArrayList<ApproveTaskMTO> approveTaskMTOS = new ArrayList<>(approveTasks.size());
                for (ApproveTask approveTask : approveTasks) {
                    ApproveTaskMTO approveTaskMTO = ApproveTaskMTO.createByApproveTask(approveTask);
                    RoleMTO roleMTO = fabosJsonDao.findById(RoleMTO.class, approveTask.getRole());
                    if (roleMTO != null) {
                        approveTaskMTO.setNodeName(roleMTO.getName());
                    }
                    approveTaskMTOS.add(approveTaskMTO);
                }
                ecrDetailFormMTO.setVerifyTaskMTO(approveTaskMTOS);
            }
        }
    }

    private void traceAndClose(ChangeRequest changeRequest, ECRDetailFormMTO ecrDetailFormMTO) {
        ApproveTask approveTask = approveTaskRepository.findFirstByBusinessKeyAndCode(changeRequest.getId(), FlowNodeBO.Node.WAIT_CLOSING.name());
        if (approveTask != null) {
            ecrDetailFormMTO.setCloseAdvice(approveTask.getResult());
            ecrDetailFormMTO.setExplain(approveTask.getExplain());
            ecrDetailFormMTO.setCloseFiles(approveTask.getAttachment());
        }
    }

    private static final String queryEcn = "FROM ECNItem e WHERE e.ecn.id = :requestId";

    private void supplement(ChangeRequest changeRequest, ECRDetailFormMTO ecrDetailFormMTO) {
        List<ECNItem> ecnItems = fabosJsonDao.getEntityManager().createQuery(queryEcn, ECNItem.class)
                .setParameter("requestId", changeRequest.getId())
                .getResultList();
        if (CollectionUtils.isNotEmpty(ecnItems)) {
            ArrayList<ECNItemMTO> ecnItemMTOS = new ArrayList<>(ecnItems.size());
            for (ECNItem ecnItem : ecnItems) {
                ECNItemMTO ecnItemMTO = ECNItemMTO.create(ecnItem);
                ecnItemMTOS.add(ecnItemMTO);
            }
            ecrDetailFormMTO.setEcnItemMTOS(ecnItemMTOS);
        }
    }
}
