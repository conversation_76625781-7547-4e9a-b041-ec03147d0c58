package cec.jiutian.bc.inventoryInspection.domain.inspectionTask.handler;

import cec.jiutian.bc.inventoryInspection.domain.inspectionTask.model.InventoryInspectionTask;
import cec.jiutian.bc.inventoryInspection.domain.inspectionTask.model.InventoryTerminationInspectionTask;
import cec.jiutian.bc.materialInspect.service.SpcProcessDataService;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/15
 * @description TODO
 */
@Component
public class ExamineInvInsTaskHandler implements OperationHandler<InventoryInspectionTask, InventoryTerminationInspectionTask> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private SpcProcessDataService spcProcessDataService;

    @Override
    public String exec(List<InventoryInspectionTask> data, InventoryTerminationInspectionTask modelObject, String[] param) {
        if (modelObject != null) {
            InventoryInspectionTask inspectionTask = data.get(0);
            inspectionTask.setInspectionResult(modelObject.getInspectionResult());
            inspectionTask.setUnqualifiedLevel(modelObject.getUnqualifiedLevel());
            inspectionTask.setCurrentState(OrderCurrentStateEnum.Enum.COMPLETE.name());
            inspectionTask.setDetectCompleteTime(new Date());
            fabosJsonDao.mergeAndFlush(inspectionTask);
            spcProcessDataService.createSpcProcessData(inspectionTask.getId());
        }
        return "alert(操作成功)";
    }
}
