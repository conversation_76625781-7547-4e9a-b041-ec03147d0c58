package cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.enumration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/28
 * @description TODO
 */
public class RelatedDocumentTypeEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum data : Enum.values()) {
            list.add(new VLModel(data.name(), data.getValue()));
        }
        return list;
    }

    /*如后续有新增的，请按照这个来排序:
# 供应商问题整改清单
# 产品审核实施计划
产品审核问题清单 - 不需要
过程管理审核实施计划 - 不需要
# 过程管理审核问题清单
# 顾客退货产品处置单
# 异常反馈处理
# MRB不合格评审
# 客诉反馈
# LIMS点巡检管理问题清单
     */
    @AllArgsConstructor
    @Getter
    public enum Enum {
        ProblemRectification("供应商问题整改清单"),
        ExamineImplementPlan("产品审核实施计划"),
        ProcessAuditListOfIssue("过程管理审核问题清单"),
        ReturnProductHandling("顾客退货产品处理"),
        ABNORMAL_FEEDBACK_HANDLING("异常反馈处理"),
        MRBUnqualifiedReview("MRB不合格评审"),
        CLIENT_COMPLAINT_FEEDBACK("客诉反馈"),
        EhsQuestion("LIMS点巡检管理问题清单"),
        OTHER("其他"),
        ;

        private final String value;

    }
}
