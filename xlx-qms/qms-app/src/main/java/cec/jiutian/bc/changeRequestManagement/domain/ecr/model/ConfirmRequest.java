package cec.jiutian.bc.changeRequestManagement.domain.ecr.model;

import cec.jiutian.bc.changeRequestManagement.domain.ecr.mto.RoleMTO;
import cec.jiutian.bc.changeRequestManagement.enums.CustomerAdviceEnum;
import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Power;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@Entity
@Table(name = "qms_change_request_confirm")
@FabosJson(
        name = "变更事项",
        orderBy = "createTime asc",
        power = @Power(add = false, edit = false, delete = false, export = false)

)
public class ConfirmRequest extends BaseModel {

    @FabosJsonField(
            views = @View(title = "内部意见"),
            edit = @Edit(title = "内部意见",
                    notNull = true,
                    type = EditType.BOOLEAN,
                    boolType = @BoolType(trueText = "同意", falseText = "不同意")
            )
    )
    private Boolean innerAdvice;

    @FabosJsonField(
            views = @View(title = "客户意见"),
            edit = @Edit(title = "客户意见",
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "involveCustomer == true"),
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = CustomerAdviceEnum.class)
            )
    )
    private String customerAdvice;

    @FabosJsonField(
            views = @View(title = "附件"),
            edit = @Edit(title = "附件",
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "involveCustomer == true"),
                    inputType = @InputType(length = 300),
                    type = EditType.ATTACHMENT,
                    tips = "最大支持100M文件",
                    attachmentType = @AttachmentType(
                            size = 100 * 1024,
                            maxLimit = 1,
                            type = AttachmentType.Type.BASE)
            )
    )
    @Column(length = 300)
    private String adviceAttachment;

    @ManyToOne
    @JoinColumn(name = "approve_by", nullable = false, foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @FabosJsonField(
            views = {
                    @View(title = "审批角色", column = "name")
            },
            edit = @Edit(title = "审批角色",
                    notNull = true,
                    allowAddMultipleRows = false,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name")
            )
    )
    private RoleMTO approveBy;

    @ManyToOne
    @JoinColumn(name = "confirm_by", nullable = false, foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @FabosJsonField(
            views = {
                    @View(title = "批准角色", column = "name")
            },
            edit = @Edit(title = "批准角色",
                    notNull = true,
                    allowAddMultipleRows = false,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name")
            )
    )
    private RoleMTO confirmBy;

    @PrePersist
    public void prePersist() {
        if (this.getCreateTime() == null) {
            this.setCreateTime(LocalDateTime.now());
        }
    }

}
