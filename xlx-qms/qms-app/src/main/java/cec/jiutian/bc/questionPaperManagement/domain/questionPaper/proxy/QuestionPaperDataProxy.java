package cec.jiutian.bc.questionPaperManagement.domain.questionPaper.proxy;

import cec.jiutian.bc.mto.OrgMTO;
import cec.jiutian.bc.processInspect.domain.publicInspectionTask.mto.ProcessUserForInsTaskMTO;
import cec.jiutian.bc.questionPaperManagement.domain.questionPaper.model.QuestionPaper;
import cec.jiutian.bc.questionPaperManagement.enums.QuestionPaperStateEnum;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class QuestionPaperDataProxy implements DataProxy<QuestionPaper> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public void beforeAdd(QuestionPaper entity) {
        entity.setCurrentState(QuestionPaperStateEnum.Enum.Edit.name());
        entity.setImproveFlag(false);
        entity.setInterimFlag(false);
    }

    @Override
    public void afterSingleFetch(Map<String, Object> map) {
        if (map.get("responsibleDepartmentId") != null) {
            OrgMTO orgMTO = fabosJsonDao.findById(OrgMTO.class, map.get("responsibleDepartmentId"));
            if (orgMTO != null) {
                map.put("responsibleDepartmentMTO", orgMTO);
                map.put("responsibleDepartmentMTO_name", orgMTO.getName());
            } else {
                throw new FabosJsonApiErrorTip("未查到部门：" + map.get("responsibleDepartmentName") + "源数据，请确认");
            }
        }
        if (map.get("responsiblePersonId") != null) {
            ProcessUserForInsTaskMTO userMTO = fabosJsonDao.findById(ProcessUserForInsTaskMTO.class, map.get("responsiblePersonId"));
            if (userMTO != null) {
                map.put("responsiblePersonMTO", userMTO);
                map.put("responsiblePersonMTO_name", userMTO.getName());
            } else {
                throw new FabosJsonApiErrorTip("未查到部门：" + map.get("responsiblePersonName") + "源数据，请确认");
            }
        }

//        List<HashMap<String, Object>> interimMeasures = (List<HashMap<String, Object>>) map.get("interimMeasures");
//        List<HashMap<String, Object>> improveMeasures = (List<HashMap<String, Object>>) map.get("improveMeasures");
//        List<HashMap<String, Object>> interimList = new ArrayList<>();
//        List<HashMap<String, Object>> improveList = new ArrayList<>();
//        for (HashMap<String, Object> data : interimMeasures) {
//            String type = data.get("type").toString();
//            if (type.equals("Interim")) {
//                interimList.add(data);
//            }
//        }
//        for (HashMap<String, Object> data : improveMeasures) {
//            String type = data.get("type").toString();
//            if (type.equals("Improve")) {
//                improveList.add(data);
//            }
//        }
//        map.put("interimMeasures", interimList);
//        map.put("improveMeasures", improveList);

    }

}
