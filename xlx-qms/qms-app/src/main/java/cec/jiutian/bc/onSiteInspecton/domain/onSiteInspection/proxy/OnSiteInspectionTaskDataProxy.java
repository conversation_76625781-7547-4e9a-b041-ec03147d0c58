package cec.jiutian.bc.onSiteInspecton.domain.onSiteInspection.proxy;

import cec.jiutian.bc.onSiteInspecton.domain.onSiteInspection.model.OnSiteInspectionTask;
import cec.jiutian.bc.onSiteInspecton.enumeration.InspectionTaskStatusEnum;
import cec.jiutian.bc.urm.domain.user.entity.User;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.config.QueryExpression;
import cec.jiutian.view.fun.DataProxy;
import cec.jiutian.view.query.Condition;
import com.alipay.sofa.common.utils.StringUtil;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@RefreshScope
public class OnSiteInspectionTaskDataProxy implements DataProxy<OnSiteInspectionTask> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    private static final String IQC_CODE = "IQC";

    @Value("${qms.debug}")
    private boolean debug;

    @Override
    public void beforeAdd(OnSiteInspectionTask task) {
        task.setState(InspectionTaskStatusEnum.Enum.CREATED.name());
    }

    @Override
    public void beforeUpdate(OnSiteInspectionTask task) {
        task.setState(InspectionTaskStatusEnum.Enum.EDIT.name());
    }

    @Override
    public String beforeFetch(List<Condition> conditions) {
        String userId = UserContext.getUserId();
        if (StringUtil.isBlank(userId)) {
            throw new ServiceException("用户未登录");
        }
        User userForInsTaskMTO = fabosJsonDao.findById(User.class, userId);
        if (userForInsTaskMTO == null) {
            throw new ServiceException("用户不存在");
        }

        if (!debug && (userForInsTaskMTO.getOrg() == null || !IQC_CODE.equals(userForInsTaskMTO.getOrg().getCode()))) {
            throw new ServiceException("用户不是IQC人员");
        }

        String startTime = null;
        String endTime = null;
        for (Condition condition : conditions) {
            if (condition.getKey().equals("startTime") && condition.getValue() != null) {
                startTime = condition.getValue().toString();
            }
            if (condition.getKey().equals("endTime") && condition.getValue() != null) {
                endTime = condition.getValue().toString();
            }
        }

        if (null != startTime && null != endTime) {
            String timeRange = startTime + "," + endTime;
            Condition condition = new Condition("creationDate", timeRange, QueryExpression.RANGE);
            conditions.add(condition);
        }

        return null;
    }
}
