package cec.jiutian.bc.clientComplaintManagement.domain.problemImprovement.mto;

import cec.jiutian.bc.clientComplaintManagement.enumeration.ProblemImprovementStatusEnum;
import cec.jiutian.bc.materialInspect.domain.publicInspectionTask.mto.UserForInsTaskMTO;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.*;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;


/**
 * <AUTHOR>
 * @date 2025/05/20
 * @description TODO
 */
@FabosJson(
        name = "分发"

)
@Table(name = "qms_ccm_problem_improvement")
@Entity
@Getter
@Setter
public class ProblemImprovementDistributeMTO extends MetaModel {
    @FabosJsonField(
            views = @View(title = "责任人", column = "name"),
            edit = @Edit(title = "责任人",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "id")
            )
    )
    @ManyToOne
    @JoinColumn(name = "response_user_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private UserForInsTaskMTO userForInsTaskMTO;


    @FabosJsonField(
            views = @View(title = "责任人id", show = false),
            edit = @Edit(title = "责任人id",
                    show = false,
                    notNull = true,
                    inputType = @InputType(length = 30)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "userForInsTaskMTO",
                    beFilledBy = "id"))
    )
    private String responsiblePersonId;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",
                    show = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ProblemImprovementStatusEnum.class)
            )
    )
    private String businessStatus;
}
