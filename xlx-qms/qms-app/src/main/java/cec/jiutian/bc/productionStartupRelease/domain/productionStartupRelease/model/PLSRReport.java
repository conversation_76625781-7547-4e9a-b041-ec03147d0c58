package cec.jiutian.bc.productionStartupRelease.domain.productionStartupRelease.model;

import cec.jiutian.bc.materialInspect.domain.publicInspectionTask.mto.UserForInsTaskMTO;
import cec.jiutian.bc.mto.FactoryArea;
import cec.jiutian.bc.mto.OrgMTO;
import cec.jiutian.bc.mto.ProductProcessMTO;
import cec.jiutian.bc.productionStartupRelease.domain.productionStartupRelease.handler.*;
import cec.jiutian.bc.productionStartupRelease.domain.productionStartupRelease.mto.PLSRApproveDetail;
import cec.jiutian.bc.productionStartupRelease.domain.productionStartupRelease.mto.PLSRReportAddMTO;
import cec.jiutian.bc.productionStartupRelease.domain.productionStartupRelease.mto.QualityTraceForm;
import cec.jiutian.bc.productionStartupRelease.domain.productionStartupRelease.proxy.PLSRDataProxy;
import cec.jiutian.bc.productionStartupRelease.enums.*;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.ReferenceAddType;
import cec.jiutian.view.config.Comment;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowBaseOperation;
import cec.jiutian.view.type.RowOperation;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@Entity
@Table(name = "qms_plsr", indexes = {
        @Index(name = "general_code", columnList = "general_code", unique = true)
})
@Setter
@Getter
@FabosJson(
        name = "生产放行",
        dataProxy = PLSRDataProxy.class,
        orderBy = "createTime desc",
        power = @Power(add = false, export = false, importable = false, edit = false),
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "status != 'EDIT'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.HIDE
                )
        },
        rowOperation = {
                @RowOperation(
                        title = "创建",
                        code = "PLSRReport@CREATE",
                        fabosJsonClass = PLSRReportAddMTO.class,
                        mode = RowOperation.Mode.BUTTON,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.ADD,
                        popupType = RowOperation.PopupType.FORM,
                        ifExpr = "1 != 1",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "PLSRReport@CREATE"
                        )
                ),
                @RowOperation(
                        title = "查看审批详情",
                        code = "PLSRReport@ApproveDetail",
                        ifExpr = "selectedItems[0].status == 'EDIT'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = PLSRApproveDetail.class,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        operationHandler = PLSRApproveDetailHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "PLSRReport@ApproveDetail"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
                @RowOperation(
                        title = "编辑",
                        code = "PLSRReport@UPDATE",
                        fabosJsonClass = PLSRReportAddMTO.class,
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.UPDATE,
                        operationHandler = PLSRReportEditOperationHandler.class,
                        popupType = RowOperation.PopupType.FORM,
                        ifExpr = "selectedItems[0].status != 'EDIT' && selectedItems[0].status != 'NOT_PASS'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "PLSRReport@UPDATE"
                        )
                ),
                @RowOperation(
                        title = "结果评审",
                        code = "PLSRReport@REVIEW",
                        ifExpr = "selectedItems[0].status != 'EDIT' && selectedItems[0].status != 'NOT_PASS'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = ReviewResult.class,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        operationHandler = ReviewOperationHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "PLSRReport@REVIEW"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
                @RowOperation(
                        title = "发布",
                        code = "PLSRReport@PUBLISH",
                        operationHandler = PubulishOperationHandler.class,
                        mode = RowOperation.Mode.HEADER,
                        callHint = "确定发布吗？",
                        ifExpr = "selectedItems[0].status != 'EDIT' && selectedItems[0].status != 'NOT_PASS'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "PLSRReport@PUBLISH"
                        )
                ),
                @RowOperation(
                        title = "提交审批",
                        code = "PLSRReport@APPROVE",
                        callHint = "确定提交审批？",
                        ifExpr = "selectedItems[0].status != 'WAIT_APPROVE'", //禁用按钮
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
                        mode = RowOperation.Mode.HEADER,
                        operationHandler = SubmitApproveOperationHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "PLSRReport@APPROVE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
        }

)
public class PLSRReport extends MetaModel {

    @Comment("业务单据编码编号")
    @Column(unique = true, nullable = false, length = 40)
    @FabosJsonField(
            views = @View(title = "生产放行单号"),
            edit = @Edit(title = "生产放行单号", readonly = @Readonly(),
                    notNull = true, search = @Search(vague = true), inputType = @InputType(length = 40), index = 0)
    )
    private String generalCode;

    @ManyToOne
    @JoinColumn(name = "quality_trace_form_id")
    @FabosJsonField(
            views = @View(title = "品质跟踪单", column = "generalCode"),
            edit = @Edit(title = "品质跟踪单",
                    filter = @Filter("businessStatus = 'APPROVED'"),
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode")
            )
    )
    private QualityTraceForm qualityTraceForm;

    @ManyToOne
    @JoinColumn(name = "workshop_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @FabosJsonField(
            views = @View(title = "车间", show = false, column = "factoryAreaName"),
            edit = @Edit(title = "车间",
                    notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "factoryAreaName"),
                    filter = @Filter(value = "factoryAreaTypeCode = '02'")
            )
    )
    private FactoryArea workshop;

    @FabosJsonField(
            views = @View(title = "", show = false),
            edit = @Edit(title = "",show = false)
    )
    @Column(length = 128)
    private Long factoryId;

    @FabosJsonField(
            views = @View(title = "车间"),
            edit = @Edit(title = "", show = false)
    )
    @Column(length = 64)
    private String workshopName;

    @ManyToOne
    @JoinColumn(name = "line_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @FabosJsonField(
            views = @View(title = "产线", show = false, column = "factoryAreaName"),
            edit = @Edit(title = "产线",
                    notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "factoryAreaName"),
                    filter = @Filter(value = "factoryAreaTypeCode = '03'"),
                    queryCondition = "{ \"pid\": \"${workshop.id}\"}",
                    dependFieldDisplay = @DependFieldDisplay(enableOrDisable = "workshopName == '' || workshopName == null")
            ),
            referenceAddType = @ReferenceAddType(referenceClass = "FactoryArea", queryCondition = "{ \"pid\": \"${workshop.id}\"}")
    )
    private FactoryArea line;

    @FabosJsonField(
            views = @View(title = "产线"),
            edit = @Edit(title = "", show = false)
    )
    @Column(length = 64)
    private String lineName;

    @FabosJsonField(
            views = @View(title = "产品类型"),
            edit = @Edit(title = "产品类型",
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = {ProductionTypeEnum.class}))
    )
    @Column(nullable = false, length = 32)
    private String productionType;

    @FabosJsonField(
            views = @View(title = "放行等级"),
            edit = @Edit(title = "放行等级",
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ReleaseLevelEnum.class)
            )
    )
    @Column(nullable = false, length = 32)
    private String releaseLevel;

    @ManyToOne
    @JoinColumn(name = "org_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @FabosJsonField(
            views = @View(title = "主导部门", column = "name", show = false),
            edit = @Edit(title = "主导部门",
                    notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false
            )
    )
    private OrgMTO orgMTO;

    @FabosJsonField(
            views = @View(title = "主导部门"),
            edit = @Edit(title = "主导部门",
                    search = @Search(vague = true),
                    show = false
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "orgMTO", beFilledBy = "name"))
    )
    @Column(length = 64)
    private String department;

    @ManyToOne
    @JoinColumn(name = "user_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @FabosJsonField(
            views = @View(title = "评审主导人", show = false, column = "name"),
            edit = @Edit(title = "评审主导人",
                    notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType()
            )
    )
    private UserForInsTaskMTO user;

    @FabosJsonField(
            views = @View(title = "评审主导人"),
            edit = @Edit(title = "评审主导人",
                    show = false,
                    readonly = @Readonly(add = true, edit = true),
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "user", beFilledBy = "name"))
    )
    @Column(length = 64)
    private String userName;

    @FabosJsonField(
            views = @View(title = "生产线类型"),
            edit = @Edit(title = "生产线类型",
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = {LineTypeEnum.class}),
                    search = @Search()
            )
    )
    @Column(nullable = false, length = 32)
    private String lineType;

    @FabosJsonField(
            views = @View(title = "产品阶段"),
            edit = @Edit(title = "产品阶段",
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = {ProductionPeriodEnum.class}),
                    search = @Search()
            )
    )
    @Column(nullable = false, length = 32)
    private String productionPeriod;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "plsr_id")
    @FabosJsonField(
            views = @View(title = "评审详情", column = "reviewResult", type = ViewType.TABLE_VIEW),
            edit = @Edit(
                    title = "评审详情",
                    type = EditType.TAB_TABLE_ADD,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType(label = "reviewResult")
            )
    )
    private List<ReviewResult> results;


    @ManyToMany
    @JoinTable(name = "qms_plsr_process",
            inverseForeignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT),
            joinColumns = @JoinColumn(name = "plsr_id"),
            inverseJoinColumns = @JoinColumn(name = "process_id"))
    @FabosJsonField(
            views = @View(title = "放行工序段", column = "id", type = ViewType.TABLE_VIEW),
            edit = @Edit(title = "放行工序段",
                    notNull = true,
                    tabTableReferType = @TabTableReferType(type = TabTableReferType.SelectShowTypeMTM.LIST),
                    type = EditType.TAB_TABLE_REFER
            )
    )
    private List<ProductProcessMTO> process;

    @FabosJsonField(
            views = @View(title = "生产线类型"),
            edit = @Edit(title = "生产线类型",
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = {SwitchTypeEnum.class}),
                    search = @Search()
            )
    )
    @Column(nullable = false, length = 32)
    private String switchType;

    @FabosJsonField(
            views = @View(title = "生产线类型"),
            edit = @Edit(title = "生产线类型",
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = {ProductJudgeEnum.class}),
                    search = @Search()
            )
    )
    @Column(nullable = false, length = 32)
    private String productJudge;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",
                    readonly = @Readonly(add = true, edit = true),
                    notNull = true,
                    defaultVal = "EDIT",
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = {StatusEnum.class}),
                    search = @Search()
            ),
            dynamicField = @DynamicField(passive = true)
    )
    @Column(nullable = false, length = 16)
    private String status;

    @PrePersist
    public void prePersist() {
        if (StringUtils.isEmpty(this.status)) {
            this.status = StatusEnum.Enum.EDIT.name();
        }
    }

}

