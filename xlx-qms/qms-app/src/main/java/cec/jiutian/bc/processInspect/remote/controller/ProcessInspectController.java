package cec.jiutian.bc.processInspect.remote.controller;

import cec.jiutian.api.remoteCallLog.dto.RemoteCallResult;
import cec.jiutian.bc.processInspect.service.ProcessInspectService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RequestMapping("/process/inspect")
@RestController
public class ProcessInspectController {
    @Resource
    private ProcessInspectService processInspectService;

    @GetMapping("/getProcessSampleTask")
    public RemoteCallResult getProcessSampleTask(@RequestBody Map<String, Object> params) {
        return RemoteCallResult.success(processInspectService.getSampleTask(params));
    }
}
