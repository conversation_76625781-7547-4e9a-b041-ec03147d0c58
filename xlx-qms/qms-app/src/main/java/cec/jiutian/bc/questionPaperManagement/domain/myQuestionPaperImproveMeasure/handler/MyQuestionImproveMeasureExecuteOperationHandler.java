package cec.jiutian.bc.questionPaperManagement.domain.myQuestionPaperImproveMeasure.handler;

import cec.jiutian.bc.questionPaperManagement.domain.myQuestionPaperImproveMeasure.model.MyQuestionPaperImproveMeasure;
import cec.jiutian.bc.questionPaperManagement.enums.QuestionPaperMeasureProgressEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class MyQuestionImproveMeasureExecuteOperationHandler implements OperationHandler<MyQuestionPaperImproveMeasure, Void> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<MyQuestionPaperImproveMeasure> data, Void modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            MyQuestionPaperImproveMeasure entity = data.get(0);
            entity.setProgress(QuestionPaperMeasureProgressEnum.Enum.Execute.name());
            fabosJsonDao.mergeAndFlush(entity);
        }
        return "msg.success('操作成功')";
    }
}
