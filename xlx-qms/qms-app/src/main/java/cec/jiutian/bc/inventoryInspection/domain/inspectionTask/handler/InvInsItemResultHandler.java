package cec.jiutian.bc.inventoryInspection.domain.inspectionTask.handler;

import cec.jiutian.bc.generalModeler.util.StringUtils;
import cec.jiutian.bc.inventoryInspection.domain.inspectionTask.mto.InventoryTaskResultEnterDetailMTO;
import cec.jiutian.bc.inventoryInspection.domain.inspectionTask.mto.MyInvTaskResultEnterMTO;
import cec.jiutian.bc.inventoryInspection.enumeration.InspectionResultEnum;
import cec.jiutian.view.DependFiled;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/4/9
 * @description TODO
 */
@Component
public class InvInsItemResultHandler implements DependFiled.DynamicHandler<MyInvTaskResultEnterMTO> {
    @Override
    public Map<String, Object> handle(MyInvTaskResultEnterMTO taskResultEnterMTO) {
        Map<String, Object> result = new HashMap<>();
        boolean flag = true;
        if (CollectionUtils.isNotEmpty(taskResultEnterMTO.getDetailList())) {
            for (InventoryTaskResultEnterDetailMTO detail : taskResultEnterMTO.getDetailList()) {
                if (StringUtils.isEmpty(detail.getInspectionItemResult())) {
                    return result;
                }
                if (InspectionResultEnum.Enum.UNQUALIFIED.name().equals(detail.getInspectionItemResult())) {
                    flag = false;
                }
            }
        }
        if (flag) {
            result.put("inspectionResult", InspectionResultEnum.Enum.QUALIFIED.name());
        }else {
            result.put("inspectionResult", InspectionResultEnum.Enum.UNQUALIFIED.name());
        }

        return result;
    }
}
