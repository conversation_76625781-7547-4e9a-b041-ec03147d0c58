package cec.jiutian.bc.changeRequestManagement.domain.ecr.handler;

import cec.jiutian.bc.changeRequestManagement.domain.bo.FlowNodeBO;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.model.ApproveTask;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.model.ChangeRequest;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.model.ConfirmRequest;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.model.ECRItem;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.repository.ApproveTaskRepository;
import cec.jiutian.bc.changeRequestManagement.enums.ECRStatusEnum;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import jakarta.persistence.LockModeType;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class ECRConfirmHandler implements OperationHandler<ChangeRequest, ConfirmRequest> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private ApproveTaskRepository approveTaskRepository;

    @Transactional
    @Override
    public String exec(List<ChangeRequest> data, ConfirmRequest modelObject, String[] param) {
        if (CollectionUtils.isEmpty(data) || data.get(0).getId() == null) {
            throw new ServiceException("数据异常");
        }
        if (modelObject.getConfirmBy() == null || modelObject.getApproveBy() == null) {
            throw new ServiceException("请选择批准角色");
        }
        ChangeRequest changeRequest = fabosJsonDao.getEntityManager().find(ChangeRequest.class, data.get(0).getId(), LockModeType.PESSIMISTIC_WRITE);
        if (ECRStatusEnum.Enum.APPROVING.name().equals(changeRequest.getStatus())) {
            throw new ServiceException("该请求已提交批准，请刷新页面");
        }
        changeRequest.setStatus(ECRStatusEnum.Enum.APPROVING.name());
        changeRequest.setConfirmRequest(modelObject);
        fabosJsonDao.mergeAndFlush(changeRequest);

        Integer maxTurn = approveTaskRepository.findMaxTurnByBusinessKeyAndCode(changeRequest.getId(), FlowNodeBO.Node.APPROVE_1.name());
        if (maxTurn == null) {
            maxTurn = 0;
        }else {
            maxTurn++;
        }
        //创建审批任务
        ApproveTask approveTask = ApproveTask.createApprove1ExamineTask(changeRequest.getId(), modelObject.getApproveBy(), maxTurn);
        fabosJsonDao.persistAndFlush(approveTask);

        // 将所有 变更事项 负责人id 保持
        ChangeRequest ecr = data.get(0);
        if (CollectionUtils.isNotEmpty(ecr.getECRItems())) {
            String userIds = ecr.getECRItems().stream().map(ECRItem::getResponsiblePersonId).distinct()
                    .collect(Collectors.joining(","));
            ecr.setAllUserIds(userIds);
            fabosJsonDao.mergeAndFlush(ecr);
        }
        return "提交成功";
    }

}
