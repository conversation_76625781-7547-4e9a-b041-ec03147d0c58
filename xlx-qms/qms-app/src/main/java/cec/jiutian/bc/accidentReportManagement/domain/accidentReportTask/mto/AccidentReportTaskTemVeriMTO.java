package cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.mto;

import cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.enumration.ArmBusinessStatusEnum;
import cec.jiutian.bc.accidentReportManagement.enumeration.ArmMeasureStatusEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.ChoiceType;
import jakarta.persistence.*;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/29
 * @description
 */
@FabosJson(
        name = "临时措施验证"
)
@Table(name = "qms_arm_accident_report_task")
@Entity
@Data
public class AccidentReportTaskTemVeriMTO extends MetaModel {
    @FabosJsonField(
            edit = @Edit(title = "临时措施", type = EditType.TAB_TABLE_ADD),
            views = @View(title = "临时措施", type = ViewType.TABLE_VIEW)
    )
    @JoinColumn(name = "qms_arm_accident_report_task_id")
    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
    @OrderBy
    private List<ArmTemporaryMeasureVerifyMTO> armTemporaryMeasureList;

    @FabosJsonField(
            views = @View(title = "临时措施状态", show = false),
            edit = @Edit(title = "临时措施状态",
                    readonly = @Readonly(),
                    type = EditType.CHOICE,
                    show = false,
                    choiceType = @ChoiceType(fetchHandler = ArmMeasureStatusEnum.class)
            )
    )
    private String temporaryMeasureStatus;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",
                    show = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ArmBusinessStatusEnum.class)
            )
    )
    private String businessStatus;
}
