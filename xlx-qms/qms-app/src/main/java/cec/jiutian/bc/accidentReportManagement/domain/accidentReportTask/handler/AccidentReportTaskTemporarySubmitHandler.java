package cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.handler;

import cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.model.ArmTemporaryMeasure;
import cec.jiutian.bc.accidentReportManagement.enumeration.ArmMeasureStatusEnum;
import cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.model.AccidentReportTask;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/29
 * @description
 */
@Component
public class AccidentReportTaskTemporarySubmitHandler implements OperationHandler<AccidentReportTask, Void> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Transactional
    @Override
    public String exec(List<AccidentReportTask> data, Void modelObject, String[] param) {
        AccidentReportTask model = data.get(0);
        List<ArmTemporaryMeasure> measureList = model.getArmTemporaryMeasureList();
        if (measureList == null || measureList.size() == 0) {
            model.setTemporaryMeasureStatus(ArmMeasureStatusEnum.Enum.TO_BE_VERIFIED.name());
        } else {
            model.setTemporaryMeasureStatus(ArmMeasureStatusEnum.Enum.SUBMITTED.name());
        }
        fabosJsonDao.update(model);
        return null;
    }
}
