package cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.issueList.proxy;

import cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.issueList.model.IssueListANA;
import cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.issueList.model.IssueListANACorrection;
import cec.jiutian.bc.qualityExceptionManagement.enumeration.inspectionIssuesImproveAndTrace.ProgressEnum;
import cec.jiutian.core.frame.module.MetaUser;
import cec.jiutian.view.fun.DataProxy;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.stream.Collectors;

@Component
public class IssueListANADataProxy implements DataProxy<IssueListANA> {

    @Override
    public void beforeUpdate(IssueListANA issueListANA) {
        if (CollectionUtils.isNotEmpty(issueListANA.getIssueListANACorrections())){
            issueListANA.setAllUserIds(issueListANA.getIssueListANACorrections().stream().map(IssueListANACorrection::getMetaUser).map(MetaUser::getId).distinct().collect(Collectors.joining(",")));
            issueListANA.getIssueListANACorrections().forEach(issueListANACorrective -> {
                issueListANACorrective.setProgress(ProgressEnum.Enum.WAIT_RUN.name());
            });
        }

    }
}
