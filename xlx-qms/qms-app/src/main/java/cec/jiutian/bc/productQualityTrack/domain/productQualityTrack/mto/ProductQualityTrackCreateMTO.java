package cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.mto;

import cec.jiutian.bc.mto.FactoryArea;
import cec.jiutian.bc.mto.SpecificationManageMTO;
import cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.enumration.*;
import cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.handler.PqtDocumentTypeChangeHandler;
import cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.handler.PqtOpenStopLineHandler;
import cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.handler.PqtProcessOperationListHandler;
import cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.handler.ProductQualityTrackDynamicHandler;
import cec.jiutian.bc.flow.domain.model.entity.ExamineModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.*;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Filter;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/15
 * @description
 */
@FabosJson(
        name = "创建产品品质跟踪",
        orderBy = "ProductQualityTrackCreateMTO.createTime desc"
)
@Table(name = "qms_pqt_product_quality_track",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Data
public class ProductQualityTrackCreateMTO extends ExamineModel {
    @FabosJsonField(
            views = @View(title = "编号"),
            edit = @Edit(title = "编号",
                    readonly = @Readonly(edit = false),
                    notNull = true,
                    dependFieldDisplay = @DependFieldDisplay(enableOrDisable = "businessStatus == 'OPENING'")
            ),
            dynamicField = @DynamicField(dependFiled = @DependFiled(buttonName = "生成", changeBy = "id",
                    dynamicHandler = ProductQualityTrackDynamicHandler.class))
    )
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "单据类型"),
            edit = @Edit(title = "单据类型",
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = PqtDocumentTypeEnum.class))
    )
    private String documentType;

    @FabosJsonField(
            views = @View(title = "开机/换线任务单",  column = "name"),
            edit = @Edit(title = "开机/换线任务单",
                    type = EditType.REFERENCE_TABLE,
                    queryCondition = "{\"type\":\"${documentType}\"}",
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType(type = TabTableReferType.SelectShowTypeMTM.LIST)
            ),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "documentType", dynamicHandler = PqtDocumentTypeChangeHandler.class))
    )
    @ManyToOne
    @Transient
    @JoinColumn(name = "ms_wc_mto_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private MachineStartupWireChangeMTO machineStartupWireChangeMTO;

    @FabosJsonField(
            views = @View(title = "开机/换线任务单编码",show = false),
            edit = @Edit(title = "开机/换线任务单编码",
                    show = false
            ),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "machineStartupWireChangeMTO",
                    dynamicHandler = PqtOpenStopLineHandler.class))
    )
    private String mwGid;

    @FabosJsonField(
            views = @View(title = "开机/换线任务单名称"),
            edit = @Edit(title = "开机/换线任务单名称",
                    show = false,
                    search = @Search(vague = true)
            )
    )
    private String mwName;

    @Transient
    @FabosJsonField(
            views = @View(title = "车间", show = false, column = "factoryAreaName"),
            edit = @Edit(title = "车间",
                    notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "factoryAreaName"),
                    filter = @Filter(value = "factoryAreaTypeCode = '02'")
            )
    )
    private FactoryArea factoryArea;

    @FabosJsonField(
            views = @View(title = "车间ID",show = false),
            edit = @Edit(title = "车间ID",
                    show = false
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "factoryArea", beFilledBy = "id"))
    )
    private String factoryAreaId;

    @FabosJsonField(
            views = @View(title = "车间名称"),
            edit = @Edit(title = "车间名称",
                    readonly = @Readonly(add = true,edit = true),
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "factoryArea", beFilledBy = "factoryAreaName"))
    )
    private String factoryAreaName;

    @Transient
    @FabosJsonField(
            views = @View(title = "产线", show = false, column = "factoryAreaName"),
            edit = @Edit(title = "产线",
                    notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "factoryAreaName"),
                    queryCondition = "{\"pid\": \"${factoryArea.id}\"}",

                    filter = @Filter(value = "factoryAreaTypeCode = '03'")
            )
    )
    private FactoryArea factoryLine;

    @FabosJsonField(
            views = @View(title = "产线Id",show = false),
            edit = @Edit(title = "产线Id",
                    show = false
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "factoryLine", beFilledBy = "id"))
    )
    private String factoryLineId;

    @FabosJsonField(
            views = @View(title = "产线名称"),
            edit = @Edit(title = "产线名称",
                    search = @Search(vague = true),
                    readonly = @Readonly(add = true,edit = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "factoryLine", beFilledBy = "factoryAreaName"))
    )
    private String factoryLineName;

    @Transient
    @FabosJsonField(
            views = @View(title = "产品名称", column = "name"),
            edit = @Edit(title = "产品名称",
                    notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    filter = @Filter(value = "validateFlag = 'Y' and specificationCode like '02%'")
            )
    )
    private SpecificationManageMTO material;

    @FabosJsonField(
            views = @View(title = "产品id", show = false),
            edit = @Edit(title = "产品id", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "material", beFilledBy = "id"))
    )
    private String materialId;

    @FabosJsonField(
            views = @View(title = "产品编码"),
            edit = @Edit(title = "产品编码", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "material", beFilledBy = "code")
            )
    )
    private String materialCode;

    @FabosJsonField(
            views = @View(title = "产品名称"),
            edit = @Edit(title = "产品名称", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "material", beFilledBy = "name")
            )
    )
    private String materialName;

    @FabosJsonField(
            views = @View(title = "产品型号"),
            edit = @Edit(title = "产品型号", readonly = @Readonly
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "material", beFilledBy = "type"))
    )
    private String materialType;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",
                    show = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ProductQualityTrackBusinessStatusEnum.class)
            )
    )
    private String businessStatus;

    @FabosJsonField(
            views = @View(title = "日期", show = false),
            edit = @Edit(title = "日期", show = false,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date documentCreateDate;

    @FabosJsonField(
            views = @View(title = "工序列表",
                    column = "processOperationName",
                    type = ViewType.TABLE_VIEW),
            edit = @Edit(title = "工序列表",
                    type = EditType.TAB_REFERENCE_GENERATE
            ),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = {"factoryLine", "factoryArea"}, dynamicHandler = PqtProcessOperationListHandler.class)),
            referenceGenerateType = @ReferenceGenerateType(editable = {"pqtMaterial", "inspectionInstrumentList"})
    )
    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumn(name = "qms_pqt_product_quality_track_id")
    private List<PqtProcessOperationCreateMTO> pqtProcessOperationList;
}
