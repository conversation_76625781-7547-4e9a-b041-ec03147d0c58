package cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.proxy;

import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.enumration.ImprovingProgressEnum;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.model.CorrectiveAction;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.model.MyCorrectiveTask;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import cec.jiutian.view.query.Condition;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;


import javax.annotation.Resource;
import java.util.*;
@Component
public class MyCorrectiveTaskProxy implements DataProxy<MyCorrectiveTask> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String beforeFetch(List<Condition> conditions) {
        String userId = UserContext.getUserId();
        return " MyCorrectiveTask.correctAllUserIds like '%"+userId+"%'";
    }

    @Override
    public void afterFetch(Collection<Map<String, Object>> list) {
        if(CollectionUtils.isNotEmpty(list)){
            list.forEach(d->{
                String id = d.get("id").toString();
                MyCorrectiveTask myCorrectiveTask = fabosJsonDao.findById(MyCorrectiveTask.class, id);
                for (CorrectiveAction corrective : myCorrectiveTask.getCorrectiveActionList()) {
                    if (ImprovingProgressEnum.Enum.TO_BE_VERIFIED.name().equals(corrective.getImprovingProgress())
                            && UserContext.getUserId().equals(corrective.getUserForInsTaskMTO().getId())) {
                        d.put("rowOperationAuthFlag",0);
                        break;
                    }
                }
            });
        }
    }

    @Override
    public void afterSingleFetch(Map<String, Object> map) {
        List<HashMap<String,Object>> list = (List<HashMap<String,Object>>) map.get("correctiveActionList");
        List<HashMap<String,Object>> result = new ArrayList<>();
        for(HashMap<String,Object> data: list){
            Map<String,Object> userMap = (Map<String, Object>) data.get("userForInsTaskMTO");
            String id = userMap.get("id").toString();
            if(id.equals(UserContext.getUserId())){
                result.add(data);
            }
        }
        map.put("correctiveActionList",result);
    }
}
