package cec.jiutian.bc.productReturnInspection.domain.samplingTask.model;

import cec.jiutian.bc.materialInspect.enumeration.InspectionResultEnum;
import cec.jiutian.bc.modeler.enumration.SampleTypeEnum;
import cec.jiutian.bc.modeler.enumration.UnqualifiedHandleWayEnum;
import cec.jiutian.bc.processInspect.domain.publicInspectionTask.mto.ProcessUserForInsTaskMTO;
import cec.jiutian.bc.productReturnInspection.domain.samplingTask.handler.ProductReturnSamplingTaskReceiveOprDynamicHandler;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.*;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/19
 * @description TODO
 */
@FabosJson(
        name = "取样任务自定义按钮模型"
)
@Table(name = "mi_sampling_task",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Getter
@Setter
@TemplateType(type = "multiTable")
public class ProductReturnSamplingTaskReceiveOpr extends MetaModel {

    @FabosJsonField(
            views = @View(title = "条码号"),
            edit = @Edit(title = "条码号", notNull = true)
    )
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "检验任务单号"),
            edit = @Edit(title = "检验任务单号", readonly = @Readonly),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "generalCode", dynamicHandler = ProductReturnSamplingTaskReceiveOprDynamicHandler.class))
    )
    private String inspectionTaskCode;

    @FabosJsonField(
            views = @View(title = "取样任务单号"),
            edit = @Edit(title = "取样任务单号", readonly = @Readonly)
    )
    private String samplingTaskCode;

    @FabosJsonField(
            views = @View(title = "检验状态",show = false),
            edit = @Edit(title = "检验状态", show = false)
    )
    private String businessState;

    @FabosJsonField(
            views = @View(title = "物资编码"),
            edit = @Edit(title = "物资编码", readonly = @Readonly)
    )
    private String materialCode;

    @FabosJsonField(
            views = @View(title = "物资名称"),
            edit = @Edit(title = "物资名称", readonly = @Readonly)
    )
    private String materialName;

    @FabosJsonField(
            views = @View(title = "批次号"),
            edit = @Edit(title = "批次号", readonly = @Readonly)
    )
    private String originLotId;

    @FabosJsonField(
            views = @View(title = "规格"),
            edit = @Edit(title = "规格", readonly = @Readonly)
    )
    private String materialSpecification;

    @FabosJsonField(
            views = @View(title = "单位"),
            edit = @Edit(title = "单位", readonly = @Readonly)
    )
    private String unit;

    @FabosJsonField(
            views = @View(title = "检验类型"),
            edit = @Edit(title = "检验类型",
                    readonly = @Readonly,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = SampleTypeEnum.class)
            )
    )
    private String inspectionType;

    @FabosJsonField(
            views = @View(title = "取样数量"),
            edit = @Edit(title = "取样数量", readonly = @Readonly,numberType = @NumberType(precision = 2))
    )
    private Double allInspectionItemQuantity;

    @FabosJsonField(
            views = @View(title = "包装方式"),
            edit = @Edit(title = "包装方式", readonly = @Readonly)
    )
    private String packageType;

    @FabosJsonField(
            views = @View(title = "取样点"),
            edit = @Edit(title = "取样点",
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(passive = true)
    )
    private String samplePoint;

    @FabosJsonField(
            views = @View(title = "送样点"),
            edit = @Edit(title = "送样点",
                    search = @Search(vague = true)
            )
    )
    private String sendPoint;

    @FabosJsonField(
            views = @View(title = "外观检验"),
            edit = @Edit(title = "外观检验",notNull = true, type = EditType.CHOICE,search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = InspectionResultEnum.class))
    )
    private String appearanceInspect;

    @FabosJsonField(
            views = @View(title = "复核数量"),
            edit = @Edit(title = "复核数量",notNull = true,
                    numberType = @NumberType(min = 0,max = 100,precision = 2))
    )
    private Double reviewQuantity;

    @Transient
    @FabosJsonField(
            views = @View(title = "收样人",show = false, column = "name"),
            edit = @Edit(title = "收样人",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType()
            )
    )
    private ProcessUserForInsTaskMTO user;

    @FabosJsonField(
            views = @View(title = "收样人id",show = false),
            edit = @Edit(title = "收样人id", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "user", beFilledBy = "id"))
    )
    private String receiveSamplePersonId;

    @FabosJsonField(
            views = @View(title = "收样人"),
            edit = @Edit(title = "收样人",
                    notNull = true,
                    readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "user", beFilledBy = "name"))
    )
    private String receiveSamplePerson;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "收样日期", type = ViewType.DATE),
            edit = @Edit(title = "收样日期", notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE))
    )
    private Date receiveSampleDate;

    @FabosJsonField(
            views = @View(title = "不合格处理"),
            edit = @Edit(title = "不合格处理",notNull = true, type = EditType.CHOICE,search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = UnqualifiedHandleWayEnum.class),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "appearanceInspect == 'UNQUALIFIED'"))
    )
    private String unqualifiedHandle;

    @FabosJsonField(
            views = @View(title = "异常描述"),
            edit = @Edit(title = "异常描述",
                    notNull = true,
                    search = @Search(),
                    type = EditType.TEXTAREA,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "unqualifiedHandle == 'exception'"))
    )
    private String abnormalDescription;

    @FabosJsonField(
            views = @View(title = "发现时间"),
            edit = @Edit(title = "发现时间",
                    dateType = @DateType(type = DateType.Type.DATE_TIME),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "appearanceInspect == 'UNQUALIFIED'")
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date submissionTime;

    @Transient
    @FabosJsonField(
            views = @View(title = "发现人",show = false, column = "name"),
            edit = @Edit(title = "发现人",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType(),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "appearanceInspect == 'UNQUALIFIED'")
            )
    )
    private ProcessUserForInsTaskMTO discoveredPerson;

    @FabosJsonField(
            views = @View(title = "发现人ID", show = false),
            edit = @Edit(title = "发现人ID", show = false
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "discoveredPerson", beFilledBy = "id"))
    )
    private String discoveredPersonId;

    @FabosJsonField(
            views = @View(title = "发现人"),
            edit = @Edit(title = "发现人",
                    notNull = true,
                    readonly = @Readonly,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "appearanceInspect == 'UNQUALIFIED'")),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "discoveredPerson", beFilledBy = "name"))
    )
    private String discoveredPersonName;

    @FabosJsonField(
            views = @View(title = "异常附件"),
            edit = @Edit(title = "异常附件", type = EditType.ATTACHMENT,
                    notNull = true,
                    search = @Search(),
                    attachmentType = @AttachmentType,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "unqualifiedHandle == 'exception'"))
    )
    private String abnormalAttachments;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "sampling_task_id")
    @OrderBy
    @FabosJsonField(
            edit = @Edit(title = "取样任务明细", type = EditType.TAB_TABLE_ADD,readonly = @Readonly),
            views = @View(title = "取样任务明细", type = ViewType.TABLE_VIEW)
    )
    private List<ProductReturnSamplingTaskReceiveOprDetail> detailList;
}
