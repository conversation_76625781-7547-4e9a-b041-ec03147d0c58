package cec.jiutian.bc.qualityExceptionManagement.domain.returnProductHandling.model;

import cec.jiutian.bc.basicData.enumeration.NamingRuleCodeEnum;
import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleBaseModel;
import cec.jiutian.bc.generalModeler.domain.supplier.model.Supplier;
import cec.jiutian.bc.generalModeler.enumeration.YesOrNoEnum;
import cec.jiutian.bc.inventoryInspection.enumeration.InspectionResultEnum;
import cec.jiutian.bc.modeler.enumration.MRBDisposalOpinionEnum;
import cec.jiutian.bc.modeler.enumration.UnqualifiedLevelEnum;
import cec.jiutian.bc.qualityExceptionManagement.domain.returnProductHandling.handler.ReturnProdHandlingCompleteOperationHandler;
import cec.jiutian.bc.qualityExceptionManagement.domain.returnProductHandling.handler.ReturnProdHandlingReleaseOperationHandler;
import cec.jiutian.bc.qualityExceptionManagement.domain.returnProductHandling.handler.ReturnProductInspectionTaskHandler;
import cec.jiutian.bc.qualityExceptionManagement.domain.returnProductHandling.mto.ReturnProductInspectionRequestDetailMTO;
import cec.jiutian.bc.qualityExceptionManagement.domain.returnProductHandling.proxy.ReturnProductHandlingDataProxy;
import cec.jiutian.bc.qualityExceptionManagement.enums.ReturnProductAnalysisMethodEnum;
import cec.jiutian.bc.qualityExceptionManagement.enums.ReturnProductHandlingStatusEnum;
import cec.jiutian.bc.qualityExceptionManagement.enums.ReturnProductHandlingSuggestionEnum;
import cec.jiutian.bc.qualityExceptionManagement.enums.ReturnProductLiabilityJudgmentEnum;
import cec.jiutian.bc.qualityExceptionManagement.enums.ReturnProductReviewCommentEnum;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.AttachmentType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DateType;
import cec.jiutian.view.field.edit.DependFieldDisplay;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowBaseOperation;
import cec.jiutian.view.type.RowOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OrderBy;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.util.List;

@Entity
@Table(name = "qem_return_product_handling",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        })
@Getter
@Setter
@FabosJson(
        name = "退货产品处理单",
        dataProxy = ReturnProductHandlingDataProxy.class,
        orderBy = "createTime desc",
        power = @Power(export = false),
//        flowCode = "ReworkTask",
//        flowProxy = ReworkTaskFlowProxy.class,
        rowBaseOperation = {
//                @RowBaseOperation(
//                        code = "examine",
//                        ifExpr = "!(status =='COMPLETE' && examineStatus =='0')",
//                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
//                ),
//                @RowBaseOperation(
//                        code = "examineDetails",
//                        ifExpr = "examineStatus =='0'",
//                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
//                ),
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "status !='Edit'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "status !='Edit'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
        },
        rowOperation = {
                @RowOperation(
                        title = "发布",
                        code = "ReturnProductHandling@RELEASE",
                        operationHandler = ReturnProdHandlingReleaseOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "是否确定操作？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "ReturnProductHandling@RELEASE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "status != 'Edit'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE
                ),
                @RowOperation(
                        title = "原因分析",
                        code = "ReturnProductHandling@ANALYSE",
                        mode = RowOperation.Mode.SINGLE,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.UPDATE,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = ReturnProductHandlingAnalyse.class,
                        ifExpr = "status != 'WaitAnalyse'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "ReturnProductHandling@ANALYSE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
                @RowOperation(
                        title = "处置方案",
                        code = "ReturnProductHandling@HANDLE",
                        mode = RowOperation.Mode.SINGLE,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.UPDATE,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = ReturnProductHandlingHandle.class,
                        ifExpr = "status != 'WaitHandle'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "ReturnProductHandling@HANDLE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
                @RowOperation(
                        title = "评审",
                        code = "ReturnProductHandling@REVIEW",
                        mode = RowOperation.Mode.SINGLE,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.UPDATE,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = ReturnProductHandlingReview.class,
                        ifExpr = "status != 'WaitReview'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "ReturnProductHandling@REVIEW"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
                @RowOperation(
                        title = "关闭",
                        code = "ReturnProductHandling@COMPLETE",
                        operationHandler = ReturnProdHandlingCompleteOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "是否确定操作？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "ReturnProductHandling@COMPLETE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "status != 'Handling'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE
                ),
        }
)
@TemplateType(type = "multiTable")
public class ReturnProductHandling extends NamingRuleBaseModel {

    @Override
    public String getNamingCode() {
        return NamingRuleCodeEnum.CustomerReturnProductHandle.name();
    }

    // 查询退货产品检验通知单的产品批次详情
    @ManyToOne
    @FabosJsonField(
            views = @View(title = "退货产品批次", column = "lotSerialId", show = false),
            edit = @Edit(title = "退货产品批次",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "lotSerialId")
            )
    )
    private ReturnProductInspectionRequestDetailMTO returnProductDetail;

    @FabosJsonField(
            views = @View(title = "产品批号"),
            edit = @Edit(title = "产品批号", show = false, search = @Search(vague = true)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "returnProductDetail", beFilledBy = "lotSerialId"))
    )
    private String lotSerialId;

    @FabosJsonField(
            views = @View(title = "产品库存id", show = false),
            edit = @Edit(title = "产品库存id", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "returnProductDetail", beFilledBy = "inventoryId"))
    )
    private String inventoryId;

    @FabosJsonField(
            views = @View(title = "产品名称"),
            edit = @Edit(title = "产品名称", readonly = @Readonly,
                    search = @Search(vague = true)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "returnProductDetail", beFilledBy = "materialName"))
    )
    private String productName;

    @FabosJsonField(
            views = @View(title = "产品编号"),
            edit = @Edit(title = "产品编号", readonly = @Readonly,
                    search = @Search(vague = true)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "returnProductDetail", beFilledBy = "materialCode"))
    )
    private String productCode;

    @FabosJsonField(
            views = @View(title = "型号规格"),
            edit = @Edit(title = "型号规格", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "returnProductDetail", beFilledBy = "materialSpecification"))
    )
    private String specification;

    @FabosJsonField(
            views = @View(title = "退货单号"),
            edit = @Edit(title = "退货单号", readonly = @Readonly, search = @Search(vague = true)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "returnProductDetail", beFilledBy = "productRefundNumber"))
    )
    private String productRefundNumber;

    @FabosJsonField(
            views = @View(title = "客户名称"),
            edit = @Edit(title = "客户名称", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "returnProductDetail", beFilledBy = "customerName"))
    )
    private String customerName;

    @FabosJsonField(
            views = @View(title = "退货原因"),
            edit = @Edit(title = "退货原因", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "returnProductDetail", beFilledBy = "refundReason"))
    )
    private String refundReason;

    @FabosJsonField(
            views = @View(title = "退货数量"),
            edit = @Edit(title = "退货数量", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "returnProductDetail", beFilledBy = "lotQuantity"))
    )
    private Double refundQuantity;

    @FabosJsonField(
            views = @View(title = "单位"),
            edit = @Edit(title = "单位", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "returnProductDetail", beFilledBy = "measureUnit"))
    )
    private String measureUnitName;

//    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
//    @FabosJsonField(
//            views = @View(title = "生产日期", type = ViewType.DATE),
//            edit = @Edit(title = "生产日期", notNull = true, search = @Search(vague = true),
//                    dateType = @DateType(type = DateType.Type.DATE)
//            )
//    )
//    private Date productionDate;

    @FabosJsonField(
            views = @View(title = "检验任务单号"),
            edit = @Edit(title = "检验任务单号", readonly = @Readonly, notNull = true,
                    search = @Search(vague = true)),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "returnProductDetail",
                    dynamicHandler = ReturnProductInspectionTaskHandler.class))
    )
    private String productReturnInspectionTaskNumber;

    @FabosJsonField(
            views = @View(title = "检验任务单Id", show = false),
            edit = @Edit(title = "检验任务单Id", show = false)
    )
    private String productReturnInspectionTaskId;

    @FabosJsonField(
            views = @View(title = "检验结果"),
            edit = @Edit(title = "检验结果", readonly = @Readonly,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = InspectionResultEnum.class))
    )
    private String inspectionResult;

    @FabosJsonField(
            views = @View(title = "不合格等级"),
            edit = @Edit(title = "不合格等级", readonly = @Readonly,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = UnqualifiedLevelEnum.class),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "inspectionResult == 'UNQUALIFIED'"))
    )
    private String unqualifiedLevel;

    @FabosJsonField(
            views = @View(title = "检验部门"),
            edit = @Edit(title = "检验部门", readonly = @Readonly)
    )
    private String inspectionDepartment;

    @FabosJsonField(
            views = @View(title = "检验人"),
            edit = @Edit(title = "检验人", readonly = @Readonly)
    )
    private String inspector;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态", show = false, search = @Search,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ReturnProductHandlingStatusEnum.class))
    )
    private String status;

    @FabosJsonField(
            views = @View(title = "退货审批单"),
            edit = @Edit(title = "退货审批单", type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(maxLimit = 10))
    )
    private String returnApprovalOrder;

    // 以下字段在各功能按钮弹窗中录入，主页面只做展示

    @FabosJsonField(
            views = @View(title = "原因分析"),
            edit = @Edit(title = "原因分析", show = false)
    )
    private String causeAnalysis;

    @FabosJsonField(
            views = @View(title = "责任判定"),
            edit = @Edit(title = "责任判定", show = false, type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ReturnProductLiabilityJudgmentEnum.class))
    )
    private String liabilityJudgment;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "供应商", column = "supplierName"),
            edit = @Edit(title = "供应商", show = false,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "supplierName")
            )
    )
    private Supplier supplier;

    @FabosJsonField(
            views = @View(title = "问题解决责任部门id", show = false),
            edit = @Edit(title = "问题解决责任部门id", show = false)
    )
    private String responsibleDepartmentId;

    @FabosJsonField(
            views = @View(title = "问题解决责任部门"),
            edit = @Edit(title = "问题解决责任部门", show = false)
    )
    private String responsibleDepartmentName;

    @FabosJsonField(
            views = @View(title = "是否需要处置"),
            edit = @Edit(title = "是否需要处置", show = false, type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = YesOrNoEnum.class))
    )
    private String handleFlag;

    @FabosJsonField(
            views = @View(title = "方案输出部门id", show = false),
            edit = @Edit(title = "方案输出部门id", show = false)
    )
    private String suggestionDepartmentId;

    @FabosJsonField(
            views = @View(title = "方案输出部门"),
            edit = @Edit(title = "方案输出部门", show = false)
    )
    private String suggestionDepartmentName;

    @FabosJsonField(
            views = @View(title = "分析人ID", show = false),
            edit = @Edit(title = "分析人ID", show = false)
    )
    private String analystId;

    @FabosJsonField(
            views = @View(title = "分析人姓名"),
            edit = @Edit(title = "分析人姓名", show = false)
    )
    private String analystName;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "分析时间", type = ViewType.DATE),
            edit = @Edit(title = "分析时间", show = false, dateType = @DateType(type = DateType.Type.DATE))
    )
    private LocalDate productionDate;

    @FabosJsonField(
            views = @View(title = "处置意见"),
            edit = @Edit(title = "处置意见", show = false, type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = MRBDisposalOpinionEnum.class))
    )
    private String handlingSuggestion;

    @FabosJsonField(
            views = @View(title = "分析方法"),
            edit = @Edit(title = "分析方法", show = false, type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ReturnProductAnalysisMethodEnum.class))
    )
    private String analysisMethod;

    @FabosJsonField(
            views = @View(title = "分析方法关联单据"),
            edit = @Edit(title = "分析方法关联单据", show = false)
    )
    private String analysisMethodOrder;

    @FabosJsonField(
            views = @View(title = "作业指导书"),
            edit = @Edit(title = "作业指导书", show = false,
                    type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(maxLimit = 10),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "handlingSuggestion == 'Rework'")
            )
    )
    private String workingInstruction;

    @FabosJsonField(
            views = @View(title = "评审意见"),
            edit = @Edit(title = "评审意见", show = false, type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ReturnProductReviewCommentEnum.class))
    )
    private String reviewComment;

    @FabosJsonField(
            views = @View(title = "意见说明"),
            edit = @Edit(title = "意见说明", show = false,
                    type = EditType.TEXTAREA, inputType = @InputType(length = 400))
    )
    private String commentDescription;

    @FabosJsonField(
            views = @View(title = "附件"),
            edit = @Edit(title = "附件", show = false, type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(maxLimit = 10))
    )
    private String attachment;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "return_product_handling_id")
    @OrderBy
    @FabosJsonField(
            edit = @Edit(title = "检验项目详情", type = EditType.TAB_REFERENCE_GENERATE),
            views = @View(title = "检验项目详情", type = ViewType.TABLE_VIEW)
    )
    private List<ReturnProductHandlingDetail> details;

}
