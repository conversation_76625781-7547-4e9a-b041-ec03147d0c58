package cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.mto;

import cec.jiutian.bc.flow.domain.model.entity.ExamineModel;
import cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.enumration.ProductQualityTrackBusinessStatusEnum;
import cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.handler.PqtProcessOperationListHandler;
import cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.handler.ProductQualityTrackDynamicHandler;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.ReferenceGenerateType;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.ChoiceType;
import jakarta.persistence.*;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/15
 * @description
 */
@FabosJson(
        name = "结果审批"
)
@Table(name = "qms_pqt_product_quality_track",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Data
public class ProductQualityTrackApprovalMTO extends ExamineModel {
    @FabosJsonField(
            views = @View(title = "编号"),
            edit = @Edit(title = "编号",
                    readonly = @Readonly()
            ),
            dynamicField = @DynamicField(dependFiled = @DependFiled(buttonName = "生成", changeBy = "id",
                    dynamicHandler = ProductQualityTrackDynamicHandler.class))
    )
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",
                    show = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ProductQualityTrackBusinessStatusEnum.class)
            )
    )
    private String businessStatus;

    @FabosJsonField(
            views = @View(title = "工序列表",
                    column = "maintenanceStandardName",
                    type = ViewType.TABLE_VIEW),
            edit = @Edit(title = "工序列表",
                    type = EditType.TAB_REFERENCE_GENERATE
            ),
            referenceGenerateType = @ReferenceGenerateType(editable = {"comprehensiveJudgment", "remark"})
    )
    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumn(name = "qms_pqt_product_quality_track_id")
    private List<PqtProcessOperationApprovalMTO> pqtProcessOperationList;
}
