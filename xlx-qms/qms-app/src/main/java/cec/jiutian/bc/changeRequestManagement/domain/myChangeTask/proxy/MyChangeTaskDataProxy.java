package cec.jiutian.bc.changeRequestManagement.domain.myChangeTask.proxy;

import cec.jiutian.bc.changeRequestManagement.domain.ecn.model.MyECNItem;
import cec.jiutian.bc.changeRequestManagement.domain.myChangeTask.model.MyECRItem;
import cec.jiutian.bc.changeRequestManagement.domain.myChangeTask.model.MyChangeTask;
import cec.jiutian.bc.changeRequestManagement.enums.ProgressEnum;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import cec.jiutian.view.query.Condition;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

@Component
public class MyChangeTaskDataProxy implements DataProxy<MyChangeTask> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String beforeFetch(List<Condition> conditions) {
        String userId = UserContext.getUserId();
        return "MyChangeTask.allUserIds like '%"+userId+"%'";
    }

    @Override
    public void afterFetch(Collection<Map<String, Object>> list) {
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(d -> {
                MyChangeTask myChangeTask = fabosJsonDao.findById(MyChangeTask.class, d.get("id").toString());
                int operateFlag = 0;
                for (MyECRItem myECRItem : myChangeTask.getMyECRItems()) {
                    // 不是当前用户 跳过
                    if (!UserContext.getUserId().equals(myECRItem.getResponsiblePersonId())) {
                        continue;
                    }
                    // 只要前用户的状态 出现待执行 则显示按钮
                    if (!ProgressEnum.Enum.PENDING_VERIFICATION.name().equals(myECRItem.getProgress())) {
                        operateFlag = 1;
                        break;
                    }
                }
                d.put("rowOperationAuthFlag", operateFlag);

                // 补充变更
                int supplementAuthFlag = 0;
                for (MyECNItem myECNItem : myChangeTask.getMyECNItems()) {
                    // 不是当前用户 跳过
                    if (!UserContext.getUserId().equals(myECNItem.getResponsiblePersonId())) {
                        continue;
                    }
                    // 只要前用户的状态 出现待执行 则显示按钮
                    if (!ProgressEnum.Enum.PENDING_VERIFICATION.name().equals(myECNItem.getProgress())) {
                        supplementAuthFlag = 1;
                        break;
                    }
                }
                d.put("supplementAuthFlag", supplementAuthFlag);
            });
        }
    }

    @Override
    public void afterSingleFetch(Map<String, Object> map) {
        List<HashMap<String,Object>> list = (List<HashMap<String,Object>>) map.get("MyECRItems");
        List<HashMap<String,Object>> result = new ArrayList<>();
        for (HashMap<String, Object> data : list) {
            String responsiblePersonId = data.get("responsiblePersonId").toString();
            if (responsiblePersonId.equals(UserContext.getUserId())) {
                result.add(data);
            }
        }
        map.put("MyECRItems", result);
    }
}
