package cec.jiutian.bc.clientComplaintManagement.domain.clientComplaintFeedback.handler;

import cec.jiutian.bc.basicData.enumeration.NamingRuleCodeEnum;
import cec.jiutian.bc.clientComplaintManagement.domain.clientComplaintFeedback.enumration.CfbCorrectPreventProgressEnum;
import cec.jiutian.bc.clientComplaintManagement.domain.clientComplaintFeedback.model.ClientComplaintFeedback;
import cec.jiutian.bc.clientComplaintManagement.domain.clientComplaintFeedback.mto.ClientComplaintFeedbackCpMTO;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.enumration.CpmBusinessStateEnum;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.enumration.RelatedDocumentTypeEnum;
import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.urm.domain.user.entity.User;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/27
 * @description TODO
 */
@Component
public class ClientComplaintFeedbackCpHandler implements OperationHandler<ClientComplaintFeedback, ClientComplaintFeedbackCpMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private NamingRuleService namingRuleService;

    @Override
    public String exec(List<ClientComplaintFeedback> data, ClientComplaintFeedbackCpMTO modelObject, String[] param) {
        if (modelObject == null) {
            return "数据异常，模型为空";
        }
        User user = fabosJsonDao.getById(User.class, UserContext.getUserId());
        modelObject.setCreateUser(user);
        fabosJsonDao.insert(modelObject);

        ClientComplaintFeedback model = data.get(0);
        model.setCorPreStatus(CfbCorrectPreventProgressEnum.Enum.RUN_SUBMIT.name());
        fabosJsonDao.mergeAndFlush(model);
        return "alert(操作成功)";
    }

    @Override
    public ClientComplaintFeedbackCpMTO fabosJsonFormValue(List<ClientComplaintFeedback> data, ClientComplaintFeedbackCpMTO fabosJsonForm, String[] param) {
        ClientComplaintFeedback model = data.get(0);
        fabosJsonForm.setCorrectPreventMeasureFormNumber(namingRuleService.getNameCode(NamingRuleCodeEnum.CORRECT_PREVENT_MEASURE.name(), 1, null).get(0));
        fabosJsonForm.setCreatedTime(new Date());
        fabosJsonForm.setRelatedDocumentType(RelatedDocumentTypeEnum.Enum.CLIENT_COMPLAINT_FEEDBACK.name());
        fabosJsonForm.setRelatedDocumentMTOCode(model.getGeneralCode());
        fabosJsonForm.setRelatedDocumentMTOName(model.getGeneralCode());
        fabosJsonForm.setBusinessState(CpmBusinessStateEnum.Enum.TO_BE_RELEASED.name());

        return fabosJsonForm;
    }
}
