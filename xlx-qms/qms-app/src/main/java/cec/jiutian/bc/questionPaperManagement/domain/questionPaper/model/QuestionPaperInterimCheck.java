package cec.jiutian.bc.questionPaperManagement.domain.questionPaper.model;

import cec.jiutian.bc.questionPaperManagement.domain.questionPaper.proxy.QuestionPaperInterimCheckDataProxy;
import cec.jiutian.bc.questionPaperManagement.enums.QuestionPaperStateEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.ReferenceGenerateType;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.ChoiceType;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OrderBy;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Entity
@Table(name = "qpm_question_paper",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        })
@Getter
@Setter
@FabosJson(
        name = "问题一页纸-临时措施验证",
        dataProxy = QuestionPaperInterimCheckDataProxy.class
)
@TemplateType(type = "multiTable")
public class QuestionPaperInterimCheck extends MetaModel {

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态", show = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = QuestionPaperStateEnum.class))
    )
    private String currentState;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "question_paper_id")
    @OrderBy
    @FabosJsonField(
            edit = @Edit(title = "临时措施", type = EditType.TAB_REFERENCE_GENERATE),
            views = @View(title = "临时措施", type = ViewType.TABLE_VIEW),
            referenceGenerateType = @ReferenceGenerateType(editable = {"checkResult", "checkDate", "checkAttachment"})
    )
    private List<QuestionPaperInterimMeasure> interimMeasures;

}
