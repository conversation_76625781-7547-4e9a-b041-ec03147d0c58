package cec.jiutian.bc.changeRequestManagement.domain.ecr.proxy;

import cec.jiutian.bc.changeRequestManagement.domain.ecr.model.ChangeRequest;
import cec.jiutian.bc.changeRequestManagement.enums.ECRStatusEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.FlowProxy;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
public class ChangeRequestFlowProxy extends FlowProxy {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public void onEvent(Object event, Object entity) {

        if (!(entity instanceof ChangeRequest)) {
            return;
        }
        ChangeRequest changeRequest = (ChangeRequest) entity;

//        if (ExamineStatusEnum.AUDITED.getCode().equals(changeRequest.getExamineStatus())) {
//            pass(changeRequest);
//            fabosJsonDao.mergeAndFlush(changeRequest);
//            return;
//        }
//
//        if (ExamineStatusEnum.REJECTED.getCode().equals(changeRequest.getExamineStatus())) {
//            reject(changeRequest);
//            fabosJsonDao.mergeAndFlush(changeRequest);
//        }
    }

    private void pass(ChangeRequest changeRequest) {
        changeRequest.setStatus(ECRStatusEnum.Enum.WAIT_PLAN.name());
    }

    private void reject(ChangeRequest changeRequest) {
        changeRequest.setStatus(ECRStatusEnum.Enum.WAIT_REVIEW.name());
    }
}
