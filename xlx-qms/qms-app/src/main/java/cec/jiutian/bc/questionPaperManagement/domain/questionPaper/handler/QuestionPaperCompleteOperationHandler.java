package cec.jiutian.bc.questionPaperManagement.domain.questionPaper.handler;

import cec.jiutian.bc.generalModeler.util.StringUtils;
import cec.jiutian.bc.questionPaperManagement.domain.questionPaper.model.QuestionPaper;
import cec.jiutian.bc.questionPaperManagement.domain.questionPaper.model.QuestionPaperImproveMeasure;
import cec.jiutian.bc.questionPaperManagement.domain.questionPaper.model.QuestionPaperInterimMeasure;
import cec.jiutian.bc.questionPaperManagement.enums.QuestionPaperMeasureProgressEnum;
import cec.jiutian.bc.questionPaperManagement.enums.QuestionPaperStateEnum;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class QuestionPaperCompleteOperationHandler implements OperationHandler<QuestionPaper, Void> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<QuestionPaper> data, Void modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            QuestionPaper entity = data.get(0);
            // 校验措施都已验证完成
            if (CollectionUtils.isNotEmpty(entity.getInterimMeasures())) {
                for (QuestionPaperInterimMeasure interimMeasure : entity.getInterimMeasures()) {
                    if (!StringUtils.equals(interimMeasure.getProgress(), QuestionPaperMeasureProgressEnum.Enum.Complete.name())) {
                        throw new FabosJsonApiErrorTip("临时措施未全部完成，不可关闭，请确认");
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(entity.getImproveMeasures())) {
                for (QuestionPaperImproveMeasure improveMeasure : entity.getImproveMeasures()) {
                    if (!StringUtils.equals(improveMeasure.getProgress(), QuestionPaperMeasureProgressEnum.Enum.Complete.name())) {
                        throw new FabosJsonApiErrorTip("改善措施未全部完成，不可关闭，请确认");
                    }
                }
            }

            entity.setCurrentState(QuestionPaperStateEnum.Enum.Complete.name());
            fabosJsonDao.mergeAndFlush(entity);
        }
        return "msg.success('操作成功')";
    }
}
