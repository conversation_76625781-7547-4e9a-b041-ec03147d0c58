package cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.handler;

import cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.enumration.AuditStatusEnum;
import cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.enumration.TaskBusinessStateEnum;
import cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.model.AbnormalFeedbackHandling;
import cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.mto.AbnormalFeedbackHandlingReviewMTO;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/27
 * @description TODO
 */
@Component
public class AbnormalFeedbackHandlingReviewHandler implements OperationHandler<AbnormalFeedbackHandling, AbnormalFeedbackHandlingReviewMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<AbnormalFeedbackHandling> data, AbnormalFeedbackHandlingReviewMTO modelObject, String[] param) {
        if (modelObject != null) {
            AbnormalFeedbackHandling model = data.get(0);
            BeanUtil.copyProperties(modelObject, model, "createBy", "createTime");
            if (modelObject.getReviewComments().equals(AuditStatusEnum.Enum.PASS.name())) {
                model.setBusinessState(TaskBusinessStateEnum.Enum.PENDING_SCHEME_REVIEW.name());
            } else {
                model.setBusinessState(TaskBusinessStateEnum.Enum.TO_BE_ANALYZED.name());
            }
            fabosJsonDao.mergeAndFlush(model);
        }
        return "alert(操作成功)";
    }

    @Override
    public AbnormalFeedbackHandlingReviewMTO fabosJsonFormValue(List<AbnormalFeedbackHandling> data, AbnormalFeedbackHandlingReviewMTO fabosJsonForm, String[] param) {
        AbnormalFeedbackHandling abnormalFeedbackHandling = data.get(0);
        BeanUtil.copyProperties(abnormalFeedbackHandling, fabosJsonForm);

        return fabosJsonForm;
    }
}
