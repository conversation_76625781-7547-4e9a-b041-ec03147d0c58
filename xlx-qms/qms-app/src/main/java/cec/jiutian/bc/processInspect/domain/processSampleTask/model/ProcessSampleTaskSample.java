package cec.jiutian.bc.processInspect.domain.processSampleTask.model;

import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleBaseModel;
import cec.jiutian.bc.modeler.enumration.TaskBusinessStateEnum;
import cec.jiutian.bc.processInspect.domain.processSampleTask.handler.IPQCSampleTaskSampleQuantityDynamicHandler;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.NumberType;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OrderBy;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@FabosJson(
        name = "过程取样任务-取样（检验样）"
)
@Table(name = "pi_sample_task",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Getter
@Setter
public class ProcessSampleTaskSample extends NamingRuleBaseModel {

    @FabosJsonField(
            views = @View(title = "检验任务单号"),
            edit = @Edit(title = "检验任务单号", readonly = @Readonly)
    )
    private String inspectionTaskCode;

    @FabosJsonField(
            views = @View(title = "检验状态"),
            edit = @Edit(title = "检验状态", readonly = @Readonly, type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = TaskBusinessStateEnum.class))
    )
    private String businessState;

    @FabosJsonField(
            views = @View(title = "产品编码"),
            edit = @Edit(title = "产品编码", readonly = @Readonly)
    )
    private String productCode;

    @FabosJsonField(
            views = @View(title = "产品名称"),
            edit = @Edit(title = "产品名称", readonly = @Readonly)
    )
    private String productName;

    @FabosJsonField(
            views = @View(title = "工艺编码"),
            edit = @Edit(title = "工艺编码", readonly = @Readonly)
    )
    private String processCode;

    @FabosJsonField(
            views = @View(title = "工序编码"),
            edit = @Edit(title = "工序编码", readonly = @Readonly)
    )
    private String operationCode;

    @FabosJsonField(
            views = @View(title = "工序名称"),
            edit = @Edit(title = "工序名称", readonly = @Readonly)
    )
    private String operationName;

    @FabosJsonField(
            views = @View(title = "实际取样批号"),
            edit = @Edit(title = "实际取样批号", notNull = true)
    )
    private String actualLotSerialId;

    @FabosJsonField(
            views = @View(title = "在制品编码"),
            edit = @Edit(title = "在制品编码")
    )
    private String workProgressCode;

    @FabosJsonField(
            views = @View(title = "在制品名称"),
            edit = @Edit(title = "在制品名称")
    )
    private String workProgressName;

    @FabosJsonField(
            views = @View(title = "在制品型号"),
            edit = @Edit(title = "在制品型号")
    )
    private String workProgressModel;

    @FabosJsonField(
            views = @View(title = "样本批次数量"),
            edit = @Edit(title = "样本批次数量", notNull = true,
                    numberType = @NumberType(min = 0, max = 9999999, precision = 2))
    )
    private Double lotQuantity;

    @FabosJsonField(
            views = @View(title = "取样数量"),
            edit = @Edit(title = "取样数量", notNull = true, readonly = @Readonly,
                    numberType = @NumberType(min = 0, max = 9999999, precision = 2)),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "lotQuantity", dynamicHandler = IPQCSampleTaskSampleQuantityDynamicHandler.class))
    )
    private Double sampleQuantity;

    @FabosJsonField(
            views = @View(title = "样品单位"),
            edit = @Edit(title = "样品单位")
    )
    private String unit;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "sample_task_id")
    @OrderBy
    @FabosJsonField(
            edit = @Edit(title = "项目详情", type = EditType.TAB_REFERENCE_GENERATE),
            views = @View(title = "项目详情", type = ViewType.TABLE_VIEW)
    )
    private List<ProcessSampleTaskDetail> details;

}
