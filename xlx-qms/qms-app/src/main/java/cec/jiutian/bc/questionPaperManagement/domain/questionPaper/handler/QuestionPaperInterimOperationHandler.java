package cec.jiutian.bc.questionPaperManagement.domain.questionPaper.handler;

import cec.jiutian.bc.questionPaperManagement.domain.questionPaper.model.QuestionPaper;
import cec.jiutian.bc.questionPaperManagement.domain.questionPaper.model.QuestionPaperInterim;
import cec.jiutian.bc.questionPaperManagement.domain.questionPaper.model.QuestionPaperInterimMeasure;
import cec.jiutian.bc.questionPaperManagement.enums.QuestionPaperMeasureProgressEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class QuestionPaperInterimOperationHandler implements OperationHandler<QuestionPaper, QuestionPaperInterim> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<QuestionPaper> data, QuestionPaperInterim modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            QuestionPaper entity = data.get(0);
            if (CollectionUtils.isNotEmpty(modelObject.getInterimMeasures())) {
//                List<QuestionPaperInterimMeasure> list = new ArrayList<>();
                List<QuestionPaperInterimMeasure> list = entity.getInterimMeasures();
                list.clear();
                modelObject.getInterimMeasures().forEach(m -> {
                    QuestionPaperInterimMeasure measure = new QuestionPaperInterimMeasure();
                    BeanUtils.copyProperties(m, measure);
                    measure.setProgress(QuestionPaperMeasureProgressEnum.Enum.WaitExecute.name());
                    list.add(measure);
                });
                entity.setInterimMeasures(list);
                entity.setInterimFlag(true);
                fabosJsonDao.mergeAndFlush(entity);
            }
        }
        return "msg.success('操作成功')";
    }
}
