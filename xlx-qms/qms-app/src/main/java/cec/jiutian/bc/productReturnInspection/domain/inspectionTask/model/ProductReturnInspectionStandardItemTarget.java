package cec.jiutian.bc.productReturnInspection.domain.inspectionTask.model;

import cec.jiutian.bc.materialInspect.enumeration.InspectionResultEnum;
import cec.jiutian.bc.modeler.enumration.ComparisonMethodEnum;
import cec.jiutian.bc.modeler.enumration.InspectionValueTypeEnum;
import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.InheritStrategy;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.*;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025/3/11
 * @description TODO
 */
@FabosJson(
        name = "检验任务质检标准-检验项指标"
)
@Table(name = "mi_quality_inspection_standard_detail_target")
@Entity
@Getter
@Setter
@InheritStrategy(
        excludeParentFields = {"inspectionItem"}
)
public class ProductReturnInspectionStandardItemTarget extends BaseModel {
    @FabosJsonField(
            views = {
                    @View(title = "检验项编码", column = "name")
            },
            edit = @Edit(title = "检验项", type = EditType.REFERENCE_TABLE,show = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"))
    )
    @ManyToOne
    @JsonIgnoreProperties("productReturnInspectionStandardItemTargetList")
    private ProductReturnQualityInspectionStandardDetail productReturnQualityInspectionStandardDetail;

    @FabosJsonField(
            views = @View(title = "检验项目id",show = false),
            edit = @Edit(title = "检验项目id",show = false)
    )
    private String targetId;

    @FabosJsonField(
            views = @View(title = "检验项指标名称"),
            edit = @Edit(title = "检验项指标名称", search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    private String name;

    @FabosJsonField(
            views = @View(title = "值类型"),
            edit = @Edit(title = "值类型", type = EditType.CHOICE,search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = InspectionValueTypeEnum.class))
    )
    private String inspectionValueType;

    @FabosJsonField(
            views = @View(title = "比较方式"),
            edit = @Edit(title = "比较方式",
                    search = @Search(),
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ComparisonMethodEnum.class))
    )
    private String comparisonMethod;

    @FabosJsonField(
            views = @View(title = "单位"),
            edit = @Edit(title = "单位",
                    inputType = @InputType(length = 40))
    )
    private String unit;

    @FabosJsonField(
            views = @View(title = "标准值"),
            edit = @Edit(title = "标准值",notNull = true)
    )
    private String standardValue;

    @FabosJsonField(
            views = @View(title = "上限值"),
            edit = @Edit(title = "上限值",notNull = true,
                    numberType = @NumberType(min=0,max = 9999999,precision = 2))
    )
    private Double UpperValue;

    @FabosJsonField(
            views = @View(title = "是否包含上限值"),
            edit = @Edit(title = "是否包含上限值", defaultVal = "true"
            )
    )
    private Boolean isContainUpper;

    @FabosJsonField(
            views = @View(title = "下限值"),
            edit = @Edit(title = "下限值",notNull = true,
                    numberType = @NumberType(min=0,max = 9999999,precision = 2))
    )
    private Double lowerValue;

    @FabosJsonField(
            views = @View(title = "是否包含下限值"),
            edit = @Edit(title = "是否包含下限值", defaultVal = "true"
            )
    )
    private Boolean isContainLower;

    @FabosJsonField(
            views = @View(title = "检验值"),
            edit = @Edit(title = "检验值")
    )
    private String resultValue;

    @FabosJsonField(
            views = @View(title = "指标检验结果"),
            edit = @Edit(title = "指标检验结果",choiceType = @ChoiceType(fetchHandler = InspectionResultEnum.class))
    )
    private String targetResult;

    @FabosJsonField(
            views = @View(title = "描述"),
            edit = @Edit(title = "描述",
                    type = EditType.TEXTAREA)
    )
    private String description;
}
