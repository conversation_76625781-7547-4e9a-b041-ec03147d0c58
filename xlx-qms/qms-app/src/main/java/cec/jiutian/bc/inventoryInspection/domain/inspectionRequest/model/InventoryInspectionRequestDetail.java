package cec.jiutian.bc.inventoryInspection.domain.inspectionRequest.model;

import cec.jiutian.bc.generalModeler.enumeration.YesOrNoEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@FabosJson(
        name = "检验通知详情"
)
@Table(name = "mi_inspection_request_detail")
@Entity
@Getter
@Setter
public class InventoryInspectionRequestDetail extends MetaModel {

    @ManyToOne
    @FabosJsonField(
            views = {
                    @View(title = "检验通知单号", column = "generalCode", show = false)
            },
            edit = @Edit(title = "检验通知单号", type = EditType.REFERENCE_TABLE, show = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode")
            )
    )
    @JsonIgnoreProperties("details")
    private InventoryInspectionRequest inventoryInspectionRequest;

    @FabosJsonField(
            views = @View(title = "物料编码"),
            edit = @Edit(title = "物料编码")
    )
    private String materialCode;

    @FabosJsonField(
            views = @View(title = "物料名称"),
            edit = @Edit(title = "物料名称")
    )
    private String materialName;

    @FabosJsonField(
            views = @View(title = "规格"),
            edit = @Edit(title = "规格")
    )
    private String materialSpecification;

    @FabosJsonField(
            views = @View(title = "来料批号"),
            edit = @Edit(title = "来料批号")
    )
    private String originLotId;

    @FabosJsonField(
            views = @View(title = "本厂批号/件次号"),
            edit = @Edit(title = "本厂批号/件次号")
    )
    private String lotSerialId;

    @FabosJsonField(
            views = @View(title = "本厂批号/件次号库存Id", show = false),
            edit = @Edit(title = "本厂批号/件次号库存Id")
    )
    private String inventoryId;

    @FabosJsonField(
            views = @View(title = "批次数量"),
            edit = @Edit(title = "批次数量")
    )
    private Double lotQuantity;

    @FabosJsonField(
            views = @View(title = "样品数量"),
            edit = @Edit(title = "样品数量")
    )
    private Double originSampleQuantity;

    @FabosJsonField(
            views = @View(title = "单位"),
            edit = @Edit(title = "单位")
    )
    private String measureUnit;

    @FabosJsonField(
            views = @View(title = "是否随样"),
            edit = @Edit(title = "是否随样", type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = YesOrNoEnum.class))
    )
    private String sampleFlag;

    @FabosJsonField(
            views = @View(title = "是否COA"),
            edit = @Edit(title = "是否COA", type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = YesOrNoEnum.class))
    )
    private String COAFlag;

}
