package cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.mto;

import cec.jiutian.bc.modeler.domain.inspectionItem.model.InspectionItem;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.ReferenceGenerateType;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.field.edit.TabTableReferType;
import cec.jiutian.view.type.Filter;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/15
 * @description
 */
@FabosJson(
        name = "创建工序列表",
        orderBy = "PqtProcessOperationCreateMTO.createTime desc"
)
@Table(name = "qms_pqt_process_operation")
@Entity
@Getter
@Setter
public class PqtProcessOperationCreateMTO extends MetaModel {
    @FabosJsonField(
            views = @View(title = "工序编码", show = false),
            edit = @Edit(title = "工序编码", show = false)
    )
    private String processOperationCode;

    @FabosJsonField(
            views = @View(title = "工序名称"),
            edit = @Edit(title = "工序名称", show = false)
    )
    private String processOperationName;

    @FabosJsonField(
            views = @View(title = "物料"),
            edit = @Edit(title = "物料"))
    private String pqtMaterial;

    @ManyToMany
    @JoinTable(
            name = "e_pqt_inspection_item",
            inverseForeignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT),
            joinColumns = @JoinColumn(name = "process_operation_list_id", referencedColumnName = "id"),
            inverseJoinColumns = @JoinColumn(name = "inspection_item_id", referencedColumnName = "id"))
    @FabosJsonField(
            views = @View(title = "检验项目",type = ViewType.TABLE_VIEW),
            edit = @Edit(title = "检验项目", type = EditType.TAB_TABLE_REFER, search = @Search(vague = true),
                    tabTableReferType = @TabTableReferType(type = TabTableReferType.SelectShowTypeMTM.LIST,
                            label = "name"),
                    filter = @Filter(value = "InspectionItem.status = 'Effective'")
            )
    )
    private List<InspectionItem> inspectionInstrumentList;

    @FabosJsonField(
            views = @View(title = "产品列表",
                    column = "processOperationName",
                    show = false,
                    type = ViewType.TABLE_VIEW),
            edit = @Edit(title = "产品列表",
                    show = false,
                    type = EditType.TAB_REFERENCE_GENERATE
            )
    )
    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumn(name = "qms_pqt_process_operation_id")
    private List<PqtProductBatchNumberCreateMTO> pqtProductBatchNumberList;
}
