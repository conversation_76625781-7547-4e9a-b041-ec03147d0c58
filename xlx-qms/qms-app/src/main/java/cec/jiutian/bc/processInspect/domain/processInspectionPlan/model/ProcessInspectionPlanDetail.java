package cec.jiutian.bc.processInspect.domain.processInspectionPlan.model;

import cec.jiutian.bc.processInspect.domain.processInspectionPlan.handler.IPQCPlanItemDynamicHandler;
import cec.jiutian.bc.processInspect.enumeration.InspectionPlanDetailTypeEnum;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.ReferenceAddType;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OrderBy;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@FabosJson(
        name = "过程检验计划详情"
)
@Table(name = "pi_inspection_plan_detail")
@Entity
@Getter
@Setter
public class ProcessInspectionPlanDetail extends MetaModel {

    @ManyToOne
    @FabosJsonField(
            views = {
                    @View(title = "计划单号", column = "generalCode", show = false)
            },
            edit = @Edit(title = "计划单号", type = EditType.REFERENCE_TABLE, show = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode")
            )
    )
    @JsonIgnoreProperties("details")
    private ProcessInspectionPlan inspectionPlan;

    @FabosJsonField(
            views = @View(title = "工序编码"),
            edit = @Edit(title = "工序编码", readonly = @Readonly)
    )
    private String operationCode;

    @FabosJsonField(
            views = @View(title = "工序名称"),
            edit = @Edit(title = "工序名称", readonly = @Readonly)
    )
    private String operationName;

    @FabosJsonField(
            views = @View(title = "类型"),
            edit = @Edit(title = "类型", readonly = @Readonly, type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = InspectionPlanDetailTypeEnum.class))
    )
    private String type;

    @FabosJsonField(
            views = @View(title = "当前状态"),
            edit = @Edit(title = "当前状态",readonly = @Readonly, defaultVal = "EDIT",
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = OrderCurrentStateEnum.class)),
            dynamicField = @DynamicField(passive = true)
    )
    private String currentState;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "inspection_plan_detail_id")
    @OrderBy
    @FabosJsonField(
            edit = @Edit(title = "计划详情检验项目", type = EditType.TAB_REFER_ADD),
            views = @View(title = "计划详情检验项目",column = "name", type = ViewType.TABLE_VIEW, extraPK = "itemId"),
            referenceAddType = @ReferenceAddType(referenceClass = "InspectionItem",
                    filter = "InspectionItem.status = 'Effective'",
                    referenceAddHandler = IPQCPlanItemDynamicHandler.class,
                    editable = {"inspectionItemGroup"})
    )
    private List<ProcessInspectionPlanItem> items;
}
