package cec.jiutian.bc.inventoryInspection.domain.reportPrintTemplate.mto;

import cec.jiutian.bc.basicData.domain.inspectionStandard.model.InspectionStandard;
import cec.jiutian.bc.basicData.enumeration.ApplyRangeEnum;
import cec.jiutian.bc.inventoryInspection.domain.inspectionRequest.model.InventoryInspectionRequest;
import cec.jiutian.bc.inventoryInspection.domain.inspectionTask.model.InventoryInspectionStandardDetail;
import cec.jiutian.bc.inventoryInspection.domain.inspectionTask.model.InventoryInspectionTaskDetail;
import cec.jiutian.bc.inventoryInspection.domain.inspectionTask.proxy.MyInvInsTaskDataProxy;
import cec.jiutian.bc.inventoryInspection.enumeration.InspectionResultEnum;
import cec.jiutian.bc.inventoryInspection.enumeration.TaskBuildTypeEnum;
import cec.jiutian.bc.modeler.enumration.TaskBusinessStateEnum;
import cec.jiutian.bc.modeler.enumration.UnqualifiedLevelEnum;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.ReferenceGenerateType;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DependFieldDisplay;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@FabosJson(
        name = "库存检验任务",
        orderBy = "createTime desc",
        filter = @Filter(value = "inspectionType = 'inventoryInspect'"),
        dataProxy = MyInvInsTaskDataProxy.class,
        power = @Power(add = false,edit = false,delete = false)
)
@Table(name = "mi_inspection_task",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Getter
@Setter
@TemplateType(type = "multiTable")
public class InventoryInspectionTaskMTO extends MetaModel {


    @FabosJsonField(
            views = @View(title = "检验任务编号"),
            edit = @Edit(title = "检验任务编号", readonly = @Readonly())
    )
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "检验状态"),
            edit = @Edit(title = "检验状态",show = false, type = EditType.CHOICE,search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = TaskBusinessStateEnum.class), readonly = @Readonly())
    )
    private String businessState;

    @FabosJsonField(
            views = @View(title = "当前状态"),
            edit = @Edit(title = "当前状态",show = false, type = EditType.CHOICE,search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = OrderCurrentStateEnum.class), readonly = @Readonly())
    )
    private String currentState;

    @FabosJsonField(
            views = @View(title = "供应商名称"),
            edit = @Edit(title = "供应商名称", search = @Search(vague = true), readonly = @Readonly())
    )
    private String supplierName;

    @FabosJsonField(
            views = @View(title = "检验部门"),
            edit = @Edit(title = "检验部门", search = @Search(vague = true), readonly = @Readonly())
    )
    private String inspectionDepartmentName;

    @FabosJsonField(
            views = @View(title = "检验人"),
            edit = @Edit(title = "检验人", search = @Search(vague = true), readonly = @Readonly())
    )
    private String inspectorName;

    @FabosJsonField(
            views = @View(title = "质检标准", column = "generalCode"),
            edit = @Edit(title = "质检标准",
                    readonly = @Readonly(), allowAddMultipleRows = false,
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode")
            )
    )
    @ManyToOne
    @JoinColumn(name = "inspection_standard_id")
    private InspectionStandard inspectionStandard;

    @FabosJsonField(
            views = @View(title = "创建类型"),
            edit = @Edit(title = "创建类型", type = EditType.CHOICE, search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = TaskBuildTypeEnum.class), readonly = @Readonly())
    )
    private String buildType;

    @FabosJsonField(
            views = @View(title = "检验类型"),
            edit = @Edit(title = "检验类型", show = false, type = EditType.CHOICE, search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = ApplyRangeEnum.class), readonly = @Readonly())
    )
    private String inspectionType;

    @FabosJsonField(
            views = @View(title = "是否加急"),
            edit = @Edit(title = "是否加急", defaultVal = "false", readonly = @Readonly())
    )
    private Boolean isUrgent;

    @FabosJsonField(
            views = @View(title = "检验结果"),
            edit = @Edit(title = "检验结果", type = EditType.CHOICE, search = @Search(), show = false,
                    choiceType = @ChoiceType(fetchHandler = InspectionResultEnum.class), readonly = @Readonly())
    )
    private String inspectionResult;

    @FabosJsonField(
            views = @View(title = "不合格等级"),
            edit = @Edit(title = "不合格等级", notNull = true,type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = UnqualifiedLevelEnum.class),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "inspectionResult == 'UNQUALIFIED'"), readonly = @Readonly())
    )
    private String unqualifiedLevel;

    @FabosJsonField(
            views = @View(title = "检验通知", column = "generalCode", type = ViewType.TABLE_FORM),
            edit = @Edit(title = "检验通知",
                    type = EditType.REFERENCE_TABLE, allowAddMultipleRows = false,
                    notNull = true,
                    readonly = @Readonly(),
                    filter = @Filter(value = "InventoryInspectionRequest.type = 'Overdue'"),
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode")
            )
    )
    @ManyToOne
    @JoinColumn(name = "inspection_request_id")
    private InventoryInspectionRequest inventoryInspectionRequest;

    @FabosJsonField(
            views = @View(title = "检验物资名称"),
            edit = @Edit(title = "检验物资名称", readonly = @Readonly())
    )
    private String materialName;

    @FabosJsonField(
            views = @View(title = "单位"),
            edit = @Edit(title = "单位", notNull = true, readonly = @Readonly())
   )
    private String unit;

    @FabosJsonField(
            views = @View(title = "批次号"),
            edit = @Edit(title = "批次号", readonly = @Readonly())
   )
    private String originLotId;

    @FabosJsonField(
            views = @View(title = "规格"),
            edit = @Edit(title = "规格", readonly = @Readonly())
    )
    private String materialSpecification;

    @FabosJsonField(
            views = @View(title = "物资分级"),
            edit = @Edit(title = "物资分级", readonly = @Readonly())
    )
    private String materialLevel;

    @FabosJsonField(
            views = @View(title = "来料数量"),
            edit = @Edit(title = "来料数量", readonly = @Readonly())
    )
    private Double arrivalQuantity;

    @FabosJsonField(
            views = @View(title = "样品数量"),
            edit = @Edit(title = "样品数量", readonly = @Readonly())
    )
    private Double sampleQuantity;

    @FabosJsonField(
            views = @View(title = "取样数量"),
            edit = @Edit(title = "取样数量", readonly = @Readonly())
    )
    private Double allInspectionItemQuantity;

    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumn(name = "inspection_task_id")
    @OrderBy
    @FabosJsonField(
            views = @View(title = "检验物资明细", type= ViewType.TABLE_VIEW,index = 8),
            edit = @Edit(title = "检验物资明细",type = EditType.TAB_REFERENCE_GENERATE, readonly = @Readonly()),
            referenceGenerateType = @ReferenceGenerateType()
    )
    private List<InventoryInspectionTaskDetail> inventoryInspectionTaskDetailList;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = false)
    @FabosJsonField(
            views = @View(title = "检验项目明细", type = ViewType.TABLE_VIEW, index = 8),
            edit = @Edit(title = "检验项目明细", type = EditType.TAB_REFERENCE_GENERATE, readonly = @Readonly()),
           referenceGenerateType = @ReferenceGenerateType()
    )
    @JoinColumn(name = "inspection_task_id")
    private List<InventoryInspectionStandardDetail> standardDetailList;

    @FabosJsonField(
            views = @View(title = "检验员姓名"),
            edit = @Edit(title = "检验员姓名",
                    show = false,
                    readonly = @Readonly(add = false, edit = false),
                    search = @Search(vague = true)
            )
    )
    private String userName;

    @FabosJsonField(
            views = @View(title = "备注"),
            edit = @Edit(title = "备注",
                    type = EditType.TEXTAREA, readonly = @Readonly()
            )
    )
    private String remark;
}
