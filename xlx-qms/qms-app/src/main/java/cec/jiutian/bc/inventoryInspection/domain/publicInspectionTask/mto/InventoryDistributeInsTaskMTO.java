package cec.jiutian.bc.inventoryInspection.domain.publicInspectionTask.mto;

import cec.jiutian.bc.inventoryInspection.domain.publicInspectionTask.proxy.InventoryDisInsTaskMTODataProxy;
import cec.jiutian.bc.modeler.enumration.TaskBusinessStateEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DateType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;


@FabosJson(
        name = "分配任务",
        orderBy = "createTime desc",
        dataProxy = InventoryDisInsTaskMTODataProxy.class,
        power = @Power(add = false,export = false)
)
@Table(name = "mi_inspection_task")
@Entity
@Getter
@Setter
@TemplateType(type = "multiTable")
public class InventoryDistributeInsTaskMTO extends MetaModel {


    @FabosJsonField(
            views = @View(title = "任务单号"),
            edit = @Edit(title = "任务单号",
                    search = @Search,
                    readonly = @Readonly(add = true, edit = true)
            )
    )
    private String generalCode;


    @FabosJsonField(
            views = @View(title = "物资名称"),
            edit = @Edit(title = "物资名称",
                    readonly = @Readonly(add = true, edit = true),
                    search = @Search(vague = true)
            )
    )
    private String materialName;

    @FabosJsonField(
            views = @View(title = "物资编码"),
            edit = @Edit(title = "物资编码",
                    readonly = @Readonly(add = true, edit = true),
                    search = @Search(vague = true)
            )
    )
    private String materialCode;


    @FabosJsonField(
            views = @View(title = "任务状态"),
            edit = @Edit(title = "任务状态",
                    readonly = @Readonly(add = true, edit = true),
                    search = @Search,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = TaskBusinessStateEnum.class)
            )
    )
    private String businessState;

    @FabosJsonField(
            views = @View(title = "创建日期"),
            edit = @Edit(title = "创建日期",
                    readonly = @Readonly(add = true, edit = true),
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date creationDate; // 创建日期

    @Transient
    @FabosJsonField(
            views = @View(title = "选择检验员",show = false, column = "name"),
            edit = @Edit(title = "检验员",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType()
            )
    )
    private InventoryUserForInsTaskMTO user;

    @FabosJsonField(
            views = @View(title = "检验员ID",show = false),
            edit = @Edit(title = "检验员ID",
                    show = false
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "user", beFilledBy = "id"))
    )
    private String userId;

    @FabosJsonField(
            views = @View(title = "检验员姓名"),
            edit = @Edit(title = "检验员姓名",
                    notNull = true,
                    readonly = @Readonly(add = true, edit = true),
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "user", beFilledBy = "name"))
    )
    private String userName;

}
