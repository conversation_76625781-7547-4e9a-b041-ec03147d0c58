package cec.jiutian.bc.processInspect.domain.publicInspectionTask.mto;


import cec.jiutian.bc.basicData.enumeration.PackageTypeEnum;
import cec.jiutian.bc.modeler.domain.samplingPlan.model.SamplingPlan;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import jakarta.persistence.CascadeType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;

import java.util.List;

//@FabosJson(
//        name = "检验项目",
//        orderBy = "createTime desc"
//)
//@Table(name = "bd_inspection_item_mto"
//)
//@Entity
//@Getter
//@Setter
//@TemplateType(type = "multiTable")
public class ProcessInsItemDetailEnterResMTO extends MetaModel {
    @FabosJsonField(
            views = @View(title = "编号"),
            edit = @Edit(title = "编号",
                    readonly = @Readonly(add = true, edit = true)
            )
    )
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "检验项目名称"),
            edit = @Edit(title = "检验项目名称",
                    readonly = @Readonly(add = true, edit = true),
                    search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    private String name;

    @FabosJsonField(
            views = @View(title = "特性"),
            edit = @Edit(title = "特性",
                    readonly = @Readonly(add = true, edit = true),
                    inputType = @InputType(length = 40))
    )
    private String feature;

    @FabosJsonField(
            views = @View(title = "是否自动判断"),
            edit = @Edit(title = "是否自动判断",
                    readonly = @Readonly(add = true, edit = true),
                    defaultVal = "false"
            )
    )
    private Boolean isAutoJudged;

    @FabosJsonField(
            views = @View(title = "包装方式"),
            edit = @Edit(title = "包装方式", type = EditType.CHOICE, search = @Search(),
                    readonly = @Readonly(add = true, edit = true),
                    choiceType = @ChoiceType(fetchHandler = PackageTypeEnum.class))
    )
    private String packageType;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "抽样方案", column = "name", type = ViewType.TABLE_FORM),
            edit = @Edit(title = "抽样方案",
                    readonly = @Readonly(add = true, edit = true),
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    search = @Search()
            )
    )
    private SamplingPlan samplingPlan;

    @FabosJsonField(
            views = @View(title = "样本量"),
            edit = @Edit(title = "样本量",
                    readonly = @Readonly(add = true, edit = true),
                    numberType = @NumberType(min = 0, max = 9999999, precision = 2))
    )
    private Double sampleSize;

    @FabosJsonField(
            views = @View(title = "取样点"),
            edit = @Edit(title = "取样点",
                    readonly = @Readonly(add = true, edit = true),
                    inputType = @InputType(length = 40))
    )
    private String samplingPoint;

    @FabosJsonField(
            views = @View(title = "送检点"),
            edit = @Edit(title = "送检点",
                    readonly = @Readonly(add = true, edit = true),
                    inputType = @InputType(length = 40))
    )
    private String sendInspectPoint;

    @FabosJsonField(
            views = @View(title = "备注"),
            edit = @Edit(title = "备注",
                    readonly = @Readonly(add = true, edit = true),
                    type = EditType.TEXTAREA)
    )
    private String remark;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = false)
    @JoinColumn(name = "inspection_item_id")
    @FabosJsonField(
            views = @View(title = "检验项指标", type = ViewType.TABLE_VIEW),
            edit = @Edit(title = "检验项指标", type = EditType.TAB_TABLE_ADD)
    )
    private List<ProcessInsItemTargetResMTO> inspectionItemTargetList;


}
