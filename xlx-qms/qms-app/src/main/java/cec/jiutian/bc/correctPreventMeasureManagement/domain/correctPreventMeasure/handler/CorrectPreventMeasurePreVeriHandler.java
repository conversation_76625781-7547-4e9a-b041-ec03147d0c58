package cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.handler;

import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.enumration.CpmBusinessStateEnum;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.enumration.ImprovingProgressEnum;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.enumration.ProgressEnum;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.enumration.VerificationResultEnum;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.model.CorrectPreventMeasure;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.mto.CorrectPreventMeasurePreVeriMTO;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.mto.PreventMeasureMTO;
import cec.jiutian.bc.ecs.dto.SendMsgGroupDTO;
import cec.jiutian.bc.ecs.provider.EcsMessageProvider;
import cec.jiutian.bc.urm.domain.user.entity.User;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/30
 * @description TODO
 */
@Component
@Slf4j
public class CorrectPreventMeasurePreVeriHandler implements OperationHandler<CorrectPreventMeasure, CorrectPreventMeasurePreVeriMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private EcsMessageProvider ecsMessageProvider;

    @Override
    public String exec(List<CorrectPreventMeasure> data, CorrectPreventMeasurePreVeriMTO modelObject, String[] param) {
        if (modelObject != null) {
            if (isPreventiveVerificationPassed(modelObject)) {
                verificationSuccessHandler(modelObject);
            } else {
                returnToCorrected(modelObject);
                sendQms(modelObject);
            }
            CorrectPreventMeasure model = data.get(0);
            BeanUtil.copyProperties(modelObject, model, "createBy", "createTime");
            fabosJsonDao.mergeAndFlush(model);
        }
        return "alert(操作成功)";
    }

    @Override
    public CorrectPreventMeasurePreVeriMTO fabosJsonFormValue(List<CorrectPreventMeasure> data, CorrectPreventMeasurePreVeriMTO fabosJsonForm, String[] param) {
        CorrectPreventMeasure model = data.get(0);
        BeanUtil.copyProperties(model, fabosJsonForm);
        return fabosJsonForm;
    }

    private void sendQms(CorrectPreventMeasurePreVeriMTO model) {
        String message = "预防验证不通过，请尽快在处理，编号：" + model.getCorrectPreventMeasureFormNumber();
        SendMsgGroupDTO sendMsgGroupDTO = new SendMsgGroupDTO();
        sendMsgGroupDTO.setMessageGroupCode("CorrectPreventMeasurePreVeriInfo");
        sendMsgGroupDTO.setContent(message);
        ecsMessageProvider.sendGroupMessage(sendMsgGroupDTO);
        log.info(message);
    }

    private void setVerifiedStatus(CorrectPreventMeasurePreVeriMTO model) {
        if (model.getCorrectVeriState().equals(ProgressEnum.Enum.RUN_SUBMIT.name())) {
            model.setBusinessState(CpmBusinessStateEnum.Enum.COMPLETED.name());
        }
        model.setPreVeriState(ProgressEnum.Enum.RUN_SUBMIT.name());
    }

    private boolean isPreventiveVerificationPassed(CorrectPreventMeasurePreVeriMTO model) {
        List<PreventMeasureMTO> cvList = model.getPreventMeasureList();
        if (cvList != null) {
            for (PreventMeasureMTO mto : cvList) {
                if (mto.getVerificationResult().equals(VerificationResultEnum.Enum.NOT_PASSED.name())) {
                    return false;
                }
            }
        }
        return true;
    }

    private void returnToCorrected(CorrectPreventMeasurePreVeriMTO model) {
        model.setBusinessState(CpmBusinessStateEnum.Enum.TO_BE_ANALYZED.name());

        List<PreventMeasureMTO> cvList = model.getPreventMeasureList();
        if (cvList != null) {
            for (PreventMeasureMTO mto : cvList) {
                mto.setImprovingProgress(ImprovingProgressEnum.Enum.TO_BE_CORRECTED.name());
                mto.setCompletionDate(null);
                mto.setCompleteSupportMaterial(null);
                mto.setVerificationResult(null);
                mto.setVerificationDate(null);
                mto.setVerificationSupportMaterial(null);
            }
        }
    }

    private void verificationSuccessHandler(CorrectPreventMeasurePreVeriMTO model) {
        setVerifiedStatus(model);
        List<PreventMeasureMTO> cvList = model.getPreventMeasureList();
        if (cvList != null) {
            User user = fabosJsonDao.getById(User.class, UserContext.getUserId());
            for (PreventMeasureMTO mto : cvList) {
                mto.setVerifiedUser(user);
            }
        }
    }
}
