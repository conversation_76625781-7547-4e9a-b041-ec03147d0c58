package cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.mto;

import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.enumration.ImprovingProgressEnum;
import cec.jiutian.bc.materialInspect.domain.publicInspectionTask.mto.UserForInsTaskMTO;
import cec.jiutian.bc.mto.OrgMTO;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/4/29
 * @description TODO
 */
@FabosJson(
        name = "纠正措施"
)
@Table(name = "qms_cpm_corrective_action")
@Entity
@Data
public class CorrectiveActionCreationMTO extends MetaModel {
    @ManyToOne
    @FabosJsonField(
            views = {
                    @View(title = "预防纠正措施", column = "correctPreventMeasureFormNumber", show = false)
            },
            edit = @Edit(title = "预防纠正措施", type = EditType.REFERENCE_TABLE, show = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "correctPreventMeasureFormNumber")
            )
    )
    @JsonIgnoreProperties("correctiveActionCreationMTOList")
    private CorrectPreventMeasureCorrectMTO correctPreventMeasureCorrectMTO;

    @FabosJsonField(
            views = @View(title = "纠正"),
            edit = @Edit(title = "纠正")
    )
    private String correct;

    @FabosJsonField(
            views = @View(title = "责任部门", column = "name"),
            edit = @Edit(title = "责任部门",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType(type = TabTableReferType.SelectShowTypeMTM.LIST)
            )
    )
    @ManyToOne
    @JoinColumn(name = "response_department_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private OrgMTO orgMTO;

    @FabosJsonField(
            views = @View(title = "责任人", column = "name"),
            edit = @Edit(title = "责任人",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType(type = TabTableReferType.SelectShowTypeMTM.LIST)
            )
    )
    @ManyToOne
    @JoinColumn(name = "person_responsible_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private UserForInsTaskMTO userForInsTaskMTO;

    //TODO: 纳期
    @FabosJsonField(
            views = @View(title = "纳期"),
            edit = @Edit(title = "纳期",
                    dateType = @DateType(type = DateType.Type.DATE_TIME),
                    notNull = true
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date deliveryTime;

    @FabosJsonField(
            views = @View(title = "改善进度", show = false),
            edit = @Edit(title = "改善进度",
                    readonly = @Readonly(),
                    type = EditType.CHOICE,
                    show = false,
                    choiceType = @ChoiceType(fetchHandler = ImprovingProgressEnum.class)
            )
    )
    private String improvingProgress;
}
