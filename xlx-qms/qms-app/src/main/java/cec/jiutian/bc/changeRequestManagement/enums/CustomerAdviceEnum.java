package cec.jiutian.bc.changeRequestManagement.enums;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

public class CustomerAdviceEnum  implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum data : Enum.values()) {
            list.add(new VLModel(data.name(), data.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        //同意、不同意、经市场-营销向顾客发起变更申请
        AGREE("同意"),
        DISAGREE("不同意"),
        CUSTOMER_ADVICE("经市场-营销向顾客发起变更申请")
        ;

        private final String value;

    }
}
