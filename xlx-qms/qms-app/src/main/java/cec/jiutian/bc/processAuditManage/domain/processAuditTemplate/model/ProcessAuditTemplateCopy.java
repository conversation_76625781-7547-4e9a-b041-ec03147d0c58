package cec.jiutian.bc.processAuditManage.domain.processAuditTemplate.model;

import cec.jiutian.bc.mto.SpecificationManageMTO;
import cec.jiutian.bc.processAuditManage.domain.processAuditTemplate.enumeration.TemplateTypeEnum;
import cec.jiutian.bc.processAuditManage.domain.processAuditTemplate.handler.ProcessAuditItemManageReferenceHandler;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.ReferenceAddType;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@FabosJson(
        name = "过程审核模板复制",
        orderBy = "ProcessAuditTemplateCopy.createTime desc"
)
@Table(name = "qms_sam_process_audit_template", uniqueConstraints = @UniqueConstraint(columnNames = "templateName"))
@Entity
@Getter
@Setter
public class ProcessAuditTemplateCopy extends MetaModel {
    @FabosJsonField(
            views = @View(title = "编号"),
            edit = @Edit(title = "编号", notNull = true, show = false)
    )
    private String generalCode;

    // 模板名称
    @FabosJsonField(
            views = @View(title = "模板名称"),
            edit = @Edit(title = "模板名称", notNull = true, search = @Search)
    )
    private String templateName;

    //产品型号
    @ManyToOne
    @FabosJsonField(
            views = @View(title = "产品型号", column = "name"),
            edit = @Edit(title = "产品型号",
                    type = EditType.REFERENCE_TABLE,
                    filter = @Filter(value = "validateFlag = 'Y' and specificationCode like '02%'"),
                    referenceTableType = @ReferenceTableType(id = "id", label = "name")
            )
    )
    private SpecificationManageMTO productType;

    //模板类型
    @FabosJsonField(
            views = @View(title = "模板类型"),
            edit = @Edit(title = "模板类型", notNull = true, type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = TemplateTypeEnum.class))
    )
    private String templateType;


    @FabosJsonField(
            views = @View(title = "审核项", column = "auditItemName", type = ViewType.TABLE_VIEW, extraPK = "processAuditItemManageId"),
            edit = @Edit(title = "审核项", type = EditType.TAB_REFER_ADD),
            referenceAddType = @ReferenceAddType(referenceClass = "ProcessAuditItemManage",
                    editable = {"processElements", "minRequires", "department", "materialStorageControl",
                            "preFirstBurnMixing", "firstSintering", "firstCrushingProcess", "washingProcess",
                            "preSecondBurnMixing", "secondSintering", "finalCrushingProcess", "inspection", "packagingSpec"},
                    referenceAddHandler = ProcessAuditItemManageReferenceHandler.class)
    )
    @JoinColumn(name = "process_audit_template_id")
    @OneToMany(cascade = CascadeType.ALL)
    private List<TemplateProcessAuditItem> templateProcessAuditItemList;
}
