package cec.jiutian.bc.qualityExceptionManagement.domain.otherDellTask.model;

import cec.jiutian.bc.qualityExceptionManagement.domain.reworkTask.proxy.ReworkTaskDataProxy;
import cec.jiutian.bc.qualityExceptionManagement.enums.TaskDetailTypeEnum;
import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowBaseOperation;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;

import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "qms_dell_detail")
@Getter
@Setter
@FabosJson(
        name = "处置明细",
        dataProxy = ReworkTaskDataProxy.class,
        power = @Power(add = false, edit = false, delete = false),
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "add",
                        ifExpr = "1 == 1",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "1 == 1",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                )
        }
)
public class TaskDetail extends BaseModel {


    @FabosJsonField(
            views = @View(title = "出/入库单号"),
            edit = @Edit(title = "出/入库单号",
                    notNull = true,
                    search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    @Column(length = 40,nullable = false)
    private String code;

    @FabosJsonField(
            views = @View(title = "出/入库类型"),
            edit = @Edit(title = "出/入库类型",
                    notNull = true,
                    type = EditType.CHOICE,
                    search = @Search,
                    choiceType = @ChoiceType(fetchHandler = TaskDetailTypeEnum.class)
            )
    )
    @Column(length = 40,nullable = false)
    private String Type;

    @FabosJsonField(
            views = @View(title = "数量"),
            edit = @Edit(title = "数量", type = EditType.NUMBER,
                    inputType = @InputType(type = "number"),
                    numberType = @NumberType(min = 0, max = Integer.MAX_VALUE, precision = 2)
            )
    )
    private BigDecimal quantity;

    @FabosJsonField(
            views = @View(title = "重量"),
            edit = @Edit(title = "重量", type = EditType.NUMBER,
                    inputType = @InputType(type = "number"),
                    numberType = @NumberType(min = 0, max = Integer.MAX_VALUE, precision = 2)
            )
    )
    @Column(length = 20)
    private Double weight;

    @FabosJsonField(
            views = @View(title = "时间",type = ViewType.DATE_TIME),
            edit = @Edit(title = "时间",
                    show = false,
                    dateType = @DateType(type = DateType.Type.DATE_TIME))
    )
    @CreationTimestamp
    private Date time;

}
