package cec.jiutian.bc.productionStartupRelease.domain.productionStartupRelease.mto;

import cec.jiutian.bc.materialInspect.domain.publicInspectionTask.mto.UserForInsTaskMTO;
import cec.jiutian.bc.mto.FactoryArea;
import cec.jiutian.bc.mto.OrgMTO;
import cec.jiutian.bc.mto.ProductProcessMTO;
import cec.jiutian.bc.productionStartupRelease.domain.productionStartupRelease.handler.FactoryDynamicHandler;
import cec.jiutian.bc.productionStartupRelease.domain.productionStartupRelease.handler.LineDynamicHandler;
import cec.jiutian.bc.productionStartupRelease.domain.productionStartupRelease.handler.PLSRReportAddMTOGenerateHandler;
import cec.jiutian.bc.productionStartupRelease.enums.*;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.*;
import cec.jiutian.view.config.Comment;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@Entity
@Table(name = "qms_plsr", indexes = {
        @Index(name = "general_code", columnList = "general_code", unique = true)
})
@Setter
@Getter
@FabosJson(
        name = "生产放行",
        orderBy = "createTime desc",
        power = @Power(add = false, importable = false, print = false)

)
public class PLSRReportAddMTO extends MetaModel {

    @Comment("业务单据编码编号")
    @Column(unique = true, nullable = false, length = 40)
    @FabosJsonField(
            views = @View(title = "生产放行单号", index = 0),
            edit = @Edit(title = "生产放行单号", readonly = @Readonly(add = false, edit = false),
                    notNull = true, search = @Search(vague = true), inputType = @InputType(length = 40), index = 0)
            ,
            dynamicField = @DynamicField(
                    dependFiled = @DependFiled(buttonName = "生成", changeBy = {"id"},
                            dynamicHandler = PLSRReportAddMTOGenerateHandler.class)
            )
    )
    private String generalCode;

    @ManyToOne
    @JoinColumn(name = "quality_trace_form_id")
    @FabosJsonField(
            views = @View(title = "品质跟踪单", column = "generalCode"),
            edit = @Edit(title = "品质跟踪单",
                    filter = @Filter("businessStatus = 'APPROVED'"),
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode")
            )
    )
    private QualityTraceForm qualityTraceForm;

    @ManyToOne
    @JoinColumn(name = "workshop_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @FabosJsonField(
            views = @View(title = "选择车间", show = false, column = "factoryAreaName"),
            edit = @Edit(title = "选择车间",
                    notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "factoryAreaName"),
                    filter = @Filter(value = "factoryAreaTypeCode = '02'")
            )
    )
    private FactoryArea workshop;

    @FabosJsonField(
            views = @View(title = "工厂id", show = false),
            edit = @Edit(title = "工厂id",show = false),
            dynamicField = @DynamicField(passive = true)
    )
    @Column(length = 128)
    private Long factoryId;

    @FabosJsonField(
            views = @View(title = "车间"),
            edit = @Edit(title = "车间",show = false),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "workshop", dynamicHandler = FactoryDynamicHandler.class))
    )
    @Column(length = 64)
    private String workshopName;

    @ManyToOne
    @JoinColumn(name = "line_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @FabosJsonField(
            views = @View(title = "产线", show = false, column = "factoryAreaName"),
            edit = @Edit(title = "产线",
                    notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "factoryAreaName"),
                    filter = @Filter(value = "factoryAreaTypeCode = '03'"),
                    queryCondition = "{ \"pid\": \"${workshop.id}\"}",
                    dependFieldDisplay = @DependFieldDisplay(enableOrDisable = "workshopName == '' || workshopName == null")
            ),
            referenceAddType = @ReferenceAddType(referenceClass = "FactoryArea", queryCondition = "{ \"pid\": \"${workshop.id}\"}")
    )
    private FactoryArea line;

    @FabosJsonField(
            views = @View(title = "产线"),
            edit = @Edit(title = "产线",show = false),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "line", dynamicHandler = LineDynamicHandler.class))
    )
    @Column(length = 64)
    private String lineName;

    @FabosJsonField(
            views = @View(title = "产品类型"),
            edit = @Edit(title = "产品类型",
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = {ProductionTypeEnum.class}))
    )
    @Column(nullable = false, length = 32)
    private String productionType;

    @FabosJsonField(
            views = @View(title = "放行等级"),
            edit = @Edit(title = "放行等级",
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ReleaseLevelEnum.class)
            )
    )
    @Column(nullable = false, length = 32)
    private String releaseLevel;

    @ManyToOne
    @JoinColumn(name = "org_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @FabosJsonField(
            views = @View(title = "主导部门", column = "name", show = false),
            edit = @Edit(title = "主导部门",
                    notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false
            )
    )
    private OrgMTO orgMTO;

    @FabosJsonField(
            views = @View(title = "主导部门"),
            edit = @Edit(title = "主导部门",
                    search = @Search(vague = true),
                    show = false
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "orgMTO", beFilledBy = "name"))
    )
    @Column(length = 64)
    private String department;

    @ManyToOne
    @JoinColumn(name = "user_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @FabosJsonField(
            views = @View(title = "评审主导人", show = false, column = "name"),
            edit = @Edit(title = "评审主导人",
                    notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType()
            )
    )
    private UserForInsTaskMTO user;

    @FabosJsonField(
            views = @View(title = "评审主导人"),
            edit = @Edit(title = "评审主导人",
                    show = false,
                    readonly = @Readonly(add = true, edit = true),
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "user", beFilledBy = "name"))
    )
    @Column(length = 64)
    private String userName;

    @FabosJsonField(
            views = @View(title = "生产线类型"),
            edit = @Edit(title = "生产线类型",
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = {LineTypeEnum.class}),
                    search = @Search()
            )
    )
    @Column(nullable = false, length = 32)
    private String lineType;

    @FabosJsonField(
            views = @View(title = "产品阶段"),
            edit = @Edit(title = "产品阶段",
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = {ProductionPeriodEnum.class}),
                    search = @Search()
            )
    )
    @Column(nullable = false, length = 32)
    private String productionPeriod;

    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(name = "qms_plsr_process",
            inverseForeignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT),
            joinColumns = @JoinColumn(name = "plsr_id"),
            inverseJoinColumns = @JoinColumn(name = "process_id"))
    @FabosJsonField(
            views = @View(title = "放行工序段", column = "id", type = ViewType.TABLE_VIEW),
            edit = @Edit(title = "放行工序段",
                    notNull = true,
                    tabTableReferType = @TabTableReferType(type = TabTableReferType.SelectShowTypeMTM.LIST),
                    type = EditType.TAB_TABLE_REFER
            )
    )
    private List<ProductProcessMTO> process;

    @FabosJsonField(
            views = @View(title = "切换类型"),
            edit = @Edit(title = "切换类型",
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = {SwitchTypeEnum.class})
            )
    )
    @Column(nullable = false, length = 32)
    private String switchType;

    @FabosJsonField(
            views = @View(title = "生产出货判定"),
            edit = @Edit(title = "生产出货判定",
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = {ProductJudgeEnum.class})
            )
    )
    @Column(nullable = false, length = 32)
    private String productJudge;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",
                    show = false,
                    defaultVal = "EDIT",
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = {StatusEnum.class})
            ),
            dynamicField = @DynamicField(passive = true)
    )
    @Column(nullable = false, length = 16)
    private String status;

    @PrePersist
    public void prePersist() {
        if (StringUtils.isEmpty(this.status)) {
            this.status = StatusEnum.Enum.EDIT.name();
        }
    }

}

