package cec.jiutian.bc.clientReviewManagement.domain.problemImprovement.handler;

import cec.jiutian.bc.clientComplaintManagement.enumeration.ReviewProblemImproveStatusEnum;
import cec.jiutian.bc.clientReviewManagement.domain.problemImprovement.model.MyReviewProblemImprovement;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/06/11
 * @description
 */
@Component
public class MyReviewProblemImprovementCompleteHandler implements OperationHandler<MyReviewProblemImprovement, Void> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Transactional
    @Override
    public String exec(List<MyReviewProblemImprovement> data, Void modelObject, String[] param) {
        if (CollectionUtils.isEmpty(data) || data.get(0) == null) {
            return "alert(行数据选择异常)";
        }
        MyReviewProblemImprovement model = data.get(0);
        model.setBusinessStatus(ReviewProblemImproveStatusEnum.Enum.VERIFYING.name());

        fabosJsonDao.mergeAndFlush(model);
        return null;
    }
}
