package cec.jiutian.bc.inventoryInspection.domain.inspectionTask.proxy;

import cec.jiutian.bc.basicData.enumeration.ApplyRangeEnum;
import cec.jiutian.bc.basicData.service.AQLSamplingTables;
import cec.jiutian.bc.basicData.service.AQSamplingDataTableService;
import cec.jiutian.bc.basicData.service.BasicDataService;
import cec.jiutian.bc.deliveryInspection.enumeration.TaskBuildTypeEnum;
import cec.jiutian.bc.inventoryInspection.domain.inspectionTask.model.InventoryInspectionStandardDetail;
import cec.jiutian.bc.inventoryInspection.domain.inspectionTask.model.InventoryInspectionStandardItemTarget;
import cec.jiutian.bc.inventoryInspection.domain.inspectionTask.model.InventoryInspectionTask;
import cec.jiutian.bc.mto.OrgMTO;
import cec.jiutian.bc.modeler.domain.samplingPlan.model.SamplingPlan;
import cec.jiutian.bc.modeler.enumration.*;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2025/3/10
 * @description TODO
 */
@Component
@Slf4j
public class InventoryInspectionTaskProxy implements DataProxy<InventoryInspectionTask> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private BasicDataService basicDataService;

    @Resource
    private AQLSamplingTables aqlSamplingTables;

    @Resource
    private AQSamplingDataTableService aqSamplingDataTableService;

    @Override
    public void beforeAdd(InventoryInspectionTask inventoryInspectionTask) {
        inventoryInspectionTask.setCurrentState(OrderCurrentStateEnum.Enum.EDIT.name());
        inventoryInspectionTask.setBusinessState(TaskBusinessStateEnum.Enum.BE_INSPECT.name());
        inventoryInspectionTask.setInspectionType(ApplyRangeEnum.Enum.inventoryInspect.name());
        inventoryInspectionTask.setBuildType(TaskBuildTypeEnum.Enum.MANUAL.name());
        setDetailData(inventoryInspectionTask);
    }

    private void setDetailData(InventoryInspectionTask inventoryInspectionTask) {
        if (CollectionUtils.isNotEmpty(inventoryInspectionTask.getInspectionTaskDetailList())) {
            AtomicInteger index = new AtomicInteger(1);
            inventoryInspectionTask.getInspectionTaskDetailList().forEach(taskDetail -> {
                taskDetail.setGeneralCode(inventoryInspectionTask.getGeneralCode() + "_" + String.format("%03d", index.get()));
                index.getAndIncrement();
            });
        }

        if (CollectionUtils.isNotEmpty(inventoryInspectionTask.getStandardDetailList())) {
            calculateStandardValueAc(inventoryInspectionTask);
        }
    }

    @Override
    public void afterSingleFetch(Map<String, Object> map) {
        if (map.get("orgMTO") == null) {
            String materialId = String.valueOf(map.get("inspectionDepartment"));
            OrgMTO orgMTO = fabosJsonDao.findById(OrgMTO.class, materialId);
            if (orgMTO != null) {
                map.put("orgMTO", orgMTO);
                map.put("orgMTO_name", orgMTO.getName());
            }else {
                throw new FabosJsonApiErrorTip("未查到部门："+String.valueOf(map.get("inspectionDepartment"))+"源数据，请确认");
            }
        }
    }

    private void calculateStandardValueAc(InventoryInspectionTask inventoryInspectionTask) {
        //样品数量 优先于 来料数量
        Double quantity = inventoryInspectionTask.getSampleQuantity();
        if (!isQuantityLegal(quantity)) {
            Double arrivalQuantity = inventoryInspectionTask.getArrivalQuantity();
            if (!isQuantityLegal(arrivalQuantity)) {
                return;
            }
            quantity = arrivalQuantity;
        }

        String lotSize = aqlSamplingTables.getLotSize(quantity);

        //检验项目明细
        List<InventoryInspectionStandardDetail> standardDetailList = inventoryInspectionTask.getStandardDetailList();
        for (InventoryInspectionStandardDetail insDetail : standardDetailList) {
            //抽样方案
            SamplingPlan samplingPlan = fabosJsonDao.getById(SamplingPlan.class, insDetail.getSamplingPlan().getId());
            String samplingStandard = samplingPlan.getSamplingStandard();
            if (!samplingStandard.equals(SamplingStandardEnum.Enum.AQLSampling.name())) {
                continue;
            }

            //AQL_S-2,AQL_Ⅲ.etc
            String levelValue = getLevelValueFromSamplingPlan(samplingPlan);
            String sampleSizeCode = aqlSamplingTables.getSampleSizeCode(lotSize, levelValue);
            String aqlValue = samplingPlan.getAqlValue();
            String acValue = aqSamplingDataTableService.getAcValue(sampleSizeCode, aqlValue);
            log.info("In the sampling plan {}, the sampleSizeCode: {}, the aqlValue: {}, the acValue: {}.",
                    samplingPlan.getName(), sampleSizeCode, aqlValue, acValue);
            setAcValueToStandardDetail(insDetail, acValue, samplingPlan);
        }
    }

    private boolean isQuantityLegal(Double quantity) {
        if (quantity == null || quantity < 2) {
            return false;
        }
        return true;
    }

    private String getLevelValueFromSamplingPlan(SamplingPlan samplingPlan) {
        String AQLInspectionType = samplingPlan.getAQLInspectionType();
        String prefixInspectionLevel = "AQL_";
        if (AQLInspectionType.equals(AQLInspectionTypeEnum.Enum.General.name())) {
            return prefixInspectionLevel + GeneralLevelValueEnum.Enum.valueOf(samplingPlan.getGeneralLevelValue()).getValue();
        } else if (AQLInspectionType.equals(AQLInspectionTypeEnum.Enum.Special.name())) {
            return prefixInspectionLevel + SpecialLevelValue.Enum.valueOf(samplingPlan.getSpecialLevelValue()).getValue();
        }
        log.error("AQLInspectionType error, the AQLInspectionType is " + AQLInspectionType);
        return "";
    }

    private void setAcValueToStandardDetail(InventoryInspectionStandardDetail insDetail, String acValue, SamplingPlan samplingPlan) {
        if (acValue == null) {
            return;
        }
        boolean modificationCompleted = false;
        List<InventoryInspectionStandardItemTarget> insList = insDetail.getInventoryInspectionStandardItemTargetList();
        if (insList != null) {
            for (InventoryInspectionStandardItemTarget insItem : insList) {
                if (insItem.getInspectionValueType().equals(InspectionValueTypeEnum.Enum.number.name())) {
                    insItem.setStandardValue(acValue);
                    modificationCompleted = true;
                }
            }
        }
        if (!modificationCompleted) {
            samplingPlan.setAcValue(Double.valueOf(acValue));
        }
    }
}
