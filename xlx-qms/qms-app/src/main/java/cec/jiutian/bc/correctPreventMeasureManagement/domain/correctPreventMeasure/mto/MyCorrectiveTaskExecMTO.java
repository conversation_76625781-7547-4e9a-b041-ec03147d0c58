package cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.mto;

import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.enumration.ProgressEnum;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.model.MyCorrectiveAction;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.ReferenceGenerateType;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.ChoiceType;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@FabosJson(
        name = "执行",
        orderBy = "MyCorrectiveTaskExecMTO.createTime desc"
)
@Table(name = "qms_cpm_correct_prevent_measure")
@Entity
@Getter
@Setter
public class MyCorrectiveTaskExecMTO extends MetaModel {
    @FabosJsonField(
            views = @View(title = "纠正状态", show = false),
            edit = @Edit(title = "纠正状态",
                    readonly = @Readonly(),
                    type = EditType.CHOICE,
                    show = false,
                    choiceType = @ChoiceType(fetchHandler = ProgressEnum.class)
            )
    )
    private String correctiveState;

    @FabosJsonField(
            edit = @Edit(title = "纠正措施", type = EditType.TAB_REFERENCE_GENERATE),
            views = @View(title = "纠正措施", type = ViewType.TABLE_VIEW),
            referenceGenerateType = @ReferenceGenerateType(editable = {"improvingProgress","completionDate","completeSupportMaterial"})
    )
    @JoinColumn(name = "qms_cpm_correct_prevent_measure_id")
    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
    @OrderBy
    private List<MyCorrectiveAction> correctiveActionList;

}
