package cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.handler;

import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.enumration.ImprovingProgressEnum;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.enumration.ProgressEnum;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.model.CorrectPreventMeasure;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.mto.CorrectPreventMeasureCorrectMTO;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.mto.CorrectiveActionCreationMTO;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/28
 * @description TODO
 */
@Component
public class CorrectPreventMeasureCorrectHandler implements OperationHandler<CorrectPreventMeasure, CorrectPreventMeasureCorrectMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<CorrectPreventMeasure> data, CorrectPreventMeasureCorrectMTO modelObject, String[] param) {
        /*if (modelObject != null) {
            CorrectPreventMeasure model = data.get(0);
            assembleMTOData(modelObject);
            BeanUtil.copyProperties(modelObject, model, "createBy", "createTime");
            model.setCorrectiveState(ProgressEnum.Enum.RUN_SUBMIT.name());
            fabosJsonDao.mergeAndFlush(model);
        }*/
        return "alert(操作成功)";
    }

    @Override
    public CorrectPreventMeasureCorrectMTO fabosJsonFormValue(List<CorrectPreventMeasure> data, CorrectPreventMeasureCorrectMTO fabosJsonForm, String[] param) {
        CorrectPreventMeasure model = data.get(0);
        BeanUtil.copyProperties(model, fabosJsonForm);
        return fabosJsonForm;
    }

    private void assembleMTOData(CorrectPreventMeasureCorrectMTO mto) {
        List<CorrectiveActionCreationMTO> correctiveActionList = mto.getCorrectiveActionList();
        if (correctiveActionList != null) {
            for (CorrectiveActionCreationMTO cacMTO : correctiveActionList) {
                cacMTO.setImprovingProgress(ImprovingProgressEnum.Enum.TO_BE_CORRECTED.name());
            }
        }
    }
}
