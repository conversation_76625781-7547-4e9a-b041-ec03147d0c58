package cec.jiutian.bc.processInspect.domain.processSampleTask.handler;

import cec.jiutian.bc.modeler.enumration.TaskBusinessStateEnum;
import cec.jiutian.bc.processInspect.domain.processInspectionTask.model.ProcessInspectionTask;
import cec.jiutian.bc.processInspect.domain.processInspectionTask.model.ProcessInspectionTaskDetail;
import cec.jiutian.bc.processInspect.domain.processSampleTask.model.ProcessSampleTask;
import cec.jiutian.bc.processInspect.domain.processSampleTask.model.ProcessSampleTaskDetail;
import cec.jiutian.bc.processInspect.domain.processSampleTask.model.ProcessSampleTaskResult;
import cec.jiutian.common.util.BigDecimalUtils;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 过程取样任务 完成检验
 * 1、取样任务：更新状态，更新检验项目结果
 * 2、检验任务：更新对应检验任务的项目结果和实际批次；
 * 3、判断检验任务所有项目均有结果，更新任务检验状态
 *
 */
@Component
public class IPQCSampleTaskCompleteOperationHandler implements OperationHandler<ProcessSampleTask, ProcessSampleTaskResult> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<ProcessSampleTask> data, ProcessSampleTaskResult modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            ProcessSampleTask entity = data.get(0);

            // 2、获取并更新检验任务
            ProcessInspectionTask inspectionTask = fabosJsonDao.findById(ProcessInspectionTask.class, entity.getInspectionTaskId());
            inspectionTask.setTotalSampleQuantity(BigDecimalUtils.add(null == inspectionTask.getTotalSampleQuantity() ? 0D : inspectionTask.getTotalSampleQuantity(), modelObject.getSampleQuantity()));
            inspectionTask.setActualLotSerialId(entity.getActualLotSerialId());
            // 实际取样批次流水 todo
            Integer seq = Integer.valueOf(entity.getActualLotSerialId().substring(entity.getActualLotSerialId().length() - 3));
            inspectionTask.setSequenceNumber(seq);

            for (ProcessSampleTaskDetail resultDetail : modelObject.getDetails()) {
                if (null == resultDetail.getInspectionResult()) {
                    throw new FabosJsonApiErrorTip("检验项目" + resultDetail.getItemName() + "未填写检验结果，请检查");
                }

                inspectionTask.getDetails().stream().filter(x -> x.getId().equals(resultDetail.getInspectionTaskDetailId())).findFirst()
                        .ifPresent(p -> p.setInspectionResult(resultDetail.getInspectionResult()));
            }

            // 1、更新取样任务
            entity.setBusinessState(TaskBusinessStateEnum.Enum.INSPECT_FINISH.name());
            entity.setDetails(modelObject.getDetails());
            fabosJsonDao.mergeAndFlush(entity);

            // 3、判断检验任务详情是否都有结果
            boolean inspectFinishFlag = true;
            for (ProcessInspectionTaskDetail inspectionTaskDetail : inspectionTask.getDetails()) {
                if (null == inspectionTaskDetail.getInspectionResult()) {
                    inspectFinishFlag = false;
                    break;
                }
            }
            if (inspectFinishFlag) {
                inspectionTask.setBusinessState(TaskBusinessStateEnum.Enum.INSPECT_FINISH.name());
            }
            fabosJsonDao.mergeAndFlush(inspectionTask);

        }
        return "msg.success('操作成功')";
    }
}
