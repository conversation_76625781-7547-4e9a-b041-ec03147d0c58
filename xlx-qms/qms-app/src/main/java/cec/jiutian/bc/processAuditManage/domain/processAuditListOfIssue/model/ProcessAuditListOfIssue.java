package cec.jiutian.bc.processAuditManage.domain.processAuditListOfIssue.model;

import cec.jiutian.bc.materialInspect.domain.publicInspectionTask.mto.UserForInsTaskMTO;
import cec.jiutian.bc.mto.OrgMTO;
import cec.jiutian.bc.processAuditManage.domain.processAuditItemManage.model.ProcessAuditItemManage;
import cec.jiutian.bc.processAuditManage.domain.processAuditListOfIssue.enumration.PaListOfIssueStatusEnum;
import cec.jiutian.bc.processAuditManage.domain.processAuditListOfIssue.enumration.PamAnalysisMethodEnum;
import cec.jiutian.bc.processAuditManage.domain.processAuditListOfIssue.enumration.SeverityTypeEnum;
import cec.jiutian.bc.processAuditManage.domain.processAuditListOfIssue.handler.ProcessAuditListOfIssueDealHandler;
import cec.jiutian.bc.processAuditManage.domain.processAuditListOfIssue.handler.ProcessAuditListOfIssueDynamicHandler;
import cec.jiutian.bc.processAuditManage.domain.processAuditListOfIssue.handler.ProcessAuditListOfIssueHandler;
import cec.jiutian.bc.processAuditManage.domain.processAuditListOfIssue.mto.ProcessAuditListOfIssueDealMTO;
import cec.jiutian.bc.processAuditManage.domain.processAuditListOfIssue.proxy.ProcessAuditListOfIssueDataProxy;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.*;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.RowBaseOperation;
import cec.jiutian.view.type.RowOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/5/15
 * @description TODO
 */
@FabosJson(
        name = "过程审核问题清单",
        orderBy = "ProcessAuditListOfIssue.createTime desc",
        dataProxy = ProcessAuditListOfIssueDataProxy.class,
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "businessStatus != 'NEW'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "businessStatus != 'NEW'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                )
        },
        rowOperation = {
                @RowOperation(
                        title = "下发",
                        code = "ProcessAuditListOfIssue@ISSUED",
                        operationHandler = ProcessAuditListOfIssueHandler.class,
                        mode = RowOperation.Mode.HEADER,
                        callHint = "该操作不可撤回，确定提交吗？",
                        ifExpr = "selectedItems[0].businessStatus != 'NEW'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "ProcessAuditListOfIssue@ISSUED"
                        )
                ),
                @RowOperation(
                        title = "处理",
                        code = "ProcessAuditListOfIssue@DEAL",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = ProcessAuditListOfIssueDealMTO.class,
                        operationHandler = ProcessAuditListOfIssueDealHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "ProcessAuditListOfIssue@DEAL"
                        ),
                        ifExpr = "selectedItems[0].businessStatus != 'RELEASE'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
        }
)
@Table(name = "qms_pam_list_of_issue",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"listOfIssueFormNumber"})
        }
)
@Entity
@Getter
@Setter
public class ProcessAuditListOfIssue extends MetaModel {

    @FabosJsonField(
            views = @View(title = "问题清单编号"),
            edit = @Edit(title = "问题清单编号", readonly = @Readonly(add = false),
                    search = @Search(vague = true), notNull = true),
            dynamicField = @DynamicField(dependFiled = @DependFiled(buttonName = "生成", changeBy = "id",
                    dynamicHandler = ProcessAuditListOfIssueDynamicHandler.class))
    )
    private String listOfIssueFormNumber;

    @FabosJsonField(
            views = @View(title = "审核项", column = "auditItemName"),
            edit = @Edit(title = "审核项",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "auditItemName")
            )
    )
    @ManyToOne
    @JoinColumn(foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private ProcessAuditItemManage auditItemManage;


    /**
     * 同一审核实施计划下的多个审核项，会生成一个或多个问题清单
     * 当所属同一审核实施计划id的问题清单都审核通过，则handler中设置审核实施计划的状态为待审核
     */
    @FabosJsonField(
            views = @View(title = "审核实施计划id", show = false),
            edit = @Edit(title = "审核实施计划id", show = false)
    )
    private String processAuditImplementPlanId;

    @FabosJsonField(
            views = @View(title = "问题描述"),
            edit = @Edit(title = "问题描述",
                    type = EditType.TEXTAREA
            )
    )
    private String problemDescription;

    @Transient
    @FabosJsonField(
            views = @View(title = "选择责任人", show = false, column = "name"),
            edit = @Edit(title = "选择责任人",
                    notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType()
            )
    )
    private UserForInsTaskMTO responsiblePersonMTO;

    @FabosJsonField(
            views = @View(title = "责任人id", show = false),
            edit = @Edit(title = "责任人id",
                    show = false,
                    notNull = true,
                    inputType = @InputType(length = 30)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "responsiblePersonMTO",
                    beFilledBy = "id"))
    )
    private String responsiblePersonId;

    @FabosJsonField(
            views = @View(title = "责任人"),
            edit = @Edit(title = "责任人",
                    search = @Search(vague = true),
                    notNull = true,
                    readonly = @Readonly(),
                    inputType = @InputType(length = 30)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "responsiblePersonMTO",
                    beFilledBy = "name"))
    )
    @Column(nullable = false, length = 30)
    private String responsiblePerson;

    @Transient
    @FabosJsonField(
            views = @View(title = "选择主要责任部门", show = false, column = "name"),
            edit = @Edit(title = "选择主要责任部门",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType()
            )
    )
    private OrgMTO orgMTO;

    @FabosJsonField(
            views = @View(title = "主要责任部门ID", show = false),
            edit = @Edit(title = "主要责任部门ID",
                    show = false
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "orgMTO", beFilledBy = "id"))
    )
    @Column(name = "department_id", length = 50)
    private String mainResponseDepartmentId;

    @FabosJsonField(
            views = @View(title = "主要责任部门"),
            edit = @Edit(title = "主要责任部门",
                    search = @Search(vague = true),
                    readonly = @Readonly()
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "orgMTO", beFilledBy = "name"))
    )
    @Column(name = "department", length = 50)
    private String mainResponseDepartmentName;

    @FabosJsonField(
            views = @View(title = "严重程度"),
            edit = @Edit(
                    title = "严重程度",
                    search = @Search(vague = true),
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = SeverityTypeEnum.class))
    )
    private String severity;

    @FabosJsonField(
            views = @View(title = "分析方法"),
            edit = @Edit(title = "分析方法",
                    type = EditType.CHOICE,
                    search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = PamAnalysisMethodEnum.class))
    )
    private String analysisMethod;

    @Transient
    @FabosJsonField(
            views = @View(title = "选择验证人", show = false, column = "name"),
            edit = @Edit(title = "选择验证人",
                    notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType()
            )
    )
    private UserForInsTaskMTO selectVerifier;

    @FabosJsonField(
            views = @View(title = "验证人id", show = false),
            edit = @Edit(title = "验证人id",
                    show = false,
                    notNull = true,
                    inputType = @InputType(length = 30)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "selectVerifier",
                    beFilledBy = "id"))
    )
    private String selectVerifierId;

    @FabosJsonField(
            views = @View(title = "验证人"),
            edit = @Edit(title = "验证人",
                    search = @Search(vague = true),
                    notNull = true,
                    readonly = @Readonly(),
                    inputType = @InputType(length = 30)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "selectVerifier",
                    beFilledBy = "name"))
    )
    @Column(nullable = false, length = 30)
    private String selectVerifierName;

    @FabosJsonField(
            views = @View(title = "确定措施纳期"),
            edit = @Edit(title = "确定措施纳期",
                    dateType = @DateType(type = DateType.Type.DATE_TIME),
                    notNull = true
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date determineMeasureDeliveryTime;

    @FabosJsonField(
            views = @View(title = "完成纳期"),
            edit = @Edit(title = "完成纳期",
                    dateType = @DateType(type = DateType.Type.DATE_TIME),
                    notNull = true
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date completeDeliveryTime;

    @FabosJsonField(
            views = @View(title = "附件", show = false),
            edit = @Edit(title = "附件", type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType)
    )
    private String attachments;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",
                    show = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = PaListOfIssueStatusEnum.class)
            )
    )
    private String businessStatus;

    @PrePersist
    public void onCreate() {
        if (this.businessStatus == null) {
            this.setBusinessStatus(PaListOfIssueStatusEnum.Enum.NEW.name());
        }
    }
}
