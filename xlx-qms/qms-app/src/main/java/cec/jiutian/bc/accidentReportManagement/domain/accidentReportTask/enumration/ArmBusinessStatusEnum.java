package cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.enumration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/29
 * @description
 */
public class ArmBusinessStatusEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum data : Enum.values()) {
            list.add(new VLModel(data.name(), data.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        TO_BE_CONFIRMED("待确认"),
        TO_BE_ANALYZED("待分析"),
        UNDER_DISPOSAL("处置中"),
        COMPLETED("已完成"),
        ;

        private final String value;

    }
}
