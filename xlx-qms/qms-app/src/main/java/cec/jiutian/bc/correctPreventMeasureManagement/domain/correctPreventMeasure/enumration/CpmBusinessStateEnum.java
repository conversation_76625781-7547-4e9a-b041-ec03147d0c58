package cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.enumration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/28
 * @description TODO
 */
public class CpmBusinessStateEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum data : Enum.values()) {
            list.add(new VLModel(data.name(), data.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        TO_BE_RELEASED("待发布"),
        TO_BE_CORRECTED("待纠正"),

        TO_BE_ANALYZED("待分析"),
        IN_PROGRESS("执行中"),
        TO_BE_VERIFIED("待验证"),

        COMPLETED("待关闭"),
        CLOSED("关闭"),
        ;

        private final String value;

    }
}
