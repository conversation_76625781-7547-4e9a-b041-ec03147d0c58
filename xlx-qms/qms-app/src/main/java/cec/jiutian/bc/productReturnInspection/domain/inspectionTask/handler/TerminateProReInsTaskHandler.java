package cec.jiutian.bc.productReturnInspection.domain.inspectionTask.handler;

import cec.jiutian.bc.materialInspect.domain.samplingTask.model.SamplingTask;
import cec.jiutian.bc.modeler.enumration.TaskBusinessStateEnum;
import cec.jiutian.bc.productReturnInspection.domain.inspectionTask.model.ProductReturnInspectionTask;
import cec.jiutian.bc.productReturnInspection.domain.inspectionTask.model.ProductReturnTerminationInspectionTask;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/15
 * @description TODO
 */
@Component
public class TerminateProReInsTaskHandler implements OperationHandler<ProductReturnInspectionTask, ProductReturnTerminationInspectionTask> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    @Transactional
    public String exec(List<ProductReturnInspectionTask> data, ProductReturnTerminationInspectionTask modelObject, String[] param) {
        if (modelObject != null) {
            ProductReturnInspectionTask inspectionTask = data.get(0);
            inspectionTask.setInspectionResult(modelObject.getInspectionResult());
            inspectionTask.setUnqualifiedLevel(modelObject.getUnqualifiedLevel());
            inspectionTask.setCurrentState(OrderCurrentStateEnum.Enum.END.name());

            SamplingTask condition = new SamplingTask();
            condition.setInspectionTaskCode(inspectionTask.getGeneralCode());
            List<SamplingTask> samplingTaskList = fabosJsonDao.select(condition);
            if (CollectionUtils.isNotEmpty(samplingTaskList)) {
                samplingTaskList.forEach(samplingTask -> {
                    samplingTask.setBusinessState(TaskBusinessStateEnum.Enum.TERMINATION.name());
                    fabosJsonDao.mergeAndFlush(samplingTask);
                });
            }

            fabosJsonDao.mergeAndFlush(inspectionTask);
        }
        return "alert(操作成功)";
    }
}
