package cec.jiutian.bc.questionPaperManagement.remote.feign;

import cec.jiutian.api.remoteCallLog.dto.RemoteCallResult;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.service.CorrectPreventMeasureService;
import cec.jiutian.bc.materialInspect.service.MaterialInspectService;
import cec.jiutian.bc.modeler.vo.CorrectPreventMeasureVO;
import cec.jiutian.bc.modeler.vo.QuestionPaperVO;
import cec.jiutian.bc.modeler.vo.Report8DAddVO;
import cec.jiutian.bc.questionPaperManagement.domain.questionPaper.QuestionPaperService;
import cec.jiutian.bc.report8D.domain.report8D.service.Report8DService;
import cec.jiutian.core.frame.constant.FabosJsonRestPath;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/3/17
 * @description TODO
 */
@RestController
@RequestMapping("/fabos-qms-app"+ FabosJsonRestPath.FABOS_REMOTE_API)
public class QuestionFeign {

    @Resource
    private QuestionPaperService  questionPaperService;
    @Resource
    private CorrectPreventMeasureService correctPreventMeasureService;
    @Resource
    private Report8DService report8DService;

    @PostMapping("/createQuestionPaper")
    public RemoteCallResult createQuestionPaper(@RequestBody QuestionPaperVO params) {
        return RemoteCallResult.success(questionPaperService.createQuestionPaper(params));
    }

    @PostMapping("/createQuestionCPA")
    public RemoteCallResult createQuestionCPA(@RequestBody CorrectPreventMeasureVO params) {
        return RemoteCallResult.success(correctPreventMeasureService.create(params));
    }

    @PostMapping("/createReport8D")
    public RemoteCallResult createReport8D(@RequestBody Report8DAddVO params) {
        return RemoteCallResult.success(report8DService.createReport8D(params));
    }


}
