package cec.jiutian.bc.productionStartupRelease.domain.productionStartupRelease.handler;

import cec.jiutian.bc.productionStartupRelease.enums.StatusEnum;
import cec.jiutian.bc.productionStartupRelease.domain.productionStartupRelease.model.PLSRReport;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Component

public class PubulishOperationHandler implements OperationHandler<PLSRReport, PLSRReport> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    @Transactional
    public String exec(List<PLSRReport> data, PLSRReport modelObject, String[] param) {

        if (CollectionUtils.isEmpty(data)) {
            throw new ServiceException("选择数据为空");
        }

        PLSRReport plsrReport = data.get(0);
        if (CollectionUtils.isEmpty(plsrReport.getResults())) {
            throw new ServiceException("请录入结果评审");
        }

        plsrReport.setStatus(StatusEnum.Enum.WAIT_APPROVE.name());

        fabosJsonDao.mergeAndFlush(plsrReport);
        return "";
    }


}
