package cec.jiutian.bc.inventoryInspection.domain.inspectionTask.handler;

import cec.jiutian.bc.inventoryInspection.domain.inspectionRequest.model.InventoryInspectionRequest;
import cec.jiutian.bc.inventoryInspection.domain.inspectionTask.model.InventoryInspectionTask;
import cec.jiutian.bc.inventoryInspection.domain.inspectionTask.model.InventoryInspectionTaskDetail;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/3/10
 * @description TODO
 */
@Component
public class InvRequestDynamicHandler implements DependFiled.DynamicHandler<InventoryInspectionTask> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public Map<String, Object> handle(InventoryInspectionTask incomingInspectionTask) {
        Map<String, Object> result = new HashMap<>();
        List<InventoryInspectionTaskDetail> detailList = new ArrayList<>();
        if (incomingInspectionTask.getInspectionRequest() != null) {
            InventoryInspectionRequest inspectionRequest = fabosJsonDao.findById(InventoryInspectionRequest.class, incomingInspectionTask.getInspectionRequest().getId());
            if (CollectionUtils.isNotEmpty(inspectionRequest.getDetails())) {
                inspectionRequest.getDetails().forEach(inspectionRequestDetail -> {
                    InventoryInspectionTaskDetail inspectionTaskDetail = new InventoryInspectionTaskDetail();
                    inspectionTaskDetail.setInventoryId(inspectionRequestDetail.getInventoryId());
                    inspectionTaskDetail.setMaterialCode(inspectionRequestDetail.getMaterialCode());
                    inspectionTaskDetail.setMaterialName(inspectionRequestDetail.getMaterialName());
                    inspectionTaskDetail.setLotSerialId(inspectionRequestDetail.getLotSerialId());
                    inspectionTaskDetail.setLotQuantity(inspectionRequestDetail.getLotQuantity());
                    inspectionTaskDetail.setMeasureUnit(inspectionRequestDetail.getMeasureUnit());
                    inspectionTaskDetail.setSampleFlag(inspectionTaskDetail.getSampleFlag());
                    detailList.add(inspectionTaskDetail);
                });
            }
        }
        result.put("inspectionTaskDetailList",detailList);
        return result;
    }
}
