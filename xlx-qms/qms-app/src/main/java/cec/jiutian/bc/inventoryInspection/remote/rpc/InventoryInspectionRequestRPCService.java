package cec.jiutian.bc.inventoryInspection.remote.rpc;

import cec.jiutian.core.frame.constant.FabosJsonRestPath;
import com.alibaba.fastjson2.JSONObject;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/fabos-qms-app"+ FabosJsonRestPath.FABOS_REMOTE_API)
public class InventoryInspectionRequestRPCService {

    @Resource
    private InventoryMaterialInspectRpc inventoryMaterialInspectRpc;


    /**
     *
     * @param jsonObject
     * 主表：materialName、materialCode、type、generalCode、supplierName、materialLevel
     * 子表：detailList ->
     *      COAFlag、originLotId、lotSerialId、materialCode、materialName、materialSpecification、measureUnit、sampleFlag、stockInQuantity、inventoryId
     * 参考模型：InventoryInspectionRequest
     *
     * @return
     * @throws Exception
     */
    @PostMapping("/materialInspectByWMS")
    public String materialInspectByWMS(@RequestBody JSONObject jsonObject) throws Exception {
        return inventoryMaterialInspectRpc.materialInspectByWMS(jsonObject);
    }
}
