package cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.handler;

import cec.jiutian.bc.accidentReportManagement.enumeration.ArmMeasureStatusEnum;
import cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.enumration.ArmVerificationResultEnum;
import cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.model.AccidentReportTask;
import cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.mto.AccidentReportTaskPreVeriMTO;
import cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.mto.ArmPreventMeasureVerifyMTO;
import cec.jiutian.bc.accidentReportManagement.enumeration.ArmImprovingProgressEnum;
import cec.jiutian.bc.urm.domain.user.entity.User;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/30
 * @description
 */
@Component
@Slf4j
public class AccidentReportTaskPreVeriHandler implements OperationHandler<AccidentReportTask, AccidentReportTaskPreVeriMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    //只有当全部位待验证时才走到这里来，因此不需要再去判断是否全部位待验证
    @Override
    public String exec(List<AccidentReportTask> data, AccidentReportTaskPreVeriMTO modelObject, String[] param) {
        if (modelObject != null) {
            if (isTemporaryVerificationPassed(modelObject)) {
                verificationSuccessHandler(modelObject);
            } else {
                returnToSubmitted(modelObject);
            }

            AccidentReportTask model = data.get(0);
            BeanUtil.copyProperties(modelObject, model, "createBy", "createTime");
            fabosJsonDao.mergeAndFlush(model);
        }
        return "alert(操作成功)";
    }

    @Override
    public AccidentReportTaskPreVeriMTO fabosJsonFormValue(List<AccidentReportTask> data, AccidentReportTaskPreVeriMTO fabosJsonForm, String[] param) {
        AccidentReportTask model = data.get(0);
        BeanUtil.copyProperties(model, fabosJsonForm);
        return fabosJsonForm;
    }

    private boolean isTemporaryVerificationPassed(AccidentReportTaskPreVeriMTO model) {
        boolean flag = true;
        List<ArmPreventMeasureVerifyMTO> cvList = model.getArmPreventMeasureList();
        if (cvList != null) {
            for (ArmPreventMeasureVerifyMTO mto : cvList) {
                if (mto.getVerificationResult().equals(ArmVerificationResultEnum.Enum.NOT_PASSED.name())) {
                    mto.setImprovingProgress(ArmImprovingProgressEnum.Enum.PENDING_EXECUTION.name());
                    flag = false;
                }
            }
        }
        return flag;
    }

    private void returnToSubmitted(AccidentReportTaskPreVeriMTO model) {
        model.setPreventMeasureStatus(ArmMeasureStatusEnum.Enum.SUBMITTED.name());
    }

    private void verificationSuccessHandler(AccidentReportTaskPreVeriMTO model) {
        model.setPreventMeasureStatus(ArmMeasureStatusEnum.Enum.VERIFIED.name());
        List<ArmPreventMeasureVerifyMTO> cvList = model.getArmPreventMeasureList();
        if (cvList != null) {
            User user = fabosJsonDao.getById(User.class, UserContext.getUserId());
            for (ArmPreventMeasureVerifyMTO mto : cvList) {
                mto.setVerifiedUser(user);
                mto.setImprovingProgress(ArmImprovingProgressEnum.Enum.COMPLETED.name());
            }
        }
    }
}
