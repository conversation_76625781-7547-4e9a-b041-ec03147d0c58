package cec.jiutian.bc.clientComplaintManagement.enumeration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/06/11
 * @description
 */
public class ReviewProblemImproveStatusEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum data : Enum.values()) {
            list.add(new VLModel(data.name(), data.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        DISTRIBUTING("待分发"),
        ANALYZING("待分析"),
        EXECUTING("待执行"),
        CONFIRMING("待确认"),
        VERIFYING("待验证"),
        COMPLETED("已完成"),
        ;

        private final String value;

    }
}
