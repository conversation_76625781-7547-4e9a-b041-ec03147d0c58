package cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.mto;

import cec.jiutian.bc.inventoryInspection.enumeration.InspectionResultEnum;
import cec.jiutian.bc.materialInspect.domain.inspectionTask.handler.IncomingInspectionItemResultHandler;
import cec.jiutian.bc.materialInspect.domain.inspectionTask.handler.IncomingInspectionResultHandler;
import cec.jiutian.bc.materialInspect.domain.inspectionTask.mto.IncomingTaskResultEnterDetailMTO;
import cec.jiutian.bc.materialInspect.domain.publicInspectionTask.mto.UserForInsTaskMTO;
import cec.jiutian.bc.modeler.enumration.UnqualifiedLevelEnum;
import cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.enumration.TaskBusinessStateEnum;
import cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.handler.AbnormalFeedbackHandlingDynamicHandler;
import cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.proxy.AbnormalFeedbackHandlingCreateMTODataProxy;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/4/25
 * @description TODO
 */
@FabosJson(
        name = "创建",
        dataProxy = AbnormalFeedbackHandlingCreateMTODataProxy.class
)
@Table(name = "qms_qe_abnormal_feedback_handling",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"abnormalFeedbackHandlingFormNumber"})
        }
)
@Entity
@Getter
@Setter
public class AbnormalFeedbackHandlingCreateMTO extends MetaModel {

    @FabosJsonField(
            views = @View(title = "单据编号"),
            edit = @Edit(title = "单据编号",
                    readonly = @Readonly(edit = false),
                    notNull = true,
                    dependFieldDisplay = @DependFieldDisplay(enableOrDisable = "businessState == 'TO_BE_SUBMITTED'")
            ),
            dynamicField = @DynamicField(dependFiled = @DependFiled(buttonName = "生成", changeBy = "id",
                    dynamicHandler = AbnormalFeedbackHandlingDynamicHandler.class))
    )
    private String abnormalFeedbackHandlingFormNumber;

    @FabosJsonField(
            views = @View(title = "创建时间"),
            edit = @Edit(title = "创建时间",
                    show = false,
                    type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @CreationTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date createdTime;

    @FabosJsonField(
            views = @View(title = "异常描述"),
            edit = @Edit(title = "异常描述",
                    notNull = true,
                    type = EditType.TEXTAREA)
    )
    private String abnormalDescription;

    @Transient
    @FabosJsonField(
            views = @View(title = "选择发现人", show = false, column = "name"),
            edit = @Edit(title = "选择发现人",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType()
            )
    )
    private UserForInsTaskMTO discoveredPerson;

    @FabosJsonField(
            views = @View(title = "发现人ID", show = false),
            edit = @Edit(title = "发现人ID",
                    notNull = true,
                    show = false
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "discoveredPerson", beFilledBy = "id"))
    )
    private String discoveredPersonId;

    @FabosJsonField(
            views = @View(title = "发现人"),
            edit = @Edit(title = "发现人",
                    readonly = @Readonly(),
                    notNull = true,
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "discoveredPerson", beFilledBy = "name"))
    )
    private String discoveredPersonName;

    @FabosJsonField(
            views = @View(title = "发现时间"),
            edit = @Edit(title = "发现时间",
                    dateType = @DateType(type = DateType.Type.DATE_TIME),
                    notNull = true
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date submissionTime;

    @FabosJsonField(
            views = @View(title = "异常附件"),
            edit = @Edit(title = "异常附件", type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(size = 5120, maxLimit = 10, type = AttachmentType.Type.BASE))
    )
    private String abnormalAttachments;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",
                    show = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = TaskBusinessStateEnum.class)
            )
    )
    private String businessState;


    @PrePersist
    public void onCreate() {
        if (this.businessState == null) {
            this.setBusinessState(TaskBusinessStateEnum.Enum.TO_BE_SUBMITTED.name());
        }
    }
}
