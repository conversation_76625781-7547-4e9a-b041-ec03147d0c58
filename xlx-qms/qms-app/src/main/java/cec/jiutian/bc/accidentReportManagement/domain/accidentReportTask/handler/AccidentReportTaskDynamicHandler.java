package cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.handler;

import cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.mto.AccidentReportTaskCreateMTO;
import cec.jiutian.bc.basicData.enumeration.NamingRuleCodeEnum;
import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/5/29
 * @description
 */
@Component
public class AccidentReportTaskDynamicHandler implements DependFiled.DynamicHandler<AccidentReportTaskCreateMTO> {

    @Resource
    private NamingRuleService namingRuleService;

    @Override
    public Map<String, Object> handle(AccidentReportTaskCreateMTO inspectionInstrument) {
        Map<String, Object> map = new HashMap<>();
        List<String> result = namingRuleService.getNameCode(NamingRuleCodeEnum.ACCIDENT_REPORT_TASK.name(), 1, null);
        map.put("generalCode", result.get(0));
        return map;
    }
}
