package cec.jiutian.bc.productExamineManagement.domain.examineImplementPlan.handler;

import cec.jiutian.bc.productExamineManagement.domain.examineImplementPlan.model.ExamineImplementPlan;
import cec.jiutian.bc.productExamineManagement.enumration.ExamineImplementStateEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/27
 * @description TODO
 */
@Component
public class ExamineImplementCancelOperationHandler implements OperationHandler<ExamineImplementPlan,Void> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<ExamineImplementPlan> data, Void modelObject, String[] param) {
        ExamineImplementPlan examineImplementPlan = data.get(0);
        examineImplementPlan.setBusinessState(ExamineImplementStateEnum.Enum.Cancel.name());
        fabosJsonDao.mergeAndFlush(examineImplementPlan);

        return "alert('操作成功')";
    }
}
