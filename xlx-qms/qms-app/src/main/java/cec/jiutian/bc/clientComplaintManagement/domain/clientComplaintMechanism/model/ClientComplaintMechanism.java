package cec.jiutian.bc.clientComplaintManagement.domain.clientComplaintMechanism.model;

import cec.jiutian.bc.clientComplaintManagement.domain.clientComplaintMechanism.handler.ClientComplaintMechanismDynamicHandler;
import cec.jiutian.bc.clientComplaintManagement.domain.clientComplaintMechanism.proxy.ClientComplaintMechanismDataProxy;
import cec.jiutian.bc.clientComplaintManagement.enumeration.CustomerComplaintLevelEnum;
import cec.jiutian.bc.clientComplaintManagement.enumeration.UpgradeMechanismEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.Search;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/05/20
 * @description TODO
 */
@FabosJson(
        name = "客诉机制",
        orderBy = "ClientComplaintMechanism.createTime desc",
        dataProxy = ClientComplaintMechanismDataProxy.class
)
@Table(name = "qms_ccm_client_complaint_mechanismr",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Data
public class ClientComplaintMechanism extends MetaModel {
    @FabosJsonField(
            views = @View(title = "编码"),
            edit = @Edit(title = "编码",
                    readonly = @Readonly(add = false),
                    search = @Search(vague = true),
                    notNull = true),
            dynamicField = @DynamicField(
                    dependFiled = @DependFiled(buttonName = "生成", changeBy = "id",
                    dynamicHandler = ClientComplaintMechanismDynamicHandler.class))
    )
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "客诉机制名称"),
            edit = @Edit(title = "客诉机制名称",
                    search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    private String clientComplaintMechanismName;

    @FabosJsonField(
            views = @View(title = "客诉等级"),
            edit = @Edit(title = "客诉等级",
                    type = EditType.CHOICE,
                    notNull = true,
                    search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = CustomerComplaintLevelEnum.class)
            )
    )
    private String clientComplaintLevel;

    @FabosJsonField(
            views = @View(title = "升级机制"),
            edit = @Edit(title = "升级机制",
                    type = EditType.CHOICE,
                    notNull = true,
                    search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = UpgradeMechanismEnum.class)
            )
    )
    private String upgradeMechanism;
}
