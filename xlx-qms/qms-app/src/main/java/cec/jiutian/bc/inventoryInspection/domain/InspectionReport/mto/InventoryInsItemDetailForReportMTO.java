package cec.jiutian.bc.inventoryInspection.domain.InspectionReport.mto;//package cec.jiutian.bc.materialInspect.domain.InspectionReport.mto;
//
//
//import cec.jiutian.bc.modeler.domain.inspectionItem.model.InspectionItem;
//import cec.jiutian.bc.modeler.domain.inspectionItem.model.InspectionItemTarget;
//import cec.jiutian.bc.basicData.enumeration.InitialInspectionTypeEnum;
//import cec.jiutian.bc.basicData.enumeration.PackageTypeEnum;
//import cec.jiutian.bc.materialInspect.domain.publicInspectionTask.mto.InsItemTargetMTO;
//import cec.jiutian.bc.materialInspect.domain.publicInspectionTask.mto.SamplingPlanMTO;
//import cec.jiutian.bc.materialInspect.domain.publicInspectionTask.proxy.InsItemDetailMTODataProxy;
//import cec.jiutian.core.frame.module.MetaModel;
//import cec.jiutian.core.view.fabosJson.FabosJson;
//import cec.jiutian.view.*;
//import cec.jiutian.view.field.*;
//import cec.jiutian.view.field.edit.*;
//import jakarta.persistence.*;
//import lombok.Getter;
//import lombok.Setter;
//import org.apache.commons.collections.CollectionUtils;
//import org.springframework.beans.BeanUtils;
//
//import java.util.ArrayList;
//import java.util.List;
//
//@FabosJson(
//        name = "检验项目",
//        orderBy = "InsItemDetail.createTime desc",
//        dataProxy = InsItemDetailMTODataProxy.class
//)
//@Table(name = "bd_ins_item_detail",
//        uniqueConstraints = {
//                @UniqueConstraint(columnNames = {"generalCode"})
//        }
//)
//@Entity
//@Getter
//@Setter
//@TemplateType(type = "multiTable")
//public class InsItemDetailForReportMTO extends MetaModel {
//
//
//
//    @FabosJsonField(
//            views = @View(title = "编号"),
//            edit = @Edit(title = "编号",
//                    readonly = @Readonly(add = true, edit = true),
//                    notNull = true)
//    )
//    private String generalCode;
//
//    @FabosJsonField(
//            views = @View(title = "检验项目名称"),
//            edit = @Edit(title = "检验项目名称",
//                    readonly = @Readonly(add = true, edit = true),
//                    search = @Search(vague = true),
//                    inputType = @InputType(length = 40))
//    )
//    private String name;
//
//    @FabosJsonField(
//            views = @View(title = "特性"),
//            edit = @Edit(title = "特性",
//                    readonly = @Readonly(add = true, edit = true),
//                    search = @Search(vague = true),
//                    inputType = @InputType(length = 40))
//    )
//    private String feature;
//
//
//    @FabosJsonField(
//            views = @View(title = "是否自动判断"),
//            edit = @Edit(title = "是否自动判断",
//                    readonly = @Readonly(add = true, edit = true),
//                    defaultVal = "false"
//            )
//    )
//    private Boolean isAutoJudged;
//
//    @FabosJsonField(
//            views = @View(title = "包装方式"),
//            edit = @Edit(title = "包装方式",
//                    readonly = @Readonly(add = true, edit = true),
//                    type = EditType.CHOICE,search = @Search(),
//                    choiceType = @ChoiceType(fetchHandler = PackageTypeEnum.class))
//    )
//    private String packageType;
//
//    @OneToOne(cascade = CascadeType.ALL)
//    @JoinColumn(name = "plan_id")
//    @FabosJsonField(
//            views = @View(title = "抽样方案", column = "name", type = ViewType.TABLE_FORM),
//            edit = @Edit(title = "抽样方案",
//                    readonly = @Readonly(add = true, edit = true),
//                    type = EditType.REFERENCE_TABLE,
//                    referenceTableType = @ReferenceTableType(id = "id", label = "name")
//            )
//    )
//    private SamplingPlanMTO samplingPlan;
//
//    @FabosJsonField(
//            views = @View(title = "样本量"),
//            edit = @Edit(title = "样本量",notNull = true,
//                    readonly = @Readonly(add = true, edit = true),
//                    numberType = @NumberType(min=0,max = 9999999,precision = 2))
//    )
//    private Double sampleSize;
//
//    @FabosJsonField(
//            views = @View(title = "检验频次"),
//            edit = @Edit(title = "检验频次", notNull = true,
//                    readonly = @Readonly(add = true, edit = true),
//                    inputGroup = @InputGroup(postfix = "次"), numberType = @NumberType(min = 0))
//    )
//    private Integer inspectFrequency;
//
//    @FabosJsonField(
//            views = @View(title = "首检类型"),
//            edit = @Edit(title = "首检类型",
//                    readonly = @Readonly(add = true, edit = true),
//                    type = EditType.CHOICE,search = @Search(),
//                    choiceType = @ChoiceType(fetchHandler = InitialInspectionTypeEnum.class))
//    )
//    private String initialInspectionType;
//
//    @FabosJsonField(
//            views = @View(title = "取样点"),
//            edit = @Edit(title = "取样点",
//                    readonly = @Readonly(add = true, edit = true),
//                    inputType = @InputType(length = 40))
//    )
//    private String samplingPoint;
//
//    @FabosJsonField(
//            views = @View(title = "送检点"),
//            edit = @Edit(title = "送检点",
//                    readonly = @Readonly(add = true, edit = true),
//                    inputType = @InputType(length = 40))
//    )
//    private String sendInspectPoint;
//
//    @FabosJsonField(
//            views = @View(title = "备注"),
//            edit = @Edit(title = "备注",
//                    readonly = @Readonly(add = true, edit = true),
//                    type = EditType.TEXTAREA)
//    )
//    private String remark;
//
//    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = false)
//    @JoinColumn(name = "ins_item_id")
//    @FabosJsonField(
//            views = @View(title = "检验项指标", type= ViewType.TABLE_VIEW),
//            edit = @Edit(title = "检验项指标", type = EditType.TAB_TABLE_ADD)
//    )
//    private List<InsItemTargetMTO> insItemTargetList;
//}
