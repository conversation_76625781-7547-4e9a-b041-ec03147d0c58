package cec.jiutian.bc.productExamineManagement.domain.examineImplementPlan.mto;

import cec.jiutian.bc.productExamineManagement.domain.examineImplementPlan.handler.UnqualifiedScoreDynamicHandler;
import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.*;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.NumberType;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToMany;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/24
 * @description TODO
 */
@Entity
@Data
@FabosJson(
        name = "产品审核实施计划执行MTO"
)
public class ProductExamineExecuteMTO extends BaseModel {

    @FabosJsonField(
            views = @View(title = "合格数"),
            edit = @Edit(title = "合格数", readonly = @Readonly,
                    numberType = @NumberType(min = 0,precision = 2))
    )
    private Double qualifiedQuantity;

    @FabosJsonField(
            views = @View(title = "不合格数"),
            edit = @Edit(title = "不合格数", readonly = @Readonly,
                    numberType = @NumberType(min = 0,precision = 2))
    )
    private Double unqualifiedQuantity;

    @FabosJsonField(
            views = @View(title = "加权系数"),
            edit = @Edit(title = "加权系数", notNull = true,
                    numberType = @NumberType(min = 0,precision = 2))
    )
    private Double weightingFactor;

    @FabosJsonField(
            views = @View(title = "不合格分数"),
            edit = @Edit(title = "不合格分数", readonly = @Readonly,
                    numberType = @NumberType(min = 0,precision = 2)),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "weightingFactor",dynamicHandler = UnqualifiedScoreDynamicHandler.class))
    )
    private Double unqualifiedScore;

    @FabosJsonField(
            views = @View(title = "KZQ分数"),
            edit = @Edit(title = "KZQ分数", notNull = true,inputGroup = @InputGroup(postfix = "%"),
                    numberType = @NumberType(min = 0,precision = 2))
    )
    private Double kqzValue;

    @FabosJsonField(
            views = @View(title = "是否创建纠正预防措施"),
            edit = @Edit(title = "是否创建纠正预防措施", defaultVal = "false"
            )
    )
    private Boolean isCorrectPreventMeasure;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = false)
    @FabosJsonField(
            views = @View(title = "检验项目明细", type = ViewType.TABLE_VIEW, index = 8),
            edit = @Edit(title = "检验项目明细",readonly = @Readonly, type = EditType.TAB_REFERENCE_GENERATE),
            referenceGenerateType = @ReferenceGenerateType()
    )
    @JoinColumn(name = "product_examine_execute_id")
    private List<ProductExamineExecuteDetailMTO> detailMTOList;
}
