package cec.jiutian.bc.clientComplaintManagement.domain.myProblemImprovement.handler;

import cec.jiutian.bc.clientComplaintManagement.domain.myProblemImprovement.model.MyProblemImprovement;
import cec.jiutian.bc.clientComplaintManagement.enumeration.ProblemImprovementStatusEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/05/20
 * @description TODO
 */
@Component
public class MyProblemImprovementCompleteHandler implements OperationHandler<MyProblemImprovement, Void> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Transactional
    @Override
    public String exec(List<MyProblemImprovement> data, Void modelObject, String[] param) {
        if (CollectionUtils.isEmpty(data) || data.get(0) == null) {
            return "alert(行数据选择异常)";
        }
        MyProblemImprovement model = data.get(0);
        model.setBusinessStatus(ProblemImprovementStatusEnum.Enum.TO_BE_VERIFIED.name());

        fabosJsonDao.mergeAndFlush(model);
        return null;
    }
}
