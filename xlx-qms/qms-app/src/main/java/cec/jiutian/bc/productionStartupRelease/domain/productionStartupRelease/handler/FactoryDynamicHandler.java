package cec.jiutian.bc.productionStartupRelease.domain.productionStartupRelease.handler;

import cec.jiutian.bc.mto.FactoryArea;
import cec.jiutian.bc.productionStartupRelease.domain.productionStartupRelease.mto.PLSRReportAddMTO;
import cec.jiutian.view.DependFiled;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class FactoryDynamicHandler implements DependFiled.DynamicHandler<PLSRReportAddMTO> {

    @Override
    public Map<String, Object> handle(PLSRReportAddMTO plsrReport) {
        HashMap<String, Object> res = new HashMap<>(8);
        res.put("factoryId",null);
        res.put("workshopName","");
        res.put("line",new FactoryArea());
        res.put("lineName","");
        if (plsrReport.getWorkshop() != null) {
            res.put("factoryId",plsrReport.getWorkshop().getPid());
            res.put("workshopName",plsrReport.getWorkshop().getFactoryAreaName());
        }
        return res;
    }
}
