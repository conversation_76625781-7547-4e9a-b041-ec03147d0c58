package cec.jiutian.bc.changeRequestManagement.domain.myChangeTask.handler;

import cec.jiutian.bc.changeRequestManagement.domain.ecn.model.MyECNItem;
import cec.jiutian.bc.changeRequestManagement.domain.myChangeTask.model.*;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Component
public class MyEcnExecHandler implements OperationHandler<MyChangeTask, MyEcnExec> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<MyChangeTask> data, MyEcnExec modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            modelObject.getMyExecECNItems().forEach(d -> {
                MyECNItem myECNItem = fabosJsonDao.findById(MyECNItem.class, d.getId());
                myECNItem.setProgress(d.getProgress());
                myECNItem.setSupplementTaskCompletionDate(d.getSupplementTaskCompletionDate());
                myECNItem.setAttachment(d.getAttachment());
                fabosJsonDao.mergeAndFlush(myECNItem);
            });
        }
        return "msg.success('操作成功')";
    }

    @Override
    public MyEcnExec fabosJsonFormValue(List<MyChangeTask> data, MyEcnExec fabosJsonForm, String[] param) {
        // data为空 直接返回
        if (CollectionUtils.isEmpty(data)) {
            return fabosJsonForm;
        }

        String userId = UserContext.getUserId();
        MyChangeTask myChangeTask = data.get(0);
        BeanUtil.copyProperties(myChangeTask, fabosJsonForm);

        //如果list为空 直接返回
        if (CollectionUtils.isEmpty(myChangeTask.getMyECRItems())) {
            return fabosJsonForm;
        }

        List<MyExecECNItem> myExecECNItemList = new ArrayList<>();
        myChangeTask.getMyECNItems().forEach(d->{
            if (Objects.equals(d.getResponsiblePersonId(), userId)) {
                MyExecECNItem myExecECNItem = new MyExecECNItem();
                BeanUtil.copyProperties(d, myExecECNItem);
                myExecECNItemList.add(myExecECNItem);
            }
        });

        fabosJsonForm.setMyExecECNItems(myExecECNItemList);
        return fabosJsonForm;
    }
}
