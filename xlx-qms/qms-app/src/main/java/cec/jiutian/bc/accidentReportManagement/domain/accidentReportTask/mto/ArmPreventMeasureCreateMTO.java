package cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.mto;

import cec.jiutian.bc.accidentReportManagement.enumeration.ArmImprovingProgressEnum;
import cec.jiutian.bc.materialInspect.domain.publicInspectionTask.mto.UserForInsTaskMTO;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DateType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.TabTableReferType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/5/29
 * @description
 */
@FabosJson(
        name = "预防措施创建",
        orderBy = "ArmPreventMeasureCreateMTO.createTime desc"
)
@Table(name = "qms_arm_prevent_measure")
@Entity
@Data
public class ArmPreventMeasureCreateMTO extends MetaModel {
    @ManyToOne
    @FabosJsonField(
            views = {
                    @View(title = "事故报告任务", column = "generalCode", show = false)
            },
            edit = @Edit(title = "事故报告任务", type = EditType.REFERENCE_TABLE, show = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode")
            )
    )
    @JsonIgnoreProperties("armPreventMeasureList")
    private AccidentReportTaskPreventMTO accidentReportTask;

    @FabosJsonField(
            views = @View(title = "预防措施"),
            edit = @Edit(title = "预防措施", notNull = true)
    )
    private String preventiveMeasure;;

    @FabosJsonField(
            views = @View(title = "责任人", column = "name"),
            edit = @Edit(title = "责任人",
                    notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType(type = TabTableReferType.SelectShowTypeMTM.LIST)
            )
    )
    @ManyToOne
    @JoinColumn(name = "responsible_person_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private UserForInsTaskMTO userForInsTaskMTO;

    @FabosJsonField(
            views = @View(title = "纳期"),
            edit = @Edit(title = "纳期",
                    dateType = @DateType(type = DateType.Type.DATE_TIME),
                    notNull = true
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date deliveryTime;

    @FabosJsonField(
            views = @View(title = "改善进度", show = false),
            edit = @Edit(title = "改善进度",
                    readonly = @Readonly(),
                    type = EditType.CHOICE,
                    show = false,
                    choiceType = @ChoiceType(fetchHandler = ArmImprovingProgressEnum.class)
            )
    )
    private String improvingProgress;

    @PrePersist
    protected void onCreate() {
        if (improvingProgress == null) {
            improvingProgress = ArmImprovingProgressEnum.Enum.PENDING_EXECUTION.name();
        }
    }
}
