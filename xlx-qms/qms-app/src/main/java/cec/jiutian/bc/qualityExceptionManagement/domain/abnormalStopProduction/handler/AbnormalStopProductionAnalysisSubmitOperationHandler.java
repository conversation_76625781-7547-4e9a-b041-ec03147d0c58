package cec.jiutian.bc.qualityExceptionManagement.domain.abnormalStopProduction.handler;

import cec.jiutian.bc.qualityExceptionManagement.domain.abnormalStopProduction.model.AbnormalStopProduction;
import cec.jiutian.bc.qualityExceptionManagement.enums.AbnormalStopProductionStateEnum;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class AbnormalStopProductionAnalysisSubmitOperationHandler implements OperationHandler<AbnormalStopProduction, Void> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<AbnormalStopProduction> data, Void modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            AbnormalStopProduction entity = data.get(0);
            if (null == entity.getEquipmentStopFlag() || null == entity.getWorkshopStopFlag() || null == entity.getProcessStopFlag()) {
                throw new FabosJsonApiErrorTip("原因分析及意见未填写完整，请检查");
            }
            entity.setCurrentState(AbnormalStopProductionStateEnum.Enum.WaitReview.name());
            fabosJsonDao.mergeAndFlush(entity);
        }
        return "msg.success('操作成功')";
    }
}
