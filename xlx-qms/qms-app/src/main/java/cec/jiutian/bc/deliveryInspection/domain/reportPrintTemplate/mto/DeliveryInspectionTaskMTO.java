package cec.jiutian.bc.deliveryInspection.domain.reportPrintTemplate.mto;

import cec.jiutian.bc.basicData.domain.inspectionStandard.model.InspectionStandard;
import cec.jiutian.bc.basicData.enumeration.ApplyRangeEnum;
import cec.jiutian.bc.deliveryInspection.domain.inspectionTask.handler.DeliveryInventoryReferenceAddHandler;
import cec.jiutian.bc.deliveryInspection.domain.inspectionTask.model.DeliveryInspectionItemDetail;
import cec.jiutian.bc.materialInspect.domain.inspectionTask.model.InspectionTaskDetail;
import cec.jiutian.bc.materialInspect.enumeration.InspectionResultEnum;
import cec.jiutian.bc.materialInspect.enumeration.TaskBuildTypeEnum;
import cec.jiutian.bc.modeler.enumration.TaskBusinessStateEnum;
import cec.jiutian.bc.modeler.enumration.UnqualifiedLevelEnum;
import cec.jiutian.bc.mto.SpecificationManageMTO;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.meta.SkipMetadataScanning;
import cec.jiutian.view.*;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DependFieldDisplay;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@FabosJson(
        name = "发货检验任务",
        orderBy = "createTime desc",
        filter = @Filter(value = "inspectionType = 'deliveryInspect' and businessState in ('INSPECTING','INSPECT_FINISH')"),
        power = @Power(add = false,edit = false,delete = false)
)
@Table(name = "mi_inspection_task",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Getter
@Setter
@TemplateType(type = "multiTable")
@SkipMetadataScanning
public class DeliveryInspectionTaskMTO extends MetaModel {

    @FabosJsonField(
        views = @View(title = "检验任务编号"),
        edit = @Edit(title = "检验任务编号", readonly = @Readonly())
    )
    private String generalCode;

    @FabosJsonField(
        views = @View(title = "检验状态"),
        edit = @Edit(title = "检验状态", show = false, type = EditType.CHOICE, search = @Search(),
            choiceType = @ChoiceType(fetchHandler = TaskBusinessStateEnum.class), readonly = @Readonly())
    )
    private String businessState;

    @FabosJsonField(
        views = @View(title = "当前状态"),
        edit = @Edit(title = "当前状态", show = false, type = EditType.CHOICE, search = @Search(),
            choiceType = @ChoiceType(fetchHandler = OrderCurrentStateEnum.class), readonly = @Readonly())
    )
    private String currentState;

    @FabosJsonField(
            views = @View(title = "质检标准",column = "generalCode"),
            edit = @Edit(title = "质检标准",
                    readonly = @Readonly(),allowAddMultipleRows = false,
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode")
            )
    )
    @ManyToOne
    @JoinColumn(name = "inspection_standard_id")
    private InspectionStandard inspectionStandard;

    @FabosJsonField(
            views = @View(title = "创建类型"),
            edit = @Edit(title = "创建类型", type = EditType.CHOICE,search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = TaskBuildTypeEnum.class))
    )
    private String buildType;

    @FabosJsonField(
            views = @View(title = "检验类型"),
            edit = @Edit(title = "检验类型", show = false,type = EditType.CHOICE,search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = ApplyRangeEnum.class))
    )
    private String inspectionType;

    @FabosJsonField(
        views = @View(title = "是否加急"),
        edit = @Edit(title = "是否加急", defaultVal = "false", readonly = @Readonly())
    )
    private Boolean isUrgent;

    @FabosJsonField(
        views = @View(title = "检验部门"),
        edit = @Edit(title = "检验部门", search = @Search(vague = true), readonly = @Readonly()),
        dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "orgMTO", beFilledBy = "name"))
    )
    private String inspectionDepartmentName;


    @FabosJsonField(
            views = @View(title = "检验员姓名"),
            edit = @Edit(title = "检验员姓名",
                    show = false,
                    readonly = @Readonly(),
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "user", beFilledBy = "name"))
    )
    private String userName;

    @FabosJsonField(
            views = @View(title = "检验结果"),
            edit = @Edit(title = "检验结果", type = EditType.CHOICE,search = @Search(),
                    readonly = @Readonly(),
                    choiceType = @ChoiceType(fetchHandler = InspectionResultEnum.class))
    )
    private String inspectionResult;

    @FabosJsonField(
            views = @View(title = "不合格等级"),
            edit = @Edit(title = "不合格等级", notNull = true,type = EditType.CHOICE,
                    readonly = @Readonly(),
                    choiceType = @ChoiceType(fetchHandler = UnqualifiedLevelEnum.class),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "inspectionResult == 'UNQUALIFIED'"))
    )
    private String unqualifiedLevel;

    @Transient
    @FabosJsonField(
            views = @View(title = "物资名称", column = "name"),
            edit = @Edit(title = "物资名称",
                    type = EditType.REFERENCE_TABLE,
                    readonly = @Readonly(),
                    filter = @Filter(value = "validateFlag = 'Y' and specificationCode like '02%'"),
                    referenceTableType = @ReferenceTableType(id = "id", label = "name")
            )
    )
    private SpecificationManageMTO material;

    @FabosJsonField(
            views = @View(title = "检验物资名称"),
            edit = @Edit(title = "检验物资名称", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "material", beFilledBy = "name"))
    )
    private String materialName;

    @FabosJsonField(
            views = @View(title = "检验物资编码"),
            edit = @Edit(title = "检验物资编码", readonly = @Readonly)
    )
    private String materialCode;

    @FabosJsonField(
            views = @View(title = "规格"),
            edit = @Edit(title = "规格",readonly = @Readonly())
    )
    private String materialSpecification;

    @FabosJsonField(
            views = @View(title = "数量"),
            edit = @Edit(title = "数量",
                    readonly = @Readonly(),
                    notNull = true)
    )
    private Double arrivalQuantity;

    @FabosJsonField(
            views = @View(title = "取样数量"),
            edit = @Edit(title = "取样数量", readonly = @Readonly)
    )
    private Double allInspectionItemQuantity;

    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumn(name = "inspection_task_id")
    @OrderBy
    @FabosJsonField(
            edit = @Edit(title = "检验物资明细", readonly = @Readonly(), type = EditType.TAB_REFER_ADD),
            referenceAddType = @ReferenceAddType(referenceClass = "InventoryMTO",
                    filter = "currentState = 'normal' and materialCode = '${inspectionRequestDetail.materialCode}'" +
                            "and materialName = '${materialName}'",
                    editable = {"requestQuantity"},
                    referenceAddHandler = DeliveryInventoryReferenceAddHandler.class),
            views = @View(title = "检验物资明细", type = ViewType.TABLE_VIEW, extraPK = "inventoryId")
    )
    private List<InspectionTaskDetail> inspectionTaskDetailList;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = false)
    @FabosJsonField(
            views = @View(title = "检验项目明细", type= ViewType.TABLE_VIEW,index = 8),
            edit = @Edit(title = "检验项目明细",readonly = @Readonly(),type = EditType.TAB_REFERENCE_GENERATE),
           referenceGenerateType = @ReferenceGenerateType()
    )
    @JoinColumn(name = "inspection_task_id")
    private List<DeliveryInspectionItemDetail> standardDetailList;

    @FabosJsonField(
        views = @View(title = "备注"),
        edit = @Edit(title = "备注", type = EditType.TEXTAREA, readonly = @Readonly())
    )
    private String remark;
}
