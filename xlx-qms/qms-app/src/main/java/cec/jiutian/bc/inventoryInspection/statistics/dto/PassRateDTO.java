package cec.jiutian.bc.inventoryInspection.statistics.dto;

import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
public class PassRateDTO {
    private String id;       // ID
    private String name;     // 缺陷名称
    private Double totalProduction;  // 生产数量
    private Double defectiveCount;   // 不良数量
    private Double qualifiedCount;   // 合格数量
    private Double passRate;         // 成品合格率
    private Double targetRate;       // 目标合格率

    public static PassRateDTO mapToDto(Object[] row) {
        return PassRateDTO.builder()
                .id(String.valueOf(row[0]))
                .name(String.valueOf(row[1]))
                .totalProduction(convertToDouble(row[2]))
                .defectiveCount(convertToDouble(row[3]))
                .qualifiedCount(convertToDouble(row[4]))
                .passRate(convertToDouble(row[5]))
                .targetRate(0.5) // 固定目标值
                .build();
    }

    private static Double convertToDouble(Object value) {
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        return 0D;
    }
}