package cec.jiutian.bc.inventoryInspection.statistics.service;

import cec.jiutian.bc.ao.QueryAO;
import cec.jiutian.bc.dto.ChartData;
import cec.jiutian.bc.enums.CharType;
import cec.jiutian.bc.enums.LineStyle;
import cec.jiutian.bc.inventoryInspection.statistics.ao.StatisticsCommonParam;
import cec.jiutian.bc.inventoryInspection.statistics.dto.QualityStatsDTO;
import cec.jiutian.bc.inventoryInspection.statistics.repository.StatisticsRepository;
import cec.jiutian.bc.inventoryInspection.statistics.vo.YieldRateVO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class YieldRateService {
    private final StatisticsRepository statsRepository;

    public YieldRateService(StatisticsRepository statsRepository) {
        this.statsRepository = statsRepository;
    }

    // 获取原始统计数据
    public List<QualityStatsDTO> getQualityStats(StatisticsCommonParam param) {
        List<Object[]> results = statsRepository.queryPassTrendData(param);
        return results.stream().map(QualityStatsDTO::mapToDto).collect(Collectors.toList());
    }

    // 获取图表数据
    public YieldRateVO getQualityChartData(QueryAO queryAO) {
        StatisticsCommonParam statisticsCommonParam = StatisticsCommonParam.createByQueryAO(queryAO);

        List<String> dimensions = queryAO.getDimension();
        ArrayList<List<QualityStatsDTO>> res = new ArrayList<>(5);
        if (CollectionUtils.isNotEmpty(dimensions)) {
            Collections.sort(dimensions);
            for (String dimension : dimensions) {
                statisticsCommonParam.setDimension(dimension);
                List<QualityStatsDTO> stats = getQualityStats(statisticsCommonParam);
                res.add(stats);
            }
        } else {
            List<QualityStatsDTO> stats = getQualityStats(statisticsCommonParam);
            res.add(stats);
        }
        return convertToChartData(res);
    }

    private YieldRateVO convertToChartData(ArrayList<List<QualityStatsDTO>> res) {
        YieldRateVO chartData = new YieldRateVO();

        List<String> timePeriods = new ArrayList<>();

        res.forEach(stats -> {
            List<String> xs = stats.stream()
                    .map(QualityStatsDTO::getTimePeriod)
                    .collect(Collectors.toList());
            timePeriods.addAll(xs);
        });
        chartData.getXAxis().setData(timePeriods);
        chartData.getXAxis().getAxisLabel().setRotate(270);

        // 2. 构建Y轴
        List<ChartData.YAxis> yAxes = new ArrayList<>();

        // 左侧Y轴（数量）
        ChartData.YAxis leftYAxis = new ChartData.YAxis();
        leftYAxis.setPosition("left");
        leftYAxis.setName("数量/重量");
        leftYAxis.setShow(true);
        yAxes.add(leftYAxis);

        // 右侧Y轴（百分比）
        ChartData.YAxis rightYAxis = new ChartData.YAxis();
        rightYAxis.setPosition("right");
        rightYAxis.setName("合格率");
        rightYAxis.setShow(false);
        yAxes.add(rightYAxis);
        chartData.setYAxis(yAxes);

        // 3. 构建系列数据
        List<ChartData.Series> seriesList = new ArrayList<>();
        List<Object> productNums = new ArrayList<>();
        List<Object> defDatas = new ArrayList<>();
        List<Object> passRates = new ArrayList<>();
        List<Object> targetRates = new ArrayList<>();
        res.forEach(stats -> {
            stats.forEach(stat -> {
                productNums.add(stat.getOutputQuantity());
                defDatas.add(stat.getDefectiveQuantity());
                passRates.add(stat.getPassRate());
                targetRates.add(stat.getTargetRate());
            });
        });
        ChartData.Series proSeries = ChartData.Series.createSeries("生产数量", CharType.bar.name(), 0,
              productNums, "#014D64", true);
        seriesList.add(proSeries);

        ChartData.Series defSeries = ChartData.Series.createSeries("不良数量", CharType.bar.name(), 0,
              defDatas, "#01A2D9", true);
        seriesList.add(defSeries);

        ChartData.Series passSeries = ChartData.Series.createSeries("合格率", CharType.line.name(), 1,
                passRates, "#046b9d", true);
        passSeries.setIsPercent(true);
        passSeries.setLineStyle(new ChartData.LineStyle(LineStyle.solid.name()));
        seriesList.add(passSeries);

        ChartData.Series targetSeries = ChartData.Series.createSeries("目标合格率", CharType.line.name(), 1,
               targetRates, "#7C260B", false);
        targetSeries.setIsPercent(true);
        targetSeries.setTargetLineType("lower");
        targetSeries.setTargetLineColor("#FF0000");
        targetSeries.setLineStyle(new ChartData.LineStyle(LineStyle.dashed.name()));
        seriesList.add(targetSeries);

        chartData.setSeries(seriesList);
        return chartData;
    }



}
