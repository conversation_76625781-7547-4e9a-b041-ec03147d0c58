package cec.jiutian.bc.inventoryInspection.domain.inspectionTask.handler;

import cec.jiutian.bc.inventoryInspection.domain.inspectionTask.model.InventoryInspectionTask;
import cec.jiutian.bc.inventoryInspection.port.mto.InventoryUserMTO;
import cec.jiutian.bc.modeler.enumration.TaskBusinessStateEnum;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import jakarta.persistence.LockModeType;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Component
@RefreshScope
public class InventoryPickupInsTaskHandler implements OperationHandler<InventoryInspectionTask, InventoryInspectionTask> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Value("${qms.debug}")
    private boolean debug;

    @Transactional
    public String exec(List<InventoryInspectionTask> data, InventoryInspectionTask modelObject, String[] param) {
        if (CollectionUtils.isEmpty(data)) {
            return null;
        }
        String userId = UserContext.getUserId();
        InventoryUserMTO inventoryUserMTO = fabosJsonDao.findById(InventoryUserMTO.class, userId);
        if (inventoryUserMTO == null) {
            throw new ServiceException("用户不存在");
        }
        if (!debug && (inventoryUserMTO.getOrg() != null && !inventoryUserMTO.getOrg().getCode().equals("IQC"))) {
            throw new ServiceException("用户不是IQC人员");
        }

        InventoryInspectionTask task = data.get(0);
        task = fabosJsonDao.getEntityManager().find(InventoryInspectionTask.class, task.getId(), LockModeType.PESSIMISTIC_WRITE);
        if (task.getUserId() != null || task.getUserName() != null) {
            throw new ServiceException("该任务已被领取");
        }
        if (task.getUserId() != null || task.getUserName() != null) {
            throw new ServiceException("该任务已被领取");
        }
        task.setUpdateBy(UserContext.getUserName());
        task.setUserName(UserContext.getUserName());
        task.setUserId(inventoryUserMTO.getId());
        task.setBusinessState(TaskBusinessStateEnum.Enum.INSPECTING.name());
        fabosJsonDao.mergeAndFlush(task);
        return "操作成功";
    }
}
