package cec.jiutian.bc.productionStartupRelease.domain.productionStartupRelease.mto;

import cec.jiutian.bc.changeRequestManagement.enums.ApproveResultEnum;
import cec.jiutian.bc.changeRequestManagement.enums.ApproveTaskStatusEnum;
import cec.jiutian.bc.productionStartupRelease.domain.AproveTask.modle.PLSRApproveTask;
import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.RowBaseOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.Column;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.Date;

@Getter
@Setter
@FabosJson(
        name = "审批任务",
        orderBy = "createDate desc",
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "1 == 1",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.HIDE
                ),
                @RowBaseOperation(
                        code = "add",
                        ifExpr = "1 == 1",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.HIDE
                ),
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "1 == 1",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.HIDE
                )
        }
)
public class ApproveTaskMTO extends BaseModel {

    @FabosJsonField(
            views = @View(title = "审批意见"),
            edit = @Edit(
                    title = "审批意见",
                    inputType = @InputType(length = 10),
                    search = @Search,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ApproveResultEnum.class)
            )
    )
    @Column(length = 10)
    private String result;

    @FabosJsonField(
            views = @View(title = "意见说明"),
            edit = @Edit(
                    title = "意见说明",
                    type = EditType.TEXTAREA,
                    inputType = @InputType(length = 100)
            )
    )
    @Column(length = 100)
    private String explain;

    @FabosJsonField(
            views = @View(title = "审批人ID", show = false),
            edit = @Edit(
                    title = "审批人ID",
                    show = false,
                    inputType = @InputType(length = 20)
            )
    )
    private String assignee;

    @FabosJsonField(
            views = @View(title = "角色", show = false),
            edit = @Edit(title = "角色",
                    show = false
            )
    )
    private String role;

    @FabosJsonField(
            views = @View(title = "附件"),
            edit = @Edit(title = "附件",
                    readonly = @Readonly,
                    inputType = @InputType(length = 300),
                    type = EditType.ATTACHMENT,
                    tips = "最大支持5个大小为100M的文件",
                    attachmentType = @AttachmentType(
                            size = 100 * 1024,
                            maxLimit = 5,
                            type = AttachmentType.Type.BASE)
            )
    )
    @Column(length = 300)
    private String attachment;

    @FabosJsonField(
            views = @View(title = "变更请求ID", show = false),
            edit = @Edit(title = "变更请求ID",
                    show = false
            )
    )
    @Column(length = 40, nullable = false)
    private String ecrId;

    @FabosJsonField(
            views = @View(title = "审批任务编码", show = false),
            edit = @Edit(title = "审批任务编码",
                    show = false,
                    inputType = @InputType(length = 20)
            )
    )
    @Column(length = 20, nullable = false)
    private String code;

    @FabosJsonField(
            views = @View(title = "审批任务名称"),
            edit = @Edit(title = "审批任务名称",
                    search = @Search(vague = true),
                    readonly = @Readonly,
                    inputType = @InputType(length = 20)
            )
    )
    @Column(length = 20, nullable = false)
    private String taskName;

    @FabosJsonField(
            views = @View(title = "审批时间",
                    type = ViewType.DATE_TIME),
            edit = @Edit(title = "审批时间",
                    show = false,
                    search = @Search(vague = true),
                    dateType = @DateType(type = DateType.Type.DATE_TIME))
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date approveTime;

    @FabosJsonField(
            views = @View(title = "创建时间",
                    type = ViewType.DATE_TIME),
            edit = @Edit(title = "创建时间",
                    show = false,
                    search = @Search(vague = true),
                    dateType = @DateType(type = DateType.Type.DATE_TIME))
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date createDate;

    @FabosJsonField(
            views = @View(title = "操作人"),
            edit = @Edit(
                    title = "操作人",
                    inputType = @InputType(length = 20)
            )
    )
    private String operator;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",
                    defaultVal = "WAIT_APPROVE",
                    type = EditType.CHOICE,
                    search = @Search,
                    choiceType = @ChoiceType(fetchHandler = ApproveTaskStatusEnum.class),
                    inputType = @InputType(length = 20)
            )
    )
    @Column(length = 20, nullable = false)
    private String status;


    public static ApproveTaskMTO create(PLSRApproveTask approveTask) {
        ApproveTaskMTO approveTaskMTO = new ApproveTaskMTO();
        BeanUtils.copyProperties(approveTask, approveTaskMTO);
        return approveTaskMTO;
    }
}
