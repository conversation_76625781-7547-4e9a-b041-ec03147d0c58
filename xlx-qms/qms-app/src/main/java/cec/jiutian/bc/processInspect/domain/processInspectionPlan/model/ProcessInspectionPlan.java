package cec.jiutian.bc.processInspect.domain.processInspectionPlan.model;

import cec.jiutian.bc.basicData.domain.inspectionStandard.model.InspectionStandard;
import cec.jiutian.bc.basicData.enumeration.NamingRuleCodeEnum;
import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleBaseModel;
import cec.jiutian.bc.processInspect.domain.processInspectionPlan.handler.IPQCPlanAddMaterialInspectionOperationHandler;
import cec.jiutian.bc.processInspect.domain.processInspectionPlan.handler.IPQCPlanDetailDynamicHandler;
import cec.jiutian.bc.processInspect.domain.processInspectionPlan.handler.IPQCPlanEndOperationHandler;
import cec.jiutian.bc.processInspect.domain.processInspectionPlan.handler.IPQCPlanExecuteTheFirstOperationHandler;
import cec.jiutian.bc.processInspect.domain.processInspectionPlan.handler.IPQCPlanExecuteTheLastOperationHandler;
import cec.jiutian.bc.processInspect.domain.processInspectionPlan.handler.IPQCPlanReleaseOperationHandler;
import cec.jiutian.bc.processInspect.domain.processInspectionPlan.mto.IPQCPlanAddMaterialInspectionMTO;
import cec.jiutian.bc.processInspect.domain.processInspectionPlan.mto.IPQCPlanExecuteTheFirstMTO;
import cec.jiutian.bc.processInspect.domain.processInspectionPlan.mto.IPQCPlanExecuteTheLastMTO;
import cec.jiutian.bc.processInspect.domain.processInspectionPlan.proxy.ProcessInspectionPlanProxy;
import cec.jiutian.bc.processInspect.enumeration.IPQCPlanStateEnum;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.ReferenceGenerateType;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.RowBaseOperation;
import cec.jiutian.view.type.RowOperation;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OrderBy;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@FabosJson(
        name = "过程检验计划",
        orderBy = "ProcessInspectionPlan.createTime desc",
        dataProxy = ProcessInspectionPlanProxy.class,
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "currentState!='EDIT'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "currentState!='EDIT'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                )
        },
        rowOperation = {
                @RowOperation(
                        title = "发布",
                        code = "ProcessInspectionPlan@RELEASE",
                        operationHandler = IPQCPlanReleaseOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "是否确定操作？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "ProcessInspectionPlan@RELEASE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "currentState != 'EDIT'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE
                ),
                @RowOperation(
                        title = "原材料首检",
                        code = "ProcessInspectionPlan@ADDMATERIAL",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        popupType = RowOperation.PopupType.FORM,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        fabosJsonClass = IPQCPlanAddMaterialInspectionMTO.class,
                        operationHandler = IPQCPlanAddMaterialInspectionOperationHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "ProcessInspectionPlan@ADDMATERIAL"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "selectedItems[0].currentState != 'EDIT' && selectedItems[0].currentState != 'IN_FIRST' && selectedItems[0].currentState != 'IN_PROCESS'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE
                ),
//                @RowOperation(
//                        title = "发布",
//                        code = "ProcessInspectionPlan@RELEASE",
//                        mode = RowOperation.Mode.SINGLE,
//                        type = RowOperation.Type.POPUP,
//                        popupType = RowOperation.PopupType.FORM,
//                        submitMethod = RowOperation.SubmitMethod.HANDLER,
//                        fabosJsonClass = IPQCPlanExecuteTheFirstMTO.class,
//                        operationHandler = IPQCPlanExecuteTheFirstOperationHandler.class,
//                        show = @ExprBool(
//                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
//                                params = "ProcessInspectionPlan@RELEASE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
//                        ),
//                        ifExpr = "currentState != 'EXECUTE' && currentState != 'EDIT'",
//                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE
//                ),
                @RowOperation(
                        title = "执行末检",
                        code = "ProcessInspectionPlan@LAST",
                        mode = RowOperation.Mode.SINGLE,
                        type = RowOperation.Type.POPUP,
                        popupType = RowOperation.PopupType.FORM,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        fabosJsonClass = IPQCPlanExecuteTheLastMTO.class,
                        operationHandler = IPQCPlanExecuteTheLastOperationHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "ProcessInspectionPlan@LAST"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "currentState != 'IN_PROCESS'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE
                ),
                @RowOperation(
                        title = "废弃",
                        code = "ProcessInspectionPlan@END",
                        operationHandler = IPQCPlanEndOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "是否确定操作？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "ProcessInspectionPlan@END"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "currentState != 'IN_FIRST' && currentState != 'IN_PROCESS' && currentState != 'IN_LAST'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE
                )
        }
)
@Table(name = "pi_inspection_plan",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Getter
@Setter
@TemplateType(type = "multiTable")
public class ProcessInspectionPlan extends NamingRuleBaseModel {

    @Override
    public String getNamingCode() {
        return NamingRuleCodeEnum.ProcessInspectionPlan.name();
    }

    @FabosJsonField(
            views = @View(title = "质检标准", column = "name"),
            edit = @Edit(title = "质检标准",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    filter = @Filter(value = "InspectionStandard.type = 'IPQC' and InspectionStandard.status = 'Effective'")
            )
    )
    @ManyToOne
    private InspectionStandard inspectionStandard;

    @FabosJsonField(
            views = @View(title = "工艺流程id", show = false),
            edit = @Edit(title = "工艺流程id", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inspectionStandard", beFilledBy = "technologyFlowId"))
    )
    private Long technologyFlowId;

    @FabosJsonField(
            views = @View(title = "产品编码"),
            edit = @Edit(title = "产品编码", readonly = @Readonly, search = @Search(vague = true)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inspectionStandard", beFilledBy = "materialCode"))
    )
    private String productCode;

    @FabosJsonField(
            views = @View(title = "产品名称"),
            edit = @Edit(title = "产品名称", readonly = @Readonly, search = @Search(vague = true)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inspectionStandard", beFilledBy = "materialName"))
    )
    private String productName;

    @FabosJsonField(
            views = @View(title = "车间id", show = false),
            edit = @Edit(title = "车间id", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inspectionStandard", beFilledBy = "factoryAreaId"))
    )
    private String factoryAreaId;

    @FabosJsonField(
            views = @View(title = "车间名称"),
            edit = @Edit(title = "车间名称", readonly = @Readonly, search = @Search(vague = true)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inspectionStandard", beFilledBy = "factoryAreaName"))
    )
    private String factoryAreaName;

    @FabosJsonField(
            views = @View(title = "产线id", show = false),
            edit = @Edit(title = "产线id", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inspectionStandard", beFilledBy = "factoryLineId"))
    )
    private String factoryLineId;

    @FabosJsonField(
            views = @View(title = "产线名称"),
            edit = @Edit(title = "产线名称", readonly = @Readonly, search = @Search(vague = true)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inspectionStandard", beFilledBy = "factoryLineName"))
    )
    private String factoryLineName;

    @FabosJsonField(
            views = @View(title = "工艺编码"),
            edit = @Edit(title = "工艺编码", readonly = @Readonly, search = @Search(vague = true)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inspectionStandard", beFilledBy = "processCode"))
    )
    private String processCode;

    @FabosJsonField(
            views = @View(title = "工艺名称"),
            edit = @Edit(title = "工艺名称", readonly = @Readonly, search = @Search(vague = true)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inspectionStandard", beFilledBy = "processName"))
    )
    private String processName;

    @FabosJsonField(
            views = @View(title = "当前状态"),
            edit = @Edit(title = "当前状态", show = false, type = EditType.CHOICE, search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = IPQCPlanStateEnum.class))
    )
    private String currentState;

    @FabosJsonField(
            views = @View(title = "备注"),
            edit = @Edit(title = "备注")
    )
    private String remark;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.EAGER)
    @JoinColumn(name = "inspection_plan_id")
    @OrderBy
    @FabosJsonField(
            edit = @Edit(title = "计划详情", type = EditType.TAB_REFERENCE_GENERATE),
            views = @View(title = "计划详情", type = ViewType.TABLE_VIEW),
            referenceGenerateType = @ReferenceGenerateType(editable = {"items"}),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = {"inspectionStandard"}, dynamicHandler = IPQCPlanDetailDynamicHandler.class))
    )
    private List<ProcessInspectionPlanDetail> details;
}
