package cec.jiutian.bc.inventoryInspection.statistics.controller;

import cec.jiutian.bc.inventoryInspection.statistics.service.FactoryAreaService;
import cec.jiutian.bc.mto.FactoryArea;
import cec.jiutian.core.frame.module.R;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/fabos-qms/param")
public class ParamQueryController {


    @Resource
    private FactoryAreaService factoryAreaService;

    @PostMapping("/area")
    public R<List<FactoryArea>> queryArea(@RequestBody List<Long> pIds) {

        List<FactoryArea> byPids = factoryAreaService.findByPids(pIds);

        return R.ok(byPids);
    }

}
