package cec.jiutian.bc.clientComplaintManagement.domain.clientComplaintFeedback.handler;

import cec.jiutian.bc.basicData.enumeration.NamingRuleCodeEnum;
import cec.jiutian.bc.clientComplaintManagement.domain.clientComplaintFeedback.mto.ProblemImprovementMTO;
import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/05/20
 * @description TODO
 */
@Component
public class ProblemImprovementMTODynamicHandler implements DependFiled.DynamicHandler<ProblemImprovementMTO> {

    @Resource
    private NamingRuleService namingRuleService;

    @Override
    public Map<String, Object> handle(ProblemImprovementMTO problemImprovementMTO) {
        Map<String, Object> map = new HashMap<>();
        List<String> result = namingRuleService.getNameCode(NamingRuleCodeEnum.PROBLEM_IMPROVEMENT.name(), 1, null);
        map.put("generalCode",result.get(0));
        return map;
    }
}
