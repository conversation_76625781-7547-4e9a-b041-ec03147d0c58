package cec.jiutian.bc.productExamineManagement.domain.examineImplementPlan.mto;

import cec.jiutian.bc.modeler.enumration.CheckResultEnum;
import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import jakarta.persistence.Entity;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/6/24
 * @description TODO
 */
@Entity
@Data
@FabosJson(
        name = "产品审核实施计划审核MTO"
)
public class ExamineImplementPlanResultMTO extends BaseModel {

    @FabosJsonField(
            views = @View(title = "审核年度计划号"),
            edit = @Edit(title = "审核年度计划号",readonly = @Readonly)
    )
    private String productExamineYearlyPlan;

    @FabosJsonField(
            views = @View(title = "审核实施计划号"),
            edit = @Edit(title = "审核实施计划号",readonly = @Readonly)
    )
    private String productExamineImplementPlan;

    @FabosJsonField(
            views = @View(title = "产品"),
            edit = @Edit(title = "产品",readonly = @Readonly)
    )
    private String productName;

    @FabosJsonField(
            views = @View(title = "样本量"),
            edit = @Edit(title = "样本量",readonly = @Readonly)
    )
    private Double sampleQuantity;

    @FabosJsonField(
            views = @View(title = "KZQ分数"),
            edit = @Edit(title = "KZQ分数",readonly = @Readonly)
    )
    private Double kqzScore;

    @FabosJsonField(
            views = @View(title = "审核结果"),
            edit = @Edit(title = "审核结果",
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = CheckResultEnum.class)
            )
    )
    private String examineResult;
}
