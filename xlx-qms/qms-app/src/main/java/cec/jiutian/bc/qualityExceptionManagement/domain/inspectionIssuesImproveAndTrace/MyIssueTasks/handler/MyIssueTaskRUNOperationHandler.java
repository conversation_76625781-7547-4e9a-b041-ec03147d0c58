package cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.MyIssueTasks.handler;

import cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.MyIssueTasks.model.MyIssueListCorrection;
import cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.MyIssueTasks.model.MyIssueTask;
import cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.MyIssueTasks.model.MyIssueTaskRUN;
import cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.issueList.model.IssueListCorrection;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class MyIssueTaskRUNOperationHandler implements OperationHandler<MyIssueTask, MyIssueTaskRUN> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<MyIssueTask> data, MyIssueTaskRUN modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            modelObject.getIssueListCorrections().forEach(d->{
                IssueListCorrection correction = fabosJsonDao.findById(IssueListCorrection.class,d.getId());
                correction.setProgress(d.getProgress());
                correction.setCompletionDate(d.getCompletionDate());
                correction.setCompletionEvidence(d.getCompletionEvidence());
                fabosJsonDao.mergeAndFlush(correction);
            });
        }
        return "msg.success('操作成功')";
    }

    /*@Override
    public MyIssueTaskRUN fabosJsonFormValue(List<MyIssueTask> data, MyIssueTaskRUN fabosJsonForm, String[] param) {
        // data为空 直接返回
        if (CollectionUtils.isEmpty(data)) {
            return fabosJsonForm;
        }

        String userId = UserContext.getUserId();
        MyIssueTask myIssueTask = data.get(0);
        BeanUtils.copyProperties(myIssueTask, fabosJsonForm);

        //如果list为空 直接返回
        if (CollectionUtils.isEmpty(myIssueTask.getIssueListCorrections())) {
            return fabosJsonForm;
        }

        List<MyIssueListCorrection> myIssueListCorrections = new ArrayList<>();
        myIssueTask.getIssueListCorrections().forEach(d->{
            if (Objects.equals(d.getMetaUser().getId(), userId)) {
                MyIssueListCorrection myIssueListCorrection = new MyIssueListCorrection();
                BeanUtils.copyProperties(d, myIssueListCorrection);
                myIssueListCorrections.add(myIssueListCorrection);
            }
        });

        fabosJsonForm.setIssueListCorrections(myIssueListCorrections);
        return fabosJsonForm;
    }*/
}
