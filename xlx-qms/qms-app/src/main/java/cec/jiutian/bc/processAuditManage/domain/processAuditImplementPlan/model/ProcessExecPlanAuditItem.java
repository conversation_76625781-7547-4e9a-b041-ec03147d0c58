package cec.jiutian.bc.processAuditManage.domain.processAuditImplementPlan.model;

import cec.jiutian.bc.processAuditManage.domain.processAuditImplementPlan.enumeration.ProcessExecAnalysisMethodEnum;
import cec.jiutian.bc.processAuditManage.domain.processAuditImplementPlan.enumeration.ProcessExecComplianceStatusEnum;
import cec.jiutian.bc.processAuditManage.domain.processAuditImplementPlan.enumeration.ProcessExecSeverityEnum;
import cec.jiutian.bc.urm.domain.org.entity.Org;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.frame.module.MetaUser;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@FabosJson(
        name = "执行-审核项",
        orderBy = "ProcessExecPlanAuditItem.createTime desc"
)
@Table(name = "qms_sam_process_plan_audit_item")
@Entity
@Getter
@Setter
public class ProcessExecPlanAuditItem extends MetaModel {
    @FabosJsonField(
            views = @View(title = "审核实施计划", column = "generalCode", show = false),
            edit = @Edit(title = "审核实施计划", type = EditType.REFERENCE_TABLE, show = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode")
            )
    )
    @ManyToOne
    @JsonIgnoreProperties("processPlanAuditItemList")
    private ProcessAuditImplementPlan auditImplementPlan;

    @FabosJsonField(
            views = @View(title = "审核项管理id", show = false),
            edit = @Edit(title = "审核项管理id", show = false)
    )
    private String auditItemManageId;

    /*// 审核项名称
    @FabosJsonField(
            views = @View(title = "审核项名称"),
            edit = @Edit(title = "审核项名称", notNull = true)
    )
    private String auditItemName;

    // 过程名称
    @FabosJsonField(
            views = @View(title = "过程名称"),
            edit = @Edit(title = "过程名称", notNull = true)
    )
    private String processName;

    // 过程要素
    @FabosJsonField(
            views = @View(title = "过程要素"),
            edit = @Edit(title = "过程要素", notNull = true)
    )
    private String processElements;

    // 最低要求
    @FabosJsonField(
            views = @View(title = "最低要求"),
            edit = @Edit(title = "最低要求", notNull = true)
    )
    private String minRequires;

    // 主要涉及部门/场地
    @ManyToOne
    @FabosJsonField(
            views = @View(title = "主要涉及部门/场地", column = "name"),
            edit = @Edit(title = "主要涉及部门/场地",notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"))
    )
    private Org department;

    // 原材料出入库
    @FabosJsonField(
            views = @View(title = "原材料出入库"),
            edit = @Edit(title = "原材料出入库")
    )
    private String materialStorageControl;

    // 一烧前配混料
    @FabosJsonField(
            views = @View(title = "一烧前配混料"),
            edit = @Edit(title = "一烧前配混料")
    )
    private String preFirstBurnMixing;

    // 一次烧结
    @FabosJsonField(
            views = @View(title = "一次烧结"),
            edit = @Edit(title = "一次烧结")
    )
    private String firstSintering;

    // 一次粉体处理
    @FabosJsonField(
            views = @View(title = "一次粉体处理"),
            edit = @Edit(title = "一次粉体处理")
    )
    private String firstCrushingProcess;

    // 水洗
    @FabosJsonField(
            views = @View(title = "水洗"),
            edit = @Edit(title = "水洗")
    )
    private String washingProcess;

    // 二烧前配混料
    @FabosJsonField(
            views = @View(title = "二烧前配混料"),
            edit = @Edit(title = "二烧前配混料")
    )
    private String preSecondBurnMixing;

    // 二次烧结
    @FabosJsonField(
            views = @View(title = "二次烧结"),
            edit = @Edit(title = "二次烧结")
    )
    private String secondSintering;

    // 二次粉体处理
    @FabosJsonField(
            views = @View(title = "二次粉体处理"),
            edit = @Edit(title = "二次粉体处理")
    )
    private String finalCrushingProcess;

    // 检验
    @FabosJsonField(
            views = @View(title = "检验"),
            edit = @Edit(title = "检验")
    )
    private String inspection;

    // 包装出库
    @FabosJsonField(
            views = @View(title = "包装出库"),
            edit = @Edit(title = "包装出库")
    )
    private String packagingSpec;*/


    //执行新增内容

    /** 审核结果 */
    @FabosJsonField(
            views = @View(title = "审核结果"),
            edit = @Edit(title = "审核结果", notNull = true)
    )
    private String auditResult;

    /** 符合情况 */
    @FabosJsonField(
            views = @View(title = "符合情况"),
            edit = @Edit(title = "符合情况",type = EditType.CHOICE, notNull = true,
                    choiceType = @ChoiceType(fetchHandler = ProcessExecComplianceStatusEnum.class))
    )
    private String complianceStatus;

    /** 问题描述*/
    @FabosJsonField(
            views = @View(title = "问题描述"),
            edit = @Edit(title = "问题描述")
    )
    private String issueDescription;

    /** 责任人 */
    @ManyToOne
    @FabosJsonField(
            views = @View(title = "责任人", column = "name"),
            edit = @Edit(title = "责任人",notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"))
    )
    @JoinColumn(foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private MetaUser responsiblePerson;

    /** 主要责任部门 */
    @ManyToOne
    @FabosJsonField(
            views = @View(title = "主要责任部门", column = "name"),
            edit = @Edit(title = "主要责任部门",notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"))
    )
    @JoinColumn(foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private Org org;

    /** 严重程度 */
    @FabosJsonField(
            views = @View(title = "严重程度"),
            edit = @Edit(title = "严重程度",type = EditType.CHOICE,notNull = true,
                    choiceType = @ChoiceType(fetchHandler = ProcessExecSeverityEnum.class))
    )
    private String severity;

    /** 分析方法 */
    @FabosJsonField(
            views = @View(title = "分析方法"),
            edit = @Edit(title = "分析方法",type = EditType.CHOICE,notNull = true,
                    choiceType = @ChoiceType(fetchHandler = ProcessExecAnalysisMethodEnum.class))
    )
    private String analysisMethod;

    /** 验证人 */
    @ManyToOne
    @FabosJsonField(
            views = @View(title = "验证人", column = "name"),
            edit = @Edit(title = "验证人",notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"))
    )
    @JoinColumn(foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private MetaUser verifier;

    /** 确定措施纳期 */
    @FabosJsonField(
            views = @View(title = "确定措施纳期"),
            edit = @Edit(title = "确定措施纳期", notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE_TIME))
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date measureDeadline;

    /** 完成纳期（ */
    @FabosJsonField(
            views = @View(title = "完成纳期"),
            edit = @Edit(title = "完成纳期", notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE_TIME))
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date completionDeadline;

    /** 附件 */
    @FabosJsonField(
            views = @View(title = "附件"),
            edit = @Edit(title = "附件",
                    type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(size = 5120, maxLimit = 10, type = AttachmentType.Type.BASE)
            )
    )
    private String execAttachments;

    @Transient
    @FabosJsonField(
            edit = @Edit(title = "请完成打分", type = EditType.DIVIDE)
    )
    private String divide;

    // 原材料出入库
    @FabosJsonField(
            views = @View(title = "原材料出入库"),
            edit = @Edit(title = "原材料出入库", notNull = true,
                    numberType = @NumberType(min = 0, max = 100, precision = 2), tips = "打分范围:0-100")
    )
    private String scoreMaterialStorageControl;

    // 一烧前配混料
    @FabosJsonField(
            views = @View(title = "一烧前配混料"),
            edit = @Edit(title = "一烧前配混料", notNull = true,
                    numberType = @NumberType(min = 0, max = 100, precision = 2), tips = "打分范围:0-100")
    )
    private String scorePreFirstBurnMixing;

    // 一次烧结
    @FabosJsonField(
            views = @View(title = "一次烧结"),
            edit = @Edit(title = "一次烧结", notNull = true,
                    numberType = @NumberType(min = 0, max = 100, precision = 2), tips = "打分范围:0-100")
    )
    private String scoreFirstSintering;

    // 一次粉体处理
    @FabosJsonField(
            views = @View(title = "一次粉体处理"),
            edit = @Edit(title = "一次粉体处理", notNull = true,
                    numberType = @NumberType(min = 0, max = 100, precision = 2), tips = "打分范围:0-100")
    )
    private String scoreFirstCrushingProcess;

    // 水洗
    @FabosJsonField(
            views = @View(title = "水洗"),
            edit = @Edit(title = "水洗", notNull = true,
                    numberType = @NumberType(min = 0, max = 100, precision = 2), tips = "打分范围:0-100")
    )
    private String scoreWashingProcess;

    // 二烧前配混料
    @FabosJsonField(
            views = @View(title = "二烧前配混料"),
            edit = @Edit(title = "二烧前配混料", notNull = true,
                    numberType = @NumberType(min = 0, max = 100, precision = 2), tips = "打分范围:0-100")
    )
    private String scorePreSecondBurnMixing;

    // 二次烧结
    @FabosJsonField(
            views = @View(title = "二次烧结"),
            edit = @Edit(title = "二次烧结", notNull = true,
                    numberType = @NumberType(min = 0, max = 100, precision = 2), tips = "打分范围:0-100")
    )
    private String scoreSecondSintering;

    // 二次粉体处理
    @FabosJsonField(
            views = @View(title = "二次粉体处理"),
            edit = @Edit(title = "二次粉体处理", notNull = true,
                    numberType = @NumberType(min = 0, max = 100, precision = 2), tips = "打分范围:0-100")
    )
    private String scoreFinalCrushingProcess;

    // 检验
    @FabosJsonField(
            views = @View(title = "检验"),
            edit = @Edit(title = "检验", notNull = true,
                    numberType = @NumberType(min = 0, max = 100, precision = 2), tips = "打分范围:0-100")
    )
    private String scoreInspection;

    // 包装出库
    @FabosJsonField(
            views = @View(title = "包装出库"),
            edit = @Edit(title = "包装出库", notNull = true,
            numberType = @NumberType(min = 0, max = 100, precision = 2), tips = "打分范围:0-100")
    )
    private String scorePackagingSpec;
}
