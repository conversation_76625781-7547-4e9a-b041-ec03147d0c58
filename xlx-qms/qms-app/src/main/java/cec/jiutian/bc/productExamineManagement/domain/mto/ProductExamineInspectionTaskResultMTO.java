package cec.jiutian.bc.productExamineManagement.domain.mto;

import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.ReferenceGenerateType;
import cec.jiutian.view.field.*;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToMany;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/24
 * @description TODO
 */
@Entity
@Getter
@Setter
@FabosJson(
        name = "产品审核检验任务结果审核MTO"
)
public class ProductExamineInspectionTaskResultMTO extends BaseModel {
    @FabosJsonField(
            views = @View(title = "产品"),
            edit = @Edit(title = "产品",readonly = @Readonly)
    )
    private String productName;

    @FabosJsonField(
            views = @View(title = "取样批次"),
            edit = @Edit(title = "取样批次",readonly = @Readonly)
    )
    private String lotSerialId;

    @FabosJsonField(
            views = @View(title = "样本量"),
            edit = @Edit(title = "样本量",readonly = @Readonly)
    )
    private Double sampleQuantity;

    @FabosJsonField(
            views = @View(title = "车间"),
            edit = @Edit(title = "车间",readonly = @Readonly)
    )
    private String workShopName;

    @FabosJsonField(
            views = @View(title = "产线"),
            edit = @Edit(title = "产线",readonly = @Readonly)
    )
    private String productLineName;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = false)
    @FabosJsonField(
            views = @View(title = "检验项目明细", type = ViewType.TABLE_VIEW, index = 8),
            edit = @Edit(title = "检验项目明细", type = EditType.TAB_REFERENCE_GENERATE),
            referenceGenerateType = @ReferenceGenerateType(editable =  {"inspectionResult","unQualifiedLevel"})
    )
    @JoinColumn(name = "inspection_task_id")
    private List<ProductExamineInspectionItemDetailResultMTO> detailResultMTOList;
}
