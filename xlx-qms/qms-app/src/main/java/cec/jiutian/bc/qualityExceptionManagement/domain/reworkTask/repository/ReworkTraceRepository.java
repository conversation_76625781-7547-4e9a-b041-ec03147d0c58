package cec.jiutian.bc.qualityExceptionManagement.domain.reworkTask.repository;

import cec.jiutian.bc.qualityExceptionManagement.domain.reworkTask.model.ReworkTrace;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ReworkTraceRepository extends JpaRepository<ReworkTrace, String> {

    List<ReworkTrace> findByInspectionResultOrderByCreateTime(String inspectionResult);

}
