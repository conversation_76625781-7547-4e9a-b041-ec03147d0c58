package cec.jiutian.bc.processAuditManage.domain.processAuditImplementPlan.handler;

import cec.jiutian.bc.processAuditManage.domain.processAuditImplementPlan.enumeration.ProcessAuditImplementPlanStatusEnum;
import cec.jiutian.bc.processAuditManage.domain.processAuditImplementPlan.model.ProcessAuditImplementPlan;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class ProcessAuditImplementPlanCancelHandler implements OperationHandler<ProcessAuditImplementPlan, Void> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<ProcessAuditImplementPlan> data, Void modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            ProcessAuditImplementPlan processAuditImplementPlan = data.get(0);
            processAuditImplementPlan.setStatus(ProcessAuditImplementPlanStatusEnum.Enum.CREATED.name());
            fabosJsonDao.mergeAndFlush(processAuditImplementPlan);
        }
        return "msg.success('操作成功')";
    }
}
