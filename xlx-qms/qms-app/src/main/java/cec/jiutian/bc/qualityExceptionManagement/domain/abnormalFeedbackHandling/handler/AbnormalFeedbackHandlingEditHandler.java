package cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.handler;

import cec.jiutian.bc.materialInspect.domain.publicInspectionTask.mto.UserForInsTaskMTO;
import cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.model.AbnormalFeedbackHandling;
import cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.mto.AbnormalFeedbackHandlingCreateMTO;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/27
 * @description TODO
 */
@Component
public class AbnormalFeedbackHandlingEditHandler implements OperationHandler<AbnormalFeedbackHandling, AbnormalFeedbackHandlingCreateMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<AbnormalFeedbackHandling> data, AbnormalFeedbackHandlingCreateMTO modelObject, String[] param) {
        if (modelObject != null) {
            AbnormalFeedbackHandling model = data.get(0);
            model.setCreatedTime(modelObject.getCreatedTime());
            model.setAbnormalDescription(modelObject.getAbnormalDescription());
            model.setSubmissionTime(modelObject.getSubmissionTime());
            model.setDiscoveredPersonId(modelObject.getDiscoveredPersonId());
            model.setDiscoveredPersonName(modelObject.getDiscoveredPersonName());
            model.setAbnormalAttachments(modelObject.getAbnormalAttachments());

            fabosJsonDao.mergeAndFlush(model);
        }
        return "alert(操作成功)";
    }

    @Override
    public AbnormalFeedbackHandlingCreateMTO fabosJsonFormValue(List<AbnormalFeedbackHandling> data, AbnormalFeedbackHandlingCreateMTO fabosJsonForm, String[] param) {
        AbnormalFeedbackHandling abnormalFeedbackHandling = data.get(0);
        BeanUtil.copyProperties(abnormalFeedbackHandling, fabosJsonForm);

        if (fabosJsonForm.getDiscoveredPersonId()!= null) {
            UserForInsTaskMTO discoveredPerson = fabosJsonDao.findById(UserForInsTaskMTO.class, fabosJsonForm.getDiscoveredPersonId());
            if (discoveredPerson != null) {
                fabosJsonForm.setDiscoveredPerson(discoveredPerson);
            } else {
                throw new FabosJsonApiErrorTip("未查到发现人数据，请确认");
            }
        }
        return fabosJsonForm;
    }
}
