package cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.issueList.handler;

import cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.issueList.model.IssueList;
import cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.issueList.model.IssueListCorrection;
import cec.jiutian.bc.qualityExceptionManagement.enumeration.inspectionIssuesImproveAndTrace.IssueListStatusEnum;
import cec.jiutian.bc.qualityExceptionManagement.enumeration.inspectionIssuesImproveAndTrace.ProgressEnum;
import cec.jiutian.bc.qualityExceptionManagement.enumeration.inspectionIssuesImproveAndTrace.VerifyResultEnum;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class VerSubmitIssueListOprHandler implements OperationHandler<IssueList, Void> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<IssueList> data, Void modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            IssueList issueList = data.get(0);
            //判断明细是否都已经验证  如果存在未验证的  不允许提交
            issueList.getIssueListCorrections().forEach(d -> {
                if (StringUtils.isBlank(d.getVerificationResult())) {
                    throw new FabosJsonApiErrorTip("提交失败：整改措施全部验证完毕才能提交");
                }
            });
            //如果全部验证通过  问题清单改为完成    如果存在一个未通过  状态改为 执行中
            String curStatus = IssueListStatusEnum.Enum.FINISH.name();
            for (IssueListCorrection correction : issueList.getIssueListCorrections()) {
                if (VerifyResultEnum.Enum.FAILED.name().equals(correction.getVerificationResult())) {
                    curStatus =IssueListStatusEnum.Enum.RUNNING.name();
                    break;
                }
            }
            issueList.setStatus(curStatus);
            //如果主单据是执行中 则修改整改措施为 已执行 只需要在我们整改任务里面 重新提交数据
            //这里如果改为 执行中  整改措施完成相关数据和验证相关的数据  都不要清除  直接回显
            if (curStatus.equals(IssueListStatusEnum.Enum.RUNNING.name())) {
                issueList.getIssueListCorrections().forEach(d -> {
                    d.setProgress(ProgressEnum.Enum.RUN_SUBMIT.name());
                });
            }
            if (curStatus.equals(IssueListStatusEnum.Enum.FINISH.name())) {
                issueList.getIssueListCorrections().forEach(d -> {
                    d.setProgress(ProgressEnum.Enum.FINISH.name());
                });
            }
            fabosJsonDao.mergeAndFlush(issueList);
        }
        return "msg.success('操作成功')";
    }
}
