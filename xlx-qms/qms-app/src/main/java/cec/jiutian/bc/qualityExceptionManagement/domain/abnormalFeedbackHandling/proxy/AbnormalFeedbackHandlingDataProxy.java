package cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.proxy;

import cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.model.AbnormalFeedbackHandling;
import cec.jiutian.view.config.QueryExpression;
import cec.jiutian.view.fun.DataProxy;
import cec.jiutian.view.query.Condition;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/25
 * @description TODO
 */
@Component
public class AbnormalFeedbackHandlingDataProxy implements DataProxy<AbnormalFeedbackHandling> {

    @Override
    public String beforeFetch(List<Condition> conditions) {
        String startTime = null;
        String endTime = null;
        for (Condition condition : conditions) {
            if (condition.getKey().equals("startTime") && condition.getValue() != null) {
                startTime = condition.getValue().toString();
            }
            if (condition.getKey().equals("endTime") && condition.getValue() != null) {
                endTime = condition.getValue().toString();
            }
        }

        if (null != startTime && null != endTime) {
            String timeRange = startTime + "," + endTime;
            Condition condition = new Condition("createdTime", timeRange, QueryExpression.RANGE);
            conditions.add(condition);
        }
        return DataProxy.super.beforeFetch(conditions);
    }

}
