package cec.jiutian.bc.productionStartupRelease.domain.AproveTask.mto;

import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleBaseModel;
import cec.jiutian.bc.mto.ProductProcessMTO;
import cec.jiutian.bc.productionStartupRelease.domain.productionStartupRelease.model.ReviewResult;
import cec.jiutian.bc.productionStartupRelease.domain.productionStartupRelease.mto.QualityTraceForm;
import cec.jiutian.bc.productionStartupRelease.enums.*;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.TabTableReferType;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowBaseOperation;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;


@Getter
@Setter
@Entity
@Table(name = "qms_plsr",
        indexes = {
                @Index(name = "general_code", columnList = "general_code", unique = true)
        })
@FabosJson(
        name = "生产放行",
        orderBy = "createTime desc",
        power = @Power(examine = false, examineDetails = false, export = false, importable = false),
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "add",
                        ifExpr = "1 != 1",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.HIDE
                ),
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "1 != 1",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.HIDE
                ),
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "currentState != 'TO_BE_PUBLISHED'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.HIDE
                )
        }
)
@TemplateType(type = "multiTable")
public class PLSRReportMTO extends NamingRuleBaseModel {

    @ManyToOne
    @JoinColumn(name = "quality_trace_form_id")
    @FabosJsonField(
            views = @View(title = "品质跟踪单", column = "generalCode"),
            edit = @Edit(title = "品质跟踪单",
                    readonly = @Readonly,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode")
            )
    )
    private QualityTraceForm qualityTraceForm;

    @FabosJsonField(
            views = @View(title = "车间"),
            edit = @Edit(title = "车间",
                    readonly = @Readonly)
    )
    @Column(length = 64)
    private String workshopName;

    @FabosJsonField(
            views = @View(title = "产线"),
            edit = @Edit(title = "产线", readonly = @Readonly)
    )
    @Column(length = 64)
    private String lineName;

    @FabosJsonField(
            views = @View(title = "产品类型"),
            edit = @Edit(title = "产品类型",
                    type = EditType.CHOICE,
                    readonly = @Readonly,
                    choiceType = @ChoiceType(fetchHandler = {ProductionTypeEnum.class}))
    )
    @Column(nullable = false, length = 32)
    private String productionType;

    @FabosJsonField(
            views = @View(title = "放行等级"),
            edit = @Edit(title = "放行等级",
                    readonly = @Readonly,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ReleaseLevelEnum.class)
            )
    )
    @Column(nullable = false, length = 32)
    private String releaseLevel;

    @FabosJsonField(
            views = @View(title = "主导部门"),
            edit = @Edit(title = "主导部门",
                    readonly = @Readonly
            )
    )
    @Column(length = 64)
    private String department;

    @FabosJsonField(
            views = @View(title = "评审主导人"),
            edit = @Edit(title = "评审主导人",
                    readonly = @Readonly
            )
    )
    @Column(length = 64)
    private String userName;

    @FabosJsonField(
            views = @View(title = "生产线类型"),
            edit = @Edit(title = "生产线类型",
                    readonly = @Readonly,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = {LineTypeEnum.class})
            )
    )
    @Column(nullable = false, length = 32)
    private String lineType;

    @FabosJsonField(
            views = @View(title = "产品阶段"),
            edit = @Edit(title = "产品阶段",
                    readonly = @Readonly,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = {ProductionPeriodEnum.class})
            )
    )
    @Column(nullable = false, length = 32)
    private String productionPeriod;

    @ManyToMany
    @JoinTable(name = "qms_plsr_process",
            inverseForeignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT),
            joinColumns = @JoinColumn(name = "plsr_id"),
            inverseJoinColumns = @JoinColumn(name = "process_id"))
    @FabosJsonField(
            views = @View(title = "放行工序段", column = "id", type = ViewType.TABLE_VIEW),
            edit = @Edit(title = "放行工序段",
                    notNull = true,
                    tabTableReferType = @TabTableReferType(type = TabTableReferType.SelectShowTypeMTM.LIST),
                    type = EditType.TAB_TABLE_REFER
            )
    )
    private List<ProductProcessMTO> process;

    @FabosJsonField(
            views = @View(title = "生产线类型"),
            edit = @Edit(title = "生产线类型",
                    readonly = @Readonly,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = {SwitchTypeEnum.class})
            )
    )
    @Column(nullable = false, length = 32)
    private String switchType;

    @FabosJsonField(
            views = @View(title = "生产线类型"),
            edit = @Edit(title = "生产线类型",
                    readonly = @Readonly,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = {ProductJudgeEnum.class})
            )
    )
    @Column(nullable = false, length = 32)
    private String productJudge;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "plsr_id")
    @FabosJsonField(
            views = @View(title = "评审详情", column = "reviewResult", type = ViewType.TABLE_VIEW),
            edit = @Edit(
                    readonly = @Readonly,
                    title = "评审详情",
                    type = EditType.TAB_TABLE_ADD,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType(label = "reviewResult")
            )
    )
    private List<ReviewResult> results;


    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",
                    readonly = @Readonly(add = true, edit = true),
                    defaultVal = "EDIT",
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = {StatusEnum.class})
            )
    )
    @Column(nullable = false, length = 16)
    private String status;
}
