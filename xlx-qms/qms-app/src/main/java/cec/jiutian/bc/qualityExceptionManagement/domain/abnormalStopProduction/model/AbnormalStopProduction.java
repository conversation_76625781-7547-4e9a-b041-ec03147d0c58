package cec.jiutian.bc.qualityExceptionManagement.domain.abnormalStopProduction.model;

import cec.jiutian.bc.basicData.enumeration.NamingRuleCodeEnum;
import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleModel;
import cec.jiutian.bc.generalModeler.enumeration.YesOrNoEnum;
import cec.jiutian.bc.mto.OrgMTO;
import cec.jiutian.bc.mto.OrderBatchSerialMTO;
import cec.jiutian.bc.qualityExceptionManagement.domain.abnormalStopProduction.handler.AbnormalStopProductionAnalysisSubmitOperationHandler;
import cec.jiutian.bc.qualityExceptionManagement.domain.abnormalStopProduction.handler.AbnormalStopProductionReleaseOperationHandler;
import cec.jiutian.bc.qualityExceptionManagement.domain.abnormalStopProduction.proxy.AbnormalStopProductionDataProxy;
import cec.jiutian.bc.qualityExceptionManagement.domain.abnormalStopProduction.proxy.AbnormalStopProductionFlowProxy;
import cec.jiutian.bc.qualityExceptionManagement.enums.AbnormalStopProductionStateEnum;
import cec.jiutian.bc.qualityExceptionManagement.enums.AbnormalTypeEnum;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.AttachmentType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DateType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowBaseOperation;
import cec.jiutian.view.type.RowOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Entity
@Table(name = "qem_abnormal_stop_production",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        })
@Getter
@Setter
@FabosJson(
        name = "异常停产单",
        dataProxy = AbnormalStopProductionDataProxy.class,
        power = @Power(examine = true, examineDetails = true, export = false),
        flowCode = "AbnormalStopProduction",
        flowProxy = AbnormalStopProductionFlowProxy.class,
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "examine",
                        ifExpr = "!(currentState =='WaitReview' && examineStatus =='0')",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "examineDetails",
                        ifExpr = "examineStatus =='0'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "currentState !='Edit'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "currentState !='Edit'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
        },
        rowOperation = {
                @RowOperation(
                        title = "发布",
                        code = "AbnormalStopProduction@RELEASE",
                        operationHandler = AbnormalStopProductionReleaseOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "是否确定操作？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "AbnormalStopProduction@RELEASE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "currentState != 'Edit'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE
                ),
                @RowOperation(
                        title = "原因分析",
                        code = "AbnormalStopProduction@ANALYSE",
                        mode = RowOperation.Mode.SINGLE,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.UPDATE,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = AbnormalStopProductionAnalyse.class,
                        ifExpr = "currentState != 'WaitAnalyse'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "AbnormalStopProduction@ANALYSE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
                @RowOperation(
                        title = "分析提交",
                        code = "AbnormalStopProduction@ANALYSISSUBMIT",
                        operationHandler = AbnormalStopProductionAnalysisSubmitOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "是否确定操作？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "AbnormalStopProduction@ANALYSISSUBMIT"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "currentState != 'WaitAnalyse'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE
                ),
        }
)
public class AbnormalStopProduction extends NamingRuleModel {

    @Override
    public String getNamingCode() {
        return NamingRuleCodeEnum.AbnormalStopProduction.name();
    }

    @FabosJsonField(
            views = @View(title = "现状与原因分析"),
            edit = @Edit(title = "现状与原因分析", notNull = true, type = EditType.TEXTAREA)
    )
    private String situationAnalysis;

    @FabosJsonField(
            views = @View(title = "异常类型"),
            edit = @Edit(title = "异常类型", notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = AbnormalTypeEnum.class))
    )
    private String abnormalType;

    // 查询MES在制品批次详情
    @Transient
    @FabosJsonField(
            views = @View(title = "生产批次", column = "serialNumber", show = false),
            edit = @Edit(title = "生产批次",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    filter = @Filter(value = "processCode = 'CP'"),
                    referenceTableType = @ReferenceTableType(id = "id", label = "serialNumber")
            )
    )
    private OrderBatchSerialMTO orderBatchSerialMTO;

    @FabosJsonField(
            views = @View(title = "生产批id", show = false),
            edit = @Edit(title = "生产批id", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "orderBatchSerialMTO", beFilledBy = "id"))
    )
    private String productLotId;

    @FabosJsonField(
            views = @View(title = "生产批号"),
            edit = @Edit(title = "生产批号", show = false, search = @Search(vague = true)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "orderBatchSerialMTO", beFilledBy = "serialNumber"))
    )
    private String lotSerialId;

    @FabosJsonField(
            views = @View(title = "产品名称"),
            edit = @Edit(title = "产品名称", readonly = @Readonly, search = @Search(vague = true)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "orderBatchSerialMTO", beFilledBy = "materialName"))
    )
    private String productName;

    @FabosJsonField(
            views = @View(title = "产品编号"),
            edit = @Edit(title = "产品编号", readonly = @Readonly, search = @Search(vague = true)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "orderBatchSerialMTO", beFilledBy = "materialCode"))
    )
    private String productCode;

    @FabosJsonField(
            views = @View(title = "型号规格"),
            edit = @Edit(title = "型号规格", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "orderBatchSerialMTO", beFilledBy = "materialDetail"))
    )
    private String specification;

    @Transient
    @FabosJsonField(
            views = @View(title = "发起部门", column = "name", show = false),
            edit = @Edit(title = "发起部门", notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false
            )
    )
    private OrgMTO orgMTO;

    @FabosJsonField(
            views = @View(title = "发起部门id", show = false),
            edit = @Edit(title = "发起部门id", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "orgMTO", beFilledBy = "id"))
    )
    private String departmentId;

    @FabosJsonField(
            views = @View(title = "发起部门"),
            edit = @Edit(title = "发起部门", search = @Search(vague = true), show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "orgMTO", beFilledBy = "name"))
    )
    private String departmentName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "发起时间", type = ViewType.DATE),
            edit = @Edit(title = "发起时间", notNull = true, dateType = @DateType(type = DateType.Type.DATE))
    )
    private LocalDateTime initiationTime;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态", show = false, search = @Search,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = AbnormalStopProductionStateEnum.class))
    )
    private String currentState;

    @FabosJsonField(
            views = @View(title = "附件"),
            edit = @Edit(title = "附件", type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(maxLimit = 10))
    )
    private String attachment;

    @FabosJsonField(
            views = @View(title = "设备动力部分析"),
            edit = @Edit(title = "设备动力部分析", show = false, type = EditType.TEXTAREA)
    )
    private String equipmentAnalysis;

    @FabosJsonField(
            views = @View(title = "设备动力部意见(是否停产)"),
            edit = @Edit(title = "设备动力部意见(是否停产)", show = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = YesOrNoEnum.class))
    )
    private String equipmentStopFlag;

    @FabosJsonField(
            views = @View(title = "正极材料生产车间分析"),
            edit = @Edit(title = "正极材料生产车间分析", show = false, type = EditType.TEXTAREA)
    )
    private String workshopAnalysis;

    @FabosJsonField(
            views = @View(title = "正极材料生产车间意见(是否停产)"),
            edit = @Edit(title = "正极材料生产车间意见(是否停产)", show = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = YesOrNoEnum.class))
    )
    private String workshopStopFlag;

    @FabosJsonField(
            views = @View(title = "工艺技术部分析"),
            edit = @Edit(title = "工艺技术部分析", show = false, type = EditType.TEXTAREA)
    )
    private String processAnalysis;

    @FabosJsonField(
            views = @View(title = "工艺技术部意见(是否停产)"),
            edit = @Edit(title = "工艺技术部意见(是否停产)", show = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = YesOrNoEnum.class))
    )
    private String processStopFlag;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "停产时间", type = ViewType.DATE),
            edit = @Edit(title = "停产时间", show = false, dateType = @DateType(type = DateType.Type.DATE))
    )
    private LocalDateTime stopTime;


}
