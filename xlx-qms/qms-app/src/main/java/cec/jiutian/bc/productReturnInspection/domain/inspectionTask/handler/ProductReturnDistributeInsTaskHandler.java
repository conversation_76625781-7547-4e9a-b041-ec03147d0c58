package cec.jiutian.bc.productReturnInspection.domain.inspectionTask.handler;

import cec.jiutian.bc.modeler.enumration.TaskBusinessStateEnum;
import cec.jiutian.bc.productReturnInspection.domain.inspectionTask.model.ProductReturnInspectionTask;
import cec.jiutian.bc.productReturnInspection.domain.publicInspectionTask.mto.ProductReturnDistributeInsTaskMTO;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.core.frame.module.MetaUser;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import jakarta.persistence.LockModeType;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Component
public class ProductReturnDistributeInsTaskHandler implements OperationHandler<ProductReturnInspectionTask, ProductReturnDistributeInsTaskMTO> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    @Transactional
    public String exec(List<ProductReturnInspectionTask> data, ProductReturnDistributeInsTaskMTO modelObject, String[] param) {
        if (modelObject == null) {
        throw new ServiceException("参数错误");
        }
        String inspector = modelObject.getUserId();
        if (StringUtils.isEmpty(inspector)) {
            throw new ServiceException("请选择检验人");
        }

        MetaUser metaUser = fabosJsonDao.findById(MetaUser.class, inspector);
        if (metaUser == null) {
            throw new ServiceException("选择的用户不存在");
        }
        ProductReturnInspectionTask task = fabosJsonDao.getEntityManager().find(ProductReturnInspectionTask.class, modelObject.getId(), LockModeType.PESSIMISTIC_WRITE);
        if (task.getUserId() != null || task.getUserName() != null) {
            throw new ServiceException("该任务已被分配/领取");
        }
        task.setUserName(metaUser.getName());
        task.setUserId(metaUser.getId());
        task.setBusinessState(TaskBusinessStateEnum.Enum.INSPECTING.name());
        task.setUpdateBy(UserContext.getUserName());
        task.setUpdateTime(LocalDateTime.now());
        fabosJsonDao.updateAndFlush(task);
        return"操作成功";
}
}
