package cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.issueList.proxy;

import cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.issueList.model.IssueList;
import cec.jiutian.bc.qualityExceptionManagement.enumeration.inspectionIssuesImproveAndTrace.IssueListStatusEnum;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

@Component
public class IssueListDataProxy implements DataProxy<IssueList> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public void afterFetch(Collection<Map<String, Object>> list) {
        if (CollectionUtils.isNotEmpty(list)) {
            for (Map<String, Object> d : list) {
                if (Objects.equals(IssueListStatusEnum.Enum.WAIT_PUBLISH.name(), d.get("status"))) {
                    continue;
                }
                String userId = UserContext.getUserId();
                Map<String,String> metaUser = (Map<String, String>)d.get("metaUser");
                if(userId.equals(metaUser.get("id").toString())){
                    d.put("rowOperationAuthFlag",1);
                }
            }
        }
    }

    @Override
    public void afterSingleFetch(Map<String, Object> map) {
        Map<String,Object> ehsMap = new HashMap<>();
        ehsMap.put("id",map.get("onSiteInspectionTaskId"));
        ehsMap.put("generalCode",map.get("taskGeneralCode"));
        map.put("onSiteInspectionTaskDTO",ehsMap);
        map.put("onSiteInspectionTaskDTO_generalCode",map.get("taskGeneralCode"));
        List<HashMap<String,Object>> ehsQuestionCorrections = (List<HashMap<String,Object>>) map.get("issueListCorrections");
        UserContext.CurrentUser currentUser = UserContext.get();
        HashMap<String,Object> userMap = new HashMap<>();
        userMap.put("id",currentUser.getUserId());
        userMap.put("name",currentUser.getUserName());
        ehsQuestionCorrections.forEach(d->{
            d.putIfAbsent("verifyMetaUser", userMap);
        });
        map.put("issueListCorrections",ehsQuestionCorrections);
    }
}
