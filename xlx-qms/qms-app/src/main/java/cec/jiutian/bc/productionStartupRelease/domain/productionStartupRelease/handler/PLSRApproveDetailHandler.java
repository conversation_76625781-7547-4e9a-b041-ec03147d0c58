package cec.jiutian.bc.productionStartupRelease.domain.productionStartupRelease.handler;

import cec.jiutian.bc.productionStartupRelease.domain.AproveTask.modle.PLSRApproveTask;
import cec.jiutian.bc.productionStartupRelease.domain.AproveTask.repository.PLSRApproveTaskRepository;
import cec.jiutian.bc.productionStartupRelease.domain.productionStartupRelease.model.PLSRReport;
import cec.jiutian.bc.productionStartupRelease.domain.productionStartupRelease.mto.ApproveTaskMTO;
import cec.jiutian.bc.productionStartupRelease.domain.productionStartupRelease.mto.PLSRApproveDetail;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class PLSRApproveDetailHandler implements OperationHandler<PLSRReport, PLSRApproveDetail> {

    @Resource
    private PLSRApproveTaskRepository plsrApproveTaskRepository;

    @Override
    public PLSRApproveDetail fabosJsonFormValue(List<PLSRReport> data, PLSRApproveDetail fabosJsonForm, String[] param) {

        if (CollectionUtils.isEmpty(data) || data.get(0).getId() == null) {
            throw new ServiceException("数据异常");
        }

        PLSRReport plsrReport = data.get(0);
        plsrReport.setGeneralCode(plsrReport.getGeneralCode());

        List<PLSRApproveTask> approveTasks = plsrApproveTaskRepository.findByBusinessKeyOrderByCreateDate(data.get(0).getId());
        if (CollectionUtils.isEmpty(approveTasks)) {
            throw new ServiceException("未查询到审批信息");
        }
        ArrayList<ApproveTaskMTO> details = new ArrayList<>(approveTasks.size());
        for (PLSRApproveTask approveTask : approveTasks) {
            details.add(ApproveTaskMTO.create(approveTask));
        }
        fabosJsonForm.setApproveTaskDetails(details);
        return fabosJsonForm;
    }

}
