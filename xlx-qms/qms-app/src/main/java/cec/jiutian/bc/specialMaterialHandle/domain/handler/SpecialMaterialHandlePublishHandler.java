package cec.jiutian.bc.specialMaterialHandle.domain.handler;

import cec.jiutian.bc.specialMaterialHandle.domain.model.SpecialMaterialHandleRequest;
import cec.jiutian.bc.specialMaterialHandle.enumeration.SpecialMaterialBusinessStatusEnum;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/26
 * @description TODO
 */
@Component
public class SpecialMaterialHandlePublishHandler implements OperationHandler<SpecialMaterialHandleRequest, Void> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<SpecialMaterialHandleRequest> data, Void modelObject, String[] param) {
        SpecialMaterialHandleRequest specialMaterialHandleRequest = data.get(0);
        if (CollectionUtils.isEmpty(specialMaterialHandleRequest.getDetailList())) {
            throw new FabosJsonApiErrorTip("未录入详情信息，不可发布，请确认");
        }else {
            specialMaterialHandleRequest.setBusinessState(SpecialMaterialBusinessStatusEnum.Enum.WAIT_AUDIT.name());
            fabosJsonDao.mergeAndFlush(specialMaterialHandleRequest);
        }

        return "alert(操作成功)";
    }
}
