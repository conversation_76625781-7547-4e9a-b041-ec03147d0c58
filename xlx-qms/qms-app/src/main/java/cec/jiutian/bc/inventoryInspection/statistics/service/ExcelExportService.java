package cec.jiutian.bc.inventoryInspection.statistics.service;

import cec.jiutian.bc.dto.ChartData;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;

import java.io.OutputStream;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class ExcelExportService {

    public void exportChartDataToExcel(ChartData chartData, OutputStream outputStream, Integer index) throws Exception {
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("Sheet1");

            // 创建标题行
            Row titleRow = sheet.createRow(0);
            titleRow.setHeightInPoints(15.6f);
            titleRow.createCell(0).setCellValue("成品合格率趋势图");

            // 处理xAxis数据（周数据）
            List<String> weeks = chartData.getXAxis().getData();

            Row weekRow = sheet.createRow(1);
            weekRow.setHeightInPoints(15.6f);
            if (index != -1) {
                Cell cell = weekRow.createCell(1);
                cell.setCellValue(weeks.get(index));
            } else {
                for (int i = 0; i < weeks.size(); i++) {
                    Cell cell = weekRow.createCell(i + 1);
                    cell.setCellValue(weeks.get(i));
                }
            }

            // 按系列名称分组方便查找
            Map<String, List<? extends Object>> seriesMap = chartData.getSeries().stream()
                    .collect(Collectors.toMap(
                            ChartData.Series::getName,
                            ChartData.Series::getData
                    ));

            // 生产数量行
            Row productionRow = sheet.createRow(2);
            productionRow.setHeightInPoints(15.6f);
            productionRow.createCell(0).setCellValue("生产数量");
            List<? extends Object> productionData = seriesMap.get("生产数量");

            if (index != -1) {
                productionRow.createCell(1)
                        .setCellValue(convertToDouble(productionData.get(index)));
            } else {
                for (int i = 0; i < productionData.size(); i++) {
                    productionRow.createCell(i + 1)
                            .setCellValue(convertToDouble(productionData.get(i)));
                }
            }
            // 不良数量行
            Row defectiveRow = sheet.createRow(3);
            defectiveRow.setHeightInPoints(15.6f);
            defectiveRow.createCell(0).setCellValue("不良数量");
            List<? extends Object> defectiveData = seriesMap.get("不良数量");
            if (index != -1) {
                defectiveRow.createCell(1)
                        .setCellValue(convertToDouble(defectiveData.get(index)));
            } else {
                for (int i = 0; i < defectiveData.size(); i++) {
                    defectiveRow.createCell(i + 1)
                            .setCellValue(convertToDouble(defectiveData.get(i)));
                }
            }

            // 成品合格率行（使用公式）
            Row qualityRateRow = sheet.createRow(4);
            qualityRateRow.setHeightInPoints(15.6f);
            qualityRateRow.createCell(0).setCellValue("成品合格率");
            List<? extends Object> qualityRateData = seriesMap.get("合格率");
            if (index != -1) {
                qualityRateRow.createCell(1)
                        .setCellValue(convertToDouble(qualityRateData.get(index)));
            } else {
                for (int i = 0; i < qualityRateData.size(); i++) {
                    qualityRateRow.createCell(i + 1)
                            .setCellValue(convertToDouble(qualityRateData.get(i)));
                }
            }

            // 目标合格率行
            Row targetRow = sheet.createRow(5);
            targetRow.setHeightInPoints(15.6f);
            targetRow.createCell(0).setCellValue("目标");
            List<? extends Object> targetData = seriesMap.get("目标合格率");
            if (index != -1) {
                targetRow.createCell(1)
                        .setCellValue(convertToDouble(targetData.get(index)));
            } else {
                for (int i = 0; i < targetData.size(); i++) {
                    targetRow.createCell(i + 1)
                            .setCellValue(convertToDouble(targetData.get(i)));
                }
            }

            // 设置百分比格式
            CellStyle percentageStyle = workbook.createCellStyle();
            percentageStyle.setDataFormat(workbook.createDataFormat().getFormat("0.00%"));
            if (index != -1) {
                sheet.getRow(4).getCell(1).setCellStyle(percentageStyle);
                sheet.getRow(5).getCell(1).setCellStyle(percentageStyle);
                sheet.autoSizeColumn(index);
            } else {
                for (int i = 1; i <= weeks.size(); i++) {
                    sheet.getRow(4).getCell(i).setCellStyle(percentageStyle);
                    sheet.getRow(5).getCell(i).setCellStyle(percentageStyle);
                }

                // 自动调整列宽
                for (int i = 0; i <= weeks.size(); i++) {
                    sheet.autoSizeColumn(i);
                }
            }
            // 写入字节数组
            workbook.write(outputStream);
        }
    }

    private double convertToDouble(Object value) {
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        return 0.0;
    }

    private String getExcelColumnName(int columnIndex) {
        StringBuilder columnName = new StringBuilder();
        while (columnIndex >= 0) {
            int remainder = columnIndex % 26;
            columnName.insert(0, (char) ('A' + remainder));
            columnIndex = (columnIndex / 26) - 1;
            if (columnIndex < 0) break;
        }
        return columnName.toString();
    }
}