package cec.jiutian.bc.inventoryInspection.domain.inspectionTask.handler;

import cec.jiutian.bc.generalModeler.util.StringUtils;
import cec.jiutian.bc.inventoryInspection.domain.inspectionTask.mto.InventoryTaskResultEnterDetailMTO;
import cec.jiutian.bc.inventoryInspection.domain.inspectionTask.mto.MyInvTaskResultEnterMTO;
import cec.jiutian.bc.inventoryInspection.enumeration.InspectionResultEnum;
import cec.jiutian.bc.modeler.enumration.ComparisonMethodEnum;
import cec.jiutian.bc.modeler.enumration.InspectionValueTypeEnum;
import cec.jiutian.common.util.NumberUtil;
import cec.jiutian.view.DependFiled;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/4/9
 * @description TODO
 */
@Component
public class InventoryInspectionItemResultHandler implements DependFiled.DynamicHandler<MyInvTaskResultEnterMTO> {
    @Override
    public Map<String, Object> handle(MyInvTaskResultEnterMTO taskResultEnterMTO) {
        Map<String, Object> result = new HashMap<>();
        List<InventoryTaskResultEnterDetailMTO> detailMTOList = taskResultEnterMTO.getDetailList();
        if (CollectionUtils.isNotEmpty(detailMTOList)) {
            for (InventoryTaskResultEnterDetailMTO detail : taskResultEnterMTO.getDetailList()) {
                if (StringUtils.isEmpty(detail.getResultValue()) || InspectionValueTypeEnum.Enum.text.name().equals(detail.getInspectionValueType())) {
                    continue;
                }

                boolean pass = false;
                if (ComparisonMethodEnum.Enum.equal.name().equals(detail.getComparisonMethod())) {
                    pass = StringUtils.equals(detail.getResultValue(), detail.getStandardValue());
                } else if (ComparisonMethodEnum.Enum.upperLimit.name().equals(detail.getComparisonMethod())) {
                    pass = NumberUtil.getNumberByString(detail.getResultValue(), Double.class) < detail.getUpperValue();
                } else if (ComparisonMethodEnum.Enum.lowerLimit.name().equals(detail.getComparisonMethod())) {
                    pass = NumberUtil.getNumberByString(detail.getResultValue(), Double.class) > detail.getLowerValue();
                } else if (ComparisonMethodEnum.Enum.range.name().equals(detail.getComparisonMethod())) {
                    Double v = NumberUtil.getNumberByString(detail.getResultValue(), Double.class);
                    pass = detail.getLowerValue() < v && v < detail.getUpperValue();
                }

                String inspectionItemResult = pass ? InspectionResultEnum.Enum.QUALIFIED.name() : InspectionResultEnum.Enum.UNQUALIFIED.name();
                detail.setInspectionItemResult(inspectionItemResult);
            }
        }
        result.put("detailList",detailMTOList);
        return result;
    }
}
