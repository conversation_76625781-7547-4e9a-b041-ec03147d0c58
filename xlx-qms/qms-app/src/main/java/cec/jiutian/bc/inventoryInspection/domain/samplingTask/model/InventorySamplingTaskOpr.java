package cec.jiutian.bc.inventoryInspection.domain.samplingTask.model;

import cec.jiutian.bc.inventoryInspection.domain.samplingTask.handler.InventorySamplingTaskOprDynamicHandler;
import cec.jiutian.bc.processInspect.domain.publicInspectionTask.mto.ProcessUserForInsTaskMTO;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.*;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/19
 * @description TODO
 */
@FabosJson(
        name = "取样任务自定义按钮模型"
)
@Table(name = "mi_sampling_task",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Getter
@Setter
@TemplateType(type = "multiTable")
public class InventorySamplingTaskOpr extends MetaModel {

    @FabosJsonField(
            views = @View(title = "条码号"),
            edit = @Edit(title = "条码号", notNull = true)
    )
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "检验任务单号"),
            edit = @Edit(title = "检验任务单号", readonly = @Readonly),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "generalCode", dynamicHandler = InventorySamplingTaskOprDynamicHandler.class))
    )
    private String inspectionTaskCode;

    @FabosJsonField(
            views = @View(title = "取样任务单号"),
            edit = @Edit(title = "取样任务单号", readonly = @Readonly)
    )
    private String samplingTaskCode;

    @FabosJsonField(
            views = @View(title = "物资编码"),
            edit = @Edit(title = "物资编码", readonly = @Readonly)
    )
    private String materialCode;

    @FabosJsonField(
            views = @View(title = "物资名称"),
            edit = @Edit(title = "物资名称", readonly = @Readonly)
    )
    private String materialName;

    @FabosJsonField(
            views = @View(title = "批次号"),
            edit = @Edit(title = "批次号", readonly = @Readonly)
    )
    private String originLotId;

    @FabosJsonField(
            views = @View(title = "规格"),
            edit = @Edit(title = "规格", readonly = @Readonly)
    )
    private String materialSpecification;

    @FabosJsonField(
            views = @View(title = "单位"),
            edit = @Edit(title = "单位", readonly = @Readonly)
    )
    private String unit;

    @FabosJsonField(
            views = @View(title = "取样类型"),
            edit = @Edit(title = "取样类型", readonly = @Readonly)
    )
    private String inspectionType;

    @FabosJsonField(
            views = @View(title = "取样数量"),
            edit = @Edit(title = "取样数量", readonly = @Readonly,numberType = @NumberType(precision = 2))
    )
    private Double allInspectionItemQuantity;

    @FabosJsonField(
            views = @View(title = "包装方式"),
            edit = @Edit(title = "包装方式", readonly = @Readonly)
    )
    private String packageType;

    @FabosJsonField(
            views = @View(title = "取样点"),
            edit = @Edit(title = "取样点",readonly = @Readonly,
                    inputType = @InputType(length = 40))
    )
    private String samplingPoint;

    @FabosJsonField(
            views = @View(title = "送检点"),
            edit = @Edit(title = "送检点",readonly = @Readonly,
                    inputType = @InputType(length = 40))
    )
    private String sendInspectPoint;

    @Transient
    @FabosJsonField(
            views = @View(title = "送样人", show = false, column = "name"),
            edit = @Edit(title = "送样人",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType(),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "businessState != 'BE_SAMPLING'")
            )
    )
    private ProcessUserForInsTaskMTO user;

    @FabosJsonField(
            views = @View(title = "送样人id", show = false),
            edit = @Edit(title = "送样人id", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "user", beFilledBy = "id"))
    )
    private String sendSamplePersonId;

    @FabosJsonField(
            views = @View(title = "送样人"),
            edit = @Edit(title = "送样人",
                    readonly = @Readonly,
                    search = @Search(vague = true),
                    dependFieldDisplay = @DependFieldDisplay(notNull = "businessState != 'BE_SAMPLING'", showOrHide = "businessState != 'BE_SAMPLING'")),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "user", beFilledBy = "name"))
    )
    private String sendSamplePerson;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "sampling_task_id")
    @OrderBy
    @FabosJsonField(
            edit = @Edit(title = "取样任务明细", type = EditType.TAB_TABLE_ADD,readonly = @Readonly),
            views = @View(title = "取样任务明细", type = ViewType.TABLE_VIEW)
    )
    private List<InventorySamplingTaskOprDetail> detailList;
}
