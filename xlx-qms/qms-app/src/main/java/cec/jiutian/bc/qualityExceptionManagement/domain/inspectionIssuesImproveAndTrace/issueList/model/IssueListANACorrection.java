package cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.issueList.model;

import cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.issueList.proxy.IssueListANACorrectionDataProxy;
import cec.jiutian.bc.qualityExceptionManagement.enumeration.inspectionIssuesImproveAndTrace.ImproveProgressEnum;
import cec.jiutian.bc.urm.domain.org.entity.Org;
import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.frame.module.MetaUser;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DateType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@FabosJson(
        name = "问题分析-整改措施",
        orderBy = "IssueListANACorrection.createTime desc",
        dataProxy = IssueListANACorrectionDataProxy.class
)
@Table(name = "qms_qem_issue_list_correction")
@Entity
@Getter
@Setter
public class IssueListANACorrection extends BaseModel {

    @FabosJsonField(
            views = @View(title = "整改措施"),
            edit = @Edit(title = "整改措施", notNull = true)
    )
    private String correction;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "责任部门", column = "name"),
            edit = @Edit(title = "责任部门",notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"))
    )
    @JoinColumn(foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private Org org;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "责任人", column = "name"),
            edit = @Edit(title = "责任人",notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"))
    )
    @JoinColumn(foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private MetaUser metaUser;

    @FabosJsonField(
            views = @View(title = "措施纳期"),
            edit = @Edit(title = "措施纳期", notNull = true, dateType = @DateType(type = DateType.Type.DATE_TIME))
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date measureDate;

    @FabosJsonField(
            views = {
                    @View(title = "问题清单", column = "name",show = false)
            },
            edit = @Edit(title = "问题清单", type = EditType.REFERENCE_TABLE,show = false,
                    referenceTableType = @ReferenceTableType(label = "name")
            )
    )
    @ManyToOne
    @JsonIgnoreProperties("issueListCorrections")
    private IssueList issueList;

    @FabosJsonField(
            views = @View(title = "改善进度"),
            edit = @Edit(title = "改善进度",type = EditType.CHOICE,readonly = @Readonly,
                    defaultVal = "WAIT_RUN",
                    choiceType = @ChoiceType(fetchHandler = ImproveProgressEnum.class))
    )
    private String progress;
}
