package cec.jiutian.bc.specialMaterialHandle.mto;

import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.AttachmentType;
import cec.jiutian.view.field.edit.InputType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025/5/26
 * @description TODO
 */
@FabosJson(
        name = "特殊物料处理申请验证MTO"
)
@Table(name = "qms_special_material_handle_request")
@Entity
@Getter
@Setter
public class HandleRequestVerifyMTO extends MetaModel {

    @FabosJsonField(
            views = @View(title = "验证说明"),
            edit = @Edit(title = "验证说明",
                    type = EditType.TEXTAREA)
    )
    private String verifyDescription;

    @FabosJsonField(
            views = @View(title = "附件"),
            edit = @Edit(title = "附件",
                    readonly = @Readonly(add = true,edit = true),
                    inputType = @InputType(length = 300),
                    type = EditType.ATTACHMENT,
                    tips = "最大支持100M文件",
                    attachmentType = @AttachmentType(
                            size = 100 * 1024,
                            maxLimit = 1,
                            type = AttachmentType.Type.BASE)
            )
    )
    @Column(length = 300)
    private String attachment;
}
