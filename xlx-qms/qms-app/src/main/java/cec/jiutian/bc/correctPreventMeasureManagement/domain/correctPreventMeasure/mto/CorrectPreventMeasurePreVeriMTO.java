package cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.mto;

import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.enumration.CpmBusinessStateEnum;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.enumration.ProgressEnum;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.handler.CorrectPreventMeasureDynamicHandler;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.ChoiceType;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/29
 * @description TODO
 */
@FabosJson(name = "措施验证")
@Table(name = "qms_cpm_correct_prevent_measure")
@Entity
@Getter
@Setter
public class CorrectPreventMeasurePreVeriMTO extends MetaModel {
    @FabosJsonField(
            views = @View(title = "单据编号", show = false),
            edit = @Edit(title = "单据编号",
                    readonly = @Readonly(edit = false),
                    show = false
            ),
            dynamicField = @DynamicField(dependFiled = @DependFiled(buttonName = "生成", changeBy = "id",
                    dynamicHandler = CorrectPreventMeasureDynamicHandler.class))
    )
    private String correctPreventMeasureFormNumber;

    @FabosJsonField(
            edit = @Edit(title = "预防措施", type = EditType.TAB_TABLE_ADD),
            views = @View(title = "预防措施", type= ViewType.TABLE_VIEW)
    )
    @JoinColumn(name = "qms_cpm_correct_prevent_measure_id")
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy
    private List<PreventMeasureMTO> preventMeasureList;

    @FabosJsonField(
            views = @View(title = "纠正验证状态", show = false),
            edit = @Edit(title = "纠正验证状态",
                    readonly = @Readonly(),
                    type = EditType.CHOICE,
                    show = false,
                    choiceType = @ChoiceType(fetchHandler = ProgressEnum.class)
            )
    )
    private String correctVeriState;

    @FabosJsonField(
            views = @View(title = "预防验证状态", show = false),
            edit = @Edit(title = "预防验证状态",
                    readonly = @Readonly(),
                    type = EditType.CHOICE,
                    show = false,
                    choiceType = @ChoiceType(fetchHandler = ProgressEnum.class)
            )
    )
    private String preVeriState;

    @FabosJsonField(
            views = @View(title = "状态", show = false),
            edit = @Edit(title = "状态",
                    show = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = CpmBusinessStateEnum.class)
            )
    )
    private String businessState;
}
