package cec.jiutian.bc.inventoryInspection.domain.publicInspectionTask.handler;

import cec.jiutian.bc.inventoryInspection.domain.publicInspectionTask.mto.InventoryInsItemTargetMTO;
import cec.jiutian.bc.inventoryInspection.enumeration.InsItemResEnum;
import cec.jiutian.view.DependFiled;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class InventoryResultJudgeHandler implements DependFiled.DynamicHandler<InventoryInsItemTargetMTO> {

    @Override
    public Map<String, Object> handle(InventoryInsItemTargetMTO inventoryInsItemTargetMTO) {
        if (inventoryInsItemTargetMTO == null || inventoryInsItemTargetMTO.getResultValue() == null) {
            return Map.of();
        }
        HashMap<String, Object> res = new HashMap<>();
        String value = inventoryInsItemTargetMTO.getResultValue();
        if (StringUtils.isBlank(value)) {
            value = "0.00";
        }
        Double resultValue = Double.valueOf(value);
        Double lowerValue = inventoryInsItemTargetMTO.getLowerValue();
        Double upperValue = inventoryInsItemTargetMTO.getUpperValue();
        if (resultValue < lowerValue || resultValue > upperValue) {
            res.put("judge", InsItemResEnum.FAIL.name());
            return res;
        }else {
            res.put("judge", InsItemResEnum.PASS.name());
            return res;
        }

    }
}
