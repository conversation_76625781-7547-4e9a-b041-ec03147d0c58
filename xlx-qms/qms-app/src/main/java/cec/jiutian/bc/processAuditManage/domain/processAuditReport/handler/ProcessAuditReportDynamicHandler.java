package cec.jiutian.bc.processAuditManage.domain.processAuditReport.handler;

import cec.jiutian.bc.basicData.enumeration.NamingRuleCodeEnum;
import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.processAuditManage.domain.processAuditReport.model.ProcessAuditReport;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/5/15
 * @description TODO
 */
@Component
public class ProcessAuditReportDynamicHandler implements DependFiled.DynamicHandler<ProcessAuditReport> {

    @Resource
    private NamingRuleService namingRuleService;


    @Override
    public Map<String, Object> handle(ProcessAuditReport inspectionInstrument) {
        Map<String, Object> map = new HashMap<>();
        List<String> result = namingRuleService.getNameCode(NamingRuleCodeEnum.PROCESS_AUDIT_REPORT.name(), 1, null);
        map.put("processAuditReportFormNumber", result.get(0));
        return map;
    }
}
