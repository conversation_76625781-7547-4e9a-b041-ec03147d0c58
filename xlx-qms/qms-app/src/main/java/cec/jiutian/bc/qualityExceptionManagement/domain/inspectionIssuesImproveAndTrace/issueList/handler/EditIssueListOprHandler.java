package cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.issueList.handler;

import cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.issueList.model.IssueList;
import cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.issueList.model.IssueListEdit;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class EditIssueListOprHandler implements OperationHandler<IssueList, IssueListEdit> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public IssueListEdit fabosJsonFormValue(List<IssueList> data, IssueListEdit fabosJsonForm, String[] param) {
        if (CollectionUtils.isEmpty(data)) {
            return fabosJsonForm;
        }
        IssueList issueList = data.get(0);
        BeanUtil.copyProperties(issueList, fabosJsonForm);
        return fabosJsonForm;
    }
}
