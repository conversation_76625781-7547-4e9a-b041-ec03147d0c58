package cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.handler;

import cec.jiutian.bc.accidentReportManagement.enumeration.ArmImprovingProgressEnum;
import cec.jiutian.bc.accidentReportManagement.enumeration.ArmMeasureStatusEnum;
import cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.model.AccidentReportTask;
import cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.mto.AccidentReportTaskTemporaryMTO;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2025/5/29
 * @description
 */
@Component
public class AccidentReportTaskTemporaryHandler implements OperationHandler<AccidentReportTask, AccidentReportTaskTemporaryMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<AccidentReportTask> data, AccidentReportTaskTemporaryMTO modelObject, String[] param) {
        modelObject.setTemporaryMeasureStatus(ArmMeasureStatusEnum.Enum.ALREADY_FILLED_IN.name());
        Set<String> userIdSet = new HashSet<>();
        Optional.ofNullable(modelObject.getArmTemporaryMeasureList())
                .ifPresent(list -> list.stream()
                        .forEach(mto -> {
                            mto.setImprovingProgress(
                                    ArmImprovingProgressEnum.Enum.PENDING_EXECUTION.name()
                            );
                            userIdSet.add(mto.getUserForInsTaskMTO().getId());
                        })
                );
        ArrayList<String> tempUsrIds = new ArrayList<>(userIdSet);

        AccidentReportTask model = data.get(0);
        BeanUtil.copyProperties(modelObject, model, "createBy", "createTime");
        model.setTempUserIds(String.join(",", tempUsrIds));
        fabosJsonDao.mergeAndFlush(model);
        return "alert(操作成功)";
    }

    @Override
    public AccidentReportTaskTemporaryMTO fabosJsonFormValue(List<AccidentReportTask> data, AccidentReportTaskTemporaryMTO fabosJsonForm, String[] param) {
        AccidentReportTask model = data.get(0);
        BeanUtil.copyProperties(model, fabosJsonForm);
        return fabosJsonForm;
    }
}
