package cec.jiutian.bc.clientReviewManagement.domain.problemImprovement.mto;

import cec.jiutian.bc.clientComplaintManagement.enumeration.ReviewProblemImproveStatusEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.AttachmentType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DateType;
import cec.jiutian.view.field.edit.Search;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/06/11
 * @description
 */
@FabosJson(
        name = "执行情况"

)
@Table(name = "qms_client_review_problem_improvement")
@Entity
@Getter
@Setter
public class MyReviewProblemImprovementExecuteMTO extends MetaModel {
    @FabosJsonField(
            views = @View(title = "实际完成日期", show = false),
            edit = @Edit(title = "实际完成日期",
                    dateType = @DateType(type = DateType.Type.DATE_TIME),
                    notNull = true
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date actualFinishTime;

    @FabosJsonField(
            views = @View(title = "执行情况"),
            edit = @Edit(title = "执行情况",
                    notNull = true,
                    search = @Search(vague = true))

    )
    private String executionStatus;

    @FabosJsonField(
            views = @View(title = "改善证据"),
            edit = @Edit(title = "改善证据", type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(maxLimit = 10))
    )
    private String improvementEvidence;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",
                    show = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ReviewProblemImproveStatusEnum.class)
            )
    )
    private String businessStatus;
}
