package cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.mto;

import cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.enumration.*;
import cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.handler.AccidentReportTaskDynamicHandler;
import cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.proxy.AccidentReportTaskCreateMTODataProxy;
import cec.jiutian.bc.materialInspect.domain.publicInspectionTask.mto.UserForInsTaskMTO;
import cec.jiutian.bc.mto.OrgMTO;
import cec.jiutian.bc.mto.FactoryArea;
import cec.jiutian.bc.urm.domain.dictionary.handler.DictChoiceFetchHandler;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Filter;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/5/29
 * @description
 */
@FabosJson(
        name = "创建事故报告任务单",
        dataProxy = AccidentReportTaskCreateMTODataProxy.class
)
@Table(name = "qms_arm_accident_report_task",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Getter
@Setter
public class AccidentReportTaskCreateMTO extends MetaModel {
    @FabosJsonField(
            views = @View(title = "任务单号"),
            edit = @Edit(title = "任务单号",
                    readonly = @Readonly(edit = false),
                    notNull = true,
                    dependFieldDisplay = @DependFieldDisplay(enableOrDisable = "businessStatus == 'TO_BE_CONFIRMED'")
            ),
            dynamicField = @DynamicField(dependFiled = @DependFiled(buttonName = "生成", changeBy = "id",
                    dynamicHandler = AccidentReportTaskDynamicHandler.class))
    )
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "车间", column = "factoryAreaName"),
            edit = @Edit(title = "车间",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "factoryAreaName"),
                    allowAddMultipleRows = false,
                    filter = @Filter(value = "FactoryArea.factoryAreaTypeCode = '02'")
            )
    )
    @ManyToOne
    private FactoryArea factoryArea;

    @FabosJsonField(
            views = @View(title = "事故类型"),
            edit = @Edit(title = "事故类型",
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = DictChoiceFetchHandler.class,
                            fetchHandlerParams = "armAccidentType")
            )
    )
    private String armAccidentType;

    @FabosJsonField(
            views = @View(title = "事故等级"),
            edit = @Edit(title = "事故等级",
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = DictChoiceFetchHandler.class,
                            fetchHandlerParams = "armAccidentLevel")
            )
    )
    private String armAccidentLevel;

    @FabosJsonField(
            views = @View(title = "发生日期"),
            edit = @Edit(title = "发生日期",
                    notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date occurrenceDate;

    @FabosJsonField(
            views = @View(title = "事故描述"),
            edit = @Edit(title = "事故描述",
                    notNull = true,
                    type = EditType.TEXTAREA)
    )
    private String accidentDescription;

    @FabosJsonField(
            views = @View(title = "责任部门", column = "name"),
            edit = @Edit(title = "责任部门",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(type = TabTableReferType.SelectShowTypeMTM.LIST)
            )
    )
    @ManyToOne
    @JoinColumn(name = "response_department_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private OrgMTO orgMTO;

    @FabosJsonField(
            views = @View(title = "责任人", column = "name"),
            edit = @Edit(title = "责任人",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(type = TabTableReferType.SelectShowTypeMTM.LIST)
            )
    )
    @ManyToOne
    @JoinColumn(name = "person_responsible_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private UserForInsTaskMTO userForInsTaskMTO;

    @FabosJsonField(
            views = @View(title = "预计纳期"),
            edit = @Edit(title = "预计纳期",
                    notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date expectedDeliveryDate;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",
                    show = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ArmBusinessStatusEnum.class)
            )
    )
    private String businessStatus;

    @PrePersist
    protected void onCreate() {
        if (businessStatus == null) {
            businessStatus = ArmBusinessStatusEnum.Enum.TO_BE_CONFIRMED.name();
        }
    }
}
