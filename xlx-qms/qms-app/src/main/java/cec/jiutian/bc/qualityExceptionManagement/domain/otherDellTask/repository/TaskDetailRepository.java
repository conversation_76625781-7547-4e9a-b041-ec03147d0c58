package cec.jiutian.bc.qualityExceptionManagement.domain.otherDellTask.repository;

import cec.jiutian.bc.qualityExceptionManagement.domain.otherDellTask.dto.TaskDetailDTO;
import cec.jiutian.bc.qualityExceptionManagement.domain.otherDellTask.model.TaskDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface TaskDetailRepository extends JpaRepository<TaskDetail, String> {

    /**
     * @param serialNumber
     */

    @Query(value = "SELECT " +
            " rd.ID AS detail_id, " +
            " rc.ID AS recorde_id, " +
            " rc.stk_in_rcrd_nb AS code, " +
            " rd.lot_srlzd_id AS serial_number, " +
            " rd.in_stk_qt AS quantity, " +
            " rd.crte_tm AS create_time " +
            "FROM " +
            " bm_stk_in_rcrd_dtl rd " +
            " INNER JOIN bm_stk_in_rcrd rc ON rd.stk_in_rcrd_id = rc.ID " +
            "WHERE " +
            " rd.lot_srlzd_id = :serialNumber " +
            " AND rd.crte_tm BETWEEN to_timestamp(:startTime/1000.0) AND to_timestamp(:endTime/1000.0)", nativeQuery = true)
    List<Map<String, Object>> queryMesStockInData(@Param("serialNumber") String serialNumber,
                                            @Param("startTime") long startTime,
                                            @Param("endTime") long endTime);

    @Query(value = "SELECT " +
            "   rd.gid AS detail_id, " +
            "   re.gid AS record_id, " +
            "   re.out_order_number AS code, " +
            "   rd.factory_lot AS serial_number, " +
            "   rd.application_quantity AS quantity, " +
            "   rd.create_ts AS create_time " +
            "FROM " +
            "  stock_out_record_detail rd " +
            "  INNER JOIN stock_out_record re ON rd.out_record_gid = re.gid " +
            "WHERE " +
            "  rd.factory_lot = :serialNumber " +
            "  AND rd.create_ts BETWEEN to_timestamp(:startTime/1000.0) AND to_timestamp(:endTime/1000.0)", nativeQuery = true)
    List<Map<String, Object>> queryMesStockOutData(@Param("serialNumber") String serialNumber,
                                             @Param("startTime") long startTime,
                                             @Param("endTime") long endTime);


    @Query(value = "SELECT " +
            "  rd.ID AS detail_id, " +
            "  re.ID AS record_id, " +
            "  re.general_code AS code, " +
            "  rd.factory_lot_identifier AS serial_number, " +
            "  rd.quantity AS quantity, " +
            "  rd.create_time AS create_time " +
            "FROM " +
            "  purchase_stock_in_detail rd " +
            "  INNER JOIN purchase_stock_in re ON rd.purchase_stock_in_id = re.ID " +
            "WHERE " +
            "  rd.factory_lot_identifier = :serialNumber " +
            "  AND rd.create_time BETWEEN to_timestamp(:startTime/1000.0) AND to_timestamp(:endTime/1000.0)", nativeQuery = true)
    List<Map<String, Object>> queryWmsStockInData(@Param("serialNumber") String serialNumber,
                                                  @Param("startTime") long startTime,
                                                  @Param("endTime") long endTime);

    @Query(value = "SELECT " +
            "  rd.ID AS detail_id, " +
            "  re.ID AS record_id, " +
            "  re.general_code AS code, " +
            "  rd.factory_lot_identifier AS serial_number, " +
            "  rd.quantity AS quantity, " +
            "  rd.create_time AS create_time " +
            "FROM " +
            "  stock_out_detail rd " +
            "  INNER JOIN stock_out re ON rd.stock_out_id = re.ID " +
            "WHERE " +
            "  rd.factory_lot_identifier = :serialNumber " +
            "  AND rd.create_time BETWEEN to_timestamp(:startTime/1000.0) AND to_timestamp(:endTime/1000.0)", nativeQuery = true)

    List<Map<String, Object>> queryWmsStockOutData(@Param("serialNumber") String serialNumber,
                                            @Param("startTime") long startTime,
                                            @Param("endTime") long endTime);
}
