package cec.jiutian.bc.qualityExceptionManagement.domain.abnormalStopProduction.proxy;

import cec.jiutian.bc.mto.OrgMTO;
import cec.jiutian.bc.mto.OrderBatchSerialMTO;
import cec.jiutian.bc.qualityExceptionManagement.domain.abnormalStopProduction.model.AbnormalStopProduction;
import cec.jiutian.bc.qualityExceptionManagement.enums.AbnormalStopProductionStateEnum;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class AbnormalStopProductionDataProxy implements DataProxy<AbnormalStopProduction> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public void beforeAdd(AbnormalStopProduction entity) {
        entity.setCurrentState(AbnormalStopProductionStateEnum.Enum.Edit.name());
    }

    @Override
    public void afterSingleFetch(Map<String, Object> map) {
        if (map.get("productLotId") != null) {
            OrderBatchSerialMTO serialMTO = fabosJsonDao.findById(OrderBatchSerialMTO.class, map.get("productLotId"));
            if (serialMTO != null) {
                map.put("orderBatchSerialMTO", serialMTO);
                map.put("orderBatchSerialMTO_serialNumber", serialMTO.getSerialNumber());
            } else {
                throw new FabosJsonApiErrorTip("未查到工单批次:" + map.get("lotSerialId") + "源数据，请确认");
            }
        }
        if (map.get("departmentId") != null) {
            OrgMTO orgMTO = fabosJsonDao.findById(OrgMTO.class, map.get("departmentId"));
            if (orgMTO != null) {
                map.put("orgMTO", orgMTO);
                map.put("orgMTO_name", orgMTO.getName());
            } else {
                throw new FabosJsonApiErrorTip("未查到部门：" + map.get("departmentName") + "源数据，请确认");
            }
        }
    }

}
