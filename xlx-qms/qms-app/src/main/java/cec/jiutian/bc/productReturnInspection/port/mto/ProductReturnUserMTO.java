package cec.jiutian.bc.productReturnInspection.port.mto;

import cec.jiutian.bc.urm.domain.role.entity.Role;
import cec.jiutian.core.data.annotation.LinkTable;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.view.OperationFixed;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Entity
@LinkTable
@Table(name = "fd_user")
@Getter
@Setter
@OperationFixed()
public class ProductReturnUserMTO extends MetaModel {

    @ManyToOne(fetch = FetchType.LAZY)
    private ProductReturnOrgMTO org;

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
            name = "fd_user_role",
            joinColumns = @JoinColumn(name = "user_id", referencedColumnName = "id",foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT)),
            inverseJoinColumns = @JoinColumn(name = "role_id", referencedColumnName = "id",foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT))
    )
    private List<Role> roles;

}
