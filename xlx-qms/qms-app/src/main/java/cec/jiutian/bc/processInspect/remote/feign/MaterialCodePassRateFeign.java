package cec.jiutian.bc.processInspect.remote.feign;

import cec.jiutian.bc.processInspect.remote.pojo.MaterialCodePassRateData;
import cec.jiutian.bc.processInspect.service.MaterialCodePassRateService;
import cec.jiutian.core.frame.constant.FabosJsonRestPath;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/fabos-qms-app" + FabosJsonRestPath.FABOS_REMOTE_API)
public class MaterialCodePassRateFeign {

    @Resource
    private MaterialCodePassRateService materialCodePassRateService;

    @PostMapping("/materialCodePassRate")
    public List<MaterialCodePassRateData> MaterialCodePassRate(@RequestParam(required = false) String CALCULATIONDIMENSION
            , @RequestParam(required = false) String STARTTIME, @RequestParam(required = false) String ENDTIME) {
        return materialCodePassRateService.getMaterialCodePassRate(CALCULATIONDIMENSION, STARTTIME, ENDTIME);
    }
}
