package cec.jiutian.bc.inventoryInspection.domain.inspectionTask.handler;

import cec.jiutian.bc.basicData.domain.inspectionStandard.model.InspectionStandard;
import cec.jiutian.bc.inventoryInspection.domain.inspectionTask.model.InventoryInspectionStandardItemTarget;
import cec.jiutian.bc.inventoryInspection.domain.inspectionTask.model.InventoryInspectionTask;
import cec.jiutian.bc.inventoryInspection.domain.inspectionTask.model.InventoryInspectionStandardDetail;
import cec.jiutian.bc.modeler.domain.inspectionItem.model.InspectionItem;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/3/11
 * @description TODO
 */
@Component
public class InventoryInspectionItemDetailDynamicHandler implements DependFiled.DynamicHandler<InventoryInspectionTask> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public Map<String, Object> handle(InventoryInspectionTask inventoryInspectionTask) {

        Map<String, Object> result = new HashMap<>();
        List<InventoryInspectionStandardDetail> inspectionStandardDetailList = new ArrayList<>();
        if (inventoryInspectionTask.getInspectionStandard() == null || StringUtils.isBlank(inventoryInspectionTask.getInspectionStandard().getId())) {
            result.put("standardDetailList", new ArrayList<>());
            return result;
        }

        // 带出质检标准
        InspectionStandard inspectionStandard = fabosJsonDao.getById(InspectionStandard.class, inventoryInspectionTask.getInspectionStandard().getId());
        if (CollectionUtils.isNotEmpty(inspectionStandard.getDetails())) {
            inspectionStandard.getDetails().forEach(detail -> {
                InventoryInspectionStandardDetail inventoryInspectionStandardDetail = new InventoryInspectionStandardDetail();
                // 带出质检标准下检验项目
                InspectionItem inspectionItem = fabosJsonDao.getById(InspectionItem.class, detail.getInspectionItem().getId());
                inventoryInspectionStandardDetail.setGroupId(detail.getInspectionItemGroup() != null ? detail.getInspectionItemGroup().getId() : null);
                inventoryInspectionStandardDetail.setItemId(inspectionItem.getId());
                inventoryInspectionStandardDetail.setCode(inspectionItem.getGeneralCode());
                inventoryInspectionStandardDetail.setName(inspectionItem.getName());
                inventoryInspectionStandardDetail.setItemType(inspectionItem.getItemType());
                inventoryInspectionStandardDetail.setFeature(inspectionItem.getFeature());
                inventoryInspectionStandardDetail.setInspectionMethod(inspectionItem.getInspectionMethod());
                inventoryInspectionStandardDetail.setSamplingPlan(inspectionItem.getSamplingPlan());
                inventoryInspectionStandardDetail.setSendInspectPoint(inspectionItem.getSendPointMTO().getName());
                inventoryInspectionStandardDetail.setSamplingPoint(inspectionItem.getSamplingPoint());
                inventoryInspectionStandardDetail.setPackageType(inspectionItem.getPackageType());
                inventoryInspectionStandardDetail.setSamplingPoint(inspectionItem.getSamplingPoint());
                inventoryInspectionStandardDetail.setSendInspectPoint(inspectionItem.getSendPointMTO().getName());
                if (CollectionUtils.isNotEmpty(inspectionItem.getInspectionItemTargetList())) {
                    List<InventoryInspectionStandardItemTarget> inventoryInspectionStandardItemTargetList = new ArrayList<>();
                    inspectionItem.getInspectionItemTargetList().forEach(inspectionItemTarget -> {
                        InventoryInspectionStandardItemTarget inventoryInspectionStandardItemTarget = new InventoryInspectionStandardItemTarget();
                        // 带出检验项下检验指标
                        inventoryInspectionStandardItemTarget.setName(inspectionItemTarget.getName());
                        inventoryInspectionStandardItemTarget.setTargetId(inspectionItemTarget.getId());
                        inventoryInspectionStandardItemTarget.setStandardValue(inspectionItemTarget.getStandardValue());
                        inventoryInspectionStandardItemTarget.setUpperValue(inspectionItemTarget.getUpperValue());
                        inventoryInspectionStandardItemTarget.setIsContainUpper(inspectionItemTarget.getIsContainUpper());
                        inventoryInspectionStandardItemTarget.setIsContainLower(inspectionItemTarget.getIsContainLower());
                        inventoryInspectionStandardItemTarget.setLowerValue(inspectionItemTarget.getLowerValue());
                        inventoryInspectionStandardItemTarget.setInspectionValueType(inspectionItemTarget.getInspectionValueType());
                        inventoryInspectionStandardItemTarget.setComparisonMethod(inspectionItemTarget.getComparisonMethod());
                        inventoryInspectionStandardItemTarget.setUnit(inspectionItemTarget.getUnit());
                        inventoryInspectionStandardItemTarget.setDescription(inspectionItemTarget.getDescription());
                        inventoryInspectionStandardItemTargetList.add(inventoryInspectionStandardItemTarget);
                    });
                    inventoryInspectionStandardDetail.setInventoryInspectionStandardItemTargetList(inventoryInspectionStandardItemTargetList);
                }
                inspectionStandardDetailList.add(inventoryInspectionStandardDetail);
            });
        }

        result.put("standardDetailList", inspectionStandardDetailList);
        return result;
    }
}
