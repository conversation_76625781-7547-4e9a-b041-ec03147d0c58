package cec.jiutian.bc.qualityPreparationPlan.service;

import cec.jiutian.bc.qualityPreparationPlan.pojo.ItemDTO;
import cec.jiutian.bc.qualityPreparationPlan.pojo.ScheduleDTO;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
public class ItemService {

    @Resource
    private FabosJsonDao fabosJsonDao;

    public List<ItemDTO> getItemByLineId(Long lineId) {
        String sql = buildSQL();

        Map<String, Object> params = new HashMap<>();
        params.put("lineId", lineId);

        List<Map<String, Object>> list = fabosJsonDao.getNamedParameterJdbcTemplate().queryForList(sql, params);

        return buildItemDTO(list);
    }

    private List<ItemDTO> buildItemDTO(List<Map<String, Object>> list) {
        List<ItemDTO> itemDTOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(list)) {
            return itemDTOList;
        }

        for (Map<String, Object> map : list) {
            ItemDTO dto = new ItemDTO();
            dto.setProcessCode((String) map.get("prcs_oprtn_cd"));
            dto.setProcessName((String) map.get("prcs_oprtn_nm"));
            dto.setItemName((String) map.get("item_nm"));
            dto.setInspectFrequency(dealFrequency((String) map.get("inspect_frequency")));
            itemDTOList.add(dto);
        }
        return itemDTOList;
    }

    private BigDecimal dealFrequency(String inspectFrequency) {
        if (StringUtils.isBlank(inspectFrequency)) {
            return BigDecimal.ZERO;
        }
        Pattern pattern = Pattern.compile("/(\\d*)批");
        Matcher matcher = pattern.matcher(inspectFrequency);
        if (matcher.find()) {
            String numberStr = matcher.group(1);
            // 处理无数字的情况（如"1次/批"）
            if (numberStr == null || numberStr.isEmpty()) {
                return BigDecimal.ONE;
            }
            return new BigDecimal(numberStr);
        } else {
            // 未匹配时添加默认值0
            return BigDecimal.ZERO;
        }
    }

    private String buildSQL() {
        return "WITH\n" +
                "line_flow AS(\n" +
                "SELECT\n" +
                " mline.fctry_ara_nm,\n" +
                " mline.fctry_ara_cd,\n" +
                " flow.ID as flow_id,\n" +
                " flow.sip_gid,\n" +
                " flow.sip_name \n" +
                "FROM\n" +
                " mo_fctry_ara mline\n" +
                " JOIN mf_prcs_flow flow ON mline.ID = flow.production_line_id \n" +
                "WHERE\n" +
                " mline.id = :lineId \n" +
                "LIMIT 1\n" +
                ")\n" +
                "\n" +
                "SELECT\n" +
                " dtl.prcs_oprtn_cd,\n" +
                " dtl.prcs_oprtn_nm,\n" +
                " dtl.item_nm,\n" +
                " dtl.inspect_frequency\n" +
                "FROM\n" +
                " line_flow lf\n" +
                " JOIN ms_sip sip ON lf.sip_gid=sip.gid\n" +
                " JOIN ms_sip_dtl dtl ON sip.gid=dtl.sip_id";
    }
}
