package cec.jiutian.bc.productExamineManagement.domain.sampleTask.handler;

import cec.jiutian.bc.modeler.enumration.SampleTypeEnum;
import cec.jiutian.bc.modeler.enumration.TaskBusinessStateEnum;
import cec.jiutian.bc.productExamineManagement.domain.sampleTask.model.ProductExamineSamplingTask;
import cec.jiutian.bc.productExamineManagement.domain.sampleTask.model.ProductExamineSamplingTaskOpr;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/12
 * @description TODO
 */
@Component
public class ProductExamineSendSampleOperationHandler implements OperationHandler<ProductExamineSamplingTask, ProductExamineSamplingTaskOpr> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<ProductExamineSamplingTask> data, ProductExamineSamplingTaskOpr modelObject, String[] param) {
        if (modelObject != null) {
            ProductExamineSamplingTask condition = new ProductExamineSamplingTask();
            condition.setGeneralCode(modelObject.getGeneralCode());
            condition.setInspectionType(SampleTypeEnum.Enum.REFUND_SAMPLING.name());
            ProductExamineSamplingTask productExamineSamplingTask = fabosJsonDao.selectOne(condition);
            if (productExamineSamplingTask == null) {
                throw new FabosJsonApiErrorTip("未查询到取样单，请确认");
            }
            if (!TaskBusinessStateEnum.Enum.SAMPLING_FINISH.name().equals(productExamineSamplingTask.getBusinessState())) {
                throw new FabosJsonApiErrorTip("取样任务单状态有误");
            }
            productExamineSamplingTask.setBusinessState(TaskBusinessStateEnum.Enum.SEND_SAMPLE.name());
            fabosJsonDao.mergeAndFlush(productExamineSamplingTask);
        }
        return "alert(操作成功)";
    }
}
