package cec.jiutian.bc.systemAuditManage.domain.saRectificationMeasuresInventory.handler;

import cec.jiutian.bc.systemAuditManage.domain.saRectificationMeasuresInventory.enumration.SaRectMeasuresInventoryStatusEnum;
import cec.jiutian.bc.systemAuditManage.domain.saRectificationMeasuresInventory.model.SaRectificationMeasuresInventory;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/13
 * @description TODO
 */
@Component
public class SaRectMeasInventoryConfirmHandler implements OperationHandler<SaRectificationMeasuresInventory, Void> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Transactional
    @Override
    public String exec(List<SaRectificationMeasuresInventory> data, Void modelObject, String[] param) {
        SaRectificationMeasuresInventory model = data.get(0);
        model.setBusinessStatus(SaRectMeasuresInventoryStatusEnum.Enum.PENDING.name());

        fabosJsonDao.update(model);
        return null;
    }
}
