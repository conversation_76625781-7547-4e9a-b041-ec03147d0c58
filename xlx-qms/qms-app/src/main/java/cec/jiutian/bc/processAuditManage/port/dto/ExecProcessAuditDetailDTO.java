package cec.jiutian.bc.processAuditManage.port.dto;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

import java.util.Date;

@Data
@Entity
@Table(name = "qms_sam_process_plan_audit_item", schema = "xlx_qms")
public class ExecProcessAuditDetailDTO {
    @Id
    private String id;

    // 审核项名称
    @Column(name = "audit_item_name")
    private String auditItemName;

    // 过程名称
    @Column(name = "process_name")
    private String processName;

    // 过程要素
    @Column(name = "process_elements")
    private String processElements;

    // 最低要求
    @Column(name = "min_requires")
    private String minRequires;

    // 主要涉及部门/场地 todo 查询id
    @Column(name = "department_id")
    private String departmentId;

    // 原材料出入库
    @Column(name = "material_storage_control")
    private String materialStorageControl;

    // 一烧前配混料
    @Column(name = "pre_first_burn_mixing")
    private String preFirstBurnMixing;

    // 一次烧结
    @Column(name = "first_sintering")
    private String firstSintering;

    // 一次粉体处理
    @Column(name = "first_crushing_process")
    private String firstCrushingProcess;

    // 水洗
    @Column(name = "washing_process")
    private String washingProcess;

    // 二烧前配混料
    @Column(name = "pre_second_burn_mixing")
    private String preSecondBurnMixing;

    // 二次烧结
    @Column(name = "second_sintering")
    private String secondSintering;

    // 二次粉体处理
    @Column(name = "final_crushing_process")
    private String finalCrushingProcess;

    // 检验
    @Column(name = "inspection")
    private String inspection;

    // 包装出库
    @Column(name = "packaging_spec")
    private String packagingSpec;

    // 审核结果
    @Column(name = "audit_result")
    private String auditResult;

    // 符合情况
    @Column(name = "compliance_status")
    private String complianceStatus;

    // 问题描述
    @Column(name = "issue_description")
    private String issueDescription;

    // 责任人  // todo 查询id
    @Column(name = "responsible_person_id")
    private String responsiblePersonId;

    // 主要责任部门 // todo 查询id
    @Column(name = "org_id")
    private String orgId;

    // 严重程度
    @Column(name = "severity")
    private String severity;

    // 分析方法
    @Column(name = "analysis_method")
    private String analysisMethod;

    // 验证人 // todo 查询id
    @Column(name = "verifier_id")
    private String verifierId;

    // 确定措施纳期
    @Column(name = "measure_deadline")
    private Date measureDeadline;

    // 完成纳期（
    @Column(name = "completion_deadline")
    private Date completionDeadline;

    // 附件
    @Column(name = "exec_attachments")
    private String execAttachments;

    // 原材料出入库
    @Column(name = "score_material_storage_control")
    private String scoreMaterialStorageControl;

    // 一烧前配混料
    @Column(name = "score_pre_first_burn_mixing")
    private String scorePreFirstBurnMixing;

    // 一次烧结
    @Column(name = "score_first_sintering")
    private String scoreFirstSintering;

    // 一次粉体处理
    @Column(name = "score_first_crushing_process")
    private String scoreFirstCrushingProcess;

    // 水洗
    @Column(name = "score_washing_process")
    private String scoreWashingProcess;

    // 二烧前配混料
    @Column(name = "score_pre_second_burn_mixing")
    private String scorePreSecondBurnMixing;

    // 二次烧结
    @Column(name = "score_second_sintering")
    private String scoreSecondSintering;

    // 二次粉体处理
    @Column(name = "score_final_crushing_process")
    private String scoreFinalCrushingProcess;

    // 检验
    @Column(name = "score_inspection")
    private String scoreInspection;

    // 包装出库
    @Column(name = "score_packaging_spec")
    private String scorePackagingSpec;

    // 实施计划ID
    @Column(name = "process_audit_implement_plan_id")
    private String processAuditImplementPlanId;
}
