package cec.jiutian.bc.supplierManagement.domain.supplierResolution.handler;

import cec.jiutian.bc.supplierManagement.domain.supplierResolution.model.ReSupplyProduct;
import cec.jiutian.bc.supplierManagement.domain.supplierResolution.model.SupplierResolution;
import cec.jiutian.bc.supplierManagement.domain.supplierResolution.mto.SupplyProductMTO;
import cec.jiutian.view.ReferenceAddType;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class ResolutionProductReferenceAddHandler implements ReferenceAddType.ReferenceAddHandler<SupplierResolution, SupplyProductMTO>{
    @Override
    public Map<String, Object> handle(SupplierResolution supplierResolution, List<SupplyProductMTO> supplyProducts) {
        Map<String, Object> result = new HashMap<>();
        List<ReSupplyProduct> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(supplyProducts)) {
            supplyProducts.forEach(supplyProduct -> {
                ReSupplyProduct reSupplyProduct = ReSupplyProduct.createByProduct(supplyProduct);
                list.add(reSupplyProduct);
            });
        }
        result.put("reSupplyProducts",list);
        return result;
    }

}
