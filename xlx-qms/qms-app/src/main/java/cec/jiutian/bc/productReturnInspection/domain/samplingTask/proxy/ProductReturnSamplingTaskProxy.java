package cec.jiutian.bc.productReturnInspection.domain.samplingTask.proxy;

import cec.jiutian.bc.productReturnInspection.domain.samplingTask.model.ProductReturnSamplingTask;
import cec.jiutian.view.fun.DataProxy;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/3/10
 * @description TODO
 */
@Component
public class ProductReturnSamplingTaskProxy implements DataProxy<ProductReturnSamplingTask> {

    @Override
    public void beforeAdd(ProductReturnSamplingTask productReturnSamplingTask) {
        DataProxy.super.beforeAdd(productReturnSamplingTask);
    }
}
