package cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.enumration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

public class MyCorrectiveExecProgressEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum value : Enum.values()) {
            list.add(new VLModel(value.name(), value.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        //对应CpmBusinessStateEnum, key不变 value换个说法
        TO_BE_CORRECTED("未完成"),
        //单条纠正措施执行完->已执行
        RUN_SUBMIT("已完成")
        ;

        private final String value;

    }
}
