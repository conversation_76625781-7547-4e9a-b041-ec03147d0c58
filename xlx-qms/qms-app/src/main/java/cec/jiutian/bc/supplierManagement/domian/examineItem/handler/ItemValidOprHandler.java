package cec.jiutian.bc.supplierManagement.domian.examineItem.handler;

import cec.jiutian.bc.supplierManagement.domian.examineItem.model.ExamineItem;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/18
 * @description TODO
 */
@Component
public class ItemValidOprHandler implements OperationHandler<ExamineItem, Void> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<ExamineItem> data, Void modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data) && CollectionUtils.isNotEmpty(Arrays.asList(param))) {
            ExamineItem examineItem = data.get(0);
            examineItem.setStatus(param[0]);
            fabosJsonDao.mergeAndFlush(examineItem);
        }
        return "alert(操作成功)";
    }
}
