package cec.jiutian.bc.productReturnInspection.domain.inspectionTask.model;

import cec.jiutian.bc.basicData.domain.inspectionStandard.model.InspectionStandard;
import cec.jiutian.bc.basicData.enumeration.ApplyRangeEnum;
import cec.jiutian.bc.materialInspect.enumeration.TaskBuildTypeEnum;
import cec.jiutian.bc.modeler.enumration.MaterialLevelEnum;
import cec.jiutian.bc.modeler.enumration.TaskBusinessStateEnum;
import cec.jiutian.bc.modeler.enumration.UnqualifiedLevelEnum;
import cec.jiutian.bc.productReturnInspection.domain.inspectionRequest.model.ProductReturnInspectionRequest;
import cec.jiutian.bc.productReturnInspection.domain.inspectionTask.handler.*;
import cec.jiutian.bc.productReturnInspection.domain.inspectionTask.proxy.ProductReturnInspectionTaskProxy;
import cec.jiutian.bc.productReturnInspection.domain.publicInspectionTask.mto.ProductReturnDistributeInsTaskMTO;
import cec.jiutian.bc.productReturnInspection.enumeration.InspectionResultEnum;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.*;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.RowBaseOperation;
import cec.jiutian.view.type.RowOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/10
 * @description TODO
 */
@FabosJson(
        name = "检验任务",
        orderBy = "ProductReturnInspectionTask.createDate desc",
        filter = @Filter(value = "inspectionType = 'productReturnInspect'"),
        dataProxy = ProductReturnInspectionTaskProxy.class,
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "currentState != 'EDIT'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "currentState != 'EDIT'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
        },
        rowOperation = {
                @RowOperation(
                        code = "ProductReturnInspectionTask@TERMINATION",
                        mode = RowOperation.Mode.SINGLE,
                        type = RowOperation.Type.POPUP,
                        popupType = RowOperation.PopupType.FORM,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        operationHandler = TerminateProReInsTaskHandler.class,
                        fabosJsonClass = ProductReturnTerminationInspectionTask.class,
                        callHint = "是否确认终止?",
                        title = "终止检验",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "ProductReturnInspectionTask@TERMINATION"
                        ),
                        ifExpr = "currentState != 'EXECUTE'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "发布",
                        code = "ProductReturnInspectionTask@PUBLISH",
                        operationHandler = ProductReturnPublishOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "是否确定操作？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "ProductReturnInspectionTask@PUBLISH"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "currentState != 'EDIT'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        code = "ProductReturnInspectionTask@DISTRIBUTE",
                        title = "分配任务",
                        type = RowOperation.Type.POPUP,
                        mode = RowOperation.Mode.SINGLE,
                        ifExpr = "currentState != 'EXECUTE' || businessState !='BE_INSPECT'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = ProductReturnDistributeInsTaskMTO.class,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        operationHandler = ProductReturnDistributeInsTaskHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "ProductReturnInspectionTask@DISTRIBUTE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
                @RowOperation(
                        code = "ProductReturnInspectionTask@PICKUP",
                        title = "领取任务",
                        mode = RowOperation.Mode.SINGLE,
                        ifExpr = "currentState != 'EXECUTE' || businessState !='BE_INSPECT'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        operationHandler = ProductReturnPickupInsTaskHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "ProductReturnInspectionTask@PICKUP"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
                @RowOperation(
                        title = "结果审核",
                        code = "ProductReturnInspectionTask@RESULTEXMINE",
                        mode = RowOperation.Mode.SINGLE,
                        type = RowOperation.Type.POPUP,
                        popupType = RowOperation.PopupType.FORM,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        operationHandler = ExamineProReInsTaskHandler.class,
                        fabosJsonClass = ProductReturnTerminationInspectionTask.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "ProductReturnInspectionTask@RESULTEXMINE"
                        ),
                        ifExpr = "businessState != 'INSPECT_FINISH'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                )
        }
)
@Table(name = "mi_inspection_task",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Getter
@Setter
@TemplateType(type = "multiTable")
public class ProductReturnInspectionTask extends MetaModel {

    @FabosJsonField(
            views = @View(title = "编号"),
            edit = @Edit(title = "编号", notNull = true, search = @Search),
            dynamicField = @DynamicField(dependFiled = @DependFiled(buttonName = "生成", changeBy = "id",
                    dynamicHandler = ProductReturnTaskCodeGenerateDynamicHandler.class))
    )
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "检验状态"),
            edit = @Edit(title = "检验状态", show = false, type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = TaskBusinessStateEnum.class))
    )
    private String businessState;

    @FabosJsonField(
            views = @View(title = "当前状态"),
            edit = @Edit(title = "当前状态", show = false, type = EditType.CHOICE, search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = OrderCurrentStateEnum.class))
    )
    private String currentState;

    @FabosJsonField(
            views = @View(title = "质检标准", column = "generalCode"),
            edit = @Edit(title = "质检标准",
                    readonly = @Readonly(add = false), allowAddMultipleRows = false,
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    filter = @Filter(value = "InspectionStandard.type = 'Other' and InspectionStandard.status = 'Effective'"),
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode")
            )
    )
    @ManyToOne
    @JoinColumn(name = "inspection_standard_id")
    private InspectionStandard inspectionStandard;

    @FabosJsonField(
            views = @View(title = "留样量"),
            edit = @Edit(title = "留样量",
                    readonly = @Readonly(),
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inspectionStandard", beFilledBy = "sampleQuantity"))
    )
    private String insSampleQuantity;

    @FabosJsonField(
            views = @View(title = "创建类型"),
            edit = @Edit(title = "创建类型", show = false, type = EditType.CHOICE, search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = TaskBuildTypeEnum.class))
    )
    private String buildType;

    @FabosJsonField(
            views = @View(title = "检验类型"),
            edit = @Edit(title = "检验类型", show = false, type = EditType.CHOICE, search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = ApplyRangeEnum.class))
    )
    private String inspectionType;

    @FabosJsonField(
            views = @View(title = "是否加急"),
            edit = @Edit(title = "是否加急", defaultVal = "false"
            )
    )
    private Boolean isUrgent;


    @FabosJsonField(
            views = @View(title = "检验结果"),
            edit = @Edit(title = "检验结果", type = EditType.CHOICE, search = @Search(), show = false,
                    choiceType = @ChoiceType(fetchHandler = InspectionResultEnum.class))
    )
    private String inspectionResult;

    @FabosJsonField(
            views = @View(title = "不合格等级"),
            edit = @Edit(title = "不合格等级", notNull = true, type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = UnqualifiedLevelEnum.class),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "inspectionResult == 'UNQUALIFIED'"))
    )
    private String unqualifiedLevel;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "检验通知", column = "generalCode", type = ViewType.TABLE_FORM),
            edit = @Edit(title = "检验通知",
                    type = EditType.REFERENCE_TABLE, allowAddMultipleRows = false,
                    notNull = true,
                    filter = @Filter(value = "ProductReturnInspectionRequest.type = 'Return' and businessState in 'BE_INSPECT'"),
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode")
            )
    )
    private ProductReturnInspectionRequest inspectionRequest;

    @FabosJsonField(
            views = @View(title = "检验通知单号"),
            edit = @Edit(title = "检验通知单号",
                    readonly = @Readonly
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inspectionRequest", beFilledBy = "sourceRequestNumber"))
    )
    private String insCode;

    @FabosJsonField(
            views = @View(title = "供应商名称"),
            edit = @Edit(title = "供应商名称",show = false, search = @Search(vague = true)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inspectionRequest", beFilledBy = "supplierName"))
    )
    private String supplierName;

//    @Transient
//    @FabosJsonField(
//            views = @View(title = "选择检验部门", show = false, column = "name"),
//            edit = @Edit(title = "选择检验部门",
//                    type = EditType.REFERENCE_TABLE,
//                    allowAddMultipleRows = false,
//                    referenceTableType = @ReferenceTableType()
//            )
//    )
//    private OrgMTO orgMTO;

    @FabosJsonField(
            views = @View(title = "检验部门id", show = false),
            edit = @Edit(title = "检验部门id", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inspectionRequest", beFilledBy = "inspectionDepartment"))
    )
    private String inspectionDepartment;

    @FabosJsonField(
            views = @View(title = "检验部门"),
            edit = @Edit(title = "检验部门", notNull = true, search = @Search(vague = true)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inspectionRequest", beFilledBy = "inspectionDepartmentName"))
    )
    private String inspectionDepartmentName;

    @FabosJsonField(
            views = @View(title = "检验人id", show = false),
            edit = @Edit(title = "检验人id", show = false)
    )
    private String userId;

    @FabosJsonField(
            views = @View(title = "检验人"),
            edit = @Edit(title = "检验人", show = false, search = @Search(vague = true))
    )
    private String userName;

    @FabosJsonField(
            views = @View(title = "检验物资编码"),
            edit = @Edit(title = "检验物资编码", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inspectionRequest", beFilledBy = "materialCode"))
    )
    private String materialCode;

    @FabosJsonField(
            views = @View(title = "检验物资名称"),
            edit = @Edit(title = "检验物资名称", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inspectionRequest", beFilledBy = "materialName"))
    )
    private String materialName;

    @FabosJsonField(
            views = @View(title = "来料批号"),
            edit = @Edit(title = "来料批号"),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inspectionRequest", beFilledBy = "originLotId"))
    )
    private String originLotId;

    @FabosJsonField(
            views = @View(title = "来料数量"),
            edit = @Edit(title = "来料数量", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inspectionRequest", beFilledBy = "arrivalQuantity"))
    )
    private Double arrivalQuantity;

    @FabosJsonField(
            views = @View(title = "样品数量"),
            edit = @Edit(title = "样品数量", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inspectionRequest", beFilledBy = "sampleQuantity"))
    )
    private Double sampleQuantity;

    @FabosJsonField(
            views = @View(title = "规格"),
            edit = @Edit(title = "规格"),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inspectionRequest", beFilledBy = "materialSpecification"))
    )
    private String materialSpecification;

    @FabosJsonField(
            views = @View(title = "物资分级"),
            edit = @Edit(title = "物资分级", notNull = true, type = EditType.CHOICE, choiceType = @ChoiceType(fetchHandler = MaterialLevelEnum.class)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inspectionRequest", beFilledBy = "materialLevel"))
    )
    private String materialLevel;

    @FabosJsonField(
            views = @View(title = "取样总数"),
            edit = @Edit(title = "取样总数", readonly = @Readonly, numberType = @NumberType(min = 0, max = 9999999, precision = 2)),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = {"standardDetailList", "productReturnInspectionTaskDetailList"}, dynamicHandler = ProductTotalSampleQuantityDynamicHandler.class))
    )
    private Double allInspectionItemQuantity;

    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumn(name = "inspection_task_id")
    @OrderBy
    @FabosJsonField(
            views = @View(title = "检验物资明细", type = ViewType.TABLE_VIEW, index = 8),
            edit = @Edit(title = "检验物资明细", type = EditType.TAB_REFERENCE_GENERATE),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "inspectionRequest", dynamicHandler = ProductReturnRequestDynamicHandler.class)),
            referenceGenerateType = @ReferenceGenerateType(editable = "requestQuantity")
    )
    private List<ProductReturnInspectionTaskDetail> productReturnInspectionTaskDetailList;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = false)
    @FabosJsonField(
            views = @View(title = "检验项目明细", type = ViewType.TABLE_VIEW, index = 8),
            edit = @Edit(title = "检验项目明细", type = EditType.TAB_REFERENCE_GENERATE),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "inspectionStandard", dynamicHandler = ProductReturnInspectionStandardDetailDynamicHandler.class)),
            referenceGenerateType = @ReferenceGenerateType()
    )
    @JoinColumn(name = "inspection_task_id")
    private List<ProductReturnQualityInspectionStandardDetail> standardDetailList;

    @FabosJsonField(
            views = @View(title = "备注"),
            edit = @Edit(title = "备注",
                    type = EditType.TEXTAREA)
    )
    private String remark;

    @FabosJsonField(
            views = @View(title = "创建时间",type = ViewType.DATE_TIME),
            edit = @Edit(title = "创建时间",
                    show = false,
                dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @CreationTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date createDate;

    @FabosJsonField(
            views = @View(title = "检验时间",type = ViewType.DATE_TIME),
            edit = @Edit(title = "检验时间",
                    show = false,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date insTime;

    @FabosJsonField(
            views = @View(title = "检验完成时间"),
            edit = @Edit(title = "检验完成时间",
                    dateType = @DateType(type = DateType.Type.DATE_TIME),
                    show = false
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date detectCompleteTime;

}
