package cec.jiutian.bc.materialInspect.repository;

import cec.jiutian.bc.materialInspect.remote.dto.IncomingQualifiedTrendDTO;
import org.springframework.data.jpa.repository.JpaRepository;

/**
 * <AUTHOR>
 * @date 2025/7/7
 * @description TODO
 */
public interface IncomingQualifiedTrendDTORepository extends JpaRepository<IncomingQualifiedTrendDTO, Long> {

//    @Query(value = "WITH date_range AS ( " +
//            "SELECT " +
//            "    CASE " +
//            "        WHEN :#{#isDiyTime} = '1' THEN TO_DATE(TO_CHAR(:#{#startTime}, 'yyyy-MM-dd'), 'yyyy-MM-dd') " +
//            "        WHEN :#{#query} = '1' THEN TRUNC(CURRENT_DATE, 'YEAR') " +
//            "        WHEN :#{#query} = '2' THEN TRUNC(CURRENT_DATE, 'Q') " +
//            "        WHEN :#{#query} = '3' THEN TRUNC(CURRENT_DATE, 'MM') " +
//            "        WHEN :#{#query} = '4' THEN TRUNC(CURRENT_DATE, 'IW') " +
//            "        WHEN :#{#query} = '5' THEN CURRENT_DATE " +
//            "        ELSE (SELECT MIN(detect_complete_time) FROM mi_inspection_task) " +
//            "    END AS min_time, " +
//            "    CASE " +
//            "        WHEN :#{#isDiyTime} = '1' THEN TO_DATE(TO_CHAR(:#{#endTime}, 'yyyy-MM-dd'), 'yyyy-MM-dd') " +
//            "        WHEN :#{#query} = '1' THEN ADD_MONTHS(TRUNC(CURRENT_DATE, 'YEAR'), 12) - INTERVAL '1' SECOND " +
//            "        WHEN :#{#query} = '2' THEN ADD_MONTHS(TRUNC(CURRENT_DATE, 'Q'), 3) - INTERVAL '1' SECOND " +
//            "        WHEN :#{#query} = '3' THEN ADD_MONTHS(TRUNC(CURRENT_DATE, 'MM'), 1) - INTERVAL '1' SECOND " +
//            "        WHEN :#{#query} = '4' THEN ADD_MONTHS(TRUNC(CURRENT_DATE, 'IW'), 1) - INTERVAL '1' SECOND " +
//            "        WHEN :#{#query} = '5' THEN CURRENT_DATE + INTERVAL '23:59:59' HOUR TO SECOND " +
//            "        ELSE (SELECT MAX(detect_complete_time) FROM mi_inspection_task) " +
//            "    END AS max_time " +
//            "), " +
//            "date_series AS ( " +
//            "    SELECT " +
//            "        ADDDATE((SELECT min_time FROM date_range), pos) + INTERVAL '23:59:59' HOUR TO SECOND AS date_value " +
//            "    FROM ( " +
//            "        SELECT posexplode(SPLIT(REPEAT('o', DATEDIFF((SELECT max_time FROM date_range), (SELECT min_time FROM date_range))), 'o')) AS (pos, val) " +
//            "    ) t " +
//            "), " +
//            "date_display AS ( " +
//            "    SELECT " +
//            "        date_value, " +
//            "        CASE :#{#dimension[0]} " +
//            "            WHEN '1' THEN CONCAT(EXTRACT(YEAR FROM date_value), '年') " +
//            "            WHEN '2' THEN CONCAT(EXTRACT(YEAR FROM date_value), '年Q', QUARTER(date_value)) " +
//            "            WHEN '3' THEN CONCAT(EXTRACT(YEAR FROM date_value), '年', LPAD(EXTRACT(MONTH FROM date_value), 2, '0'), '月') " +
//            "            WHEN '4' THEN CONCAT( " +
//            "                LPAD(EXTRACT(MONTH FROM DATE_SUB(date_value, INTERVAL WEEKDAY(date_value) DAY)), 2, '0'), '月', " +
//            "                LPAD(EXTRACT(DAY FROM DATE_SUB(date_value, INTERVAL WEEKDAY(date_value) DAY)), 2, '0'), '日-', " +
//            "                LPAD(EXTRACT(MONTH FROM DATE_SUB(date_value, INTERVAL WEEKDAY(date_value) - 6 DAY)), 2, '0'), '月', " +
//            "                LPAD(EXTRACT(DAY FROM DATE_SUB(date_value, INTERVAL WEEKDAY(date_value) - 6 DAY)), 2, '0'), '日' " +
//            "            ) " +
//            "            WHEN '5' THEN CONCAT( " +
//            "                LPAD(EXTRACT(MONTH FROM date_value), 2, '0'), '月', " +
//            "                LPAD(EXTRACT(DAY FROM date_value), 2, '0'), '日' " +
//            "            ) " +
//            "            ELSE CONCAT( " +
//            "                LPAD(EXTRACT(MONTH FROM date_value), 2, '0'), '月', " +
//            "                LPAD(EXTRACT(DAY FROM date_value), 2, '0'), '日' " +
//            "            ) " +
//            "        END AS time_display " +
//            "    FROM date_series " +
//            "), " +
//            "inspection_task_status AS ( " +
//            "    SELECT " +
//            "        TO_DATE(detect_complete_time) AS check_date, " +
//            "        arrival_quantity, " +
//            "        all_inspection_item_quantity, " +
//            "        CASE " +
//            "            WHEN inspection_result = 'QUALIFIED' THEN TRUE " +
//            "            ELSE FALSE " +
//            "        END AS abnormal " +
//            "    FROM mi_inspection_task " +
//            "    WHERE inspection_type = 'materialInspect' " +
//            "    AND business_state = 'INSPECT_FINISH' " +
//            "    AND TO_DATE(detect_complete_time) >= (SELECT min_time FROM date_range) " +
//            "    AND TO_DATE(detect_complete_time) <= (SELECT max_time FROM date_range) " +
//            "    <#if supplierName> " +
//            "        AND supplier_name = :#{#supplierName} " +
//            "    </#if> " +
//            "    <#if materialCode> " +
//            "        AND material_code LIKE CONCAT('%', :#{#materialCode}, '%') " +
//            "    </#if> " +
//            "    <#if materialTypeName> " +
//            "        AND material_code IN ( " +
//            "            SELECT fcn.spcfcn_cd " +
//            "            FROM ms_spcdct dct " +
//            "            LEFT JOIN ms_spcfcn fcn ON dct.spcdct_cd = fcn.spcdct_cd " +
//            "            WHERE dct.spcdct_nm LIKE CONCAT('%', :#{#materialTypeName}, '%') " +
//            "        ) " +
//            "    </#if> " +
//            "    <#if feature> " +
//            "        AND id IN ( " +
//            "            SELECT inspection_task_id " +
//            "            FROM mi_Quality_inspection_standard_detail " +
//            "            WHERE feature = :#{#feature} " +
//            "        ) " +
//            "    </#if> " +
//            "    <#if inspectName> " +
//            "        AND id IN ( " +
//            "            SELECT inspection_task_id " +
//            "            FROM mi_Quality_inspection_standard_detail " +
//            "            WHERE name = :#{#inspectName} " +
//            "        ) " +
//            "    </#if> " +
//            "), " +
//            "daily_stats AS ( " +
//            "    SELECT " +
//            "        CASE :#{#dimension} " +
//            "            WHEN '1' THEN CONCAT(EXTRACT(YEAR FROM check_date), '年') " +
//            "            WHEN '2' THEN CONCAT(EXTRACT(YEAR FROM check_date), '年Q', QUARTER(check_date)) " +
//            "            WHEN '3' THEN CONCAT(EXTRACT(YEAR FROM check_date), '年', LPAD(EXTRACT(MONTH FROM check_date), 2, '0'), '月') " +
//            "            WHEN '4' THEN CONCAT( " +
//            "                LPAD(EXTRACT(MONTH FROM DATE_SUB(check_date, INTERVAL WEEKDAY(check_date) DAY)), 2, '0'), '月', " +
//            "                LPAD(EXTRACT(DAY FROM DATE_SUB(check_date, INTERVAL WEEKDAY(check_date) DAY)), 2, '0'), '日-', " +
//            "                LPAD(EXTRACT(MONTH FROM DATE_SUB(check_date, INTERVAL WEEKDAY(check_date) - 6 DAY)), 2, '0'), '月', " +
//            "                LPAD(EXTRACT(DAY FROM DATE_SUB(check_date, INTERVAL WEEKDAY(check_date) - 6 DAY)), 2, '0'), '日' " +
//            "            ) " +
//            "            WHEN '5' THEN CONCAT( " +
//            "                LPAD(EXTRACT(MONTH FROM check_date), 2, '0'), '月', " +
//            "                LPAD(EXTRACT(DAY FROM check_date), 2, '0'), '日' " +
//            "            ) " +
//            "            ELSE CONCAT( " +
//            "                LPAD(EXTRACT(MONTH FROM check_date), 2, '0'), '月', " +
//            "                LPAD(EXTRACT(DAY FROM check_date), 2, '0'), '日' " +
//            "            ) " +
//            "        END AS time_display, " +
//            "        COUNT(*) AS total_checks, " +
//            "        SUM(CASE WHEN abnormal THEN 0 ELSE all_inspection_item_quantity END) AS unqualified_count, " +
//            "        SUM(arrival_quantity) AS arrival_count " +
//            "    FROM inspection_task_status " +
//            "    GROUP BY " +
//            "        CASE :#{#dimension} " +
//            "            WHEN '1' THEN EXTRACT(YEAR FROM check_date) " +
//            "            WHEN '2' THEN CONCAT(EXTRACT(YEAR FROM check_date), 'Q', QUARTER(check_date)) " +
//            "            WHEN '3' THEN CONCAT(EXTRACT(YEAR FROM check_date), LPAD(EXTRACT(MONTH FROM check_date), 2, '0')) " +
//            "            WHEN '4' THEN DATE_SUB(check_date, INTERVAL WEEKDAY(check_date) DAY) " +
//            "            WHEN '5' THEN check_date " +
//            "            ELSE check_date " +
//            "        END " +
//            ") " +
//            "SELECT " +
//            "    dd.time_display AS timeDisplay, " +
//            "    COALESCE(ds.arrival_count, 0) AS arrival_count, " +
//            "    COALESCE(ds.unqualified_count, 0) AS unqualified_count, " +
//            "    CASE " +
//            "        WHEN COALESCE(ds.arrival_count, 0) = 0 THEN 1 " +
//            "        ELSE ROUND((ds.arrival_count - COALESCE(ds.unqualified_count, 0)) * 1.0 / ds.arrival_count, 2) " +
//            "    END AS qualification_rate, " +
//            "    0.5 AS target_rate " +
//            "FROM date_display dd " +
//            "LEFT JOIN daily_stats ds ON ds.time_display = dd.time_display",
//            nativeQuery = true)
//    List<IncomingQualifiedTrendDTO> queryTrend(@Param("isDiyTime") String isDiyTime,
//                                               @Param("query") String query,
//                                               @Param("startTime") Date startTime,
//                                               @Param("endTime") Date endTime,
//                                               @Param("dimension") String dateDimension,
//                                               @Param("supplierName") String supplierName,
//                                               @Param("materialCode") String materialCode,
//                                               @Param("inspectName") String inspectItemName,
//                                               @Param("feature") String featureName,
//                                               @Param("materialType") String materialTypeName);
}
