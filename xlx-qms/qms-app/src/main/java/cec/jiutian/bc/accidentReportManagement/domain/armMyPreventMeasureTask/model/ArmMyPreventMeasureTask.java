package cec.jiutian.bc.accidentReportManagement.domain.armMyPreventMeasureTask.model;

import cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.enumration.ArmBusinessStatusEnum;
import cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.model.ArmPreventMeasure;
import cec.jiutian.bc.accidentReportManagement.domain.armMyPreventMeasureTask.handler.ArmMyPreventMeasureTaskExecHandler;
import cec.jiutian.bc.accidentReportManagement.domain.armMyPreventMeasureTask.handler.ArmMyPreventMeasureTaskSubmitHandler;
import cec.jiutian.bc.accidentReportManagement.domain.armMyPreventMeasureTask.proxy.ArmMyPreventMeasureTaskProxy;
import cec.jiutian.bc.accidentReportManagement.enumeration.ArmMeasureStatusEnum;
import cec.jiutian.bc.materialInspect.domain.publicInspectionTask.mto.UserForInsTaskMTO;
import cec.jiutian.bc.mto.OrgMTO;
import cec.jiutian.bc.mto.FactoryArea;
import cec.jiutian.bc.urm.domain.dictionary.handler.DictChoiceFetchHandler;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@Entity
@Table(name = "qms_arm_accident_report_task")
@FabosJson(
        name = "我的预防措施",
        orderBy = "ArmMyPreventMeasureTask.createTime desc",
        power = @Power(add = false, edit = false, export = false, importable = false),
        filter = @Filter("preventMeasureStatus in ('SUBMITTED')"),
        dataProxy = ArmMyPreventMeasureTaskProxy.class,
        rowOperation = {
                @RowOperation(
                        title = "任务执行",
                        code = "ArmMyPreventMeasureTask@Exec",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        operationHandler = ArmMyPreventMeasureTaskExecHandler.class,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = ArmMyPreventMeasureTaskExec.class,
                        ifExpr = "selectedItems[0].rowOperationAuthFlag == 0",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "ArmMyPreventMeasureTask@Exec"
                        )
                ),
                @RowOperation(
                        title = "任务提交",
                        code = "ArmMyPreventMeasureTask@Submit",
                        mode = RowOperation.Mode.HEADER,
                        operationHandler = ArmMyPreventMeasureTaskSubmitHandler.class,
                        callHint = "请确认是否提交？",
                        ifExpr = "selectedItems[0].rowOperationAuthFlag == 0",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "ArmMyPreventMeasureTask@Submit"
                        )
                ),
        }
)
@TemplateType(type = "multiTable")
public class ArmMyPreventMeasureTask extends MetaModel {

    //创建
    @FabosJsonField(
            views = @View(title = "任务单号"),
            edit = @Edit(title = "任务单号",
                    readonly = @Readonly(edit = false)
            )
    )
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "车间", column = "factoryAreaName"),
            edit = @Edit(title = "车间",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "factoryAreaName"),
                    allowAddMultipleRows = false,
                    filter = @Filter(value = "FactoryArea.factoryAreaTypeCode = '02'")
            )
    )
    @ManyToOne
    private FactoryArea factoryArea;

    @FabosJsonField(
            views = @View(title = "事故类型"),
            edit = @Edit(title = "事故类型",
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = DictChoiceFetchHandler.class,
                            fetchHandlerParams = "armAccidentType")
            )
    )
    private String armAccidentType;

    @FabosJsonField(
            views = @View(title = "事故等级"),
            edit = @Edit(title = "事故等级",
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = DictChoiceFetchHandler.class,
                            fetchHandlerParams = "armAccidentLevel")
            )
    )
    private String armAccidentLevel;

    @FabosJsonField(
            views = @View(title = "发生日期"),
            edit = @Edit(title = "发生日期",
                    notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date occurrenceDate;

    @FabosJsonField(
            views = @View(title = "事故描述"),
            edit = @Edit(title = "事故描述",
                    notNull = true,
                    type = EditType.TEXTAREA)
    )
    private String accidentDescription;

    @FabosJsonField(
            views = @View(title = "责任部门", column = "name"),
            edit = @Edit(title = "责任部门",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType(type = TabTableReferType.SelectShowTypeMTM.LIST)
            )
    )
    @ManyToOne
    @JoinColumn(name = "response_department_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private OrgMTO orgMTO;

    @FabosJsonField(
            views = @View(title = "责任人", column = "name"),
            edit = @Edit(title = "责任人",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType(type = TabTableReferType.SelectShowTypeMTM.LIST)
            )
    )
    @ManyToOne
    @JoinColumn(name = "person_responsible_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private UserForInsTaskMTO userForInsTaskMTO;

    @FabosJsonField(
            views = @View(title = "预计纳期"),
            edit = @Edit(title = "预计纳期",
                    notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date expectedDeliveryDate;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",
                    show = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ArmBusinessStatusEnum.class)
            )
    )
    private String businessStatus;

    //原因分析
    @FabosJsonField(
            views = @View(title = "事故原因（5why 分析））"),
            edit = @Edit(title = "事故原因（5why 分析）",
                    type = EditType.TEXTAREA)
    )
    private String accidentCause;

    @FabosJsonField(
            views = @View(title = "原因分析附件", show = false),
            edit = @Edit(title = "原因分析附件", type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType)
    )
    private String causeAnalysisAttachments;

    //预防措施
    @FabosJsonField(
            edit = @Edit(title = "预防措施", type = EditType.TAB_TABLE_ADD),
            views = @View(title = "预防措施", type = ViewType.TABLE_VIEW)
    )
    @JoinColumn(name = "qms_arm_accident_report_task_id")
    @OneToMany(cascade = CascadeType.ALL)
    @OrderBy
    private List<ArmPreventMeasure> armPreventMeasureList;

    @FabosJsonField(
            views = @View(title = "预防措施状态", show = false),
            edit = @Edit(title = "预防措施状态",
                    readonly = @Readonly(),
                    type = EditType.CHOICE,
                    show = false,
                    choiceType = @ChoiceType(fetchHandler = ArmMeasureStatusEnum.class)
            )
    )
    private String preventMeasureStatus;

    @FabosJsonField(
            views = @View(title = "预防措施下所有责任人的id集合", show = false),
            edit = @Edit(title = "预防措施下所有责任人的id集合",show = false)
    )
    private String preventUserIds;
}
