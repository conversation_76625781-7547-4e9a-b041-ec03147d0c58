package cec.jiutian.bc.productExamineManagement.domain.inspectionTask.handler;

import cec.jiutian.bc.basicData.domain.inspectionStandard.model.InspectionStandard;
import cec.jiutian.bc.modeler.domain.inspectionItem.model.InspectionItem;
import cec.jiutian.bc.productExamineManagement.domain.inspectionTask.model.ProductExamineInspectionItemDetail;
import cec.jiutian.bc.productExamineManagement.domain.inspectionTask.model.ProductExamineInspectionTask;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/3/11
 * @description TODO
 */
@Component
public class ProductExamineInspectionStandardDetailDynamicHandler implements DependFiled.DynamicHandler<ProductExamineInspectionTask> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public Map<String, Object> handle(ProductExamineInspectionTask productExamineInspectionTask) {

        Map<String, Object> result = new HashMap<>();
        List<ProductExamineInspectionItemDetail> inspectionStandardDetailList = new ArrayList<>();
        if (productExamineInspectionTask.getInspectionStandard() != null) {
            // 带出质检标准
            InspectionStandard inspectionStandard = fabosJsonDao.getById(InspectionStandard.class, productExamineInspectionTask.getInspectionStandard().getId());
            if (CollectionUtils.isNotEmpty(inspectionStandard.getDetails())) {
                inspectionStandard.getDetails().forEach(detail -> {
                    ProductExamineInspectionItemDetail productExamineInspectionItemDetail = new ProductExamineInspectionItemDetail();
                    // 带出质检标准下检验项目
                    InspectionItem inspectionItem = fabosJsonDao.getById(InspectionItem.class, detail.getInspectionItem().getId());
                    productExamineInspectionItemDetail.setItemId(inspectionItem.getId());
                    productExamineInspectionItemDetail.setCode(inspectionItem.getGeneralCode());
                    productExamineInspectionItemDetail.setName(inspectionItem.getName());
                    productExamineInspectionItemDetail.setItemType(inspectionItem.getItemType());
                    productExamineInspectionItemDetail.setFeature(inspectionItem.getFeature());
                    productExamineInspectionItemDetail.setSendInspectPoint(inspectionItem.getSendPointMTO().getName());
                    productExamineInspectionItemDetail.setSamplingPoint(inspectionItem.getSamplingPoint());
                    productExamineInspectionItemDetail.setSamplingPoint(inspectionItem.getSamplingPoint());
                    productExamineInspectionItemDetail.setSendInspectPoint(inspectionItem.getSendPointMTO().getName());
                    productExamineInspectionItemDetail.setGroupId(inspectionItem.getGroupId());
                    inspectionStandardDetailList.add(productExamineInspectionItemDetail);
                });
            }
        }

        result.put("standardDetailList", inspectionStandardDetailList);
        return result;
    }
}
