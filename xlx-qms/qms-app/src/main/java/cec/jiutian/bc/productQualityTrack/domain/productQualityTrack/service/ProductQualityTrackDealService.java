package cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.service;


import cec.jiutian.bc.mto.TechnologyFLowMTO;
import cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.mto.*;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/07/18
 * @description TODO
 */
@Slf4j
@Service
public class ProductQualityTrackDealService {

    @Resource
    private FabosJsonDao fabosJsonDao;

    public void disposeProcessOperationList(ProductQualityTrackCreateMTO model, List<PqtProcessOperationCreateMTO> pqtProcessOperationList) {
        TechnologyFLowMTO tfCondition = new TechnologyFLowMTO();
        if (model.getFactoryArea() == null) {
            return;
        }
        tfCondition.setWorkshopId(String.valueOf(model.getFactoryArea().getId()));
        tfCondition.setProductionLineId(String.valueOf(model.getFactoryLine().getId()));
        TechnologyFLowMTO tfResult = fabosJsonDao.selectOne(tfCondition);
        if (tfResult != null) {
            WorkmanshipMTO wsCondition = new WorkmanshipMTO();
            wsCondition.setId(tfResult.getProcessId());
            WorkmanshipMTO wsResult = fabosJsonDao.selectOne(wsCondition);
            if (wsResult == null) {
                log.info("工艺id为{}无对应的工艺信息", tfResult.getProcessId());
                return;
            }

            //对应工艺code查询工序：
            ProcessProcedureMTO ppCondition = new ProcessProcedureMTO();
            ppCondition.setProcessCode(wsResult.getProcessCode());
            List<ProcessProcedureMTO> processProcedureMTOList = fabosJsonDao.select(ppCondition);
            if (CollectionUtils.isNotEmpty(processProcedureMTOList)) {
                for (ProcessProcedureMTO poMTO : processProcedureMTOList) {
                    PqtProcessOperationCreateMTO pqtMTO = new PqtProcessOperationCreateMTO();
                    pqtMTO.setProcessOperationCode(poMTO.getProcessOperationCode());
                    pqtMTO.setProcessOperationName(poMTO.getProcessOperationName());

                    //三个产品批号信息
                    List<PqtProductBatchNumberCreateMTO> pqtProductBatchNumberList = new ArrayList<>();
                    pqtProductBatchNumberList.add(getPqtProductBatchNumberCreateMTO(model, poMTO, "1"));
                    pqtProductBatchNumberList.add(getPqtProductBatchNumberCreateMTO(model, poMTO, "2"));
                    pqtProductBatchNumberList.add(getPqtProductBatchNumberCreateMTO(model, poMTO, "3"));
                    pqtMTO.setPqtProductBatchNumberList(pqtProductBatchNumberList);

                    pqtProcessOperationList.add(pqtMTO);
                }
            }
        } else {
            log.info("产线{}无对应的工艺流程", model.getFactoryLine().getFactoryAreaName());
        }
    }

    private PqtProductBatchNumberCreateMTO getPqtProductBatchNumberCreateMTO(ProductQualityTrackCreateMTO model, ProcessProcedureMTO poMTO, String trackingFrequency) {
        PqtProductBatchNumberCreateMTO firstMTO = new PqtProductBatchNumberCreateMTO();
        firstMTO.setFactoryAreaId(String.valueOf(model.getFactoryArea().getId()));
        firstMTO.setFactoryLineId(String.valueOf(model.getFactoryLine().getId()));
        firstMTO.setProcessOperationCode(poMTO.getProcessOperationCode());
        firstMTO.setTrackingFrequency(trackingFrequency);
        return firstMTO;
    }
}
