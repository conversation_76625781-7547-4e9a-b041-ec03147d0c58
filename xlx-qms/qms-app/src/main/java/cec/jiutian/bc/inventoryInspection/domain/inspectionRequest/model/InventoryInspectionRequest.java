package cec.jiutian.bc.inventoryInspection.domain.inspectionRequest.model;

import cec.jiutian.bc.basicData.enumeration.NamingRuleCodeEnum;
import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleBaseModel;
import cec.jiutian.bc.inventoryInspection.enumeration.InspectionRequestTypeEnum;
import cec.jiutian.bc.modeler.enumration.MaterialLevelEnum;
import cec.jiutian.bc.modeler.enumration.TaskBusinessStateEnum;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@FabosJson(
        name = "检验通知",
        orderBy = "createTime desc",
        filter = @Filter(value = "type in ('Overdue','Dull')"),
        power = @Power(add = false, edit = false, delete = false)
)
@Table(name = "mi_inspection_request",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Getter
@Setter
@TemplateType(type = "multiTable")
public class InventoryInspectionRequest extends NamingRuleBaseModel {

    @FabosJsonField(
            views = @View(title = "来源检验通知单号"),
            edit = @Edit(title = "来源检验通知单号")
    )
    private String sourceRequestNumber;

    @FabosJsonField(
            views = @View(title = "物料编码"),
            edit = @Edit(title = "物料编码")
    )
    private String materialCode;

    @FabosJsonField(
            views = @View(title = "物料名称"),
            edit = @Edit(title = "物料名称")
    )
    private String materialName;


    @FabosJsonField(
            views = @View(title = "类型"),
            edit = @Edit(title = "类型", type = EditType.CHOICE, search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = InspectionRequestTypeEnum.class))
    )
    private String type;

    @FabosJsonField(
            views = @View(title = "物资分级"),
            edit = @Edit(title = "物资分级", type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = MaterialLevelEnum.class),
                    search = @Search())
    )
    private String materialLevel;

    @FabosJsonField(
            views = @View(title = "单位"),
            edit = @Edit(title = "单位", notNull = true)
    )
    private String measureUnit;

    @FabosJsonField(
            views = @View(title = "规格"),
            edit = @Edit(title = "规格")
    )
    private String materialSpecification;

    @FabosJsonField(
            views = @View(title = "来料批号", show = false),
            edit = @Edit(title = "来料批号", show = false)
    )
    private String originLotId;

    @FabosJsonField(
            views = @View(title = "来料数量"),
            edit = @Edit(title = "来料数量",
                    inputType = @InputType(type = "number"),
                    readonly = @Readonly)
    )
    private Double arrivalQuantity;

    @FabosJsonField(
            views = @View(title = "样品数量"),
            edit = @Edit(title = "样品数量",
                    inputType = @InputType(type = "number"),
                    readonly = @Readonly)
    )
    private Double originSampleQuantity;

    @FabosJsonField(
            views = @View(title = "供应商名称"),
            edit = @Edit(title = "供应商名称", search = @Search(vague = true))
    )
    private String supplierName;

    @FabosJsonField(
            views = @View(title = "检验部门"),
            edit = @Edit(title = "检验部门", show = false, search = @Search(vague = true))
    )
    private String inspectionDepartmentName;


    @FabosJsonField(
            views = @View(title = "检验部门id", show = false),
            edit = @Edit(title = "检验部门id", show = false)
    )
    private String inspectionDepartment;

    @FabosJsonField(
            views = @View(title = "检验人id", show = false),
            edit = @Edit(title = "检验人id", show = false)
    )
    private String inspector;

    @FabosJsonField(
            views = @View(title = "检验人"),
            edit = @Edit(title = "检验人", search = @Search(vague = true))
    )
    private String inspectorName;

    @FabosJsonField(
            views = @View(title = "当前状态"),
            edit = @Edit(title = "当前状态",
                    readonly = @Readonly(),
                    defaultVal = "EDIT", type = EditType.CHOICE, search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = OrderCurrentStateEnum.class)),
            dynamicField = @DynamicField(passive = true)
    )
    private String currentState;

    @FabosJsonField(
            views = @View(title = "检验状态"),
            edit = @Edit(title = "检验状态", show = false, type = EditType.CHOICE, search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = TaskBusinessStateEnum.class))
    )
    private String businessState;

    @PrePersist
    public void onCreate() {
        if (this.currentState == null) {
            this.currentState = OrderCurrentStateEnum.Enum.EDIT.name();
        }
    }

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "inspection_request_id")
    @OrderBy
    @FabosJsonField(
            edit = @Edit(title = "检验通知明细", type = EditType.TAB_TABLE_ADD),
            views = @View(title = "检验通知明细", type = ViewType.TABLE_VIEW)
    )
    private List<InventoryInspectionRequestDetail> details;

    @Override
    public String getNamingCode() {
        return NamingRuleCodeEnum.InventoryInspectionRequest.name();
    }
}
