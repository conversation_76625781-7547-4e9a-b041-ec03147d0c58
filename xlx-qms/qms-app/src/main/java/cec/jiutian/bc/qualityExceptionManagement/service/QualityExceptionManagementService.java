package cec.jiutian.bc.qualityExceptionManagement.service;

import cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.enumration.ArmBusinessStatusEnum;
import cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.model.AccidentReportTask;
import cec.jiutian.bc.basicData.enumeration.NamingRuleCodeEnum;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.enumration.CpmBusinessStateEnum;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.enumration.RelatedDocumentTypeEnum;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.model.CorrectPreventMeasure;
import cec.jiutian.bc.generalModeler.enumeration.YesOrNoEnum;
import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.generalModeler.util.StringUtils;
import cec.jiutian.bc.mto.OrgMTO;
import cec.jiutian.bc.modeler.enumration.MRBDisposalOpinionEnum;
import cec.jiutian.bc.qualityExceptionManagement.domain.otherDellTask.model.OtherDellTask;
import cec.jiutian.bc.qualityExceptionManagement.domain.returnProductHandling.model.ReturnProductHandling;
import cec.jiutian.bc.qualityExceptionManagement.domain.reworkTask.model.ReworkTask;
import cec.jiutian.bc.qualityExceptionManagement.enums.ReturnProductAnalysisMethodEnum;
import cec.jiutian.bc.qualityExceptionManagement.enums.ReworkTaskStatusEnum;
import cec.jiutian.bc.qualityExceptionManagement.enums.TaskTypeEnum;
import cec.jiutian.bc.questionPaperManagement.domain.questionPaper.model.QuestionPaper;
import cec.jiutian.bc.questionPaperManagement.enums.QuestionPaperStateEnum;
import cec.jiutian.bc.report8D.domain.report8D.model.Report8D;
import cec.jiutian.bc.report8D.enums.MeasureCompleteStatusEnum;
import cec.jiutian.bc.report8D.enums.Report8DStatusEnum;
import cec.jiutian.bc.specialMaterialHandle.domain.model.SpecialMaterialHandleRequest;
import cec.jiutian.bc.specialMaterialHandle.domain.model.SpecialMaterialHandleRequestDetail;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
@Component
public class QualityExceptionManagementService {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private NamingRuleService namingRuleService;

    public String returnProductHandlingReviewPass(String handlingId) {
        String orderNumber = null;
        ReturnProductHandling productHandling = fabosJsonDao.findById(ReturnProductHandling.class, handlingId);
        if (null != productHandling) {
            // 若处置意见为返工，则生成返工处置任务；若为B、C、报废则生成特殊物料处理；剩余其他则生成其他处置任务
            if (productHandling.getHandleFlag().equals(YesOrNoEnum.Enum.Y.name())) {
                String handlingSuggestion = productHandling.getHandlingSuggestion();
                if (StringUtils.equals(handlingSuggestion, MRBDisposalOpinionEnum.Enum.REWORK.name())) {
                    createReworkTaskByReturnHandling(productHandling);
                } else if (StringUtils.equals(handlingSuggestion, MRBDisposalOpinionEnum.Enum.SCRAPPED.name())
                        || StringUtils.equals(handlingSuggestion, MRBDisposalOpinionEnum.Enum.DOWNGRADED_TO_CLASSB.name())
                        || StringUtils.equals(handlingSuggestion, MRBDisposalOpinionEnum.Enum.DOWNGRADED_TO_CLASSC.name())) {
                    createSpecialMaterialHandle(productHandling);
                } else {
                    createOtherTaskByReturnHandling(productHandling);
                }
            }

            // 根据分析方法（预防纠正措施表、8D、一页纸、事故报告），创建对应方法的单据，并关联单号
            ReturnProductAnalysisMethodEnum.Enum analysisMethodType = ReturnProductAnalysisMethodEnum.Enum.valueOf(productHandling.getAnalysisMethod());
            orderNumber = switch (analysisMethodType) {
                case Measure -> createCorrectMeasureByReturnHandling(productHandling);
                case Paper -> createQuestionPaperByReturnHandling(productHandling);
                case EightD -> createEightDByReturnHandling(productHandling);
                case Report -> createAccidentReportByReturnHandling(productHandling);
                default -> null;
            };
        }
        return orderNumber;

    }

    public void createReworkTaskByReturnHandling(ReturnProductHandling productHandling) {
        ReworkTask reworkTask = new ReworkTask();
        reworkTask.setGeneralCode(namingRuleService.getNameCode(reworkTask.getNamingCode(), 1, null).get(0));
        reworkTask.setTaskType(TaskTypeEnum.Enum.CUSTOMER_RETURN.name());
        reworkTask.setTaskCode(productHandling.getGeneralCode());
        reworkTask.setProductCode(productHandling.getProductCode());
        reworkTask.setProductName(productHandling.getProductName());
        reworkTask.setBatchCode(productHandling.getLotSerialId());
        reworkTask.setReworkQuantity(productHandling.getRefundQuantity());
        reworkTask.setStatus(ReworkTaskStatusEnum.Enum.CREATE.name());
        reworkTask.setExamineStatus("0");

        fabosJsonDao.saveOrUpdate(reworkTask);

    }

    public void createOtherTaskByReturnHandling(ReturnProductHandling productHandling) {
        OtherDellTask task = new OtherDellTask();
        task.setGeneralCode(namingRuleService.getNameCode(task.getNamingCode(), 1, null).get(0));
        task.setPlan(productHandling.getHandlingSuggestion());
        task.setTaskType(TaskTypeEnum.Enum.CUSTOMER_RETURN.name());
        task.setTaskCode(productHandling.getGeneralCode());
        task.setProductCode(productHandling.getProductCode());
        task.setProductName(productHandling.getProductName());
        task.setBatchCode(productHandling.getLotSerialId());
        task.setReworkQuantity(productHandling.getRefundQuantity());
        task.setStatus(ReworkTaskStatusEnum.Enum.CREATE.name());
        task.setExamineStatus("0");

        fabosJsonDao.saveOrUpdate(task);

    }

    public void createSpecialMaterialHandle(ReturnProductHandling productHandling) {
        SpecialMaterialHandleRequest specialMaterialHandleRequest = new SpecialMaterialHandleRequest();
        specialMaterialHandleRequest.setGeneralCode(namingRuleService.getNameCode(cec.jiutian.bc.modeler.enumration.NamingRuleCodeEnum.SpecialMaterialHandleRequest.name(), 1, null).get(0));
        specialMaterialHandleRequest.setUseRisk(productHandling.getRefundReason());
        specialMaterialHandleRequest.setRequestDate(new Date());
        specialMaterialHandleRequest.setBusinessState(OrderCurrentStateEnum.Enum.EDIT.name());
        specialMaterialHandleRequest.setExamineStatus("0");

        List<SpecialMaterialHandleRequestDetail> detailList = new ArrayList<>();
        SpecialMaterialHandleRequestDetail detail = new SpecialMaterialHandleRequestDetail();
        detail.setInventoryId(productHandling.getInventoryId());
        detail.setOrderCode(productHandling.getGeneralCode());
        detail.setLotSerialId(productHandling.getLotSerialId());
        detail.setMaterialCode(productHandling.getProductCode());
        detail.setMaterialName(productHandling.getProductName());
        detail.setWeight(productHandling.getRefundQuantity());
        detail.setUnit(productHandling.getMeasureUnitName());
        detailList.add(detail);
        specialMaterialHandleRequest.setDetailList(detailList);

        fabosJsonDao.saveOrUpdate(specialMaterialHandleRequest);

    }

    public String createCorrectMeasureByReturnHandling(ReturnProductHandling productHandling) {
        CorrectPreventMeasure measure = new CorrectPreventMeasure();
        measure.setCorrectPreventMeasureFormNumber(namingRuleService.getNameCode(NamingRuleCodeEnum.CORRECT_PREVENT_MEASURE.name(), 1, null).get(0));
        measure.setRelatedDocumentType(RelatedDocumentTypeEnum.Enum.ReturnProductHandling.name());
        measure.setRelatedDocumentMTOCode(productHandling.getGeneralCode());
        measure.setRelatedDocumentMTOName(productHandling.getGeneralCode());
        measure.setBusinessState(CpmBusinessStateEnum.Enum.TO_BE_RELEASED.name());
        measure.setNonConformanceStatement(productHandling.getRefundReason());
        measure.setVerificationNonConformity(productHandling.getCauseAnalysis());
        fabosJsonDao.saveOrUpdate(measure);

        return measure.getCorrectPreventMeasureFormNumber();
    }

    private String createQuestionPaperByReturnHandling(ReturnProductHandling productHandling) {
        QuestionPaper questionPaper = new QuestionPaper();
        questionPaper.setGeneralCode(namingRuleService.getNameCode(NamingRuleCodeEnum.QuestionPaper.name(), 1, null).get(0));
        questionPaper.setQuestionDescription(productHandling.getRefundReason());
        questionPaper.setOccurDate(LocalDate.now());
        questionPaper.setResponsibleDepartmentId(productHandling.getResponsibleDepartmentId());
        questionPaper.setResponsibleDepartmentName(productHandling.getResponsibleDepartmentName());
        questionPaper.setCurrentState(QuestionPaperStateEnum.Enum.Edit.name());
        fabosJsonDao.saveOrUpdate(questionPaper);

        return questionPaper.getGeneralCode();
    }

    private String createEightDByReturnHandling(ReturnProductHandling productHandling) {
        Report8D report = new Report8D();
        report.setGeneralCode(namingRuleService.getNameCode(cec.jiutian.bc.modeler.enumration.NamingRuleCodeEnum.Report8D.name(), 1, null).get(0));
        report.setTitle("顾客退货产品处理");
        report.setOccurrenceTime(new Date());
        report.setRaisedDate(new Date());
        report.setProductCode(productHandling.getLotSerialId());
        report.setProductName(productHandling.getProductName());
        report.setCustomer(productHandling.getCustomerName());
        report.setStatus(Report8DStatusEnum.Enum.WAIT_SUBMIT.name());
        String measureState = MeasureCompleteStatusEnum.Enum.NOT_FINISH.name();
        report.setTempStatus(measureState);
        report.setLongStatus(measureState);
        report.setPreventStatus(measureState);
        fabosJsonDao.mergeAndFlush(report);

        return report.getGeneralCode();
    }

    private String createAccidentReportByReturnHandling(ReturnProductHandling productHandling) {
        AccidentReportTask reportTask = new AccidentReportTask();
        reportTask.setGeneralCode(namingRuleService.getNameCode(NamingRuleCodeEnum.ACCIDENT_REPORT_TASK.name(), 1, null).get(0));
        reportTask.setAccidentDescription(productHandling.getRefundReason());
        OrgMTO orgMTO = fabosJsonDao.findById(OrgMTO.class, productHandling.getResponsibleDepartmentId());
        reportTask.setOrgMTO(orgMTO);
        reportTask.setBusinessStatus(ArmBusinessStatusEnum.Enum.TO_BE_CONFIRMED.name());
        reportTask.setOccurrenceDate(new Date());
        fabosJsonDao.saveOrUpdate(reportTask);

        return reportTask.getGeneralCode();
    }

}
