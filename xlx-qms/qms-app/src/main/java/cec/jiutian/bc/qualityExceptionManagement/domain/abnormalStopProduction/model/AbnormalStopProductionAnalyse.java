package cec.jiutian.bc.qualityExceptionManagement.domain.abnormalStopProduction.model;

import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleModel;
import cec.jiutian.bc.generalModeler.enumeration.YesOrNoEnum;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.AttachmentType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DateType;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Entity
@Table(name = "qem_abnormal_stop_production",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        })
@Getter
@Setter
@FabosJson(
        name = "异常停产单-原因分析"
)
public class AbnormalStopProductionAnalyse extends NamingRuleModel {

    @FabosJsonField(
            views = @View(title = "设备动力部分析"),
            edit = @Edit(title = "设备动力部分析", type = EditType.TEXTAREA)
    )
    private String equipmentAnalysis;

    @FabosJsonField(
            views = @View(title = "设备动力部意见(是否停产)"),
            edit = @Edit(title = "设备动力部意见(是否停产)",
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = YesOrNoEnum.class))
    )
    private String equipmentStopFlag;

    @FabosJsonField(
            views = @View(title = "正极材料生产车间分析"),
            edit = @Edit(title = "正极材料生产车间分析", type = EditType.TEXTAREA)
    )
    private String workshopAnalysis;

    @FabosJsonField(
            views = @View(title = "正极材料生产车间意见(是否停产)"),
            edit = @Edit(title = "正极材料生产车间意见(是否停产)",
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = YesOrNoEnum.class))
    )
    private String workshopStopFlag;

    @FabosJsonField(
            views = @View(title = "工艺技术部分析"),
            edit = @Edit(title = "工艺技术部分析", type = EditType.TEXTAREA)
    )
    private String processAnalysis;

    @FabosJsonField(
            views = @View(title = "工艺技术部意见(是否停产)"),
            edit = @Edit(title = "工艺技术部意见(是否停产)",
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = YesOrNoEnum.class))
    )
    private String processStopFlag;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "停产时间", type = ViewType.DATE),
            edit = @Edit(title = "停产时间", dateType = @DateType(type = DateType.Type.DATE))
    )
    private LocalDateTime stopTime;

    @FabosJsonField(
            views = @View(title = "附件"),
            edit = @Edit(title = "附件", type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(maxLimit = 10))
    )
    private String attachment;


}
