package cec.jiutian.bc.productExamineManagement.domain.examineImplementPlan.mto;

import cec.jiutian.bc.basicData.domain.inspectionStandard.model.InspectionStandard;
import cec.jiutian.bc.basicData.enumeration.ApplyRangeEnum;
import cec.jiutian.bc.mto.FactoryArea;
import cec.jiutian.bc.mto.InventoryMTO;
import cec.jiutian.bc.mto.SpecificationManageMTO;
import cec.jiutian.bc.productExamineManagement.domain.inspectionTask.handler.ProductExamineInspectionStandardDetailDynamicHandler;
import cec.jiutian.bc.productExamineManagement.domain.inspectionTask.model.ProductExamineInspectionItemDetail;
import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.ReferenceGenerateType;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import jakarta.persistence.*;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/23
 * @description TODO
 */
@Entity
@Data
@FabosJson(
        name = "产品审核实施计划生成检验任务MTO"
)
public class ProductExamineInspectTaskMTO extends BaseModel {

    @FabosJsonField(
            views = @View(title = "检验方案", column = "generalCode"),
            edit = @Edit(title = "检验方案",
                    allowAddMultipleRows = false,
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    filter = @Filter(value = "InspectionStandard.type = 'Other' and InspectionStandard.status = 'Effective'"),
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode")
            )
    )
    @ManyToOne
    @JoinColumn(name = "inspection_standard_id")
    private InspectionStandard inspectionStandard;

    @FabosJsonField(
            views = @View(title = "检验类型"),
            edit = @Edit(title = "检验类型",readonly = @Readonly, type = EditType.CHOICE,search = @Search(vague = true),
                    defaultVal = "ProductExamineInspect",choiceType = @ChoiceType(fetchHandler = ApplyRangeEnum.class)),
            dynamicField = @DynamicField(passive = true)
    )
    private String inspectionType;

    @FabosJsonField(
            views = @View(title = "取样产品", column = "name"),
            edit = @Edit(title = "取样产品", notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    filter = @Filter(value = "validateFlag = 'Y' and specificationCode like '01%'")
            )
    )
    @ManyToOne
    @JoinColumn(name = "specification_manage_id")
    private SpecificationManageMTO specificationManageMTO;

    @FabosJsonField(
            views = @View(title = "取样批次",
                    type = ViewType.TABLE_VIEW,
                    column = "lotSerialId",
                    show = false
            ),
            edit = @Edit(title = "取样批次",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    queryCondition = "{\"materialCode\":\"${specificationManageMTO.code}\",\"stockType\":\"PRODUCT\"}",
                    referenceTableType = @ReferenceTableType(id = "id", label = "lotSerialId")
            )
    )
    @ManyToOne
    @JoinColumn(name = "inventory_id")
    private InventoryMTO inventoryMTO;

    @ManyToOne
    @JoinColumn(name = "workshop_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @FabosJsonField(
            views = @View(title = "车间", column = "name", type = ViewType.TABLE_FORM),
            edit = @Edit(title = "车间",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    readonly = @Readonly(add = true, edit = true),
                    notNull = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name")
            )
    )
    private FactoryArea workshop;

    @ManyToOne
    @JoinColumn(name = "production_line_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @FabosJsonField(
            views = @View(title = "产线", column = "name", type = ViewType.TABLE_FORM),
            edit = @Edit(title = "产线",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    readonly = @Readonly(add = true, edit = true),
                    notNull = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name")
            )
    )
    private FactoryArea productionLine;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = false)
    @FabosJsonField(
            views = @View(title = "检验项目明细", type = ViewType.TABLE_VIEW, index = 8),
            edit = @Edit(title = "检验项目明细", type = EditType.TAB_REFERENCE_GENERATE),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "inspectionStandard", dynamicHandler = ProductExamineInspectionStandardDetailDynamicHandler.class)),
            referenceGenerateType = @ReferenceGenerateType()
    )
    @JoinColumn(name = "inspection_task_id")
    private List<ProductExamineInspectionItemDetail> standardDetailList;
}
