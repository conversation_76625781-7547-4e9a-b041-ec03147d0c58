package cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.handler;

import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.enumration.CpmBusinessStateEnum;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.enumration.ProgressEnum;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.enumration.VerificationResultEnum;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.model.CorrectPreventMeasure;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.mto.CorrectPreventMeasureCloseMTO;
import cec.jiutian.bc.ecs.dto.SendMsgGroupDTO;
import cec.jiutian.bc.ecs.provider.EcsMessageProvider;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/30
 * @description TODO
 */
@Component
@Slf4j
public class CorrectPreventMeasureCloseHandler implements OperationHandler<CorrectPreventMeasure, CorrectPreventMeasureCloseMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private EcsMessageProvider ecsMessageProvider;

    @Override
    public String exec(List<CorrectPreventMeasure> data, CorrectPreventMeasureCloseMTO modelObject, String[] param) {
        if (modelObject != null) {
            String closeApprovalComments = modelObject.getCloseApprovalComments();
            if (closeApprovalComments.equals(VerificationResultEnum.Enum.PASSED.name())) {
                modelObject.setBusinessState(CpmBusinessStateEnum.Enum.CLOSED.name());
            } else {
                modelObject.setBusinessState(CpmBusinessStateEnum.Enum.TO_BE_VERIFIED.name());
                modelObject.setCorrectVeriState(ProgressEnum.Enum.WAIT_RUN.name());
                modelObject.setPreVeriState(ProgressEnum.Enum.WAIT_RUN.name());

                sendQms(modelObject);
            }

            CorrectPreventMeasure model = data.get(0);
            BeanUtil.copyProperties(modelObject, model, "createBy", "createTime");
            fabosJsonDao.mergeAndFlush(model);
        }
        return "alert(操作成功)";
    }

    @Override
    public CorrectPreventMeasureCloseMTO fabosJsonFormValue(List<CorrectPreventMeasure> data, CorrectPreventMeasureCloseMTO fabosJsonForm, String[] param) {
        CorrectPreventMeasure model = data.get(0);
        BeanUtil.copyProperties(model, fabosJsonForm);
        return fabosJsonForm;
    }

    private void sendQms(CorrectPreventMeasureCloseMTO model) {
        String message = "关闭审批不通过，请尽快在处理，编号：" + model.getCorrectPreventMeasureFormNumber();
        SendMsgGroupDTO sendMsgGroupDTO = new SendMsgGroupDTO();
        sendMsgGroupDTO.setMessageGroupCode("CorrectPreventMeasureCloseInfo");
        sendMsgGroupDTO.setContent(message);
        ecsMessageProvider.sendGroupMessage(sendMsgGroupDTO);
        log.info(message);
    }
}
