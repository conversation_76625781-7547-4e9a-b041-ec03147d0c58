package cec.jiutian.bc.deliveryInspection.domain.inspectionTask.model;

import cec.jiutian.bc.basicData.domain.inspectionStandard.model.InspectionStandard;
import cec.jiutian.bc.basicData.enumeration.ApplyRangeEnum;
import cec.jiutian.bc.deliveryInspection.domain.inspectionTask.handler.DeliveryInspectionResultCommitOperationHandler;
import cec.jiutian.bc.deliveryInspection.domain.inspectionTask.handler.DeliveryInventoryReferenceAddHandler;
import cec.jiutian.bc.deliveryInspection.domain.inspectionTask.handler.DeliveryTaskResultEnterOperationHandler;
import cec.jiutian.bc.deliveryInspection.domain.inspectionTask.mto.DeliveryTaskResultEnterMTO;
import cec.jiutian.bc.deliveryInspection.domain.inspectionTask.proxy.MyDeliveryInspectionTaskProxy;
import cec.jiutian.bc.materialInspect.domain.inspectionTask.handler.InspectionStandardDetailDynamicHandler;
import cec.jiutian.bc.materialInspect.domain.inspectionTask.model.InspectionTaskDetail;
import cec.jiutian.bc.materialInspect.enumeration.InspectionResultEnum;
import cec.jiutian.bc.materialInspect.enumeration.TaskBuildTypeEnum;
import cec.jiutian.bc.mto.OrgMTO;
import cec.jiutian.bc.modeler.enumration.TaskBusinessStateEnum;
import cec.jiutian.bc.modeler.enumration.UnqualifiedLevelEnum;
import cec.jiutian.bc.mto.SpecificationManageMTO;
import cec.jiutian.bc.processInspect.domain.publicInspectionTask.mto.ProcessUserForInsTaskMTO;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.*;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DependFieldDisplay;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowOperation;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@FabosJson(
        name = "我的发货检验任务",
        orderBy = "MyDeliveryInspectionTask.createTime desc",
        filter = @Filter(value = "inspectionType = 'deliveryInspect' and businessState in ('INSPECTING','INSPECT_FINISH')"),
        power = @Power(add = false,edit = false,delete = false),
        dataProxy = MyDeliveryInspectionTaskProxy.class,
        rowOperation = {
                @RowOperation(
                        title = "结果录入",
                        code = "MyDeliveryInspectionTask@RESLTENTER",
                        type = RowOperation.Type.POPUP,
                        mode = RowOperation.Mode.HEADER,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = DeliveryTaskResultEnterMTO.class,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        operationHandler = DeliveryTaskResultEnterOperationHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "MyDeliveryInspectionTask@RESLTENTER"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "selectedItems[0].businessState != 'INSPECTING'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "结果提交",
                        code = "MyDeliveryInspectionTask@RESLTCOMMIT",
                        operationHandler = DeliveryInspectionResultCommitOperationHandler.class,
                        mode = RowOperation.Mode.HEADER,
                        callHint = "是否确定提交？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "MyDeliveryInspectionTask@RESLTCOMMIT"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "selectedItems[0].businessState != 'INSPECTING'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
        }
)
@Table(name = "mi_inspection_task",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Getter
@Setter
@TemplateType(type = "multiTable")
public class MyDeliveryInspectionTask extends MetaModel {

    @FabosJsonField(
            views = @View(title = "检验任务编号"),
            edit = @Edit(title = "检验任务编号",show = false,search = @Search)
    )
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "检验状态"),
            edit = @Edit(title = "检验状态",show = false, type = EditType.CHOICE,search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = TaskBusinessStateEnum.class))
    )
    private String businessState;

    @FabosJsonField(
            views = @View(title = "当前状态"),
            edit = @Edit(title = "当前状态",show = false, type = EditType.CHOICE,search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = OrderCurrentStateEnum.class))
    )
    private String currentState;

    @FabosJsonField(
            views = @View(title = "质检标准",column = "generalCode"),
            edit = @Edit(title = "质检标准",
                    readonly = @Readonly(add = false),allowAddMultipleRows = false,
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode")
            )
    )
    @ManyToOne
    @JoinColumn(name = "inspection_standard_id")
    private InspectionStandard inspectionStandard;

    @FabosJsonField(
            views = @View(title = "创建类型"),
            edit = @Edit(title = "创建类型", type = EditType.CHOICE,search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = TaskBuildTypeEnum.class))
    )
    private String buildType;

    @FabosJsonField(
            views = @View(title = "检验类型"),
            edit = @Edit(title = "检验类型", show = false,type = EditType.CHOICE,search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = ApplyRangeEnum.class))
    )
    private String inspectionType;

    @FabosJsonField(
            views = @View(title = "是否加急"),
            edit = @Edit(title = "是否加急", defaultVal = "false"
            )
    )
    private Boolean isUrgent;

    @Transient
    @FabosJsonField(
            views = @View(title = "检验部门", show = false, column = "name"),
            edit = @Edit(title = "检验部门",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType()
            )
    )
    private OrgMTO orgMTO;

    @FabosJsonField(
            views = @View(title = "检验部门id",show = false),
            edit = @Edit(title = "检验部门id", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "orgMTO", beFilledBy = "id"))
    )
    private String inspectionDepartment;

    @FabosJsonField(
            views = @View(title = "检验部门"),
            edit = @Edit(title = "检验部门", search = @Search(vague = true)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "orgMTO", beFilledBy = "name"))
    )
    private String inspectionDepartmentName;

    @Transient
    @FabosJsonField(
            views = @View(title = "检验员", show = false, column = "name"),
            edit = @Edit(title = "检验员",
                    show = false,
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType()
            )
    )
    private ProcessUserForInsTaskMTO user;

    @FabosJsonField(
            views = @View(title = "检验员ID",show = false),
            edit = @Edit(title = "检验员ID",
                    show = false
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "user", beFilledBy = "id"))
    )
    private String userId;

    @FabosJsonField(
            views = @View(title = "检验员姓名"),
            edit = @Edit(title = "检验员姓名",
                    show = false,
                    readonly = @Readonly(add = false, edit = false),
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "user", beFilledBy = "name"))
    )
    private String userName;

    @FabosJsonField(
            views = @View(title = "检验结果"),
            edit = @Edit(title = "检验结果", type = EditType.CHOICE,search = @Search(),show = false,
                    choiceType = @ChoiceType(fetchHandler = InspectionResultEnum.class))
    )
    private String inspectionResult;

    @FabosJsonField(
            views = @View(title = "不合格等级"),
            edit = @Edit(title = "不合格等级", notNull = true,type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = UnqualifiedLevelEnum.class),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "inspectionResult == 'UNQUALIFIED'"))
    )
    private String unqualifiedLevel;

    @Transient
    @FabosJsonField(
            views = @View(title = "物资名称", column = "name"),
            edit = @Edit(title = "物资名称",
                    type = EditType.REFERENCE_TABLE,
                    filter = @Filter(value = "validateFlag = 'Y' and specificationCode like '02%'"),
                    referenceTableType = @ReferenceTableType(id = "id", label = "name")
            )
    )
    private SpecificationManageMTO material;

    @FabosJsonField(
            views = @View(title = "物资Id",show = false),
            edit = @Edit(title = "物资Id",
                    show = false
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "material", beFilledBy = "id"))
    )
    private String materialId;

    @FabosJsonField(
            views = @View(title = "检验物资名称"),
            edit = @Edit(title = "检验物资名称", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "material", beFilledBy = "name"))
    )
    private String materialName;

    @FabosJsonField(
            views = @View(title = "检验物资编码"),
            edit = @Edit(title = "检验物资编码", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "material", beFilledBy = "code"))
    )
    private String materialCode;

    @FabosJsonField(
            views = @View(title = "规格"),
            edit = @Edit(title = "规格"),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "material", beFilledBy = "type"))
    )
    private String materialSpecification;

    @FabosJsonField(
            views = @View(title = "数量"),
            edit = @Edit(title = "数量", notNull = true)
    )
    private Double arrivalQuantity;

    @FabosJsonField(
            views = @View(title = "取样数量"),
            edit = @Edit(title = "取样数量", readonly = @Readonly)
    )
    private Double allInspectionItemQuantity;

    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumn(name = "inspection_task_id")
    @OrderBy
    @FabosJsonField(
            edit = @Edit(title = "检验物资明细", type = EditType.TAB_REFER_ADD),
            referenceAddType = @ReferenceAddType(referenceClass = "InventoryMTO",
                    filter = "currentState = 'normal' and materialCode = '${inspectionRequestDetail.materialCode}'" +
                            "and materialName = '${materialName}'",
                    editable = {"requestQuantity"},
                    referenceAddHandler = DeliveryInventoryReferenceAddHandler.class),
            views = @View(title = "检验物资明细", type = ViewType.TABLE_VIEW, extraPK = "inventoryId")
    )
    private List<InspectionTaskDetail> inspectionTaskDetailList;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = false)
    @FabosJsonField(
            views = @View(title = "检验项目明细", type= ViewType.TABLE_VIEW,index = 8),
            edit = @Edit(title = "检验项目明细",type = EditType.TAB_REFERENCE_GENERATE),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "inspectionStandard", dynamicHandler = InspectionStandardDetailDynamicHandler.class)),
            referenceGenerateType = @ReferenceGenerateType()
    )
    @JoinColumn(name = "inspection_task_id")
    private List<DeliveryInspectionItemDetail> standardDetailList;

    @FabosJsonField(
            views = @View(title = "备注"),
            edit = @Edit(title = "备注",
                    type = EditType.TEXTAREA)
    )
    private String remark;
}
