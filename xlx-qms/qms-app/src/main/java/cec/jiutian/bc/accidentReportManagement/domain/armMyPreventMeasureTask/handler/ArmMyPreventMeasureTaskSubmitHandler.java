package cec.jiutian.bc.accidentReportManagement.domain.armMyPreventMeasureTask.handler;

import cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.model.AccidentReportTask;
import cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.model.ArmPreventMeasure;
import cec.jiutian.bc.accidentReportManagement.domain.armMyPreventMeasureTask.model.ArmMyPreventMeasureTask;
import cec.jiutian.bc.accidentReportManagement.enumeration.ArmImprovingProgressEnum;
import cec.jiutian.bc.accidentReportManagement.enumeration.ArmMeasureStatusEnum;
import cec.jiutian.bc.accidentReportManagement.enumeration.ArmProgressEnum;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component
public class ArmMyPreventMeasureTaskSubmitHandler implements OperationHandler<ArmMyPreventMeasureTask, Void> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<ArmMyPreventMeasureTask> data, Void modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            ArmMyPreventMeasureTask armMyPreventMeasureTask = data.get(0);
            List<ArmPreventMeasure> updateDetailList = new ArrayList<>();
            for (ArmPreventMeasure p : armMyPreventMeasureTask.getArmPreventMeasureList()) {
                if(!p.getUserForInsTaskMTO().getId().equals(UserContext.getUserId())){
                    continue;
                }
                if(!ArmProgressEnum.Enum.RUN_SUBMIT.name().equals(p.getProgress())){
                    throw new FabosJsonApiErrorTip("提交失败：需要完成所有任务才可提交");
                }
                updateDetailList.add(p);
            }

            updateDetailList.forEach(d->{
                d.setImprovingProgress(ArmImprovingProgressEnum.Enum.TO_BE_VERIFIED.name());
                fabosJsonDao.mergeAndFlush(d);
            });

            AccidentReportTask accidentReportTask = fabosJsonDao.findById(AccidentReportTask.class, armMyPreventMeasureTask.getId());
            if(checkCurrentStatus(accidentReportTask)){
                accidentReportTask.setPreventMeasureStatus(ArmMeasureStatusEnum.Enum.TO_BE_VERIFIED.name());
                fabosJsonDao.mergeAndFlush(accidentReportTask);
            }
        }
        return "msg.success('操作成功')";
    }

    private boolean checkCurrentStatus(AccidentReportTask accidentReportTask) {
        for (ArmPreventMeasure preventMeasures : accidentReportTask.getArmPreventMeasureList()) {
            if (!ArmImprovingProgressEnum.Enum.TO_BE_VERIFIED.name().equals(preventMeasures.getImprovingProgress())) {
                return false;
            }
        }
        return true;
    }
}
