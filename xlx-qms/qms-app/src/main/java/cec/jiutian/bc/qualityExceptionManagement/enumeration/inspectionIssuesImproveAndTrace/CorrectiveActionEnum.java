package cec.jiutian.bc.qualityExceptionManagement.enumeration.inspectionIssuesImproveAndTrace;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

public class CorrectiveActionEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum value : Enum.values()) {
            list.add(new VLModel(value.name(), value.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        EIGHT_D("8D"),
        FIVE_WHY_ANALYSIS("5why 分析"),
        ONE_PAGE("一页纸"),
        PRE_CORRECTIVE_ACTION("预防纠正措施"),
        ;

        private final String value;
    }
}
