package cec.jiutian.bc.productReturnInspection.domain.inspectionTask.handler;

import cec.jiutian.bc.modeler.domain.inspectionItem.model.InspectionItem;
import cec.jiutian.bc.processInspect.domain.processInspectionTask.mto.TaskResultEnterDetailMTO;
import cec.jiutian.bc.productReturnInspection.domain.inspectionTask.model.MyProductReturnInspectionTask;
import cec.jiutian.bc.productReturnInspection.domain.inspectionTask.mto.MyProTaskResultEnterMTO;
import cec.jiutian.bc.productReturnInspection.enumeration.InspectionResultEnum;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/4/9
 * @description
 */
@Component
public class MyProReTaskResultEnterHandler implements OperationHandler<MyProductReturnInspectionTask, MyProTaskResultEnterMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<MyProductReturnInspectionTask> data, MyProTaskResultEnterMTO modelObject, String[] param) {
        if (modelObject != null) {
            MyProductReturnInspectionTask myProductReturnInspectionTask = data.get(0);
            myProductReturnInspectionTask.setInspectionResult(modelObject.getInspectionResult());
            myProductReturnInspectionTask.setUnqualifiedLevel(modelObject.getUnqualifiedLevel());
            myProductReturnInspectionTask.setInsTime(new Date());
            myProductReturnInspectionTask.getStandardDetailList().forEach(detail -> {
                List<TaskResultEnterDetailMTO> resultDetailList = modelObject.getDetailList().stream().filter(resultDetail -> resultDetail.getInspectItemId().equals(detail.getItemId())).toList();
                if (CollectionUtils.isNotEmpty(detail.getProductReturnInspectionStandardItemTargetList())) {
                    detail.getProductReturnInspectionStandardItemTargetList().forEach(detailTarget -> {
                        Optional<TaskResultEnterDetailMTO> optional = resultDetailList.stream().filter(resultDetail -> resultDetail.getTargetId().equals(detailTarget.getTargetId()) && resultDetail.getInspectionValueType().equals(detailTarget.getInspectionValueType())).findFirst();
                        if (optional.isPresent()) {
                            TaskResultEnterDetailMTO resultEnterDetailMTO = optional.get();
                            detailTarget.setResultValue(resultEnterDetailMTO.getResultValue());
                            detailTarget.setTargetResult(resultEnterDetailMTO.getInspectionItemResult());
                        }
                    });
                }
                if (CollectionUtils.isNotEmpty(resultDetailList)) {
                    if (resultDetailList.stream().anyMatch(resultDetail -> InspectionResultEnum.Enum.UNQUALIFIED.name().equals(resultDetail.getInspectionItemResult()))) {
                        detail.setInspectionResult(InspectionResultEnum.Enum.UNQUALIFIED.name());
                    } else {
                        detail.setInspectionResult(InspectionResultEnum.Enum.QUALIFIED.name());
                    }
                }
            });

            fabosJsonDao.mergeAndFlush(myProductReturnInspectionTask);
        }
        return "alert(操作成功)";
    }

    @Override
    public MyProTaskResultEnterMTO fabosJsonFormValue(List<MyProductReturnInspectionTask> data, MyProTaskResultEnterMTO fabosJsonForm, String[] param) {
        MyProductReturnInspectionTask myProcessInspectionTask = data.get(0);
        BeanUtil.copyProperties(myProcessInspectionTask, fabosJsonForm);
        if (CollectionUtils.isEmpty(myProcessInspectionTask.getStandardDetailList())) {
            throw new ServiceException("检验项目为空");
        }
        List<TaskResultEnterDetailMTO> detailMTOList = new ArrayList<>();
        myProcessInspectionTask.getStandardDetailList().forEach(item -> {
            if (item.getItemId() == null) {
                throw new ServiceException("检验项为空");
            }
            InspectionItem inspectionItem = fabosJsonDao.getById(InspectionItem.class, item.getItemId());
            if (inspectionItem == null || CollectionUtils.isEmpty(inspectionItem.getInspectionItemTargetList())) {
                throw new ServiceException("检验项指标为空");
            }

            item.getProductReturnInspectionStandardItemTargetList().forEach(target -> {
                TaskResultEnterDetailMTO taskResultEnterDetailMTO = new TaskResultEnterDetailMTO();
                taskResultEnterDetailMTO.setTaskCode(myProcessInspectionTask.getGeneralCode());
                taskResultEnterDetailMTO.setName(inspectionItem.getName());
                taskResultEnterDetailMTO.setFeature(inspectionItem.getFeature());
                taskResultEnterDetailMTO.setItemType(inspectionItem.getItemType());
                taskResultEnterDetailMTO.setTargetId(target.getTargetId());
                taskResultEnterDetailMTO.setTargetName(target.getName());
                taskResultEnterDetailMTO.setInspectionValueType(target.getInspectionValueType());
                taskResultEnterDetailMTO.setComparisonMethod(target.getComparisonMethod());
                taskResultEnterDetailMTO.setUnit(target.getUnit());
                taskResultEnterDetailMTO.setStandardValue(target.getStandardValue());
                taskResultEnterDetailMTO.setLowerValue(target.getLowerValue());
                taskResultEnterDetailMTO.setUpperValue(target.getUpperValue());
                taskResultEnterDetailMTO.setInspectItemId(inspectionItem.getId());
                taskResultEnterDetailMTO.setResultValue(target.getResultValue());
                taskResultEnterDetailMTO.setInspectionItemResult(target.getTargetResult());
                detailMTOList.add(taskResultEnterDetailMTO);
            });
            fabosJsonForm.setDetailList(detailMTOList);

        });

        return fabosJsonForm;
    }
}
