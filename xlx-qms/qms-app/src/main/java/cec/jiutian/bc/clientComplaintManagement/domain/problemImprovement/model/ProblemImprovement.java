package cec.jiutian.bc.clientComplaintManagement.domain.problemImprovement.model;

import cec.jiutian.bc.clientComplaintManagement.domain.clientComplaintFeedback.model.ClientComplaintFeedback;
import cec.jiutian.bc.clientComplaintManagement.domain.problemImprovement.handler.*;
import cec.jiutian.bc.clientComplaintManagement.domain.problemImprovement.mto.ProblemImprovementDistributeMTO;
import cec.jiutian.bc.clientComplaintManagement.domain.problemImprovement.mto.ProblemImprovementResultValidateMTO;
import cec.jiutian.bc.clientComplaintManagement.enumeration.ProblemImprovementStatusEnum;
import cec.jiutian.bc.clientComplaintManagement.domain.problemImprovement.proxy.ProblemImprovementDataProxy;
import cec.jiutian.bc.materialInspect.domain.publicInspectionTask.mto.UserForInsTaskMTO;
import cec.jiutian.bc.mto.OrgMTO;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.frame.module.MetaUser;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/05/20
 * @description TODO
 */
@FabosJson(
        name = "问题改善",
        orderBy = "ProblemImprovement.createTime desc",
        dataProxy = ProblemImprovementDataProxy.class,
        power = @Power(add = false,edit = false,delete = false),
        rowOperation = {
                @RowOperation(
                        title = "分发",
                        code = "ProblemImprovement@DISTRIBUTE",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = ProblemImprovementDistributeMTO.class,
                        operationHandler = ProblemImprovementDistributeMTOHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "ProblemImprovement@DISTRIBUTE"
                        ),
                        ifExpr = "selectedItems[0].businessStatus != 'TO_BE_DISTRIBUTED'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "结果验证",
                        code = "ProblemImprovement@RESULTVALIDATE",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = ProblemImprovementResultValidateMTO.class,
                        operationHandler = ProblemImprovementResultValidateMTOHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "ProblemImprovement@RESULTVALIDATE"
                        ),
                        ifExpr = "selectedItems[0].businessStatus != 'TO_BE_VERIFIED'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
        }
)
@Table(name = "qms_ccm_problem_improvement",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Data
public class ProblemImprovement extends MetaModel {
    @FabosJsonField(
            views = @View(title = "问题改善任务单号"),
            edit = @Edit(title = "问题改善任务单号",
                    readonly = @Readonly(add = false),
                    search = @Search(vague = true),
                    notNull = true)
    )
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "问题描述"),
            edit = @Edit(title = "问题描述",
                    notNull = true,
                    search = @Search(vague = true))

    )
    private String problemDescription;

    @ManyToOne
    @FabosJsonField(
            views = {
                    @View(title = "客诉反馈", column = "generalCode")
            },
            edit = @Edit(title = "客诉反馈",
                    type = EditType.REFERENCE_TABLE,
                    readonly = @Readonly(),
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode")
            )
    )
    private ClientComplaintFeedback clientComplaintFeedback;

    @FabosJsonField(
            views = @View(title = "责任部门", column = "name"),
            edit = @Edit(title = "责任部门",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType(type = TabTableReferType.SelectShowTypeMTM.LIST)
            )
    )
    @ManyToOne
    @JoinColumn(name = "response_department_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private OrgMTO orgMTO;

    @FabosJsonField(
            views = @View(title = "责任人", column = "name"),
            edit = @Edit(title = "责任人",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "id")
            )
    )
    @ManyToOne
    @JoinColumn(name = "response_user_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private UserForInsTaskMTO userForInsTaskMTO;

    @FabosJsonField(
            views = @View(title = "责任人id", show = false),
            edit = @Edit(title = "责任人id",
                    show = false,
                    notNull = true,
                    inputType = @InputType(length = 30)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "userForInsTaskMTO",
                    beFilledBy = "id"))
    )
    private String responsiblePersonId;

    @FabosJsonField(
            views = @View(title = "分析方法"),
            edit = @Edit(title = "分析方法",
                    notNull = true,
                    search = @Search(vague = true))

    )
    private String method;

    @FabosJsonField(
            views = @View(title = "纳期"),
            edit = @Edit(title = "纳期",
                    dateType = @DateType(type = DateType.Type.DATE_TIME),
                    notNull = true
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date deliveryTime;

    //原因分析
    @FabosJsonField(
            views = @View(title = "原因分析", show = false),
            edit = @Edit(title = "原因分析",
                    search = @Search(vague = true))
    )
    private String causeAnalysis;

    @FabosJsonField(
            views = @View(title = "整改措施", show = false),
            edit = @Edit(title = "整改措施",
                    search = @Search(vague = true))
    )
    private String rectificationMeasures;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "负责人", column = "name", show = false),
            edit = @Edit(title = "负责人",
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"))
    )
    private MetaUser director;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "验证人", column = "name", show = false),
            edit = @Edit(title = "验证人",
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"))
    )
    private MetaUser verifier;

    @FabosJsonField(
            views = @View(title = "预防纠正措施", show = false),
            edit = @Edit(title = "预防纠正措施",
                    search = @Search(vague = true))
    )
    private String preCorMeasure;

    //执行
    @FabosJsonField(
            views = @View(title = "实际完成日期", show = false),
            edit = @Edit(title = "实际完成日期",
                    dateType = @DateType(type = DateType.Type.DATE_TIME),
                    notNull = true,
                    readonly = @Readonly()
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date actualFinishTime;

    @FabosJsonField(
            views = @View(title = "执行附件", show = false),
            edit = @Edit(title = "执行附件",
                    type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType)
    )
    private String executeDocument;

    //结果验证
    @FabosJsonField(
            views = @View(title = "验收情况", show = false),
            edit = @Edit(title = "验收情况",
                    search = @Search(vague = true))
    )
    private String acceptanceStatus;

    @FabosJsonField(
            views = @View(title = "验证附件", show = false),
            edit = @Edit(title = "验证附件",
                    type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType)
    )
    private String verifyAttachment;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",
                    show = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ProblemImprovementStatusEnum.class)
            )
    )
    private String businessStatus;
}
