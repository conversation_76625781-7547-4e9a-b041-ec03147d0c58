package cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.issueList.service;

import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.modeler.domain.inspectionIssuesImproveAndTrace.dto.IssueListDTO;
import cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.issueList.model.IssueList;
import cec.jiutian.bc.qualityExceptionManagement.enumeration.inspectionIssuesImproveAndTrace.IssueListStatusEnum;
import cec.jiutian.common.util.BeanUtils;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

@Service
@Component
public class IssueListService {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private NamingRuleService namingRuleService;

    public String createIssueList(IssueListDTO issueListDTO) {
        IssueList issue = new IssueList();
        BeanUtils.copyProperties(issueListDTO,issue);
        issue.setGeneralCode(namingRuleService.getNameCode(cec.jiutian.bc.modeler.enumration.NamingRuleCodeEnum.IssueList.name(), 1, null).get(0));
        issue.setStatus(IssueListStatusEnum.Enum.WAIT_PUBLISH.name());
        fabosJsonDao.saveOrUpdate(issue);
        return issue.getGeneralCode();
    }
}
