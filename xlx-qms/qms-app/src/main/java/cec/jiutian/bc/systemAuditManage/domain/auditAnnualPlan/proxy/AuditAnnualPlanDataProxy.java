package cec.jiutian.bc.systemAuditManage.domain.auditAnnualPlan.proxy;

import cec.jiutian.bc.systemAuditManage.domain.auditAnnualPlan.model.AuditAnnualPlan;
import cec.jiutian.bc.systemAuditManage.enumeration.AuditAnnualPlanStatusEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
public class AuditAnnualPlanDataProxy implements DataProxy<AuditAnnualPlan> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public void beforeAdd(AuditAnnualPlan auditAnnualPlan) {
        auditAnnualPlan.setStatus(AuditAnnualPlanStatusEnum.Enum.CREATED.name());
    }
}
