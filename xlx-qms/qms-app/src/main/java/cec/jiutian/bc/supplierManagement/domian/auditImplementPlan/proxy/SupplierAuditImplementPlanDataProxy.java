package cec.jiutian.bc.supplierManagement.domian.auditImplementPlan.proxy;

import cec.jiutian.bc.processInspect.domain.publicInspectionTask.mto.ProcessUserForInsTaskMTO;
import cec.jiutian.bc.supplierManagement.domian.auditImplementPlan.model.SupplierAuditImplementPlan;
import cec.jiutian.bc.supplierManagement.enums.AuditImplementPlanStateEnum;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class SupplierAuditImplementPlanDataProxy implements DataProxy<SupplierAuditImplementPlan> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public void beforeAdd(SupplierAuditImplementPlan entity) {
        entity.setCurrentState(AuditImplementPlanStateEnum.Enum.Edit.name());
    }

    @Override
    public void afterSingleFetch(Map<String, Object> map) {
        if (map.get("leaderId") != null) {
            ProcessUserForInsTaskMTO userMTO = fabosJsonDao.findById(ProcessUserForInsTaskMTO.class, map.get("leaderId"));
            if (userMTO != null) {
                map.put("leaderMTO", userMTO);
                map.put("leaderMTO_name", userMTO.getName());
            } else {
                throw new FabosJsonApiErrorTip("未查到人员:" + map.get("leaderName") + "源数据，请确认");
            }
        }
        if (map.get("memberId") != null) {
            ProcessUserForInsTaskMTO userMTO = fabosJsonDao.findById(ProcessUserForInsTaskMTO.class, map.get("memberId"));
            if (userMTO != null) {
                map.put("memberMTO", userMTO);
                map.put("memberMTO_name", userMTO.getName());
            } else {
                throw new FabosJsonApiErrorTip("未查到人员:" + map.get("memberName") + "源数据，请确认");
            }
        }
    }

}
