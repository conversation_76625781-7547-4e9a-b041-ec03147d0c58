package cec.jiutian.bc.inventoryInspection.domain.publicInspectionTask.mto;

import cec.jiutian.bc.basicData.enumeration.InspectionValueTypeEnum;
import cec.jiutian.bc.inventoryInspection.domain.publicInspectionTask.handler.InventoryResultJudgeHandler;
import cec.jiutian.bc.inventoryInspection.enumeration.InsItemResEnum;
import cec.jiutian.bc.modeler.domain.inspectionItem.model.InspectionItemTarget;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowBaseOperation;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR>
 * @date 2025/3/4
 * @description TODO
 */
@Table(name = "mi_quality_inspection_standard_detail_target")
@FabosJson(
        name = "检验项指标",
        orderBy = "InspectionItemTarget.createTime desc",
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "1==1",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.HIDE
                )
        },
        power = @Power(add = false, delete = false)
)
@Entity
@Getter
@Setter
public class InventoryInsItemTargetMTO extends MetaModel {

    @FabosJsonField(
            views = @View(title = "检验项指标名称"),
            edit = @Edit(title = "检验项指标名称", search = @Search(vague = true),
                    readonly = @Readonly,
                    inputType = @InputType(length = 40))
    )
    private String name;

    @FabosJsonField(
            views = @View(title = "值类型"),
            edit = @Edit(title = "值类型", type = EditType.CHOICE, search = @Search(),
                    readonly = @Readonly,
                    choiceType = @ChoiceType(fetchHandler = InspectionValueTypeEnum.class))
    )
    private String inspectionValueType;

    @FabosJsonField(
            views = @View(title = "单位"),
            edit = @Edit(title = "单位",
                    readonly = @Readonly,
                    inputType = @InputType(length = 40))
    )
    private String unit;

    @FabosJsonField(
            views = @View(title = "标准值"),
            edit = @Edit(title = "标准值",notNull = true)
    )
    private String standardValue;

    @FabosJsonField(
            views = @View(title = "上限值"),
            edit = @Edit(title = "上限值", notNull = true,
                    readonly = @Readonly,
                    numberType = @NumberType(min = 0, max = 9999999, precision = 2))
    )
    private Double UpperValue;

    @FabosJsonField(
            views = @View(title = "是否包含上限值"),
            edit = @Edit(title = "是否包含上限值", defaultVal = "true",
                    readonly = @Readonly
            )
    )
    private Boolean isContainUpper;

    @FabosJsonField(
            views = @View(title = "下限值"),
            edit = @Edit(title = "下限值", notNull = true,
                    readonly = @Readonly,
                    numberType = @NumberType(min = 0, max = 9999999, precision = 2))
    )
    private Double lowerValue;

    @FabosJsonField(
            views = @View(title = "是否包含下限值"),
            edit = @Edit(title = "是否包含下限值",
                    readonly = @Readonly, defaultVal = "true"
            )
    )
    private Boolean isContainLower;

    @FabosJsonField(
            views = @View(title = "检验结果值"),
            edit = @Edit(title = "检验结果值", notNull = true)
    )
    private String resultValue;

    @FabosJsonField(
            views = @View(title = "判定结果"),
            edit = @Edit(title = "判定结果",
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = InsItemResEnum.ChoiceFetch.class)),
            dynamicField = @DynamicField(dependFiled = @DependFiled( changeBy = "resultValue",buttonName = "判定",
                    dynamicHandler = InventoryResultJudgeHandler.class))
    )
    private String judge;

    @FabosJsonField(
            views = @View(title = "描述"),
            edit = @Edit(title = "描述",
                    readonly = @Readonly,
                    type = EditType.TEXTAREA)
    )
    private String description;


    public static InventoryInsItemTargetMTO createByInspectionItemTarget(InspectionItemTarget inspectionItemTarget) {
        InventoryInsItemTargetMTO inventoryInsItemTargetMTO = new InventoryInsItemTargetMTO();
        BeanUtils.copyProperties(inspectionItemTarget, inventoryInsItemTargetMTO);
        inventoryInsItemTargetMTO.setResultValue("0.0");
        inventoryInsItemTargetMTO.setId(null);
        return inventoryInsItemTargetMTO;
    }

}
