package cec.jiutian.bc.clientReviewManagement.domain.customerReviewTask.handler;

import cec.jiutian.bc.clientReviewManagement.domain.customerReviewTask.model.CustomerReviewTask;
import cec.jiutian.bc.clientReviewManagement.domain.customerReviewTask.model.CustomerReviewTaskDetail;
import cec.jiutian.bc.mto.OrgMTO;
import cec.jiutian.view.ReferenceAddType;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class CustomerReviewTaskDetailAddHandler implements ReferenceAddType.ReferenceAddHandler<CustomerReviewTask, OrgMTO> {
    @Override
    public Map<String, Object> handle(CustomerReviewTask entity, List<OrgMTO> orgMTOList) {
        HashMap<String, Object> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(orgMTOList)) {
            List<CustomerReviewTaskDetail> list = new ArrayList<>();
            for (OrgMTO orgMTO : orgMTOList) {
                CustomerReviewTaskDetail detail = new CustomerReviewTaskDetail();
                detail.setDepartmentId(orgMTO.getId());
                detail.setDepartmentName(orgMTO.getName());

                list.add(detail);
            }
            map.put("details", list);
        }
        return map;
    }
}
