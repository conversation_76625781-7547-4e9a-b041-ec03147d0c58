package cec.jiutian.bc.clientReviewManagement.domain.customerReviewTask.handler;

import cec.jiutian.bc.clientReviewManagement.domain.customerReviewTask.model.CustomerReviewTask;
import cec.jiutian.bc.clientReviewManagement.domain.customerReviewTask.model.CustomerReviewTaskDetailUpload;
import cec.jiutian.bc.clientReviewManagement.domain.customerReviewTask.model.CustomerReviewTaskUpload;
import cec.jiutian.bc.clientReviewManagement.enumeration.CustomerReviewNatureEnum;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> chenwg
 * @date : 2025/6/16
 */
@Component
public class ReviewUploadOprHandler implements OperationHandler<CustomerReviewTask, CustomerReviewTaskUpload> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<CustomerReviewTask> data, CustomerReviewTaskUpload modelObject, String[] param) {
        if (modelObject == null || CollectionUtils.isEmpty(modelObject.getDetails())) {
            throw new ServiceException("请上传资料");
        }
        List<CustomerReviewTaskDetailUpload> details = modelObject.getDetails();
        boolean all = true;
        for (CustomerReviewTaskDetailUpload detail : details) {
            all = StringUtils.isNotBlank(detail.getAttachment());
            if (!all) break;
        }
        if (all) {
            modelObject.setCurrentState(CustomerReviewNatureEnum.Enum.WaitSubmit.name());
        }
        fabosJsonDao.mergeAndFlush(modelObject);
        return "alert('操作成功')";
    }

    @Override
    public CustomerReviewTaskUpload fabosJsonFormValue(List<CustomerReviewTask> data, CustomerReviewTaskUpload fabosJsonForm, String[] param) {
        CustomerReviewTask customerReviewTask = data.get(0);
        if (CollectionUtils.isNotEmpty(customerReviewTask.getDetails())) {
            fabosJsonForm.setId(customerReviewTask.getId());
            fabosJsonForm.setName(customerReviewTask.getCustomerName());
            fabosJsonForm.setGeneralCode(customerReviewTask.getGeneralCode());
            List<CustomerReviewTaskDetailUpload> details = new ArrayList<>();
            customerReviewTask.getDetails().forEach(detail -> {
                CustomerReviewTaskDetailUpload detailUpload = new CustomerReviewTaskDetailUpload();
                BeanUtil.copyProperties(detail, detailUpload);
                detailUpload.setId(detail.getId());
                details.add(detailUpload);
            });
            fabosJsonForm.setDetails(details);
        }

        return fabosJsonForm;
    }
}
