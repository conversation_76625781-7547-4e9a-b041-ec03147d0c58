package cec.jiutian.bc.accidentReportManagement.domain.armMyTempMeasureTask.handler;

import cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.model.ArmTemporaryMeasure;
import cec.jiutian.bc.accidentReportManagement.domain.armMyTempMeasureTask.model.ArmMyTempMeasure;
import cec.jiutian.bc.accidentReportManagement.domain.armMyTempMeasureTask.model.ArmMyTempMeasureTask;
import cec.jiutian.bc.accidentReportManagement.domain.armMyTempMeasureTask.model.ArmMyTempMeasureTaskExec;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Component
public class ArmMyTempMeasureTaskExecHandler implements OperationHandler<ArmMyTempMeasureTask, ArmMyTempMeasureTaskExec> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<ArmMyTempMeasureTask> data, ArmMyTempMeasureTaskExec modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            modelObject.getArmMyTempMeasureList().forEach(d->{
                ArmTemporaryMeasure tempMeasure = fabosJsonDao.findById(ArmTemporaryMeasure.class, d.getId());
                tempMeasure.setProgress(d.getProgress());
                tempMeasure.setCompletionDate(d.getCompletionDate());
                tempMeasure.setCompleteSupportMaterial(d.getCompleteSupportMaterial());
                fabosJsonDao.mergeAndFlush(tempMeasure);
            });
        }
        return "msg.success('操作成功')";
    }

    @Override
    public ArmMyTempMeasureTaskExec fabosJsonFormValue(List<ArmMyTempMeasureTask> data, ArmMyTempMeasureTaskExec fabosJsonForm, String[] param) {
        // data为空 直接返回
        if (CollectionUtils.isEmpty(data)) {
            return fabosJsonForm;
        }

        String userId = UserContext.getUserId();
        ArmMyTempMeasureTask armMyTempMeasureTask = data.get(0);
        BeanUtil.copyProperties(armMyTempMeasureTask, fabosJsonForm);

        //如果list为空 直接返回
        if (CollectionUtils.isEmpty(armMyTempMeasureTask.getArmTemporaryMeasureList())) {
            return fabosJsonForm;
        }

        List<ArmMyTempMeasure> armMyTempMeasureList = new ArrayList<>();
        armMyTempMeasureTask.getArmTemporaryMeasureList().forEach(d->{
            if (Objects.equals(d.getUserForInsTaskMTO().getId(), userId)) {
                ArmMyTempMeasure armMyTempMeasure = new ArmMyTempMeasure();
                BeanUtil.copyProperties(d, armMyTempMeasure);
                armMyTempMeasureList.add(armMyTempMeasure);
            }
        });

        fabosJsonForm.setArmMyTempMeasureList(armMyTempMeasureList);
        return fabosJsonForm;
    }
}
