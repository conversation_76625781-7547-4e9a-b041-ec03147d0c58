package cec.jiutian.bc.qualityExceptionManagement.domain.returnProductHandling.handler;

import cec.jiutian.bc.productReturnInspection.domain.inspectionRequest.model.ProductReturnInspectionRequest;
import cec.jiutian.bc.productReturnInspection.domain.inspectionTask.model.ProductReturnInspectionStandardItemTarget;
import cec.jiutian.bc.productReturnInspection.domain.inspectionTask.model.ProductReturnInspectionTask;
import cec.jiutian.bc.productReturnInspection.domain.inspectionTask.model.ProductReturnQualityInspectionStandardDetail;
import cec.jiutian.bc.qualityExceptionManagement.domain.returnProductHandling.model.ReturnProductHandling;
import cec.jiutian.bc.qualityExceptionManagement.domain.returnProductHandling.model.ReturnProductHandlingDetail;
import cec.jiutian.bc.qualityExceptionManagement.domain.returnProductHandling.model.ReturnProductHandlingItem;
import cec.jiutian.common.util.BeanUtils;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class ReturnProductInspectionTaskHandler implements DependFiled.DynamicHandler<ReturnProductHandling> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public Map<String, Object> handle(ReturnProductHandling entity) {
        Map<String, Object> result = new HashMap<>();
        result.put("productReturnInspectionTaskNumber", Strings.EMPTY);
        result.put("productReturnInspectionTaskId", Strings.EMPTY);
        result.put("inspectionResult", Strings.EMPTY);
        result.put("unqualifiedLevel", Strings.EMPTY);
        result.put("inspectionDepartment", Strings.EMPTY);
        result.put("inspector", Strings.EMPTY);
        result.put("details", Collections.emptyList());
        if (null != entity.getReturnProductDetail() && null != entity.getReturnProductDetail().getRequestId()) {
            ProductReturnInspectionRequest request = fabosJsonDao.findById(ProductReturnInspectionRequest.class, entity.getReturnProductDetail().getRequestId());
            ProductReturnInspectionTask condition = new ProductReturnInspectionTask();
            condition.setInspectionRequest(request);
            ProductReturnInspectionTask task = fabosJsonDao.selectOne(condition);
            if (null != task) {
                result.put("productReturnInspectionTaskNumber", task.getGeneralCode());
                result.put("productReturnInspectionTaskId", task.getId());
                result.put("inspectionResult", task.getInspectionResult());
                result.put("unqualifiedLevel", task.getUnqualifiedLevel());
                result.put("inspectionDepartment", task.getInspectionDepartmentName());
                result.put("inspector", task.getUserName());

                List<ReturnProductHandlingDetail> details = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(task.getStandardDetailList())) {
                    for (ProductReturnQualityInspectionStandardDetail inspectionStandardDetail : task.getStandardDetailList()) {
                        ReturnProductHandlingDetail detail = new ReturnProductHandlingDetail();
                        BeanUtils.copyProperties(inspectionStandardDetail, detail);
                        detail.setId(null);

                        List<ReturnProductHandlingItem> handlingItems = new ArrayList<>();
                        if (CollectionUtils.isNotEmpty(inspectionStandardDetail.getProductReturnInspectionStandardItemTargetList())) {
                            for (ProductReturnInspectionStandardItemTarget itemTarget : inspectionStandardDetail.getProductReturnInspectionStandardItemTargetList()) {
                                ReturnProductHandlingItem item = new ReturnProductHandlingItem();
                                BeanUtils.copyProperties(itemTarget, item);
                                item.setId(null);
                                handlingItems.add(item);
                            }
                        }
                        detail.setItems(handlingItems);

                        details.add(detail);
                    }
                    result.put("details", details);
                }
            }

        }
        return result;
    }
}
