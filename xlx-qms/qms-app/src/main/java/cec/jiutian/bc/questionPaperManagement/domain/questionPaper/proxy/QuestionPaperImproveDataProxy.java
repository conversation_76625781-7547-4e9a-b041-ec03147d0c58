package cec.jiutian.bc.questionPaperManagement.domain.questionPaper.proxy;

import cec.jiutian.bc.questionPaperManagement.domain.questionPaper.model.QuestionPaperImprove;
import cec.jiutian.bc.questionPaperManagement.enums.QuestionPaperMeasureProgressEnum;
import cec.jiutian.view.fun.DataProxy;
import org.apache.commons.collections.CollectionUtils;

public class QuestionPaperImproveDataProxy implements DataProxy<QuestionPaperImprove> {

    @Override
    public void beforeUpdate(QuestionPaperImprove entity) {
        if (CollectionUtils.isNotEmpty(entity.getImproveMeasures())) {
            entity.setImproveFlag(true);
            entity.getImproveMeasures().forEach(m -> {
                m.setProgress(QuestionPaperMeasureProgressEnum.Enum.WaitExecute.name());
            });
        }
    }

}
