package cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.handler;

import cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.enumration.ProductQualityTrackBusinessStatusEnum;
import cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.model.ProductQualityTrack;
import cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.mto.ProductQualityTrackApprovalMTO;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/05
 * @description TODO
 */
@Component
@Slf4j
public class ProductQualityTrackApprovalHandler implements OperationHandler<ProductQualityTrack, ProductQualityTrackApprovalMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<ProductQualityTrack> data, ProductQualityTrackApprovalMTO modelObject, String[] param) {
        if (modelObject != null) {
            ProductQualityTrack model = data.get(0);
            model.getPqtProcessOperationList().forEach(pq -> {
                pq.setJudgingPersonId(UserContext.getUserId());
                pq.setJudgingPerson(UserContext.getUserName());
            });

            BeanUtil.copyProperties(modelObject, model, "createBy", "createTime");
            model.setBusinessStatus(ProductQualityTrackBusinessStatusEnum.Enum.PENDING_APPROVAL.name());
            fabosJsonDao.mergeAndFlush(model);
        }
        return "alert(操作成功)";
    }

    @Override
    public ProductQualityTrackApprovalMTO fabosJsonFormValue(List<ProductQualityTrack> data, ProductQualityTrackApprovalMTO fabosJsonForm, String[] param) {
        ProductQualityTrack model = data.get(0);
        BeanUtil.copyProperties(model, fabosJsonForm);
        return fabosJsonForm;
    }
}
