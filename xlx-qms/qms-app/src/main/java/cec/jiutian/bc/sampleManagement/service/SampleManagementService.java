package cec.jiutian.bc.sampleManagement.service;

import cec.jiutian.bc.sampleManagement.domain.sampleManagement.enumration.SampleManagementStatusEnum;
import cec.jiutian.bc.sampleManagement.domain.sampleManagement.model.SampleManagement;
import cec.jiutian.bc.sampleManagement.port.dto.SampleManagementDTO;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Component
@Slf4j
public class SampleManagementService {

    @Resource
    private FabosJsonDao fabosJsonDao;

    /**
     * 还样成功，修改单据状态
     */
    @Transactional
    public boolean returnSampleSuccess(SampleManagementDTO sampleManagementDTO) {
        log.info("Successfully returned the sample, the code is " + sampleManagementDTO.getSampleManagementFormNumber());
        SampleManagement condition = new SampleManagement();
        condition.setSampleManagementFormNumber(sampleManagementDTO.getSampleManagementFormNumber());

        SampleManagement sampleManagement = fabosJsonDao.selectOne(condition);
        sampleManagement.setBusinessStatus(SampleManagementStatusEnum.Enum.STORED.name());
        return true;
    }
}
