package cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.MyIssueTasks.model;

import cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.issueList.model.IssueList;
import cec.jiutian.bc.qualityExceptionManagement.enumeration.inspectionIssuesImproveAndTrace.ImproveProgressEnum;
import cec.jiutian.bc.qualityExceptionManagement.enumeration.inspectionIssuesImproveAndTrace.ProgressRUNEnum;
import cec.jiutian.bc.urm.domain.org.entity.Org;
import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.frame.module.MetaUser;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.AttachmentType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DateType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.type.RowBaseOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@FabosJson(
        name = "我的整改任务-整改措施",
        orderBy = "MyIssueListCorrection.createTime desc"
)
@Table(name = "qms_qem_issue_list_correction")
@Entity
@Getter
@Setter
public class MyIssueListCorrection extends BaseModel {
    @FabosJsonField(
            views = @View(title = "整改措施"),
            edit = @Edit(title = "整改措施", readonly = @Readonly)
    )
    private String correction;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "责任部门", column = "name"),
            edit = @Edit(title = "责任部门", readonly = @Readonly,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"))
    )
    @JoinColumn(foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private Org org;
    @ManyToOne
    @FabosJsonField(
            views = @View(title = "责任人", column = "name"),
            edit = @Edit(title = "责任人", readonly = @Readonly,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"))
    )
    @JoinColumn(foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private MetaUser metaUser;

    @FabosJsonField(
            views = @View(title = "纳期"),
            edit = @Edit(title = "纳期", readonly = @Readonly,
                    dateType = @DateType(type = DateType.Type.DATE_TIME))
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date measureDate;

    @FabosJsonField(
            views = {
                    @View(title = "问题清单", column = "name",show = false)
            },
            edit = @Edit(title = "问题清单", type = EditType.REFERENCE_TABLE,show = false,
                    referenceTableType = @ReferenceTableType(label = "name")
            )
    )
    @ManyToOne
    @JsonIgnoreProperties("issueListCorrections")
    private IssueList issueList;

    @FabosJsonField(
            views = @View(title = "是否完成"),
            edit = @Edit(title = "是否完成",type = EditType.CHOICE,notNull = true,
                    choiceType = @ChoiceType(fetchHandler = ProgressRUNEnum.class))
    )
    private String progress;

    @FabosJsonField(
            views = @View(title = "完成日期"),
            edit = @Edit(title = "完成日期", notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE_TIME))
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date completionDate;

    @FabosJsonField(
            views = @View(title = "完成佐证材料"),
            edit = @Edit(title = "完成佐证材料",type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(size = 5120, maxLimit = 10, type = AttachmentType.Type.BASE))
    )
    private String completionEvidence;
}
