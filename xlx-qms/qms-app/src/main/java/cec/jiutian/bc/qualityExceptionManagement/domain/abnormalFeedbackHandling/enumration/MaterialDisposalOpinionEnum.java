package cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.enumration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/27
 * @description TODO
 */
public class MaterialDisposalOpinionEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum data : Enum.values()) {
            list.add(new VLModel(data.name(), data.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        //返工处置任务
        REWORK("返工"),
        //特殊物料处理
        SCRAPPED("报废"),
        DOWNGRADED_TO_CLASSB("降级为B类"),
        DOWNGRADED_TO_CLASSC("降级为C类"),
        //其他处置任务
        CONCESSION_ACCEPTANCE("让步接收"),
        RETURN("退货"),
        SELECT("挑选"),
        ;

        private final String value;

    }
}
