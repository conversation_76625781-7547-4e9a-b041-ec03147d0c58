package cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.proxy;

import cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.mto.AccidentReportTaskCreateMTO;
import cec.jiutian.bc.ecs.dto.SendMsgGroupDTO;
import cec.jiutian.bc.ecs.provider.EcsMessageProvider;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/5/29
 * @description
 */
@Component
@Slf4j
public class AccidentReportTaskCreateMTODataProxy implements DataProxy<AccidentReportTaskCreateMTO> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private EcsMessageProvider ecsMessageProvider;

    @Override
    public void beforeAdd(AccidentReportTaskCreateMTO mto) {
        if (mto == null) {
            return;
        }
    }

    @Override
    public void afterAdd(AccidentReportTaskCreateMTO mto) {
        if (mto == null) {
            return;
        }
        sendQms(mto);
    }

    private void sendQms(AccidentReportTaskCreateMTO model) {
        String message = "事故报告任务单创建成功，请尽快在处理，编号：" + model.getGeneralCode();
        SendMsgGroupDTO sendMsgGroupDTO = new SendMsgGroupDTO();
        sendMsgGroupDTO.setMessageGroupCode("AccidentReportTaskInfo");
        sendMsgGroupDTO.setContent(message);
        ecsMessageProvider.sendGroupMessage(sendMsgGroupDTO);
        log.info(message);
    }
}
