package cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.handler;

import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.model.CorrectiveAction;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.model.MyCorrectiveTask;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.mto.MyCorrectiveTaskExecMTO;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class MyCorrectiveTaskExecHandler implements OperationHandler<MyCorrectiveTask, MyCorrectiveTaskExecMTO> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<MyCorrectiveTask> data, MyCorrectiveTaskExecMTO modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            modelObject.getCorrectiveActionList().forEach(d -> {
                CorrectiveAction correctiveAction = fabosJsonDao.findById(CorrectiveAction.class, d.getId());
                correctiveAction.setImprovingProgress(d.getImprovingProgress());
                correctiveAction.setCompletionDate(d.getCompletionDate());
                correctiveAction.setCompleteSupportMaterial(d.getCompleteSupportMaterial());
                fabosJsonDao.mergeAndFlush(correctiveAction);
            });
        }
        return "msg.success('操作成功')";
    }

    /*@Override
    public MyCorrectiveTaskExecMTO fabosJsonFormValue(List<MyCorrectiveTask> data, MyCorrectiveTaskExecMTO fabosJsonForm, String[] param) {
        // data为空 直接返回
        if (CollectionUtils.isEmpty(data)) {
            return fabosJsonForm;
        }

        String userId = UserContext.getUserId();
        MyCorrectiveTask myCorrectiveTask = data.get(0);
        BeanUtils.copyProperties(myCorrectiveTask, fabosJsonForm);

        //如果list为空 直接返回
        if (CollectionUtils.isEmpty(myCorrectiveTask.getCorrectiveActionList())) {
            return fabosJsonForm;
        }

        List<MyCorrectiveAction> myCorrectiveActions = new ArrayList<>();
        myCorrectiveTask.getCorrectiveActionList().forEach(d->{
            if (Objects.equals(d.getUserForInsTaskMTO().getId(), userId)) {
                MyCorrectiveAction myCorrectiveAction = new MyCorrectiveAction();
                BeanUtils.copyProperties(d, myCorrectiveAction);
                myCorrectiveActions.add(myCorrectiveAction);
            }
        });

        fabosJsonForm.setCorrectiveActionList(myCorrectiveActions);
        return fabosJsonForm;
    }*/
}
