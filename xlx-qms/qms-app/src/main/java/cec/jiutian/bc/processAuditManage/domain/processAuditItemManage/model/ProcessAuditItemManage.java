package cec.jiutian.bc.processAuditManage.domain.processAuditItemManage.model;

import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleBaseModel;
import cec.jiutian.bc.modeler.enumration.NamingRuleCodeEnum;
import cec.jiutian.bc.urm.domain.org.entity.Org;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ReferenceTableType;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

@FabosJson(
        name = "审核项管理",
        orderBy = "ProcessAuditItemManage.createTime desc"
)
@Table(name = "qms_sam_process_audit_item_manage")
@Entity
@Getter
@Setter
public class ProcessAuditItemManage extends NamingRuleBaseModel {
    @Override
    public String getNamingCode() {
        return NamingRuleCodeEnum.ProcessAuditManage.name();
    }

    // 审核项名称
    @FabosJsonField(
            views = @View(title = "审核项名称"),
            edit = @Edit(title = "审核项名称", notNull = true)
    )
    private String auditItemName;

    // 过程名称
    @FabosJsonField(
            views = @View(title = "过程名称"),
            edit = @Edit(title = "过程名称", notNull = true)
    )
    private String processName;

    // 过程要素
    @FabosJsonField(
            views = @View(title = "过程要素"),
            edit = @Edit(title = "过程要素", notNull = true)
    )
    private String processElements;

    // 最低要求
    @FabosJsonField(
            views = @View(title = "最低要求"),
            edit = @Edit(title = "最低要求", notNull = true)
    )
    private String minRequires;

    // 主要涉及部门/场地
    @ManyToOne
    @FabosJsonField(
            views = @View(title = "主要涉及部门/场地", column = "name"),
            edit = @Edit(title = "主要涉及部门/场地",notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"))
    )
    @JoinColumn(foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private Org department;

    // 原材料出入库
    @FabosJsonField(
            views = @View(title = "原材料出入库"),
            edit = @Edit(title = "原材料出入库")
    )
    private String materialStorageControl;

    // 一烧前配混料
    @FabosJsonField(
            views = @View(title = "一烧前配混料"),
            edit = @Edit(title = "一烧前配混料")
    )
    private String preFirstBurnMixing;

    // 一次烧结
    @FabosJsonField(
            views = @View(title = "一次烧结"),
            edit = @Edit(title = "一次烧结")
    )
    private String firstSintering;

    // 一次粉体处理
    @FabosJsonField(
            views = @View(title = "一次粉体处理"),
            edit = @Edit(title = "一次粉体处理")
    )
    private String firstCrushingProcess;

    // 水洗
    @FabosJsonField(
            views = @View(title = "水洗"),
            edit = @Edit(title = "水洗")
    )
    private String washingProcess;

    // 二烧前配混料
    @FabosJsonField(
            views = @View(title = "二烧前配混料"),
            edit = @Edit(title = "二烧前配混料")
    )
    private String preSecondBurnMixing;

    // 二次烧结
    @FabosJsonField(
            views = @View(title = "二次烧结"),
            edit = @Edit(title = "二次烧结")
    )
    private String secondSintering;

    // 二次粉体处理
    @FabosJsonField(
            views = @View(title = "二次粉体处理"),
            edit = @Edit(title = "二次粉体处理")
    )
    private String finalCrushingProcess;

    // 检验
    @FabosJsonField(
            views = @View(title = "检验"),
            edit = @Edit(title = "检验")
    )
    private String inspection;

    // 包装出库
    @FabosJsonField(
            views = @View(title = "包装出库"),
            edit = @Edit(title = "包装出库")
    )
    private String packagingSpec;
}
