package cec.jiutian.bc.productExamineManagement.domain.sampleTask.proxy;

import cec.jiutian.bc.productExamineManagement.domain.sampleTask.model.ProductExamineSamplingTask;
import cec.jiutian.view.fun.DataProxy;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/3/10
 * @description TODO
 */
@Component
public class ProductExamineSamplingTaskProxy implements DataProxy<ProductExamineSamplingTask> {

    @Override
    public void beforeAdd(ProductExamineSamplingTask productExamineSamplingTask) {
        DataProxy.super.beforeAdd(productExamineSamplingTask);
    }
}
