package cec.jiutian.bc.processAuditManage.domain.processAuditImplementPlan.model;

import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.ReferenceGenerateType;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@FabosJson(
        name = "执行",
        orderBy = "ProcessAuditImplementPlanExec.createTime desc"
)
@Table(name = "qms_sam_process_audit_implement_plan", uniqueConstraints = @UniqueConstraint(columnNames = "generalCode"))
@Entity
@Getter
@Setter
public class ProcessAuditImplementPlanExec extends MetaModel {
    //审核项
    @FabosJsonField(
            views = @View(title = "审核项", type = ViewType.TABLE_VIEW),
            edit = @Edit(title = "审核项", type = EditType.TAB_REFERENCE_GENERATE),
            referenceGenerateType = @ReferenceGenerateType(editable = {"auditResult","issueDescription","org",
                    "analysisMethod","measureDeadline","execAttachments","complianceStatus","responsiblePerson","severity",
                    "verifier","completionDeadline","scoreMaterialStorageControl","scorePreFirstBurnMixing",
                    "scoreFirstSintering","scoreFirstCrushingProcess","scoreWashingProcess","scorePreSecondBurnMixing",
                    "scoreSecondSintering","scoreFinalCrushingProcess","scoreInspection","scorePackagingSpec"})
    )
    @JoinColumn(name = "process_audit_implement_plan_id")
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    private List<ProcessExecPlanAuditItem> processExecPlanAuditItemList;
}
