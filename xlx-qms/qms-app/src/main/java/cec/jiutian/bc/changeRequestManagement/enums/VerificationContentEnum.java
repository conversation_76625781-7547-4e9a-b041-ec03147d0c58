package cec.jiutian.bc.changeRequestManagement.enums;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

public class VerificationContentEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum data : Enum.values()) {
            list.add(new VLModel(data.name(), data.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        VERIFICATION_REPORT("完成验证报告（必要时添附件）"),
        MATERIAL_EVALUATION("完成变更前物料评审"),
        EVALUATION_ITEMS("完成变更评审事项"),
        DOCUMENT_STANDARDIZATION("完成文件标准化"),
        OTHER("其它"),

        ;

        private final String value;

    }
}
