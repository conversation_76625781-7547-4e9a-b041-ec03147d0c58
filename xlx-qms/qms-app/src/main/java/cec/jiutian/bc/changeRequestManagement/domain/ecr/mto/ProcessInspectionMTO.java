package cec.jiutian.bc.changeRequestManagement.domain.ecr.mto;

import cec.jiutian.bc.materialInspect.enumeration.InspectionResultEnum;
import cec.jiutian.bc.modeler.enumration.TaskBusinessStateEnum;
import cec.jiutian.bc.processInspect.enumeration.InspectionPlanDetailTypeEnum;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.meta.SkipMetadataScanning;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DependFieldDisplay;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.Search;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

@Table(name = "pi_inspection_task",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Getter
@Setter
@FabosJson(
        name = "过程检验"
)
@SkipMetadataScanning
public class ProcessInspectionMTO extends BaseModel {
    @Column(unique = true)
    @FabosJsonField(
            views = @View(title = "编号", index = -9),
            edit = @Edit(title = "编号",
                    readonly = @Readonly(add = false, edit = true),
                    notNull = true, search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "产品编码"),
            edit = @Edit(title = "产品编码", readonly = @Readonly, search = @Search(vague = true)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "product", beFilledBy = "code"))

    )
    private String productCode;

    @FabosJsonField(
            views = @View(title = "产品名称"),
            edit = @Edit(title = "产品名称", readonly = @Readonly, search = @Search(vague = true)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "product", beFilledBy = "name"))
    )
    private String productName;

    @FabosJsonField(
            views = @View(title = "工艺编码"),
            edit = @Edit(title = "工艺编码", search = @Search(vague = true))
    )
    private String processCode;

    @FabosJsonField(
            views = @View(title = "工序编码"),
            edit = @Edit(title = "工序编码")
    )
    private String operationCode;

    @FabosJsonField(
            views = @View(title = "工序名称"),
            edit = @Edit(title = "工序名称")
    )
    private String operationName;

    @FabosJsonField(
            views = @View(title = "流水序号", show = false),
            edit = @Edit(title = "流水序号", show = false)
    )
    private Integer sequenceNumber;

    @FabosJsonField(
            views = @View(title = "实际取样批号"),
            edit = @Edit(title = "实际取样批号")
    )
    private String actualLotSerialId;

    @FabosJsonField(
            views = @View(title = "取样数量"),
            edit = @Edit(title = "取样数量")
    )
    private Double sampleQuantity;

    @FabosJsonField(
            views = @View(title = "过程检类型"),
            edit = @Edit(title = "过程检类型", type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = InspectionPlanDetailTypeEnum.class))
    )
    private String processInspectionType;

    @FabosJsonField(
            views = @View(title = "检验状态"),
            edit = @Edit(title = "检验状态",
                    search = @Search(),
                    type = EditType.CHOICE,
                    defaultVal = "BE_INSPECT",
                    choiceType = @ChoiceType(fetchHandler = TaskBusinessStateEnum.class),
                    notNull = true
            )
    )
    private String businessState;

    @FabosJsonField(
            views = @View(title = "当前状态"),
            edit = @Edit(title = "当前状态", show = false, type = EditType.CHOICE, search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = OrderCurrentStateEnum.class))
    )
    private String currentState;

    @FabosJsonField(
            views = @View(title = "检验结果"),
            edit = @Edit(title = "检验结果", show = false, type = EditType.CHOICE, search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = InspectionResultEnum.class))
    )
    private String result;

    @FabosJsonField(
            views = @View(title = "不合格等级"),
            edit = @Edit(title = "不合格等级",
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "result == 'UNQUALIFIED'"))
    )
    private String unqualifiedLevel;

    @FabosJsonField(
            views = @View(title = "检验员ID",show = false),
            edit = @Edit(title = "检验员ID",
                    show = false
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "user", beFilledBy = "id"))
    )
    private String userId;

    @FabosJsonField(
            views = @View(title = "检验员姓名"),
            edit = @Edit(title = "检验员姓名",
                    show = false,
                    readonly = @Readonly(add = false, edit = false),
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "user", beFilledBy = "name"))
    )
    private String userName;

}
