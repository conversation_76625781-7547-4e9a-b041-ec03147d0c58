package cec.jiutian.bc.productReturnInspection.domain.inspectionTask.handler;

import cec.jiutian.bc.basicData.domain.inspectionStandard.model.InspectionStandard;
import cec.jiutian.bc.modeler.domain.inspectionItem.model.InspectionItem;
import cec.jiutian.bc.productReturnInspection.domain.inspectionTask.model.ProductReturnInspectionStandardItemTarget;
import cec.jiutian.bc.productReturnInspection.domain.inspectionTask.model.ProductReturnInspectionTask;
import cec.jiutian.bc.productReturnInspection.domain.inspectionTask.model.ProductReturnQualityInspectionStandardDetail;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/3/11
 * @description TODO
 */
@Component
public class ProductReturnInspectionStandardDetailDynamicHandler implements DependFiled.DynamicHandler<ProductReturnInspectionTask> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public Map<String, Object> handle(ProductReturnInspectionTask productReturnInspectionTask) {

        Map<String, Object> result = new HashMap<>();
        List<ProductReturnQualityInspectionStandardDetail> inspectionStandardDetailList = new ArrayList<>();
        if (productReturnInspectionTask.getInspectionStandard() != null) {
            // 带出质检标准
            InspectionStandard inspectionStandard = fabosJsonDao.getById(InspectionStandard.class, productReturnInspectionTask.getInspectionStandard().getId());
            if (CollectionUtils.isNotEmpty(inspectionStandard.getDetails())) {
                inspectionStandard.getDetails().forEach(detail -> {
                    ProductReturnQualityInspectionStandardDetail productReturnQualityInspectionStandardDetail = new ProductReturnQualityInspectionStandardDetail();
                    // 带出质检标准下检验项目
                    InspectionItem inspectionItem = fabosJsonDao.getById(InspectionItem.class, detail.getInspectionItem().getId());
                    productReturnQualityInspectionStandardDetail.setGroupId(detail.getInspectionItemGroup().getId());
                    productReturnQualityInspectionStandardDetail.setItemId(inspectionItem.getId());
                    productReturnQualityInspectionStandardDetail.setCode(inspectionItem.getGeneralCode());
                    productReturnQualityInspectionStandardDetail.setName(inspectionItem.getName());
                    productReturnQualityInspectionStandardDetail.setItemType(inspectionItem.getItemType());
                    productReturnQualityInspectionStandardDetail.setFeature(inspectionItem.getFeature());
                    productReturnQualityInspectionStandardDetail.setInspectionMethod(inspectionItem.getInspectionMethod());
                    productReturnQualityInspectionStandardDetail.setSamplingPlan(inspectionItem.getSamplingPlan());
                    productReturnQualityInspectionStandardDetail.setSendInspectPoint(inspectionItem.getSendPointMTO().getName());
                    productReturnQualityInspectionStandardDetail.setSamplingPoint(inspectionItem.getSamplingPoint());
                    productReturnQualityInspectionStandardDetail.setPackageType(inspectionItem.getPackageType());
                    productReturnQualityInspectionStandardDetail.setSamplingPoint(inspectionItem.getSamplingPoint());
                    productReturnQualityInspectionStandardDetail.setSendInspectPoint(inspectionItem.getSendPointMTO().getName());
                    if (CollectionUtils.isNotEmpty(inspectionItem.getInspectionItemTargetList())) {
                        List<ProductReturnInspectionStandardItemTarget> productReturnInspectionStandardItemTargetList = new ArrayList<>();
                        inspectionItem.getInspectionItemTargetList().forEach(inspectionItemTarget -> {
                            ProductReturnInspectionStandardItemTarget productReturnInspectionStandardItemTarget = new ProductReturnInspectionStandardItemTarget();
                            // 带出检验项下检验指标
                            productReturnInspectionStandardItemTarget.setTargetId(inspectionItemTarget.getId());
                            productReturnInspectionStandardItemTarget.setName(inspectionItemTarget.getName());
                            productReturnInspectionStandardItemTarget.setStandardValue(inspectionItemTarget.getStandardValue());
                            productReturnInspectionStandardItemTarget.setUpperValue(inspectionItemTarget.getUpperValue());
                            productReturnInspectionStandardItemTarget.setIsContainUpper(inspectionItemTarget.getIsContainUpper());
                            productReturnInspectionStandardItemTarget.setIsContainLower(inspectionItemTarget.getIsContainLower());
                            productReturnInspectionStandardItemTarget.setLowerValue(inspectionItemTarget.getLowerValue());
                            productReturnInspectionStandardItemTarget.setInspectionValueType(inspectionItemTarget.getInspectionValueType());
                            productReturnInspectionStandardItemTarget.setComparisonMethod(inspectionItemTarget.getComparisonMethod());
                            productReturnInspectionStandardItemTarget.setUnit(inspectionItemTarget.getUnit());
                            productReturnInspectionStandardItemTarget.setDescription(inspectionItemTarget.getDescription());
                            productReturnInspectionStandardItemTargetList.add(productReturnInspectionStandardItemTarget);
                        });
                        productReturnQualityInspectionStandardDetail.setProductReturnInspectionStandardItemTargetList(productReturnInspectionStandardItemTargetList);
                    }
                    inspectionStandardDetailList.add(productReturnQualityInspectionStandardDetail);
                });
            }
        }

        result.put("standardDetailList", inspectionStandardDetailList);
        return result;
    }
}
