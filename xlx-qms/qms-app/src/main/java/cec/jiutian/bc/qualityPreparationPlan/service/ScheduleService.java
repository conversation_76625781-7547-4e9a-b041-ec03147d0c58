package cec.jiutian.bc.qualityPreparationPlan.service;

import cec.jiutian.bc.qualityPreparationPlan.pojo.ScheduleDTO;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class ScheduleService {

    @Resource
    private FabosJsonDao fabosJsonDao;

    public List<ScheduleDTO> getScheduleByLineAndTime(Long lineId, int year, int month) {
        // 获取指定月份的第一天和最后一天
        YearMonth yearMonth = YearMonth.of(year, month);
        LocalDateTime startOfDay = yearMonth.atDay(1).atStartOfDay();
        LocalDateTime endOfDay = yearMonth.atEndOfMonth().atTime(LocalTime.MAX);

        // 转换为 Timestamp
        Timestamp startDate = Timestamp.valueOf(startOfDay);
        Timestamp endDate = Timestamp.valueOf(endOfDay);


        String sql = buildSQL();

        Map<String, Object> params = new HashMap<>();
        params.put("lineId", lineId);
        params.put("startDate", Timestamp.valueOf(startOfDay));
        params.put("endDate", Timestamp.valueOf(endOfDay));

        List<Map<String, Object>> list = fabosJsonDao.getNamedParameterJdbcTemplate().queryForList(sql, params);

        return buildScheduleDTO(list);
    }

    private List<ScheduleDTO> buildScheduleDTO(List<Map<String, Object>> list) {
        List<ScheduleDTO> scheduleDTOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(list)) {
            return scheduleDTOList;
        }

        for (Map<String, Object> map : list) {
            ScheduleDTO dto = new ScheduleDTO();
            dto.setScheduleDate((Timestamp) map.get("schedule_date"));
            dto.setProcessCode((String) map.get("process_code"));
            dto.setProcessName((String) map.get("process_name"));
            dto.setLineId((Long) map.get("line_id"));
            dto.setBatchNum((BigDecimal) map.get("batch_num"));
            scheduleDTOList.add(dto);
        }
        return scheduleDTOList;
    }

    private String buildSQL() {
        return "WITH\n" +
                "schedule AS(\n" +
                "SELECT\n" +
                " pps.process_id,\n" +
                " pps.schedule_date,\n" +
                " SUM ( pps.remark ) AS remark,\n" +
                " mpo.prcs_oprtn_nm AS process_name,\n" +
                " pps.process_code,\n" +
                " pps.factory_area_id,\n" +
                " mfa.fctry_ara_nm AS workshop_name,\n" +
                " mfa1.fctry_ara_nm AS factory_area_name,\n" +
                " mfa1.ID AS line_id,\n" +
                " pps.schedule_end_date \n" +
                "FROM\n" +
                " production_plan_schedule pps\n" +
                " LEFT JOIN mf_prcs_oprtn mpo ON mpo.ID = pps.process_id\n" +
                " LEFT JOIN mo_fctry_ara mfa ON pps.workshop_id = mfa.\n" +
                " ID LEFT JOIN mo_fctry_ara mfa1 ON pps.factory_area_id = mfa1.ID \n" +
                "WHERE \n" +
                " mfa1.ID = :lineId AND pps.schedule_date BETWEEN :startDate AND :endDate\n" +
                "GROUP BY\n" +
                " pps.process_id,\n" +
                " pps.schedule_date,\n" +
                " mpo.prcs_oprtn_nm,\n" +
                " pps.process_code,\n" +
                " pps.factory_area_id,\n" +
                " mfa.fctry_ara_nm,\n" +
                " mfa1.fctry_ara_nm,\n" +
                " mfa1.ID,\n" +
                " pps.schedule_end_date\n" +
                "),\n" +
                "batch AS (\n" +
                "SELECT\n" +
                " process_operation_code,\n" +
                " process_operation_name,\n" +
                " ROUND((reasonable_error_weight_lower + reasonable_error_weight_upper) / 2.0, 2) AS avg_weight\n" +
                "FROM\n" +
                " batch_error_rule_config\n" +
                ")\n" +
                "SELECT\n" +
                " sc.schedule_date,\n" +
                " sc.process_code,\n" +
                " sc.process_name,\n" +
                " sc.line_id,\n" +
                " ROUND(NULLIF(sc.remark,0)/ba.avg_weight, 2) as batch_num\n" +
                "FROM\n" +
                " schedule sc JOIN batch ba ON sc.process_code=ba.process_operation_code";
    }
}
