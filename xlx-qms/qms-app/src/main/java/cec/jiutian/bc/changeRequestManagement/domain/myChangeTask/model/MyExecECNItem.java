package cec.jiutian.bc.changeRequestManagement.domain.myChangeTask.model;

import cec.jiutian.bc.changeRequestManagement.enums.MyEcnExecProgressEnum;
import cec.jiutian.bc.materialInspect.domain.publicInspectionTask.mto.UserForInsTaskMTO;
import cec.jiutian.bc.mto.OrgMTO;
import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Power;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@Entity
@Table(name = "qms_change_request_item_supplement")
@FabosJson(
        name = "补充变更任务",
        orderBy = "deadline asc",
        power = @Power(add = false, edit = false,delete = false, export = false)
)
public class MyExecECNItem extends BaseModel {

    @FabosJsonField(
            views = @View(title = "变更事项"),
            edit = @Edit(
                    title = "变更事项",
                    inputType = @InputType(length = 60),
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "change_item", length = 60)
    private String changeItem;

    @Transient
    @FabosJsonField(
            views = @View(title = "选择责任部门", column = "name", show = false),
            edit = @Edit(
                    title = "选择责任部门",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType()
            )
    )
    private OrgMTO org;

    @FabosJsonField(
            views = @View(title = "责任部门"),
            edit = @Edit(
                    title = "责任部门",
                    inputType = @InputType(length = 30),
                    notNull = true,
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "org", beFilledBy = "name"))
    )
    @Column(name = "responsible_dept", length = 30)
    private String responsibleDept;

    @FabosJsonField(
            views = @View(title = "责任部门id",show = false),
            edit = @Edit(
                    title = "责任部门",
                    show = false,
                    inputType = @InputType(length = 50),
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "org", beFilledBy = "id"))
    )
    @Column(name = "department_id", length = 50)
    private String departmentId;

    @Transient
    @FabosJsonField(
            views = @View(title = "选择责任人", column = "name", show = false),
            edit = @Edit(
                    title = "选择责任人",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType()
            )
    )
    private UserForInsTaskMTO user;

    @FabosJsonField(
            views = @View(title = "责任人"),
            edit = @Edit(
                    title = "责任人",
                    inputType = @InputType(length = 20),
                    notNull = true,
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "user", beFilledBy = "name"))
    )
    @Column(name = "responsible_person", length = 20)
    private String responsiblePerson;

    @FabosJsonField(
            views = @View(title = "申请人ID", show = false),
            edit = @Edit(
                    show = false,
                    title = "申请人ID",
                    inputType = @InputType(length = 50)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "user", beFilledBy = "id"))
    )
    @Column(name = "responsible_person_id", length = 50)
    private String responsiblePersonId;

    @FabosJsonField(
            views = @View(title = "纳期", type = ViewType.DATE_TIME),
            edit = @Edit(
                    title = "纳期",
                    notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @Column(name = "deadline")
    private Date deadline;

    @FabosJsonField(
            views = @View(title = "是否完成"),
            edit = @Edit(
                    title = "是否完成",
                    inputType = @InputType(length = 20),
                    notNull = true,
                    search = @Search,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = MyEcnExecProgressEnum.class)
            ),
            dynamicField = @DynamicField(passive = true)
    )
    @Column(name = "improvement_progress", length = 20)
    private String progress;

    @FabosJsonField(
            views = @View(title = "补充任务完成日期", type = ViewType.DATE_TIME),
            edit = @Edit(
                    title = "补充任务完成日期",
                    notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @Column(name = "supplement_task_completion_date")
    private Date supplementTaskCompletionDate;

    @FabosJsonField(
            views = @View(title = "完成佐证材料"),
            edit = @Edit(title = "完成佐证材料",
                    inputType = @InputType(length = 300),
                    type = EditType.ATTACHMENT,
                    tips = "最大支持100M文件",
                    attachmentType = @AttachmentType(
                            size = 100 * 1024,
                            maxLimit = 1,
                            type = AttachmentType.Type.BASE)
            )
    )
    @Column(length = 300)
    private String attachment;
}
