package cec.jiutian.bc.clientReviewManagement.domain.problemImprovement.model;

import cec.jiutian.bc.clientComplaintManagement.enumeration.ReviewProblemImproveStatusEnum;
import cec.jiutian.bc.clientReviewManagement.domain.problemImprovement.handler.*;
import cec.jiutian.bc.clientReviewManagement.domain.problemImprovement.mto.MyReviewProblemImprovementAnalyMTO;
import cec.jiutian.bc.clientReviewManagement.domain.problemImprovement.mto.MyReviewProblemImprovementExecuteMTO;
import cec.jiutian.bc.clientReviewManagement.domain.problemImprovement.proxy.MyReviewProblemImprovementDataProxy;
import cec.jiutian.bc.mto.OrgMTO;
import cec.jiutian.bc.urm.domain.user.entity.User;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/05/20
 * @description TODO
 */
@FabosJson(
        name = "我的问题改善任务",
        orderBy = "MyReviewProblemImprovement.createTime desc",
        dataProxy = MyReviewProblemImprovementDataProxy.class,
        power = @Power(add = false,edit = false,delete = false),
        rowOperation = {
                @RowOperation(
                        title = "原因分析",
                        code = "MyReviewProblemImprovement@CAUSEANALY",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = MyReviewProblemImprovementAnalyMTO.class,
                        operationHandler = MyReviewProblemImprovementAnalyMTOHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "MyReviewProblemImprovement@CAUSEANALYZE"
                        ),
                        ifExpr = "selectedItems[0].businessStatus != 'ANALYZING'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "执行",
                        code = "MyReviewProblemImprovement@EXECUTE",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = MyReviewProblemImprovementExecuteMTO.class,
                        operationHandler = MyReviewProblemImprovementExecuteMTOHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "MyReviewProblemImprovement@EXECUTE"
                        ),
                        ifExpr = "selectedItems[0].businessStatus != 'EXECUTING'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "确认",
                        code = "MyReviewProblemImprovement@COMPLETE",
                        operationHandler = MyReviewProblemImprovementCompleteHandler.class,
                        mode = RowOperation.Mode.HEADER,
                        callHint = "该操作不可撤回，确定提交吗？",
                        ifExpr = "selectedItems[0].businessStatus != 'CONFIRMING'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "MyReviewProblemImprovement@COMPLETE"
                        )
                ),
        }
)
@Table(name = "qms_client_review_problem_improvement",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Data
public class MyReviewProblemImprovement extends MetaModel {
    @FabosJsonField(
            views = @View(title = "评审任务单号"),
            edit = @Edit(title = "评审任务单号",
                    readonly = @Readonly(add = false),
                    search = @Search(vague = true),
                    notNull = true)
    )
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "序号"),
            edit = @Edit(title = "序号", show = false)
    )
    private Integer sequenceNumber;

    @FabosJsonField(
            views = @View(title = "问题描述"),
            edit = @Edit(title = "问题描述",
                    notNull = true,
                    search = @Search(vague = true))

    )
    private String problemDescription;

    @FabosJsonField(
            views = @View(title = "计划完成日期", type = ViewType.DATE_TIME),
            edit = @Edit(
                    title = "计划完成日期",
                    notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @Column(name = "planned_completion_date")
    private Date plannedCompletionDate;

    @FabosJsonField(
            views = @View(title = "主责部门", column = "name"),
            edit = @Edit(title = "主责部门",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType(type = TabTableReferType.SelectShowTypeMTM.LIST)
            )
    )
    @ManyToOne
    @JoinColumn(name = "primary_response_department_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private OrgMTO primaryRespOrgMTO;

    @FabosJsonField(
            views = @View(title = "次责部门", column = "name"),
            edit = @Edit(title = "次责部门",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType(type = TabTableReferType.SelectShowTypeMTM.LIST)
            )
    )
    @ManyToOne
    @JoinColumn(name = "secondary_response_department_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private OrgMTO secondaryRespOrgMTO;

    @FabosJsonField(
            views = @View(title = "责任人ID", show = false),
            edit = @Edit(title = "责任人ID", show = false)
    )
    private String responsiblePersonId;

    @FabosJsonField(
            views = @View(title = "责任人"),
            edit = @Edit(title = "责任人")
    )
    private String responsiblePersonName;

    @FabosJsonField(
            views = @View(title = "原因"),
            edit = @Edit(title = "原因", inputType = @InputType(length = 200))
    )
    private String reason;

    @FabosJsonField(
            views = @View(title = "整改措施"),
            edit = @Edit(title = "整改措施")
    )
    private String correction;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "验证人", column = "name"),
            edit = @Edit(title = "验证人",
                    type = EditType.REFERENCE_TABLE,
                    search = @Search(),
                    show = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name")
            )
    )
    private User verifiedUser;

    @FabosJsonField(
            views = @View(title = "实际完成日期", type = ViewType.DATE_TIME),
            edit = @Edit(
                    title = "实际完成日期",
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date actualFinishTime;

    @FabosJsonField(
            views = @View(title = "执行情况"),
            edit = @Edit(title = "执行情况",
                    search = @Search(vague = true))

    )
    private String executionStatus;

    @FabosJsonField(
            views = @View(title = "改善证据"),
            edit = @Edit(title = "改善证据", type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(maxLimit = 10))
    )
    private String improvementEvidence;

    @FabosJsonField(
            views = @View(title = "验收情况"),
            edit = @Edit(title = "验收情况")
    )
    private String acceptanceDescription;

    @FabosJsonField(
            views = @View(title = "佐证材料"),
            edit = @Edit(title = "佐证材料",type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(size = 5120, maxLimit = 10, type = AttachmentType.Type.BASE))
    )
    private String supportProof;

    @FabosJsonField(
            views = @View(title = "完成状态"),
            edit = @Edit(title = "完成状态",
                    show = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ReviewProblemImproveStatusEnum.class),
                    search = @Search(vague = true)
            )
    )
    private String businessStatus;

}
