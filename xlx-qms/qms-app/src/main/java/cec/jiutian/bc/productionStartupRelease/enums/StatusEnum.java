package cec.jiutian.bc.productionStartupRelease.enums;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

public class StatusEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum data : Enum.values()) {
            list.add(new VLModel(data.name(), data.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        //开立，待审批，审批中，审批通过，审批不通过
        EDIT("开立"),
        WAIT_APPROVE("待审批"),
        APPROVE_ING("审批中"),
        PASS("审批通过"),
        NOT_PASS("审批不通过");

        ;

        private final String value;

    }

}
