package cec.jiutian.bc.productionStartupRelease.domain.productionStartupRelease.handler;

import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.productionStartupRelease.domain.productionStartupRelease.model.PLSRReport;
import cec.jiutian.bc.productionStartupRelease.domain.productionStartupRelease.mto.PLSRReportAddMTO;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import lombok.SneakyThrows;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class PLSRReportAddMTOGenerateHandler implements DependFiled.DynamicHandler<PLSRReportAddMTO> {

    @Resource
    private NamingRuleService namingRuleService;

    @Override
    @SneakyThrows
    public Map<String, Object> handle(PLSRReportAddMTO plsrReportAddMTO) {
        Map<String, Object> map = new HashMap<>();
        map.put("generalCode", String.valueOf(namingRuleService.getNameCode(PLSRReport.class.getSimpleName(), 1, null)));
        return map;
    }

}