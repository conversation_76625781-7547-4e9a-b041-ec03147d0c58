package cec.jiutian.bc.qualityRateAlert.domain.sampleManagement.model;

import cec.jiutian.bc.changeRequestManagement.domain.ecr.mto.RoleMTO;
import cec.jiutian.bc.qualityRateAlert.domain.sampleManagement.enumration.ProductTypeEnum;
import cec.jiutian.bc.qualityRateAlert.domain.sampleManagement.handler.ProductQualityRateAlertDynamicHandler;
import cec.jiutian.bc.qualityRateAlert.domain.sampleManagement.proxy.ProductQualityRateAlertDataProxy;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import jakarta.persistence.*;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/06/23
 * @description
 */
@FabosJson(
        name = "产品预警规则",
        orderBy = "ProductQualityRateAlert.createTime desc",
        dataProxy = ProductQualityRateAlertDataProxy.class
)
@Table(name = "qms_product_quality_alert",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Data
public class ProductQualityRateAlert extends MetaModel {
    @FabosJsonField(
            views = @View(title = "预警规则编码"),
            edit = @Edit(title = "预警规则编码",
                    readonly = @Readonly(add = false),
                    search = @Search(vague = true), notNull = true),
            dynamicField = @DynamicField(dependFiled = @DependFiled(buttonName = "生成", changeBy = "id",
                    dynamicHandler = ProductQualityRateAlertDynamicHandler.class))
    )
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "产品类型"),
            edit = @Edit(title = "产品类型",
                    type = EditType.CHOICE,
                    notNull = true,
                    choiceType = @ChoiceType(fetchHandler = ProductTypeEnum.class)
            )
    )
    private String productType;

    @FabosJsonField(
            views = @View(title = "预警下限"),
            edit = @Edit(title = "预警下限",
                    numberType = @NumberType(min = 0, max = 1, precision = 2)))
    private Double minAlterRate;

    @FabosJsonField(
            views = @View(title = "预警上限"),
            edit = @Edit(title = "预警上限",
                    numberType = @NumberType(min = 0, max = 1, precision = 2)))
    private Double maxAlterRate;

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
            name = "qms_product_quality_alert_roles",
            joinColumns = @JoinColumn(name = "alert_id", referencedColumnName = "id",foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT)),
            inverseJoinColumns = @JoinColumn(name = "role_id", referencedColumnName = "id",foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT))
    )
    @FabosJsonField(
            views = @View(title = "通知角色",type = ViewType.TABLE_VIEW),
            edit = @Edit(title = "通知角色", type = EditType.TAB_TABLE_REFER, search = @Search(vague = true),
                    tabTableReferType = @TabTableReferType(type = TabTableReferType.SelectShowTypeMTM.LIST,
                            label = "name")
            )
    )
    private List<RoleMTO> roles;
}
