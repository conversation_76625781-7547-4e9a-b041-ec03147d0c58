package cec.jiutian.bc.productionStartupRelease.domain.AproveTask.handler;

import cec.jiutian.bc.changeRequestManagement.enums.ApproveTaskStatusEnum;
import cec.jiutian.bc.productionStartupRelease.domain.AproveTask.event.PLSRApproveTaskEvent;
import cec.jiutian.bc.productionStartupRelease.domain.AproveTask.modle.PLSRApproveTask;
import cec.jiutian.bc.productionStartupRelease.domain.AproveTask.mto.PLSRApproveMTO;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import jakarta.persistence.LockModeType;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Component
public class PLSRApproveHandler implements OperationHandler<PLSRApproveTask, PLSRApproveMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private PLSRApproveTaskEvent plsrApproveTaskEvent;

    @Override
    @Transactional
    public String exec(List<PLSRApproveTask> data, PLSRApproveMTO modelObject, String[] param) {
        if (CollectionUtils.isEmpty(data)) {
            throw new RuntimeException("数据异常");
        }
        String userName = UserContext.getUserName();
        String userId = UserContext.getUserId();
        if (StringUtils.isBlank(userId) || StringUtils.isBlank(userName)) {
            throw new ServiceException("用户未登录");
        }

        PLSRApproveTask approveTask = fabosJsonDao.getEntityManager().find(PLSRApproveTask.class, data.get(0).getId(), LockModeType.PESSIMISTIC_WRITE);
        if (!ApproveTaskStatusEnum.Enum.WAIT_APPROVE.name().equals(approveTask.getStatus())) {
            return "alert('请刷新页面，该任务已被处理')";
        }
        approveTask.setStatus(ApproveTaskStatusEnum.Enum.COMPLETE.name());
        approveTask.setResult(modelObject.getResult());
        approveTask.setExplain(modelObject.getExplain());
        approveTask.setAttachment(modelObject.getAttachment());
        approveTask.setApproveTime(new Date());
        approveTask.setOperator(userName);
        approveTask.setOperatorId(userId);
        fabosJsonDao.updateAndFlush(approveTask);
        plsrApproveTaskEvent.onEvent(approveTask);
        return "执行成功";
    }


}
