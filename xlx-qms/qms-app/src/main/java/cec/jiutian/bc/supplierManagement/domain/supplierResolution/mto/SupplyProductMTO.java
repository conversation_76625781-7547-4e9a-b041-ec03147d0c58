package cec.jiutian.bc.supplierManagement.domain.supplierResolution.mto;

import cec.jiutian.bc.mto.SpecificationManageMTO;
import cec.jiutian.bc.supplierManagement.domain.problemRectificationList.mto.SupplierMTO;
import cec.jiutian.bc.supplierManagement.enumration.BlackWhiteListEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

@Setter
@Getter
@Entity
@Table(name = "qms_sm_supplier_product")
@FabosJson(
        name = "供应产品",
        orderBy = "createTime desc"
)
public class SupplyProductMTO extends MetaModel {

    @Transient
    @FabosJsonField(
            views = @View(title = "选择物资", show = false, column = "name"),
            edit = @Edit(title = "选择物资",
                    type = EditType.REFERENCE_TABLE,
                    filter = @Filter(value = "validateFlag = 'Y' and specificationCode like '01%'"),
                    referenceTableType = @ReferenceTableType(id = "id", label = "name")
            )
    )
    private SpecificationManageMTO material;

    @FabosJsonField(
            views = @View(title = "产品名称", index = 1),
            edit = @Edit(title = "物资名称",
                    notNull = true,
                    readonly = @Readonly,
                    search = @Search(vague = true),
                    inputType = @InputType(length = 40)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "material", beFilledBy = "name"))
    )
    @Column(length = 40)
    private String name;

    @FabosJsonField(
            views = @View(title = "物资id", show = false),
            edit = @Edit(title = "物资id", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "material", beFilledBy = "id"))
    )
    private String productId;

    @FabosJsonField(
            views = @View(title = "规格型号"),
            edit = @Edit(title = "规格型号",
                    notNull = true,
                    search = @Search(vague = true),
                    inputType = @InputType(length = 40)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "material", beFilledBy = "type"))
    )
    @Column(length = 40)
    private String specification;

    @FabosJsonField(
            views = @View(title = "产品类型", index = 3),
            edit = @Edit(title = "产品类型",
                    notNull = true,
                    search = @Search,
                    inputType = @InputType(length = 20))
    )
    @Column(length = 20)
    private String type;

    @FabosJsonField(
            views = @View(title = "适用产品型号", index = 4),
            edit = @Edit(title = "适用产品型号",
                    notNull = true,
                    search = @Search(vague = true),
                    inputType = @InputType(length = 20))
    )
    private String applicableProductModel;

    @FabosJsonField(
            views = @View(title = "黑/白名单", show = false, index = 5),
            edit = @Edit(title = "黑/白名单",
                    defaultVal = "WHITE",
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = BlackWhiteListEnum.class)
            ),
            dynamicField = @DynamicField(passive = true)
    )
    @Column(length = 20, nullable = false)
    private String blackWhiteList;

    @ManyToOne
    @JoinColumn(name = "supplier_id")
    @JsonIgnore
    @FabosJsonField(
            views = @View(title = "供应商", show = false),
            edit = @Edit(title = "供应商", show = false)
    )
    private SupplierMTO supplierMTO;

    @PrePersist
    public void prePersist() {
        if (StringUtils.isBlank(this.blackWhiteList)) {
            this.blackWhiteList = BlackWhiteListEnum.Enum.White.name();
        }
    }
}

