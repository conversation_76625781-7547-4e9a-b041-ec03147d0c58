package cec.jiutian.bc.inventoryInspection.domain.inspectionTask.mto;

import cec.jiutian.bc.inventoryInspection.enumeration.InspectionResultEnum;
import cec.jiutian.bc.modeler.domain.inspectionItem.handler.ComparisonMethodDynamicHandler;
import cec.jiutian.bc.modeler.enumration.ComparisonMethodEnum;
import cec.jiutian.bc.modeler.enumration.InspectionValueTypeEnum;
import cec.jiutian.bc.modeler.enumration.ItemTypeEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.RowBaseOperation;
import jakarta.persistence.Entity;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025/4/9
 * @description TODO
 */
@FabosJson(
        name = "检验任务结果录入详情MTO",
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "add",
                        ifExpr = "1 == 1",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "1 == 1",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
        }
)
@Entity
@Getter
@Setter
public class InventoryTaskResultEnterDetailMTO extends MetaModel {

    @FabosJsonField(
            views = @View(title = "检验任务编码"),
            edit = @Edit(title = "检验任务编码",readonly = @Readonly, search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    private String taskCode;

    @FabosJsonField(
            views = @View(title = "检验项id",show = false),
            edit = @Edit(title = "检验项id",show = false)
    )
    private String inspectItemId;

    @FabosJsonField(
            views = @View(title = "检验项目名称"),
            edit = @Edit(title = "检验项目名称",readonly = @Readonly, search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    private String name;

    @FabosJsonField(
            views = @View(title = "特性"),
            edit = @Edit(title = "特性",readonly = @Readonly, search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    private String feature;

    @FabosJsonField(
            views = @View(title = "检验项类型"),
            edit = @Edit(title = "检验项类型", type = EditType.CHOICE, readonly = @Readonly,
                    inputType = @InputType(length = 40),
                    choiceType = @ChoiceType(fetchHandler = ItemTypeEnum.class))
    )
    private String itemType;

    @FabosJsonField(
            views = @View(title = "检验项目id",show = false),
            edit = @Edit(title = "检验项目id",show = false)
    )
    private String targetId;

    @FabosJsonField(
            views = @View(title = "检验项指标名称"),
            edit = @Edit(title = "检验项指标名称",readonly = @Readonly, search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    private String targetName;

    @FabosJsonField(
            views = @View(title = "值类型"),
            edit = @Edit(title = "值类型",readonly = @Readonly, type = EditType.CHOICE,search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = InspectionValueTypeEnum.class))
    )
    private String inspectionValueType;

    @FabosJsonField(
            views = @View(title = "比较方式"),
            edit = @Edit(title = "比较方式",
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "inspectionValueType == 'number' || inspectionValueType == 'percentage'"),
                    search = @Search(),
                    type = EditType.CHOICE,
                    readonly = @Readonly,
                    choiceType = @ChoiceType(fetchHandler = ComparisonMethodEnum.class)),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "inspectionValueType",dynamicHandler = ComparisonMethodDynamicHandler.class))
    )
    private String comparisonMethod;

    @FabosJsonField(
            views = @View(title = "单位"),
            edit = @Edit(title = "单位",readonly = @Readonly,
                    inputType = @InputType(length = 40),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "inspectionValueType == 'number'"))
    )
    private String unit;

    @FabosJsonField(
            views = @View(title = "标准值"),
            edit = @Edit(title = "标准值",readonly = @Readonly,dependFieldDisplay = @DependFieldDisplay(showOrHide = "inspectionValueType == 'text' || comparisonMethod == 'equal'"))
    )
    private String standardValue;

    @FabosJsonField(
            views = @View(title = "上限值"),
            edit = @Edit(title = "上限值",readonly = @Readonly,
                    numberType = @NumberType(min=0,max = 9999999,precision = 2),dependFieldDisplay = @DependFieldDisplay(showOrHide = "comparisonMethod == 'range'"))
    )
    private Double upperValue;

    @FabosJsonField(
            views = @View(title = "下限值"),
            edit = @Edit(title = "下限值",readonly = @Readonly,
                    numberType = @NumberType(min=0,max = 9999999,precision = 2),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "comparisonMethod == 'range' || comparisonMethod == 'lowerLimit'"))
    )
    private Double lowerValue;

    @FabosJsonField(
            views = @View(title = "检验值"),
            edit = @Edit(title = "检验值",notNull = true)
    )
    private String resultValue;

    @FabosJsonField(
            views = @View(title = "检验项结果"),
            edit = @Edit(title = "检验项结果",type = EditType.CHOICE,choiceType = @ChoiceType(fetchHandler = InspectionResultEnum.class))
    )
    private String inspectionItemResult;
}
