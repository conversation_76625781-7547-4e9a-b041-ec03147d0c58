package cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.mto;

import cec.jiutian.bc.accidentReportManagement.enumeration.ArmProgressEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025/5/29
 * @description
 */
@FabosJson(name = "原因分析")
@Table(name = "qms_arm_accident_report_task")
@Entity
@Getter
@Setter
public class AccidentReportTaskCauseAnalyMTO extends MetaModel {
    @FabosJsonField(
            views = @View(title = "任务单号", show = false),
            edit = @Edit(title = "任务单号",
                    show = false
            )
    )
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "事故原因（5why 分析））"),
            edit = @Edit(title = "事故原因（5why 分析）",
                    notNull = true,
                    type = EditType.TEXTAREA)
    )
    private String accidentCause;

    @FabosJsonField(
            views = @View(title = "原因分析附件"),
            edit = @Edit(title = "原因分析附件", type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType)
    )
    private String causeAnalysisAttachments;

    @FabosJsonField(
            views = @View(title = "分析状态", show = false),
            edit = @Edit(title = "分析状态",
                    readonly = @Readonly(),
                    type = EditType.CHOICE,
                    show = false,
                    choiceType = @ChoiceType(fetchHandler = ArmProgressEnum.class)
            )
    )
    private String analyticalStatus;

}
