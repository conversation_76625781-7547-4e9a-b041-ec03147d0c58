package cec.jiutian.bc.productReturnInspection.domain.inspectionTask.handler;

import cec.jiutian.bc.modeler.enumration.TaskBusinessStateEnum;
import cec.jiutian.bc.productReturnInspection.domain.inspectionTask.model.ProductReturnInspectionTask;
import cec.jiutian.bc.urm.domain.user.entity.User;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import jakarta.persistence.LockModeType;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Component
@RefreshScope
public class ProductReturnPickupInsTaskHandler implements OperationHandler<ProductReturnInspectionTask, ProductReturnInspectionTask> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Value("${qms.debug}")
    private boolean debug;

    @Override
    @Transactional
    public String exec(List<ProductReturnInspectionTask> data, ProductReturnInspectionTask modelObject, String[] param) {
        if (CollectionUtils.isEmpty(data)) {
            return null;
        }
        String userId = UserContext.getUserId();
        User productReturnUserMTO = fabosJsonDao.findById(User.class, userId);
        if (productReturnUserMTO == null) {
            throw new ServiceException("用户不存在");
        }
        if (!debug && (productReturnUserMTO.getOrg() != null && !productReturnUserMTO.getOrg().getCode().equals("IQC"))) {
            throw new ServiceException("用户不是IQC人员");
        }

        ProductReturnInspectionTask task = data.get(0);
        task = fabosJsonDao.getEntityManager().find(ProductReturnInspectionTask.class, task.getId(), LockModeType.PESSIMISTIC_WRITE);
        if (task.getUserId() != null || task.getUserName() != null) {
            throw new ServiceException("该任务已被领取");
        }
        task.setUpdateBy(UserContext.getUserName());
        task.setUserName(UserContext.getUserName());
        task.setUserId(productReturnUserMTO.getId());
        task.setBusinessState(TaskBusinessStateEnum.Enum.INSPECTING.name());
        fabosJsonDao.mergeAndFlush(task);
        return "操作成功";
    }
}
