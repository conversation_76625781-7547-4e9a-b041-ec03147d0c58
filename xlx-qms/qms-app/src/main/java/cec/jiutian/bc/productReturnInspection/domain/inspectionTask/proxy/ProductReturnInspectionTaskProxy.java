package cec.jiutian.bc.productReturnInspection.domain.inspectionTask.proxy;

import cec.jiutian.bc.basicData.enumeration.ApplyRangeEnum;
import cec.jiutian.bc.basicData.service.AQLSamplingTables;
import cec.jiutian.bc.basicData.service.AQSamplingDataTableService;
import cec.jiutian.bc.basicData.service.BasicDataService;
import cec.jiutian.bc.modeler.domain.samplingPlan.model.SamplingPlan;
import cec.jiutian.bc.modeler.enumration.*;
import cec.jiutian.bc.productReturnInspection.domain.inspectionTask.model.ProductReturnInspectionStandardItemTarget;
import cec.jiutian.bc.productReturnInspection.domain.inspectionTask.model.ProductReturnInspectionTask;
import cec.jiutian.bc.productReturnInspection.domain.inspectionTask.model.ProductReturnQualityInspectionStandardDetail;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2025/3/10
 * @description TODO
 */
@Component
@Slf4j
public class ProductReturnInspectionTaskProxy implements DataProxy<ProductReturnInspectionTask> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private BasicDataService basicDataService;

    @Resource
    private AQLSamplingTables aqlSamplingTables;

    @Resource
    private AQSamplingDataTableService aqSamplingDataTableService;

    @Override
    public void beforeAdd(ProductReturnInspectionTask productReturnInspectionTask) {
        productReturnInspectionTask.setCurrentState(OrderCurrentStateEnum.Enum.EDIT.name());
        productReturnInspectionTask.setBusinessState(TaskBusinessStateEnum.Enum.BE_INSPECT.name());
        productReturnInspectionTask.setInspectionType(ApplyRangeEnum.Enum.productReturnInspect.name());
        setDetailData(productReturnInspectionTask);
    }

    private void setDetailData(ProductReturnInspectionTask productReturnInspectionTask) {
        if (CollectionUtils.isNotEmpty(productReturnInspectionTask.getProductReturnInspectionTaskDetailList())) {
            AtomicInteger index = new AtomicInteger(1);
            productReturnInspectionTask.getProductReturnInspectionTaskDetailList().forEach(taskDetail -> {
                taskDetail.setGeneralCode(productReturnInspectionTask.getGeneralCode() + "_" + String.format("%03d", index.get()));
                index.getAndIncrement();
            });
        }

        if (CollectionUtils.isNotEmpty(productReturnInspectionTask.getStandardDetailList())) {
            calculateStandardValueAc(productReturnInspectionTask);
        }
    }

    private void calculateStandardValueAc(ProductReturnInspectionTask productReturnInspectionTask) {
        //样品数量 优先于 来料数量
        Double quantity = productReturnInspectionTask.getSampleQuantity();
        if (!isQuantityLegal(quantity)) {
            Double arrivalQuantity = productReturnInspectionTask.getArrivalQuantity();
            if (!isQuantityLegal(arrivalQuantity)) {
                return;
            }
            quantity = arrivalQuantity;
        }

        String lotSize = aqlSamplingTables.getLotSize(quantity);

        //检验项目明细
        List<ProductReturnQualityInspectionStandardDetail> standardDetailList = productReturnInspectionTask.getStandardDetailList();
        for (ProductReturnQualityInspectionStandardDetail insDetail : standardDetailList) {
            //抽样方案
            SamplingPlan samplingPlan = fabosJsonDao.getById(SamplingPlan.class, insDetail.getSamplingPlan().getId());
            String samplingStandard = samplingPlan.getSamplingStandard();
            if (!samplingStandard.equals(SamplingStandardEnum.Enum.AQLSampling.name())) {
                continue;
            }

            //AQL_S-2,AQL_Ⅲ.etc
            String levelValue = getLevelValueFromSamplingPlan(samplingPlan);
            String sampleSizeCode = aqlSamplingTables.getSampleSizeCode(lotSize, levelValue);
            String aqlValue = samplingPlan.getAqlValue();
            String acValue = aqSamplingDataTableService.getAcValue(sampleSizeCode, aqlValue);
            log.info("In the sampling plan {}, the sampleSizeCode: {}, the aqlValue: {}, the acValue: {}.",
                    samplingPlan.getName(), sampleSizeCode, aqlValue, acValue);
            setAcValueToStandardDetail(insDetail, acValue, samplingPlan);
        }
    }

    private boolean isQuantityLegal(Double quantity) {
        if (quantity == null || quantity < 2) {
            return false;
        }
        return true;
    }

    private String getLevelValueFromSamplingPlan(SamplingPlan samplingPlan) {
        String AQLInspectionType = samplingPlan.getAQLInspectionType();
        String prefixInspectionLevel = "AQL_";
        if (AQLInspectionType.equals(AQLInspectionTypeEnum.Enum.General.name())) {
            return prefixInspectionLevel + GeneralLevelValueEnum.Enum.valueOf(samplingPlan.getGeneralLevelValue()).getValue();
        } else if (AQLInspectionType.equals(AQLInspectionTypeEnum.Enum.Special.name())) {
            return prefixInspectionLevel + SpecialLevelValue.Enum.valueOf(samplingPlan.getSpecialLevelValue()).getValue();
        }
        log.error("AQLInspectionType error, the AQLInspectionType is " + AQLInspectionType);
        return "";
    }

    private void setAcValueToStandardDetail(ProductReturnQualityInspectionStandardDetail insDetail, String acValue, SamplingPlan samplingPlan) {
        if (acValue == null) {
            return;
        }
        boolean modificationCompleted = false;
        List<ProductReturnInspectionStandardItemTarget> insList = insDetail.getProductReturnInspectionStandardItemTargetList();
        if (insList != null) {
            for (ProductReturnInspectionStandardItemTarget insItem : insList) {
                if (insItem.getInspectionValueType().equals(InspectionValueTypeEnum.Enum.number.name())) {
                    insItem.setStandardValue(acValue);
                    modificationCompleted = true;
                }
            }
        }
        if (!modificationCompleted) {
            samplingPlan.setAcValue(Double.valueOf(acValue));
        }
    }
}
