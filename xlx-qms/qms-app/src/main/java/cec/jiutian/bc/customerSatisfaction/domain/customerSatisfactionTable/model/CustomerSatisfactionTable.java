package cec.jiutian.bc.customerSatisfaction.domain.customerSatisfactionTable.model;

import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.AttachmentType;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025/7/1
 * @description TODO
 */
@FabosJson(
        name = "客户满意度调查表",
        orderBy = "CustomerSatisfactionTable.createTime desc"
)
@Table(name = "qms_cs_customer_satisfaction_table"
)
@Entity
@Getter
@Setter
public class CustomerSatisfactionTable extends MetaModel {

    @FabosJsonField(
            views = @View(title = "附件"),
            edit = @Edit(title = "附件", type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(maxLimit = 10))
    )
    private String attachment;

    @FabosJsonField(
            views = @View(title = "备注"),
            edit = @Edit(title = "备注",
                    type = EditType.TEXTAREA)
    )
    private String remark;
}
