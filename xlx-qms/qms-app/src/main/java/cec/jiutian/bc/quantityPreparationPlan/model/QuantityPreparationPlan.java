package cec.jiutian.bc.quantityPreparationPlan.model;

import cec.jiutian.bc.basicData.enumeration.StatusEnum;
import cec.jiutian.bc.mto.FactoryArea;
import cec.jiutian.bc.quantityPreparationPlan.enumration.MonthEnum;
import cec.jiutian.bc.quantityPreparationPlan.handler.PlanCodeGenerateDynamicHandler;
import cec.jiutian.bc.quantityPreparationPlan.handler.PreparationPlanDetailDynamicHandler;
import cec.jiutian.bc.quantityPreparationPlan.proxy.QuantityPreparationPlanProxy;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.*;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/5
 * @description TODO
 */
@FabosJson(
        name = "质量准备计划",
        orderBy = "QuantityPreparationPlan.createTime desc",
        dataProxy = QuantityPreparationPlanProxy.class
)
@Table(name = "pre_quantity_preparation_plan",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Getter
@Setter
@TemplateType(type = "multiTable")
public class QuantityPreparationPlan extends MetaModel {
    @FabosJsonField(
            views = @View(title = "编号"),
            edit = @Edit(title = "编号", notNull = true),
            dynamicField = @DynamicField(dependFiled = @DependFiled(buttonName = "生成", changeBy = "id",
                    dynamicHandler = PlanCodeGenerateDynamicHandler.class))
    )
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "质量准备计划名称"),
            edit = @Edit(title = "质量准备计划名称", search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    private String name;

    @Transient
    @FabosJsonField(
            views = @View(title = "产线", show = false, column = "factoryAreaName"),
            edit = @Edit(title = "产线",
                    notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "factoryAreaName"),
                    filter = @Filter(value = "factoryAreaTypeCode = '03'"))
    )
    private FactoryArea factoryLine;

    @FabosJsonField(
            views = @View(title = "线体ID", show = false),
            edit = @Edit(title = "线体ID",
                    show = false
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "factoryLine", beFilledBy = "id"))
    )
    private String factoryLineId;

    @FabosJsonField(
            views = @View(title = "产线名称"),
            edit = @Edit(title = "产线名称",
                    search = @Search(vague = true),
                    readonly = @Readonly(add = true, edit = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "factoryLine", beFilledBy = "factoryAreaName"))
    )
    private String factoryLineName;

    @FabosJsonField(
            views = @View(title = "准备计划月份"),
            edit = @Edit(title = "准备计划月份", type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = MonthEnum.class))
    )
    private String planMonth;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态", type = EditType.CHOICE,search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = StatusEnum.class))
    )
    private String status;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "quantity_preparation_plan_id")
    @OrderBy
    @FabosJsonField(
            edit = @Edit(title = "质量准备计划明细", type = EditType.TAB_TABLE_ADD),
            views = @View(title = "质量准备计划明细", type = ViewType.TABLE_VIEW),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = {"factoryLine","planMonth"}, dynamicHandler = PreparationPlanDetailDynamicHandler.class))
    )
    private List<QuantityPreparationPlanDetail> detailList;
}
