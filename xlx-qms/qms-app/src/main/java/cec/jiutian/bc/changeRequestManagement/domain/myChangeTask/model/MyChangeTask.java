package cec.jiutian.bc.changeRequestManagement.domain.myChangeTask.model;

import cec.jiutian.bc.changeRequestManagement.domain.ecn.model.MyECNItem;
import cec.jiutian.bc.changeRequestManagement.domain.myChangeTask.handler.MyEcnExecHandler;
import cec.jiutian.bc.changeRequestManagement.domain.myChangeTask.handler.MyEcnSubmitHandler;
import cec.jiutian.bc.changeRequestManagement.domain.myChangeTask.handler.MyEcrExecHandler;
import cec.jiutian.bc.changeRequestManagement.domain.myChangeTask.handler.MyEcrSubmitHandler;
import cec.jiutian.bc.changeRequestManagement.domain.myChangeTask.proxy.MyChangeTaskDataProxy;
import cec.jiutian.bc.changeRequestManagement.enums.ChangeLevel;
import cec.jiutian.bc.changeRequestManagement.enums.ChangeType;
import cec.jiutian.bc.changeRequestManagement.enums.ECRStatusEnum;
import cec.jiutian.bc.changeRequestManagement.enums.FileStandardEnum;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.module.QMSBaseNamingRuleModel;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@Entity
@Table(name = "qms_change_request",
        indexes = {
                @Index(name = "idx_group_code", columnList = "general_code", unique = true)
        })
@FabosJson(
        name = "我的变更任务",
        orderBy = "createTime desc",
        power = @Power(add = false, delete = false, edit = false, viewDetails = false),
        filter = @Filter("status in ('EXECUTING', 'SUPPLEMENT_EXECUTING')"),
        dataProxy = MyChangeTaskDataProxy.class,
        rowOperation = {
                @RowOperation(
                        title = "变更事项执行",
                        code = "MyChangeTasks@EXEC",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        operationHandler = MyEcrExecHandler.class,
                        fabosJsonClass = MyEcrExec.class,
                        ifExpr = "selectedItems[0].status != 'EXECUTING' || selectedItems[0].rowOperationAuthFlag == 0",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "MyChangeTask@EXEC"
                        )
                ),
                @RowOperation(
                        title = "变更事项结果提交",
                        code = "MyChangeTask@SUBMIT",
                        mode = RowOperation.Mode.HEADER,
                        operationHandler = MyEcrSubmitHandler.class,
                        callHint = "请确认是否提交？",
                        ifExpr = "selectedItems[0].status != 'EXECUTING' || selectedItems[0].rowOperationAuthFlag == 0",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "MyChangeTask@SUBMIT"
                        )
                ),
                @RowOperation(
                        title = "补充任务执行",
                        code = "MyChangeTask@EXECSUPPLEMENT",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        operationHandler = MyEcnExecHandler.class,
                        fabosJsonClass = MyEcnExec.class,
                        ifExpr = "selectedItems[0].status != 'SUPPLEMENT_EXECUTING' || selectedItems[0].supplementAuthFlag == 0",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "MyChangeTask@EXECSUPPLEMENT"
                        )
                ),
                @RowOperation(
                        title = "补充任务结果提交",
                        code = "MyChangeTask@SUBMITSUPPLEMENT",
                        mode = RowOperation.Mode.HEADER,
                        operationHandler = MyEcnSubmitHandler.class,
                        callHint = "请确认是否提交？",
                        ifExpr = "selectedItems[0].status != 'SUPPLEMENT_EXECUTING' || selectedItems[0].supplementAuthFlag == 0",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "MyChangeTask@SUBMITSUPPLEMENT"
                        )
                )
        }
)
@TemplateType(type = "multiTable")
public class MyChangeTask extends QMSBaseNamingRuleModel {

    @FabosJsonField(
            views = @View(title = "变更主题"),
            edit = @Edit(
                    title = "变更主题",
                    inputType = @InputType(length = 20),
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "change_subject", length = 20)
    private String changeSubject;


    @FabosJsonField(
            views = @View(title = "变更类型"),
            edit = @Edit(
                    title = "变更类型",
                    inputType = @InputType(length = 20),
                    notNull = true,
                    search = @Search,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ChangeType.class)
            )
    )
    @Column(name = "change_type", length = 20)
    private String changeType;

    @FabosJsonField(
            views = @View(title = "变更级别"),
            edit = @Edit(
                    title = "变更级别",
                    inputType = @InputType(length = 20),
                    notNull = true,
                    search = @Search,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ChangeLevel.class)
            )
    )
    @Column(name = "change_level", length = 20)
    private String changeLevel;

    @FabosJsonField(
            views = @View(title = "变更原因"),
            edit = @Edit(
                    type = EditType.TEXTAREA,
                    title = "变更原因",
                    inputType = @InputType(length = 100),
                    notNull = true
            )
    )
    @Column(name = "change_reason", length = 100)
    private String changeReason;

    @FabosJsonField(
            views = @View(title = "变更前"),
            edit = @Edit(
                    title = "变更前",
                    type = EditType.TEXTAREA,
                    inputType = @InputType(length = 100),
                    notNull = true
            )
    )
    @Column(name = "before_change", length = 100)
    private String beforeChange;

    @FabosJsonField(
            views = @View(title = "变更后"),
            edit = @Edit(
                    title = "变更前",
                    type = EditType.TEXTAREA,
                    inputType = @InputType(length = 100)
            )
    )
    @Column(name = "after_change", length = 100)
    private String afterChange;

    @FabosJsonField(
            views = @View(title = "预计变更切换时间", type = ViewType.DATE_TIME),
            edit = @Edit(
                    title = "预计变更切换时间",
                    notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @Column(name = "estimated_switch_time")
    private Date estimatedSwitchTime;


    @FabosJsonField(
            views = @View(title = "风险影响"),
            edit = @Edit(
                    title = "风险影响",
                    type = EditType.TEXTAREA,
                    inputType = @InputType(length = 100),
                    notNull = true
            )
    )
    @Column(name = "risk_impact", length = 100)
    private String riskImpact;

    @FabosJsonField(
            views = @View(title = "文件标准化"),
            edit = @Edit(
                    title = "文件标准化",
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = FileStandardEnum.class),
                    inputType = @InputType(length = 100)
            )
    )
    @Column(name = "file_standardization", length = 100)
    private String fileStandardization;

    @FabosJsonField(
            views = @View(title = "附件"),
            edit = @Edit(title = "附件",
                    inputType = @InputType(length = 300),
                    type = EditType.ATTACHMENT,
                    tips = "最大支持100M文件",
                    attachmentType = @AttachmentType(
                            size = 100 * 1024,
                            maxLimit = 1,
                            type = AttachmentType.Type.BASE)
            )
    )
    @Column(length = 300)
    private String attachment;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(
                    title = "状态",
                    inputType = @InputType(length = 20),
                    notNull = true,
                    search = @Search,
                    readonly = @Readonly(add = true, edit = true),
                    defaultVal = "WAIT_SUBMIT",
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ECRStatusEnum.class)
            ),
            dynamicField = @DynamicField(passive = true)
    )
    @Column(name = "status", length = 20)
    private String status;


    @FabosJsonField(
            views = @View(title = "申请人"),
            edit = @Edit(
                    title = "申请人",
                    inputType = @InputType(length = 20),
                    readonly = @Readonly(add = true, edit = true),
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "applicant", length = 20)
    private String applicant;

    @FabosJsonField(
            views = @View(title = "申请人ID", show = false),
            edit = @Edit(
                    show = false,
                    title = "申请人ID",
                    inputType = @InputType(length = 50)
            )
    )
    @Column(name = "applicant_id", length = 50)
    private String applicantId;

    @FabosJsonField(
            views = @View(title = "申请时间", type = ViewType.DATE_TIME),
            edit = @Edit(
                    title = "申请时间",
                    notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @Column(name = "apply_time", nullable = false)
    private Date applyTime;

    @FabosJsonField(
            views = @View(title = "申请部门"),
            edit = @Edit(
                    title = "申请部门",
                    inputType = @InputType(length = 20),
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "apply_department", length = 20)
    private String applyDepartment;

    @FabosJsonField(
            views = @View(title = "申请部门ID", show = false),
            edit = @Edit(
                    show = false,
                    title = "申请部门ID"
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "org", beFilledBy = "id"))
    )
    @Column(name = "department_id", length = 50)
    private String departmentId;

    @FabosJsonField(
            views = @View(title = "申请审批人"),
            edit = @Edit(
                    title = "申请审批人",
                    inputType = @InputType(length = 20),
                    readonly = @Readonly(add = true, edit = true),
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "apply_approver", length = 20)
    private String applyApprover;

    @FabosJsonField(
            views = @View(title = "申请审批人ID", show = false),
            edit = @Edit(
                    show = false,
                    title = "申请审批人ID",
                    inputType = @InputType(length = 50)
            )
    )
    @Column(name = "apply_approve_id", length = 50)
    private String applyApproveId;


    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "ecr_id")
    @FabosJsonField(
            views = @View(title = "变更事项", column = "changeItem", type = ViewType.TABLE_VIEW),
            edit = @Edit(
                    title = "变更事项",
                    type = EditType.TAB_TABLE_ADD,
                    allowAddMultipleRows = true,
                    referenceTableType = @ReferenceTableType(label = "changeItem")
            )
    )
    private List<MyECRItem> MyECRItems;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "ecn_id")
    @FabosJsonField(
            views = @View(title = "补充变更任务", column = "changeItem", type = ViewType.TABLE_VIEW),
            edit = @Edit(
                    title = "补充变更任务",
                    type = EditType.TAB_TABLE_ADD,
                    allowAddMultipleRows = true,
                    referenceTableType = @ReferenceTableType(label = "changeItem")
            )
    )
    private List<MyECNItem> myECNItems;

    @FabosJsonField(
            views = @View(title = "变更事项所有责任人的id集合,我的变更任务查询使用", show = false),
            edit = @Edit(title = "变更事项所有责任人的id集合,我的变更任务查询使用", show = false)
    )
    private String allUserIds;

    @FabosJsonField(
            views = @View(title = "闭环时间",
                    show = false,
                    type = ViewType.DATE_TIME),
            edit = @Edit(
                    show = false,
                    title = "闭环时间",
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date endDate;
}
