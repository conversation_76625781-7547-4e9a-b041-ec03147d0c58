package cec.jiutian.bc.supplierManagement.domian.annualAuditPlan.model;

import cec.jiutian.bc.basicData.enumeration.NamingRuleCodeEnum;
import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleBaseModel;
import cec.jiutian.bc.processInspect.domain.publicInspectionTask.mto.ProcessUserForInsTaskMTO;
import cec.jiutian.bc.supplierManagement.domian.annualAuditPlan.handler.AnnualAuditPlanConfirmOperationHandler;
import cec.jiutian.bc.supplierManagement.domian.annualAuditPlan.handler.AnnualAuditPlanReleaseOperationHandler;
import cec.jiutian.bc.supplierManagement.domian.annualAuditPlan.proxy.AnnualAuditPlanDataProxy;
import cec.jiutian.bc.supplierManagement.domian.supplierInventory.model.SupplierInventory;
import cec.jiutian.bc.supplierManagement.enums.AnnualAuditPlanApplicationEnum;
import cec.jiutian.bc.supplierManagement.enums.AnnualAuditPlanStateEnum;
import cec.jiutian.bc.supplierManagement.enums.AnnualAuditPlanTypeEnum;
import cec.jiutian.bc.supplierManagement.enums.SupplierAuditTypeEnum;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DateType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowBaseOperation;
import cec.jiutian.view.type.RowOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

@Entity
@Table(name = "sm_annual_audit_plan",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        })
@Getter
@Setter
@FabosJson(
        name = "年度审核计划",
        dataProxy = AnnualAuditPlanDataProxy.class,
        power = @Power(export = false),
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "currentState !='Edit'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "currentState !='Edit'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
        },
        rowOperation = {
                @RowOperation(
                        title = "发布",
                        code = "AnnualAuditPlan@RELEASE",
                        operationHandler = AnnualAuditPlanReleaseOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "是否确定操作？",
                        ifExpr = "currentState !='Edit'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "AnnualAuditPlan@RELEASE"
                        )
                ),
                @RowOperation(
                        title = "确认",
                        code = "AnnualAuditPlan@CONFIRM",
                        operationHandler = AnnualAuditPlanConfirmOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "是否确定操作？",
                        ifExpr = "currentState !='WaitConfirm'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "AnnualAuditPlan@CONFIRM"
                        )
                ),
        }
)
public class SupplierAnnualAuditPlan extends NamingRuleBaseModel {

    @Override
    public String getNamingCode() {
        return NamingRuleCodeEnum.SupplierAnnualAuditPlan.name();
    }

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "供应商", column = "supplierName", show = false),
            edit = @Edit(title = "供应商", notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "supplierName")
            )
    )
    private SupplierInventory supplierInventory;

    @FabosJsonField(
            views = @View(title = "供应商"),
            edit = @Edit(title = "供应商", show = false, search = @Search(vague = true)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "supplierInventory", beFilledBy = "supplierName"))
    )
    private String supplierName;

    @FabosJsonField(
            views = @View(title = "等级"),
            edit = @Edit(title = "等级"/*, notNull = true, search = @Search(),
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = AnnualAuditPlanLevelEnum.class)*/)
    )
    private String level;

    @FabosJsonField(
            views = @View(title = "运用场景"),
            edit = @Edit(title = "运用场景", notNull = true, search = @Search(),
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = AnnualAuditPlanApplicationEnum.class))
    )
    private String applicationScenario;

    @FabosJsonField(
            views = @View(title = "计划类型"),
            edit = @Edit(title = "计划类型", notNull = true, search = @Search(),
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = AnnualAuditPlanTypeEnum.class))
    )
    private String type;

    // todo: 审核类型 为复选框 页面显示不了中文名
    @FabosJsonField(
            views = @View(title = "审核类型"),
            edit = @Edit(title = "审核类型", search = @Search(),
                    type = EditType.multiple_select,
                    choiceType = @ChoiceType(fetchHandler = SupplierAuditTypeEnum.class))
    )
    private String auditType;

    @FabosJsonField(
            views = @View(title = "已通量管理体系"),
            edit = @Edit(title = "已通量管理体系")
    )
    private String fluxManagement;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "计划时间", type = ViewType.DATE),
            edit = @Edit(title = "计划时间", dateType = @DateType(type = DateType.Type.DATE))
    )
    private LocalDate planTime;

    @Transient
    @FabosJsonField(
            views = @View(title = "负责人", column = "name", show = false),
            edit = @Edit(title = "负责人", notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType()
            )
    )
    private ProcessUserForInsTaskMTO userMTO;

    @FabosJsonField(
            views = @View(title = "负责人ID", show = false),
            edit = @Edit(title = "负责人ID", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "userMTO", beFilledBy = "id"))
    )
    private String directorId;

    @FabosJsonField(
            views = @View(title = "负责人"),
            edit = @Edit(title = "负责人", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "userMTO", beFilledBy = "name"))
    )
    private String directorName;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态", show = false, search = @Search,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = AnnualAuditPlanStateEnum.class))
    )
    private String currentState;

}
