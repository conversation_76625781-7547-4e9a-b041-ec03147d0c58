package cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.service;

import cec.jiutian.bc.basicData.enumeration.NamingRuleCodeEnum;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.mto.CorrectPreventMeasureCreateMTO;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.proxy.CorrectPreventMeasureCreateMTODataProxy;
import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.modeler.vo.CorrectPreventMeasureVO;
import cec.jiutian.bc.urm.domain.user.entity.User;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.common.util.BeanUtils;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <AUTHOR>
 * @description:
 */
@Service
public class CorrectPreventMeasureService {
    @Resource
    public FabosJsonDao fabosJsonDao;
    @Resource
    private NamingRuleService namingRuleService;
    @Resource
    private CorrectPreventMeasureCreateMTODataProxy correctPreventMeasureCreateMTODataProxy;

    public String create(CorrectPreventMeasureVO correctPreventMeasureVO){
        CorrectPreventMeasureCreateMTO correctPreventMeasureCreateMTO = new CorrectPreventMeasureCreateMTO();
        BeanUtils.copyNotEmptyProperties(correctPreventMeasureVO, correctPreventMeasureCreateMTO);
        String code = namingRuleService.getNameCode(NamingRuleCodeEnum.CORRECT_PREVENT_MEASURE.name(), 1, null).get(0);
        correctPreventMeasureCreateMTO.setCreatedTime(new Date());
        correctPreventMeasureCreateMTODataProxy.beforeAdd(correctPreventMeasureCreateMTO);
        fabosJsonDao.persistAndFlush(correctPreventMeasureCreateMTO);
        return code;
    }
}
