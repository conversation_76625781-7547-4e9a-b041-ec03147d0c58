package cec.jiutian.bc.productionStartupRelease.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.HashMap;

public class ApproveNodeEnum {

    private static HashMap<String, Node> approveChainMap = new HashMap<>();

    private static ArrayList<Node> approveChain  = new ArrayList<>();
    static {
        Node[] values = Node.values();
        for (Node node : values) {
            approveChain.add(node);
            approveChainMap.put(node.name(), node);
        }
    }

    public static Node getNode(String status) {
        return approveChainMap.get(status);
    }

    @Getter
    @AllArgsConstructor
    public enum Node {
        WORKSHOP_PROCESS("车间工艺负责人"),
        PROCESS_TECHNOLOGY("工艺技术部负责人"),

        WORKSHOP_PRODUCTION("车间生产负责人"),
        CATHODE_MATERIAL_PRODUCTION("正极材料生产车间负责人"),

        WORKSHOP_EQUIPMENT("车间设备负责人"),
        EQUIPMENT_POWER("设备动力部负责人"),

        WORKSHOP_QUALITY("车间品质负责人"),
        QUALITY_MANAGEMENT("质量管理部负责人");

        private String value;
    }
}
