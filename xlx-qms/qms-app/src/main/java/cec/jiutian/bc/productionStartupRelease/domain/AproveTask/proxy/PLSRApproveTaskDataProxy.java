package cec.jiutian.bc.productionStartupRelease.domain.AproveTask.proxy;

import cec.jiutian.bc.productionStartupRelease.domain.AproveTask.modle.PLSRApproveTask;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.view.fun.DataProxy;
import cec.jiutian.view.query.Condition;
import org.apache.commons.collections.CollectionUtils;

import java.util.HashSet;
import java.util.List;

public class PLSRApproveTaskDataProxy implements DataProxy<PLSRApproveTask> {

    @Override
    public String beforeFetch(List<Condition> conditions) {

        UserContext.CurrentUser currentUser = UserContext.get();
        List<String> roleIds = currentUser.getRoleIds();
        String userId = currentUser.getUserId();

        StringBuilder conditionBuilder = new StringBuilder();
        conditionBuilder.append("PLSRApproveTask.assignee = '").append(userId).append("' ");
        if (CollectionUtils.isNotEmpty(roleIds)) {
            HashSet<String> rIds = new HashSet<>();
            rIds.addAll(roleIds);

            conditionBuilder.append("or PLSRApproveTask.role in (");
            rIds.forEach(roleId -> {
                conditionBuilder.append("'").append(roleId).append("',");
            });
            conditionBuilder.replace(conditionBuilder.length() - 1, conditionBuilder.length(), ")");
        }
        return conditionBuilder.toString();
    }
}
