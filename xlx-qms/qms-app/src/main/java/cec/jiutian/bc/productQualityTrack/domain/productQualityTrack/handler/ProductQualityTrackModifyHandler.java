package cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.handler;

import cec.jiutian.bc.mto.FactoryArea;
import cec.jiutian.bc.mto.SpecificationManageMTO;
import cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.model.ProductQualityTrack;
import cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.mto.MachineStartupWireChangeMTO;
import cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.mto.ProductQualityTrackCreateMTO;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/15
 * @description
 */
@Component
public class ProductQualityTrackModifyHandler implements OperationHandler<ProductQualityTrack, ProductQualityTrackCreateMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<ProductQualityTrack> data, ProductQualityTrackCreateMTO modelObject, String[] param) {
        if (modelObject != null) {
            ProductQualityTrack model = data.get(0);
            BeanUtil.copyProperties(modelObject, model, "createBy", "createTime");
            fabosJsonDao.mergeAndFlush(model);
        }
        return "alert(操作成功)";
    }

    @Override
    public ProductQualityTrackCreateMTO fabosJsonFormValue(List<ProductQualityTrack> data, ProductQualityTrackCreateMTO fabosJsonForm, String[] param) {
        ProductQualityTrack model = data.get(0);
        BeanUtil.copyProperties(model, fabosJsonForm);
        if (fabosJsonForm.getFactoryAreaId() != null) {
            FactoryArea faCondition = new FactoryArea();
            faCondition.setId(Long.parseLong(fabosJsonForm.getFactoryAreaId()));
            fabosJsonForm.setFactoryArea(fabosJsonDao.selectOne(faCondition));
        }

        if (fabosJsonForm.getFactoryLineId() != null) {
            FactoryArea faCondition = new FactoryArea();
            faCondition.setId(Long.parseLong(fabosJsonForm.getFactoryLineId()));
            fabosJsonForm.setFactoryLine(fabosJsonDao.selectOne(faCondition));
        }

        if (fabosJsonForm.getMaterialId() != null) {
            SpecificationManageMTO condition = new SpecificationManageMTO();
            condition.setId(Long.parseLong(fabosJsonForm.getMaterialId()));
            fabosJsonForm.setMaterial(fabosJsonDao.selectOne(condition));
        }

        if (fabosJsonForm.getMwGid() != null) {
            MachineStartupWireChangeMTO mwMTO = new MachineStartupWireChangeMTO();
            mwMTO.setId(fabosJsonForm.getMwGid());
            fabosJsonForm.setMachineStartupWireChangeMTO(fabosJsonDao.selectOne(mwMTO));
        }

        return fabosJsonForm;
    }
}
