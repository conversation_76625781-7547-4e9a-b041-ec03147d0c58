package cec.jiutian.bc.productReturnInspection.enumeration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.Getter;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Getter
public enum InsItemResEnum {
    //合格,不合格
    PASS("合格"),
    FAIL("不合格")
    ;

    private String value;

    InsItemResEnum(String value) {
        this.value = value;
    }
    public static class ChoiceFetch implements ChoiceFetchHandler {
        @Override
        public List<VLModel> fetch(String[] params) {
            return Stream.of(InsItemResEnum.values()).map(insItemResEnum ->
                    new VLModel(insItemResEnum.name(), insItemResEnum.getValue())).collect(Collectors.toList());
        }

    }

}
