package cec.jiutian.bc.managementReview.domain.managementReviewPlan.proxy;

import cec.jiutian.bc.managementReview.domain.managementReviewPlan.model.ManagementReviewPlan;
import cec.jiutian.bc.processInspect.domain.publicInspectionTask.mto.ProcessUserForInsTaskMTO;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class ManagementReviewPlanDataProxy implements DataProxy<ManagementReviewPlan> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public void beforeAdd(ManagementReviewPlan entity) {
        entity.setCurrentState(OrderCurrentStateEnum.Enum.EDIT.name());
    }

    @Override
    public void afterSingleFetch(Map<String, Object> map) {
        if (map.get("presenterId") != null) {
            ProcessUserForInsTaskMTO userMTO = fabosJsonDao.findById(ProcessUserForInsTaskMTO.class, map.get("presenterId"));
            if (userMTO != null) {
                map.put("presenterMTO", userMTO);
                map.put("presenterMTO_name", userMTO.getName());
            } else {
                throw new FabosJsonApiErrorTip("未查到人员:" + map.get("presenterName") + "源数据，请确认");
            }
        }
        if (map.get("memberId") != null) {
            ProcessUserForInsTaskMTO userMTO = fabosJsonDao.findById(ProcessUserForInsTaskMTO.class, map.get("memberId"));
            if (userMTO != null) {
                map.put("memberMTO", userMTO);
                map.put("memberMTO_name", userMTO.getName());
            } else {
                throw new FabosJsonApiErrorTip("未查到人员:" + map.get("memberName") + "源数据，请确认");
            }
        }
        if (map.get("reviewerId") != null) {
            ProcessUserForInsTaskMTO userMTO = fabosJsonDao.findById(ProcessUserForInsTaskMTO.class, map.get("reviewerId"));
            if (userMTO != null) {
                map.put("reviewerMTO", userMTO);
                map.put("reviewerMTO_name", userMTO.getName());
            } else {
                throw new FabosJsonApiErrorTip("未查到人员:" + map.get("reviewerName") + "源数据，请确认");
            }
        }
        if (map.get("managerId") != null) {
            ProcessUserForInsTaskMTO userMTO = fabosJsonDao.findById(ProcessUserForInsTaskMTO.class, map.get("managerId"));
            if (userMTO != null) {
                map.put("managerMTO", userMTO);
                map.put("managerMTO_name", userMTO.getName());
            } else {
                throw new FabosJsonApiErrorTip("未查到人员:" + map.get("managerName") + "源数据，请确认");
            }
        }

    }

}
