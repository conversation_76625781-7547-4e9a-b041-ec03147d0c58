package cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.enumration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/25
 * @description TODO
 */
public class AnomalyClassificationEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum data : Enum.values()) {
            list.add(new VLModel(data.name(), data.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        DEVICE_EXCEPTION("设备异常"),
        ABNORMAL_MATERIAL_INDICATORS("物料指标异常"),
        SCREEN_BREAKAGE("筛网破损"),
        ABNORMAL_PERSONNEL_OPERATION("人员操作异常"),
        PROCESS_POSITION_EXCEPTION("工序仓位异常"),
        ABNORMAL_MATERIAL("异常物料"),
        DETECTING_ANOMALIES("检验异常"),
        ;

        private final String value;

    }
}
