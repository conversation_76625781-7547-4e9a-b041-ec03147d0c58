package cec.jiutian.bc.accidentReportManagement.domain.armMyTempMeasureTask.model;

import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.ReferenceGenerateType;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.type.Power;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@Entity
@Table(name = "qms_arm_accident_report_task")
@FabosJson(
        name = "我的临时措施",
        orderBy = "ArmMyTempMeasureTaskExec.createTime desc",
        power = @Power(add = false,delete = false,edit = false)
)
public class ArmMyTempMeasureTaskExec extends MetaModel {

    //临时措施
    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumn(name = "qms_arm_accident_report_task_id")
    @FabosJsonField(
            views = @View(title = "临时措施", column = "temporaryMeasure", type = ViewType.TABLE_VIEW),
            edit = @Edit(
                    title = "临时措施",
                    type = EditType.TAB_REFERENCE_GENERATE),
            referenceGenerateType = @ReferenceGenerateType(editable = {"progress", "completionDate", "completeSupportMaterial"})
    )
    private List<ArmMyTempMeasure> armMyTempMeasureList;
}
