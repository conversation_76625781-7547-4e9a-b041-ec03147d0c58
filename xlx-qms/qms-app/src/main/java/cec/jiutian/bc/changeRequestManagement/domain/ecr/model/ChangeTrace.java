package cec.jiutian.bc.changeRequestManagement.domain.ecr.model;

import cec.jiutian.bc.changeRequestManagement.enums.ReqTraceResultEnum;
import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.NumberType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Setter
@Getter
@Entity
@Table(name = "qms_change_trace")
@FabosJson(
        name = "变更追溯"
)
public class ChangeTrace extends BaseModel {

    @FabosJsonField(
            views = @View(title = "工序流水号"),
            edit = @Edit(title = "工序流水号", readonly = @Readonly)
    )
    @Column(name = "serial_number", nullable = false, length = 256)
    private String serialNumber; // 工序流水号

    @FabosJsonField(
            views = @View(title = "工序"),
            edit = @Edit(title = "工序", readonly = @Readonly)
    )
    @Column(name = "process", length = 256)
    private String process;

    @FabosJsonField(
            views = @View(title = "工序编码",show = false),
            edit = @Edit(title = "工序编码",
                    show = false
            )
    )
    @Column(name = "process_code", length = 256)
    private String processCode;

    @FabosJsonField(
            views = @View(title = "重量"),
            edit = @Edit(title = "重量",
                    numberType = @NumberType(min = 0, precision = 2),
                    readonly = @Readonly)
    )
    @Column(name = "weight", length = 20)
    private BigDecimal weight;

    @FabosJsonField(
            views = @View(title = "检验任务单号"),
            edit = @Edit(title = "检验任务单号", readonly = @Readonly)
    )
    @Column(name = "inspection_code", length = 256)
    private String inspectionCode;

    @FabosJsonField(
            views = @View(title = "检验结果"),
            edit = @Edit(title = "检验结果",
                    readonly = @Readonly,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ReqTraceResultEnum.class)
            )
    )
    @Column(name = "inspection_result", length = 20)
    private String inspectionResult;    // 检验结果

    @Column(name = "ecr_id", length = 32)
    private String ecrId;


}
