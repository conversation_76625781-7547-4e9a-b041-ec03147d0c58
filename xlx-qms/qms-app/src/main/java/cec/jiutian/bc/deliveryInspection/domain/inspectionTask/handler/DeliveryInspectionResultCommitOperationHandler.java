package cec.jiutian.bc.deliveryInspection.domain.inspectionTask.handler;

import cec.jiutian.bc.deliveryInspection.domain.inspectionTask.model.DeliveryInspectionItemDetail;
import cec.jiutian.bc.deliveryInspection.domain.inspectionTask.model.MyDeliveryInspectionTask;
import cec.jiutian.bc.generalModeler.util.StringUtils;
import cec.jiutian.bc.modeler.enumration.TaskBusinessStateEnum;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/28
 * @description TODO
 */
@Component
public class DeliveryInspectionResultCommitOperationHandler implements OperationHandler<MyDeliveryInspectionTask, Void> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<MyDeliveryInspectionTask> data, Void modelObject, String[] param) {
        MyDeliveryInspectionTask myDeliveryInspectionTask = data.get(0);
        List<DeliveryInspectionItemDetail> standardDetailList = myDeliveryInspectionTask.getStandardDetailList().stream().filter(detail -> StringUtils.isEmpty(detail.getInspectionResult())).toList();
        if (CollectionUtils.isNotEmpty(standardDetailList)) {
            throw new FabosJsonApiErrorTip("检验任务中检验项结果未全部录入，请确认");
        }else {
            myDeliveryInspectionTask.setBusinessState(TaskBusinessStateEnum.Enum.INSPECT_FINISH.name());
            fabosJsonDao.mergeAndFlush(myDeliveryInspectionTask);
        }

        return "alert('操作成功')";
    }
}
