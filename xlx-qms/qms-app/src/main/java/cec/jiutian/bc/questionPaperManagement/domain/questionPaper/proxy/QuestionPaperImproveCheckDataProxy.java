package cec.jiutian.bc.questionPaperManagement.domain.questionPaper.proxy;

import cec.jiutian.bc.questionPaperManagement.domain.questionPaper.model.QuestionPaperImproveCheck;
import cec.jiutian.bc.questionPaperManagement.enums.QuestionPaperMeasureCheckEnum;
import cec.jiutian.bc.questionPaperManagement.enums.QuestionPaperMeasureProgressEnum;
import cec.jiutian.view.fun.DataProxy;
import org.apache.commons.collections.CollectionUtils;

public class QuestionPaperImproveCheckDataProxy implements DataProxy<QuestionPaperImproveCheck> {

    @Override
    public void beforeUpdate(QuestionPaperImproveCheck entity) {
        if (CollectionUtils.isNotEmpty(entity.getImproveMeasures())) {
            entity.getImproveMeasures().forEach(m -> {
                // 更新 待验证 状态的措施
                if (m.getProgress().equals(QuestionPaperMeasureProgressEnum.Enum.WaitCheck.name())) {
                    if (m.getCheckResult().equals(QuestionPaperMeasureCheckEnum.Enum.Pass.name())) {
                        m.setProgress(QuestionPaperMeasureProgressEnum.Enum.Complete.name());
                    } else {
                        m.setProgress(QuestionPaperMeasureProgressEnum.Enum.WaitExecute.name());
                    }
                }

            });
        }
    }

}
