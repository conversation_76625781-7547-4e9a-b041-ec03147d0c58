package cec.jiutian.bc.qualityExceptionManagement.domain.returnProductHandling.mto;

import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.View;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Getter;
import lombok.Setter;

@FabosJson(
        name = "退货产品批次信息",
        dataProxy = ReturnProductInspectionDtlMTODataProxy.class
)
@Table(name = "mi_inspection_request_detail")
//@QueryModel(hql = "select new map(" +
//        "request.productRefundNumber as productRefundNumber, " +
//        "request.supplierName as customerName, " +
//        "request.refundReason as refundReason, " +
//        "request.id as requestId, " +
//        "detail.lotSerialId as lotSerialId, " +
//        "detail.inventoryId as inventoryId, " +
//        "detail.materialCode as materialCode, " +
//        "detail.materialName as materialName, " +
//        "detail.materialSpecification as materialSpecification, " +
//        "detail.lotQuantity as lotQuantity, " +
//        "detail.measureUnit as measureUnit, detail.id as id" +
//        ") " +
//        "from ProductReturnInspectionRequest request " +
//        "join request.details as detail")
@Entity
@Getter
@Setter
public class ReturnProductInspectionRequestDetailMTO extends MetaModel {

    //        @Formula("(select t2.product_refund_number from bwi_product_refund_detail t1, bwi_product_refund t2 where t1.product_refund_id = t2.id and t1.id = id)")
    @Transient
    @FabosJsonField(
            views = @View(title = "退货单号"),
            edit = @Edit(title = "退货单号")
    )
    private String productRefundNumber;

    @Transient
    @FabosJsonField(
            views = @View(title = "退货检验通知单id", show = false),
            edit = @Edit(title = "退货检验通知单id")
    )
    private String requestId;

    //    @Formula("(select t2.customer_name from bwi_product_refund_detail t1, bwi_product_refund t2 where t1.product_refund_id = t2.id and t1.id = id)")
    @Transient
    @FabosJsonField(
            views = @View(title = "客户名称"),
            edit = @Edit(title = "客户名称")
    )
    private String customerName;

    //    @Formula("(select t2.refund_reason from bwi_product_refund_detail t1, bwi_product_refund t2 where t1.product_refund_id = t2.id and t1.id = id)")
    @Transient
    @FabosJsonField(
            views = @View(title = "退货原因"),
            edit = @Edit(title = "退货原因")
    )
    private String refundReason;

    @FabosJsonField(
            views = @View(title = "本厂批号"),
            edit = @Edit(title = "本厂批号")
    )
    private String lotSerialId;

    @FabosJsonField(
            views = @View(title = "本厂批号/件次号库存Id", show = false),
            edit = @Edit(title = "本厂批号/件次号库存Id")
    )
    private String inventoryId;

    @FabosJsonField(
            views = @View(title = "产品编码"),
            edit = @Edit(title = "产品编码")
    )
    private String materialCode;

    @FabosJsonField(
            views = @View(title = "产品名称"),
            edit = @Edit(title = "产品名称")
    )
    private String materialName;

    @FabosJsonField(
            views = @View(title = "型号规格"),
            edit = @Edit(title = "型号规格")
    )
    private String materialSpecification;

    @FabosJsonField(
            views = @View(title = "退货数量"),
            edit = @Edit(title = "退货数量")
    )
    private Double lotQuantity;

    @FabosJsonField(
            views = @View(title = "单位"),
            edit = @Edit(title = "单位")
    )
    private String measureUnit;

}
