package cec.jiutian.bc.productionStartupRelease.domain.AproveTask.mto;

import cec.jiutian.bc.changeRequestManagement.enums.ApproveResultEnum;
import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.AttachmentType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.InputType;
import jakarta.persistence.Column;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@FabosJson(
        name = "审批"
)
public class PLSRApproveMTO extends BaseModel {

    @FabosJsonField(
            views = @View(title = "审批意见"),
            edit = @Edit(
                    title = "审批意见",
                    notNull = true,
                    inputType = @InputType(length = 10),
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ApproveResultEnum.class)
            )
    )
    @Column(length = 10)
    private String result;

    @FabosJsonField(
            views = @View(title = "意见说明"),
            edit = @Edit(
                    title = "意见说明",
                    inputType = @InputType(length = 100)
            )
    )
    @Column(length = 100)
    private String explain;

    @FabosJsonField(
            views = @View(title = "附件"),
            edit = @Edit(title = "上传附件",
                    inputType = @InputType(length = 300),
                    type = EditType.ATTACHMENT,
                    tips = "最大支持5个大小为100M的文件",
                    attachmentType = @AttachmentType(
                            size = 100 * 1024,
                            maxLimit = 5,
                            type = AttachmentType.Type.BASE)
            )
    )
    @Column(length = 300)
    private String attachment;

}
