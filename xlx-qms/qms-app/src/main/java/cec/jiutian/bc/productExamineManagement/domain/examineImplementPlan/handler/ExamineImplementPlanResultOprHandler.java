package cec.jiutian.bc.productExamineManagement.domain.examineImplementPlan.handler;

import cec.jiutian.bc.modeler.enumration.CheckResultEnum;
import cec.jiutian.bc.productExamineManagement.domain.examineImplementPlan.model.ExamineImplementPlan;
import cec.jiutian.bc.productExamineManagement.domain.examineImplementPlan.mto.ExamineImplementPlanResultMTO;
import cec.jiutian.bc.productExamineManagement.domain.examineYearlyPlan.model.ProductExamineYearlyPlan;
import cec.jiutian.bc.productExamineManagement.enumration.ExamineImplementStateEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/24
 * @description TODO
 */
@Component
public class ExamineImplementPlanResultOprHandler implements OperationHandler<ExamineImplementPlan, ExamineImplementPlanResultMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<ExamineImplementPlan> data, ExamineImplementPlanResultMTO modelObject, String[] param) {
        ExamineImplementPlan examineImplementPlan = data.get(0);
        if (CheckResultEnum.Enum.Pass.name().equals(modelObject.getExamineResult())) {
            examineImplementPlan.setBusinessState(ExamineImplementStateEnum.Enum.Examined.name());
        }else {
            examineImplementPlan.setBusinessState(ExamineImplementStateEnum.Enum.Edit.name());
        }
        fabosJsonDao.mergeAndFlush(examineImplementPlan);

        ProductExamineYearlyPlan productExamineYearlyPlan = fabosJsonDao.getById(ProductExamineYearlyPlan.class,examineImplementPlan.getProductExamineYearlyPlan().getId());
        if (productExamineYearlyPlan != null) {
            productExamineYearlyPlan.setActualDate(new Date());
            fabosJsonDao.mergeAndFlush(productExamineYearlyPlan);
        }
        return "alert";
    }

    @Override
    public ExamineImplementPlanResultMTO fabosJsonFormValue(List<ExamineImplementPlan> data, ExamineImplementPlanResultMTO fabosJsonForm, String[] param) {
        ExamineImplementPlan examineImplementPlan = data.get(0);
        fabosJsonForm.setProductExamineYearlyPlan(examineImplementPlan.getProductExamineYearlyPlan().getPlanName());
        fabosJsonForm.setProductExamineImplementPlan(examineImplementPlan.getGeneralCode());
        fabosJsonForm.setProductName(examineImplementPlan.getMaterialName());
        fabosJsonForm.setSampleQuantity(examineImplementPlan.getSampleQuantity());
        fabosJsonForm.setKqzScore(examineImplementPlan.getKQZScore());

        return fabosJsonForm;
    }
}
