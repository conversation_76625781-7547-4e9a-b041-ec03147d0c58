package cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.handler;

import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.enumration.ProgressEnum;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.model.CorrectPreventMeasure;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.mto.CorrectPreventMeasureCauseAnalyMTO;
import cec.jiutian.bc.urm.domain.user.entity.User;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/30
 * @description TODO
 */
@Component
public class CorrectPreventMeasureCauseAnalyHandler implements OperationHandler<CorrectPreventMeasure, CorrectPreventMeasureCauseAnalyMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<CorrectPreventMeasure> data, CorrectPreventMeasureCauseAnalyMTO modelObject, String[] param) {
        /*if (modelObject != null) {
            CorrectPreventMeasure model = data.get(0);
            assembleMTOData(modelObject);
            BeanUtil.copyProperties(modelObject, model, "createBy", "createTime");
            model.setCorrectiveState(ProgressEnum.Enum.RUN_SUBMIT.name());
            fabosJsonDao.mergeAndFlush(model);
        }*/
        return "alert(操作成功)";
    }

    @Override
    public CorrectPreventMeasureCauseAnalyMTO fabosJsonFormValue(List<CorrectPreventMeasure> data, CorrectPreventMeasureCauseAnalyMTO fabosJsonForm, String[] param) {
        CorrectPreventMeasure model = data.get(0);
        BeanUtil.copyProperties(model, fabosJsonForm);
        return fabosJsonForm;
    }

    private void assembleMTOData(CorrectPreventMeasureCauseAnalyMTO mto) {
        User user = fabosJsonDao.getById(User.class, UserContext.getUserId());
        mto.setAnalyticalUser(user);
        mto.setAnalyticalTime(new Date());
        mto.setAnalyticalState(ProgressEnum.Enum.RUN_SUBMIT.name());
    }
}
