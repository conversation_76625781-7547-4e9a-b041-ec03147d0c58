package cec.jiutian.bc.supplierManagement.domian.auditImplementPlan.handler;

import cec.jiutian.bc.supplierManagement.domian.auditImplementPlan.model.SupplierAuditImplementPlan;
import cec.jiutian.bc.supplierManagement.domian.auditImplementPlan.model.SupplierAuditImplementPlanDetail;
import cec.jiutian.bc.supplierManagement.domian.supplierAuditTemplate.model.SupplierAuditTemplate;
import cec.jiutian.bc.supplierManagement.domian.supplierAuditTemplate.model.SupplierAuditTemplateDetail;
import cec.jiutian.common.util.BeanUtils;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class SplAuditImplementPlanDetailChangeHandler implements DependFiled.DynamicHandler<SupplierAuditImplementPlan> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public Map<String, Object> handle(SupplierAuditImplementPlan entity) {
        Map<String, Object> result = new HashMap<>();
        result.put("details", Collections.emptyList());
        if (null != entity.getAuditTemplate()) {
            SupplierAuditTemplate template = fabosJsonDao.findById(SupplierAuditTemplate.class, entity.getAuditTemplate().getId());
            List<SupplierAuditImplementPlanDetail> details = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(template.getDetails())) {
                for (SupplierAuditTemplateDetail templateDetail : template.getDetails()) {
                    SupplierAuditImplementPlanDetail detail = new SupplierAuditImplementPlanDetail();
                    BeanUtils.copyProperties(templateDetail, detail);
                    detail.setId(null);
                    details.add(detail);
                }
                result.put("details", details);
            }
        }
        return result;
    }
}
