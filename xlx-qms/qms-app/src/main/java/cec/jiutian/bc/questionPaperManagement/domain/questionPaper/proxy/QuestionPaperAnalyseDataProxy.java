package cec.jiutian.bc.questionPaperManagement.domain.questionPaper.proxy;

import cec.jiutian.bc.questionPaperManagement.domain.questionPaper.model.QuestionPaperAnalyse;
import cec.jiutian.bc.questionPaperManagement.enums.QuestionPaperStateEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;

public class QuestionPaperAnalyseDataProxy implements DataProxy<QuestionPaperAnalyse> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public void beforeUpdate(QuestionPaperAnalyse entity) {
        entity.setCurrentState(QuestionPaperStateEnum.Enum.Execute.name());
    }

}
