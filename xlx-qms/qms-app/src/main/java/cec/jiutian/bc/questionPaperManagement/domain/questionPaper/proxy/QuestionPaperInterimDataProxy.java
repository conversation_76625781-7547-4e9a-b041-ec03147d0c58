package cec.jiutian.bc.questionPaperManagement.domain.questionPaper.proxy;

import cec.jiutian.bc.questionPaperManagement.domain.questionPaper.model.QuestionPaperInterim;
import cec.jiutian.bc.questionPaperManagement.enums.QuestionPaperMeasureProgressEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

@Component
public class QuestionPaperInterimDataProxy implements DataProxy<QuestionPaperInterim> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public void beforeUpdate(QuestionPaperInterim entity) {
        if (CollectionUtils.isNotEmpty(entity.getInterimMeasures())) {
            entity.setInterimFlag(true);
            entity.getInterimMeasures().forEach(m -> m.setProgress(QuestionPaperMeasureProgressEnum.Enum.WaitExecute.name()));
        }
    }

}
