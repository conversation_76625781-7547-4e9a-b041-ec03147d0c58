package cec.jiutian.bc.qualityExceptionManagement.enumeration.inspectionIssuesImproveAndTrace;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;
public class ProgressRUNEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum value : Enum.values()) {
            list.add(new VLModel(value.name(), value.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        //以下枚举来自 ProgressEnum
        //他们是同一个字段  只是在执行整改措施的时候  换了一个说法 叫做 已完成和未完成
        // 这里只是表示整改任务执行是否完成  。
        WAIT_RUN("未完成"),
        RUN_SUBMIT("已完成"),
        ;

        private final String value;

    }

}
