package cec.jiutian.bc.supplierManagement.domain.problemRectificationList.model;

import cec.jiutian.bc.supplierManagement.domain.problemRectificationList.enums.RemediesResultEnum;
import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Setter
@Getter
@Entity
@Table(name = "qms_supplier_problem_rectification")
@FabosJson(
        name = "整改结果",
        orderBy = "createTime desc"
)
public class RectificationResults extends BaseModel {

    @FabosJsonField(
            views = @View(title = "整改次数", index = 1),
            edit = @Edit(title = "整改次数",
                    notNull = true,
                    search = @Search(vague = true),
                    defaultVal = "1",
                    numberType = @NumberType(min = 1, max = 50, precision = 0),
                    inputType = @InputType(length = 2)
            )
    )
    @Column(length = 2)
    private Integer number;

    //完成时间
    @FabosJsonField(
            views = @View(title = "完成时间",type = ViewType.DATE_TIME, index = 2),
            edit = @Edit(title = "完成时间",
                    notNull = true,
                    search = @Search(vague = true),
                    type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @Column(length = 20)
    private Date completionTime;

    @FabosJsonField(
            views = @View(title = "整改结果", index = 3),
            edit = @Edit(title = "整改结果",
                    notNull = true,
                    inputType = @InputType(length = 30),
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(
                            fetchHandler = RemediesResultEnum.class
                    )
            )
    )
    @Column(length = 30)
    private String result;

    @FabosJsonField(
            views = @View(title = "整改结果佐证材料"),
            edit = @Edit(title = "佐证材料",
                    inputType = @InputType(length = 300),
                    type = EditType.ATTACHMENT,
                    tips = "最大支持3个100M文件",
                    attachmentType = @AttachmentType(
                            size = 100 * 1024,
                            maxLimit = 3,
                            type = AttachmentType.Type.BASE)
            )
    )
    @Column(length = 300)
    private String resultAttachment;
}
