package cec.jiutian.bc.productReturnInspection.domain.inspectionTask.handler;

import cec.jiutian.bc.basicData.domain.inspectionItemGroup.model.InspectionItemGroup;
import cec.jiutian.bc.basicData.domain.inspectionStandard.model.InspectionStandard;
import cec.jiutian.bc.basicData.enumeration.NamingRuleCodeEnum;
import cec.jiutian.bc.generalModeler.enumeration.YesOrNoEnum;
import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.generalModeler.util.StringUtils;
import cec.jiutian.bc.modeler.enumration.SampleTypeEnum;
import cec.jiutian.bc.modeler.enumration.TaskBusinessStateEnum;
import cec.jiutian.bc.productReturnInspection.domain.inspectionRequest.model.ProductReturnInspectionRequest;
import cec.jiutian.bc.productReturnInspection.domain.inspectionTask.model.ProductReturnInspectionTask;
import cec.jiutian.bc.productReturnInspection.domain.inspectionTask.model.ProductReturnInspectionTaskDetail;
import cec.jiutian.bc.productReturnInspection.domain.inspectionTask.model.ProductReturnQualityInspectionStandardDetail;
import cec.jiutian.bc.productReturnInspection.domain.samplingTask.model.ProductReturnSamplingTask;
import cec.jiutian.bc.productReturnInspection.domain.samplingTask.model.ProductReturnSamplingTaskDetail;
import cec.jiutian.bc.productReturnInspection.port.client.ProductInspectFeignClient;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/10
 * @description TODO
 */
@Component
public class ProductReturnPublishOperationHandler implements OperationHandler<ProductReturnInspectionTask, Void> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private NamingRuleService namingRuleService;

    @Resource
    private ProductInspectFeignClient productInspectFeignClient;

    @Override
    @Transactional
    public String exec(List<ProductReturnInspectionTask> data, Void modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            ProductReturnInspectionTask productReturnInspectionTask = data.get(0);
            if (CollectionUtils.isEmpty(productReturnInspectionTask.getProductReturnInspectionTaskDetailList())) {
                throw new FabosJsonApiErrorTip("未选择检验物资批次，无法发布，请确认");
            }else {
                Double inventoryAllQuantity = productReturnInspectionTask.getProductReturnInspectionTaskDetailList().stream().mapToDouble(ProductReturnInspectionTaskDetail::getRequestQuantity).sum();
                if (!Objects.equals(productReturnInspectionTask.getAllInspectionItemQuantity(), inventoryAllQuantity)) {
                    throw new FabosJsonApiErrorTip("检验物资批次总数量与取样数量不符，无法发布，请确认");
                }
            }
            productReturnInspectionTask.setCurrentState(OrderCurrentStateEnum.Enum.EXECUTE.name());

            ProductReturnInspectionRequest inspectionRequest = fabosJsonDao.findById(ProductReturnInspectionRequest.class,productReturnInspectionTask.getInspectionRequest().getId());
            if (inspectionRequest != null) {
                inspectionRequest.setBusinessState(TaskBusinessStateEnum.Enum.INSPECTING.name());
                fabosJsonDao.mergeAndFlush(inspectionRequest);
            }

            Map<String, Object> outboundRequest = createOutboundRequest(productReturnInspectionTask);
            productInspectFeignClient.createOutboundRequest(outboundRequest);

            fabosJsonDao.mergeAndFlush(productReturnInspectionTask);

            // 根据检验任务创建取样任务，一个检验组对应一个取样任务
            if (CollectionUtils.isNotEmpty(productReturnInspectionTask.getStandardDetailList())) {
                List<ProductReturnQualityInspectionStandardDetail> groupList = productReturnInspectionTask.getStandardDetailList().stream().filter(q -> StringUtils.isNotEmpty(q.getGroupId())).toList();
                if (CollectionUtils.isNotEmpty(groupList)) {
                    Map<String, List<ProductReturnQualityInspectionStandardDetail>> map = groupList.stream()
                            .collect(Collectors.groupingBy(ProductReturnQualityInspectionStandardDetail::getGroupId));
                    for (String key : map.keySet()) {
                        if (productReturnInspectionTask.getAllInspectionItemQuantity() != null && productReturnInspectionTask.getAllInspectionItemQuantity() > 0D
                                && hasNonZeroSampleSize(map.get(key))) {
                            createSamplingTask(false,key, map.get(key), productReturnInspectionTask);
                        }
                    }
                }

                List<ProductReturnQualityInspectionStandardDetail> nullGroupList = productReturnInspectionTask.getStandardDetailList().stream().filter(q -> StringUtils.isEmpty(q.getGroupId())).toList();
                if (CollectionUtils.isNotEmpty(nullGroupList)) {
                    nullGroupList.forEach(detail -> {
                        List<ProductReturnQualityInspectionStandardDetail> list = new ArrayList<>();
                        list.add(detail);
                        if (productReturnInspectionTask.getAllInspectionItemQuantity() != null && productReturnInspectionTask.getAllInspectionItemQuantity() > 0D) {
                            createSamplingTask(false,null, list, productReturnInspectionTask);
                        }
                    });
                }

                // 根据质检标准判断是否创建留样类型取样单
                InspectionStandard inspectionStandard = fabosJsonDao.getById(InspectionStandard.class, productReturnInspectionTask.getInspectionStandard().getId());
                if (YesOrNoEnum.Enum.Y.name().equals(inspectionStandard.getKeepSampleFlag())) {
                    createSamplingTask(true,null, null, productReturnInspectionTask);
                }
            }
        }
        return "alert(操作成功)";
    }

    private boolean hasNonZeroSampleSize(List<ProductReturnQualityInspectionStandardDetail> detailList) {
        if (detailList == null || detailList.isEmpty()) {
            return false;
        }
        double sum = 0.0;
        for (ProductReturnQualityInspectionStandardDetail
                detail : detailList) {
            if (detail != null) {
                sum += detail.getSampleSize() != null ? detail.getSampleSize() : 0.0;
            }
        }
        return Math.abs(sum) > 1e-10;
    }

    private void createSamplingTask(Boolean isKeepSample, String groupId, List<ProductReturnQualityInspectionStandardDetail> list, ProductReturnInspectionTask productReturnInspectionTask) {
        ProductReturnSamplingTask productReturnSamplingTask = new ProductReturnSamplingTask();
        productReturnSamplingTask.setIsSaveSample(isKeepSample);
        if (StringUtils.isNotEmpty(groupId)) {
            InspectionItemGroup inspectionItemGroup = fabosJsonDao.getById(InspectionItemGroup.class, groupId);
            productReturnSamplingTask.setInspectionItemGroupName(inspectionItemGroup.getGroupName());
            productReturnSamplingTask.setSamplePoint(inspectionItemGroup.getSamplePoint());
            productReturnSamplingTask.setSendPoint(inspectionItemGroup.getSendPointMTO().getName());
            productReturnSamplingTask.setUnit(inspectionItemGroup.getUsualUnit());
            productReturnSamplingTask.setPackageType(inspectionItemGroup.getPackageType());
        }

        productReturnSamplingTask.setSamplePlanDate(new Date());
        productReturnSamplingTask.setGeneralCode(String.valueOf(namingRuleService.getNameCode(NamingRuleCodeEnum.SamplingTask.name(), 1, null).get(0)));
        productReturnSamplingTask.setInspectionTaskCode(productReturnInspectionTask.getGeneralCode());
        productReturnSamplingTask.setIsUrgent(productReturnInspectionTask.getIsUrgent());
        productReturnSamplingTask.setInspectionType(SampleTypeEnum.Enum.REFUND_SAMPLING.name());
        productReturnSamplingTask.setBusinessState(TaskBusinessStateEnum.Enum.BE_SAMPLING.name());
        productReturnSamplingTask.setMaterialCode(productReturnInspectionTask.getMaterialCode());
        productReturnSamplingTask.setMaterialName(productReturnInspectionTask.getMaterialName());
        productReturnSamplingTask.setSupplierName(productReturnInspectionTask.getSupplierName());
        productReturnSamplingTask.setUnit(productReturnSamplingTask.getUnit());
        productReturnSamplingTask.setOriginLotId(productReturnInspectionTask.getOriginLotId());
        productReturnSamplingTask.setMaterialSpecification(productReturnSamplingTask.getMaterialSpecification());

        if (CollectionUtils.isNotEmpty(list)) {
            List<ProductReturnSamplingTaskDetail> detailList = new ArrayList<>();
            list.forEach(standardDetail -> {
                ProductReturnSamplingTaskDetail productReturnSamplingTaskDetail = new ProductReturnSamplingTaskDetail();
                productReturnSamplingTaskDetail.setProductReturnSamplingTask(productReturnSamplingTask);
                productReturnSamplingTaskDetail.setName(standardDetail.getName());
                productReturnSamplingTaskDetail.setSamplingPlan(standardDetail.getSamplingPlan());
                productReturnSamplingTaskDetail.setInspectionMethod(standardDetail.getInspectionMethod());
                productReturnSamplingTaskDetail.setItemId(standardDetail.getItemId());
                productReturnSamplingTaskDetail.setCode(standardDetail.getCode());
                productReturnSamplingTaskDetail.setSampleSize(standardDetail.getSampleSize());
                productReturnSamplingTask.setAllInspectionItemQuantity((productReturnSamplingTask.getAllInspectionItemQuantity() == null ? 0.0 : productReturnSamplingTask.getAllInspectionItemQuantity())
                        + standardDetail.getSampleSize());
                detailList.add(productReturnSamplingTaskDetail);
            });
            productReturnSamplingTask.setDetailList(detailList);
        }
        fabosJsonDao.mergeAndFlush(productReturnSamplingTask);
    }

    private Map<String, Object> createOutboundRequest(ProductReturnInspectionTask productReturnInspectionTask) {
        Map<String, Object> result = new HashMap<>();
        UserContext.CurrentUser currentUser = UserContext.get();
        if (productReturnInspectionTask != null && currentUser != null) {
            result.put("inspectionTaskNumber",productReturnInspectionTask.getGeneralCode());
            result.put("inspectionRequestNumber",productReturnInspectionTask.getInspectionRequest().getGeneralCode());
            result.put("inspectItemType",productReturnInspectionTask.getInspectionType());
            result.put("username",currentUser.getUserName());
            result.put("org_id",currentUser.getOrgId());
            result.put("applyDate",new Date());
            if (CollectionUtils.isNotEmpty(productReturnInspectionTask.getProductReturnInspectionTaskDetailList())) {
                List<Map<String, Object>> list = new ArrayList<>();
                productReturnInspectionTask.getProductReturnInspectionTaskDetailList().forEach(detail -> {
                    Map<String, Object> detailMap = new HashMap<>();
                    detailMap.put("inspectMaterialApplyDetailNumber",detail.getGeneralCode());
                    detailMap.put("materialName", productReturnInspectionTask.getMaterialName());
                    detailMap.put("materialCode", productReturnInspectionTask.getMaterialCode());
                    detailMap.put("measureUnit", detail.getMeasureUnit());
                    detailMap.put("originLotId", productReturnInspectionTask.getOriginLotId());
                    detailMap.put("serialLotId",detail.getLotSerialId());
                    detailMap.put("inventoryId",detail.getInventoryId());
                    detailMap.put("requestQuantity", detail.getRequestQuantity());
                    list.add(detailMap);
                    result.put("details",list);
                });
            }
        }
        return result;
    }
}
