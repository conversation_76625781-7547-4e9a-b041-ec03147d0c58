package cec.jiutian.bc.productExamineManagement.domain.sampleTask.handler;

import cec.jiutian.bc.basicData.enumeration.NamingRuleCodeEnum;
import cec.jiutian.bc.deliveryInspection.enumeration.InspectionResultEnum;
import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.generalModeler.util.StringUtils;
import cec.jiutian.bc.materialInspect.domain.publicInspectionTask.mto.UserForInsTaskMTO;
import cec.jiutian.bc.modeler.enumration.SampleTypeEnum;
import cec.jiutian.bc.modeler.enumration.TaskBusinessStateEnum;
import cec.jiutian.bc.modeler.enumration.UnqualifiedHandleWayEnum;
import cec.jiutian.bc.productExamineManagement.domain.sampleTask.model.ProductExamineSamplingTask;
import cec.jiutian.bc.productExamineManagement.domain.sampleTask.model.ProductExamineSamplingTaskDetail;
import cec.jiutian.bc.productExamineManagement.domain.sampleTask.model.ProductExamineSamplingTaskReceiveOpr;
import cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.model.AbnormalFeedbackHandling;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/12
 * @description TODO
 */
@Component
public class ProductExamineReceiveSampleOperationHandler implements OperationHandler<ProductExamineSamplingTask, ProductExamineSamplingTaskReceiveOpr> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private NamingRuleService namingRuleService;

    @Override
    public String exec(List<ProductExamineSamplingTask> data, ProductExamineSamplingTaskReceiveOpr modelObject, String[] param) {
        if (modelObject != null) {
            ProductExamineSamplingTask condition = new ProductExamineSamplingTask();
            condition.setGeneralCode(modelObject.getGeneralCode());
            condition.setInspectionType(SampleTypeEnum.Enum.REFUND_SAMPLING.name());
            ProductExamineSamplingTask productExamineSamplingTask = fabosJsonDao.selectOne(condition);
            if (productExamineSamplingTask == null) {
                throw new FabosJsonApiErrorTip("未查询到取样单，请确认");
            }
            modelObject.setId(productExamineSamplingTask.getId());
            if (!TaskBusinessStateEnum.Enum.SEND_SAMPLE.name().equals(productExamineSamplingTask.getBusinessState())) {
                throw new FabosJsonApiErrorTip("取样任务单状态有误");
            }

            productExamineSamplingTask.setReceiveSamplePersonId(modelObject.getReceiveSamplePersonId());
            productExamineSamplingTask.setReceiveSampleDate(modelObject.getReceiveSampleDate());
            productExamineSamplingTask.setReceiveSamplePerson(modelObject.getReceiveSamplePerson());
            if (InspectionResultEnum.Enum.QUALIFIED.name().equals(modelObject.getAppearanceInspect())) {
                productExamineSamplingTask.setBusinessState(TaskBusinessStateEnum.Enum.RECEIVED_SAMPLE.name());
            }else {
                if (UnqualifiedHandleWayEnum.Enum.rePackage.name().equals(modelObject.getUnqualifiedHandle())) {
                    productExamineSamplingTask.setBusinessState(TaskBusinessStateEnum.Enum.SAMPLING_FINISH.name());
                }else if (UnqualifiedHandleWayEnum.Enum.reSampling.name().equals(modelObject.getUnqualifiedHandle())) {
                    productExamineSamplingTask.setBusinessState(TaskBusinessStateEnum.Enum.EXCEPTION_STOP.name());
                    ProductExamineSamplingTask newSamplingTask = new ProductExamineSamplingTask();
                    BeanUtil.copyProperties(productExamineSamplingTask, newSamplingTask);
                    newSamplingTask.setId(null);
                    newSamplingTask.setDetailList(null);
                    newSamplingTask.setGeneralCode(namingRuleService.getNameCode(NamingRuleCodeEnum.InspectionTask.name(), 1, null).get(0));
                    List<ProductExamineSamplingTaskDetail> detailList = new ArrayList<>();
                    productExamineSamplingTask.getDetailList().forEach(detail -> {
                        ProductExamineSamplingTaskDetail newDetail = new ProductExamineSamplingTaskDetail();
                        BeanUtil.copyProperties(detail, newDetail);
                        newDetail.setId(null);
                        newDetail.setProductExamineSamplingTask(newSamplingTask);
                        detailList.add(newDetail);
                    });
                    newSamplingTask.setDetailList(detailList);
                    fabosJsonDao.mergeAndFlush(newSamplingTask);
                }else if (UnqualifiedHandleWayEnum.Enum.exception.name().equals(modelObject.getUnqualifiedHandle())) {
                    productExamineSamplingTask.setBusinessState(TaskBusinessStateEnum.Enum.SEND_SAMPLE.name());
                    if (StringUtils.isNotEmpty(modelObject.getAbnormalDescription())) {
                        AbnormalFeedbackHandling abnormalFeedbackHandling = new AbnormalFeedbackHandling();
                        abnormalFeedbackHandling.setAbnormalFeedbackHandlingFormNumber(namingRuleService.getNameCode(NamingRuleCodeEnum.ABNORMAL_FEEDBACK_HANDLING.name(), 1, null).get(0));
                        abnormalFeedbackHandling.setAnalyzeAssociatedDocumentNumber(modelObject.getGeneralCode());
                        abnormalFeedbackHandling.setAbnormalDescription(modelObject.getAbnormalDescription());
                        abnormalFeedbackHandling.setSubmissionTime(modelObject.getSubmissionTime());
                        abnormalFeedbackHandling.setDiscoveredPersonId(modelObject.getDiscoveredPersonId());
                        UserForInsTaskMTO userForInsTaskMTO = fabosJsonDao.getById(UserForInsTaskMTO.class, modelObject.getDiscoveredPersonId());
                        abnormalFeedbackHandling.setDiscoveredPersonName(userForInsTaskMTO == null ? null : userForInsTaskMTO.getName());
                        abnormalFeedbackHandling.setAbnormalAttachments(modelObject.getAbnormalAttachments());
                        fabosJsonDao.mergeAndFlush(abnormalFeedbackHandling);
                    }
                }
            }
            fabosJsonDao.mergeAndFlush(productExamineSamplingTask);
        }
        return "alert(操作成功)";
    }
}
