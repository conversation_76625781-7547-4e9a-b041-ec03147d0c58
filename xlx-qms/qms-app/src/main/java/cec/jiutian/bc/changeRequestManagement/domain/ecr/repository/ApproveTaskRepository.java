package cec.jiutian.bc.changeRequestManagement.domain.ecr.repository;

import cec.jiutian.bc.changeRequestManagement.domain.ecr.model.ApproveTask;
import jakarta.persistence.LockModeType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ApproveTaskRepository extends JpaRepository<ApproveTask, String> {

    List<ApproveTask> findByBusinessKeyOrderByCreateDate(String ecrId);

    @Query("select max(t.turn) from ApproveTask t where t.businessKey = :ecrId and t.code = :code")
    Integer findMaxTurnByBusinessKeyAndCode(@Param("ecrId")String ecrId, @Param("code")String code);

    @Lock(LockModeType.PESSIMISTIC_WRITE)
    @Query("select t from ApproveTask t where t.businessKey = :ecrId and t.code = :code and t.turn = (SELECT MAX(t2.turn) FROM ApproveTask t2 where t2.businessKey = :ecrId and t2.code = :code)")
    List<ApproveTask> findByBusinessKeyAndCodeMaxTurn(@Param("ecrId") String ecrId, @Param("code") String code);

    @Lock(LockModeType.PESSIMISTIC_WRITE)
    List<ApproveTask> findByBusinessKeyAndCode(String ecrId, String code);

    ApproveTask findFirstByBusinessKeyAndCode(String ecrId, String code);

    @Lock(LockModeType.PESSIMISTIC_WRITE)
    List<ApproveTask> findByBusinessKeyAndCodeAndStatus(String ecrId, String code, String status);

    ApproveTask findFirstByBusinessKeyAndCodeAndRoleOrderByCreateDateDesc(String ecrId, String code, String role);
}
