package cec.jiutian.bc.clientComplaintManagement.domain.problemImprovement.handler;

import cec.jiutian.bc.clientComplaintManagement.domain.problemImprovement.model.ProblemImprovement;
import cec.jiutian.bc.clientComplaintManagement.domain.problemImprovement.mto.ProblemImprovementDistributeMTO;
import cec.jiutian.bc.clientComplaintManagement.enumeration.ProblemImprovementStatusEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/28
 * @description TODO
 */
@Component
public class ProblemImprovementDistributeMTOHandler implements OperationHandler<ProblemImprovement, ProblemImprovementDistributeMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<ProblemImprovement> data, ProblemImprovementDistributeMTO modelObject, String[] param) {
        modelObject.setBusinessStatus(ProblemImprovementStatusEnum.Enum.TO_BE_EXECUTE.name());

        ProblemImprovement model = data.get(0);
        BeanUtil.copyProperties(modelObject, model, "createBy", "createTime");
        fabosJsonDao.mergeAndFlush(model);
        return "alert(操作成功)";
    }

    @Override
    public ProblemImprovementDistributeMTO fabosJsonFormValue(List<ProblemImprovement> data, ProblemImprovementDistributeMTO fabosJsonForm, String[] param) {
        ProblemImprovement model = data.get(0);
        BeanUtil.copyProperties(model, fabosJsonForm);
        return fabosJsonForm;
    }
}
