package cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.handler;

import cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.model.ArmPreventMeasure;
import cec.jiutian.bc.accidentReportManagement.enumeration.ArmMeasureStatusEnum;
import cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.model.AccidentReportTask;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/29
 * @description
 */
@Component
public class AccidentReportTaskPreventSubmitHandler implements OperationHandler<AccidentReportTask, Void> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Transactional
    @Override
    public String exec(List<AccidentReportTask> data, Void modelObject, String[] param) {
        AccidentReportTask model = data.get(0);
        List<ArmPreventMeasure> measureList = model.getArmPreventMeasureList();
        if (measureList == null || measureList.size() == 0) {
            model.setPreventMeasureStatus(ArmMeasureStatusEnum.Enum.TO_BE_VERIFIED.name());
        } else {
            model.setPreventMeasureStatus(ArmMeasureStatusEnum.Enum.SUBMITTED.name());
        }
        fabosJsonDao.update(model);
        return null;
    }
}
