package cec.jiutian.bc.clientComplaintManagement.domain.clientComplaintFeedback.handler;

import cec.jiutian.bc.basicData.enumeration.NamingRuleCodeEnum;
import cec.jiutian.bc.clientComplaintManagement.domain.clientComplaintFeedback.model.ClientComplaintFeedback;
import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/05/20
 * @description TODO
 */
@Component
public class ClientComplaintFeedbackDynamicHandler implements DependFiled.DynamicHandler<ClientComplaintFeedback> {

    @Resource
    private NamingRuleService namingRuleService;

    @Override
    public Map<String, Object> handle(ClientComplaintFeedback clientComplaintFeedback) {
        Map<String, Object> map = new HashMap<>();
        List<String> result = namingRuleService.getNameCode(NamingRuleCodeEnum.CLIENT_COMPLAINT_FEEDBACK.name(), 1, null);
        map.put("generalCode",result.get(0));
        return map;
    }
}
