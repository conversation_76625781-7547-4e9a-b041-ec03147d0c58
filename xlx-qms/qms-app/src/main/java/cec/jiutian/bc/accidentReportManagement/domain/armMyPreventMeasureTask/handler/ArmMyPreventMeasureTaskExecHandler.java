package cec.jiutian.bc.accidentReportManagement.domain.armMyPreventMeasureTask.handler;

import cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.model.ArmPreventMeasure;
import cec.jiutian.bc.accidentReportManagement.domain.armMyPreventMeasureTask.model.ArmMyPreventMeasureTask;
import cec.jiutian.bc.accidentReportManagement.domain.armMyPreventMeasureTask.model.ArmMyPreventMeasureTaskExec;
import cec.jiutian.bc.accidentReportManagement.domain.armMyPreventMeasureTask.model.ArmMyPreventMeasure;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Component
public class ArmMyPreventMeasureTaskExecHandler implements OperationHandler<ArmMyPreventMeasureTask, ArmMyPreventMeasureTaskExec> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<ArmMyPreventMeasureTask> data, ArmMyPreventMeasureTaskExec modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            modelObject.getArmMyPreventMeasuresList().forEach(d->{
                ArmPreventMeasure armPreventMeasure = fabosJsonDao.findById(ArmPreventMeasure.class, d.getId());
                armPreventMeasure.setProgress(d.getProgress());
                armPreventMeasure.setCompletionDate(d.getCompletionDate());
                armPreventMeasure.setCompleteSupportMaterial(d.getCompleteSupportMaterial());
                fabosJsonDao.mergeAndFlush(armPreventMeasure);
            });
        }
        return "msg.success('操作成功')";
    }

    @Override
    public ArmMyPreventMeasureTaskExec fabosJsonFormValue(List<ArmMyPreventMeasureTask> data, ArmMyPreventMeasureTaskExec fabosJsonForm, String[] param) {
        // data为空 直接返回
        if (CollectionUtils.isEmpty(data)) {
            return fabosJsonForm;
        }

        String userId = UserContext.getUserId();
        ArmMyPreventMeasureTask armMyPreventMeasureTask = data.get(0);
        BeanUtil.copyProperties(armMyPreventMeasureTask, fabosJsonForm);

        //如果list为空 直接返回
        if (CollectionUtils.isEmpty(armMyPreventMeasureTask.getArmPreventMeasureList())) {
            return fabosJsonForm;
        }

        List<ArmMyPreventMeasure> armMyPreventMeasuresList = new ArrayList<>();
        armMyPreventMeasureTask.getArmPreventMeasureList().forEach(d->{
            if (Objects.equals(d.getUserForInsTaskMTO().getId(), userId)) {
                ArmMyPreventMeasure myPrevent = new ArmMyPreventMeasure();
                BeanUtil.copyProperties(d, myPrevent);
                armMyPreventMeasuresList.add(myPrevent);
            }
        });

        fabosJsonForm.setArmMyPreventMeasuresList(armMyPreventMeasuresList);
        return fabosJsonForm;
    }
}
