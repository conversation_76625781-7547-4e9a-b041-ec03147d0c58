package cec.jiutian.bc.inventoryInspection.domain.inspectionTask.handler;

import cec.jiutian.bc.inventoryInspection.domain.inspectionTask.model.InventoryInspectionTask;
import cec.jiutian.bc.inventoryInspection.domain.inspectionTask.model.InventoryTerminationInspectionTask;
import cec.jiutian.bc.inventoryInspection.domain.samplingTask.model.InventorySamplingTask;
import cec.jiutian.bc.modeler.enumration.TaskBusinessStateEnum;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/15
 * @description TODO
 */
@Component
public class TerminateInvInsTaskHandler implements OperationHandler<InventoryInspectionTask, InventoryTerminationInspectionTask> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    @Transactional
    public String exec(List<InventoryInspectionTask> data, InventoryTerminationInspectionTask modelObject, String[] param) {
        if (modelObject != null) {
            InventoryInspectionTask inspectionTask = data.get(0);
            inspectionTask.setInspectionResult(modelObject.getInspectionResult());
            inspectionTask.setUnqualifiedLevel(modelObject.getUnqualifiedLevel());
            inspectionTask.setCurrentState(OrderCurrentStateEnum.Enum.END.name());

            InventorySamplingTask condition = new InventorySamplingTask();
            condition.setInspectionTaskCode(inspectionTask.getGeneralCode());
            List<InventorySamplingTask> samplingTaskList = fabosJsonDao.select(condition);
            if (CollectionUtils.isNotEmpty(samplingTaskList)) {
                samplingTaskList.forEach(samplingTask -> {
                    samplingTask.setBusinessState(TaskBusinessStateEnum.Enum.TERMINATION.name());
                    fabosJsonDao.mergeAndFlush(samplingTask);
                });
            }

            fabosJsonDao.mergeAndFlush(inspectionTask);
        }
        return "alert(操作成功)";
    }
}
