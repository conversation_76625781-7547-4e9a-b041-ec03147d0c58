package cec.jiutian.bc.quantityPreparationPlan.model;

import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.NumberType;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/5/21
 * @description TODO
 */
@FabosJson(
        name = "质量准备计划详情-检验项信息"
)
@Table(name = "pre_quantity_preparation_plan_detail_item")
@Entity
@Getter
public class QuantityPreparationDetailItem extends MetaModel {

    @FabosJsonField(
            views = @View(title = "检验项"),
            edit = @Edit(title = "检验项")
    )
    private String itemName;

    @FabosJsonField(
            views = @View(title = "工序"),
            edit = @Edit(title = "工序")
    )
    private String processOperationName;

    @FabosJsonField(
            views = @View(title = "任务数"),
            edit = @Edit(title = "任务数", notNull = true, numberType = @NumberType(min = 0))
    )
    private Integer quantity;
}
