package cec.jiutian.bc.reportPrintTemplateSuper;

import cec.jiutian.bc.basicData.enumeration.InspectionValueTypeEnum;
import cec.jiutian.bc.basicData.enumeration.PackageTypeEnum;
import cec.jiutian.bc.materialInspect.domain.publicInspectionTask.mto.SamplingPlanMTO;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "Ins_Item_And_Index")
@Getter
@FabosJson(
        name = "检验项指标"
)
@Setter
public class InsItemAndIndex extends MetaModel {
    @FabosJsonField(
            views = @View(title = "编号"),
            edit = @Edit(title = "编号",
                    readonly = @Readonly(add = true, edit = true),
                    notNull = true)
    )
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "检验项目名称"),
            edit = @Edit(title = "检验项目名称",
                    readonly = @Readonly(add = true, edit = true),
                    search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    private String name;

    @FabosJsonField(
            views = @View(title = "特性"),
            edit = @Edit(title = "特性",
                    readonly = @Readonly(add = true, edit = true),
                    search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    private String feature;

    @FabosJsonField(
            views = @View(title = "包装方式"),
            edit = @Edit(title = "包装方式",
                    readonly = @Readonly(add = true, edit = true),
                    type = EditType.CHOICE,search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = PackageTypeEnum.class))
    )
    private String packageType;

    @OneToOne(cascade = CascadeType.ALL)
    @JoinColumn(name = "plan_id")
    @FabosJsonField(
            views = @View(title = "抽样方案", column = "name", type = ViewType.TABLE_FORM),
            edit = @Edit(title = "抽样方案",
                    readonly = @Readonly(add = true, edit = true),
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name")
            )
    )
    private SamplingPlanMTO samplingPlan;

    @FabosJsonField(
            views = @View(title = "样本量"),
            edit = @Edit(title = "样本量",notNull = true,
                    readonly = @Readonly(add = true, edit = true),
                    numberType = @NumberType(min=0,max = 9999999,precision = 2))
    )
    private Double sampleSize;

    @FabosJsonField(
            views = @View(title = "取样点"),
            edit = @Edit(title = "取样点",
                    readonly = @Readonly(add = true, edit = true),
                    inputType = @InputType(length = 40))
    )
    private String samplingPoint;

    @FabosJsonField(
            views = @View(title = "送检点"),
            edit = @Edit(title = "送检点",
                    readonly = @Readonly(add = true, edit = true),
                    inputType = @InputType(length = 40))
    )
    private String sendInspectPoint;

    @FabosJsonField(
            views = @View(title = "备注"),
            edit = @Edit(title = "备注",
                    readonly = @Readonly(add = true, edit = true),
                    type = EditType.TEXTAREA)
    )
    private String remark;


    @FabosJsonField(
            views = @View(title = "检验项指标名称"),
            edit = @Edit(title = "检验项指标名称", search = @Search(vague = true),
                    readonly = @Readonly,
                    inputType = @InputType(length = 40))
    )
    private String indexName;

    @FabosJsonField(
            views = @View(title = "值类型"),
            edit = @Edit(title = "值类型", type = EditType.CHOICE, search = @Search(),
                    readonly = @Readonly,
                    choiceType = @ChoiceType(fetchHandler = InspectionValueTypeEnum.class))
    )
    private String inspectionValueType;

    @FabosJsonField(
            views = @View(title = "单位"),
            edit = @Edit(title = "单位",
                    readonly = @Readonly,
                    inputType = @InputType(length = 40))
    )
    private String unit;

    @FabosJsonField(
            views = @View(title = "标准值"),
            edit = @Edit(title = "标准值", notNull = true,
                    readonly = @Readonly
            )
    )
    private String standardValue;

    @FabosJsonField(
            views = @View(title = "上限值"),
            edit = @Edit(title = "上限值", notNull = true,
                    readonly = @Readonly,
                    numberType = @NumberType(min = 0, max = 9999999, precision = 2))
    )
    private Double UpperValue;

    @FabosJsonField(
            views = @View(title = "是否包含上限值"),
            edit = @Edit(title = "是否包含上限值", defaultVal = "true",
                    readonly = @Readonly
            )
    )
    private Boolean isContainUpper;

    @FabosJsonField(
            views = @View(title = "下限值"),
            edit = @Edit(title = "下限值", notNull = true,
                    readonly = @Readonly,
                    numberType = @NumberType(min = 0, max = 9999999, precision = 2))
    )
    private Double lowerValue;

    @FabosJsonField(
            views = @View(title = "是否包含下限值"),
            edit = @Edit(title = "是否包含下限值",
                    readonly = @Readonly, defaultVal = "true"
            )
    )
    private Boolean isContainLower;

    @FabosJsonField(
            views = @View(title = "检验结果值"),
            edit = @Edit(title = "检验结果值", notNull = true)
    )
    private String resultValue;

    @FabosJsonField(
            views = @View(title = "描述"),
            edit = @Edit(title = "描述",
                    readonly = @Readonly,
                    type = EditType.TEXTAREA)
    )
    private String description;
}
