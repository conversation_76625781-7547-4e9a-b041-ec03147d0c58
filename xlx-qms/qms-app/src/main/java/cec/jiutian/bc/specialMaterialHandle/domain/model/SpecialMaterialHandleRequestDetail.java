package cec.jiutian.bc.specialMaterialHandle.domain.model;

import cec.jiutian.bc.materialInspect.domain.publicInspectionTask.mto.UserForInsTaskMTO;
import cec.jiutian.bc.specialMaterialHandle.enumeration.HandleTypeEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.TabTableReferType;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025/5/22
 * @description TODO
 */
@FabosJson(
        name = "特殊物料处理申请物料清单"
)
@Table(name = "qms_special_material_handle_request_detail")
@Entity
@Getter
@Setter
public class SpecialMaterialHandleRequestDetail extends MetaModel {

    @FabosJsonField(
            views = @View(title = "来源主键id", show = false),
            edit = @Edit(title = "来源主键id", show = false)
    )
    private String inventoryId;

    @FabosJsonField(
            views = @View(title = "来源单号", show = false),
            edit = @Edit(title = "来源单号", show = false)
    )
    private String orderCode;

    @FabosJsonField(
            views = @View(title = "批次号"),
            edit = @Edit(title = "批次号")
    )
    private String lotSerialId;

    @FabosJsonField(
            views = @View(title = "物料编码"),
            edit = @Edit(title = "物料编码")
    )
    private String materialCode;

    @FabosJsonField(
            views = @View(title = "物料名称"),
            edit = @Edit(title = "物料名称")
    )
    private String materialName;

    @FabosJsonField(
            views = @View(title = "重量"),
            edit = @Edit(title = "重量",numberType = @NumberType(min = 0,precision = 2))
    )
    private Double weight;

    @FabosJsonField(
            views = @View(title = "单位"),
            edit = @Edit(title = "单位")
    )
    private String unit;

    @FabosJsonField(
            views = @View(title = "存放地点"),
            edit = @Edit(title = "存放地点")
    )
    private String storageLocation;

    @FabosJsonField(
            views = @View(title = "物料等级"),
            edit = @Edit(title = "物料等级")
    )
    private String materialLevel;

    @FabosJsonField(
            views = @View(title = "处理意见"),
            edit = @Edit(title = "处理意见",notNull = true,type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = HandleTypeEnum.class))
    )
    private String handleType;

    @FabosJsonField(
            views = @View(title = "责任人", column = "name"),
            edit = @Edit(title = "责任人",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType(type = TabTableReferType.SelectShowTypeMTM.LIST)
            )
    )
    @ManyToOne
    @JoinColumn(name = "person_responsible_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private UserForInsTaskMTO chargePerson;

    @FabosJsonField(
            views = @View(title = "备注"),
            edit = @Edit(title = "备注",
                    type = EditType.TEXTAREA)
    )
    private String remark;
}
