package cec.jiutian.bc.productExamineManagement.enumration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/13
 * @description TODO
 */
public class ImplementPlanHandleResultEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum data : Enum.values()) {
            list.add(new VLModel(data.name(), data.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        ReleaseSlip("放行"),
        Storage("封存"),
        Return("全检后返工并追溯其他产品"),
        Correct("纠正措施"),
        ;

        private final String value;

    }
}
