package cec.jiutian.bc.inventoryInspection.domain.samplingTask.handler;

import cec.jiutian.bc.inventoryInspection.domain.samplingTask.model.InventorySamplingTask;
import cec.jiutian.bc.inventoryInspection.domain.samplingTask.model.InventorySamplingTaskOpr;
import cec.jiutian.bc.modeler.enumration.TaskBusinessStateEnum;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/12
 * @description TODO
 */
@Component
public class InventorySendSampleOperationHandler implements OperationHandler<InventorySamplingTask, InventorySamplingTaskOpr> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<InventorySamplingTask> data, InventorySamplingTaskOpr modelObject, String[] param) {
        if (modelObject != null) {
            InventorySamplingTask condition = new InventorySamplingTask();
            condition.setGeneralCode(modelObject.getGeneralCode());
            InventorySamplingTask inventorySamplingTask = fabosJsonDao.selectOne(condition);
            if (inventorySamplingTask == null) {
                throw new FabosJsonApiErrorTip("未查询到取样单，请确认");
            }
            if (!TaskBusinessStateEnum.Enum.SAMPLING_FINISH.name().equals(inventorySamplingTask.getBusinessState())) {
                throw new FabosJsonApiErrorTip("取样任务单状态有误");
            }
            inventorySamplingTask.setBusinessState(TaskBusinessStateEnum.Enum.SEND_SAMPLE.name());
            fabosJsonDao.mergeAndFlush(inventorySamplingTask);
        }
        return "alert(操作成功)";
    }
}
