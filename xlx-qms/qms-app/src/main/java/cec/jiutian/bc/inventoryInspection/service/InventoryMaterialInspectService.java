package cec.jiutian.bc.inventoryInspection.service;

import cec.jiutian.bc.inventoryInspection.domain.inspectionRequest.model.InventoryInspectionRequest;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

@Service
@Component
public class InventoryMaterialInspectService {

    @Resource
    private FabosJsonDao fabosJsonDao;

    /**
     * 自动创建检验通知单
     * 1.WMS系统检验通知发布时调用
     */
    public InventoryInspectionRequest createInspectionRequest(InventoryInspectionRequest inventoryInspectionRequest) {
        if (null != inventoryInspectionRequest) {
            inventoryInspectionRequest.setCurrentState(OrderCurrentStateEnum.Enum.EDIT.name());
            fabosJsonDao.insert(inventoryInspectionRequest);
        }
        return inventoryInspectionRequest;
    }

}
