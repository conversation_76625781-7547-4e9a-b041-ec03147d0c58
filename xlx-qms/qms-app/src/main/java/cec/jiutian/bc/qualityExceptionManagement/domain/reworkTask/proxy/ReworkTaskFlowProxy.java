package cec.jiutian.bc.qualityExceptionManagement.domain.reworkTask.proxy;

import cec.jiutian.bc.flow.enums.ExamineStatusEnum;
import cec.jiutian.bc.qualityExceptionManagement.domain.reworkTask.model.ReworkTask;
import cec.jiutian.bc.qualityExceptionManagement.enums.ReworkTaskStatusEnum;
import cec.jiutian.view.fun.FlowProxy;
import org.springframework.stereotype.Component;

@Component
public class ReworkTaskFlowProxy extends FlowProxy {
    @Override
    public void onEvent(Object event, Object entity) {
        if (entity instanceof ReworkTask reworkTask) {
            if (reworkTask.getExamineStatus().equals(ExamineStatusEnum.AUDITED.getCode())) {
                // todo 预留
                reworkTask.setStatus(ReworkTaskStatusEnum.Enum.EXECUTING.name());
            }
            if (reworkTask.getExamineStatus().equals(ExamineStatusEnum.REJECTED.getCode())) {
                reworkTask.setStatus(ReworkTaskStatusEnum.Enum.CREATE.name());
            }
        }
    }

    @Override
    public void afterSubmit(Object entity) {
        if (entity instanceof ReworkTask reworkTask) {
            reworkTask.setStatus(ReworkTaskStatusEnum.Enum.EXECUTING.name());
        }
    }
}
