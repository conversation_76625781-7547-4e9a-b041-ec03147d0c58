package cec.jiutian.bc.clientReviewManagement.domain.customerReviewTask.model;

import cec.jiutian.bc.clientComplaintManagement.enumeration.ReviewProblemImproveStatusEnum;
import cec.jiutian.bc.mto.OrgMTO;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@FabosJson(
        name = "执行详情"
)
@Table(name = "qms_client_review_problem_improvement")
@Entity
@Getter
@Setter
public class CustomerReviewTaskDetailProblemExec extends MetaModel {

    @FabosJsonField(
            views = @View(title = "评审任务单号",show = false),
            edit = @Edit(title = "评审任务单号",show = false)
    )
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "问题描述"),
            edit = @Edit(title = "问题描述",
                    notNull = true,
                    search = @Search(vague = true))

    )
    private String problemDescription;

    @FabosJsonField(
            views = @View(title = "计划完成日期", type = ViewType.DATE_TIME),
            edit = @Edit(
                    title = "计划完成日期",
                    notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @Column(name = "planned_completion_date")
    private Date plannedCompletionDate;

    @FabosJsonField(
            views = @View(title = "主责部门", column = "name"),
            edit = @Edit(title = "主责部门",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType(type = TabTableReferType.SelectShowTypeMTM.LIST)
            )
    )
    @ManyToOne
    @JoinColumn(name = "primary_response_department_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private OrgMTO primaryRespOrgMTO;

    @FabosJsonField(
            views = @View(title = "次责部门", column = "name"),
            edit = @Edit(title = "次责部门",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType(type = TabTableReferType.SelectShowTypeMTM.LIST)
            )
    )
    @ManyToOne
    @JoinColumn(name = "secondary_response_department_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private OrgMTO secondaryRespOrgMTO;

    @FabosJsonField(
            views = @View(title = "完成状态"),
            edit = @Edit(title = "完成状态",
                    show = false,
                    defaultVal = "DISTRIBUTING",
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ReviewProblemImproveStatusEnum.class),
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(passive = true)
    )
    private String businessStatus;
}
