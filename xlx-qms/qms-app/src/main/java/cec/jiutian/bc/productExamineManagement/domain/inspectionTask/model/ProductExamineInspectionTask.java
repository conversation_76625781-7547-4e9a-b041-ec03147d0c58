package cec.jiutian.bc.productExamineManagement.domain.inspectionTask.model;

import cec.jiutian.bc.basicData.domain.inspectionStandard.model.InspectionStandard;
import cec.jiutian.bc.basicData.enumeration.ApplyRangeEnum;
import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleBaseModel;
import cec.jiutian.bc.modeler.enumration.NamingRuleCodeEnum;
import cec.jiutian.bc.mto.FactoryArea;
import cec.jiutian.bc.mto.InventoryMTO;
import cec.jiutian.bc.mto.SpecificationManageMTO;
import cec.jiutian.bc.productExamineManagement.domain.examineImplementPlan.model.ExamineImplementPlan;
import cec.jiutian.bc.productExamineManagement.domain.inspectionTask.handler.ProductExamineInspectionStandardDetailDynamicHandler;
import cec.jiutian.bc.productExamineManagement.domain.inspectionTask.handler.ProductExamineInspectionTaskResultOprHandler;
import cec.jiutian.bc.productExamineManagement.domain.inspectionTask.handler.ProductExaminePublishOperationHandler;
import cec.jiutian.bc.productExamineManagement.domain.inspectionTask.proxy.ProductExamineInspectionTaskProxy;
import cec.jiutian.bc.productExamineManagement.domain.mto.ProductExamineInspectionTaskResultMTO;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.*;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.RowBaseOperation;
import cec.jiutian.view.type.RowOperation;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/5
 * @description TODO
 */
@FabosJson(
        name = "检验任务",
        orderBy = "ProductExamineInspectionTask.createTime desc",
        dataProxy = ProductExamineInspectionTaskProxy.class,
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "currentState != 'EDIT'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "currentState != 'EDIT'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
        },
        rowOperation = {
                @RowOperation(
                        title = "发布",
                        code = "ProductExamineInspectionTask@PUBLISH",
                        operationHandler = ProductExaminePublishOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "是否确定操作？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "ProductExamineInspectionTask@PUBLISH"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "currentState != 'EDIT'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "结果审核",
                        code = "ProductExamineInspectionTask@RESULTEXMINE",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        popupType = RowOperation.PopupType.FORM,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        operationHandler = ProductExamineInspectionTaskResultOprHandler.class,
                        fabosJsonClass = ProductExamineInspectionTaskResultMTO.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "ProductExamineInspectionTask@RESULTEXMINE"
                        ),
                        ifExpr = "selectedItems[0].currentState != 'EXECUTE'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                )
        }
)
@Table(name = "qms_pem_product_examine_inspection_task",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Getter
@Setter
@TemplateType(type = "multiTable")
public class ProductExamineInspectionTask extends NamingRuleBaseModel {

    @Override
    public String getNamingCode() {
        return NamingRuleCodeEnum.ProductExamineInspectionTask.name();
    }

    @FabosJsonField(
            views = @View(title = "产品审核实施计划", column = "generalCode"),
            edit = @Edit(title = "产品审核实施计划",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType(label = "generalCode")
            )
    )
    @ManyToOne
    @JoinColumn(name = "examine_implement_plan_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private ExamineImplementPlan examineImplementPlan;

    @FabosJsonField(
            views = @View(title = "检验方案", column = "generalCode"),
            edit = @Edit(title = "检验方案",
                    allowAddMultipleRows = false,
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    filter = @Filter(value = "InspectionStandard.type = 'Other' and InspectionStandard.status = 'Effective'"),
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode")
            )
    )
    @ManyToOne
    @JoinColumn(name = "inspection_standard_id",foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private InspectionStandard inspectionStandard;

    @FabosJsonField(
            views = @View(title = "检验类型"),
            edit = @Edit(title = "检验类型",readonly = @Readonly, type = EditType.CHOICE,search = @Search(vague = true),
                    defaultVal = "ProductExamineInspect",choiceType = @ChoiceType(fetchHandler = ApplyRangeEnum.class)),
            dynamicField = @DynamicField(passive = true)
    )
    private String inspectionType;

    @FabosJsonField(
            views = @View(title = "取样产品", column = "name"),
            edit = @Edit(title = "取样产品", notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    filter = @Filter(value = "validateFlag = 'Y' and specificationCode like '01%'")
            )
    )
    @ManyToOne
    @JoinColumn(name = "specification_manage_id",foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private SpecificationManageMTO specificationManageMTO;

    @FabosJsonField(
            views = @View(title = "样本量"),
            edit = @Edit(title = "样本量",notNull = true,numberType = @NumberType(min = 0,precision = 2))
    )
    private Double sampleQuantity;

    @FabosJsonField(
            views = @View(title = "取样批次",
                    type = ViewType.TABLE_VIEW,
                    column = "lotSerialId"
            ),
            edit = @Edit(title = "取样批次",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    allowAddMultipleRows = false,
                    queryCondition = "{\"materialName\":\"${specificationManageMTO.name}\",\"stockType\":\"PRODUCT\"}",
                    referenceTableType = @ReferenceTableType(id = "id", label = "lotSerialId")
            )
    )
    @ManyToOne
    @JoinColumn(name = "inventory_id",foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private InventoryMTO inventoryMTO;

    @ManyToOne
    @JoinColumn(name = "workshop_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @FabosJsonField(
            views = @View(title = "车间", column = "factoryAreaName", type = ViewType.TABLE_FORM),
            edit = @Edit(title = "车间",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    notNull = true,
                    filter = @Filter(value = "factoryAreaTypeCode = '02'"),
                    referenceTableType = @ReferenceTableType(id = "id", label = "factoryAreaName")
            )
    )
    private FactoryArea workshop;

    @ManyToOne
    @JoinColumn(name = "production_line_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @FabosJsonField(
            views = @View(title = "产线", column = "factoryAreaName", type = ViewType.TABLE_FORM),
            edit = @Edit(title = "产线",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    notNull = true,
                    filter = @Filter(value = "factoryAreaTypeCode = '03'"),
                    referenceTableType = @ReferenceTableType(id = "id", label = "factoryAreaName")
            )
    )
    private FactoryArea productionLine;

    @FabosJsonField(
            views = @View(title = "当前状态"),
            edit = @Edit(title = "当前状态",
                    readonly = @Readonly(),
                    defaultVal = "EDIT", type = EditType.CHOICE, search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = OrderCurrentStateEnum.class)),
            dynamicField = @DynamicField(passive = true)
    )
    private String currentState;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = false)
    @FabosJsonField(
            views = @View(title = "检验项目明细", type = ViewType.TABLE_VIEW, index = 8),
            edit = @Edit(title = "检验项目明细", type = EditType.TAB_REFERENCE_GENERATE),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "inspectionStandard", dynamicHandler = ProductExamineInspectionStandardDetailDynamicHandler.class)),
            referenceGenerateType = @ReferenceGenerateType(editable = {"inspectionResult","unQualifiedLevel"})
    )
    @JoinColumn(name = "inspection_task_id")
    private List<ProductExamineInspectionItemDetail> standardDetailList;
}
