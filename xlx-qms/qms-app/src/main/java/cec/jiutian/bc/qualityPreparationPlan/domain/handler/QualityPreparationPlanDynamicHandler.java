package cec.jiutian.bc.qualityPreparationPlan.domain.handler;

import cec.jiutian.bc.qualityPreparationPlan.domain.model.QualityPreparationDetailItem;
import cec.jiutian.bc.qualityPreparationPlan.domain.model.QualityPreparationPlan;
import cec.jiutian.bc.qualityPreparationPlan.domain.model.QualityPreparationPlanDetail;
import cec.jiutian.bc.qualityPreparationPlan.pojo.ItemDTO;
import cec.jiutian.bc.qualityPreparationPlan.pojo.ScheduleDTO;
import cec.jiutian.bc.qualityPreparationPlan.service.ItemService;
import cec.jiutian.bc.qualityPreparationPlan.service.ScheduleService;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.ZoneId;
import java.util.*;

@Slf4j
@Component
public class QualityPreparationPlanDynamicHandler implements DependFiled.DynamicHandler<QualityPreparationPlan> {

    @Resource
    private ScheduleService scheduleService;

    @Resource
    private ItemService itemService;

    @Override
    public Map<String, Object> handle(QualityPreparationPlan qualityPreparationPlan) {
        HashMap<String, Object> map = new HashMap<>();

        if (Objects.nonNull(qualityPreparationPlan.getLineId())
                &&Objects.nonNull(qualityPreparationPlan.getPlanYearAndMonth())) {

            YearMonth ym = YearMonth.parse(qualityPreparationPlan.getPlanYearAndMonth());
            List<ScheduleDTO> scheduleList = scheduleService.getScheduleByLineAndTime(
                    Long.valueOf(qualityPreparationPlan.getLineId()), ym.getYear(), ym.getMonthValue());

            List<ItemDTO> itemList = itemService.getItemByLineId(Long.valueOf(qualityPreparationPlan.getLineId()));

            List<QualityPreparationPlanDetail> qualityPreparationPlanDetailList = new ArrayList<>();
            for (ScheduleDTO scheduleDTO : scheduleList) {
                //这里要判断是否同一天的工序  是-则加到list  否-新加项
                QualityPreparationPlanDetail qualityPlan = findByDate(qualityPreparationPlanDetailList, scheduleDTO.getScheduleDate());
                boolean isNullFlag = false;
                if (Objects.isNull(qualityPlan)) {
                    isNullFlag = true;
                    qualityPlan = new QualityPreparationPlanDetail();
                    qualityPlan.setDate(scheduleDTO.getScheduleDate());
                }

                // 检验项明细
                List<QualityPreparationDetailItem> detailItemList = isNullFlag ? new ArrayList<>() : qualityPlan.getQualityPreparationDetailItemList();
                boolean isMatchFlag = false;
                for (ItemDTO item : itemList) {
                    // 工序不相等 跳过
                    if (!Objects.equals(item.getProcessCode(), scheduleDTO.getProcessCode())) {
                        continue;
                    }

                    //匹配上
                    isMatchFlag = true;

                    QualityPreparationDetailItem detailItem = new QualityPreparationDetailItem();
                    detailItem.setItemName(item.getItemName());
                    detailItem.setProcessCode(scheduleDTO.getProcessCode());
                    detailItem.setProcessName(scheduleDTO.getProcessName());
                    //计算 检验任务数
                    int inspectNum = scheduleDTO.getBatchNum().divide(item.getInspectFrequency(), 0, RoundingMode.UP).intValue();
                    detailItem.setInspectQuantity(inspectNum);
                    detailItemList.add(detailItem);
                }
                if (isNullFlag) {
                    qualityPlan.setQualityPreparationDetailItemList(detailItemList);
                }
                //检验任务总数
                int inspectSum = detailItemList.stream().mapToInt(d -> Objects.nonNull(d.getInspectQuantity()) ? d.getInspectQuantity() : 0).sum();
                qualityPlan.setTotalDetectionTasks(inspectSum);
                // 检验项明细都没匹配上 则跳到下一个
                if (!isMatchFlag) {
                    continue;
                }

                if (isNullFlag) {
                    qualityPreparationPlanDetailList.add(qualityPlan);
                }
            }
            map.put("qualityPreparationPlanDetailList", qualityPreparationPlanDetailList);
        }
        return map;
    }

    private QualityPreparationPlanDetail findByDate(List<QualityPreparationPlanDetail> qualityPreparationPlanDetailList, Date scheduleDate) {
        return qualityPreparationPlanDetailList.stream()
                .filter(detail -> Objects.equals(detail.getDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate(), scheduleDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate()))
                .findFirst()
                .orElse(null);
    }
}
