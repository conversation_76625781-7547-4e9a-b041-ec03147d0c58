package cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.issueList.handler;

import cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.issueList.model.IssueList;
import cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.issueList.model.IssueListCorrection;
import cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.issueList.model.IssueListVerify;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component
public class IssueListVerifyHandler implements OperationHandler<IssueList, IssueListVerify> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<IssueList> data, IssueListVerify modelObject, String[] param) {
        if (CollectionUtils.isEmpty(data)) {
            return "msg.success('操作成功')";
        }
        IssueList issueList = data.get(0);
        if (CollectionUtils.isNotEmpty(modelObject.getIssueListCorrections())) {
            modelObject.getIssueListCorrections().forEach(d->{
                IssueListCorrection issueListCorrection = fabosJsonDao.findById(IssueListCorrection.class, d.getId());
                issueListCorrection.setVerificationResult(d.getVerificationResult());
                issueListCorrection.setVerificationDate(d.getVerificationDate());
                issueListCorrection.setVerificationEvidence(d.getVerificationEvidence());
                fabosJsonDao.mergeAndFlush(issueListCorrection);
            });
        }

        return "msg.success('操作成功')";
    }

    @Override
    public IssueListVerify fabosJsonFormValue(List<IssueList> data, IssueListVerify fabosJsonForm, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            IssueList issueList = data.get(0);
            List<IssueListCorrection> issueListCorrections = new ArrayList<>();
            issueList.getIssueListCorrections().forEach(d->{
                IssueListCorrection issueListCorrection = new IssueListCorrection();
                BeanUtil.copyProperties(d,issueListCorrection);
                issueListCorrections.add(issueListCorrection);
            });
            fabosJsonForm.setIssueListCorrections(issueListCorrections);
        }

        return fabosJsonForm;
    }
}
