package cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.issueList.dto;

import cec.jiutian.bc.qualityExceptionManagement.enumeration.inspectionIssuesImproveAndTrace.InsResultEnum;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.QueryModel;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

@FabosJson(name = "现场巡检任务-用于创建问题清单",
        power = @Power(add = false, delete = false)
)
@Data
@QueryModel(hql = "select " +
        "new map (d.id as id," +
        " t.name as name," +
        " t.generalCode as generalCode," +
        " t.description as description," +
        " t.standardName as inspectionStandard," +
        " d.detailName as inspectionProject," +
        " d.content as content," +
        " d.result as insResult)"+
        " from OnSiteInspectionTask t join t.standardItems d"
)
public class OnSiteInspectionTaskDTO {

    @FabosJsonField(
            views = @View(title = "id",show = false),
            edit = @Edit(title = "id"))
    @Id
    @Column(name = "id")
    private String id;

    @FabosJsonField(
            views = @View(title = "编码"),
            edit = @Edit(title = "编码", search = @Search(vague = true),readonly = @Readonly)
    )
    @Column(name = "general_code")
    private String generalCode;
    // 任务名称
    @FabosJsonField(
            views = @View(title = "任务名称"),
            edit = @Edit(title = "任务名称", search = @Search(vague = true),readonly = @Readonly)
    )
    @Column(name = "name")
    private String name;

    // 任务描述
    @FabosJsonField(
            views = @View(title = "任务描述", toolTip = true),
            edit = @Edit(title = "任务描述",readonly = @Readonly)
    )
    @Column(name = "description")
    private String description;

    @FabosJsonField(
            views = @View(title = "巡检标准"),
            edit = @Edit(title = "巡检标准", readonly = @Readonly)
    )
    @Column(name = "inspection_standard")
    private String inspectionStandard;
    // 巡检项 的名称
    @FabosJsonField(
            views = @View(title = "巡检项目"),
            edit = @Edit(title = "巡检项目",readonly = @Readonly)
    )
    @Column(name = "inspection_project")
    private String inspectionProject;

    @FabosJsonField(
            views = @View(title = "巡检内容", toolTip = true),
            edit = @Edit(title = "巡检内容", search = @Search(vague = true), readonly = @Readonly)
    )
    @Column(name = "content")
    private String content;

    @FabosJsonField(
            views = @View(title = "巡检结果", toolTip = true),
            edit = @Edit(title = "巡检结果",readonly = @Readonly,type= EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler =  InsResultEnum.class))
    )
    @Column(name = "ins_result")
    private String insResult;
}
