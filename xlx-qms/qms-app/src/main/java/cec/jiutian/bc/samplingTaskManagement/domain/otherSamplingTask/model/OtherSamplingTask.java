package cec.jiutian.bc.samplingTaskManagement.domain.otherSamplingTask.model;

import cec.jiutian.bc.modeler.enumration.SampleTypeEnum;
import cec.jiutian.bc.modeler.enumration.TaskBusinessStateEnum;
import cec.jiutian.bc.samplingTaskManagement.domain.otherSamplingTask.handler.GetOtherSampleOperationHandler;
import cec.jiutian.bc.samplingTaskManagement.domain.otherSamplingTask.handler.OtherSamplingCodeGenerateDynamicHandler;
import cec.jiutian.bc.samplingTaskManagement.domain.otherSamplingTask.handler.ReceiveOtherSampleOperationHandler;
import cec.jiutian.bc.samplingTaskManagement.domain.otherSamplingTask.handler.SendOtherSampleOperationHandler;
import cec.jiutian.bc.samplingTaskManagement.domain.otherSamplingTask.proxy.OtherSamplingTaskProxy;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DateType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowBaseOperation;
import cec.jiutian.view.type.RowOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/12
 * @description TODO
 */
@FabosJson(
        name = "其他取样任务",
        orderBy = "OtherSamplingTask.createTime desc",
        dataProxy = OtherSamplingTaskProxy.class,
        filter = @Filter(value = "inspectionType in ('QC_SAMPLING','QC_INVENTORY_SAMPLING','QC_KEEP_SAMPLING','BENCH_SAMPLING','BENCH_INVENTORY_SAMPLING','KEEP_SAMPLING','EXT_SAMPLING','SAMP_SHIPMENT','MSA_SAMPLING','MSA_INVENTORY_SAMPLING','MSA_KEEP_SAMPLING')" ),
        power = @Power(add = false),
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "businessState != 'BE_SAMPLING'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "businessState != 'BE_SAMPLING'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                )
        },
        rowOperation = {
                @RowOperation(
                        title = "取样",
                        code = "OtherSamplingTask@GET",
                        operationHandler = GetOtherSampleOperationHandler.class,
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        popupType = RowOperation.PopupType.FORM,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        fabosJsonClass = OtherSamplingTaskOpr.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "OtherSamplingTask@GET"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "selectedItems[0].businessState != 'BE_SAMPLING'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "送样",
                        code = "OtherSamplingTask@SEND",
                        operationHandler = SendOtherSampleOperationHandler.class,
                        mode = RowOperation.Mode.BUTTON,
                        type = RowOperation.Type.POPUP,
                        popupType = RowOperation.PopupType.FORM,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        fabosJsonClass = OtherSamplingTaskOpr.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "OtherSamplingTask@SEND"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
//                        ifExpr = "selectedItems[0].businessState != 'SAMPLING_FINISH'",
//                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "收样",
                        code = "OtherSamplingTask@RECEIVE",
                        operationHandler = ReceiveOtherSampleOperationHandler.class,
                        mode = RowOperation.Mode.BUTTON,
                        type = RowOperation.Type.POPUP,
                        popupType = RowOperation.PopupType.FORM,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        fabosJsonClass = OtherSamplingTaskReceiveOpr.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "OtherSamplingTask@RECEIVE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
//                        ifExpr = "selectedItems[0].businessState != 'SEND_SAMPLE'",
//                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
        }
)
@Table(name = "mi_sampling_task",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Getter
@Setter
@TemplateType(type = "multiTable")
public class OtherSamplingTask extends MetaModel {

        @FabosJsonField(
                views = @View(title = "编号"),
                edit = @Edit(title = "编号", notNull = true,search = @Search),
                dynamicField = @DynamicField(dependFiled = @DependFiled(buttonName = "生成", changeBy = "id",
                        dynamicHandler = OtherSamplingCodeGenerateDynamicHandler.class))
        )
        private String generalCode;

        @FabosJsonField(
                views = @View(title = "检测任务单号"),
                edit = @Edit(title = "检测任务单号", notNull = true)
        )
        private String inspectionTaskCode;

        @FabosJsonField(
                views = @View(title = "是否加急"),
                edit = @Edit(title = "是否加急", defaultVal = "false"
                )
        )
        private Boolean isUrgent;

        @FabosJsonField(
                views = @View(title = "是否留样"),
                edit = @Edit(title = "是否留样", defaultVal = "false"
                )
        )
        private Boolean isSaveSample;

        @FabosJsonField(
                views = @View(title = "供应商名称"),
                edit = @Edit(title = "供应商名称", search = @Search(vague = true))
        )
        private String supplierName;

        @FabosJsonField(
                views = @View(title = "检验状态"),
                edit = @Edit(title = "检验状态", type = EditType.CHOICE,search = @Search(),
                        choiceType = @ChoiceType(fetchHandler = TaskBusinessStateEnum.class))
        )
        private String businessState;

        @FabosJsonField(
                views = @View(title = "物资编码"),
                edit = @Edit(title = "物资编码", notNull = true)
        )
        private String materialCode;

        @FabosJsonField(
                views = @View(title = "物资名称"),
                edit = @Edit(title = "物资名称", notNull = true)
        )
        private String materialName;

        @FabosJsonField(
                views = @View(title = "批次号"),
                edit = @Edit(title = "批次号", notNull = true)
        )
        private String originLotId;

        @FabosJsonField(
                views = @View(title = "规格"),
                edit = @Edit(title = "规格", notNull = true)
        )
        private String materialSpecification;

        @FabosJsonField(
                views = @View(title = "单位"),
                edit = @Edit(title = "单位", notNull = true)
        )
        private String unit;

        @FabosJsonField(
                views = @View(title = "检验类型"),
                edit = @Edit(title = "检验类型",readonly = @Readonly, type = EditType.CHOICE,search = @Search(vague = true),
                        choiceType = @ChoiceType(fetchHandler = SampleTypeEnum.class))
        )
        private String inspectionType;

        @FabosJsonField(
                views = @View(title = "检验项目组"),
                edit = @Edit(title = "检验项目组", search = @Search(vague = true))
        )
        private String inspectionItemGroupName;

        @FabosJsonField(
                views = @View(title = "取样点"),
                edit = @Edit(title = "取样点",
                        search = @Search(vague = true)
                )
        )
        private String samplePoint;

        @FabosJsonField(
                views = @View(title = "送样点"),
                edit = @Edit(title = "送样点",
                        search = @Search(vague = true)
                )
        )
        private String sendPoint;
        @FabosJsonField(
                views = @View(title = "取样数量"),
                edit = @Edit(title = "取样数量", readonly = @Readonly)
        )
        private Double allInspectionItemQuantity;

        @FabosJsonField(
                views = @View(title = "送样人id",show = false),
                edit = @Edit(title = "送样人id", show = false)
        )
        private String sendSamplePersonId;

        @FabosJsonField(
                views = @View(title = "送样人"),
                edit = @Edit(title = "送样人", search = @Search(vague = true))
        )
        private String sendSamplePerson;

        @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
        @FabosJsonField(
                views = @View(title = "送样日期", type = ViewType.DATE),
                edit = @Edit(title = "送样日期", notNull = true,
                        dateType = @DateType(type = DateType.Type.DATE))
        )
        private Date sendSampleDate;

        @FabosJsonField(
                views = @View(title = "收样人id",show = false),
                edit = @Edit(title = "收样人id", show = false)
        )
        private String receiveSamplePersonId;

        @FabosJsonField(
                views = @View(title = "收样人"),
                edit = @Edit(title = "收样人", search = @Search(vague = true))
        )
        private String receiveSamplePerson;

        @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
        @FabosJsonField(
                views = @View(title = "收样日期", type = ViewType.DATE),
                edit = @Edit(title = "收样日期", notNull = true,
                        dateType = @DateType(type = DateType.Type.DATE))
        )
        private Date receiveSampleDate;

        @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true,fetch = FetchType.EAGER)
        @JoinColumn(name = "sampling_task_id")
        @OrderBy
        @FabosJsonField(
                edit = @Edit(title = "取样任务明细", type = EditType.TAB_TABLE_ADD),
                views = @View(title = "取样任务明细", type = ViewType.TABLE_VIEW)
        )
        private List<OtherSamplingTaskDetail> detailList;

        @FabosJsonField(
                views = @View(title = "lims部门id",show = false),
                edit = @Edit(title = "lims部门id", show = false)
        )
        private String orgId;

        @FabosJsonField(
                views = @View(title = "wms库存id",show = false),
                edit = @Edit(title = "wms库存id", show = false)
        )
        private String wmsInventoryId;
}
