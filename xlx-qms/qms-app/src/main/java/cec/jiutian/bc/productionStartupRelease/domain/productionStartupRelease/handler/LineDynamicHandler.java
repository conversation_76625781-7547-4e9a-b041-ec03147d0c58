package cec.jiutian.bc.productionStartupRelease.domain.productionStartupRelease.handler;

import cec.jiutian.bc.mto.FactoryArea;
import cec.jiutian.bc.productionStartupRelease.domain.productionStartupRelease.mto.PLSRReportAddMTO;
import cec.jiutian.view.DependFiled;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class LineDynamicHandler implements DependFiled.DynamicHandler<PLSRReportAddMTO> {

    @Override
    public Map<String, Object> handle(PLSRReportAddMTO plsrReport) {
        HashMap<String, Object> res = new HashMap<>(8);
        res.put("line",new FactoryArea());
        res.put("lineName","");
        if (plsrReport.getLine() != null) {
            res.put("line",plsrReport.getLine());
            res.put("lineName",plsrReport.getLine().getFactoryAreaName());
        }
        return res;
    }
}
