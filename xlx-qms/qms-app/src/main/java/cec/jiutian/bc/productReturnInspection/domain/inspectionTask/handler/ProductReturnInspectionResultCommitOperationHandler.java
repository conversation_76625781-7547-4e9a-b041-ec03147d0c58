package cec.jiutian.bc.productReturnInspection.domain.inspectionTask.handler;

import cec.jiutian.bc.generalModeler.util.StringUtils;
import cec.jiutian.bc.modeler.enumration.TaskBusinessStateEnum;
import cec.jiutian.bc.productReturnInspection.domain.inspectionTask.model.MyProductReturnInspectionTask;
import cec.jiutian.bc.productReturnInspection.domain.inspectionTask.model.ProductReturnQualityInspectionStandardDetail;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/28
 * @description TODO
 */
@Component
public class ProductReturnInspectionResultCommitOperationHandler implements OperationHandler<MyProductReturnInspectionTask, Void> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<MyProductReturnInspectionTask> data, Void modelObject, String[] param) {
        MyProductReturnInspectionTask myProductReturnInspectionTask = data.get(0);
        List<ProductReturnQualityInspectionStandardDetail> standardDetailList = myProductReturnInspectionTask.getStandardDetailList().stream().filter(detail -> StringUtils.isEmpty(detail.getInspectionResult())).toList();
        if (CollectionUtils.isNotEmpty(standardDetailList)) {
            throw new FabosJsonApiErrorTip("检验项检验结果未全部录入，请确认");
        }else {
            myProductReturnInspectionTask.setBusinessState(TaskBusinessStateEnum.Enum.INSPECT_FINISH.name());
            fabosJsonDao.mergeAndFlush(myProductReturnInspectionTask);
        }

        return "alert('操作成功')";
    }
}
