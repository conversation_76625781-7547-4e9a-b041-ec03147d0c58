package cec.jiutian.bc.questionPaperManagement.domain.questionPaper.model;

import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleBaseModel;
import cec.jiutian.bc.questionPaperManagement.domain.questionPaper.proxy.QuestionPaperAnalyseDataProxy;
import cec.jiutian.bc.questionPaperManagement.enums.QuestionPaperStateEnum;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.AttachmentType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.InputType;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "qpm_question_paper",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        })
@Getter
@Setter
@FabosJson(
        name = "问题一页纸",
        dataProxy = QuestionPaperAnalyseDataProxy.class
)
@TemplateType(type = "multiTable")
public class QuestionPaperAnalyse extends NamingRuleBaseModel {

    @FabosJsonField(
            views = @View(title = "原因分析（5why）"),
            edit = @Edit(title = "原因分析（5why）", notNull = true,
                    type = EditType.TEXTAREA, inputType = @InputType(length = 400))
    )
    private String causeAnalysis;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态", show = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = QuestionPaperStateEnum.class))
    )
    private String currentState;

    @FabosJsonField(
            views = @View(title = "附件"),
            edit = @Edit(title = "附件", type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(maxLimit = 10))
    )
    private String attachment;

}
