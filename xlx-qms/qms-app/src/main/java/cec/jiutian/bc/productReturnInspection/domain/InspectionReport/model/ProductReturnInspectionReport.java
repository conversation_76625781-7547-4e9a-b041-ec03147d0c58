package cec.jiutian.bc.productReturnInspection.domain.InspectionReport.model;//package cec.jiutian.bc.materialInspect.domain.InspectionReport.model;
//
//import cec.jiutian.bc.materialInspect.domain.publicInspectionTask.model.InsItemDetail;
//import cec.jiutian.core.frame.module.MetaModel;
//import cec.jiutian.core.view.fabosJson.FabosJson;
//import cec.jiutian.view.FabosJsonField;
//import cec.jiutian.view.field.Edit;
//import cec.jiutian.view.field.View;
//import cec.jiutian.view.field.edit.Search;
//import cec.jiutian.view.type.Power;
//import jakarta.persistence.*;
//import lombok.Getter;
//import lombok.Setter;
//
//import java.util.List;
//
//@Getter
//@Setter
//@Entity
//@Table(name = "inspection_task_exe")
//@FabosJson(
//        name = "检验报告",
////        dataProxy = InspectionReportProxy.class,
//        orderBy = "createTime desc",
//        power = @Power(importable = false, print = false)
//)
//public class InspectionReport extends MetaModel {
//    @FabosJsonField(
//            views = @View(title = "任务单号"),
//            edit = @Edit(title = "任务单号",
//                    search = @Search
//            )
//    )
//    @Column(name = "general_code")
//    private String generalCode; // 检验任务单号 (IQC-XXXXX)
//
//    @FabosJsonField(
//            views = @View(title = "物资名称"),
//            edit = @Edit(title = "物资名称",
//                    search = @Search(vague = true)
//            )
//    )
//    @Column(name = "material_name")
//    private String materialName; // 物资名称
//
//    @FabosJsonField(
//            views = @View(title = "物资编码"),
//            edit = @Edit(title = "物资编码",
//                    search = @Search
//            )
//    )
//    @Column(name = "material_code")
//    private String materialCode;
//
//    @FabosJsonField(
//            views = @View(title = "规格型号"),
//            edit = @Edit(title = "规格型号",
//                    search = @Search
//            )
//    )
//    @Column(name = "specification_model")
//    private String specificationModel; // 规格型号
//
//    @FabosJsonField(
//            views = @View(title = "数量"),
//            edit = @Edit(title = "数量"
//            )
//    )
//    @Column(name = "sample_quantity")
//    private Double sampleQuantity;
//    @ManyToMany(cascade = CascadeType.ALL)
//    @JoinTable(
//            name = "ins_task_item", //中间表表名
//            inverseForeignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT),
//            joinColumns = @JoinColumn(name = "task_id", referencedColumnName = "id"),
//            inverseJoinColumns = @JoinColumn(name = "item_id", referencedColumnName = "id"))
//    private List<InsItemDetail> inspectionItems; // 检验项目列表
//}
