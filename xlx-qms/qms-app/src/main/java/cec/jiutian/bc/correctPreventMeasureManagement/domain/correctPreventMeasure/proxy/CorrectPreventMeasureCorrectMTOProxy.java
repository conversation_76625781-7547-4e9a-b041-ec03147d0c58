package cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.proxy;

import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.enumration.ImprovingProgressEnum;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.enumration.ProgressEnum;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.mto.CorrectPreventMeasureCorrectMTO;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.mto.CorrectiveActionCreationMTO;
import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.view.fun.DataProxy;
import org.apache.commons.collections4.CollectionUtils;

import java.util.stream.Collectors;

public class CorrectPreventMeasureCorrectMTOProxy implements DataProxy<CorrectPreventMeasureCorrectMTO> {
    @Override
    public void beforeUpdate(CorrectPreventMeasureCorrectMTO mto) {
        if (CollectionUtils.isNotEmpty(mto.getCorrectiveActionList())) {
            mto.setCorrectAllUserIds(mto.getCorrectiveActionList().stream().map(CorrectiveActionCreationMTO::getUserForInsTaskMTO)
                    .map(BaseModel::getId).distinct().collect(Collectors.joining(",")));
            // handler移过来的内容
            for (CorrectiveActionCreationMTO cacMTO : mto.getCorrectiveActionList()) {
                cacMTO.setImprovingProgress(ImprovingProgressEnum.Enum.TO_BE_CORRECTED.name());
            }

            mto.setCorrectiveState(ProgressEnum.Enum.RUN_SUBMIT.name());
        }
    }
}
