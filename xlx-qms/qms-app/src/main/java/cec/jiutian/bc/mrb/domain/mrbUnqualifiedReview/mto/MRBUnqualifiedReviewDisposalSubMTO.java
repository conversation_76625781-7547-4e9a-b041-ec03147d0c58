package cec.jiutian.bc.mrb.domain.mrbUnqualifiedReview.mto;

import cec.jiutian.bc.materialInspect.domain.publicInspectionTask.mto.UserForInsTaskMTO;
import cec.jiutian.bc.modeler.enumration.UnqualifiedLevelEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.*;
import jakarta.persistence.*;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/6/11
 * @description
 */
@FabosJson(
        name = "处置提交"
)
@Table(name = "qms_mrb_unqualified_review")
@Entity
@Data
public class MRBUnqualifiedReviewDisposalSubMTO extends MetaModel {
    @FabosJsonField(
            views = @View(title = "不合格等级"),
            edit = @Edit(title = "不合格等级", notNull = true,type = EditType.CHOICE,
                    readonly = @Readonly(),
                    choiceType = @ChoiceType(fetchHandler = UnqualifiedLevelEnum.class))
    )
    private String unqualifiedLevel;

    @FabosJsonField(
            views = @View(title = "质量管理部评审人", column = "name"),
            edit = @Edit(title = "质量管理部评审人",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType(type = TabTableReferType.SelectShowTypeMTM.LIST)
            )
    )
    @ManyToOne
    @JoinColumn(name = "quality_depart_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private UserForInsTaskMTO qualityDepart;

    @FabosJsonField(
            views = @View(title = "分管领导评审人", column = "name"),
            edit = @Edit(title = "分管领导评审人",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType(type = TabTableReferType.SelectShowTypeMTM.LIST),
                    dependFieldDisplay = @DependFieldDisplay(notNull = "unqualifiedLevel != 'Slight'", showOrHide = "unqualifiedLevel != 'Slight'")
            )
    )
    @ManyToOne
    @JoinColumn(name = "response_leader_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private UserForInsTaskMTO responseLeader;

    @FabosJsonField(
            views = @View(title = "总经理评审人", column = "name"),
            edit = @Edit(title = "总经理评审人",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType(type = TabTableReferType.SelectShowTypeMTM.LIST),
                    dependFieldDisplay = @DependFieldDisplay(notNull = "unqualifiedLevel == 'Serious'", showOrHide = "unqualifiedLevel == 'Serious'")
            )
    )
    @ManyToOne
    @JoinColumn(name = "general_manager_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private UserForInsTaskMTO generalManager;
}
