package cec.jiutian.bc.quantityPreparationPlan.model;

import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.DateType;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/5
 * @description TODO
 */
@FabosJson(
        name = "质量准备计划详情"
)
@Table(name = "pre_quantity_preparation_plan_detail")
@Entity
@Getter
@Setter
public class QuantityPreparationPlanDetail extends MetaModel {

    @ManyToOne
    @FabosJsonField(
            views = {
                    @View(title = "质量准备计划编号", column = "generalCode", show = false)
            },
            edit = @Edit(title = "质量准备计划编号", type = EditType.REFERENCE_TABLE, show = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode")
            )
    )
    @JsonIgnoreProperties("detailList")
    private QuantityPreparationPlan quantityPreparationPlan;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "日期", type = ViewType.DATE),
            edit = @Edit(title = "日期", notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE))
    )
    private Date planInspectDate;

    @FabosJsonField(
            views = @View(title = "任务总数"),
            edit = @Edit(title = "任务总数", notNull = true, numberType = @NumberType(min = 0))
    )
    private Integer allQuantity;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "quantity_preparation_plan_detail_id")
    @OrderBy
    @FabosJsonField(
            edit = @Edit(title = "检验项明细", type = EditType.TAB_TABLE_ADD),
            views = @View(title = "检验项明细", type = ViewType.TABLE_VIEW)
    )
    private List<QuantityPreparationDetailItem> itemList;

}
