package cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.handler;

import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.enumration.CpmBusinessStateEnum;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.enumration.ProgressEnum;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.model.CorrectPreventMeasure;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/29
 * @description TODO
 */
@Component
public class CorrectPreventMeasureAnalySubmitHandler implements OperationHandler<CorrectPreventMeasure, Void> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Transactional
    @Override
    public String exec(List<CorrectPreventMeasure> data, Void modelObject, String[] param) {
        CorrectPreventMeasure model = data.get(0);
        model.setBusinessState(CpmBusinessStateEnum.Enum.IN_PROGRESS.name());
        model.setPreVeriState(ProgressEnum.Enum.PENDING_EXECUTION.name());
        fabosJsonDao.update(model);
        return null;
    }
}
