package cec.jiutian.bc.qualityExceptionManagement.domain.reworkTask.model;

import cec.jiutian.bc.qualityExceptionManagement.domain.reworkTask.proxy.ReworkTaskDataProxy;
import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowBaseOperation;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Entity
@Table(name = "qms_rework_detail")
@Getter
@Setter
@FabosJson(
        name = "返工明细",
        dataProxy = ReworkTaskDataProxy.class,
        power = @Power(add = false, edit = false, delete = false),
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "add",
                        ifExpr = "1 == 1",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "1 == 1",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                )
        }
)
public class ReworkDetail extends BaseModel {
    //返工工单号
    @FabosJsonField(
            views = @View(title = "返工工单号"),
            edit = @Edit(title = "返工工单号", notNull = true,
                    search = @Search(vague = true), inputType = @InputType(length = 40))
    )
    @Column(nullable = false)
    private String reworkTaskCode;

    @FabosJsonField(
            views = @View(title = "返工量"),
            edit = @Edit(title = "返工量",
                    numberType = @NumberType(min = 0, max = Integer.MAX_VALUE, precision = 2),
                    inputType= @InputType(length = 40))
    )
    private Double reworkQuantity;

    @FabosJsonField(
            views = @View(title = "返工日期", type = ViewType.DATE),
            edit = @Edit(title = "返工日期",
                    dateType = @DateType(type = DateType.Type.DATE)
            )
    )
    private Date reworkDate;

    @FabosJsonField(
            views = @View(title = "成品批次"),
            edit = @Edit(title = "成品批次",
                    search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    private String productBatch;

    @FabosJsonField(
            views = @View(title = "备注"),
            edit = @Edit(title = "备注",
                    type = EditType.TEXTAREA,
                    inputType = @InputType(type = "text", length = 60))
    )
    private String remark;

    @FabosJsonField(
            views = @View(title = "附件"),
            edit = @Edit(title = "附件",
                    type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(maxLimit = 1, size = 100 * 1024))
    )
    @Column(columnDefinition = "text")
    private String attachment;

    // 添加 reworkTask 关联
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "rework_task_id", insertable = false, updatable = false)
    private ReworkTask reworkTask;

    @Column(name = "detail_id")
    private String detailId;

}
