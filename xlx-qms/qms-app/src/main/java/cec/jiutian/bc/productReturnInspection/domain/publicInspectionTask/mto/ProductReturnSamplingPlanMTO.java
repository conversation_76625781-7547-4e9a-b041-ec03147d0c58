package cec.jiutian.bc.productReturnInspection.domain.publicInspectionTask.mto;

import cec.jiutian.bc.basicData.enumeration.*;
import cec.jiutian.bc.modeler.domain.samplingPlan.handler.SamplingPlanCodeGenerateDynamicHandler;
import cec.jiutian.bc.modeler.domain.samplingPlan.model.SamplingPlan;
import cec.jiutian.common.util.BeanUtils;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.*;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@FabosJson(
        name = "抽样方案",
        orderBy = "createTime desc"
)
@Table(name = "bd_sampling_plan"
)
@Entity
@Getter
@Setter
public class ProductReturnSamplingPlanMTO extends MetaModel {

    @FabosJsonField(
            views = @View(title = "编号"),
            edit = @Edit(title = "编号", notNull = true),
            dynamicField = @DynamicField(dependFiled = @DependFiled(buttonName = "生成", changeBy = "id",
                    dynamicHandler = SamplingPlanCodeGenerateDynamicHandler.class))
    )
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "抽样方案名称"),
            edit = @Edit(title = "抽样方案名称", search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    private String name;

    @FabosJsonField(
            views = @View(title = "适用范围"),
            edit = @Edit(title = "适用范围",type = EditType.CHOICE,search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = ApplyRangeEnum.class))
    )
    private String applyRange;

    @FabosJsonField(
            views = @View(title = "抽样方案标准"),
            edit = @Edit(title = "抽样方案标准",type = EditType.CHOICE,search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = SamplingStandardEnum.class))
    )
    private String samplingStandard;

    @FabosJsonField(
            views = @View(title = "件次"),
            edit = @Edit(title = "件次",
                    dependFieldDisplay = @DependFieldDisplay(notNull = "samplingStandard == 'fullSampling'", showOrHide = "samplingStandard == 'fullSampling'"))
    )
    private String pieceItem;

    @FabosJsonField(
            views = @View(title = "抽样比例"),
            edit = @Edit(title = "抽样比例",
                    dependFieldDisplay = @DependFieldDisplay(notNull = "samplingStandard == 'proportionSampling'", showOrHide = "samplingStandard == 'proportionSampling'"))
    )
    private String proportion;

    @FabosJsonField(
            views = @View(title = "固定抽样数量"),
            edit = @Edit(title = "固定抽样数量",
                    numberType = @NumberType(min = 0,max = 100,precision = 2),
                    dependFieldDisplay = @DependFieldDisplay(notNull = "samplingStandard == 'fixedSampling'", showOrHide = "samplingStandard == 'fixedSampling'"))
    )
    private Double fixedCount;

    @FabosJsonField(
            views = @View(title = "自定义抽样数量"),
            edit = @Edit(title = "自定义抽样数量",
                    dependFieldDisplay = @DependFieldDisplay(notNull = "samplingStandard == 'customSampling'", showOrHide = "samplingStandard == 'customSampling'"))
    )
    private String customCount;

    @FabosJsonField(
            views = @View(title = "检验水平类型"),
            edit = @Edit(title = "检验水平类型",type = EditType.CHOICE,search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = cec.jiutian.bc.basicData.enumeration.AQLInspectionType.class),
                    dependFieldDisplay = @DependFieldDisplay(notNull = "samplingStandard == 'AQLSampling'", showOrHide = "samplingStandard == 'AQLSampling'"))
    )
    private String AQLInspectionType;

    @FabosJsonField(
            views = @View(title = "检验水平值"),
            edit = @Edit(title = "检验水平值",type = EditType.CHOICE,search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = GeneralLevelValue.class),
                    dependFieldDisplay = @DependFieldDisplay(notNull = "samplingStandard == 'AQLSampling' && AQLInspectionType == 'General'", showOrHide = "samplingStandard == 'AQLSampling' && AQLInspectionType == 'General'"))
    )
    private String generalLevelValue;

    @FabosJsonField(
            views = @View(title = "检验水平值"),
            edit = @Edit(title = "检验水平值",type = EditType.CHOICE,search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = SpecialLevelValue.class),
                    dependFieldDisplay = @DependFieldDisplay(notNull = "samplingStandard == 'AQLSampling' && AQLInspectionType == 'Special'", showOrHide = "samplingStandard == 'AQLSampling' && AQLInspectionType == 'Special'"))
    )
    private String specialLevelValue;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态", type = EditType.CHOICE,search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = StatusEnum.class))
    )
    private String status;

    @FabosJsonField(
            views = @View(title = "备注"),
            edit = @Edit(title = "备注",
                    type = EditType.TEXTAREA)
    )
    private String remark;

    public static ProductReturnSamplingPlanMTO createBySamplingPlan(SamplingPlan samplingPlan) {
        if (samplingPlan == null) {
            return null;
        }
        ProductReturnSamplingPlanMTO productReturnSamplingPlanMTO = new ProductReturnSamplingPlanMTO();
        BeanUtils.copyProperties(samplingPlan, productReturnSamplingPlanMTO);
        productReturnSamplingPlanMTO.setId(null);
        return productReturnSamplingPlanMTO;
    }
}
