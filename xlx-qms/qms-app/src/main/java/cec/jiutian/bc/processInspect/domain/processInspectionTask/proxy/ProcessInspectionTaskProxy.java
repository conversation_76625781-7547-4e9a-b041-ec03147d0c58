package cec.jiutian.bc.processInspect.domain.processInspectionTask.proxy;

import cec.jiutian.bc.generalModeler.enumeration.YesOrNoEnum;
import cec.jiutian.bc.modeler.enumration.TaskBusinessStateEnum;
import cec.jiutian.bc.mto.ProcessOperationMTO;
import cec.jiutian.bc.mto.TechnologyFLowMTO;
import cec.jiutian.bc.processInspect.domain.processInspectionTask.model.ProcessInspectionTask;
import cec.jiutian.bc.processInspect.domain.processInspectionTask.model.ProcessInspectionTaskDetail;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public class ProcessInspectionTaskProxy implements DataProxy<ProcessInspectionTask> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public void beforeAdd(ProcessInspectionTask entity) {
        checkNotNull(entity.getDetails());
        entity.setCurrentState(OrderCurrentStateEnum.Enum.EDIT.name());
        entity.setBusinessState(TaskBusinessStateEnum.Enum.BE_INSPECT.name());
        entity.setExtraFlag(YesOrNoEnum.Enum.Y.name());
    }

    private void checkNotNull(List<ProcessInspectionTaskDetail> details) {
        if (CollectionUtils.isNotEmpty(details)) {
            for (ProcessInspectionTaskDetail detail : details) {
                if (null == detail.getInspectionItemGroup()) {
                    throw new FabosJsonApiErrorTip("详情中检验组必填，请确认");
                }
            }
        }
    }

    @Override
    public void beforeUpdate(ProcessInspectionTask entity) {
        if (!entity.getCurrentState().equals(OrderCurrentStateEnum.Enum.EDIT.name())) {
            throw new FabosJsonApiErrorTip("开立状态允许编辑");
        }
        checkNotNull(entity.getDetails());
    }

    @Override
    public void beforeDelete(ProcessInspectionTask entity) {
        if (!entity.getCurrentState().equals(OrderCurrentStateEnum.Enum.EDIT.name())) {
            throw new FabosJsonApiErrorTip("开立状态允许删除");
        }
    }

    @Override
    public void afterSingleFetch(Map<String, Object> map) {
        if (null != map.get("technologyFlowId")) {
            TechnologyFLowMTO fLowMTO = fabosJsonDao.findById(TechnologyFLowMTO.class, map.get("technologyFlowId"));
            if (fLowMTO != null) {
                map.put("technologyFlow", fLowMTO);
                map.put("technologyFlow_flowName", fLowMTO.getFlowName());
            } else {
                throw new FabosJsonApiErrorTip("未查到工艺流程源数据，请确认");
            }
        }
        if (null != map.get("processOperationId")) {
            ProcessOperationMTO operationMTO = fabosJsonDao.findById(ProcessOperationMTO.class, map.get("processOperationId"));
            if (null != operationMTO) {
                map.put("processOperation", operationMTO);
                map.put("processOperation_processOperationCode", operationMTO.getProcessOperationCode());
            } else {
                throw new FabosJsonApiErrorTip("未查到工艺工序源数据，请确认");
            }
        }
    }

}
