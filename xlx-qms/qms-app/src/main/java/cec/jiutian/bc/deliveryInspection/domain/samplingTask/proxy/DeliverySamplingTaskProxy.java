package cec.jiutian.bc.deliveryInspection.domain.samplingTask.proxy;

import cec.jiutian.bc.deliveryInspection.domain.samplingTask.model.DeliverySamplingTask;
import cec.jiutian.view.fun.DataProxy;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/3/10
 * @description TODO
 */
@Component
public class DeliverySamplingTaskProxy implements DataProxy<DeliverySamplingTask> {

    @Override
    public void beforeAdd(DeliverySamplingTask deliverySamplingTask) {
        DataProxy.super.beforeAdd(deliverySamplingTask);
    }
}
