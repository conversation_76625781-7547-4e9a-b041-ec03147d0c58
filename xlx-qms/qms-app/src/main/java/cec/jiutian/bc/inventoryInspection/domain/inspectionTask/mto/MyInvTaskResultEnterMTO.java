package cec.jiutian.bc.inventoryInspection.domain.inspectionTask.mto;

import cec.jiutian.bc.basicData.domain.inspectionStandard.model.InspectionStandard;
import cec.jiutian.bc.basicData.enumeration.ApplyRangeEnum;
import cec.jiutian.bc.inventoryInspection.domain.inspectionRequest.model.InventoryInspectionRequestDetail;
import cec.jiutian.bc.inventoryInspection.domain.inspectionTask.handler.InvInsItemResultHandler;
import cec.jiutian.bc.inventoryInspection.domain.inspectionTask.handler.InventoryInspectionItemResultHandler;
import cec.jiutian.bc.inventoryInspection.enumeration.InspectionResultEnum;
import cec.jiutian.bc.modeler.enumration.TaskBusinessStateEnum;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DependFieldDisplay;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/9
 * @description TODO
 */
@FabosJson(
        name = "检验任务结果录入MTO",
        power = @Power(add = false,edit = false,delete = false)
)
@Entity
@Getter
@Setter
public class MyInvTaskResultEnterMTO extends MetaModel {

    @FabosJsonField(
            views = @View(title = "编号"),
            edit = @Edit(title = "编号", readonly = @Readonly)
    )
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "供应商名称"),
            edit = @Edit(title = "供应商名称",  readonly = @Readonly)
    )
    private String supplierName;

    @FabosJsonField(
            views = @View(title = "检验部门"),
            edit = @Edit(title = "检验部门",  readonly = @Readonly)
    )
    private String inspectionDepartment;

    @FabosJsonField(
            views = @View(title = "检验人"),
            edit = @Edit(title = "检验人",
                    readonly = @Readonly,
                    search = @Search(vague = true))
    )
    private String inspector;

    @FabosJsonField(
            views = @View(title = "检验状态"),
            edit = @Edit(title = "检验状态",  readonly = @Readonly, type = EditType.CHOICE, search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = TaskBusinessStateEnum.class))
    )
    private String businessState;

    @FabosJsonField(
            views = @View(title = "当前状态"),
            edit = @Edit(title = "当前状态",  readonly = @Readonly, type = EditType.CHOICE, search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = OrderCurrentStateEnum.class))
    )
    private String currentState;

    @FabosJsonField(
            views = @View(title = "质检标准", column = "generalCode"),
            edit = @Edit(title = "质检标准",
                    readonly = @Readonly, allowAddMultipleRows = false,
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode")
            )
    )
    @ManyToOne
    @JoinColumn(name = "inspection_standard_id")
    private InspectionStandard inspectionStandard;

    @FabosJsonField(
            views = @View(title = "创建类型"),
            edit = @Edit(title = "创建类型", type = EditType.CHOICE, readonly = @Readonly,
                    choiceType = @ChoiceType(fetchHandler = cec.jiutian.bc.inventoryInspection.enumeration.TaskBuildTypeEnum.class))
    )
    private String buildType;

    @FabosJsonField(
            views = @View(title = "检验类型"),
            edit = @Edit(title = "检验类型",  readonly = @Readonly, type = EditType.CHOICE, search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = ApplyRangeEnum.class))
    )
    private String inspectionType;

    @FabosJsonField(
            views = @View(title = "是否加急"),
            edit = @Edit(title = "是否加急", defaultVal = "false"
            )
    )
    private Boolean isUrgent;

    @FabosJsonField(
            views = @View(title = "不合格等级"),
            edit = @Edit(title = "不合格等级",  readonly = @Readonly,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "inspectionResult == 'UNQUALIFIED'"))
    )
    private String unqualifiedLevel;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "检验通知物资", column = "materialCode", type = ViewType.TABLE_FORM),
            edit = @Edit(title = "检验通知物资",
                    type = EditType.REFERENCE_TABLE, allowAddMultipleRows = false,
                    readonly = @Readonly,
                    referenceTableType = @ReferenceTableType(id = "id", label = "materialCode")
            )
    )
    private InventoryInspectionRequestDetail inventoryInspectionRequestDetail;

    @FabosJsonField(
            views = @View(title = "检验通知单号"),
            edit = @Edit(title = "检验通知单号", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inventoryInspectionRequestDetail", beFilledBy = "inspectionRequest_generalCode"))
    )
    private String inspectionRequestCode;

    @FabosJsonField(
            views = @View(title = "检验物资名称"),
            edit = @Edit(title = "检验物资名称", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inspectionRequestDetail", beFilledBy = "materialName"))
    )
    private String materialName;

    @FabosJsonField(
            views = @View(title = "单位"),
            edit = @Edit(title = "单位", notNull = true),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inspectionRequestDetail", beFilledBy = "measureUnit"))
    )
    private String unit;

    @FabosJsonField(
            views = @View(title = "批次号",show = false),
            edit = @Edit(title = "批次号",show = false, readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inspectionRequestDetail", beFilledBy = "originLotId"))
    )
    private String originLotId;

    @FabosJsonField(
            views = @View(title = "规格"),
            edit = @Edit(title = "规格"),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inspectionRequestDetail", beFilledBy = "materialSpecification"))
    )
    private String materialSpecification;

    @FabosJsonField(
            views = @View(title = "来料数量"),
            edit = @Edit(title = "来料数量", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inspectionRequestDetail", beFilledBy = "lotQuantity"))
    )
    private Double arrivalQuantity;

    @FabosJsonField(
            views = @View(title = "样品数量"),
            edit = @Edit(title = "样品数量", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inspectionRequestDetail", beFilledBy = "originSampleQuantity"))
    )
    private Double sampleQuantity;

    @FabosJsonField(
            views = @View(title = "取样数量"),
            edit = @Edit(title = "取样数量", readonly = @Readonly)
    )
    private Double allInspectionItemQuantity;

    @FabosJsonField(
            views = @View(title = "检验结果"),
            edit = @Edit(title = "检验结果", type = EditType.CHOICE, choiceType = @ChoiceType(fetchHandler = InspectionResultEnum.class)),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "detailList",
                    dynamicHandler = InvInsItemResultHandler.class))
    )
    private String inspectionResult;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = false)
    @FabosJsonField(
            views = @View(title = "检验项指标", type = ViewType.TABLE_VIEW),
            edit = @Edit(title = "检验项指标", type = EditType.TAB_TABLE_ADD, readonly = @Readonly(edit = false)),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "detailList",
                    dynamicHandler = InventoryInspectionItemResultHandler.class))
    )
    private List<InventoryTaskResultEnterDetailMTO> detailList;
}
