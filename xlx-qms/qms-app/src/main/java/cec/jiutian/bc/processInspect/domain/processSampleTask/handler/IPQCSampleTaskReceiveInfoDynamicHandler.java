package cec.jiutian.bc.processInspect.domain.processSampleTask.handler;

import cec.jiutian.bc.materialInspect.domain.publicInspectionTask.mto.UserForInsTaskMTO;
import cec.jiutian.bc.processInspect.domain.processSampleTask.model.ProcessSampleTask;
import cec.jiutian.bc.processInspect.domain.processSampleTask.model.ProcessSampleTaskDetail;
import cec.jiutian.bc.processInspect.domain.processSampleTask.model.ProcessSampleTaskReceive;
import cec.jiutian.bc.processInspect.domain.publicInspectionTask.mto.ProcessUserForInsTaskMTO;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.DependFiled;
import cn.hutool.core.bean.BeanUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class IPQCSampleTaskReceiveInfoDynamicHandler implements DependFiled.DynamicHandler<ProcessSampleTaskReceive> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public Map<String, Object> handle(ProcessSampleTaskReceive processSampleTaskReceive) {
        Map<String, Object> result = new HashMap<>();
        if (StringUtils.isNotEmpty(processSampleTaskReceive.getGeneralCode())) {
            ProcessSampleTaskReceive condition = new ProcessSampleTaskReceive();
            condition.setGeneralCode(processSampleTaskReceive.getGeneralCode());
            ProcessSampleTaskReceive samplingTask = fabosJsonDao.selectOne(condition);
            result.put("inspectionTaskCode", samplingTask.getInspectionTaskCode());
            result.put("businessState", samplingTask.getBusinessState());
            result.put("productCode", samplingTask.getProductCode());
            result.put("productName", samplingTask.getProductName());
            result.put("processCode", samplingTask.getProcessCode());
            result.put("operationCode", samplingTask.getOperationCode());
            result.put("operationName", samplingTask.getOperationName());
            result.put("actualLotSerialId", samplingTask.getActualLotSerialId());
            result.put("sampleQuantity", samplingTask.getSampleQuantity());
            result.put("unit", samplingTask.getUnit());
            result.put("processInspectionType", samplingTask.getProcessInspectionType());
            result.put("samplingPoint",samplingTask.getSamplePoint());
            result.put("sendInspectPoint", samplingTask.getSendPoint());
            if (StringUtils.isNotEmpty(samplingTask.getReceiveSamplePersonId())) {
                UserForInsTaskMTO user = fabosJsonDao.findById(UserForInsTaskMTO.class, samplingTask.getReceiveSamplePersonId());
                if (user != null) {
                    result.put("receiveSamplePersonId", user.getId());
                    result.put("user", user);
                    result.put("receiveSamplePerson",user.getName());
                }
            }
            result.put("submissionTime",samplingTask.getSubmissionTime());
            result.put("receiveSampleDate", samplingTask.getReceiveSampleDate());
            result.put("appearanceInspect",samplingTask.getAppearanceInspect());
            result.put("reviewQuantity",samplingTask.getReviewQuantity());
            result.put("unqualifiedHandle",samplingTask.getUnqualifiedHandle());
            result.put("abnormalDescription",samplingTask.getAbnormalDescription());
            result.put("abnormalAttachments",samplingTask.getAbnormalAttachments());
            if (StringUtils.isNotEmpty(samplingTask.getDiscoveredPersonId())) {
                ProcessUserForInsTaskMTO processUserForInsTaskMTO = fabosJsonDao.findById(ProcessUserForInsTaskMTO.class, samplingTask.getDiscoveredPersonId());
                if (processUserForInsTaskMTO != null) {
                    result.put("discoveredPersonId", processUserForInsTaskMTO.getId());
                    result.put("discoveredPerson", processUserForInsTaskMTO);
                    result.put("discoveredPersonName",processUserForInsTaskMTO.getName());
                }
            }
            ProcessSampleTask samplingTaskData = fabosJsonDao.findById(ProcessSampleTask.class,samplingTask.getId());
            if (CollectionUtils.isNotEmpty(samplingTaskData.getDetails())) {
                List<ProcessSampleTaskDetail> detailList = new ArrayList<>();
                samplingTaskData.getDetails().forEach(samplingTaskDetail -> {
                    ProcessSampleTaskDetail detail = new ProcessSampleTaskDetail();
                    BeanUtil.copyProperties(samplingTaskDetail, detail);
                    detailList.add(detail);
                });
                result.put("detailList", detailList);
            }
        }
        return result;
    }
}
