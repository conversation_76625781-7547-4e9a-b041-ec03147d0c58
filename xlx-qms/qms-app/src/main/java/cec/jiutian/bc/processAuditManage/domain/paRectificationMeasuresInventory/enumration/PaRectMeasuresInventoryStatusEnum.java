package cec.jiutian.bc.processAuditManage.domain.paRectificationMeasuresInventory.enumration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/15
 * @description TODO
 */
public class PaRectMeasuresInventoryStatusEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum data : Enum.values()) {
            list.add(new VLModel(data.name(), data.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        NEW("新建"),
        PENDING("待处理"),
        TO_BE_VERIFIED("待验证"),
        PENDING_APPROVAL("待审批"),
        IN_APPROVAL("审批中"),
        COMPLETE("完成"),
        ;

        private final String value;

    }
}
