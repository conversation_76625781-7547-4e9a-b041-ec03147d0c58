package cec.jiutian.bc.changeRequestManagement.domain.ecr.mto.formDetail;

import cec.jiutian.bc.changeRequestManagement.domain.ecr.model.ChangeRequest;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.model.ECRItem;
import cec.jiutian.bc.changeRequestManagement.enums.*;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.meta.SkipMetadataScanning;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.Column;
import jakarta.persistence.Transient;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Setter
@Getter
@FabosJson(
        name = "详情",
        formColumnElements = 2,
        subTableDisplayType = FabosJson.SubTableDisplayTypeEnum.INDEX
)
@SkipMetadataScanning
public class ECRDetailFormMTO extends BaseModel {

    @FabosJsonField(
            views = @View(title = "变更主题"),
            edit = @Edit(
                    readonly = @Readonly,
                    title = "变更主题",
                    inputType = @InputType(length = 20)
            )
    )
    private String changeSubject;

    @FabosJsonField(
            views = @View(title = "申请部门"),
            edit = @Edit(
                    title = "申请部门",
                    readonly = @Readonly,
                    inputType = @InputType(length = 20)
            )
    )
    private String applyDepartment;

    @FabosJsonField(
            views = @View(title = "申请人"),
            edit = @Edit(
                    title = "申请人",
                    inputType = @InputType(length = 20),
                    readonly = @Readonly(add = true, edit = true)
            )
    )
    private String applicant;

    @FabosJsonField(
            views = @View(title = "申请时间", type = ViewType.DATE_TIME),
            edit = @Edit(
                    title = "申请时间",
                    readonly = @Readonly(add = true, edit = true),
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date applyTime;

    @FabosJsonField(
            views = @View(title = "变更类型"),
            edit = @Edit(
                    readonly = @Readonly,
                    title = "变更类型",
                    inputType = @InputType(length = 20),
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ChangeType.class)
            )
    )
    private String changeType;

    @FabosJsonField(
            views = @View(title = "变更级别"),
            edit = @Edit(
                    readonly = @Readonly,
                    title = "变更级别",
                    inputType = @InputType(length = 20),
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ChangeLevel.class)
            )
    )
    private String changeLevel;

    @FabosJsonField(
            views = @View(title = "变更原因"),
            edit = @Edit(
                    formColumns = 3,
                    readonly = @Readonly,
                    type = EditType.TEXTAREA,
                    title = "变更原因",
                    inputType = @InputType(length = 100)
            )
    )
    private String changeReason;

    @FabosJsonField(
            views = @View(title = "变更内容变更前"),
            edit = @Edit(
                    formColumns = 3,
                    readonly = @Readonly,
                    title = "变更前",
                    type = EditType.TEXTAREA,
                    inputType = @InputType(length = 100)
            )
    )
    private String beforeChange;

    @FabosJsonField(
            views = @View(title = "变更后"),
            edit = @Edit(
                    formColumns = 3,
                    readonly = @Readonly,
                    title = "变更后",
                    type = EditType.TEXTAREA,
                    inputType = @InputType(length = 100)
            )
    )
    private String afterChange;

    @FabosJsonField(
            views = @View(title = "预计变更切换时间", type = ViewType.DATE_TIME),
            edit = @Edit(
                    readonly = @Readonly,
                    title = "预计变更切换时间",
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date estimatedSwitchTime;


    @FabosJsonField(
            views = @View(title = "风险影响"),
            edit = @Edit(
                    readonly = @Readonly,
                    title = "风险影响",
                    type = EditType.TEXTAREA,
                    inputType = @InputType(length = 100)
            )
    )
    private String riskImpact;

    @FabosJsonField(
            views = @View(title = "文件标准化"),
            edit = @Edit(
                    readonly = @Readonly,
                    title = "文件标准化",
                    type = EditType.multiple_select,
                    choiceType = @ChoiceType(fetchHandler = FileStandardEnum.class),
                    inputType = @InputType(length = 100)
            )
    )
    @Column(name = "file_standardization", length = 100)
    private String fileStandardization;

    @FabosJsonField(
            views = @View(title = "附件"),
            edit = @Edit(title = "附件",
                    readonly = @Readonly,
                    inputType = @InputType(length = 300),
                    type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(
                            size = 100 * 1024,
                            maxLimit = 1,
                            type = AttachmentType.Type.BASE)
            )
    )
    private String attachment;

    @FabosJsonField(
            views = @View(title = "变更申请审批", column = "taskName", type = ViewType.TABLE_VIEW),
            edit = @Edit(
                    title = "变更申请审批",
                    type = EditType.TAB_TABLE_ADD,
                    allowAddMultipleRows = true,
                    referenceTableType = @ReferenceTableType(label = "taskName")
            )
    )
    private List<ApproveTaskMTO> approveTaskMTO;

    @FabosJsonField(
            views = @View(title = "代办事项", column = "changeItem", type = ViewType.TABLE_VIEW),
            edit = @Edit(
                    title = "代办事项",
                    type = EditType.TAB_TABLE_ADD,
                    allowAddMultipleRows = true,
                    referenceTableType = @ReferenceTableType(label = "changeItem")
            )
    )
    private List<ECRItemMTO> ecrItemMTOS;

    @Transient
    @FabosJsonField(
            edit = @Edit(title = "变更申请批准", type = EditType.DIVIDE)
    )
    private String divide;

    @FabosJsonField(
            views = @View(title = "内部意见"),
            edit = @Edit(title = "内部意见",
                    readonly = @Readonly,
                    type = EditType.BOOLEAN,
                    boolType = @BoolType(trueText = "同意", falseText = "不同意")
            )
    )
    private Boolean innerAdvice;

    @FabosJsonField(
            views = @View(title = "客户意见"),
            edit = @Edit(title = "客户意见",
                    readonly = @Readonly,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = CustomerAdviceEnum.class)
            )
    )
    private String customerAdvice;

    @FabosJsonField(
            views = @View(title = "审批人"),
            edit = @Edit(title = "审批人",
                    readonly = @Readonly)
    )
    private String approver;

    @FabosJsonField(
            views = @View(title = "批准人"),
            edit = @Edit(title = "批准人",
                    readonly = @Readonly
            )
    )
    private String conformer;

    @FabosJsonField(
            views = @View(title = "附件"),
            edit = @Edit(title = "附件",
                    readonly = @Readonly,
                    inputType = @InputType(length = 300),
                    type = EditType.ATTACHMENT,
                    tips = "最大支持100M文件",
                    attachmentType = @AttachmentType(
                            size = 100 * 1024,
                            maxLimit = 1,
                            type = AttachmentType.Type.BASE)
            )
    )
    @Column(length = 300)
    private String adviceAttachment;

    @Transient
    @FabosJsonField(
            edit = @Edit(title = "变更实施与验证", type = EditType.DIVIDE)
    )
    private String divide1;

    @FabosJsonField(
            views = @View(title = "变更切换日期", type = ViewType.DATE_TIME),
            edit = @Edit(
                    readonly = @Readonly,
                    title = "变更切换日期",
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @Column(name = "change_switch_date")
    private Date changeSwitchDate;

    @FabosJsonField(
            views = @View(title = "计划变更执行日期", type = ViewType.DATE_TIME),
            edit = @Edit(
                    readonly = @Readonly,
                    title = "计划变更执行日期",
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @Column(name = "planned_execution_date")
    private Date plannedExecutionDate;

    @FabosJsonField(
            views = @View(title = "方案验证内容"),
            edit = @Edit(
                    readonly = @Readonly,
                    title = "方案验证内容",
                    type = EditType.multiple_select,
                    choiceType = @ChoiceType(fetchHandler = VerificationContentEnum.class)
            )
    )
    private String verificationContent;

    @FabosJsonField(
            views = @View(title = "附件"),
            edit = @Edit(title = "附件",
                    readonly = @Readonly,
                    inputType = @InputType(length = 400),
                    type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(
                            size = 100 * 1024,
                            maxLimit = 5,
                            type = AttachmentType.Type.BASE)
            )
    )
    @Column(length = 400)
    private String files;

    @FabosJsonField(
            views = @View(title = "实施与验证审核", column = "taskName", type = ViewType.TABLE_VIEW),
            edit = @Edit(
                    readonly = @Readonly,
                    title = "实施与验证审核",
                    type = EditType.TAB_TABLE_ADD,
                    allowAddMultipleRows = true,
                    referenceTableType = @ReferenceTableType(label = "taskName")
            )
    )
    private List<ApproveTaskMTO> verifyTaskMTO;

    @Transient
    @FabosJsonField(
            edit = @Edit(title = "变更跟踪与关闭", type = EditType.DIVIDE)
    )
    private String divide3;

    @FabosJsonField(
            views = @View(title = "关闭意见"),
            edit = @Edit(
                    title = "关闭意见",
                    inputType = @InputType(length = 10),
                    search = @Search,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ApproveResultEnum.class)
            )
    )
    private String closeAdvice;

    @FabosJsonField(
            views = @View(title = "意见说明"),
            edit = @Edit(
                    title = "意见说明",
                    type = EditType.TEXTAREA,
                    inputType = @InputType(length = 100)
            )
    )
    @Column(length = 100)
    private String explain;

    @FabosJsonField(
            views = @View(title = "附件"),
            edit = @Edit(title = "附件",
                    readonly = @Readonly,
                    inputType = @InputType(length = 300),
                    type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(
                            size = 100 * 1024,
                            maxLimit = 5,
                            type = AttachmentType.Type.BASE)
            )
    )
    private String closeFiles;

    @Transient
    @FabosJsonField(
            edit = @Edit(title = "", type = EditType.DIVIDE)
    )
    private String divide4;
    @FabosJsonField(
            views = @View(title = "变更补充任务", column = "changeItem", type = ViewType.TABLE_VIEW),
            edit = @Edit(
                    readonly = @Readonly,
                    title = "变更补充任务",
                    type = EditType.TAB_TABLE_ADD,
                    allowAddMultipleRows = true,
                    referenceTableType = @ReferenceTableType(label = "changeItem")
            )
    )
    private List<ECNItemMTO> ecnItemMTOS;



    public void init(ChangeRequest changeRequest) {
        if (changeRequest == null) {
            throw new ServiceException("参数异常");
        }
        this.setChangeSubject(changeRequest.getChangeSubject());
        this.setApplyDepartment(changeRequest.getApplyDepartment());
        this.setChangeType(changeRequest.getChangeType());
        this.setChangeLevel(changeRequest.getChangeLevel());
        this.setChangeReason(changeRequest.getChangeReason());
        this.setBeforeChange(changeRequest.getBeforeChange());
        this.setAfterChange(changeRequest.getAfterChange());
        this.setEstimatedSwitchTime(changeRequest.getEstimatedSwitchTime());
        this.setRiskImpact(changeRequest.getRiskImpact());
        this.setFileStandardization(changeRequest.getFileStandardization());
        this.setAttachment(changeRequest.getAttachment());
        this.setApplicant(changeRequest.getApplicant());
        this.setApplyTime(changeRequest.getApplyTime());

        List<ECRItem> ecrItems = changeRequest.getECRItems();
        if (CollectionUtils.isNotEmpty(ecrItems)) {
            ArrayList<ECRItemMTO> changeApplyReviewMTOS = new ArrayList<>(ecrItems.size());
            for (ECRItem ecrItem : ecrItems) {
                ECRItemMTO ecrItemMTO = ECRItemMTO.create(ecrItem);
                changeApplyReviewMTOS.add(ecrItemMTO);
            }
            this.setEcrItemMTOS(changeApplyReviewMTOS);
        }

    }


}
