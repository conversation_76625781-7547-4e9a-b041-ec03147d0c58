package cec.jiutian.bc.productReturnInspection.domain.inspectionTask.handler;

import cec.jiutian.bc.basicData.domain.inspectionStandard.model.InspectionStandard;
import cec.jiutian.bc.basicData.service.AQLSamplingTables;
import cec.jiutian.bc.generalModeler.enumeration.YesOrNoEnum;
import cec.jiutian.bc.modeler.domain.samplingPlan.model.SamplingPlan;
import cec.jiutian.bc.modeler.enumration.AQLInspectionTypeEnum;
import cec.jiutian.bc.productReturnInspection.domain.inspectionTask.model.ProductReturnInspectionTask;
import cec.jiutian.bc.productReturnInspection.domain.inspectionTask.model.ProductReturnQualityInspectionStandardDetail;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/3/12
 * @description TODO
 */
@Component
public class ProductReturnSampleQuantityDynamicHandler implements DependFiled.DynamicHandler<ProductReturnInspectionTask> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private AQLSamplingTables aqlSamplingTables;

    @Override
    public Map<String, Object> handle(ProductReturnInspectionTask productReturnInspectionTask) {
        Map<String, Object> result = new HashMap<>();
        double sampleQuantity = 0.0;
        if (CollectionUtils.isNotEmpty(productReturnInspectionTask.getStandardDetailList())) {
                for (ProductReturnQualityInspectionStandardDetail standardDetail : productReturnInspectionTask.getStandardDetailList()) {
                    SamplingPlan samplingPlan = fabosJsonDao.getById(SamplingPlan.class, standardDetail.getSamplingPlan().getId());
                    switch (samplingPlan.getSamplingStandard()) {
                        case "AQLSampling":
                            if (AQLInspectionTypeEnum.Enum.General.name().equals(samplingPlan.getAQLInspectionType())) {
                                standardDetail.setSampleSize((double)aqlSamplingTables.getAQlSampleQuantity(productReturnInspectionTask.getArrivalQuantity(), samplingPlan.getGeneralLevelValue()));
                            }else {
                                standardDetail.setSampleSize((double)aqlSamplingTables.getAQlSampleQuantity(productReturnInspectionTask.getArrivalQuantity(), samplingPlan.getSpecialLevelValue()));
                            }
                            break;
                        case "fullSampling":
                            standardDetail.setSampleSize(productReturnInspectionTask.getArrivalQuantity());
                            break;
                        case "proportionSampling":
                            try {
                                BigDecimal data = BigDecimal.valueOf(productReturnInspectionTask.getArrivalQuantity());
                                double per = samplingPlan.getProportion() / 100;
                                BigDecimal percentage = new BigDecimal(per);
                                standardDetail.setSampleSize(data.multiply(percentage).doubleValue());
                            }catch (Exception e) {
                                throw new FabosJsonApiErrorTip("抽样方案中比例输入格式有误");
                            }
                            break;
                        case "fixedSampling":
                            standardDetail.setSampleSize(samplingPlan.getFixedCount());
                            break;
                        case "customSampling":
                            standardDetail.setSampleSize(samplingPlan.getFixedCount());
                            break;
                    }
                    fabosJsonDao.mergeAndFlush(standardDetail);
                    sampleQuantity = sampleQuantity + standardDetail.getSampleSize();
                }

            // 加上留样数量作为总取样数量
            InspectionStandard inspectionStandard = fabosJsonDao.getById(InspectionStandard.class, productReturnInspectionTask.getInspectionStandard().getId());
            if (YesOrNoEnum.Enum.Y.name().equals(inspectionStandard.getKeepSampleFlag())) {
                sampleQuantity = sampleQuantity + inspectionStandard.getSampleQuantity();
            }
        }

        result.put("sampleQuantity", sampleQuantity);
        return result;
    }
}
