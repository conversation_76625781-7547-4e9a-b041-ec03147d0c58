package cec.jiutian.bc.clientReviewManagement.domain.customerReviewTask.model;

import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.*;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@FabosJson(
        name = "执行详情"
)
@Table(name = "qms_client_review_task")
@Entity
@Getter
@Setter
public class CustomerReviewTaskDetailExec extends MetaModel {
    @FabosJsonField(
            views = @View(title = "评审结果"),
            edit = @Edit(title = "评审结果")
    )
    private String reviewResult;

    @FabosJsonField(
            views = @View(title = "问题项", type = ViewType.TABLE_VIEW),
            edit = @Edit(title = "问题项", type = EditType.TAB_TABLE_ADD)
    )
    @JoinColumn(name = "review_task_id")
    @OneToMany(cascade = CascadeType.ALL)
    private List<CustomerReviewTaskDetailProblemExec> customerReviewTaskDetailProblemExecs; // 问题>
}
