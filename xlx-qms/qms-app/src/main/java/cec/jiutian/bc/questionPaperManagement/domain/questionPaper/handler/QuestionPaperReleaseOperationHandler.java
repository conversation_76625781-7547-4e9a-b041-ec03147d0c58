package cec.jiutian.bc.questionPaperManagement.domain.questionPaper.handler;

import cec.jiutian.bc.questionPaperManagement.domain.questionPaper.model.QuestionPaper;
import cec.jiutian.bc.questionPaperManagement.enums.QuestionPaperStateEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class QuestionPaperReleaseOperationHandler implements OperationHandler<QuestionPaper, Void> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<QuestionPaper> data, Void modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            QuestionPaper entity = data.get(0);
            entity.setCurrentState(QuestionPaperStateEnum.Enum.WaitAnalyse.name());
            fabosJsonDao.mergeAndFlush(entity);
        }
        return "msg.success('操作成功')";
    }
}
