package cec.jiutian.bc.productReturnInspection.domain.inspectionTask.handler;

import cec.jiutian.bc.materialInspect.service.SpcProcessDataService;
import cec.jiutian.bc.productReturnInspection.domain.inspectionTask.model.ProductReturnInspectionTask;
import cec.jiutian.bc.productReturnInspection.domain.inspectionTask.model.ProductReturnTerminationInspectionTask;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/15
 * @description TODO
 */
@Component
public class ExamineProReInsTaskHandler implements OperationHandler<ProductReturnInspectionTask, ProductReturnTerminationInspectionTask> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private SpcProcessDataService spcProcessDataService;

    @Override
    public String exec(List<ProductReturnInspectionTask> data, ProductReturnTerminationInspectionTask modelObject, String[] param) {
        if (modelObject != null) {
            ProductReturnInspectionTask inspectionTask = data.get(0);
            inspectionTask.setInspectionResult(modelObject.getInspectionResult());
            inspectionTask.setUnqualifiedLevel(modelObject.getUnqualifiedLevel());
            inspectionTask.setCurrentState(OrderCurrentStateEnum.Enum.COMPLETE.name());
            inspectionTask.setDetectCompleteTime(new Date());
            fabosJsonDao.mergeAndFlush(inspectionTask);
            spcProcessDataService.createSpcProcessData(inspectionTask.getId());
        }
        return "alert(操作成功)";
    }
}
