package cec.jiutian.bc.materialInspect.domain.inspectionTask.mto;

import cec.jiutian.bc.materialInspect.domain.inspectionTask.proxy.DisIncomingInsTaskMTODataProxy;
import cec.jiutian.bc.modeler.enumration.TaskBusinessStateEnum;
import cec.jiutian.bc.processInspect.domain.publicInspectionTask.mto.ProcessUserForInsTaskMTO;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Getter;
import lombok.Setter;


@FabosJson(
        name = "分配任务",
        orderBy = "createTime desc",
        dataProxy = DisIncomingInsTaskMTODataProxy.class,
        power = @Power(add = false,export = false)
)
@Table(name = "mi_inspection_task")
@Entity
@Getter
@Setter
@TemplateType(type = "multiTable")
public class DistributeInsIncomingTaskMTO extends MetaModel {


    @FabosJsonField(
            views = @View(title = "任务单号"),
            edit = @Edit(title = "任务单号",
                    search = @Search,
                    readonly = @Readonly(add = true, edit = true)
            )
    )
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "检验计划编号"),
            edit = @Edit(title = "检验计划编号", show = false, readonly = @Readonly, search = @Search(vague = true))
    )
    private String inspectionPlanNumber;

    @FabosJsonField(
            views = @View(title = "工艺编码"),
            edit = @Edit(title = "工艺编码",readonly = @Readonly,
                    inputType = @InputType(length = 40))
    )
    private String processCode;

    @FabosJsonField(
            views = @View(title = "工序编码"),
            edit = @Edit(title = "工序编码",readonly = @Readonly)
    )
    private String operationCode;

    @FabosJsonField(
            views = @View(title = "批次号"),
            edit = @Edit(title = "批次号",readonly = @Readonly)
    )
    private String actualLotSerialId;


    @FabosJsonField(
            views = @View(title = "任务状态"),
            edit = @Edit(title = "任务状态",
                    readonly = @Readonly(add = true, edit = true),
                    search = @Search,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = TaskBusinessStateEnum.class)
            )
    )
    private String businessState;

    @Transient
    @FabosJsonField(
            views = @View(title = "选择检验员",show = false, column = "name"),
            edit = @Edit(title = "选择检验员",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType()
            )
    )
    private ProcessUserForInsTaskMTO user;

    @FabosJsonField(
            views = @View(title = "检验人id",show = false),
            edit = @Edit(title = "检验人id", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "user", beFilledBy = "id"))
    )
    private String userId;

    @FabosJsonField(
            views = @View(title = "检验人"),
            edit = @Edit(title = "检验人",
                    notNull = true,
                    readonly = @Readonly,
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "user", beFilledBy = "name"))
    )
    private String userName;
}
