package cec.jiutian.bc.inventoryInspection.domain.inspectionTask.handler;

import cec.jiutian.bc.basicData.domain.inspectionStandard.model.InspectionStandard;
import cec.jiutian.bc.basicData.service.BasicDataService;
import cec.jiutian.bc.generalModeler.enumeration.YesOrNoEnum;
import cec.jiutian.bc.inventoryInspection.domain.inspectionTask.model.InventoryInspectionStandardDetail;
import cec.jiutian.bc.inventoryInspection.domain.inspectionTask.model.InventoryInspectionTask;
import cec.jiutian.bc.modeler.domain.samplingPlan.model.SamplingPlan;
import cec.jiutian.bc.modeler.enumration.SamplingStandardEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/4/25
 * @description TODO
 */
@Component
public class InventoryTotalSampleQuantityDynamicHandler implements DependFiled.DynamicHandler<InventoryInspectionTask> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private BasicDataService basicDataService;

    @Override
    public Map<String, Object> handle(InventoryInspectionTask inventoryInspectionTask) {
        Map<String, Object> result = new HashMap<>();
        double allInspectionItemQuantity = 0D;
        if (CollectionUtils.isNotEmpty(inventoryInspectionTask.getStandardDetailList())) {
            for (InventoryInspectionStandardDetail standardDetail : inventoryInspectionTask.getStandardDetailList()) {
                SamplingPlan samplingPlan = fabosJsonDao.getById(SamplingPlan.class, standardDetail.getSamplingPlan().getId());
                standardDetail.setSampleSize(basicDataService.getSampleQuantity(inventoryInspectionTask.getArrivalQuantity(), samplingPlan));
                //基础数据管理-检验项目没有选择抽样方案的话，样本量设置为零
                if (samplingPlan == null) {
                    standardDetail.setSampleSize(0D);
                } else {
                    //全检不加上去
                    if (!samplingPlan.getSamplingStandard().equals(SamplingStandardEnum.Enum.fullSampling.name())) {
                        allInspectionItemQuantity = allInspectionItemQuantity + standardDetail.getSampleSize();
                    }
                }
            }
        }

        // 加上留样数量作为总取样数量
        if (inventoryInspectionTask.getInspectionStandard() != null && StringUtils.isNotBlank(inventoryInspectionTask.getInspectionStandard().getId())) {
            InspectionStandard inspectionStandard = fabosJsonDao.getById(InspectionStandard.class, inventoryInspectionTask.getInspectionStandard().getId());
            if (YesOrNoEnum.Enum.Y.name().equals(inspectionStandard.getKeepSampleFlag())) {
                allInspectionItemQuantity = allInspectionItemQuantity +inspectionStandard.getSampleQuantity();
            }
        }

        result.put("allInspectionItemQuantity",allInspectionItemQuantity);
        result.put("standardDetailList",inventoryInspectionTask.getStandardDetailList());
        return result;
    }
}
