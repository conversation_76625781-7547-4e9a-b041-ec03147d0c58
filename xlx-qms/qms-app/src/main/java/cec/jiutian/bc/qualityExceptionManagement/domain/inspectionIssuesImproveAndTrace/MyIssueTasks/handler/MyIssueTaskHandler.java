package cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.MyIssueTasks.handler;

import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.type.RowBaseOperation;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

@FabosJson(
        name = "我的整改任务",
        orderBy = "RectificationTasksHandler.createTime desc",
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "status != 'Invalid'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "status != 'Invalid'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                )
        }
)
@Table(name = "os_rectification_tasks",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Getter
@Setter
@TemplateType(type = "multiTable")
public class MyIssueTaskHandler extends MetaModel {


}
