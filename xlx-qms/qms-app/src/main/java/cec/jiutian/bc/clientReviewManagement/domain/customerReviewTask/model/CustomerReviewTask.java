package cec.jiutian.bc.clientReviewManagement.domain.customerReviewTask.model;

import cec.jiutian.bc.clientReviewManagement.domain.customerReviewTask.handler.*;
import cec.jiutian.bc.clientReviewManagement.domain.customerReviewTask.proxy.CustomerReviewTaskDataProxy;
import cec.jiutian.bc.clientReviewManagement.enumeration.CustomerReviewNatureEnum;
import cec.jiutian.bc.processInspect.domain.publicInspectionTask.mto.ProcessUserForInsTaskMTO;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.*;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowBaseOperation;
import cec.jiutian.view.type.RowOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.util.List;

@Entity
@Table(name = "qms_client_review_task",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        })
@Getter
@Setter
@FabosJson(
        name = "客户评审任务",
        dataProxy = CustomerReviewTaskDataProxy.class,
        orderBy = "generalCode desc",
        power = @Power(export = false),
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "currentState !='Edit'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "currentState !='Edit'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
        },
        rowOperation = {
                @RowOperation(
                        title = "资料上传",
                        code = "CustomerReviewTask@UPLOAD",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        operationHandler = ReviewUploadOprHandler.class,
                        fabosJsonClass = CustomerReviewTaskUpload.class,
                        ifExpr = "selectedItems[0].currentState !='Edit'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "CustomerReviewTask@UPLOAD"
                        )
                ),
                @RowOperation(
                        title = "提交",
                        code = "CustomerReviewTask@SUBMIT",
                        mode = RowOperation.Mode.HEADER,
                        operationHandler = CustomerReviewSubmitHandler.class,
                        callHint = "请确认是否提交？",
                        ifExpr = "selectedItems[0].currentState !='WaitSubmit'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "CustomerReviewTask@SUBMIT"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
                @RowOperation(
                        title = "执行",
                        code = "CustomerReviewTask@EXECUTE",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        operationHandler = CustomerReviewTaskExecuteOperationHandler.class,
                        fabosJsonClass = CustomerReviewTaskDetailExec.class,
                        popupType = RowOperation.PopupType.FORM,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "CustomerReviewTask@EXECUTE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "selectedItems[0].currentState !='WaitExecute'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),

        }
)
@TemplateType(type = "multiTable")
public class CustomerReviewTask extends MetaModel {

    @FabosJsonField(
            views = @View(title = "评审任务单号"),
            edit = @Edit(title = "评审任务单号",
                    notNull = true,
                    search = @Search
            ),
            dynamicField = @DynamicField(dependFiled = @DependFiled(buttonName = "生成", changeBy = "id",
                    dynamicHandler = CustomerReviewGeneralCodeDynamicHandler.class))
    )
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "顾客名称"),
            edit = @Edit(title = "顾客名称", notNull = true, search = @Search(vague = true))
    )
    private String customerName;

    @FabosJsonField(
            views = @View(title = "审核目的"),
            edit = @Edit(title = "审核目的")
    )
    private String purpose;

    @Transient
    @FabosJsonField(
            views = @View(title = "选择审核人员", column = "name", show = false),
            edit = @Edit(title = "选择审核人员",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType()
            )
    )
    private ProcessUserForInsTaskMTO reviewerMTO;

    @FabosJsonField(
            views = @View(title = "审核人员ID", show = false),
            edit = @Edit(title = "审核人员ID", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "reviewerMTO", beFilledBy = "id"))
    )
    private String reviewerId;

    @FabosJsonField(
            views = @View(title = "审核人员"),
            edit = @Edit(title = "审核人员"),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "reviewerMTO", beFilledBy = "name"))
    )
    private String reviewerName;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "审核日期", type = ViewType.DATE),
            edit = @Edit(title = "审核日期", dateType = @DateType(type = DateType.Type.DATE))
    )
    private LocalDate reviewTime;


    // 资料上传备注
    @FabosJsonField(
            views = @View(title = "资料上传备注"),
            edit = @Edit(title = "资料上传备注",
                    type = EditType.TEXTAREA,
                    inputType = @InputType(length = 100)
            )
    )
    private String uploadNotes;


    @FabosJsonField(
            views = @View(title = "评审结果"),
            edit = @Edit(title = "评审结果", show = false)
    )
    private String reviewResult;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态", show = false, search = @Search,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = CustomerReviewNatureEnum.class))
    )
    private String currentState;

    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumn(name = "review_task_id")
    @OrderBy
    @FabosJsonField(
            edit = @Edit(title = "资料详情", type = EditType.TAB_REFER_ADD),
            views = @View(title = "资料详情", column = "name", type = ViewType.TABLE_VIEW, extraPK = "departmentId"),
            referenceAddType = @ReferenceAddType(referenceClass = "OrgMTO",
//                    filter = "ExamineItem.status = 'Effective'",
                    editable = {"informationRequirement", "deliveryTime"},
                    referenceAddHandler = CustomerReviewTaskDetailAddHandler.class)
    )
    private List<CustomerReviewTaskDetail> details;

    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumn(name = "review_task_id")
    @OrderBy
    @FabosJsonField(
            edit = @Edit(title = "执行详情", type = EditType.TAB_REFERENCE_GENERATE, show = false),
            views = @View(title = "执行详情", type = ViewType.TABLE_VIEW)
    )
    private List<CustomerReviewTaskDetailProblemExec> execDetails;

}
