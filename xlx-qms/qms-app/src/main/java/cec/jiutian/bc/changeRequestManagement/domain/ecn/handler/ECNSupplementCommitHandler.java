package cec.jiutian.bc.changeRequestManagement.domain.ecn.handler;

import cec.jiutian.bc.changeRequestManagement.domain.ecn.model.ChangeRequestExecute;
import cec.jiutian.bc.changeRequestManagement.enums.ECRStatusEnum;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Component
public class ECNSupplementCommitHandler implements OperationHandler<ChangeRequestExecute, ChangeRequestExecute> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Transactional
    @Override
    public String exec(List<ChangeRequestExecute> data, ChangeRequestExecute modelObject, String[] param) {
        if (CollectionUtils.isEmpty(data) || data.get(0).getId() == null) {
            throw new ServiceException("数据异常");
        }
        ChangeRequestExecute changeRequestExecute = data.get(0);
        changeRequestExecute.setStatus(ECRStatusEnum.Enum.SUPPLEMENT_EXECUTING.name());
        fabosJsonDao.persistAndFlush(changeRequestExecute);
        return "提交成功";
    }
}
