package cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.mto;

import cec.jiutian.bc.materialInspect.domain.publicInspectionTask.mto.UserForInsTaskMTO;
import cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.enumration.AuditStatusEnum;
import cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.enumration.TaskBusinessStateEnum;
import cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.proxy.AbnormalFeedbackHandlingCreateMTODataProxy;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.Entity;
import jakarta.persistence.Transient;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/4/27
 * @description TODO
 */
@FabosJson(
        name = "评审"
)
@Entity
@Getter
@Setter
public class AbnormalFeedbackHandlingReviewMTO extends MetaModel {

    @FabosJsonField(
            views = @View(title = "评审意见"),
            edit = @Edit(title = "评审意见",
                    notNull = true,
                    type = EditType.CHOICE, search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = AuditStatusEnum.class))
    )
    private String reviewComments;

    @FabosJsonField(
            views = @View(title = "意见说明"),
            edit = @Edit(title = "意见说明",
                    type = EditType.TEXTAREA)
    )
    private String opinionExplanation;

    @FabosJsonField(
            views = @View(title = "评审附件"),
            edit = @Edit(title = "评审附件", type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType)
    )
    private String reviewCommentsAttachments;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",
                    show = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = TaskBusinessStateEnum.class)
            )
    )
    private String businessState;
}
