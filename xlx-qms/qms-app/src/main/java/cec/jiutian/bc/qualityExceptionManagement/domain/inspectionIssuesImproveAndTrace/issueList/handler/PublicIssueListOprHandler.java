package cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.issueList.handler;

import cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.issueList.model.IssueList;
import cec.jiutian.bc.qualityExceptionManagement.enumeration.inspectionIssuesImproveAndTrace.IssueListStatusEnum;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.transaction.Transactional;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Component
public class PublicIssueListOprHandler implements OperationHandler<IssueList, Void> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    @Transactional
    public String exec(List<IssueList> data, Void modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            IssueList issueList = data.get(0);
            if (Objects.isNull(issueList.getMetaUser()) || Objects.isNull(issueList.getOrg())) {
                throw new FabosJsonApiErrorTip("请先填写责任人和责任部门后，再进行问题发布");
            }
            issueList.setStatus(IssueListStatusEnum.Enum.WAIT_RUN.name());
            fabosJsonDao.mergeAndFlush(issueList);
        }
        return "msg.success('操作成功')";
    }
}
