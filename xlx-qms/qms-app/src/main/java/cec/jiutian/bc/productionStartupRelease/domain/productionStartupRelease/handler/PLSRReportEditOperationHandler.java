package cec.jiutian.bc.productionStartupRelease.domain.productionStartupRelease.handler;

import cec.jiutian.bc.productionStartupRelease.domain.productionStartupRelease.model.PLSRReport;
import cec.jiutian.bc.productionStartupRelease.domain.productionStartupRelease.mto.PLSRReportAddMTO;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.view.fun.OperationHandler;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class PLSRReportEditOperationHandler implements OperationHandler<PLSRReport, PLSRReportAddMTO> {


    @Override
    public PLSRReportAddMTO fabosJsonFormValue(List<PLSRReport> data, PLSRReportAddMTO fabosJsonForm, String[] param) {
        if (CollectionUtils.isEmpty(data)) {
            throw new ServiceException("请选择操作数据");
        }
        PLSRReport plsrReport = data.get(0);

        BeanUtils.copyProperties(plsrReport, fabosJsonForm);
        return fabosJsonForm;
    }
}
