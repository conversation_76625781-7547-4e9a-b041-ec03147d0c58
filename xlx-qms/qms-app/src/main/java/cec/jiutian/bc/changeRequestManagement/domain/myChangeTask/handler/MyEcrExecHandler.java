package cec.jiutian.bc.changeRequestManagement.domain.myChangeTask.handler;

import cec.jiutian.bc.changeRequestManagement.domain.myChangeTask.model.MyECRItem;
import cec.jiutian.bc.changeRequestManagement.domain.myChangeTask.model.MyChangeTask;
import cec.jiutian.bc.changeRequestManagement.domain.myChangeTask.model.MyEcrExec;
import cec.jiutian.bc.changeRequestManagement.domain.myChangeTask.model.MyExecECRItem;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Component
public class MyEcrExecHandler implements OperationHandler<MyChangeTask, MyEcrExec> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<MyChangeTask> data, MyEcrExec modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            modelObject.getMyExecECRItems().forEach(myExecECRItem -> {
                MyECRItem myECRItem = fabosJsonDao.findById(MyECRItem.class, myExecECRItem.getId());
                myECRItem.setProgress(myExecECRItem.getProgress());
                myECRItem.setCompletionDate(myExecECRItem.getChangeTaskCompletionDate());
                myECRItem.setAttachment(myExecECRItem.getAttachment());
                fabosJsonDao.mergeAndFlush(myECRItem);
            });
        }
        return "msg.success('操作成功')";
    }

    @Override
    public MyEcrExec fabosJsonFormValue(List<MyChangeTask> data, MyEcrExec fabosJsonForm, String[] param) {
        // data为空 直接返回
        if (CollectionUtils.isEmpty(data)) {
            return fabosJsonForm;
        }

        String userId = UserContext.getUserId();
        MyChangeTask myChangeTask = data.get(0);
        BeanUtil.copyProperties(myChangeTask, fabosJsonForm);

        //如果list为空 直接返回
        if (CollectionUtils.isEmpty(myChangeTask.getMyECRItems())) {
            return fabosJsonForm;
        }

        List<MyExecECRItem> myExecECRItemList = new ArrayList<>();
        myChangeTask.getMyECRItems().forEach(d->{
            if (Objects.equals(d.getResponsiblePersonId(), userId)) {
                MyExecECRItem exec = new MyExecECRItem();
                BeanUtil.copyProperties(d, exec);
                myExecECRItemList.add(exec);
            }
        });

        fabosJsonForm.setMyExecECRItems(myExecECRItemList);
        return fabosJsonForm;
    }
}
