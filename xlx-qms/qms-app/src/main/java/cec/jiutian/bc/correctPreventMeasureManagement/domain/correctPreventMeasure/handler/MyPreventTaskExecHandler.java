package cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.handler;

import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.model.*;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.mto.MyPreventTaskExecMTO;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class MyPreventTaskExecHandler implements OperationHandler<MyPreventTask, MyPreventTaskExecMTO> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<MyPreventTask> data, MyPreventTaskExecMTO modelObject, String[] param) {
        if (CollectionUtils.isEmpty(data)) {
            return "msg.success('操作成功')";
        }
        modelObject.getPreventMeasureList().forEach(d -> {
            PreventMeasure preventMeasure = fabosJsonDao.findById(PreventMeasure.class, d.getId());
            preventMeasure.setImprovingProgress(d.getImprovingProgress());
            preventMeasure.setCompletionDate(d.getCompletionDate());
            preventMeasure.setCompleteSupportMaterial(d.getCompleteSupportMaterial());
            fabosJsonDao.mergeAndFlush(preventMeasure);
        });
        return "msg.success('操作成功')";
    }

    /*@Override
    public MyPreventTaskExecMTO fabosJsonFormValue(List<MyPreventTask> data, MyPreventTaskExecMTO fabosJsonForm, String[] param) {
        // data为空 直接返回
        if (CollectionUtils.isEmpty(data)) {
            return fabosJsonForm;
        }

        String userId = UserContext.getUserId();
        MyPreventTask myPreventTask = data.get(0);
        BeanUtils.copyProperties(myPreventTask, fabosJsonForm);

        //如果list为空 直接返回
        if (CollectionUtils.isEmpty(myPreventTask.getPreventMeasureList())) {
            return fabosJsonForm;
        }

        List<MyPreventMeasure> myPreventMeasures = new ArrayList<>();
        myPreventTask.getPreventMeasureList().forEach(d->{
            if (Objects.equals(d.getUserForInsTaskMTO().getId(), userId)) {
                MyPreventMeasure myPreventMeasure = new MyPreventMeasure();
                BeanUtils.copyProperties(d, myPreventMeasure);
                myPreventMeasures.add(myPreventMeasure);
            }
        });

        fabosJsonForm.setPreventMeasureList(myPreventMeasures);
        return fabosJsonForm;
    }*/
}
