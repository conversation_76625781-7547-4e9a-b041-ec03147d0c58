package cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.handler;

import cec.jiutian.bc.mto.ProcessOperationMTO;
import cec.jiutian.bc.mto.TechnologyFLowMTO;
import cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.mto.PqtProcessOperationCreateMTO;
import cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.mto.PqtProductBatchNumberCreateMTO;
import cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.mto.ProductQualityTrackCreateMTO;
import cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.service.ProductQualityTrackDealService;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class PqtProcessOperationListHandler implements DependFiled.DynamicHandler<ProductQualityTrackCreateMTO> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private ProductQualityTrackDealService dealService;

    @Override
    public Map<String, Object> handle(ProductQualityTrackCreateMTO model) {
        HashMap<String, Object> resultMap = new HashMap<>();
        List<PqtProcessOperationCreateMTO> pqtProcessOperationList = new ArrayList<>();
        resultMap.put("pqtProcessOperationList", pqtProcessOperationList);
        if (model != null) {
            if (model.getFactoryLine() == null) {
                return resultMap;
            } else {
                dealService.disposeProcessOperationList(model, pqtProcessOperationList);
            }
        }
        return resultMap;
    }
}
