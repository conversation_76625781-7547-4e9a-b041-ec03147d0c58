package cec.jiutian.bc.samplingTaskManagement.domain.otherSamplingTask.handler;

import cec.jiutian.bc.modeler.enumration.TaskBusinessStateEnum;
import cec.jiutian.bc.samplingTaskManagement.domain.otherSamplingTask.model.OtherSamplingTask;
import cec.jiutian.bc.samplingTaskManagement.domain.otherSamplingTask.model.OtherSamplingTaskOpr;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/12
 * @description TODO
 */
@Component
public class SendOtherSampleOperationHandler implements OperationHandler<OtherSamplingTask, OtherSamplingTaskOpr> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<OtherSamplingTask> data, OtherSamplingTaskOpr modelObject, String[] param) {
        if (modelObject != null) {
            OtherSamplingTask condition = new OtherSamplingTask();
            condition.setGeneralCode(modelObject.getGeneralCode());
            OtherSamplingTask otherSamplingTask = fabosJsonDao.selectOne(condition);
            if (otherSamplingTask == null) {
                throw new FabosJsonApiErrorTip("未查询到取样单，请确认");
            }
            if (!TaskBusinessStateEnum.Enum.SAMPLING_FINISH.name().equals(otherSamplingTask.getBusinessState())) {
                throw new FabosJsonApiErrorTip("取样任务单状态有误");
            }
            otherSamplingTask.setBusinessState(TaskBusinessStateEnum.Enum.SEND_SAMPLE.name());
            otherSamplingTask.setSendSamplePersonId(modelObject.getSendSamplePersonId());
            otherSamplingTask.setSendSamplePerson(modelObject.getSendSamplePerson());
            otherSamplingTask.setSendSampleDate(modelObject.getSendSampleDate());
            fabosJsonDao.mergeAndFlush(otherSamplingTask);
        }
        return "alert(操作成功)";
    }
}
