package cec.jiutian.bc.qualityExceptionManagement.domain.returnProductHandling.proxy;

import cec.jiutian.bc.generalModeler.enumeration.YesOrNoEnum;
import cec.jiutian.bc.qualityExceptionManagement.domain.returnProductHandling.model.ReturnProductHandlingAnalyse;
import cec.jiutian.bc.qualityExceptionManagement.enums.ReturnProductHandlingStatusEnum;
import cec.jiutian.view.fun.DataProxy;

public class ReturnProductHandlingAnalyseDataProxy implements DataProxy<ReturnProductHandlingAnalyse> {

    @Override
    public void beforeUpdate(ReturnProductHandlingAnalyse entity) {
        if (entity.getHandleFlag().equals(YesOrNoEnum.Enum.Y.name())) {
            entity.setStatus(ReturnProductHandlingStatusEnum.Enum.WaitHandle.name());
        } else {
            entity.setStatus(ReturnProductHandlingStatusEnum.Enum.WaitReview.name());
        }
    }

}
