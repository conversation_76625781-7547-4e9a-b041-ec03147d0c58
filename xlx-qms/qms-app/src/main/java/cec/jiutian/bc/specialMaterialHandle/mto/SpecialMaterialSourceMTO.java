package cec.jiutian.bc.specialMaterialHandle.mto;

import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/5/22
 * @description TODO
 */
@Entity
@Table(name = "special_material_source")
@Getter
@FabosJson(
        name = "特殊物资处理-物资来源",
        power = @Power(add = false, edit = false, delete = false, print = false, importable = false)
)
public class SpecialMaterialSourceMTO {

    @Id
    @FabosJsonField(
            edit = @Edit(title = "", show = false)
    )
    @Column(name = "id")
    private String id;

    @FabosJsonField(
            views = @View(title = "来源"),
            edit = @Edit(title = "来源")
    )
    private String source;

    @FabosJsonField(
            views = @View(title = "来源单号"),
            edit = @Edit(title = "来源单号")
    )
    private String orderCode;

    @FabosJsonField(
            views = @View(title = "批次号"),
            edit = @Edit(title = "批次号")
    )
    private String lotSerialId;

    @FabosJsonField(
            views = @View(title = "物资编码"),
            edit = @Edit(title = "物资编码",
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "material_code", length = 40)
    private String materialCode;

    @FabosJsonField(
            views = @View(title = "物资名称"),
            edit = @Edit(title = "物资名称",
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "material_name", length = 40)
    private String materialName;

    @FabosJsonField(
            views = @View(title = "库存数量"),
            edit = @Edit(title = "库存数量")
    )
    @Column(name = "inventory_quantity")
    private Double inventoryQuantity;

    @FabosJsonField(
            views = @View(title = "单位"),
            edit = @Edit(title = "单位")
    )
    private String unit;

    @FabosJsonField(
            views = @View(title = "物资分级"),
            edit = @Edit(title = "物资分级")
    )
    @Column(name = "material_level")
    private String materialLevel;
}
