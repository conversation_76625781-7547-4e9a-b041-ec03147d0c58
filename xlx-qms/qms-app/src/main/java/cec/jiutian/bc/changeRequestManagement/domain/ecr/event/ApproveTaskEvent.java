package cec.jiutian.bc.changeRequestManagement.domain.ecr.event;

import cec.jiutian.bc.changeRequestManagement.domain.bo.FlowAssigneeConfig;
import cec.jiutian.bc.changeRequestManagement.domain.bo.FlowNodeBO;
import cec.jiutian.bc.changeRequestManagement.domain.ecn.model.ChangeRequestExecute;
import cec.jiutian.bc.changeRequestManagement.domain.ecn.model.MyECNItem;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.model.ApproveTask;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.model.ChangeRequest;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.model.ConfirmRequest;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.repository.ApproveTaskRepository;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.service.ChangeTraceService;
import cec.jiutian.bc.changeRequestManagement.domain.myChangeTask.model.MyChangeTask;
import cec.jiutian.bc.changeRequestManagement.domain.myChangeTask.model.MyECRItem;
import cec.jiutian.bc.changeRequestManagement.enums.*;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import jakarta.annotation.Resource;
import jakarta.persistence.LockModeType;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@Component
public class ApproveTaskEvent {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private ApproveTaskRepository approveTaskRepository;

    @Resource
    private FlowAssigneeConfig flowAssigneeConfig;

    private ChangeRequest queryChangeRequest(String changeRequestId) {
        return fabosJsonDao.findById(ChangeRequest.class, changeRequestId);
    }

    private MyChangeTask queryMyChangeTask(String changeRequestId) {
        return fabosJsonDao.findById(MyChangeTask.class, changeRequestId);
    }

    private void changeRequestStatus(String changeRequestId, String status) {
        ChangeRequest changeRequest = queryChangeRequest(changeRequestId);
        changeRequest.setStatus(status);
        fabosJsonDao.updateAndFlush(changeRequest);
    }

    private void changeMyChangeTaskStatus(String changeRequestId, String status) {
        MyChangeTask myChangeTask = queryMyChangeTask(changeRequestId);
        myChangeTask.setStatus(status);
        List<MyECRItem> myECRItems = myChangeTask.getMyECRItems();
        if (CollectionUtils.isNotEmpty(myECRItems)) {
            for (MyECRItem myECRItem : myECRItems) {
                myECRItem.setProgress(ProgressEnum.Enum.PENDING_EXECUTION.name());
            }
        }
        fabosJsonDao.updateAndFlush(myChangeTask);
    }

    public void onEvent(ApproveTask approveTask) {
        switch (approveTask.getResult()) {
            case "PASS" -> {
                onPassEvent(approveTask);
                break;
            }
            case "REJECT" -> {
                onRejectEvent(approveTask);
                break;
            }
            default -> {
                throw new ServiceException("无效的操作");
            }
        }
    }

    private void onPassEvent(ApproveTask approveTask) {
        FlowNodeBO.Node node = FlowNodeBO.getNode(approveTask.getCode());
        switch (node) {
            case EXAMINE:
                doExamineChange(approveTask.getBusinessKey(), ECRStatusEnum.Enum.WAIT_REVIEW.name());
                break;
            case REVIEW:
                if (countersign(approveTask)) {
                    changeRequestStatus(approveTask.getBusinessKey(), ECRStatusEnum.Enum.WAIT_PLAN.name());
                }
                break;
            case APPROVE_1:
                createApprove2ExamineTask(approveTask);
                break;
            case APPROVE_2:
                changeRequestStatus(approveTask.getBusinessKey(), ECRStatusEnum.Enum.EXECUTING.name());
                break;
            case VERIFY_REVIEW:
                if (countersign(approveTask)) {
                    onVerifyReviewPass(approveTask);
                }
                break;
            case VERIFY_REVIEW_A:
                onVerifyAPass(approveTask);
                break;
            case VERIFY_APPROVE:
                onVerifyApprovePass(approveTask);
                break;
            case WAIT_CLOSING:
            case SUPPLEMENT_VERIFY:
                onCompletePass(approveTask);
                break;

        }
    }

    private void onRejectEvent(ApproveTask approveTask) {
        FlowNodeBO.Node node = FlowNodeBO.getNode(approveTask.getCode());
        switch (node) {
            case EXAMINE:
                doExamineChange(approveTask.getBusinessKey(), ECRStatusEnum.Enum.WAIT_SUBMIT.name());
                break;
            case REVIEW:
                onCountersignRejected(approveTask);
                changeRequestStatus(approveTask.getBusinessKey(), ECRStatusEnum.Enum.WAIT_SUBMIT.name());
                break;
            case APPROVE_1:
                changeRequestStatus(approveTask.getBusinessKey(), ECRStatusEnum.Enum.WAIT_PLAN.name());
                break;
            case APPROVE_2:
                changeRequestStatus(approveTask.getBusinessKey(), ECRStatusEnum.Enum.WAIT_PLAN.name());
                break;
            case VERIFY_REVIEW:
                onCountersignRejected(approveTask);
                changeMyChangeTaskStatus(approveTask.getBusinessKey(), ECRStatusEnum.Enum.EXECUTING.name());
                break;
            case VERIFY_REVIEW_A:
                changeMyChangeTaskStatus(approveTask.getBusinessKey(), ECRStatusEnum.Enum.EXECUTING.name());
                break;
            case VERIFY_APPROVE:
                changeMyChangeTaskStatus(approveTask.getBusinessKey(), ECRStatusEnum.Enum.EXECUTING.name());
                break;
            case WAIT_CLOSING:
                changeRequestStatus(approveTask.getBusinessKey(), ECRStatusEnum.Enum.WAIT_SUPPLEMENT.name());
                break;
            case SUPPLEMENT_VERIFY:
                changeRequestStatus(approveTask.getBusinessKey(), ECRStatusEnum.Enum.WAIT_SUPPLEMENT.name());
                break;

        }
    }

    public void onVerifyReviewPass(ApproveTask approveTask) {
        ChangeRequestExecute changeRequestExecute = fabosJsonDao.getEntityManager().find(ChangeRequestExecute.class, approveTask.getBusinessKey(), LockModeType.PESSIMISTIC_WRITE);
        if (ChangeLevel.Enum.A.name().equals(changeRequestExecute.getChangeLevel())) {
            //创建A类验证审批
            Integer maxTurn = approveTaskRepository.findMaxTurnByBusinessKeyAndCode(approveTask.getBusinessKey(), FlowNodeBO.Node.VERIFY_REVIEW_A.name());
            if (maxTurn == null) {
                maxTurn = 0;
            }else {
                maxTurn++;
            }

            List<String> assigneeIds = flowAssigneeConfig.getAssigneeIds(FlowNodeBO.Node.VERIFY_REVIEW_A.name());
            ApproveTask verifyATask = ApproveTask.createVerifyATask(approveTask.getBusinessKey(), assigneeIds.get(0), maxTurn);
            fabosJsonDao.persistAndFlush(verifyATask);
        } else {
            onVerifyAPass(approveTask);
        }
    }

    private void onVerifyAPass(ApproveTask approveTask) {
        //创建验证批准任务
        Integer maxTurn = approveTaskRepository.findMaxTurnByBusinessKeyAndCode(approveTask.getBusinessKey(), FlowNodeBO.Node.VERIFY_APPROVE.name());
        if (maxTurn == null) {
            maxTurn = 0;
        }else {
            maxTurn++;
        }
        List<String> roleIds = flowAssigneeConfig.getAssigneeIds(FlowNodeBO.Node.VERIFY_APPROVE.name());
        for (String roleId : roleIds) {
            ApproveTask verifyApproveTask = ApproveTask.createVerifyApproveTask(approveTask.getBusinessKey(), roleId, maxTurn);
            fabosJsonDao.persistAndFlush(verifyApproveTask);
        }
    }

    private void onVerifyApprovePass(ApproveTask approveTask) {
        ChangeRequest changeRequest = queryChangeRequest(approveTask.getBusinessKey());
        changeRequest.setStatus(ECRStatusEnum.Enum.WAIT_CLOSING.name());
        String applyApproveId = changeRequest.getApplyApproveId();
        Integer maxTurn = approveTaskRepository.findMaxTurnByBusinessKeyAndCode(approveTask.getBusinessKey(), FlowNodeBO.Node.WAIT_CLOSING.name());
        if (maxTurn == null) {
            maxTurn = 0;
        }else {
            maxTurn++;
        }

        ApproveTask confirmTask = ApproveTask.createCloseConfirmTask(approveTask.getBusinessKey(), applyApproveId, maxTurn);
        fabosJsonDao.persistAndFlush(confirmTask);
    }


    @Resource
    private ChangeTraceService changeTraceService;

    private void onCompletePass(ApproveTask approveTask) {
        MyChangeTask myChangeTask = queryMyChangeTask(approveTask.getBusinessKey());
        myChangeTask.setStatus(ECRStatusEnum.Enum.COMPLETE.name());
        myChangeTask.setEndDate(new Date());
        List<MyECRItem> myECRItems = myChangeTask.getMyECRItems();
        if (CollectionUtils.isNotEmpty(myECRItems)) {
            for (MyECRItem myECRItem : myECRItems) {
                myECRItem.setProgress(ProgressEnum.Enum.COMPLETED.name());
                fabosJsonDao.updateAndFlush(myECRItem);
            }
        }
        List<MyECNItem> myECNItems = myChangeTask.getMyECNItems();
        if (CollectionUtils.isNotEmpty(myECNItems)) {
            for (MyECNItem myECNItem : myECNItems) {
                myECNItem.setProgress(ProgressEnum.Enum.COMPLETED.name());
                fabosJsonDao.updateAndFlush(myECNItem);
            }
        }
        fabosJsonDao.updateAndFlush(myChangeTask);
        changeTraceService.pullData(approveTask.getBusinessKey());
//        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
//            changeTraceService.pullData(approveTask.getBusinessKey());
//        });
    }

    private void createApprove2ExamineTask(ApproveTask approveTask) {
        ChangeRequest changeRequest = queryChangeRequest(approveTask.getBusinessKey());
        ConfirmRequest confirmRequest = changeRequest.getConfirmRequest();

        Integer maxTurn = approveTaskRepository.findMaxTurnByBusinessKeyAndCode(changeRequest.getId(), FlowNodeBO.Node.APPROVE_2.name());
        if (maxTurn == null) {
            maxTurn = 0;
        }else {
            maxTurn++;
        }

        ApproveTask confirmTask = ApproveTask.createApprove2ExamineTask(approveTask.getBusinessKey(), confirmRequest.getConfirmBy(), maxTurn);
        fabosJsonDao.persistAndFlush(confirmTask);
    }

    /**
     * 会签节点，一票否决
     *
     * @param approveTask
     * @return
     */
    private boolean countersign(ApproveTask approveTask) {
        List<ApproveTask> tasks = approveTaskRepository.findByBusinessKeyAndCodeMaxTurn(approveTask.getBusinessKey(), approveTask.getCode());
        for (ApproveTask task : tasks) {
            if (task.getResult() == null || !ApproveResultEnum.Enum.PASS.name().equals(task.getResult())) {
                return false;
            }
        }
        return true;
    }

    private void onCountersignRejected(ApproveTask approveTask) {
        List<ApproveTask> tasks = approveTaskRepository.findByBusinessKeyAndCodeAndStatus(approveTask.getBusinessKey(),
                approveTask.getCode(), ApproveTaskStatusEnum.Enum.WAIT_APPROVE.name());
        for (ApproveTask task : tasks) {
            task.setStatus(ApproveTaskStatusEnum.Enum.SKIPPED.name());
            fabosJsonDao.updateAndFlush(task);
        }
    }


    private void doExamineChange(String ecrId, String status) {
        String userId = UserContext.getUserId();
        String userName = UserContext.getUserName();
        if (StringUtils.isBlank(userName) || StringUtils.isBlank(userId)) {
            throw new ServiceException("用户未登录");
        }
        ChangeRequest changeRequest = queryChangeRequest(ecrId);
        changeRequest.setStatus(status);
        changeRequest.setApplyApproveId(userId);
        changeRequest.setApplyApprover(userName);
        fabosJsonDao.mergeAndFlush(changeRequest);
    }
}
