package cec.jiutian.bc.changeRequestManagement.domain.ecr.model;

import cec.jiutian.bc.changeRequestManagement.domain.ecn.model.ECNItem;
import cec.jiutian.bc.changeRequestManagement.domain.ecn.model.VerificationIDetail;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.dto.ECRSubmitDTO;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.handler.*;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.mto.ApproveDetail;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.mto.ApproveViewMTO;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.mto.ChangeRequestAddItemMTO;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.mto.formDetail.ECRDetailFormMTO;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.proxy.ChangeRequestDataProxy;
import cec.jiutian.bc.changeRequestManagement.enums.ChangeLevel;
import cec.jiutian.bc.changeRequestManagement.enums.ChangeType;
import cec.jiutian.bc.changeRequestManagement.enums.ECRStatusEnum;
import cec.jiutian.bc.changeRequestManagement.enums.FileStandardEnum;
import cec.jiutian.bc.materialInspect.domain.publicInspectionTask.mto.UserForInsTaskMTO;
import cec.jiutian.bc.mto.OrgMTO;
import cec.jiutian.bc.mto.FactoryArea;
import cec.jiutian.bc.mto.ProductProcessMTO;
import cec.jiutian.bc.mto.SpecificationManageMTO;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.module.QMSBaseNamingRuleModel;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowBaseOperation;
import cec.jiutian.view.type.RowOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@Entity
@Table(name = "qms_change_request",
        indexes = {
                @Index(name = "idx_group_code", columnList = "general_code", unique = true)
        })
@FabosJson(
        name = "ECR",
        orderBy = "ChangeRequest.generalCode desc",
        dataProxy = ChangeRequestDataProxy.class,
        power = @Power(examine = false,viewDetails = false, examineDetails = false, export = false, importable = false),
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "status !='WAIT_SUBMIT'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "status !='WAIT_SUBMIT'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
        },
        rowOperation = {
                @RowOperation(
                        title = "详情",
                        code = "ChangeRequest@DETAIL",
                        operationHandler = ECRDetailFormHandler.class,
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = ECRDetailFormMTO.class,
                        show = @ExprBool(
                                params = "ChangeRequest@DETAIL",
                                exprHandler = UserRowOperationExprHandler.class
                        )
                ),

                @RowOperation(
                        title = "查看审批详情",
                        code = "ChangeRequest@ApproveDetail",
                        ifExpr = "selectedItems[0].status =='WAIT_SUBMIT'", //禁用按钮
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = ApproveDetail.class,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        operationHandler = ApproveDetailViewHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "ChangeRequest@ApproveDetail"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
                @RowOperation(
                        title = "提交",
                        code = "ChangeRequest@COMMIT",
                        ifExpr = "selectedItems[0].status !='WAIT_SUBMIT'", //禁用按钮
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = ECRSubmitDTO.class,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        operationHandler = ECRCommitHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "ChangeRequest@COMMIT"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
                @RowOperation(
                        title = "发起评审",
                        code = "ChangeRequest@Reveiw",
                        ifExpr = "selectedItems[0].status !='WAIT_REVIEW'", //禁用按钮
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = ApproveViewMTO.class,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        operationHandler = ECRCommitReviewHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "ChangeRequest@Reveiw"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
                @RowOperation(
                        title = "录入变更事项",
                        code = "ChangeRequest@ENTERCHANGEITEM",
                        ifExpr = "selectedItems[0].status != 'WAIT_PLAN'", //禁用按钮
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = ChangeRequestAddItemMTO.class,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        operationHandler = ECREnterChangeItemHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "ChangeRequest@ENTERCHANGEITEM"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
                @RowOperation(
                        title = "计划发布",
                        code = "ChangeRequest@PUBLISHPLAN",
                        ifExpr = "selectedItems[0].status != 'WAIT_PLAN'", //禁用按钮
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
                        mode = RowOperation.Mode.HEADER,
                        callHint = "确认发布？",
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        operationHandler = ECRPublishPlanHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "ChangeRequest@PUBLISHPLAN"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
                @RowOperation(
                        title = "发起批准",
                        code = "ChangeRequest@CONFIRM",
                        ifExpr = "selectedItems[0].status !='WAIT_APPROVE'", //禁用按钮
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = ConfirmRequest.class,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        operationHandler = ECRConfirmHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "ChangeRequest@CONFIRM"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
        }
)
@TemplateType(type = "multiTable")
public class ChangeRequest extends QMSBaseNamingRuleModel {

    @FabosJsonField(
            views = @View(title = "变更主题"),
            edit = @Edit(
                    title = "变更主题",
                    inputType = @InputType(length = 20),
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "change_subject", length = 20)
    private String changeSubject;


    @FabosJsonField(
            views = @View(title = "变更类型"),
            edit = @Edit(
                    title = "变更类型",
                    inputType = @InputType(length = 20),
                    notNull = true,
                    search = @Search,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ChangeType.class)
            )
    )
    @Column(name = "change_type", length = 20)
    private String changeType;

    @FabosJsonField(
            views = @View(title = "变更级别"),
            edit = @Edit(
                    title = "变更级别",
                    inputType = @InputType(length = 20),
                    notNull = true,
                    search = @Search,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ChangeLevel.class)
            )
    )
    @Column(name = "change_level", length = 20)
    private String changeLevel;

    @Transient
    @FabosJsonField(
            views = @View(title = "车间", show = false, column = "factoryAreaName"),
            edit = @Edit(title = "车间",
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "factoryAreaName"),
                    filter = @Filter(value = "factoryAreaTypeCode = '02'")
            )
    )
    private FactoryArea factoryArea;

    @FabosJsonField(
            views = @View(title = "车间ID",show = false),
            edit = @Edit(title = "车间ID",
                    show = false
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "factoryArea", beFilledBy = "id"))
    )
    private Long factoryAreaId;

    @FabosJsonField(
            views = @View(title = "车间名称"),
            edit = @Edit(title = "车间名称",
                    readonly = @Readonly(add = true,edit = true),
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "factoryArea", beFilledBy = "factoryAreaName"))
    )
    private String factoryAreaName;

    @Transient
    @FabosJsonField(
            views = @View(title = "产线", show = false, column = "factoryAreaName"),
            edit = @Edit(title = "产线",
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "factoryAreaName"),
                    queryCondition = "{\"pid\": \"${factoryArea.id}\"}",
                    filter = @Filter(value = "factoryAreaTypeCode = '03'")
            )
    )
    private FactoryArea factoryLine;

    @FabosJsonField(
            views = @View(title = "产线Id",show = false),
            edit = @Edit(title = "产线Id",
                    show = false
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "factoryLine", beFilledBy = "id"))
    )
    private Long factoryLineId;

    @FabosJsonField(
            views = @View(title = "产线名称"),
            edit = @Edit(title = "产线名称",
                    search = @Search(vague = true),
                    readonly = @Readonly(add = true,edit = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "factoryLine", beFilledBy = "factoryAreaName"))
    )
    private String factoryLineName;

    @ManyToOne
    @JoinColumn(name = "product_process_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @FabosJsonField(
            views = @View(title = "工序",column = "name", type = ViewType.TABLE_FORM),
            edit = @Edit(
                    title = "工序",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name")
            )
    )
    private ProductProcessMTO productProcessMTO;

    @Transient
    @FabosJsonField(
            views = @View(title = "产品",column = "name", show = false, type = ViewType.TABLE_FORM),
            edit = @Edit(
                    title = "选择产品",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    filter = @Filter(value = "validateFlag = 'Y' and specificationCode like '02%'")
            )
    )
    private SpecificationManageMTO product;

    @FabosJsonField(
            views = @View(title = "产品id",show = false),
            edit = @Edit(
                    show = false,
                    title = "产品id",
                    inputType = @InputType(length = 30)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "product", beFilledBy = "id"))
    )
    @Column(name = "product_id", length = 30)
    private String productId;

    @FabosJsonField(
            views = @View(title = "产品编号"),
            edit = @Edit(
                    readonly = @Readonly(add = true,edit = true),
                    notNull = true,
                    title = "产品编号",
                    inputType = @InputType(length = 30)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "product", beFilledBy = "code"))
    )
    @Column(name = "product_code", length = 30)
    private String productCode;

    @FabosJsonField(
            views = @View(title = "产品名称"),
            edit = @Edit(
                    readonly = @Readonly(add = true,edit = true),
                    notNull = true,
                    title = "产品名称",
                    inputType = @InputType(length = 30)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "product", beFilledBy = "name"))
    )
    @Column(name = "product_name", length = 30)
    private String productName;

    @FabosJsonField(
            views = @View(title = "产品型号"),
            edit = @Edit(
                    readonly = @Readonly(add = true,edit = true),
                    title = "产品型号",
                    inputType = @InputType(length = 30)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "product", beFilledBy = "type"))
    )
    @Column(name = "product_model", length = 30)
    private String productModel;

    @FabosJsonField(
            views = @View(title = "变更原因"),
            edit = @Edit(
                    type = EditType.TEXTAREA,
                    title = "变更原因",
                    inputType = @InputType(length = 100),
                    notNull = true
            )
    )
    @Column(name = "change_reason", length = 100)
    private String changeReason;

    @FabosJsonField(
            views = @View(title = "变更前"),
            edit = @Edit(
                    title = "变更前",
                    type = EditType.TEXTAREA,
                    inputType = @InputType(length = 100),
                    notNull = true
            )
    )
    @Column(name = "before_change", length = 100)
    private String beforeChange;

    @FabosJsonField(
            views = @View(title = "变更后"),
            edit = @Edit(
                    title = "变更后",
                    type = EditType.TEXTAREA,
                    inputType = @InputType(length = 100)
            )
    )
    @Column(name = "after_change", length = 100)
    private String afterChange;

    @FabosJsonField(
            views = @View(title = "预计变更切换时间", type = ViewType.DATE_TIME),
            edit = @Edit(
                    title = "预计变更切换时间",
                    notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @Column(name = "estimated_switch_time")
    private Date estimatedSwitchTime;


    @FabosJsonField(
            views = @View(title = "风险影响"),
            edit = @Edit(
                    title = "风险影响",
                    type = EditType.TEXTAREA,
                    inputType = @InputType(length = 100),
                    notNull = true
            )
    )
    @Column(name = "risk_impact", length = 100)
    private String riskImpact;

    @FabosJsonField(
            views = @View(title = "文件标准化"),
            edit = @Edit(
                    title = "文件标准化",
                    notNull = true,
                    type = EditType.multiple_select,
                    choiceType = @ChoiceType(fetchHandler = FileStandardEnum.class),
                    inputType = @InputType(length = 100)
            )
    )
    @Column(name = "file_standardization", length = 100)
    private String fileStandardization;

    @FabosJsonField(
            views = @View(title = "附件"),
            edit = @Edit(title = "附件",
                    inputType = @InputType(length = 300),
                    type = EditType.ATTACHMENT,
                    tips = "最大支持100M文件",
                    attachmentType = @AttachmentType(
                            size = 100 * 1024,
                            maxLimit = 1,
                            type = AttachmentType.Type.BASE)
            )
    )
    @Column(length = 300)
    private String attachment;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(
                    title = "状态",
                    inputType = @InputType(length = 30),
                    notNull = true,
                    search = @Search,
                    readonly = @Readonly(add = true, edit = true),
                    defaultVal = "WAIT_SUBMIT",
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ECRStatusEnum.class)
            ),
            dynamicField = @DynamicField(passive = true)
    )
    @Column(name = "status", length = 30)
    private String status;

    @Transient
    @FabosJsonField(
            views = @View(title = "选择申请人", column = "name", show = false),
            edit = @Edit(
                    title = "选择申请人",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType()
            )
    )
    private UserForInsTaskMTO user;

    @FabosJsonField(
            views = @View(title = "申请人"),
            edit = @Edit(
                    title = "申请人",
                    inputType = @InputType(length = 20),
                    readonly = @Readonly(add = true, edit = true),
                    notNull = true,
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "user", beFilledBy = "name"))
    )
    @Column(name = "applicant", length = 20)
    private String applicant;

    @FabosJsonField(
            views = @View(title = "申请人ID", show = false),
            edit = @Edit(
                    show = false,
                    title = "申请人ID",
                    inputType = @InputType(length = 50)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "user", beFilledBy = "id"))
    )
    @Column(name = "applicant_id", length = 50)
    private String applicantId;

    @FabosJsonField(
            views = @View(title = "申请时间", type = ViewType.DATE_TIME),
            edit = @Edit(
                    title = "申请时间",
                    notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @Column(name = "apply_time", nullable = false)
    private Date applyTime;

    @Transient
    @FabosJsonField(
            views = @View(title = "选择申请部门", column = "name", show = false),
            edit = @Edit(
                    title = "选择申请部门",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType()
            )
    )
    private OrgMTO org;

    @FabosJsonField(
            views = @View(title = "申请部门"),
            edit = @Edit(
                    title = "申请部门",
                    readonly = @Readonly,
                    inputType = @InputType(length = 20),
                    notNull = true,
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "org", beFilledBy = "name"))
    )
    @Column(name = "apply_department", length = 20)
    private String applyDepartment;

    @FabosJsonField(
            views = @View(title = "申请部门ID", show = false),
            edit = @Edit(
                    show = false,
                    title = "申请部门ID"
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "org", beFilledBy = "id"))
    )
    @Column(name = "department_id", length = 50)
    private String departmentId;

    @FabosJsonField(
            views = @View(title = "变更切换日期", type = ViewType.DATE_TIME),
            edit = @Edit(
                    title = "变更切换日期",
                    notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @Column(name = "change_switch_date")
    private Date changeSwitchDate;

    @FabosJsonField(
            views = @View(title = "计划变更执行日期", type = ViewType.DATE_TIME),
            edit = @Edit(
                    title = "计划变更执行日期",
                    notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @Column(name = "planned_execution_date")
    private Date plannedExecutionDate;

    @FabosJsonField(
            views = @View(title = "是否涉及客户", show = false),
            edit = @Edit(title = "是否涉及客户",
                    show = false,
                    type = EditType.BOOLEAN,
                    boolType = @BoolType(trueText = "是", falseText = "否")
            )
    )
    private Boolean involveCustomer;

    @Transient
    @FabosJsonField(
            views = @View(title = "申请审批人", column = "name", show = false),
            edit = @Edit(
                    title = "申请审批人",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType()
            )
    )
    private UserForInsTaskMTO applyApproveUserMTO;

    @FabosJsonField(
            views = @View(title = "申请审批人"),
            edit = @Edit(
                    title = "申请审批人",
                    inputType = @InputType(length = 20),
                    readonly = @Readonly(add = true, edit = true),
                    notNull = true,
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "applyApproveUserMTO", beFilledBy = "name"))
    )
    @Column(name = "apply_approver", length = 20)
    private String applyApprover;

    @FabosJsonField(
            views = @View(title = "申请审批人ID", show = false),
            edit = @Edit(
                    show = false,
                    title = "申请审批人ID",
                    inputType = @InputType(length = 50)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "applyApproveUserMTO", beFilledBy = "id"))
    )
    @Column(name = "apply_approve_id", length = 50)
    private String applyApproveId;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "ecr_id")
    @FabosJsonField(
            views = @View(title = "变更事项", column = "changeItem", type = ViewType.TABLE_VIEW),
            edit = @Edit(
                    title = "变更事项",
                    type = EditType.TAB_TABLE_ADD,
                    allowAddMultipleRows = true,
                    referenceTableType = @ReferenceTableType(label = "changeItem")
            )
    )
    private List<ECRItem> ECRItems;

    @ManyToOne(cascade = CascadeType.ALL)
    @JoinColumn(name = "confirm_request_id")
    @FabosJsonField(
            views = @View(
                    title = "批准请求",
                    show = false,
                    column = "innerAdvice"
            ),
            edit = @Edit(
                    show = false,
                    title = "批准请求",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType(label = "innerAdvice")
            )
    )
    private ConfirmRequest confirmRequest;

    @ManyToOne(cascade = CascadeType.ALL)
    @JoinColumn(name = "verification_id")
    @FabosJsonField(
            views = @View(
                    show = false,
                    title = "验证请求项",
                    column = "generalCode"
            ),
            edit = @Edit(
                    show = false,
                    title = "验证请求项",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType(label = "generalCode")
            )
    )
    private VerificationIDetail verificationIDetail;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "ecn_id")
    @FabosJsonField(
            views = @View(title = "补充变更任务", column = "changeItem", type = ViewType.TABLE_VIEW),
            edit = @Edit(
                    show = false,
                    title = "补充变更任务",
                    type = EditType.TAB_TABLE_ADD,
                    allowAddMultipleRows = true,
                    referenceTableType = @ReferenceTableType(label = "changeItem")
            )
    )
    private List<ECNItem> ECNItems;

    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumn(name = "ecr_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @FabosJsonField(
            views = @View(title = "变更追溯", column = "serialNumber", type = ViewType.TABLE_VIEW),
            edit = @Edit(
                    show = false,
                    title = "变更追溯",
                    type = EditType.TAB_TABLE_ADD,
                    allowAddMultipleRows = true,
                    referenceTableType = @ReferenceTableType(label = "serialNumber")
            )
    )
    private List<ChangeTrace> changeTraces;

    @FabosJsonField(
            views = @View(title = "闭环时间",
                    show = false,
                    type = ViewType.DATE_TIME),
            edit = @Edit(
                    show = false,
                    title = "闭环时间",
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date endDate;

    @FabosJsonField(
            views = @View(title = "变更事项所有责任人的id集合,我的变更任务查询使用", show = false),
            edit = @Edit(title = "变更事项所有责任人的id集合,我的变更任务查询使用",show = false)
    )
    private String allUserIds;
}