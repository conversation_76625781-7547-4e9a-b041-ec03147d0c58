package cec.jiutian.bc.qualityExceptionManagement.domain.returnProductHandling.proxy;

import cec.jiutian.bc.qualityExceptionManagement.domain.returnProductHandling.model.ReturnProductHandling;
import cec.jiutian.bc.qualityExceptionManagement.enums.ReturnProductHandlingStatusEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
public class ReturnProductHandlingDataProxy implements DataProxy<ReturnProductHandling> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public void beforeAdd(ReturnProductHandling entity) {
        entity.setStatus(ReturnProductHandlingStatusEnum.Enum.Edit.name());
    }

}
