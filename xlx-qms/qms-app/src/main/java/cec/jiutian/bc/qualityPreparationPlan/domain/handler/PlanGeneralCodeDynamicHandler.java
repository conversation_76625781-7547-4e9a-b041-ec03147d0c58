package cec.jiutian.bc.qualityPreparationPlan.domain.handler;

import cec.jiutian.bc.basicData.enumeration.NamingRuleCodeEnum;
import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.qualityPreparationPlan.domain.model.QualityPreparationPlan;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class PlanGeneralCodeDynamicHandler implements DependFiled.DynamicHandler<QualityPreparationPlan> {

    @Resource
    private NamingRuleService namingRuleService;

    @Override
    public Map<String, Object> handle(QualityPreparationPlan qualityPreparationPlan) {
        Map<String, Object> map = new HashMap<>();
        map.put("generalCode", String.valueOf(namingRuleService.getNameCode(NamingRuleCodeEnum.QuantityPreparationPlan.name(), 1, null).get(0)));
        return map;
    }
}
