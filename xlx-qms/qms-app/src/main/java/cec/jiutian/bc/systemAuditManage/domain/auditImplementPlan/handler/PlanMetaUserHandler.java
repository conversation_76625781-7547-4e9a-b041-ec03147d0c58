package cec.jiutian.bc.systemAuditManage.domain.auditImplementPlan.handler;

import cec.jiutian.bc.systemAuditManage.domain.auditImplementPlan.model.AuditImplementPlan;
import cec.jiutian.core.frame.module.MetaUser;
import cec.jiutian.view.ReferenceAddType;
import cn.hutool.core.bean.BeanUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class PlanMetaUserHandler implements ReferenceAddType.ReferenceAddHandler<AuditImplementPlan, MetaUser> {
    @Override
    public Map<String, Object> handle(AuditImplementPlan auditImplementPlan, List<MetaUser> metaUsers) {
        Map<String, Object> result = new HashMap<>();
        List<MetaUser> metaUserList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(metaUsers)) {
            metaUsers.forEach(d -> {
                MetaUser metaUser = new MetaUser();
                BeanUtil.copyProperties(d, metaUser);
                metaUserList.add(metaUser);
            });
        }
        result.put("metaUserList", metaUserList);
        return result;
    }
}
