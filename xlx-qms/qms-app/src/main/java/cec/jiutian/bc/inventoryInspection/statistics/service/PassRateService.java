package cec.jiutian.bc.inventoryInspection.statistics.service;

import cec.jiutian.bc.ao.QueryAO;
import cec.jiutian.bc.dto.ChartData;
import cec.jiutian.bc.enums.CharType;
import cec.jiutian.bc.enums.LineStyle;
import cec.jiutian.bc.inventoryInspection.statistics.ao.StatisticsCommonParam;
import cec.jiutian.bc.inventoryInspection.statistics.dto.LineRateResDTO;
import cec.jiutian.bc.inventoryInspection.statistics.dto.PassRateDTO;
import cec.jiutian.bc.inventoryInspection.statistics.repository.StatisticsRepository;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class PassRateService {

    private final StatisticsRepository statsRepository;

    public PassRateService(StatisticsRepository statsRepository) {
        this.statsRepository = statsRepository;
    }

    // 获取原始统计数据
    public List<LineRateResDTO> queryLinePassRateData(QueryAO queryAO) {
        StatisticsCommonParam param = StatisticsCommonParam.createByQueryAO(queryAO);
        List<Object[]> results = statsRepository.queryLinePassRateData(param);
        return results.stream().map(LineRateResDTO::mapToDto).collect(Collectors.toList());
    }

    public ChartData getLinePassRateChartData(QueryAO queryAO) {
        List<LineRateResDTO> res = queryLinePassRateData(queryAO);
        return convertToChartData(res);
    }

    private ChartData convertToChartData(List<LineRateResDTO> res) {
        ChartData chartData = new ChartData();
        List<String> xs = res.stream()
                .map(LineRateResDTO::getProductionLineName)
                .collect(Collectors.toList());
        chartData.getXAxis().setData(xs);
        chartData.getXAxis().getAxisLabel().setRotate(270);

        // 2. 构建Y轴
        List<ChartData.YAxis> yAxes = new ArrayList<>();

        // 左侧Y轴（数量）
        ChartData.YAxis leftYAxis = new ChartData.YAxis();
        leftYAxis.setPosition("left");
        leftYAxis.setName("数量/重量");
        leftYAxis.setShow(true);
        yAxes.add(leftYAxis);

        // 右侧Y轴（百分比）
        ChartData.YAxis rightYAxis = new ChartData.YAxis();
        rightYAxis.setPosition("right");
        rightYAxis.setName("累计占比");
        rightYAxis.setShow(false);
        yAxes.add(rightYAxis);
        chartData.setYAxis(yAxes);

        // 3. 构建系列数据
        List<ChartData.Series> seriesList = new ArrayList<>();
        List<Object> productNums = new ArrayList<>();
        List<Object> defDatas = new ArrayList<>();
        List<Object> passRates = new ArrayList<>();
        List<Object> lineName = new ArrayList<>();
        List<Object> targets = new ArrayList<>();

        res.forEach(resDTO -> {
            productNums.add(resDTO.getOutputQuantity());
            defDatas.add(resDTO.getUnqualifiedQuantity());
            passRates.add(resDTO.getPassRate());
            lineName.add(resDTO.getProductionLineName());
            targets.add(resDTO.getTarget());
        });

        ChartData.Series proSeries = ChartData.Series.createSeries("生产数量", CharType.bar.name(), 0,
                productNums, "#014D64", true);
        seriesList.add(proSeries);

        ChartData.Series defSeries = ChartData.Series.createSeries("不良数量", CharType.bar.name(), 0,
                defDatas, "#01A2D9", true);
        seriesList.add(defSeries);

        ChartData.Series passSeries = ChartData.Series.createSeries("合格率", CharType.line.name(), 1,
              passRates, "#046b9d", true);
        passSeries.setIsPercent(true);
        passSeries.setLineStyle(new ChartData.LineStyle(LineStyle.solid.name()));
        seriesList.add(passSeries);

        ChartData.Series targetSeries = ChartData.Series.createSeries("目标合格率", CharType.line.name(), 1,
                targets, "#7C260B", false);
        targetSeries.setIsPercent(true);
        targetSeries.setTargetLineType("lower");
        targetSeries.setTargetLineColor("#FF0000");
        targetSeries.setLineStyle(new ChartData.LineStyle(LineStyle.dashed.name()));
        seriesList.add(targetSeries);

        chartData.setSeries(seriesList);
        return chartData;
    }


    // 获取原始统计数据
    public List<PassRateDTO> queryWorkshopPassRateData(QueryAO queryAO) {
        StatisticsCommonParam param = StatisticsCommonParam.createByQueryAO(queryAO);
        List<Object[]> results = statsRepository.queryWorkshopPassRate(param);
        return results.stream()
                .map(PassRateDTO::mapToDto)
                .collect(Collectors.toList());
    }

    public ChartData getWorkshopPassRateChartData(QueryAO queryAO) {
        List<PassRateDTO> res = queryWorkshopPassRateData(queryAO);
        return workshopConvertToChartData(res);
    }

    private ChartData workshopConvertToChartData(List<PassRateDTO> res) {
        ChartData chartData = new ChartData();
        List<String> xs = res.stream()
                .map(PassRateDTO::getName)
                .collect(Collectors.toList());
        chartData.getXAxis().setData(xs);
        chartData.getXAxis().getAxisLabel().setRotate(270);
        List<ChartData.YAxis> yAxes = new ArrayList<>();
        ChartData.YAxis leftYAxis = new ChartData.YAxis();
        leftYAxis.setPosition("left");
        leftYAxis.setName("数量/重量");
        leftYAxis.setShow(true);
        yAxes.add(leftYAxis);
        ChartData.YAxis rightYAxis = new ChartData.YAxis();
        rightYAxis.setPosition("right");
        rightYAxis.setName("合格率");
        rightYAxis.setShow(false);
        yAxes.add(rightYAxis);
        chartData.setYAxis(yAxes);
        List<ChartData.Series> seriesList = new ArrayList<>();
        List<Object> productNums = new ArrayList<>();
        List<Object> defDatas = new ArrayList<>();
        List<Object> passRates = new ArrayList<>();
        List<Object> workshopName = new ArrayList<>();
        List<Object> targets = new ArrayList<>();
        res.forEach(resDTO -> {
            productNums.add(resDTO.getTotalProduction());
            defDatas.add(resDTO.getDefectiveCount());
            passRates.add(resDTO.getPassRate());
            workshopName.add(resDTO.getName());
            targets.add(resDTO.getTargetRate());
        });
        ChartData.Series proSeries = ChartData.Series.createSeries("生产数量", CharType.bar.name(), 0,
                productNums, "#014D64", true);
        seriesList.add(proSeries);
        ChartData.Series defSeries = ChartData.Series.createSeries("不良数量", CharType.bar.name(), 0,
              defDatas, "#01A2D9", true);
        seriesList.add(defSeries);
        ChartData.Series passSeries = ChartData.Series.createSeries("合格率", CharType.line.name(), 1,
             passRates, "#046b9d", true);
        passSeries.setIsPercent(true);
        passSeries.setLineStyle(new ChartData.LineStyle(LineStyle.solid.name()));
        seriesList.add(passSeries);

        ChartData.Series targetSeries = ChartData.Series.createSeries("目标合格率", CharType.line.name(), 1,
                targets, "#7C260B", false);
        targetSeries.setIsPercent(true);
        targetSeries.setTargetLineType("lower");
        targetSeries.setTargetLineColor("#FF0000");
        targetSeries.setLineStyle(new ChartData.LineStyle(LineStyle.dashed.name()));
        seriesList.add(targetSeries);
        chartData.setSeries(seriesList);
        return chartData;
    }



    // 获取原始统计数据
    public List<PassRateDTO> queryCategoryPassRateData(QueryAO queryAO) {
        StatisticsCommonParam param = StatisticsCommonParam.createByQueryAO(queryAO);
        List<Object[]> results = statsRepository.queryCategoryPassRate(param);
        return results.stream()
                .map(PassRateDTO::mapToDto)
                .collect(Collectors.toList());
    }


    public ChartData getCategoryPassRateChartData(QueryAO queryAO) {
        List<PassRateDTO> res = queryCategoryPassRateData(queryAO);
        return categoryConvertToChartData(res);
    }


    private ChartData categoryConvertToChartData(List<PassRateDTO> res) {
        ChartData chartData = new ChartData();
        List<String> xs = res.stream()
                .map(PassRateDTO::getName)
                .collect(Collectors.toList());
        chartData.getXAxis().setData(xs);
        chartData.getXAxis().getAxisLabel().setRotate(270);

        List<ChartData.YAxis> yAxes = new ArrayList<>();
        ChartData.YAxis leftYAxis = new ChartData.YAxis();
        leftYAxis.setPosition("left");
        leftYAxis.setName("数量/重量");
        leftYAxis.setShow(true);
        yAxes.add(leftYAxis);
        ChartData.YAxis rightYAxis = new ChartData.YAxis();
        rightYAxis.setPosition("right");
        rightYAxis.setName("成品合格率");
        rightYAxis.setShow(false);
        yAxes.add(rightYAxis);
        chartData.setYAxis(yAxes);

        List<ChartData.Series> seriesList = new ArrayList<>();
        List<Object> productNums = new ArrayList<>();
        List<Object> defDatas = new ArrayList<>();
        List<Object> passRates = new ArrayList<>();
        List<Object> targets = new ArrayList<>();

        List<Object> nm = new ArrayList<>();
        res.forEach(resDTO -> {
            productNums.add(resDTO.getTotalProduction());
            defDatas.add(resDTO.getDefectiveCount());
            passRates.add(resDTO.getPassRate());
            nm.add(resDTO.getName());
            targets.add(resDTO.getTargetRate());
        });
        ChartData.Series proSeries = ChartData.Series.createSeries("生产数量", CharType.bar.name(), 0,
                productNums, "#014D64", true);
        seriesList.add(proSeries);
        ChartData.Series defSeries = ChartData.Series.createSeries("不良数量", CharType.bar.name(), 0,
                defDatas, "#01A2D9", true);
        seriesList.add(defSeries);
        ChartData.Series passSeries = ChartData.Series.createSeries("合格率", CharType.line.name(), 1,
                passRates, "#046b9d", true);
        passSeries.setIsPercent(true);
        passSeries.setLineStyle(new ChartData.LineStyle(LineStyle.solid.name()));
        seriesList.add(passSeries);

        ChartData.Series targetSeries = ChartData.Series.createSeries("目标合格率", CharType.line.name(), 1,
                targets, "#7C260B", false);
        targetSeries.setIsPercent(true);
        targetSeries.setTargetLineType("lower");
        targetSeries.setTargetLineColor("#FF0000");
        targetSeries.setLineStyle(new ChartData.LineStyle(LineStyle.dashed.name()));
        seriesList.add(targetSeries);

        chartData.setSeries(seriesList);
        return chartData;
    }

    // 获取原始统计数据
    public List<PassRateDTO> queryFactoryPassRateData(QueryAO queryAO) {
        StatisticsCommonParam param = StatisticsCommonParam.createByQueryAO(queryAO);
        List<Object[]> results = statsRepository.queryFactoryPassRate(param);
        return results.stream()
                .map(PassRateDTO::mapToDto)
                .collect(Collectors.toList());
    }

    public ChartData getFactoryPassRateChartData(QueryAO queryAO) {
        List<PassRateDTO> res = queryFactoryPassRateData(queryAO);
        return factoryConvertToChartData(res);
    }


    private ChartData factoryConvertToChartData(List<PassRateDTO> res) {
        ChartData chartData = new ChartData();
        List<String> xs = res.stream()
                .map(PassRateDTO::getName)
                .collect(Collectors.toList());
        chartData.getXAxis().setData(xs);
        chartData.getXAxis().getAxisLabel().setRotate(270);

        List<ChartData.YAxis> yAxes = new ArrayList<>();
        ChartData.YAxis leftYAxis = new ChartData.YAxis();
        leftYAxis.setPosition("left");
        leftYAxis.setName("数量/重量");
        leftYAxis.setShow(true);
        yAxes.add(leftYAxis);
        ChartData.YAxis rightYAxis = new ChartData.YAxis();
        rightYAxis.setPosition("right");
        rightYAxis.setName("合格率");
        rightYAxis.setShow(false);
        yAxes.add(rightYAxis);
        chartData.setYAxis(yAxes);
        List<ChartData.Series> seriesList = new ArrayList<>();
        List<Object> productNums = new ArrayList<>();
        List<Object> defDatas = new ArrayList<>();
        List<Object> passRates = new ArrayList<>();
        List<Object> targets = new ArrayList<>();
        res.forEach(resDTO -> {
            productNums.add(resDTO.getTotalProduction());
            defDatas.add(resDTO.getDefectiveCount());
            passRates.add(resDTO.getPassRate());
            targets.add(0.5D);
        });
        ChartData.Series proSeries = ChartData.Series.createSeries("生产数量", CharType.bar.name(), 0,
                productNums, "#014D64", true);
        seriesList.add(proSeries);
        ChartData.Series defSeries = ChartData.Series.createSeries("不良数量", CharType.bar.name(), 0,
                defDatas, "#01A2D9", true);
        seriesList.add(defSeries);
        ChartData.Series passSeries = ChartData.Series.createSeries("合格率", CharType.line.name(), 1,
                passRates, "#046b9d", true);
        passSeries.setIsPercent(true);
        passSeries.setLineStyle(new ChartData.LineStyle(LineStyle.solid.name()));
        seriesList.add(passSeries);
        ChartData.Series targetSeries = ChartData.Series.createSeries("目标合格率", CharType.line.name(), 1,
                targets, "#7C260B", false);
        targetSeries.setIsPercent(true);
        targetSeries.setTargetLineType("lower");
        targetSeries.setTargetLineColor("#FF0000");
        targetSeries.setLineStyle(new ChartData.LineStyle(LineStyle.dashed.name()));
        seriesList.add(targetSeries);
        chartData.setSeries(seriesList);
        return chartData;
    }


    // 获取原始统计数据
    public List<PassRateDTO> queryCodePassRateData(QueryAO queryAO) {
        StatisticsCommonParam param = StatisticsCommonParam.createByQueryAO(queryAO);
        List<Object[]> results = statsRepository.queryCodePassRate(param);
        return results.stream()
                .map(o -> PassRateDTO.mapToDto(o)).collect(Collectors.toList());
    }

    public ChartData getCodePassRateChartData(QueryAO queryAO) {
        List<PassRateDTO> res = queryCodePassRateData(queryAO);
        return codeConvertToChartData(res);
    }

    private ChartData codeConvertToChartData(List<PassRateDTO> res) {
        ChartData chartData = new ChartData();
        List<String> xs = res.stream()
                .map(PassRateDTO::getName)
                .collect(Collectors.toList());
        chartData.getXAxis().setData(xs);
        chartData.getXAxis().getAxisLabel().setRotate(270);
        List<ChartData.YAxis> yAxes = new ArrayList<>();
        ChartData.YAxis leftYAxis = new ChartData.YAxis();
        leftYAxis.setPosition("left");
        leftYAxis.setName("数量/重量");
        leftYAxis.setShow(true);
        yAxes.add(leftYAxis);
        ChartData.YAxis rightYAxis = new ChartData.YAxis();
        rightYAxis.setPosition("right");
        rightYAxis.setName("合格率");
        rightYAxis.setShow(false);
        yAxes.add(rightYAxis);
        chartData.setYAxis(yAxes);
        List<ChartData.Series> seriesList = new ArrayList<>();
        List<Object> productNums = new ArrayList<>();
        List<Object> defDatas = new ArrayList<>();
        List<Object> passRates = new ArrayList<>();
        List<Object> targets = new ArrayList<>();

        res.forEach(resDTO -> {
            productNums.add(resDTO.getTotalProduction());
            defDatas.add(resDTO.getDefectiveCount());
            passRates.add(resDTO.getPassRate());
            targets.add(0.5D);
        });
        ChartData.Series proSeries = ChartData.Series.createSeries("生产数量", CharType.bar.name(), 0,
                productNums, "#014D64", true);
        seriesList.add(proSeries);
        ChartData.Series defSeries = ChartData.Series.createSeries("不良数量", CharType.bar.name(), 0,
                defDatas, "#01A2D9", true);
        seriesList.add(defSeries);
        ChartData.Series passSeries = ChartData.Series.createSeries("合格率", CharType.line.name(), 1,
                passRates, "#046b9d", true);
        passSeries.setIsPercent(true);
        passSeries.setLineStyle(new ChartData.LineStyle(LineStyle.solid.name()));
        seriesList.add(passSeries);
        ChartData.Series targetSeries = ChartData.Series.createSeries("目标合格率", CharType.line.name(), 1,
                targets, "#7C260B", false);
        targetSeries.setIsPercent(true);
        targetSeries.setTargetLineType("lower");
        targetSeries.setTargetLineColor("#FF0000");
        targetSeries.setLineStyle(new ChartData.LineStyle(LineStyle.dashed.name()));
        seriesList.add(targetSeries);
        chartData.setSeries(seriesList);
        return chartData;
    }

    public List<PassRateDTO> queryInsItemPassRateData(QueryAO queryAO) {
        StatisticsCommonParam param = StatisticsCommonParam.createByQueryAO(queryAO);
        List<Object[]> results = statsRepository.queryInsItemPassRate(param);
        return results.stream()
                .map(PassRateDTO::mapToDto)
                .collect(Collectors.toList());
    }

    public ChartData getInsItemPassRateChartData(QueryAO queryAO) {
        List<PassRateDTO> res = queryInsItemPassRateData(queryAO);
        return insItemConvertToChartData(res);
    }

    private ChartData insItemConvertToChartData(List<PassRateDTO> res) {
        ChartData chartData = new ChartData();
        List<String> xs = res.stream()
                .map(PassRateDTO::getName)
                .collect(Collectors.toList());
        chartData.getXAxis().setData(xs);
        chartData.getXAxis().getAxisLabel().setRotate(270);
        List<ChartData.YAxis> yAxes = new ArrayList<>();
        ChartData.YAxis leftYAxis = new ChartData.YAxis();
        leftYAxis.setPosition("left");
        leftYAxis.setName("数量/重量");
        leftYAxis.setShow(true);
        yAxes.add(leftYAxis);
        ChartData.YAxis rightYAxis = new ChartData.YAxis();
        rightYAxis.setPosition("right");
        rightYAxis.setName("合格率");
        rightYAxis.setShow(false);
        yAxes.add(rightYAxis);
        chartData.setYAxis(yAxes);
        List<ChartData.Series> seriesList = new ArrayList<>();
        List<Object> productNums = new ArrayList<>();
        List<Object> defDatas = new ArrayList<>();
        List<Object> passRates = new ArrayList<>();
        List<Object> targets = new ArrayList<>();

        res.forEach(resDTO -> {
            productNums.add(resDTO.getTotalProduction());
            defDatas.add(resDTO.getDefectiveCount());
            passRates.add(resDTO.getPassRate());
            targets.add(0.5D);
        });
        ChartData.Series proSeries = ChartData.Series.createSeries("生产数量", CharType.bar.name(), 0,
                productNums, "#014D64", true);
        seriesList.add(proSeries);
        ChartData.Series defSeries = ChartData.Series.createSeries("不良数量", CharType.bar.name(), 0,
                defDatas, "#01A2D9", true);
        seriesList.add(defSeries);
        ChartData.Series passSeries = ChartData.Series.createSeries("合格率", CharType.line.name(), 1,
                passRates, "#046b9d", true);
        passSeries.setIsPercent(true);
        passSeries.setLineStyle(new ChartData.LineStyle(LineStyle.solid.name()));
        seriesList.add(passSeries);
        ChartData.Series targetSeries = ChartData.Series.createSeries("目标合格率", CharType.line.name(), 1,
                targets, "#7C260B", false);
        targetSeries.setIsPercent(true);
        targetSeries.setTargetLineType("lower");
        targetSeries.setTargetLineColor("#FF0000");
        targetSeries.setLineStyle(new ChartData.LineStyle(LineStyle.dashed.name()));
        seriesList.add(targetSeries);
        chartData.setSeries(seriesList);
        return chartData;
    }
}
