package cec.jiutian.bc.productReturnInspection.domain.reportPrintTemplate.mto;

import cec.jiutian.bc.basicData.domain.inspectionStandard.model.InspectionStandard;
import cec.jiutian.bc.basicData.enumeration.ApplyRangeEnum;
import cec.jiutian.bc.materialInspect.domain.inspectionTask.handler.RequestDynamicHandler;
import cec.jiutian.bc.materialInspect.enumeration.InspectionResultEnum;
import cec.jiutian.bc.materialInspect.enumeration.TaskBuildTypeEnum;
import cec.jiutian.bc.modeler.enumration.UnqualifiedLevelEnum;
import cec.jiutian.bc.productReturnInspection.domain.inspectionRequest.model.ProductReturnInspectionRequest;
import cec.jiutian.bc.productReturnInspection.domain.inspectionTask.model.ProductReturnInspectionTaskDetail;
import cec.jiutian.bc.productReturnInspection.domain.inspectionTask.model.ProductReturnQualityInspectionStandardDetail;
import cec.jiutian.bc.productReturnInspection.domain.inspectionTask.proxy.MyProductReInsTaskDataProxy;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.meta.SkipMetadataScanning;
import cec.jiutian.view.*;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Filter;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;

import java.util.Date;
import java.util.List;

@FabosJson(
        name = "成品退货检验任务",
        orderBy = "createTime desc",
        filter = @Filter(value = "inspectionType = 'productReturnInspect'"),
        dataProxy = MyProductReInsTaskDataProxy.class
)
@Table(name = "mi_inspection_task",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Getter
@Setter
@TemplateType(type = "multiTable")
@SkipMetadataScanning
public class ProReturnInspectionTaskMTO extends MetaModel {

    @FabosJsonField(
            views = @View(title = "检验任务编号"),
            edit = @Edit(readonly = @Readonly(),title = "检验任务编号")
    )
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "质检标准",column = "generalCode"),
            edit = @Edit(title = "质检标准",
                    readonly = @Readonly(),
                    allowAddMultipleRows = false,
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode")
            )
    )
    @ManyToOne
    @JoinColumn(name = "inspection_standard_id")
    private InspectionStandard inspectionStandard;

    @FabosJsonField(
            views = @View(title = "创建类型"),
            edit = @Edit(title = "创建类型",readonly = @Readonly(), type = EditType.CHOICE,search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = TaskBuildTypeEnum.class))
    )
    private String buildType;

    @FabosJsonField(
            views = @View(title = "检验类型"),
            edit = @Edit(title = "检验类型", show = false,type = EditType.CHOICE,search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = ApplyRangeEnum.class))
    )
    private String inspectionType;

    @FabosJsonField(
            views = @View(title = "是否加急"),
            edit = @Edit(title = "是否加急", readonly = @Readonly(),defaultVal = "false"
            )
    )
    private Boolean isUrgent;

    @FabosJsonField(
            views = @View(title = "检验结果"),
            edit = @Edit(title = "检验结果", readonly = @Readonly(),type = EditType.CHOICE,search = @Search(),show = false,
                    choiceType = @ChoiceType(fetchHandler = InspectionResultEnum.class))
    )
    private String inspectionResult;

    @FabosJsonField(
            views = @View(title = "不合格等级"),
            edit = @Edit(title = "不合格等级",readonly = @Readonly(), notNull = true,type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = UnqualifiedLevelEnum.class),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "inspectionResult == 'UNQUALIFIED'"))
    )
    private String unqualifiedLevel;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "检验通知",column = "generalCode", type = ViewType.TABLE_FORM),
            edit = @Edit(title = "检验通知",
                    type = EditType.REFERENCE_TABLE,allowAddMultipleRows = false,
                    notNull = true,readonly = @Readonly(),
                    filter = @Filter(value = "ProductReturnInspectionRequest.type = 'Return'"),
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode")
            )
    )
    private ProductReturnInspectionRequest inspectionRequest;

    @FabosJsonField(
            views = @View(title = "检验通知单号"),
            edit = @Edit(title = "检验通知单号",
                    readonly = @Readonly
            )
    )
    private String insCode;

    @FabosJsonField(
            views = @View(title = "供应商名称"),
            edit = @Edit(title = "供应商名称",readonly = @Readonly(), search = @Search(vague = true))
    )
    private String supplierName;

    @FabosJsonField(
            views = @View(title = "检验部门"),
            edit = @Edit(title = "检验部门",readonly = @Readonly(), search = @Search(vague = true))
   )
    private String inspectionDepartmentName;

    @FabosJsonField(
            views = @View(title = "检验人"),
            edit = @Edit(title = "检验人",readonly = @Readonly(),
                    search = @Search(vague = true))
    )
    private String userName;

    @FabosJsonField(
            views = @View(title = "检验物资编码"),
            edit = @Edit(title = "检验物资编码", readonly = @Readonly)
   )
    private String materialCode;

    @FabosJsonField(
            views = @View(title = "检验物资名称"),
            edit = @Edit(title = "检验物资名称", readonly = @Readonly)
    )
    private String materialName;

    @FabosJsonField(
            views = @View(title = "来料批号"),
            edit = @Edit(readonly = @Readonly(),title = "来料批号")
   )
    private String originLotId;

    @FabosJsonField(
            views = @View(title = "来料数量"),
            edit = @Edit(title = "来料数量", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inspectionRequest", beFilledBy = "arrivalQuantity"))
    )
    private Double arrivalQuantity;

    @FabosJsonField(
            views = @View(title = "样品数量"),
            edit = @Edit(title = "样品数量", readonly = @Readonly)
    )
    private Double sampleQuantity;

    @FabosJsonField(
            views = @View(title = "单位"),
            edit = @Edit(title = "单位",readonly = @Readonly())
   )
    private String unit;

    @FabosJsonField(
            views = @View(title = "规格"),
            edit = @Edit(readonly = @Readonly(),title = "规格")
    )
    private String materialSpecification;

    @FabosJsonField(
            views = @View(title = "物资分级"),
            edit = @Edit(readonly = @Readonly(),title = "物资分级"))
    private String materialLevel;

    @FabosJsonField(
            views = @View(title = "取样数量"),
            edit = @Edit(title = "取样数量", readonly = @Readonly())
    )
    private Double allInspectionItemQuantity;

    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumn(name = "inspection_task_id")
    @OrderBy
    @FabosJsonField(
            views = @View(title = "检验物资明细", type= ViewType.TABLE_VIEW,index = 8),
            edit = @Edit(title = "检验物资明细",readonly = @Readonly(),type = EditType.TAB_REFERENCE_GENERATE),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "inspectionRequest", dynamicHandler = RequestDynamicHandler.class)),
            referenceGenerateType = @ReferenceGenerateType()
    )
    private List<ProductReturnInspectionTaskDetail> inspectionTaskDetailList;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = false)
    @FabosJsonField(
            views = @View(title = "检验项目明细", type= ViewType.TABLE_VIEW,index = 8),
            edit = @Edit(title = "检验项目明细",readonly = @Readonly(),type = EditType.TAB_REFERENCE_GENERATE),
           referenceGenerateType = @ReferenceGenerateType()
    )
    @JoinColumn(name = "inspection_task_id")
    private List<ProductReturnQualityInspectionStandardDetail> standardDetailList;

    @FabosJsonField(
            views = @View(title = "备注"),
            edit = @Edit(title = "备注",readonly = @Readonly(),
                    type = EditType.TEXTAREA)
    )
    private String remark;

    @FabosJsonField(
            views = @View(title = "创建时间",type = ViewType.DATE_TIME),
            edit = @Edit(title = "创建时间",
                    readonly = @Readonly,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @CreationTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date createDate;

    @FabosJsonField(
            views = @View(title = "检验时间",type = ViewType.DATE_TIME),
            edit = @Edit(title = "检验时间",
                    readonly = @Readonly,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date insTime;
}
