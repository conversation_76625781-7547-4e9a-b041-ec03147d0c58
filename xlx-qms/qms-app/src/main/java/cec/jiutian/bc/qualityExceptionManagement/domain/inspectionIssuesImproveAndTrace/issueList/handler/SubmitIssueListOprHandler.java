package cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.issueList.handler;

import cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.issueList.model.IssueList;
import cec.jiutian.bc.qualityExceptionManagement.enumeration.inspectionIssuesImproveAndTrace.IssueListStatusEnum;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

@Component
public class SubmitIssueListOprHandler implements OperationHandler<IssueList, Void> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    @Transactional
    public String exec(List<IssueList> data, Void modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            IssueList issueList = data.get(0);
            checkIssueList(issueList);
            issueList.setStatus(IssueListStatusEnum.Enum.RUNNING.name());
            fabosJsonDao.mergeAndFlush(issueList);
        }
        return "msg.success('操作成功')";
    }

    private void checkIssueList(IssueList issueList) {
        //需要分析完成   原因不能为空  整改措施不为空
        if(StringUtils.isBlank(issueList.getCauseAnalysis())){
            throw new FabosJsonApiErrorTip("原因分析不能为空");
        }
        if(CollectionUtils.isEmpty(issueList.getIssueListCorrections())){
            throw new FabosJsonApiErrorTip("整改措施不能为空");
        }
    }


}
