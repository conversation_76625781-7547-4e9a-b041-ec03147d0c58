package cec.jiutian.bc.deliveryInspection.domain.reportPrintTemplate.handler;

import cec.jiutian.bc.deliveryInspection.domain.reportPrintTemplate.model.DeliInsReportPrintTemplate;
import cec.jiutian.bc.deliveryInspection.domain.reportPrintTemplate.mto.DeliveryInspectionTaskMTO;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class DliViewDetailHandler implements OperationHandler<DeliInsReportPrintTemplate, DeliveryInspectionTaskMTO> {
    @Resource
    private FabosJsonDao fabosJsonDao;


    @Override
    public DeliveryInspectionTaskMTO fabosJsonFormValue(List<DeliInsReportPrintTemplate> data, DeliveryInspectionTaskMTO fabosJsonForm, String[] param) {
        DeliInsReportPrintTemplate reportPrintTemplate = data.get(0);
        DeliveryInspectionTaskMTO task = fabosJsonDao.findById(DeliveryInspectionTaskMTO.class, reportPrintTemplate.getId());
        return task;
    }

}
