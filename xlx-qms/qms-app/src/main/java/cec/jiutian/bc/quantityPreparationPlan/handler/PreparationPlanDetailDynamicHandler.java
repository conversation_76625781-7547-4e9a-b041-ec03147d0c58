package cec.jiutian.bc.quantityPreparationPlan.handler;

import cec.jiutian.bc.generalModeler.util.StringUtils;
import cec.jiutian.bc.quantityPreparationPlan.model.QuantityPreparationPlan;
import cec.jiutian.bc.quantityPreparationPlan.port.client.QuantityPreparationFeignClient;
import cec.jiutian.view.DependFiled;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/5/21
 * @description TODO
 */
@Component
public class PreparationPlanDetailDynamicHandler implements DependFiled.DynamicHandler<QuantityPreparationPlan> {

    @Resource
    private QuantityPreparationFeignClient quantityPreparationFeignClient;

    @Override
    public Map<String, Object> handle(QuantityPreparationPlan quantityPreparationPlan) {
        Map<String, Object> result = new HashMap<>();
        if (StringUtils.isNotEmpty(quantityPreparationPlan.getPlanMonth()) && StringUtils.isNotEmpty(quantityPreparationPlan.getFactoryLineName())) {
            Map<String, Object> scheduleCondition = new HashMap<>();
            scheduleCondition.put("factoryAreaId",quantityPreparationPlan.getFactoryLineId());
            JSONObject scheduleJSONObject = quantityPreparationFeignClient.scheduleListFromMES(scheduleCondition);
            if (scheduleJSONObject != null) {
                JSONArray jsonArray = JSONArray.parseArray(String.valueOf(scheduleJSONObject));
            }
        }
        return result;
    }
}
