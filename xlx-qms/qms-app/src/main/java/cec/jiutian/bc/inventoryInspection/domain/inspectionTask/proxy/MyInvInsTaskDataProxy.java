package cec.jiutian.bc.inventoryInspection.domain.inspectionTask.proxy;

import cec.jiutian.bc.inventoryInspection.domain.inspectionTask.model.MyInventoryInspectionTask;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.view.fun.DataProxy;
import cec.jiutian.view.query.Condition;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class MyInvInsTaskDataProxy implements DataProxy<MyInventoryInspectionTask> {
    @Override
    public String beforeFetch(List<Condition> conditions) {
        String userId = UserContext.getUserId();
        if (StringUtils.isBlank(userId)) {
            throw new RuntimeException("当前用户未登录");
        }
        return "userId = '" + userId + "'";
    }
}
