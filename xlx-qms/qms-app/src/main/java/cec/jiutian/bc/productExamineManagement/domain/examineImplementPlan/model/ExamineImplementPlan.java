package cec.jiutian.bc.productExamineManagement.domain.examineImplementPlan.model;

import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleBaseModel;
import cec.jiutian.bc.modeler.enumration.NamingRuleCodeEnum;
import cec.jiutian.bc.mto.SpecificationManageMTO;
import cec.jiutian.bc.productExamineManagement.domain.examineImplementPlan.handler.*;
import cec.jiutian.bc.productExamineManagement.domain.examineImplementPlan.mto.ExamineImplementPlanResultMTO;
import cec.jiutian.bc.productExamineManagement.domain.examineImplementPlan.mto.ProductExamineExecuteMTO;
import cec.jiutian.bc.productExamineManagement.domain.examineReport.model.ExamineReport;
import cec.jiutian.bc.productExamineManagement.domain.examineYearlyPlan.model.ProductExamineYearlyPlan;
import cec.jiutian.bc.productExamineManagement.domain.inspectionTask.model.ProductExamineInspectionTask;
import cec.jiutian.bc.productExamineManagement.enumration.ExamineImplementStateEnum;
import cec.jiutian.bc.supplierManagement.mto.UserForSupplierMTO;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.RowBaseOperation;
import cec.jiutian.view.type.RowOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/16
 * @description TODO
 */
@FabosJson(
        name = "审核实施计划",
        orderBy = "ExamineImplementPlan.createTime desc",
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "businessState != 'Edit' && businessState !='Cancel'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "businessState != 'Edit' && businessState !='Cancel'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
        },
        rowOperation = {
                @RowOperation(
                        title = "发布",
                        code = "ExamineImplementPlan@PUBLISH",
                        operationHandler = ExamineImplementPublishOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "是否确定操作？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "ExamineImplementPlan@PUBLISH"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "businessState != 'Edit' && businessState !='Cancel'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "取消",
                        code = "ExamineImplementPlan@CANCEL",
                        operationHandler = ExamineImplementCancelOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "是否确定操作？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "ExamineImplementPlan@CANCEL"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "businessState != 'Publish'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "取样检验",
                        code = "ExamineImplementPlan@TASK",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        popupType = RowOperation.PopupType.FORM,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        fabosJsonClass = ProductExamineInspectionTask.class,
                        operationHandler = ProductExamineInspectTaskGenerateOprHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "ExamineImplementPlan@TASK"
                        ),
                        ifExpr = "selectedItems[0].businessState != 'Publish'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "执行",
                        code = "ExamineImplementPlan@EXECUTE",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        popupType = RowOperation.PopupType.FORM,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        fabosJsonClass = ProductExamineExecuteMTO.class,
                        operationHandler = ProductExamineExecuteOprHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "ExamineImplementPlan@EXECUTE"
                        ),
                        ifExpr = "selectedItems[0].businessState != 'Inspecting'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "结果审核",
                        code = "ExamineImplementPlan@RESULTEXMINE",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        popupType = RowOperation.PopupType.FORM,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        operationHandler = ExamineImplementPlanResultOprHandler.class,
                        fabosJsonClass = ExamineImplementPlanResultMTO.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "ExamineImplementPlan@RESULTEXMINE"
                        ),
                        ifExpr = "selectedItems[0].businessState != 'Examine'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "报告生成",
                        code = "ExamineImplementPlan@REPORT",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        popupType = RowOperation.PopupType.FORM,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        fabosJsonClass = ExamineReport.class,
                        operationHandler = GenerateExamineReportOprHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "ExamineImplementPlan@REPORT"
                        ),
                        ifExpr = "selectedItems[0].businessState != 'Examined'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
        }
)
@Table(name = "qms_pem_product_examine_implement_plan"
)
@Entity
@Getter
@Setter
public class ExamineImplementPlan extends NamingRuleBaseModel {

    @Override
    public String getNamingCode() {
        return NamingRuleCodeEnum.ExamineImplementPlan.name();
    }

    @FabosJsonField(
            views = @View(title = "审核年度计划", column = "planName"),
            edit = @Edit(title = "审核年度计划",
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "planName")
                    , allowAddMultipleRows = false
            )
    )
    @ManyToOne
    @JoinColumn(name = "response_department_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private ProductExamineYearlyPlan productExamineYearlyPlan;

    @FabosJsonField(
            views = @View(title = "计划日期"),
            edit = @Edit(title = "计划日期",
                    notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "productExamineYearlyPlan", beFilledBy = "planDate"))
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date planDate;

    @FabosJsonField(
            views = @View(title = "审核组组长", column = "name"),
            edit = @Edit(title = "审核组组长",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(type = TabTableReferType.SelectShowTypeMTM.LIST)
            )
    )
    @ManyToOne
    @JoinColumn(name = "person_Leader_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private UserForSupplierMTO examineLeader;

    @ManyToMany
    @JoinTable(
            name = "e_examine_plan_member", //中间表表名
            inverseForeignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT),
            joinColumns = @JoinColumn(name = "product_examine_implement_plan_id", referencedColumnName = "id"),
            inverseJoinColumns = @JoinColumn(name = "member_id", referencedColumnName = "id"))
    @FabosJsonField(
            views = @View(title = "审核组成员",type = ViewType.TABLE_VIEW),
            edit = @Edit(title = "审核组成员", type = EditType.TAB_TABLE_REFER, notNull = true,
                    tabTableReferType = @TabTableReferType(type = TabTableReferType.SelectShowTypeMTM.LIST,
                            label = "name")
            )
    )
    private List<UserForSupplierMTO> examineMember;

    @FabosJsonField(
            views = @View(title = "样本量"),
            edit = @Edit(title = "样本量",notNull = true,numberType = @NumberType(min = 0,precision = 2))
    )
    private Double sampleQuantity;

    @Transient
    @FabosJsonField(
            views = @View(title = "取样产品", column = "code"),
            edit = @Edit(title = "取样产品",notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    filter = @Filter(value = "validateFlag = 'Y' and specificationCode like '02%'"),
                    referenceTableType = @ReferenceTableType(id = "id", label = "code")
            ),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "productExamineYearlyPlan",dynamicHandler = ExamineMaterialDynamicHandler.class))
    )
    private SpecificationManageMTO material;

    @FabosJsonField(
            views = @View(title = "物资Id",show = false),
            edit = @Edit(title = "物资Id",
                    show = false
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "material", beFilledBy = "id"))
    )
    private String materialId;

    @FabosJsonField(
            views = @View(title = "取样产品名称"),
            edit = @Edit(title = "取样产品名称", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "material", beFilledBy = "name"))
    )
    private String materialName;

    @FabosJsonField(
            views = @View(title = "审核开始日期"),
            edit = @Edit(title = "审核开始日期",
                    notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date examineStartDate;

    @FabosJsonField(
            views = @View(title = "审核结束日期"),
            edit = @Edit(title = "审核结束日期",
                    notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date examineEndDate;

    @FabosJsonField(
            views = @View(title = "KQZ分数"),
            edit = @Edit(title = "KQZ分数",show = false,numberType = @NumberType(min=0,max = 100,precision = 2),tips = "范围：0-100")
    )
    private Double KQZScore;

    @FabosJsonField(
            views = @View(title = "附件"),
            edit = @Edit(title = "附件",
                    inputType = @InputType(length = 300),
                    type = EditType.ATTACHMENT,
                    tips = "最大支持100M文件",
                    attachmentType = @AttachmentType(
                            size = 100 * 1024,
                            maxLimit = 1,
                            type = AttachmentType.Type.BASE)
            )
    )
    @Column(length = 300)
    private String attachment;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",readonly = @Readonly,type = EditType.CHOICE,
                    defaultVal = "Edit",
                    choiceType = @ChoiceType(fetchHandler = ExamineImplementStateEnum.class)),
            dynamicField = @DynamicField(passive = true)
    )
    private String businessState;

    @FabosJsonField(
            views = @View(title = "是否创建纠正预防措施",show = false),
            edit = @Edit(title = "是否创建纠正预防措施", show = false
            )
    )
    private Boolean isCorrectPreventMeasure;

    @FabosJsonField(
            views = @View(title = "纠正预防措施id",show = false),
            edit = @Edit(title = "纠正预防措施id", show = false
            )
    )
    private String correctPreventMeasureId;
}
