package cec.jiutian.bc.changeRequestManagement.domain.ecr.mto.formDetail;

import cec.jiutian.bc.changeRequestManagement.domain.ecr.model.ApproveTask;
import cec.jiutian.bc.changeRequestManagement.enums.ApproveResultEnum;
import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.meta.SkipMetadataScanning;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DateType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.RowBaseOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.Column;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Setter
@Getter
@FabosJson(
        name = "变更申请评审",
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "1 == 1",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.HIDE
                ),
                @RowBaseOperation(
                        code = "add",
                        ifExpr = "1 == 1",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.HIDE
                ),
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "1 == 1",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.HIDE
                )
        }
)
@SkipMetadataScanning
public class ApproveTaskMTO extends BaseModel {

    @FabosJsonField(
            views = @View(title = "审核人"),
            edit = @Edit(
                    title = "审核人",
                    inputType = @InputType(length = 20)
            )
    )
    private String operator;

    @FabosJsonField(
            views = @View(title = "审核意见"),
            edit = @Edit(
                    title = "审核意见",
                    inputType = @InputType(length = 10),
                    search = @Search,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ApproveResultEnum.class)
            )
    )
    private String result;

    @FabosJsonField(
            views = @View(title = "意见说明"),
            edit = @Edit(
                    title = "意见说明",
                    type = EditType.TEXTAREA,
                    inputType = @InputType(length = 100)
            )
    )
    @Column(length = 100)
    private String explain;

    @FabosJsonField(
            views = @View(title = "审核阶段"),
            edit = @Edit(title = "审核阶段",
                    readonly = @Readonly,
                    inputType = @InputType(length = 20)
            )
    )
    private String nodeName;

    @FabosJsonField(
            views = @View(title = "审批时间",
                    type = ViewType.DATE_TIME),
            edit = @Edit(title = "审批时间",
                    dateType = @DateType(type = DateType.Type.DATE_TIME))
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date approveTime;

    public static ApproveTaskMTO createByApproveTask(ApproveTask approveTask) {
        ApproveTaskMTO approveTaskMTO = new ApproveTaskMTO();
        approveTaskMTO.setResult(approveTask.getResult());
        approveTaskMTO.setExplain(approveTask.getExplain());
        approveTaskMTO.setOperator(approveTask.getOperator());
        approveTaskMTO.setApproveTime(approveTask.getApproveTime());
        return approveTaskMTO;
    }
}
