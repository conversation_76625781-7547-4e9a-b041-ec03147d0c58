package cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.mto;

import cec.jiutian.bc.mto.ProcessFlowMTO;
import cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.enumration.ProductBatchNumberStatusEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.field.edit.TabTableReferType;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025/7/15
 * @description
 */
@FabosJson(
        name = "结果审核产品列表",
        orderBy = "PqtProductBatchNumberApprovalMTO.createTime desc"
)
@Table(name = "qms_pqt_product_batch_number")
@Entity
@Getter
@Setter
public class PqtProductBatchNumberApprovalMTO extends MetaModel {
    @FabosJsonField(
            views = @View(title = "车间ID", show = false),
            edit = @Edit(title = "车间ID", show = false)
    )
    private String factoryAreaId;

    @FabosJsonField(
            views = @View(title = "产线Id",show = false),
            edit = @Edit(title = "产线Id", show = false)
    )
    private String factoryLineId;

    @FabosJsonField(
            views = @View(title = "工序编码", show = false),
            edit = @Edit(title = "工序编码", show = false)
    )
    private String processOperationCode;

    @FabosJsonField(
            views = @View(title = "工序流水号ID", show = false),
            edit = @Edit(title = "工序流水号ID",
                    show = false
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "processFlowMTO", beFilledBy = "id"))
    )
    @JoinColumn(name = "process_flow_id")
    private String processFlowMTOID;

    //工序流水号
    @FabosJsonField(
            views = @View(title = "产品批号"),
            edit = @Edit(title = "产品批号",
                    search = @Search(vague = true),
                    notNull = true,
                    readonly = @Readonly()
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "processFlowMTO", beFilledBy = "serialNumber"))
    )
    private String serialNumber;

    @FabosJsonField(
            views = @View(title = "跟踪次数"),
            edit = @Edit(title = "跟踪次数")
    )
    private String trackingFrequency;

    @FabosJsonField(
            views = @View(title = "结果"),
            edit = @Edit(title = "结果",
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ProductBatchNumberStatusEnum.class)
            )
    )
    private String result;

    @FabosJsonField(
            views = @View(title = "跟踪人id", show = false),
            edit = @Edit(title = "跟踪人id", show = false)
    )
    private String trackerId;

    @FabosJsonField(
            views = @View(title = "跟踪人", show = false),
            edit = @Edit(title = "跟踪人", show = false)
    )
    private String tracker;
}
