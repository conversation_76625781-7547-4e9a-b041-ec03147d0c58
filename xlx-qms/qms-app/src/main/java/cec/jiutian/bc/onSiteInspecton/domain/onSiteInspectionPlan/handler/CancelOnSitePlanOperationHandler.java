package cec.jiutian.bc.onSiteInspecton.domain.onSiteInspectionPlan.handler;

import cec.jiutian.bc.onSiteInspecton.domain.onSiteInspectionPlan.model.OnSiteInspectionPlan;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/12
 * @description TODO
 */
@Component
public class CancelOnSitePlanOperationHandler implements OperationHandler<OnSiteInspectionPlan, Void> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<OnSiteInspectionPlan> data, Void modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            OnSiteInspectionPlan onSiteInspectionPlan = data.get(0);
            onSiteInspectionPlan.setCurrentState(OrderCurrentStateEnum.Enum.END.name());
            fabosJsonDao.mergeAndFlush(onSiteInspectionPlan);
        }
        return "alert(操作成功)";
    }
}
