package cec.jiutian.bc.qualityExceptionManagement.domain.returnProductHandling.model;

import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleBaseModel;
import cec.jiutian.bc.modeler.enumration.MRBDisposalOpinionEnum;
import cec.jiutian.bc.qualityExceptionManagement.domain.returnProductHandling.proxy.ReturnProductHandlingReviewDataProxy;
import cec.jiutian.bc.qualityExceptionManagement.enums.ReturnProductAnalysisMethodEnum;
import cec.jiutian.bc.qualityExceptionManagement.enums.ReturnProductHandlingStatusEnum;
import cec.jiutian.bc.qualityExceptionManagement.enums.ReturnProductHandlingSuggestionEnum;
import cec.jiutian.bc.qualityExceptionManagement.enums.ReturnProductReviewCommentEnum;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.AttachmentType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.InputType;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "qem_return_product_handling",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        })
@Getter
@Setter
@FabosJson(
        name = "退货产品处理单-评审",
        dataProxy = ReturnProductHandlingReviewDataProxy.class
)
public class ReturnProductHandlingReview extends NamingRuleBaseModel {

    @FabosJsonField(
            views = @View(title = "处置意见"),
            edit = @Edit(title = "处置意见", show = false, type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = MRBDisposalOpinionEnum.class))
    )
    private String handlingSuggestion;

    @FabosJsonField(
            views = @View(title = "分析方法"),
            edit = @Edit(title = "分析方法", show = false, type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ReturnProductAnalysisMethodEnum.class))
    )
    private String analysisMethod;

    @FabosJsonField(
            views = @View(title = "评审意见"),
            edit = @Edit(title = "评审意见", notNull = true, type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ReturnProductReviewCommentEnum.class))
    )
    private String reviewComment;

    @FabosJsonField(
            views = @View(title = "意见说明"),
            edit = @Edit(title = "意见说明",
                    type = EditType.TEXTAREA, inputType = @InputType(length = 400))
    )
    private String commentDescription;

    @FabosJsonField(
            views = @View(title = "附件"),
            edit = @Edit(title = "附件", type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(maxLimit = 10))
    )
    private String attachment;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态", show = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ReturnProductHandlingStatusEnum.class))
    )
    private String status;

    @FabosJsonField(
            views = @View(title = "分析方法关联单据"),
            edit = @Edit(title = "分析方法关联单据", show = false)
    )
    private String analysisMethodOrder;

}
