package cec.jiutian.bc.clientComplaintManagement.enumeration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/05/20
 * @description TODO
 */
public class CustomerComplaintLevelEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum value : Enum.values()) {
            list.add(new VLModel(value.name(), value.getValue()));
        }
        return list;
    }

    //TODO：这个等级的枚举量
    @AllArgsConstructor
    @Getter
    public enum Enum {
        LEVEL_A("A"),
        LEVEL_B("B"),
        LEVEL_C("C"),
        LEVEL_D("D"),
        ;

        private final String value;

    }

}
