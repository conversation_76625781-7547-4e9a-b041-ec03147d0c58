package cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.issueList.proxy;

import cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.issueList.model.IssueListEdit;
import cec.jiutian.bc.qualityExceptionManagement.enumeration.inspectionIssuesImproveAndTrace.IssueListStatusEnum;
import cec.jiutian.view.fun.DataProxy;
import org.springframework.stereotype.Component;

@Component
public class IssueListEditProxy implements DataProxy<IssueListEdit> {
    @Override
    public void beforeAdd(IssueListEdit issueListEdit) {
        issueListEdit.setStatus(IssueListStatusEnum.Enum.WAIT_PUBLISH.name());
    }
}
