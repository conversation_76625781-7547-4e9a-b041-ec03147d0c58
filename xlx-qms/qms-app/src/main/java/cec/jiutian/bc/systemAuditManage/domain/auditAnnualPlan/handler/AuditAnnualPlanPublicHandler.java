package cec.jiutian.bc.systemAuditManage.domain.auditAnnualPlan.handler;

import cec.jiutian.bc.systemAuditManage.domain.auditAnnualPlan.model.AuditAnnualPlan;
import cec.jiutian.bc.systemAuditManage.enumeration.AuditAnnualPlanStatusEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class AuditAnnualPlanPublicHandler implements OperationHandler<AuditAnnualPlan, Void> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<AuditAnnualPlan> data, Void modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            AuditAnnualPlan auditAnnualPlan = data.get(0);
            auditAnnualPlan.setStatus(AuditAnnualPlanStatusEnum.Enum.PUBLICISED.name());
            fabosJsonDao.mergeAndFlush(auditAnnualPlan);
        }

        return "msg.success('操作成功')";
    }
}
