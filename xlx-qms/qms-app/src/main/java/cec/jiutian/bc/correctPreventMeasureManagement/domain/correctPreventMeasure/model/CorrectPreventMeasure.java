package cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.model;

import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.handler.*;
import cec.jiutian.bc.mto.OrgMTO;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.enumration.*;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.mto.*;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.proxy.CorrectPreventMeasureDataProxy;
import cec.jiutian.bc.urm.domain.user.entity.User;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/28
 * @description TODO
 */
@FabosJson(
        name = "纠正预防措施",
        orderBy = "CorrectPreventMeasure.createTime desc",
        power = @Power(add = false, edit = false, delete = false),
        dataProxy = CorrectPreventMeasureDataProxy.class,
        rowOperation = {
                @RowOperation(
                        title = "创建",
                        code = "CorrectPreventMeasure@CREATE",
                        mode = RowOperation.Mode.BUTTON,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.ADD,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = CorrectPreventMeasureCreateMTO.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "CorrectPreventMeasure@CREATE"
                        )
                ),
                @RowOperation(
                        title = "编辑",
                        code = "CorrectPreventMeasure@MODIFY",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = CorrectPreventMeasureCreateMTO.class,
                        operationHandler = CorrectPreventMeasureModifyHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "CorrectPreventMeasure@MODIFY"
                        ),
                        ifExpr = "selectedItems[0].businessState != 'TO_BE_RELEASED'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "提交",
                        code = "CorrectPreventMeasure@SUBMIT",
                        operationHandler = CorrectPreventMeasureSubmitHandler.class,
                        mode = RowOperation.Mode.HEADER,
                        callHint = "该操作不可撤回，确定提交吗？",
                        ifExpr = "selectedItems[0].businessState != 'TO_BE_RELEASED'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "CorrectPreventMeasure@SUBMIT"
                        )
                ),
                @RowOperation(
                        title = "纠正",
                        code = "CorrectPreventMeasure@CORRECT",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.UPDATE,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = CorrectPreventMeasureCorrectMTO.class,
                        operationHandler = CorrectPreventMeasureCorrectHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "CorrectPreventMeasure@CORRECT"
                        ),
                        ifExpr = "selectedItems[0].businessState != 'TO_BE_CORRECTED'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "纠正发布",
                        code = "CorrectPreventMeasure@CORRECTRELEASE",
                        operationHandler = CorrectPreventMeasureCorrectReleaseHandler.class,
                        mode = RowOperation.Mode.HEADER,
                        callHint = "该操作不可撤回，确定提交吗？",
                        //  || selectedItems[0].correctiveState != 'RUN_SUBMIT'
                        ifExpr = "selectedItems[0].businessState != 'TO_BE_CORRECTED' || selectedItems[0].correctiveState != 'RUN_SUBMIT'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "CorrectPreventMeasure@CORRECTRELEASE"
                        )
                ),
                @RowOperation(
                        title = "原因分析",
                        code = "CorrectPreventMeasure@CAUSEANALYSIS",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.UPDATE,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = CorrectPreventMeasureCauseAnalyMTO.class,
                        operationHandler = CorrectPreventMeasureCauseAnalyHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "CorrectPreventMeasure@CAUSEANALYSIS"
                        ),
                        ifExpr = "selectedItems[0].businessState != 'TO_BE_ANALYZED'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "分析提交",
                        code = "CorrectPreventMeasure@ANALYSUBMIT",
                        operationHandler = CorrectPreventMeasureAnalySubmitHandler.class,
                        mode = RowOperation.Mode.HEADER,
                        callHint = "该操作不可撤回，确定提交吗？",
                        //   || selectedItems[0].analyticalState != 'RUN_SUBMIT'
                        ifExpr = "selectedItems[0].businessState != 'TO_BE_ANALYZED' || selectedItems[0].analyticalState != 'RUN_SUBMIT'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "CorrectPreventMeasure@ANALYSUBMIT"
                        )
                ),
                @RowOperation(
                        title = "纠正验证",
                        code = "CorrectPreventMeasure@CORRECTVERI",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = CorrectPreventMeasureCorrectVeriMTO.class,
                        operationHandler = CorrectPreventMeasureCorrectVeriHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "CorrectPreventMeasure@CORRECTVERI"
                        ),
                        ifExpr = "selectedItems[0].businessState != 'TO_BE_VERIFIED' || selectedItems[0].correctVeriState != 'WAIT_RUN'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "预防验证",
                        code = "CorrectPreventMeasure@PREVERI",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = CorrectPreventMeasurePreVeriMTO.class,
                        operationHandler = CorrectPreventMeasurePreVeriHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "CorrectPreventMeasure@PREVERI"
                        ),
                        ifExpr = "selectedItems[0].businessState != 'TO_BE_VERIFIED' || selectedItems[0].preVeriState != 'WAIT_RUN'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "关闭",
                        code = "CorrectPreventMeasure@CLOSE",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = CorrectPreventMeasureCloseMTO.class,
                        operationHandler = CorrectPreventMeasureCloseHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "CorrectPreventMeasure@CLOSE"
                        ),
                        ifExpr = "selectedItems[0].businessState != 'COMPLETED'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
        }
)
@Table(name = "qms_cpm_correct_prevent_measure",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"correctPreventMeasureFormNumber"})
        }
)
@Entity
@Getter
@Setter
public class CorrectPreventMeasure extends MetaModel {

    //创建
    @FabosJsonField(
            views = @View(title = "单据编号"),
            edit = @Edit(title = "单据编号",
                    readonly = @Readonly(edit = false),
                    notNull = true,
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(dependFiled = @DependFiled(buttonName = "生成", changeBy = "id",
                    dynamicHandler = CorrectPreventMeasureDynamicHandler.class))
    )
    private String correctPreventMeasureFormNumber;

    @FabosJsonField(
            views = @View(title = "创建日期"),
            edit = @Edit(title = "创建日期",
                    show = false,
                    type = EditType.DATE,
                    search = @Search(),
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @CreationTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date createdTime;

    @FabosJsonField(
            views = @View(title = "关联单据"),
            edit = @Edit(title = "关联单据",
                    type = EditType.CHOICE,
                    notNull = true,
                    search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = RelatedDocumentTypeEnum.class)
            )
    )
    private String relatedDocumentType;

//    @FabosJsonField(
//            views = @View(title = "关联单据", column = "name"),
//            edit = @Edit(title = "关联单据",
//                    notNull = true,
//                    search = @Search(),
//                    type = EditType.REFERENCE_TABLE,
//                    queryCondition = "{\"type\":\"${relatedDocumentType}\"}",
//                    allowAddMultipleRows = false,
//                    referenceTableType = @ReferenceTableType(type = TabTableReferType.SelectShowTypeMTM.LIST)
//            )
//    )
//    @ManyToOne
//    @JoinColumn(name = "related_document_mto_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
//    private RelatedDocumentMTO relatedDocumentMTO;

    @FabosJsonField(
            views = @View(title = "关联单据编码",show = false),
            edit = @Edit(title = "关联单据编码",
                    show = false
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "relatedDocumentMTO", beFilledBy = "code"))
    )
    private String relatedDocumentMTOCode;

    @FabosJsonField(
            views = @View(title = "关联单据"),
            edit = @Edit(title = "关联单据",
                    readonly = @Readonly,
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "relatedDocumentMTO", beFilledBy = "name"))
    )
    private String relatedDocumentMTOName;

    @FabosJsonField(
            views = @View(title = "不合格（潜在不合格）陈述"),
            edit = @Edit(title = "不合格（潜在不合格）陈述",
                    search = @Search(),
                    type = EditType.TEXTAREA)
    )
    private String nonConformanceStatement;

    @FabosJsonField(
            views = @View(title = "不合格（潜在不合格）情况核实（初步原因分析）"),
            edit = @Edit(title = "不合格（潜在不合格）情况核实（初步原因分析）",
                    search = @Search(),
                    type = EditType.TEXTAREA)
    )
    private String verificationNonConformity;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "提出人", column = "name"),
            edit = @Edit(title = "提出人",
                    type = EditType.REFERENCE_TABLE,
                    search = @Search(),
                    show = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name")
            )
    )
    private User createUser;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",
                    show = false,
                    type = EditType.CHOICE,
                    search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = CpmBusinessStateEnum.class)
            )
    )
    private String businessState;

    //纠正
    @FabosJsonField(
            views = @View(title = "责任部门", column = "name"),
            edit = @Edit(title = "责任部门",
                    search = @Search(),
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType(type = TabTableReferType.SelectShowTypeMTM.LIST)
            )
    )
    @ManyToOne
    @JoinColumn(name = "response_department_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private OrgMTO orgMTO;

    //TODO: 这个框到底是什么
    @FabosJsonField(
            views = @View(title = "原因分析纳期"),
            edit = @Edit(title = "原因分析纳期",
                    search = @Search(),
                    dateType = @DateType(type = DateType.Type.DATE_TIME),
                    notNull = true
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date reasonAnalysisDeliveryTime;

    @FabosJsonField(
            edit = @Edit(title = "纠正措施", type = EditType.TAB_TABLE_ADD),
            views = @View(title = "纠正措施", type = ViewType.TABLE_VIEW)
    )
    @JoinColumn(name = "qms_cpm_correct_prevent_measure_id")
    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
    @OrderBy
    private List<CorrectiveAction> correctiveActionList;

    @FabosJsonField(
            views = @View(title = "纠正状态", show = false),
            edit = @Edit(title = "纠正状态",
                    readonly = @Readonly(),
                    type = EditType.CHOICE,
                    show = false,
                    choiceType = @ChoiceType(fetchHandler = ProgressEnum.class)
            )
    )
    private String correctiveState;

    @FabosJsonField(
            views = @View(title = "不合格（潜在）根本原因分析（5why分析法）"),
            edit = @Edit(title = "不合格（潜在）根本原因分析（5why分析法）",
                    type = EditType.TEXTAREA)
    )
    private String rootCauseAnalysis;

    @FabosJsonField(
            views = @View(title = "原因分析附件"),
            edit = @Edit(title = "原因分析附件", type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType)
    )
    private String causeAnalysisAttachments;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "分析人", column = "name"),
            edit = @Edit(title = "分析人",
                    type = EditType.REFERENCE_TABLE,
                    search = @Search(),
                    referenceTableType = @ReferenceTableType(id = "id", label = "name")
            )
    )
    private User analyticalUser;

    @FabosJsonField(
            views = @View(title = "分析时间"),
            edit = @Edit(title = "分析时间",
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date analyticalTime;

    @FabosJsonField(
            edit = @Edit(title = "预防措施", type = EditType.TAB_TABLE_ADD),
            views = @View(title = "预防措施", type = ViewType.TABLE_VIEW)
    )
    @JoinColumn(name = "qms_cpm_correct_prevent_measure_id")
    @OneToMany(cascade = CascadeType.ALL)
    @OrderBy
    private List<PreventMeasure> preventMeasureList;

    @FabosJsonField(
            views = @View(title = "分析状态", show = false),
            edit = @Edit(title = "分析状态",
                    readonly = @Readonly(),
                    type = EditType.CHOICE,
                    show = false,
                    choiceType = @ChoiceType(fetchHandler = ProgressEnum.class)
            )
    )
    private String analyticalState;

    @FabosJsonField(
            views = @View(title = "纠正验证状态", show = false),
            edit = @Edit(title = "纠正验证状态",
                    readonly = @Readonly(),
                    type = EditType.CHOICE,
                    show = false,
                    choiceType = @ChoiceType(fetchHandler = ProgressEnum.class)
            )
    )
    private String correctVeriState;

    @FabosJsonField(
            views = @View(title = "预防验证状态", show = false),
            edit = @Edit(title = "预防验证状态",
                    readonly = @Readonly(),
                    type = EditType.CHOICE,
                    show = false,
                    choiceType = @ChoiceType(fetchHandler = ProgressEnum.class)
            )
    )
    private String preVeriState;

    @FabosJsonField(
            views = @View(title = "关闭审批意见", show = false),
            edit = @Edit(title = "关闭审批意见",
                    type = EditType.CHOICE,
                    notNull = true,
                    choiceType = @ChoiceType(fetchHandler = VerificationResultEnum.class)
            )
    )
    private String closeApprovalComments;

    @FabosJsonField(
            views = @View(title = "意见说明", show = false),
            edit = @Edit(title = "意见说明",
                    type = EditType.TEXTAREA)
    )
    private String opinionExplanation;

    @FabosJsonField(
            views = @View(title = "附件", show = false),
            edit = @Edit(title = "附件", type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType)
    )
    private String closeAttachments;
}
