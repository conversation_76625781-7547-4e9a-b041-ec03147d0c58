package cec.jiutian.bc.productExamineManagement.domain.examineImplementPlan.handler;

import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.model.CorrectPreventMeasure;
import cec.jiutian.bc.flow.enums.ExamineStatusEnum;
import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.modeler.enumration.NamingRuleCodeEnum;
import cec.jiutian.bc.productExamineManagement.domain.examineImplementPlan.model.ExamineImplementPlan;
import cec.jiutian.bc.productExamineManagement.domain.examineReport.model.ExamineReport;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/13
 * @description TODO
 */
@Component
public class GenerateExamineReportOprHandler implements OperationHandler<ExamineImplementPlan, ExamineReport> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private NamingRuleService namingRuleService;

    @Override
    public String exec(List<ExamineImplementPlan> data, ExamineReport modelObject, String[] param) {
        ExamineImplementPlan examineImplementPlan = data.get(0);
        if (examineImplementPlan.getCorrectPreventMeasureId() != null) {
            CorrectPreventMeasure measure = fabosJsonDao.getById(CorrectPreventMeasure.class, examineImplementPlan.getCorrectPreventMeasureId());
            if (measure != null) {
                modelObject.setIsCorrectPreventMeasure(examineImplementPlan.getIsCorrectPreventMeasure());
                modelObject.setCorrectPreventMeasure(measure);
            }
        }
        modelObject.setExamineStatus(ExamineStatusEnum.UNAUDITED.getCode());
        fabosJsonDao.mergeAndFlush(modelObject);
        return "alert(操作成功)";
    }

    @Override
    public ExamineReport fabosJsonFormValue(List<ExamineImplementPlan> data, ExamineReport fabosJsonForm, String[] param) {
        fabosJsonForm.setCurrentState(OrderCurrentStateEnum.Enum.EDIT.name());
        fabosJsonForm.setGeneralCode(String.valueOf(namingRuleService.getNameCode(NamingRuleCodeEnum.ExamineImplementPlan.name(), 1, null).get(0)));
        return fabosJsonForm;
    }
}
