package cec.jiutian.bc.correctPreventMeasureManagement.remote.provider;

import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.enumration.CpmBusinessStateEnum;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.enumration.ImprovingProgressEnum;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.model.CorrectPreventMeasure;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.model.CorrectiveAction;
import cec.jiutian.bc.ecs.dto.SendMsgGroupDTO;
import cec.jiutian.bc.ecs.provider.EcsMessageProvider;
import cec.jiutian.bc.job.provider.IJobProvider;
import cec.jiutian.core.frame.annotation.FabosCustomizedService;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.meta.FabosJob;
import jakarta.annotation.Resource;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/6
 * @description 定时任务提醒任务
 */
@FabosCustomizedService(value = CorrectPreventMeasure.class)
@Slf4j
@Component
@Transactional
public class CorrectPreventMeasureScheduleProvider implements IJobProvider {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private EcsMessageProvider ecsMessageProvider;

    @FabosJob(comment = "定时提醒")
    @Override
    public String exec(String code, String param) {
        CorrectPreventMeasure condition = new CorrectPreventMeasure();
        condition.setBusinessState(CpmBusinessStateEnum.Enum.IN_PROGRESS.name());
        List<CorrectPreventMeasure> modelList = fabosJsonDao.select(condition);
        log.info("The CorrectPreventMeasure list size is :" + modelList.size());
        overdueReminder(modelList);
        return null;
    }

    private void overdueReminder(List<CorrectPreventMeasure> modelList) {
        if (modelList == null) {
            return;
        }

        for (CorrectPreventMeasure model : modelList) {
            model = fabosJsonDao.findById(CorrectPreventMeasure.class, model.getId());
            List<CorrectiveAction> correctiveActionList = model.getCorrectiveActionList();
            if (correctiveActionList == null) {
                continue;
            }
            String formNumber = model.getCorrectPreventMeasureFormNumber();
            for (CorrectiveAction correctiveAction : correctiveActionList) {
                if (isNeedReminder(correctiveAction)) {
                    //超期提醒
                    sendQms(formNumber, correctiveAction);
                    log.info("Overdue Reminder:" + formNumber + "; Corrective Action:" + correctiveAction.getCorrect());
                }
            }
        }
    }

    private boolean isNeedReminder(CorrectiveAction correctiveAction) {
        if (correctiveAction.getImprovingProgress().equals(ImprovingProgressEnum.Enum.TO_BE_CORRECTED.name())) {
            long deliveryTime = correctiveAction.getDeliveryTime().getTime();
            long nowTime = (new Date()).getTime();

            if (nowTime >= deliveryTime) {
                return true;
            }

            long diffMillis = Math.abs(deliveryTime - nowTime);
            double last = diffMillis / (3600.0 * 1000 * 24);
            return last < 1.0;
        }
        return false;
    }

    private void sendQms(String formNumber, CorrectiveAction correctiveAction) {
        SendMsgGroupDTO sendMsgGroupDTO = new SendMsgGroupDTO();
        sendMsgGroupDTO.setMessageGroupCode("CorrectPreventMeasureInfo");
        sendMsgGroupDTO.setContent("请尽快在" + correctiveAction.getDeliveryTime() + "前完成纠正措施，单据编号：" +formNumber + "纠正单号：" + correctiveAction.getCorrect());
        ecsMessageProvider.sendGroupMessage(sendMsgGroupDTO);
    }
}
