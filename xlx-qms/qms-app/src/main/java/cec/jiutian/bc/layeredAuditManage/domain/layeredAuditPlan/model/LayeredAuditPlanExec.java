package cec.jiutian.bc.layeredAuditManage.domain.layeredAuditPlan.model;

import cec.jiutian.bc.systemAuditManage.domain.auditImplementPlan.model.ExecPlanAuditItem;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.ReferenceGenerateType;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@FabosJson(
        name = "执行",
        orderBy = "LayeredAuditPlanExec.createTime desc"
)
@Table(name = "qms_sam_layered_audit_plan", uniqueConstraints = @UniqueConstraint(columnNames = "generalCode"))
@Entity
@Getter
@Setter
public class LayeredAuditPlanExec extends MetaModel {

    @FabosJsonField(
            views = @View(title = "执行详情", type = ViewType.TABLE_VIEW),
            edit = @Edit(title = "执行详情", type = EditType.TAB_REFERENCE_GENERATE),
            referenceGenerateType = @ReferenceGenerateType(editable = {"occurrenceTime","location","issueContent",
                    "responsibleDepartment","type","responsiblePerson","issueLevel","analysisType","image"})
    )
    @JoinColumn(name = "layered_audit_plan_id")
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    private List<ExecPlanDetailMto> execPlanDetailMtoList;
}
