package cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.issueList.model;

import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleBaseModel;
import cec.jiutian.bc.modeler.enumration.NamingRuleCodeEnum;
import cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.issueList.dto.OnSiteInspectionTaskDTO;
import cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.issueList.proxy.IssueListADDProxy;
import cec.jiutian.bc.qualityExceptionManagement.enumeration.inspectionIssuesImproveAndTrace.CorrectiveActionEnum;
import cec.jiutian.bc.qualityExceptionManagement.enumeration.inspectionIssuesImproveAndTrace.InsResultEnum;
import cec.jiutian.bc.qualityExceptionManagement.enumeration.inspectionIssuesImproveAndTrace.IssueListStatusEnum;
import cec.jiutian.bc.qualityExceptionManagement.enumeration.inspectionIssuesImproveAndTrace.SeverityLevelEnum;
import cec.jiutian.bc.urm.domain.org.entity.Org;
import cec.jiutian.core.frame.module.MetaUser;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Power;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@FabosJson(name = "问题清单",
        power = @Power(add = false, delete = false),
        orderBy = "IssueListADD.createTime desc",
        dataProxy = IssueListADDProxy.class
)
@Table(name = "qms_qem_issue_list")
@Entity
@Getter
@Setter
public class IssueListADD extends NamingRuleBaseModel {
    @Override
    public String getNamingCode() {
        return NamingRuleCodeEnum.IssueList.name();
    }

    @Transient
    @FabosJsonField(
            views = @View(title = "巡检任务", show = false, column = "generalCode"),
            edit = @Edit(title = "巡检任务",
                    type = EditType.REFERENCE_TABLE,notNull = true,readonly = @Readonly(add = false),
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode")
            )
    )
    private OnSiteInspectionTaskDTO onSiteInspectionTaskDTO;

    @FabosJsonField(
            views = @View(title = "任务编号"),
            edit = @Edit(title = "任务编号", search = @Search(vague = true),readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "onSiteInspectionTaskDTO", beFilledBy = "generalCode"))
    )
    private String taskGeneralCode;

    @FabosJsonField(
            views = @View(title = "任务名称"),
            edit = @Edit(title = "任务名称", search = @Search(vague = true), readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "onSiteInspectionTaskDTO", beFilledBy = "name"))
    )
    private String name;

    @FabosJsonField(
            views = @View(title = "任务描述"),
            edit = @Edit(title = "任务描述", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "onSiteInspectionTaskDTO", beFilledBy = "description"))

    )
    private String description;

    @FabosJsonField(
            views = @View(title = "巡检标准"),
            edit = @Edit(title = "巡检标准", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "onSiteInspectionTaskDTO", beFilledBy = "inspectionStandard"))
    )
    private String inspectionStandard;

    @FabosJsonField(
            views = @View(title = "巡检项目"),
            edit = @Edit(title = "巡检项目", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "onSiteInspectionTaskDTO", beFilledBy = "inspectionProject"))
    )
    private String inspectionProject;

    @FabosJsonField(
            views = @View(title = "巡检内容", toolTip = true),
            edit = @Edit(title = "巡检内容", search = @Search(vague = true), readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "onSiteInspectionTaskDTO", beFilledBy = "content"))
    )
    private String content;

    @FabosJsonField(
            views = @View(title = "巡检结果", toolTip = true),
            edit = @Edit(title = "巡检结果",readonly = @Readonly,type= EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler =  InsResultEnum.class)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "onSiteInspectionTaskDTO", beFilledBy = "insResult"))
    )
    private String insResult;

    @FabosJsonField(
            views = @View(title = "问题描述"),
            edit = @Edit(title = "问题描述", notNull = true)
    )
    private String issueDescription;

    //严重程度  轻微 一般 严重
    @FabosJsonField(
            views = @View(title = "严重程度"),
            edit = @Edit(title = "严重程度",type = EditType.CHOICE,notNull = true,
                    choiceType = @ChoiceType(fetchHandler = SeverityLevelEnum.class))
    )
    private String severityLevel;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "责任部门", column = "name"),
            edit = @Edit(title = "责任部门",notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"))
    )
    @JoinColumn(foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private Org org;
    @ManyToOne
    @FabosJsonField(
            views = @View(title = "责任人", column = "name"),
            edit = @Edit(title = "责任人",notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"))
    )
    @JoinColumn(foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private MetaUser metaUser;

    // todo 关联单据后 回显
    @FabosJsonField(
            views = @View(title = "分析对策"),
            edit = @Edit(title = "分析对策",type = EditType.CHOICE,notNull = true,
                    choiceType = @ChoiceType(fetchHandler = CorrectiveActionEnum.class))
    )
    private String correctiveAction;

    @FabosJsonField(
            views = @View(title = "措施纳期"),
            edit = @Edit(title = "措施纳期", notNull = true, dateType = @DateType(type = DateType.Type.DATE_TIME))
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date measuresDeadline;

    @FabosJsonField(
            views = @View(title = "问题重复次数"),
            edit = @Edit(title = "问题重复次数", numberType = @NumberType(min = 0), notNull = true)
    )
    private Integer issueRepeatedTimes;
    //问题附件
    @FabosJsonField(
            views = @View(title = "问题附件"),
            edit = @Edit(title = "问题附件",
                    type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(size = 5120, maxLimit = 10, type = AttachmentType.Type.BASE)
            )
    )
    private String attachment;

    //状态
    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",type = EditType.CHOICE,show = false,
                    choiceType = @ChoiceType(fetchHandler = IssueListStatusEnum.class))
    )
    private String status;
}



