package cec.jiutian.bc.samplingTaskManagement.domain.otherSamplingTask.handler;

import cec.jiutian.bc.basicData.enumeration.ApplyRangeEnum;
import cec.jiutian.bc.basicData.enumeration.NamingRuleCodeEnum;
import cec.jiutian.bc.deliveryInspection.enumeration.InspectionResultEnum;
import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.generalModeler.util.StringUtils;
import cec.jiutian.bc.materialInspect.domain.publicInspectionTask.mto.UserForInsTaskMTO;
import cec.jiutian.bc.modeler.enumration.TaskBusinessStateEnum;
import cec.jiutian.bc.modeler.enumration.UnqualifiedHandleWayEnum;
import cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.model.AbnormalFeedbackHandling;
import cec.jiutian.bc.samplingTaskManagement.domain.otherSamplingTask.model.OtherSamplingTask;
import cec.jiutian.bc.samplingTaskManagement.domain.otherSamplingTask.model.OtherSamplingTaskDetail;
import cec.jiutian.bc.samplingTaskManagement.domain.otherSamplingTask.model.OtherSamplingTaskReceiveOpr;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/12
 * @description TODO
 */
@Component
public class ReceiveOtherSampleOperationHandler implements OperationHandler<OtherSamplingTask, OtherSamplingTaskReceiveOpr> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private NamingRuleService namingRuleService;

    @Override
    public String exec(List<OtherSamplingTask> data, OtherSamplingTaskReceiveOpr modelObject, String[] param) {
        if (modelObject != null) {
            OtherSamplingTask condition = new OtherSamplingTask();
            condition.setGeneralCode(modelObject.getGeneralCode());
            OtherSamplingTask otherSamplingTask = fabosJsonDao.selectOne(condition);
            if (otherSamplingTask == null) {
                throw new FabosJsonApiErrorTip("未查询到取样单，请确认");
            }
            if (!TaskBusinessStateEnum.Enum.SEND_SAMPLE.name().equals(otherSamplingTask.getBusinessState())) {
                throw new FabosJsonApiErrorTip("取样任务单状态有误");
            }

            if (InspectionResultEnum.Enum.QUALIFIED.name().equals(modelObject.getAppearanceInspect())) {
                otherSamplingTask.setBusinessState(TaskBusinessStateEnum.Enum.RECEIVED_SAMPLE.name());
            }else {
                if (UnqualifiedHandleWayEnum.Enum.rePackage.name().equals(modelObject.getUnqualifiedHandle())) {
                    otherSamplingTask.setBusinessState(TaskBusinessStateEnum.Enum.SAMPLING_FINISH.name());
                }else if (UnqualifiedHandleWayEnum.Enum.reSampling.name().equals(modelObject.getUnqualifiedHandle())) {
                    otherSamplingTask.setBusinessState(TaskBusinessStateEnum.Enum.EXCEPTION_STOP.name());
                    if (!ApplyRangeEnum.Enum.Other.name().equals(otherSamplingTask.getInspectionType())) {
                        OtherSamplingTask newOtherSamplingTask = new OtherSamplingTask();
                        BeanUtil.copyProperties(otherSamplingTask, newOtherSamplingTask);
                        newOtherSamplingTask.setId(null);
                        newOtherSamplingTask.setDetailList(null);
                        List<OtherSamplingTaskDetail> detailList = new ArrayList<>();
                        otherSamplingTask.getDetailList().forEach(detail -> {
                            OtherSamplingTaskDetail newDetail = new OtherSamplingTaskDetail();
                            BeanUtil.copyProperties(detail, newDetail);
                            newDetail.setId(null);
                            newDetail.setOtherSamplingTask(newOtherSamplingTask);
                            detailList.add(newDetail);
                        });
                        newOtherSamplingTask.setDetailList(detailList);
                        fabosJsonDao.mergeAndFlush(newOtherSamplingTask);
                    }
                } else if (UnqualifiedHandleWayEnum.Enum.exception.name().equals(modelObject.getUnqualifiedHandle())) {
                    if (StringUtils.isNotEmpty(modelObject.getAbnormalDescription())) {
                        AbnormalFeedbackHandling abnormalFeedbackHandling = new AbnormalFeedbackHandling();
                        abnormalFeedbackHandling.setAbnormalFeedbackHandlingFormNumber(namingRuleService.getNameCode(NamingRuleCodeEnum.ABNORMAL_FEEDBACK_HANDLING.name(), 1, null).get(0));
                        abnormalFeedbackHandling.setAnalyzeAssociatedDocumentNumber(modelObject.getGeneralCode());
                        abnormalFeedbackHandling.setAbnormalDescription(modelObject.getAbnormalDescription());
                        abnormalFeedbackHandling.setSubmissionTime(modelObject.getSubmissionTime());
                        abnormalFeedbackHandling.setDiscoveredPersonId(modelObject.getDiscoveredPersonId());
                        UserForInsTaskMTO userForInsTaskMTO = fabosJsonDao.getById(UserForInsTaskMTO.class, modelObject.getDiscoveredPersonId());
                        abnormalFeedbackHandling.setDiscoveredPersonName(userForInsTaskMTO == null ? null : userForInsTaskMTO.getName());
                        abnormalFeedbackHandling.setAbnormalAttachments(modelObject.getAbnormalAttachments());
                        fabosJsonDao.mergeAndFlush(abnormalFeedbackHandling);
                    }
                }
            }
            otherSamplingTask.setReceiveSamplePersonId(modelObject.getReceiveSamplePersonId());
            otherSamplingTask.setReceiveSamplePerson(modelObject.getReceiveSamplePerson());
            otherSamplingTask.setReceiveSampleDate(modelObject.getReceiveSampleDate());
            fabosJsonDao.mergeAndFlush(modelObject);
            fabosJsonDao.mergeAndFlush(otherSamplingTask);
        }
        return "alert(操作成功)";
    }
}
