package cec.jiutian.bc.qualityPreparationPlan.domain.model;

import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.type.Power;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@FabosJson(
        name = "质量准备计划-检验项明细",
        power = @Power(add = false, edit = false, delete = false)
)
@Table(name = "qms_quality_preparation_plan_detail_item")
@Entity
@Getter
@Setter
public class QualityPreparationDetailItem extends MetaModel {

    @FabosJsonField(
            views = @View(title = "工序Code",show = false),
            edit = @Edit(title = "工序Code",show = false)
    )
    private String processCode;

    @FabosJsonField(
            views = @View(title = "工序"),
            edit = @Edit(title = "工序")
    )
    private String processName;

    @FabosJsonField(
            views = @View(title = "检验项"),
            edit = @Edit(title = "检验项")
    )
    private String itemName;

    @FabosJsonField(
            views = @View(title = "检验任务数"),
            edit = @Edit(title = "检验任务数", notNull = true,
                    numberType = @NumberType(min = 0))
    )
    private Integer inspectQuantity;
}
