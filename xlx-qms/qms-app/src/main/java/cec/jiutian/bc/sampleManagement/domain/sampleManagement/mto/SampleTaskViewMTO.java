package cec.jiutian.bc.sampleManagement.domain.sampleManagement.mto;

import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.DateType;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/05/23
 * @description TODO
 */
@FabosJson(
        name = "取样任务MTO"
)
@Entity
@Getter
@Setter
@Table(name = "sample_task_view")
public class SampleTaskViewMTO {
    @Id
    @FabosJsonField(
            edit = @Edit(title = "", show = false)
    )
    private String id;

    @FabosJsonField(
            views = @View(title = "取样任务单号"),
            edit = @Edit(title = "取样任务单号")
    )
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "物资名称"),
            edit = @Edit(title = "物资名称", notNull = true)
    )
    private String materialName;

    @FabosJsonField(
            views = @View(title = "物资编码"),
            edit = @Edit(title = "物资编码", notNull = true)
    )
    private String materialCode;

    @FabosJsonField(
            views = @View(title = "型号规格"),
            edit = @Edit(title = "型号规格", notNull = true)
    )
    private String modelSpec;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "取样时期", type = ViewType.DATE),
            edit = @Edit(title = "取样时期",
                    dateType = @DateType(type = DateType.Type.DATE))
    )
    private Date sampleTime;

    @FabosJsonField(
            views = @View(title = "取样量"),
            edit = @Edit(title = "取样量", notNull = true)
    )
    private Double amount;

    @FabosJsonField(
            views = @View(title = "检验任务单号"),
            edit = @Edit(title = "检验任务单号", notNull = true)
    )
    private String inspectionTaskCode;

    @FabosJsonField(
            views = @View(title = "表单类型"),
            edit = @Edit(title = "表单类型", notNull = true)
    )
    private String formType;
}
