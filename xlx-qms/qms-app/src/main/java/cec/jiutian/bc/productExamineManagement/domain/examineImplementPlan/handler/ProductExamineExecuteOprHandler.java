package cec.jiutian.bc.productExamineManagement.domain.examineImplementPlan.handler;

import cec.jiutian.bc.basicData.enumeration.NamingRuleCodeEnum;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.enumration.CpmBusinessStateEnum;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.enumration.RelatedDocumentTypeEnum;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.model.CorrectPreventMeasure;
import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.productExamineManagement.domain.examineImplementPlan.model.ExamineImplementPlan;
import cec.jiutian.bc.productExamineManagement.domain.examineImplementPlan.mto.ProductExamineExecuteDetailMTO;
import cec.jiutian.bc.productExamineManagement.domain.examineImplementPlan.mto.ProductExamineExecuteMTO;
import cec.jiutian.bc.productExamineManagement.domain.inspectionTask.model.ProductExamineInspectionItemDetail;
import cec.jiutian.bc.productExamineManagement.domain.inspectionTask.model.ProductExamineInspectionTask;
import cec.jiutian.bc.productExamineManagement.enumration.ExamineImplementStateEnum;
import cec.jiutian.bc.productExamineManagement.enumration.ItemUnQualifiedLevelEnum;
import cec.jiutian.bc.productReturnInspection.enumeration.InspectionResultEnum;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import jakarta.persistence.TypedQuery;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/24
 * @description TODO
 */
@Component
public class ProductExamineExecuteOprHandler implements OperationHandler<ExamineImplementPlan, ProductExamineExecuteMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private NamingRuleService namingRuleService;

    @Override
    public String exec(List<ExamineImplementPlan> data, ProductExamineExecuteMTO modelObject, String[] param) {
        ExamineImplementPlan examineImplementPlan = data.get(0);
        examineImplementPlan.setKQZScore(modelObject.getKqzValue());
        examineImplementPlan.setBusinessState(ExamineImplementStateEnum.Enum.Examine.name());
        examineImplementPlan.setIsCorrectPreventMeasure(modelObject.getIsCorrectPreventMeasure());

        // 创建纠正预防措施
        if (modelObject.getIsCorrectPreventMeasure()) {
            CorrectPreventMeasure measure = new CorrectPreventMeasure();
            measure.setCorrectPreventMeasureFormNumber(namingRuleService.getNameCode(NamingRuleCodeEnum.CORRECT_PREVENT_MEASURE.name(), 1, null).get(0));
            measure.setRelatedDocumentType(RelatedDocumentTypeEnum.Enum.MRBUnqualifiedReview.name());
            measure.setRelatedDocumentMTOCode(examineImplementPlan.getGeneralCode());
            measure.setRelatedDocumentMTOName(examineImplementPlan.getGeneralCode());
            measure.setNonConformanceStatement("产品审核实施计划号：" + examineImplementPlan.getGeneralCode());
            measure.setBusinessState(CpmBusinessStateEnum.Enum.TO_BE_RELEASED.name());
            CorrectPreventMeasure measureData = fabosJsonDao.mergeAndFlush(measure);
            examineImplementPlan.setCorrectPreventMeasureId(measureData.getId());
        }
        fabosJsonDao.mergeAndFlush(examineImplementPlan);
        return "alert(操作成功)";
    }

    @Override
    public ProductExamineExecuteMTO fabosJsonFormValue(List<ExamineImplementPlan> data, ProductExamineExecuteMTO fabosJsonForm, String[] param) {
        ExamineImplementPlan examineImplementPlan = data.get(0);
        String hql = "from ProductExamineInspectionTask task where task.examineImplementPlan.generalCode = :planCode and task.currentState = :currentState";
        TypedQuery<ProductExamineInspectionTask> query = fabosJsonDao.getEntityManager().createQuery(hql, ProductExamineInspectionTask.class);
        query.setParameter("planCode",examineImplementPlan.getGeneralCode());
        query.setParameter("currentState",OrderCurrentStateEnum.Enum.COMPLETE.name());
        List<ProductExamineInspectionTask> productExamineInspectionTasks = query.getResultList();

        if (CollectionUtils.isNotEmpty(productExamineInspectionTasks)) {
            ProductExamineInspectionTask productExamineInspectionTask = productExamineInspectionTasks.get(0);
            boolean isCorrectPreventMeasure = false;
            if (CollectionUtils.isNotEmpty(productExamineInspectionTasks)) {
                if (CollectionUtils.isNotEmpty(productExamineInspectionTask.getStandardDetailList())) {
                    double qualifiedQuantity = 0D;
                    double unqualifiedQuantity = 0D;
                    List<ProductExamineExecuteDetailMTO> detailMTOList = new ArrayList<>();
                    for (ProductExamineInspectionItemDetail item : productExamineInspectionTask.getStandardDetailList()) {
                        ProductExamineExecuteDetailMTO productExamineExecuteDetailMTO = new ProductExamineExecuteDetailMTO();
                        productExamineExecuteDetailMTO.setName(item.getName());
                        productExamineExecuteDetailMTO.setInspectionResult(item.getInspectionResult());
                        if (ItemUnQualifiedLevelEnum.Enum.A.name().equals(item.getUnQualifiedLevel()) ||
                                ItemUnQualifiedLevelEnum.Enum.B.name().equals(item.getUnQualifiedLevel())) {
                            isCorrectPreventMeasure = true;
                        }
                        productExamineExecuteDetailMTO.setUnQualifiedLevel(item.getUnQualifiedLevel());
                        if (InspectionResultEnum.Enum.QUALIFIED.name().equals(item.getInspectionResult())) {
                            qualifiedQuantity = qualifiedQuantity + 1;
                        }else {
                            unqualifiedQuantity = unqualifiedQuantity + 1;
                        }
                        detailMTOList.add(productExamineExecuteDetailMTO);
                    }
                    fabosJsonForm.setIsCorrectPreventMeasure(isCorrectPreventMeasure);
                    fabosJsonForm.setQualifiedQuantity(qualifiedQuantity);
                    fabosJsonForm.setUnqualifiedQuantity(unqualifiedQuantity);
                    fabosJsonForm.setDetailMTOList(detailMTOList);
                }
            }
        }else {
            throw new FabosJsonApiErrorTip("请检查实施计划对应检验任务是否审核完成");
        }
        return fabosJsonForm;
    }
}
