package cec.jiutian.bc.qualityPreparationPlan.domain.proxy;

import cec.jiutian.bc.qualityPreparationPlan.domain.model.QualityPreparationPlan;
import cec.jiutian.bc.qualityPreparationPlan.enums.QualityPreparationPlanStatusEnum;
import cec.jiutian.view.fun.DataProxy;
import org.springframework.stereotype.Component;

@Component
public class QualityPreparationPlanProxy implements DataProxy<QualityPreparationPlan> {
    @Override
    public void beforeAdd(QualityPreparationPlan qualityPreparationPlan) {
        qualityPreparationPlan.setStatus(QualityPreparationPlanStatusEnum.Enum.EFFECTIVE.name());
    }
}
