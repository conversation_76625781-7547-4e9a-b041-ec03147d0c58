package cec.jiutian.bc.inventoryInspection.domain.inspectionTask.handler;

import cec.jiutian.bc.basicData.domain.inspectionStandard.model.InspectionStandard;
import cec.jiutian.bc.inventoryInspection.domain.inspectionTask.model.InventoryInspectionTask;
import cec.jiutian.view.DependFiled;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class InvInsStandardDynamicHandler implements DependFiled.DynamicHandler<InventoryInspectionTask> {

    @Override
    public Map<String, Object> handle(InventoryInspectionTask inventoryInspectionTask) {
        Map<String, Object> map = new HashMap<>();
        map.put("inspectionStandard",new InspectionStandard());
        return map;
    }
}
