package cec.jiutian.bc.clientComplaintManagement.domain.myProblemImprovement.proxy;

import cec.jiutian.bc.clientComplaintManagement.domain.myProblemImprovement.model.MyProblemImprovement;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.view.fun.DataProxy;
import cec.jiutian.view.query.Condition;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/06/13
 * @description TODO
 */
@Component
public class MyProblemImprovementDataProxy implements DataProxy<MyProblemImprovement> {
    @Override
    public String beforeFetch(List<Condition> conditions) {
        String userId = UserContext.getUserId();
        if (StringUtils.isBlank(userId)) {
            throw new RuntimeException("当前用户未登录");
        }
        return "MyProblemImprovement.responsiblePersonId = '" + userId + "'";
    }
}
