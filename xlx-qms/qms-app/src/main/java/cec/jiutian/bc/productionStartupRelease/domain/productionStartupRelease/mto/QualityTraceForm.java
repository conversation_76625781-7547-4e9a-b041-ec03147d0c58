package cec.jiutian.bc.productionStartupRelease.domain.productionStartupRelease.mto;

import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleBaseModel;
import cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.enumration.PqtDocumentTypeEnum;
import cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.enumration.ProductQualityTrackBusinessStatusEnum;
import cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.handler.PqtProcessOperationListHandler;
import cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.model.PqtProcessOperation;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DateType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Entity
@Table(name = "qms_pqt_product_quality_track")
@Setter
@Getter
@FabosJson(
        name = "品质跟踪单",
        orderBy = "createTime desc",
        power = @Power(importable = false, print = false,add = false, edit = false,delete = false)
)
public class QualityTraceForm extends NamingRuleBaseModel {
    @FabosJsonField(
            views = @View(title = "单据类型"),
            edit = @Edit(title = "单据类型",
                    readonly = @Readonly,
                    type = EditType.CHOICE, search = @Search(vague = true),
                    choiceType = @ChoiceType(fetchHandler = PqtDocumentTypeEnum.class))
    )
    private String documentType;

    @FabosJsonField(
            views = @View(title = "开机/换线任务单名称"),
            edit = @Edit(title = "开机/换线任务单名称",
                    readonly = @Readonly,
                    search = @Search(vague = true))
    )
    private String mwName;


    @FabosJsonField(
            views = @View(title = "车间名称"),
            edit = @Edit(title = "车间名称",
                    readonly = @Readonly,
                    search = @Search(vague = true)
            )
    )
    private String factoryAreaName;


    @FabosJsonField(
            views = @View(title = "产线名称"),
            edit = @Edit(title = "产线名称",
                    readonly = @Readonly,
                    search = @Search(vague = true))
    )
    private String factoryLineName;

    @FabosJsonField(
            views = @View(title = "产品编码"),
            edit = @Edit(title = "产品编码",
                    readonly = @Readonly,
                    search = @Search(vague = true))
    )
    private String materialCode;

    @FabosJsonField(
            views = @View(title = "产品名称"),
            edit = @Edit(title = "产品名称",
                    readonly = @Readonly,
                    search = @Search(vague = true))
    )
    private String materialName;

    @FabosJsonField(
            views = @View(title = "产品型号"),
            edit = @Edit(title = "产品型号",
                    readonly = @Readonly,
                    search = @Search(vague = true))
    )
    private String materialType;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",
                    readonly = @Readonly,
                    search = @Search(vague = true),
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ProductQualityTrackBusinessStatusEnum.class)
            )
    )
    private String businessStatus;

    @FabosJsonField(
            views = @View(title = "日期"),
            edit = @Edit(title = "日期",
                    readonly = @Readonly,
                    search = @Search(vague = true),
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date documentCreateDate;

    @FabosJsonField(
            views = @View(title = "工序列表",
                    column = "processOperationName",
                    type = ViewType.TABLE_VIEW),
            edit = @Edit(title = "工序列表",
                    readonly = @Readonly,
                    type = EditType.TAB_REFERENCE_GENERATE
            ),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "materialId", dynamicHandler = PqtProcessOperationListHandler.class))
    )
    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumn(name = "qms_pqt_product_quality_track_id")
    private List<PqtProcessOperation> pqtProcessOperationList;
}
