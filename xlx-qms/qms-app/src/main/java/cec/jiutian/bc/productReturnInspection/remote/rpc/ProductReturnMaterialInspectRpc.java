package cec.jiutian.bc.productReturnInspection.remote.rpc;

import cec.jiutian.bc.productReturnInspection.domain.inspectionRequest.model.ProductReturnInspectionRequest;
import cec.jiutian.bc.productReturnInspection.domain.inspectionRequest.model.ProductReturnInspectionRequestDetail;
import cec.jiutian.bc.productReturnInspection.service.ProductReturnMaterialInspectService;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import jakarta.annotation.Resource;
import org.codehaus.plexus.util.StringUtils;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;

@Component
public class ProductReturnMaterialInspectRpc {

    @Resource
    private ProductReturnMaterialInspectService productReturnMaterialInspectService;

    @Resource
    private FabosJsonDao fabosJsonDao;

    /**
     * WMS发布检验通知 todo
     * 1.创建QMS检验通知
     * 2.创建检验任务
     *
     * @param params
     * @return
     */
    public String materialInspectByWMS(String params) throws ParseException {
        if (StringUtils.isNotBlank(params)) {
            JSONObject jsonObject = JSONObject.parseObject(params);
            JSONArray detailList = jsonObject.getJSONArray("detailList");

            ProductReturnInspectionRequest request = new ProductReturnInspectionRequest();
            request.setType(jsonObject.getString("type"));
            request.setGeneralCode(jsonObject.getString("generalCode"));
            request.setSupplierName(jsonObject.getString("supplierName"));

            // 处理详情
            List<ProductReturnInspectionRequestDetail> requestDetails = new ArrayList<>();
            for (int i = 0; i < detailList.size(); i++) {
                JSONObject detailJson = detailList.getJSONObject(i);
                ProductReturnInspectionRequestDetail requestDetail = new ProductReturnInspectionRequestDetail();
                requestDetail.setCOAFlag(detailJson.getString("COAFlag"));
                requestDetail.setOriginLotId(detailJson.getString("originLotId"));
                requestDetail.setLotSerialId(detailJson.getString("lotSerialId"));
                requestDetail.setMaterialCode(detailJson.getString("materialCode"));
                requestDetail.setMaterialName(detailJson.getString("materialName"));
                requestDetail.setMaterialSpecification(detailJson.getString("materialSpecification"));
                requestDetail.setMeasureUnit(detailJson.getString("measureUnit"));
                requestDetail.setSampleFlag(detailJson.getString("sampleFlag"));
                if (StringUtils.isNotBlank(detailJson.getString("stockInQuantity"))) {
                    requestDetail.setLotQuantity(Double.parseDouble(detailJson.getString("stockInQuantity")));
                }
                requestDetail.setInventoryId(detailJson.getString("inventoryId"));

                requestDetails.add(requestDetail);
            }
            request.setDetails(requestDetails);
            ProductReturnInspectionRequest productReturnInspectionRequest = productReturnMaterialInspectService.createInspectionRequest(request);
            return productReturnInspectionRequest.getGeneralCode();
        }
        return "";
    }
}
