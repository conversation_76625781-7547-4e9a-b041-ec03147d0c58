package cec.jiutian.bc.inventoryInspection.domain.samplingTask.handler;

import cec.jiutian.bc.basicData.enumeration.NamingRuleCodeEnum;
import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.inventoryInspection.domain.samplingTask.model.InventorySamplingTask;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/2/27
 * @description 编号生成handler
 */
@Component
public class InventorySamplingCodeGenerateDynamicHandler implements DependFiled.DynamicHandler<InventorySamplingTask> {

    @Resource
    private NamingRuleService namingRuleService;

    @Override
    public Map<String, Object> handle(InventorySamplingTask inventorySamplingTask) {
        Map<String, Object> map = new HashMap<>();
        map.put("generalCode",String.valueOf(namingRuleService.getNameCode(NamingRuleCodeEnum.InspectionTask.name(), 1, null).get(0)));
        return map;
    }
}
