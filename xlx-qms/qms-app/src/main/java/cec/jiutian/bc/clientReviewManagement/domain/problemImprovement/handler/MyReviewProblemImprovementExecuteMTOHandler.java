package cec.jiutian.bc.clientReviewManagement.domain.problemImprovement.handler;

import cec.jiutian.bc.clientComplaintManagement.enumeration.ReviewProblemImproveStatusEnum;
import cec.jiutian.bc.clientReviewManagement.domain.problemImprovement.model.MyReviewProblemImprovement;
import cec.jiutian.bc.clientReviewManagement.domain.problemImprovement.mto.MyReviewProblemImprovementExecuteMTO;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/06/11
 * @description
 */
@Component
public class MyReviewProblemImprovementExecuteMTOHandler implements OperationHandler<MyReviewProblemImprovement, MyReviewProblemImprovementExecuteMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<MyReviewProblemImprovement> data, MyReviewProblemImprovementExecuteMTO modelObject, String[] param) {
        modelObject.setBusinessStatus(ReviewProblemImproveStatusEnum.Enum.CONFIRMING.name());

        MyReviewProblemImprovement model = data.get(0);
        BeanUtil.copyProperties(modelObject, model, "createBy", "createTime");
        fabosJsonDao.mergeAndFlush(model);
        return "alert(操作成功)";
    }

    @Override
    public MyReviewProblemImprovementExecuteMTO fabosJsonFormValue(List<MyReviewProblemImprovement> data, MyReviewProblemImprovementExecuteMTO fabosJsonForm, String[] param) {
        MyReviewProblemImprovement model = data.get(0);
        BeanUtil.copyProperties(model, fabosJsonForm);
        return fabosJsonForm;
    }
}
