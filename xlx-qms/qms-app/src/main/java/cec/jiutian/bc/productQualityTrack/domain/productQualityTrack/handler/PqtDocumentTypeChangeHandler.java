package cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.handler;

import cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.mto.ProductQualityTrackCreateMTO;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class PqtDocumentTypeChangeHandler implements DependFiled.DynamicHandler<ProductQualityTrackCreateMTO> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public Map<String, Object> handle(ProductQualityTrackCreateMTO model) {
        HashMap<String, Object> resultMap = new HashMap<>();
        resultMap.put("machineStartupWireChangeMTO", null);
        resultMap.put("mwGid", null);
        resultMap.put("mwName", null);
        resultMap.put("factoryArea", null);
        resultMap.put("factoryAreaId", null);
        resultMap.put("factoryAreaName", null);
        resultMap.put("factoryLine", null);
        resultMap.put("factoryLineId", null);
        resultMap.put("factoryLineName", null);
        resultMap.put("material", null);
        resultMap.put("materialId", null);
        resultMap.put("materialCode", null);
        resultMap.put("materialName", null);
        resultMap.put("materialType", null);
        return resultMap;
    }
}
