package cec.jiutian.bc.changeRequestManagement.domain.ecr.handler;

import cec.jiutian.bc.changeRequestManagement.domain.ecr.model.ChangeRequest;
import cec.jiutian.bc.changeRequestManagement.enums.ECRStatusEnum;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import jakarta.persistence.LockModeType;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Component
public class ECRPublishPlanHandler implements OperationHandler<ChangeRequest, ChangeRequest> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Transactional
    @Override
    public String exec(List<ChangeRequest> data, ChangeRequest modelObject, String[] param) {
        if (CollectionUtils.isEmpty(data) || data.get(0).getId() == null) {
            throw new ServiceException("数据异常");
        }
        ChangeRequest changeRequest = fabosJsonDao.getEntityManager().find(ChangeRequest.class, data.get(0).getId(), LockModeType.PESSIMISTIC_WRITE);
        if (ECRStatusEnum.Enum.WAIT_APPROVE.name().equals(changeRequest.getStatus())) {
            return "alert('该计划已发布，请刷新页面')";
        }
        if (CollectionUtils.isEmpty(changeRequest.getECRItems())) {
            throw new ServiceException("变更事项不能为空");
        }
        changeRequest.setStatus(ECRStatusEnum.Enum.WAIT_APPROVE.name());
        fabosJsonDao.persistAndFlush(changeRequest);
        return "提交成功";
    }
}
