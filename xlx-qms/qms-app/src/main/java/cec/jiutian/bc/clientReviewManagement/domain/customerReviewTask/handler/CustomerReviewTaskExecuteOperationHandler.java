package cec.jiutian.bc.clientReviewManagement.domain.customerReviewTask.handler;

import cec.jiutian.bc.clientComplaintManagement.enumeration.ReviewProblemImproveStatusEnum;
import cec.jiutian.bc.clientReviewManagement.domain.customerReviewTask.model.CustomerReviewTask;
import cec.jiutian.bc.clientReviewManagement.domain.customerReviewTask.model.CustomerReviewTaskDetailExec;
import cec.jiutian.bc.clientReviewManagement.domain.customerReviewTask.model.CustomerReviewTaskDetailProblemExec;
import cec.jiutian.bc.clientReviewManagement.enumeration.CustomerReviewNatureEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class CustomerReviewTaskExecuteOperationHandler implements OperationHandler<CustomerReviewTask, CustomerReviewTaskDetailExec> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<CustomerReviewTask> data, CustomerReviewTaskDetailExec modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            CustomerReviewTask entity = data.get(0);

            // 更新主任务状态
            entity.setCurrentState(CustomerReviewNatureEnum.Enum.WaitCheck.name());
            // 处理执行详情数据
            if (modelObject != null) {
                // 将执行详情的数据更新到主任务中
                if (modelObject.getReviewResult() != null) {
                    entity.setReviewResult(modelObject.getReviewResult());
                }
                List<CustomerReviewTaskDetailProblemExec> listProblems  = new ArrayList<>();
                modelObject.getCustomerReviewTaskDetailProblemExecs().forEach(m -> {
                    CustomerReviewTaskDetailProblemExec customerReviewTaskDetailProblemExec = new CustomerReviewTaskDetailProblemExec();
                    BeanUtil.copyProperties(m, customerReviewTaskDetailProblemExec);
                    customerReviewTaskDetailProblemExec.setBusinessStatus(ReviewProblemImproveStatusEnum.Enum.DISTRIBUTING.name());
                    customerReviewTaskDetailProblemExec.setGeneralCode(entity.getGeneralCode());
                    listProblems.add(customerReviewTaskDetailProblemExec);
                });
                entity.setExecDetails(listProblems);

                fabosJsonDao.mergeAndFlush(entity);
            }
        }
        return "msg.success('执行成功')";
    }

    @Override
    public CustomerReviewTaskDetailExec fabosJsonFormValue(List<CustomerReviewTask> data, CustomerReviewTaskDetailExec fabosJsonForm, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            CustomerReviewTask customerReviewTask = data.get(0);

            // 将主任务的数据复制到执行详情表单中
            if (customerReviewTask.getReviewResult() != null) {
                fabosJsonForm.setReviewResult(customerReviewTask.getReviewResult());
            }

            // 设置其他初始值
            // fabosJsonForm.setProblemDescription("");
            // fabosJsonForm.setPlannedCompletionDate(new Date());
        }

        return fabosJsonForm;
    }
}