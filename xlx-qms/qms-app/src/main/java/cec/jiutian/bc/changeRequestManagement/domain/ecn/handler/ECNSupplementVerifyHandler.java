package cec.jiutian.bc.changeRequestManagement.domain.ecn.handler;

import cec.jiutian.bc.changeRequestManagement.domain.bo.FlowAssigneeConfig;
import cec.jiutian.bc.changeRequestManagement.domain.bo.FlowNodeBO;
import cec.jiutian.bc.changeRequestManagement.domain.ecn.model.ChangeRequestExecute;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.model.ApproveTask;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.repository.ApproveTaskRepository;
import cec.jiutian.bc.changeRequestManagement.enums.ECRStatusEnum;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import jakarta.persistence.LockModeType;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Component
public class ECNSupplementVerifyHandler implements OperationHandler<ChangeRequestExecute, ChangeRequestExecute> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private FlowAssigneeConfig flowAssigneeConfig;

    @Resource
    private ApproveTaskRepository approveTaskRepository;

    @Transactional
    @Override
    public String exec(List<ChangeRequestExecute> data, ChangeRequestExecute modelObject, String[] param) {
        if (CollectionUtils.isEmpty(data) || data.get(0).getId() == null) {
            throw new ServiceException("数据异常");
        }
        ChangeRequestExecute ecn = fabosJsonDao.getEntityManager().find(ChangeRequestExecute.class, data.get(0).getId(), LockModeType.PESSIMISTIC_WRITE);
        if (ECRStatusEnum.Enum.SUPPLEMENT_VERIFYING.name().equals(ecn.getStatus())) {
            throw new ServiceException("该请求已提交批准，请刷新页面");
        }
        ecn.setStatus(ECRStatusEnum.Enum.SUPPLEMENT_VERIFYING.name());
        fabosJsonDao.mergeAndFlush(ecn);

        //创建审批任务
        Integer maxTurn = approveTaskRepository.findMaxTurnByBusinessKeyAndCode(ecn.getId(), FlowNodeBO.Node.SUPPLEMENT_VERIFY.name());
        if (maxTurn == null) {
            maxTurn = 0;
        }else {
            maxTurn++;
        }

        List<String> roleIds = flowAssigneeConfig.getAssigneeIds(FlowNodeBO.Node.SUPPLEMENT_VERIFY.name());
        for (String roleId : roleIds) {
            ApproveTask approveTask = ApproveTask.createSupplementVerifyTask(ecn.getId(), roleId, maxTurn);
            fabosJsonDao.persistAndFlush(approveTask);
        }
        return "提交成功";
    }

}
