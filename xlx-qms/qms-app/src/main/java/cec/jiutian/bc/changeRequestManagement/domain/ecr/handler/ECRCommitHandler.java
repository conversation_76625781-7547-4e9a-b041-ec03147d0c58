package cec.jiutian.bc.changeRequestManagement.domain.ecr.handler;

import cec.jiutian.bc.changeRequestManagement.domain.bo.FlowNodeBO;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.dto.ECRSubmitDTO;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.model.ApproveTask;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.model.ChangeRequest;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.repository.ApproveTaskRepository;
import cec.jiutian.bc.changeRequestManagement.enums.ECRStatusEnum;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Component
public class ECRCommitHandler implements OperationHandler<ChangeRequest, ECRSubmitDTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private ApproveTaskRepository approveTaskRepository;

    @Transactional
    @Override
    public String exec(List<ChangeRequest> data, ECRSubmitDTO modelObject, String[] param) {
        if (CollectionUtils.isEmpty(data) || data.get(0).getId() == null) {
            throw new ServiceException("数据异常");
        }
        ChangeRequest changeRequest = data.get(0);
        changeRequest.setStatus(ECRStatusEnum.Enum.WAIT_EXAMINE.name());
        fabosJsonDao.mergeAndFlush(changeRequest);
        //创建审批任务
        Integer maxTurn = approveTaskRepository.findMaxTurnByBusinessKeyAndCode(changeRequest.getId(), FlowNodeBO.Node.EXAMINE.name());
        if (maxTurn == null) {
            maxTurn = 0;
        }else {
            maxTurn++;
        }

        ApproveTask approveTask = ApproveTask.createExamineTask(changeRequest.getId(), modelObject,maxTurn);
        fabosJsonDao.persistAndFlush(approveTask);
        return "提交成功";
    }
}
