package cec.jiutian.bc.changeRequestManagement.domain.ecr.repository;

import cec.jiutian.bc.changeRequestManagement.domain.ecr.mto.ProcessMTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface ProcessMTORepository extends JpaRepository<ProcessMTO, String> {

    List<ProcessMTO> findByOutputTimeBetweenAndWorkshopIdAndProductionLineId(Date startTime, Date endTime, Long workshopId, Long productionLineId);
}
