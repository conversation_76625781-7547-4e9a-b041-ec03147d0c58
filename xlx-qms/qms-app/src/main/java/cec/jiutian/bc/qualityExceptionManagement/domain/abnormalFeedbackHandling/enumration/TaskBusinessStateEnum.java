package cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.enumration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/25
 * @description TODO
 */
public class TaskBusinessStateEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum data : Enum.values()) {
            list.add(new VLModel(data.name(), data.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        TO_BE_SUBMITTED("待提交"),
        TO_BE_VERIFIED("待核实"),
        TO_BE_ANALYZED("待分析"),
        PENDING_REVIEW("待评审"),
        PENDING_SCHEME_REVIEW("待验证"),
        COMPLETED("已完成"),
        CLOSED("已关闭"),
        ;

        private final String value;

    }
}
