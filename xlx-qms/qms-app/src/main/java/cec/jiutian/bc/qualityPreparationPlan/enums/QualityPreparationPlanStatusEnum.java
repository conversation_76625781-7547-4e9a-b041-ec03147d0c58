package cec.jiutian.bc.qualityPreparationPlan.enums;

import cec.jiutian.bc.systemAuditManage.enumeration.AuditImplementPlanStatusEnum;
import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

public class QualityPreparationPlanStatusEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum value : Enum.values()) {
            list.add(new VLModel(value.name(), value.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        CREATED("新建"),
        EFFECTIVE("生效"),
        INVALID("失效")
        ;

        private final String value;

    }
}
