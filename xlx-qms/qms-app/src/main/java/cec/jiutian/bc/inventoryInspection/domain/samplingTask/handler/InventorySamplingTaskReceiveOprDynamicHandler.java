package cec.jiutian.bc.inventoryInspection.domain.samplingTask.handler;

import cec.jiutian.bc.inventoryInspection.domain.samplingTask.model.InventorySamplingTaskReceiveOpr;
import cec.jiutian.bc.inventoryInspection.domain.samplingTask.model.InventorySamplingTaskReceiveOprDetail;
import cec.jiutian.bc.materialInspect.domain.publicInspectionTask.mto.UserForInsTaskMTO;
import cec.jiutian.bc.materialInspect.domain.samplingTask.model.SamplingTask;
import cec.jiutian.bc.modeler.enumration.TaskBusinessStateEnum;
import cec.jiutian.bc.processInspect.domain.publicInspectionTask.mto.ProcessUserForInsTaskMTO;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.DependFiled;
import cn.hutool.core.bean.BeanUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：2025/3/20 14:35
 * @description：
 */
@Component
public class InventorySamplingTaskReceiveOprDynamicHandler implements DependFiled.DynamicHandler<InventorySamplingTaskReceiveOpr> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public Map<String, Object> handle(InventorySamplingTaskReceiveOpr inventorySamplingTaskReceiveOpr) {
        Map<String, Object> result = new HashMap<>();
        if (StringUtils.isNotEmpty(inventorySamplingTaskReceiveOpr.getGeneralCode())) {
            InventorySamplingTaskReceiveOpr condition = new InventorySamplingTaskReceiveOpr();
            condition.setGeneralCode(inventorySamplingTaskReceiveOpr.getGeneralCode());
            condition.setBusinessState(TaskBusinessStateEnum.Enum.SEND_SAMPLE.name());
            InventorySamplingTaskReceiveOpr samplingTask = fabosJsonDao.selectOne(condition);
            if (samplingTask == null) {
                throw new FabosJsonApiErrorTip("未查询到取样单，请检查单据是否存在及是否为已送样");
            }
            result.put("inspectionTaskCode",samplingTask.getInspectionTaskCode());
            result.put("samplingTaskCode",samplingTask.getGeneralCode());
            result.put("materialCode", samplingTask.getMaterialCode());
            result.put("materialName", samplingTask.getMaterialName());
            result.put("originLotId", samplingTask.getOriginLotId());
            result.put("allInspectionItemQuantity", samplingTask.getAllInspectionItemQuantity());
            result.put("unit", samplingTask.getUnit());
            result.put("inspectionType", samplingTask.getInspectionType());
            result.put("samplingPoint",samplingTask.getSamplePoint());
            result.put("sendInspectPoint", samplingTask.getSendPoint());
            result.put("packageType",samplingTask.getPackageType());
            if (StringUtils.isNotEmpty(samplingTask.getReceiveSamplePersonId())) {
                UserForInsTaskMTO user = fabosJsonDao.findById(UserForInsTaskMTO.class, samplingTask.getReceiveSamplePersonId());
                if (user != null) {
                    result.put("receiveSamplePersonId", user.getId());
                    result.put("user", user);
                    result.put("receiveSamplePerson",user.getName());
                }
            }
            result.put("submissionTime",samplingTask.getSubmissionTime());
            result.put("receiveSampleDate", samplingTask.getReceiveSampleDate());
            result.put("appearanceInspect",samplingTask.getAppearanceInspect());
            result.put("reviewQuantity",samplingTask.getReviewQuantity());
            result.put("unqualifiedHandle",samplingTask.getUnqualifiedHandle());
            result.put("abnormalDescription",samplingTask.getAbnormalDescription());
            result.put("abnormalAttachments",samplingTask.getAbnormalAttachments());
            if (StringUtils.isNotEmpty(samplingTask.getDiscoveredPersonId())) {
                ProcessUserForInsTaskMTO processUserForInsTaskMTO = fabosJsonDao.findById(ProcessUserForInsTaskMTO.class, samplingTask.getDiscoveredPersonId());
                if (processUserForInsTaskMTO != null) {
                    result.put("discoveredPersonId", processUserForInsTaskMTO.getId());
                    result.put("discoveredPerson", processUserForInsTaskMTO);
                    result.put("discoveredPersonName",processUserForInsTaskMTO.getName());
                }
            } else {
                result.put("discoveredPerson", null);
            }
            SamplingTask samplingTaskData = fabosJsonDao.findById(SamplingTask.class,samplingTask.getId());
            if (CollectionUtils.isNotEmpty(samplingTaskData.getDetailList())) {
                List<InventorySamplingTaskReceiveOprDetail> detailList = new ArrayList<>();
                samplingTaskData.getDetailList().forEach(samplingTaskDetail -> {
                    InventorySamplingTaskReceiveOprDetail oprDetail = new InventorySamplingTaskReceiveOprDetail();
                    BeanUtil.copyProperties(samplingTaskDetail, oprDetail);
                    detailList.add(oprDetail);
                });
                result.put("detailList", detailList);
            }
        }
        return result;
    }
}
