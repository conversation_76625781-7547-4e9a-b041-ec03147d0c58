package cec.jiutian.bc.changeRequestManagement.domain.ecr.repository;

import cec.jiutian.bc.changeRequestManagement.domain.ecr.model.ChangeTrace;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ChangeTraceRepository extends JpaRepository<ChangeTrace, String> {


    List<ChangeTrace> findByInspectionResultOrderByCreateTimeDesc(String inspectionResult);

}
