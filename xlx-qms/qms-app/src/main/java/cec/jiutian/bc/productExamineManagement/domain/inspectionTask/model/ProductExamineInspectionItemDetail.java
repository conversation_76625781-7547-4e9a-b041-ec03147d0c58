package cec.jiutian.bc.productExamineManagement.domain.inspectionTask.model;

import cec.jiutian.bc.productExamineManagement.enumration.ItemUnQualifiedLevelEnum;
import cec.jiutian.bc.productReturnInspection.enumeration.InspectionResultEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.*;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/6/5
 * @description TODO
 */
@FabosJson(
        name = "产品审核任务检验项详情"
)
@Table(name = "qms_pem_product_examine_inspection_Item_detail")
@Entity
@Data
public class ProductExamineInspectionItemDetail extends MetaModel {

    @ManyToOne
    @FabosJsonField(
            views = {
                    @View(title = "检验任务", column = "generalCode", show = false)
            },
            edit = @Edit(title = "检验任务", type = EditType.REFERENCE_TABLE, show = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode")
            )
    )
    @JsonIgnoreProperties("inspectionTaskDetailList")
    private ProductExamineInspectionTask productExamineInspectionTask;

    @FabosJsonField(
            views = @View(title = "检验项目id",show = false),
            edit = @Edit(title = "检验项目id",show = false)
    )
    private String itemId;

    @FabosJsonField(
            views = @View(title = "检验项目编码"),
            edit = @Edit(title = "检验项目编码", search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    private String code;

    @FabosJsonField(
            views = @View(title = "检验项目名称"),
            edit = @Edit(title = "检验项目名称", search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    private String name;

    @FabosJsonField(
            views = @View(title = "特性"),
            edit = @Edit(title = "特性", search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    private String feature;

    @FabosJsonField(
            views = @View(title = "检验项类型"),
            edit = @Edit(title = "检验项类型")
    )
    private String itemType;

    @FabosJsonField(
            views = @View(title = "取样点"),
            edit = @Edit(title = "取样点",
                    inputType = @InputType(length = 40))
    )
    private String samplingPoint;

    @FabosJsonField(
            views = @View(title = "送检点"),
            edit = @Edit(title = "送检点",
                    inputType = @InputType(length = 40))
    )
    private String sendInspectPoint;

    @FabosJsonField(
            views = @View(title = "检验结果"),
            edit = @Edit(title = "检验结果",show = false, type = EditType.CHOICE,search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = InspectionResultEnum.class))
    )
    private String inspectionResult;

    @FabosJsonField(
            views = @View(title = "不合格类型"),
            edit = @Edit(title = "不合格类型",notNull = true,type = EditType.CHOICE,choiceType = @ChoiceType(fetchHandler = ItemUnQualifiedLevelEnum.class),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "inspectionResult == 'UNQUALIFIED'"))
    )
    private String unQualifiedLevel;

    @FabosJsonField(
            views = @View(title = "备注"),
            edit = @Edit(title = "备注",
                    type = EditType.TEXTAREA)
    )
    private String remark;

    @FabosJsonField(
            views = @View(title = "所属检验组",show = false),
            edit = @Edit(title = "所属检验组",show = false)
    )
    private String groupId;
}
