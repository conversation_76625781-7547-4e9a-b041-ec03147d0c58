package cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.handler;

import cec.jiutian.bc.basicData.enumeration.NamingRuleCodeEnum;
import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.model.AbnormalFeedbackHandling;
import cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.mto.AbnormalFeedbackHandlingCreateMTO;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/4/25
 * @description TODO
 */
@Component
public class AbnormalFeedbackHandlingDynamicHandler implements DependFiled.DynamicHandler<AbnormalFeedbackHandlingCreateMTO> {

    @Resource
    private NamingRuleService namingRuleService;

    @Override
    public Map<String, Object> handle(AbnormalFeedbackHandlingCreateMTO inspectionInstrument) {
        Map<String, Object> map = new HashMap<>();
        List<String> result = namingRuleService.getNameCode(NamingRuleCodeEnum.ABNORMAL_FEEDBACK_HANDLING.name(), 1, null);
        map.put("abnormalFeedbackHandlingFormNumber", result.get(0));
        return map;
    }
}
