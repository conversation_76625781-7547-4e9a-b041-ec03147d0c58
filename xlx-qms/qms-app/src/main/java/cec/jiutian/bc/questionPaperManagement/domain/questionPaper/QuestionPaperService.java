package cec.jiutian.bc.questionPaperManagement.domain.questionPaper;

import cec.jiutian.bc.basicData.enumeration.NamingRuleCodeEnum;
import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.modeler.vo.QuestionPaperVO;
import cec.jiutian.bc.questionPaperManagement.domain.questionPaper.model.QuestionPaper;
import cec.jiutian.bc.questionPaperManagement.domain.questionPaper.proxy.QuestionPaperDataProxy;
import cec.jiutian.common.util.BeanUtils;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
/**
 * <AUTHOR>
 * @description:
 */
@Service
public class QuestionPaperService {
    @Resource
    private FabosJsonDao fabosJsonDao;
    @Resource
    private NamingRuleService namingRuleService;
    @Resource
    private QuestionPaperDataProxy questionPaperDataProxy;

    public String createQuestionPaper(QuestionPaperVO questionPaperVO) {
        QuestionPaper  questionPaper = new QuestionPaper();
        BeanUtils.copyNotEmptyProperties(questionPaperVO,  questionPaper);
//        ProcessUserForInsTaskMTO processUserForInsTaskMTO = fabosJsonDao.findById(ProcessUserForInsTaskMTO.class, questionPaperVO.getResponsiblePersonId());
//        questionPaper.setResponsiblePersonMTO(processUserForInsTaskMTO);
//        OrgMTO orgMTO = fabosJsonDao.findById(OrgMTO.class, questionPaperVO.getResponsibleDepartmentId());
//        questionPaper.setResponsibleDepartmentMTO(orgMTO);
        questionPaper.setGeneralCode(namingRuleService.getNameCode(NamingRuleCodeEnum.QuestionPaper.name(), 1, null).get(0));
        questionPaperDataProxy.beforeAdd(questionPaper);
        fabosJsonDao.persistAndFlush(questionPaper);
        return questionPaper.getGeneralCode();
    }
}
