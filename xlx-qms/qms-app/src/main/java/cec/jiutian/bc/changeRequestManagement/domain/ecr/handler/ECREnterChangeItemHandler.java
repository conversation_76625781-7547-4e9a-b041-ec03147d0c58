package cec.jiutian.bc.changeRequestManagement.domain.ecr.handler;

import cec.jiutian.bc.changeRequestManagement.domain.ecr.model.ChangeRequest;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.model.ECRItem;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.mto.ChangeRequestAddItemMTO;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import jakarta.persistence.LockModeType;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Component
public class ECREnterChangeItemHandler implements OperationHandler<ChangeRequest, ChangeRequestAddItemMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Transactional
    @Override
    public String exec(List<ChangeRequest> data, ChangeRequestAddItemMTO modelObject, String[] param) {
        if (CollectionUtils.isEmpty(data) || data.get(0).getId() == null) {
            throw new ServiceException("数据异常");
        }
        ChangeRequest changeRequest = fabosJsonDao.getEntityManager().find(ChangeRequest.class, data.get(0).getId(), LockModeType.PESSIMISTIC_WRITE);
        List<ECRItem> ecrItems = modelObject.getECRItems();
        if (CollectionUtils.isEmpty(ecrItems)) {
            throw new ServiceException("请添加变更明细");
        }
        for (ECRItem ecrItem : ecrItems) {
            ecrItem.setEcr(changeRequest);
        }
        fabosJsonDao.mergeAndFlush(modelObject);
        return "提交成功";
    }

    @Override
    public ChangeRequestAddItemMTO fabosJsonFormValue(List<ChangeRequest> data, ChangeRequestAddItemMTO fabosJsonForm, String[] param) {
        if (CollectionUtils.isEmpty(data)) {
            throw new ServiceException("数据异常");
        }
        ChangeRequest changeRequest = data.get(0);
        BeanUtils.copyProperties(changeRequest, fabosJsonForm);
        fabosJsonForm.setECRItems(changeRequest.getECRItems());
        return fabosJsonForm;
    }
}
