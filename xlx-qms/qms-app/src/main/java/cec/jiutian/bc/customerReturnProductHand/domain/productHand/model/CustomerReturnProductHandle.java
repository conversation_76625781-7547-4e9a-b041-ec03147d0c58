package cec.jiutian.bc.customerReturnProductHand.domain.productHand.model;

import cec.jiutian.bc.basicData.enumeration.NamingRuleCodeEnum;
import cec.jiutian.bc.customerReturnProductHand.domain.productHand.proxy.CustomerReturnProductHandleProxy;
import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleBaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.DateType;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import java.util.Date;

/**
 * 客户退货产品处置单
 */

@Table(name = "customer_return_product_handle")
@Entity
@Getter
@Setter
@FabosJson(
        name = "客户产品退货处置单",
        orderBy = "createTime desc",
        dataProxy = CustomerReturnProductHandleProxy.class
)
public class CustomerReturnProductHandle  extends NamingRuleBaseModel {

    @Override
    public String getNamingCode() {
        return NamingRuleCodeEnum.CustomerReturnProductHandle.name();
    }

    @FabosJsonField(
            views = @View(title = "处置单号"),
            edit = @Edit(title = "处置单号", show = false))
    private String handleNumber;

    @FabosJsonField(
            views = @View(title = "顾客名称"),
            edit = @Edit(title = "顾客名称"))
    private String customerName;

    @FabosJsonField(
            views = @View(title = "退货日期"),
            edit = @Edit(title = "退货日期"))
    private Date returnDate;

    @FabosJsonField(
            views = @View(title = "产品编码"),
            edit = @Edit(title = "产品编码"))
    private String productCode;

    @FabosJsonField(
            views = @View(title = "产品名称"),
            edit = @Edit(title = "产品名称"))
    private String productName;

    @FabosJsonField(
            views = @View(title = "型号规格"),
            edit = @Edit(title = "型号规格"))
    private String modelSpecifications;

    @FabosJsonField(
            views = @View(title = "产品批次号"),
            edit = @Edit(title = "产品批次号"))
    private String productBatchNumber;

    @FabosJsonField(
            views = @View(title = "数量"),
            edit = @Edit(title = "数量"))
    private Double quantity;


    @FabosJsonField(
            views = @View(title = "重量"),
            edit = @Edit(title = "重量"))
    private Double weight;

    @FabosJsonField(
            views = @View(title = "顾客退货原因"),
            edit = @Edit(title = "顾客退货原因"))
    private String returnReason;

    @FabosJsonField(
            views = @View(title = "营销经办人"),
            edit = @Edit(title = "营销经办人"))
    private String marketingAgent;

    @FabosJsonField(
            views = @View(title = "成品退货检验单号"),
            edit = @Edit(title = "成品退货检验单号",show = false))
    private String inspectionNumber;


    @FabosJsonField(
            views = @View(title = "检验结果"),
            edit = @Edit(title = "检验结果",show = false))
    private String detectionResult;

    @FabosJsonField(
            views = @View(title = "原因分析"),
            edit = @Edit(title = "原因分析",show = false))
    private String causeAnalysis;

    @FabosJsonField(
            views = @View(title = "责任判定"),
            edit = @Edit(title = "责任判定",show = false))
    private String liabilityJudgment;

    @FabosJsonField(
            views = @View(title = "分析人"),
            edit = @Edit(title = "分析人",show = false))
    private String analysisPerson;

    @FabosJsonField(
            views = @View(title = "分析时间"),
            edit = @Edit(title = "分析时间",show = false))
    private Date analysisTime;

    @FabosJsonField(
            views = @View(title = "分析附件"),
            edit = @Edit(title = "分析附件",show = false))
    private String analysisAnnex;


    @FabosJsonField(
            views = @View(title = "创建时间"),
            edit = @Edit(title = "创建时间",
                    show = false,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @CreationTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date reportTime;
}
