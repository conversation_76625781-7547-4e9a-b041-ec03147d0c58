package cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.issueList.model;

import cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.issueList.handler.*;
import cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.issueList.proxy.IssueListDataProxy;
import cec.jiutian.bc.qualityExceptionManagement.enumeration.inspectionIssuesImproveAndTrace.CorrectiveActionEnum;
import cec.jiutian.bc.qualityExceptionManagement.enumeration.inspectionIssuesImproveAndTrace.InsResultEnum;
import cec.jiutian.bc.qualityExceptionManagement.enumeration.inspectionIssuesImproveAndTrace.IssueListStatusEnum;
import cec.jiutian.bc.qualityExceptionManagement.enumeration.inspectionIssuesImproveAndTrace.SeverityLevelEnum;
import cec.jiutian.bc.urm.domain.org.entity.Org;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.frame.module.MetaUser;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.*;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@FabosJson(
        name = "问题清单",
        orderBy = "IssueList.createTime desc",
        power = @Power(add = false,delete = false,edit = false,export = false),
        dataProxy = IssueListDataProxy.class,
        rowOperation = {
                @RowOperation(
                        title = "创建",
                        code = "IssueList@CUSTOM_ADD",
                        mode = RowOperation.Mode.BUTTON,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.ADD,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = IssueListADD.class,

                        //该配置需要校验 readonly配置是按照新增或者修改 来校验
                        readonly = @RowOperationReadonly(readOnlyAffectMode = RowOperationReadonly.AffectMode.add),
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "IssueList@CUSTOM_ADD"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
                @RowOperation(
                        title = "编辑",
                        code = "IssueList@CUSTOM_EDIT",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.UPDATE,
                        operationHandler = EditIssueListOprHandler.class,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = IssueListEdit.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "IssueList@CUSTOM_EDIT"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "selectedItems[0].status!='WAIT_PUBLISH'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "问题发布",
                        code = "IssueList@PUBLISH",
                        mode = RowOperation.Mode.HEADER,
                        operationHandler = PublicIssueListOprHandler.class,
                        callHint = "请确认是否发布？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "IssueList@PUBLISH"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "selectedItems[0].status != 'WAIT_PUBLISH'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "问题分析",
                        code = "IssueList@ANA",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        operationHandler = IssueListANAHandler.class,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = IssueListANA.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "IssueList@ANA"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "selectedItems[0].rowOperationAuthFlag != 1 || selectedItems[0].status != 'WAIT_RUN'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "分析提交",
                        code = "IssueList@SUBMIT",
                        mode = RowOperation.Mode.HEADER,
                        operationHandler = SubmitIssueListOprHandler.class,
                        callHint = "请确认是否提交分析？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "IssueList@SUBMIT"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "selectedItems[0].rowOperationAuthFlag != 1 || selectedItems[0].status != 'WAIT_RUN'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "验证",
                        code = "IssueList@VER",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        operationHandler = IssueListVerifyHandler.class,
                        fabosJsonClass = IssueListVerify.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "IssueList@VER"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "selectedItems[0].status != 'VERIFIED'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "验证提交",
                        code = "IssueList@VER_SUBMIT",
                        mode = RowOperation.Mode.HEADER,
                        operationHandler = VerSubmitIssueListOprHandler.class,
                        callHint = "请确认是否提交？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "IssueList@VER_SUBMIT"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "selectedItems[0].status != 'VERIFIED'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                )
        }
)
@Table(name = "qms_qem_issue_list")
@Entity
@Getter
@Setter
public class IssueList extends MetaModel {

    @FabosJsonField(
            views = @View(title = "问题清单编号"),
            edit = @Edit(title = "问题清单编号", search = @Search(vague = true))
    )
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "任务编号"),
            edit = @Edit(title = "任务编号", search = @Search(vague = true),readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "OnSiteInspectionTaskDTO", beFilledBy = "generalCode"))
    )
    private String taskGeneralCode;

    @FabosJsonField(
            views = @View(title = "任务名称"),
            edit = @Edit(title = "任务名称", search = @Search(vague = true), readonly = @Readonly)
    )
    private String name;

    @FabosJsonField(
            views = @View(title = "任务描述"),
            edit = @Edit(title = "任务描述", readonly = @Readonly)
    )
    private String description;

    @FabosJsonField(
            views = @View(title = "巡检标准"),
            edit = @Edit(title = "巡检标准", readonly = @Readonly)
    )
    private String inspectionStandard;

    @FabosJsonField(
            views = @View(title = "巡检项目"),
            edit = @Edit(title = "巡检项目", readonly = @Readonly)
    )
    private String inspectionProject;

    @FabosJsonField(
            views = @View(title = "巡检内容", toolTip = true),
            edit = @Edit(title = "巡检内容", search = @Search(vague = true), readonly = @Readonly)
    )
    private String content;

    @FabosJsonField(
            views = @View(title = "巡检结果", toolTip = true),
            edit = @Edit(title = "巡检结果",readonly = @Readonly,type= EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler =  InsResultEnum.class))
    )
    private String insResult;

    @FabosJsonField(
            views = @View(title = "问题描述"),
            edit = @Edit(title = "问题描述")
    )
    private String issueDescription;

    //严重程度  轻微 一般 严重
    @FabosJsonField(
            views = @View(title = "严重程度"),
            edit = @Edit(title = "严重程度",type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = SeverityLevelEnum.class))
    )
    private String severityLevel;

    // todo  待发布 待执行 执行中 待验证 已完成
    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态", type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = IssueListStatusEnum.class))
    )
    private String status;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "责任部门", column = "name"),
            edit = @Edit(title = "责任部门",notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"))
    )
    @JoinColumn(foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private Org org;
    @ManyToOne
    @FabosJsonField(
            views = @View(title = "责任人", column = "name"),
            edit = @Edit(title = "责任人",notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"))
    )
    @JoinColumn(foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private MetaUser metaUser;

    // todo 关联单据后 回显
    @FabosJsonField(
            views = @View(title = "分析对策"),
            edit = @Edit(title = "分析对策",type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = CorrectiveActionEnum.class))
    )
    private String correctiveAction;

    // todo
    @FabosJsonField(
            views = @View(title = "关联单据号"),
            edit = @Edit(title = "关联单据号")
    )
    private String relatedDocumentNumber;

    @FabosJsonField(
            views = @View(title = "措施纳期"),
            edit = @Edit(title = "措施纳期", dateType = @DateType(type = DateType.Type.DATE_TIME))
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date measuresDeadline;

    @FabosJsonField(
            views = @View(title = "问题重复次数"),
            edit = @Edit(title = "问题重复次数", numberType = @NumberType(min = 0), notNull = true)
    )
    private Integer issueRepeatedTimes;

    @FabosJsonField(
            views = @View(title = "问题附件"),
            edit = @Edit(title = "问题附件")
    )
    private String attachment;

    @FabosJsonField(
            views = @View(title = "原因分析", toolTip = true),
            edit = @Edit(title = "原因分析",type = EditType.TEXTAREA)
    )
    private String causeAnalysis;

    // 子表
    @FabosJsonField(
            edit = @Edit(title = "整改措施", type = EditType.TAB_TABLE_ADD),
            views = @View(title = "整改措施", column = "correction", type= ViewType.TABLE_VIEW)
    )
    @JoinColumn(name = "issue_list_id")
    @OneToMany(cascade = CascadeType.ALL)
    private List<IssueListCorrection> issueListCorrections;

    @FabosJsonField(
            views = @View(title = "整改任务下所有责任人的id集合,我的整改任务查询数据使用", show = false),
            edit = @Edit(title = "整改任务下所有责任人的id集合,我的整改任务查询数据使用",show = false)
    )
    private String allUserIds;

}


