package cec.jiutian.bc.changeRequestManagement.domain.ecr.mto;

import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.FabosJsonI18n;
import cec.jiutian.view.SubTableField;
import cec.jiutian.view.constant.AnnotationConst;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.BoolType;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "fd_role")
@Getter
@Setter
@FabosJson(name = "角色管理",
        filter = @Filter("status = true"),
        orderBy = "sort asc, createTime desc"
)
@FabosJsonI18n
public class RoleMTO extends MetaModel {


    @Column(length = AnnotationConst.CODE_LENGTH)
    @FabosJsonField(
            views = @View(title = "编码"),
            edit = @Edit(title = "编码", notNull = true, search = @Search(vague = true))
    )
    @SubTableField
    private String code;

    @FabosJsonField(
            views = @View(title = "名称", toolTip = true),
            edit = @Edit(title = "名称", notNull = true, search = @Search(vague = true))
    )
    @SubTableField
    private String name;

    @FabosJsonField(
            views = @View(title = "角色说明", toolTip = true),
            edit = @Edit(title = "角色说明")
    )
    @SubTableField
    private String description;

    @FabosJsonField(
            views = @View(title = "显示顺序", sortable = true),
            edit = @Edit(title = "显示顺序", notNull = true, numberType = @NumberType(min = 1, max = 9999))
    )
    private Integer sort;

    @FabosJsonField(
            views = @View(title = "状态", sortable = true),
            edit = @Edit(
                    title = "状态",
                    type = EditType.BOOLEAN,
                    notNull = true,
                    defaultVal = "true",
                    search = @Search(vague = true, defaultVal = "true"),
                    boolType = @BoolType(trueText = "有效", falseText = "失效")
            )
    )
    private Boolean status = true;

}