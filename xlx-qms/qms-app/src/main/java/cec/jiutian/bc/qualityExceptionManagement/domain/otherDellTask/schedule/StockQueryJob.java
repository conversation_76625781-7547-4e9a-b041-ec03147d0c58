package cec.jiutian.bc.qualityExceptionManagement.domain.otherDellTask.schedule;

import cec.jiutian.bc.job.provider.IJobProvider;
import cec.jiutian.bc.qualityExceptionManagement.domain.otherDellTask.dto.TaskDetailDTO;
import cec.jiutian.bc.qualityExceptionManagement.domain.otherDellTask.model.OtherDellTask;
import cec.jiutian.bc.qualityExceptionManagement.domain.otherDellTask.model.TaskDetail;
import cec.jiutian.bc.qualityExceptionManagement.domain.otherDellTask.repository.OtherTaskRepository;
import cec.jiutian.bc.qualityExceptionManagement.domain.otherDellTask.repository.TaskDetailRepository;
import cec.jiutian.bc.qualityExceptionManagement.enums.TaskDetailTypeEnum;
import cec.jiutian.core.frame.annotation.FabosCustomizedService;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.meta.FabosJob;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Slf4j
@FabosCustomizedService(value = TaskDetail.class)
@Component
public class StockQueryJob implements IJobProvider {

    @Resource
    private TaskDetailRepository taskDetailRepository;

    @Resource
    private OtherTaskRepository otherTaskRepository;

    @Resource
    private FabosJsonDao fabosJsonDao;

    @FabosJob(comment = "其他处置出入口明细查询")
    @Override
    @Transactional
    public String exec(String code, String param) {
        //获取当前时间和30天前时间字符串
        long endTime = System.currentTimeMillis();
        // 30天前的时间戳
        long startTime = endTime - 30L * 24 * 60 * 60 * 1000;
        LocalDateTime start = LocalDateTime.ofInstant(Instant.ofEpochMilli(startTime), ZoneId.systemDefault());
        LocalDateTime end = LocalDateTime.ofInstant(Instant.ofEpochMilli(endTime), ZoneId.systemDefault());

        List<OtherDellTask> otherDellTasks = otherTaskRepository.findByCreationTimeBetween(start, end);
        if (CollectionUtils.isEmpty(otherDellTasks)) {
            log.warn("没有查询到数据");
            return "";
        }
        for (OtherDellTask otherDellTask : otherDellTasks) {
            try {
                String batchCode = otherDellTask.getBatchCode();
                if (StringUtils.isEmpty(batchCode)) {
                    continue;
                }
                safeSaveDetails(otherDellTask, batchCode, TaskDetailTypeEnum.Enum.IN_STOCK.name(), () ->
                        taskDetailRepository.queryMesStockInData(batchCode, startTime, endTime)
                );
                safeSaveDetails(otherDellTask, batchCode, TaskDetailTypeEnum.Enum.OUT_STOCK.name(), () ->
                        taskDetailRepository.queryMesStockOutData(batchCode, startTime, endTime)
                );
                safeSaveDetails(otherDellTask, batchCode, TaskDetailTypeEnum.Enum.IN_STOCK.name(), () ->
                        taskDetailRepository.queryWmsStockInData(batchCode, startTime, endTime)
                );
                safeSaveDetails(otherDellTask, batchCode, TaskDetailTypeEnum.Enum.OUT_STOCK.name(), () ->
                        taskDetailRepository.queryWmsStockOutData(batchCode, startTime, endTime)
                );
            } catch (Exception e) {
                log.error("处理批次[{}]时发生异常: {}", otherDellTask.getBatchCode(), e.getMessage(), e);
            }
        }

        return "执行成功";
    }

    private void safeSaveDetails(OtherDellTask otherDellTask, String batchCode, String type, Supplier<List<Map<String, Object>>> querySupplier) {
        try {
            List<Map<String, Object>> details = querySupplier.get();
            saveDetail(otherDellTask, details, type);
        } catch (Exception e) {
            log.error("保存批次[{}]的{}明细失败: {}", batchCode, type, e.getMessage(), e);
        }
    }

    public void saveDetail(OtherDellTask otherDellTask, List<Map<String, Object>> detailDTOS, String type) {
        if (CollectionUtils.isEmpty(detailDTOS)) {
            return;
        }
        List<TaskDetail> details = new ArrayList<>();
        Set<String> codes = new HashSet<>();
        if (!otherDellTask.getTaskDetails().isEmpty()) {
            details.addAll(otherDellTask.getTaskDetails());
            codes.addAll(otherDellTask.getTaskDetails().stream().map(TaskDetail::getCode).collect(Collectors.toSet()));
        }

        for (Map<String, Object> map : detailDTOS) {
            TaskDetail detail = new TaskDetail();
            detail.setType(type);
            detail.setWeight(0D);
            detail.setCode(Objects.nonNull(map.get("code")) ? map.get("code").toString() : "");
            //如果包含 则不新增
            if (codes.contains(detail.getCode())) {
                continue;
            }
            Object quantity = map.get("quantity");
            if (quantity instanceof BigDecimal) {
                detail.setQuantity((BigDecimal) quantity);
            } else if (quantity instanceof Number) {
                detail.setQuantity(BigDecimal.valueOf(((Number) quantity).doubleValue()));
            }

            Object createTime = map.get("create_time");
            if (createTime instanceof java.sql.Timestamp) {
                detail.setCreateTime(((java.sql.Timestamp) createTime).toLocalDateTime());
            } else if (createTime instanceof java.util.Date) {
                detail.setCreateTime(LocalDateTime.ofInstant(((Date)createTime).toInstant(), ZoneId.systemDefault()));
            }
            details.add(detail);
        }
        otherDellTask.setTaskDetails(details);
        fabosJsonDao.mergeAndFlush(otherDellTask);
    }

    private String getCurrentTime(Long currentTime) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            return sdf.format(currentTime);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    //获取N天前当前时间
    private String getNTime(Long currentTime, int n) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            return sdf.format(System.currentTimeMillis() - n * 24 * 60 * 60 * 1000);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

}
