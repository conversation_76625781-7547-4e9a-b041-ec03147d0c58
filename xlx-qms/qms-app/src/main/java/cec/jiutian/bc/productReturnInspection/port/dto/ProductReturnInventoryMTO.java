package cec.jiutian.bc.productReturnInspection.port.dto;

import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.Search;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025/3/10
 * @description TODO
 */
@FabosJson(
        name = "库存台账MTO"
)
@Entity
@Getter
@Setter
@Table(name = "bwi_inventory")
public class ProductReturnInventoryMTO extends MetaModel {

    @FabosJsonField(
            views = @View(title = "库存批次"),
            edit = @Edit(title = "库存批次", notNull = true,
                    search = @Search(vague = true),
                    inputType = @InputType(length = 20))
    )
    private String inventoryLotId;

    @FabosJsonField(
            views = @View(title = "本厂批号"),
            edit = @Edit(title = "本厂批号")
    )
    private String lotSerialId;

    @FabosJsonField(
            views = @View(title = "原炉/原厂批号"),
            edit = @Edit(title = "原炉/原厂批号")
    )
    private String supplierLotSerialId;

    @FabosJsonField(
            views = @View(title = "物料编号"),
            edit = @Edit(title = "物料编号")
    )
    private String materialCode;

    @FabosJsonField(
            views = @View(title = "物料名称"),
            edit = @Edit(title = "物料名称")
    )
    private String materialName;

    @FabosJsonField(
            views = @View(title = "规格"),
            edit = @Edit(title = "规格")
    )
    private String materialSpecification;

    @FabosJsonField(
            views = @View(title = "单位"),
            edit = @Edit(title = "单位")
    )
    private String accountingUnit;

    @FabosJsonField(
            views = @View(title = "批次总数量"),
            edit = @Edit(title = "批次总数量")
    )
    private Double accountingUnitQuantity;

    @FabosJsonField(
            views = @View(title = "可用数量"),
            edit = @Edit(title = "可用数量")
    )
    private Double availableQuantity;

    @FabosJsonField(
            views = @View(title = "当前状态"),
            edit = @Edit(title = "当前状态")
    )
    private String currentState;
}
