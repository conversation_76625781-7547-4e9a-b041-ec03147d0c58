package cec.jiutian.bc.specialMaterialHandle.domain.handler;

import cec.jiutian.bc.specialMaterialHandle.domain.model.SpecialMaterialHandleRequest;
import cec.jiutian.bc.specialMaterialHandle.domain.model.SpecialMaterialHandleRequestDetail;
import cec.jiutian.bc.specialMaterialHandle.mto.SpecialMaterialSourceMTO;
import cec.jiutian.view.ReferenceAddType;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/5/22
 * @description TODO
 */
@Component
public class MaterialDetailReferenceAddHandler implements ReferenceAddType.ReferenceAddHandler<SpecialMaterialHandleRequest, SpecialMaterialSourceMTO> {
    @Override
    public Map<String, Object> handle(SpecialMaterialHandleRequest specialMaterialHandleRequest, List<SpecialMaterialSourceMTO> specialMaterialSourceMTOS) {
        Map<String, Object> result = new HashMap<>();
        List<SpecialMaterialHandleRequestDetail> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(specialMaterialSourceMTOS)) {
            specialMaterialSourceMTOS.forEach(material -> {
                SpecialMaterialHandleRequestDetail detail = new SpecialMaterialHandleRequestDetail();
                detail.setInventoryId(material.getId());
                detail.setOrderCode(material.getOrderCode());
                detail.setLotSerialId(material.getLotSerialId());
                detail.setMaterialCode(material.getMaterialCode());
                detail.setMaterialName(material.getMaterialName());
                detail.setWeight(material.getInventoryQuantity());
                detail.setUnit(material.getUnit());
                detail.setMaterialLevel(material.getMaterialLevel());
                list.add(detail);
            });
        }
        result.put("detailList",list);
        return result;
    }
}
