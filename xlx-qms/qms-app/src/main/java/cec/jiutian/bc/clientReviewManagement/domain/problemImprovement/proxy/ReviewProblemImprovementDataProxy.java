package cec.jiutian.bc.clientReviewManagement.domain.problemImprovement.proxy;

import cec.jiutian.bc.clientComplaintManagement.enumeration.ReviewProblemImproveStatusEnum;
import cec.jiutian.bc.clientReviewManagement.domain.problemImprovement.model.ReviewProblemImprovement;
import cec.jiutian.view.fun.DataProxy;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/06/11
 * @description
 */
@Component
public class ReviewProblemImprovementDataProxy implements DataProxy<ReviewProblemImprovement> {
    @Override
    public void beforeAdd(ReviewProblemImprovement entity) {
        entity.setBusinessStatus(ReviewProblemImproveStatusEnum.Enum.DISTRIBUTING.name());
    }
}
