package cec.jiutian.bc.inventoryInspection.statistics.dto;

import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
public class QualityStatsDTO {
    private String timePeriod;      // 时间段
    private String processName;     // 工序名称
    private Double outputQuantity;    // 生产数量
    private Double defectiveQuantity; // 不良数量
    private Double passRate;        // 合格率
    private Double targetRate;      // 目标合格率

    public static QualityStatsDTO mapToDto(Object[] row) {
        return QualityStatsDTO.builder()
                .timePeriod(String.valueOf(row[0]))
                .outputQuantity(convertToDouble(row[1]))
                .defectiveQuantity(convertToDouble(row[2]))
                .passRate(convertToDouble(row[3]))
                .targetRate(0.5)
                .build();
    }

    private static Double convertToDouble(Object value) {
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        return 0D;
    }
}