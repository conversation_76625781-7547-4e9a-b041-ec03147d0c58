package cec.jiutian.bc.inventoryInspection.enumeration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.Getter;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Getter
public enum InsTaskStatusEnum {
    //  已完成    检验中    待检中    待发布
    COMPLETE("已完成"),
    CHECKING("检验中"),
    WAITING_CHECK("待检中"),
    WAITING_PUBLISH("待发布")
    ;
    private String value;

    InsTaskStatusEnum(String value) {
        this.value = value;
    }

    public static class ChoiceFetch implements ChoiceFetchHandler {
        @Override
        public List<VLModel> fetch(String[] params) {
            return Stream.of(InsTaskStatusEnum.values()).map(insTaskStatusEnum ->
                    new VLModel(insTaskStatusEnum.name(), insTaskStatusEnum.getValue())).collect(Collectors.toList());
        }

    }
}
