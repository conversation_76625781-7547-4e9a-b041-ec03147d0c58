package cec.jiutian.bc.clientReviewManagement.domain.problemImprovement.mto;

import cec.jiutian.bc.clientComplaintManagement.enumeration.ReviewProblemImproveStatusEnum;
import cec.jiutian.bc.urm.domain.user.entity.User;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.*;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025/06/11
 * @description
 */
@FabosJson(
        name = "原因分析"

)
@Table(name = "qms_client_review_problem_improvement")
@Entity
@Getter
@Setter
public class MyReviewProblemImprovementAnalyMTO extends MetaModel {
    @FabosJsonField(
            views = @View(title = "原因"),
            edit = @Edit(title = "原因", inputType = @InputType(length = 200))
    )
    private String reason;

    @FabosJsonField(
            views = @View(title = "整改措施"),
            edit = @Edit(title = "整改措施")
    )
    private String correction;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "验证人", column = "name"),
            edit = @Edit(title = "验证人",
                    type = EditType.REFERENCE_TABLE,
                    search = @Search(),
                    show = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name")
            )
    )
    private User verifiedUser;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",
                    show = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ReviewProblemImproveStatusEnum.class)
            )
    )
    private String businessStatus;
}
