package cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.issueList.model;

import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.ReferenceGenerateType;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.type.Power;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Entity
@Table(name = "qms_qem_issue_list")
@Getter
@Setter
@FabosJson(name = "问题清单-验证",power = @Power(add = false,delete = false,edit = false),
        // dataProxy = IssueListVerifyProxy.class,
        orderBy = "IssueListVerify.createTime desc"
)
public class IssueListVerify extends BaseModel {
    @FabosJsonField(
            views = @View(title = "整改措施", type = ViewType.TABLE_VIEW),
            edit = @Edit(title = "整改措施", type = EditType.TAB_REFERENCE_GENERATE),
            referenceGenerateType = @ReferenceGenerateType(editable = {"verifyMetaUser","verificationResult","verificationDate","verificationEvidence"})
    )
    @JoinColumn(name = "issue_list_id")
    @OneToMany(cascade = CascadeType.ALL)
    private List<IssueListCorrection> issueListCorrections;
}
