package cec.jiutian.bc.changeRequestManagement.domain.ecr.mto;

import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.FabosJsonI18n;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;


@Entity
@Table(name = "batch_serial")
@Getter
@Setter
@FabosJsonI18n
@FabosJson(
        name = "工序流水",
        orderBy = "serialNumber desc",
        power = @Power(add = false, edit = false, delete = false, print = false, importable = false)
)
public class ProcessMTO {
    @FabosJsonField(
            views = @View(title = "id", show = false),
            edit = @Edit(title = "id")
    )
    @Id
    @Column(name = "id")
    private String id;

    @FabosJsonField(
            views = @View(title = "工序流水号"),
            edit = @Edit(title = "工序流水号",
                    notNull = true
            )
    )
    @Column(nullable = false, length = 256)
    private String serialNumber;

    @FabosJsonField(
            views = @View(title = "工序编码"),
            edit = @Edit(title = "工序编码",
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    private String processCode;

    @FabosJsonField(
            views = @View(title = "工序名称"),
            edit = @Edit(title = "工序名称",
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column( length = 256)
    private String processName;

    @FabosJsonField(
            views = @View(title = "重量"),
            edit = @Edit(title = "重量",
                    search = @Search(vague = true)
            )
    )
    @Column(name = "byproduct_weight", precision = 10, scale = 2)
    private BigDecimal byproductWeight;

    @FabosJsonField(
            views = @View(title = "完成时间"),
            edit = @Edit(title = "完成时间",
                    search = @Search(vague = true)
            )
    )
    private Date outputTime;

    @FabosJsonField(
            views = @View(title = "车间id"),
            edit = @Edit(title = "车间id"
            )
    )
    @Column(name = "workshop_id")
    private Long workshopId;

    @FabosJsonField(
            views = @View(title = "车间名称"),
            edit = @Edit(title = "车间名称"
            )
    )
    @Column(name = "workshop_name", length = 256)
    private String workshopName;

    @FabosJsonField(
            views = @View(title = "产线id"),
            edit = @Edit(title = "产线id"
            )
    )
    @Column(name = "production_line_id")
    private Long productionLineId;

    @FabosJsonField(
            views = @View(title = "产线名称"),
            edit = @Edit(title = "产线名称"
            )
    )
    @Column(name = "production_line_name", length = 256)
    private String productionLineName;
}
