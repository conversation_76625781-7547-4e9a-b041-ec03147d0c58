package cec.jiutian.bc.accidentReportManagement.enumeration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/29
 * @description
 */
public class ArmMeasureStatusEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum value : Enum.values()) {
            list.add(new VLModel(value.name(), value.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        NOT_FILLED_IN("未填写"),
        ALREADY_FILLED_IN("已填写"),
        SUBMITTED("已提交"),
        TO_BE_VERIFIED("待验证"),
        VERIFIED("已验证"),
        CLOSED("已关闭"),
        ;

        private final String value;

    }

}
