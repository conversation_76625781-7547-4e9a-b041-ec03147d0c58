package cec.jiutian.bc.specialMaterialHandle.domain.proxy;

import cec.jiutian.bc.flow.enums.ExamineStatusEnum;
import cec.jiutian.bc.specialMaterialHandle.domain.model.SpecialMaterialHandleRequest;
import cec.jiutian.bc.specialMaterialHandle.domain.model.SpecialMaterialHandleRequestDetail;
import cec.jiutian.bc.specialMaterialHandle.enumeration.SpecialMaterialBusinessStatusEnum;
import cec.jiutian.bc.specialMaterialHandle.port.client.SpecialMaterialFeignClient;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.view.fun.FlowProxy;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/5/23
 * @description TODO
 */
@Component
public class SpecialMaterialHandleRequestFlowProxy extends FlowProxy {

    @Resource
    private SpecialMaterialFeignClient specialMaterialFeignClient;

    @Override
    @Transactional
    public void onEvent(Object event, Object data) {
        if (data instanceof SpecialMaterialHandleRequest entity) {
            if (entity.getExamineStatus().equals(ExamineStatusEnum.AUDITED.getCode())) {
                entity.setBusinessState(SpecialMaterialBusinessStatusEnum.Enum.WAIT_VERIFY.name());
                Map<String, Object> map = new HashMap<>();
                List<String> sampleReturnNos = entity.getDetailList().stream().map(SpecialMaterialHandleRequestDetail::getOrderCode).toList();
                map.put("sampleReturnNos",sampleReturnNos);
                specialMaterialFeignClient.specialMaterialHandle(map);
            }
        }
    }

    @Override
    public void beforeSubmit(Object entity) {
        if (entity instanceof SpecialMaterialHandleRequest data) {
            data.setBusinessState(SpecialMaterialBusinessStatusEnum.Enum.WAIT_VERIFY.name());
            if (CollectionUtils.isEmpty(data.getDetailList())) {
                throw new FabosJsonApiErrorTip("请先选择特殊物料处理批次信息");
            }
        }
    }
}
