package cec.jiutian.bc.productExamineManagement.domain.inspectionTask.handler;

import cec.jiutian.bc.basicData.domain.inspectionItemGroup.model.InspectionItemGroup;
import cec.jiutian.bc.basicData.domain.inspectionStandard.model.InspectionStandard;
import cec.jiutian.bc.basicData.enumeration.ApplyRangeEnum;
import cec.jiutian.bc.basicData.enumeration.NamingRuleCodeEnum;
import cec.jiutian.bc.generalModeler.enumeration.YesOrNoEnum;
import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.generalModeler.util.StringUtils;
import cec.jiutian.bc.modeler.domain.inspectionItem.model.InspectionItem;
import cec.jiutian.bc.modeler.enumration.TaskBusinessStateEnum;
import cec.jiutian.bc.productExamineManagement.domain.inspectionTask.model.ProductExamineInspectionItemDetail;
import cec.jiutian.bc.productExamineManagement.domain.inspectionTask.model.ProductExamineInspectionTask;
import cec.jiutian.bc.productExamineManagement.domain.sampleTask.model.ProductExamineSamplingTask;
import cec.jiutian.bc.productExamineManagement.domain.sampleTask.model.ProductExamineSamplingTaskDetail;
import cec.jiutian.bc.productReturnInspection.port.client.ProductInspectFeignClient;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/10
 * @description TODO
 */
@Component
public class ProductExaminePublishOperationHandler implements OperationHandler<ProductExamineInspectionTask, Void> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private NamingRuleService namingRuleService;

    @Resource
    private ProductInspectFeignClient productInspectFeignClient;

    @Override
    @Transactional
    public String exec(List<ProductExamineInspectionTask> data, Void modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            ProductExamineInspectionTask productExamineInspectionTask = data.get(0);
            productExamineInspectionTask.setCurrentState(OrderCurrentStateEnum.Enum.EXECUTE.name());
            fabosJsonDao.mergeAndFlush(productExamineInspectionTask);

            Map<String, Object> outboundRequest = createOutboundRequest(productExamineInspectionTask);
            productInspectFeignClient.createOutboundRequest(outboundRequest);

            fabosJsonDao.mergeAndFlush(productExamineInspectionTask);

            // 根据检验任务创建取样任务，一个检验组对应一个取样任务
            if (CollectionUtils.isNotEmpty(productExamineInspectionTask.getStandardDetailList())) {
                List<ProductExamineInspectionItemDetail> groupList = productExamineInspectionTask.getStandardDetailList().stream().filter(q -> StringUtils.isNotEmpty(q.getGroupId())).toList();
                if (CollectionUtils.isNotEmpty(groupList)) {
                    Map<String, List<ProductExamineInspectionItemDetail>> map = groupList.stream()
                            .collect(Collectors.groupingBy(ProductExamineInspectionItemDetail::getGroupId));
                    for (String key : map.keySet()) {
                        if (productExamineInspectionTask.getSampleQuantity() != null && productExamineInspectionTask.getSampleQuantity() > 0D) {
                            createSamplingTask(false, key, map.get(key), productExamineInspectionTask);
                        }
                    }
                }

                List<ProductExamineInspectionItemDetail> nullGroupList = productExamineInspectionTask.getStandardDetailList().stream().filter(q -> StringUtils.isEmpty(q.getGroupId())).toList();
                if (CollectionUtils.isNotEmpty(nullGroupList)) {
                    nullGroupList.forEach(detail -> {
                        List<ProductExamineInspectionItemDetail> list = new ArrayList<>();
                        list.add(detail);
                        if (productExamineInspectionTask.getSampleQuantity() != null && productExamineInspectionTask.getSampleQuantity() > 0D) {
                            createSamplingTask(false, null, list, productExamineInspectionTask);
                        }
                    });
                }

                // 根据质检标准判断是否创建留样类型取样单
                InspectionStandard inspectionStandard = fabosJsonDao.getById(InspectionStandard.class, productExamineInspectionTask.getInspectionStandard().getId());
                if (YesOrNoEnum.Enum.Y.name().equals(inspectionStandard.getKeepSampleFlag())) {
                    createSamplingTask(true, null, null, productExamineInspectionTask);
                }
            }
            fabosJsonDao.mergeAndFlush(productExamineInspectionTask);
        }
        return "alert(操作成功)";
    }

    private void createSamplingTask(Boolean isKeepSample, String groupId, List<ProductExamineInspectionItemDetail> list, ProductExamineInspectionTask productExamineInspectionTask) {
        ProductExamineSamplingTask productExamineSamplingTask = new ProductExamineSamplingTask();
        productExamineSamplingTask.setIsSaveSample(isKeepSample);
        if (StringUtils.isNotEmpty(groupId)) {
            InspectionItemGroup inspectionItemGroup = fabosJsonDao.getById(InspectionItemGroup.class, groupId);
            productExamineSamplingTask.setInspectionItemGroupName(inspectionItemGroup.getGroupName());
            productExamineSamplingTask.setSamplePoint(inspectionItemGroup.getSamplePoint());
            productExamineSamplingTask.setSendPoint(inspectionItemGroup.getSendPointMTO().getName());
            productExamineSamplingTask.setUnit(inspectionItemGroup.getUsualUnit());
            productExamineSamplingTask.setPackageType(inspectionItemGroup.getPackageType());
        }

        productExamineSamplingTask.setSamplePlanDate(new Date());
        productExamineSamplingTask.setGeneralCode(String.valueOf(namingRuleService.getNameCode(NamingRuleCodeEnum.SamplingTask.name(), 1, null).get(0)));
        productExamineSamplingTask.setInspectionTaskCode(productExamineInspectionTask.getGeneralCode());
        productExamineSamplingTask.setIsUrgent(false);
        productExamineSamplingTask.setInspectionType(ApplyRangeEnum.Enum.ProductExamineInspect.name());
        productExamineSamplingTask.setBusinessState(TaskBusinessStateEnum.Enum.BE_SAMPLING.name());
        productExamineSamplingTask.setMaterialCode(productExamineInspectionTask.getSpecificationManageMTO().getCode());
        productExamineSamplingTask.setMaterialName(productExamineInspectionTask.getSpecificationManageMTO().getName());
        productExamineSamplingTask.setOriginLotId(productExamineInspectionTask.getInventoryMTO().getInventoryLotId());
        productExamineSamplingTask.setMaterialSpecification(productExamineInspectionTask.getSpecificationManageMTO().getType());
        productExamineSamplingTask.setAllInspectionItemQuantity(productExamineInspectionTask.getSampleQuantity());

        if (CollectionUtils.isNotEmpty(list)) {
            List<ProductExamineSamplingTaskDetail> detailList = new ArrayList<>();
            list.forEach(standardDetail -> {
                InspectionItem inspectionItem = fabosJsonDao.getById(InspectionItem.class,standardDetail.getItemId());
                ProductExamineSamplingTaskDetail productExamineSamplingTaskDetail = new ProductExamineSamplingTaskDetail();
                productExamineSamplingTaskDetail.setProductExamineSamplingTask(productExamineSamplingTask);
                productExamineSamplingTaskDetail.setName(standardDetail.getName());
                productExamineSamplingTaskDetail.setSamplingPlan(inspectionItem.getSamplingPlan());
                productExamineSamplingTaskDetail.setInspectionMethod(inspectionItem.getInspectionMethod());
                productExamineSamplingTaskDetail.setItemId(standardDetail.getItemId());
                productExamineSamplingTaskDetail.setCode(standardDetail.getCode());
                detailList.add(productExamineSamplingTaskDetail);
            });
            productExamineSamplingTask.setDetailList(detailList);
        }
        fabosJsonDao.mergeAndFlush(productExamineSamplingTask);
    }

    private Map<String, Object> createOutboundRequest(ProductExamineInspectionTask productExamineInspectionTask) {
        Map<String, Object> result = new HashMap<>();
        UserContext.CurrentUser currentUser = UserContext.get();
        if (productExamineInspectionTask != null && currentUser != null) {
            result.put("inspectionTaskNumber", productExamineInspectionTask.getGeneralCode());
            result.put("inspectionRequestNumber", productExamineInspectionTask.getExamineImplementPlan().getGeneralCode());
            result.put("inspectItemType", productExamineInspectionTask.getInspectionType());
            result.put("username", currentUser.getUserName());
            result.put("org_id", currentUser.getOrgId());
            result.put("applyDate", new Date());
            if (productExamineInspectionTask.getInventoryMTO() != null) {
                List<Map<String, Object>> list = new ArrayList<>();
                Map<String, Object> detailMap = new HashMap<>();
                detailMap.put("inspectMaterialApplyDetailNumber", productExamineInspectionTask.getGeneralCode());
                detailMap.put("materialName", productExamineInspectionTask.getSpecificationManageMTO().getName());
                detailMap.put("materialCode", productExamineInspectionTask.getSpecificationManageMTO().getCode());
                detailMap.put("measureUnit", "");
                detailMap.put("originLotId", productExamineInspectionTask.getInventoryMTO().getInventoryLotId());
                detailMap.put("serialLotId", productExamineInspectionTask.getInventoryMTO().getLotSerialId());
                detailMap.put("inventoryId", productExamineInspectionTask.getInventoryMTO().getId());
                detailMap.put("requestQuantity", productExamineInspectionTask.getSampleQuantity());
                list.add(detailMap);
                result.put("details", list);
            }
        }
        return result;
    }
}
