package cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.proxy;

import cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.enumration.ProductQualityTrackBusinessStatusEnum;
import cec.jiutian.bc.productQualityTrack.domain.productQualityTrack.model.ProductQualityTrack;
import cec.jiutian.bc.flow.enums.ExamineStatusEnum;

import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.FlowProxy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/07/17
 * @description TODO
 */
@Component
@Slf4j
public class ProductQualityTrackFlowProxy extends FlowProxy {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public void onEvent(Object event, Object entity) {
        if (entity instanceof ProductQualityTrack model) {
            if (model.getExamineStatus().equals(ExamineStatusEnum.AUDITED.getCode())) {
                model.setBusinessStatus(ProductQualityTrackBusinessStatusEnum.Enum.APPROVED.name());
            } else if (model.getExamineStatus().equals(ExamineStatusEnum.REJECTED.getCode())) {
                model.setBusinessStatus(ProductQualityTrackBusinessStatusEnum.Enum.OPENING.name());
            }
            fabosJsonDao.mergeAndFlush(model);
        }
    }

    @Override
    public void afterSubmit(Object entity) {
        if (entity instanceof ProductQualityTrack model) {
            model.setBusinessStatus(ProductQualityTrackBusinessStatusEnum.Enum.IN_APPROVAL.name());
            fabosJsonDao.mergeAndFlush(model);
        }
    }
}
