package cec.jiutian.bc.inventoryInspection.domain.samplingTask.handler;

import cec.jiutian.bc.basicData.enumeration.NamingRuleCodeEnum;
import cec.jiutian.bc.deliveryInspection.enumeration.InspectionResultEnum;
import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.generalModeler.util.StringUtils;
import cec.jiutian.bc.inventoryInspection.domain.samplingTask.model.InventorySamplingTask;
import cec.jiutian.bc.inventoryInspection.domain.samplingTask.model.InventorySamplingTaskDetail;
import cec.jiutian.bc.inventoryInspection.domain.samplingTask.model.InventorySamplingTaskReceiveOpr;
import cec.jiutian.bc.materialInspect.domain.publicInspectionTask.mto.UserForInsTaskMTO;
import cec.jiutian.bc.modeler.enumration.SampleTypeEnum;
import cec.jiutian.bc.modeler.enumration.TaskBusinessStateEnum;
import cec.jiutian.bc.modeler.enumration.UnqualifiedHandleWayEnum;
import cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.model.AbnormalFeedbackHandling;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/12
 * @description TODO
 */
@Component
public class InventoryReceiveSampleOperationHandler implements OperationHandler<InventorySamplingTask, InventorySamplingTaskReceiveOpr> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private NamingRuleService namingRuleService;
    @Override
    public String exec(List<InventorySamplingTask> data, InventorySamplingTaskReceiveOpr modelObject, String[] param) {
        if (modelObject != null) {
            InventorySamplingTask condition = new InventorySamplingTask();
            condition.setGeneralCode(modelObject.getGeneralCode());
            condition.setInspectionType(SampleTypeEnum.Enum.INVENTORY_SAMPLING.name());
            InventorySamplingTask inventorySamplingTask = fabosJsonDao.selectOne(condition);
            if (inventorySamplingTask == null) {
                throw new FabosJsonApiErrorTip("未查询到取样单，请确认");
            }
            if (!TaskBusinessStateEnum.Enum.SEND_SAMPLE.name().equals(inventorySamplingTask.getBusinessState())) {
                throw new FabosJsonApiErrorTip("取样任务单状态有误");
            }

            inventorySamplingTask.setReceiveSamplePersonId(modelObject.getReceiveSamplePersonId());
            inventorySamplingTask.setReceiveSampleDate(modelObject.getReceiveSampleDate());
            inventorySamplingTask.setReceiveSamplePerson(modelObject.getReceiveSamplePerson());
            if (InspectionResultEnum.Enum.QUALIFIED.name().equals(modelObject.getAppearanceInspect())) {
                inventorySamplingTask.setBusinessState(TaskBusinessStateEnum.Enum.RECEIVED_SAMPLE.name());
            }else {
                if (UnqualifiedHandleWayEnum.Enum.rePackage.name().equals(modelObject.getUnqualifiedHandle())) {
                    inventorySamplingTask.setBusinessState(TaskBusinessStateEnum.Enum.SAMPLING_FINISH.name());
                }else if (UnqualifiedHandleWayEnum.Enum.reSampling.name().equals(modelObject.getUnqualifiedHandle())) {
                    inventorySamplingTask.setBusinessState(TaskBusinessStateEnum.Enum.EXCEPTION_STOP.name());
                    InventorySamplingTask newSamplingTask = new InventorySamplingTask();
                    BeanUtil.copyProperties(inventorySamplingTask, newSamplingTask);
                    newSamplingTask.setId(null);
                    newSamplingTask.setDetailList(null);
                    newSamplingTask.setBusinessState(TaskBusinessStateEnum.Enum.BE_INSPECT.name());
                    newSamplingTask.setGeneralCode(namingRuleService.getNameCode(NamingRuleCodeEnum.InspectionTask.name(), 1, null).get(0));
                    List<InventorySamplingTaskDetail> detailList = new ArrayList<>();
                    inventorySamplingTask.getDetailList().forEach(detail -> {
                        InventorySamplingTaskDetail newDetail = new InventorySamplingTaskDetail();
                        BeanUtil.copyProperties(detail, newDetail);
                        newDetail.setId(null);
                        newDetail.setInventorySamplingTask(newSamplingTask);
                        detailList.add(newDetail);
                    });
                    newSamplingTask.setDetailList(detailList);
                    fabosJsonDao.mergeAndFlush(newSamplingTask);
                }else if (UnqualifiedHandleWayEnum.Enum.exception.name().equals(modelObject.getUnqualifiedHandle())) {
                    inventorySamplingTask.setBusinessState(TaskBusinessStateEnum.Enum.BE_SAMPLING.name());
                    if (StringUtils.isNotEmpty(modelObject.getAbnormalDescription())) {
                        AbnormalFeedbackHandling abnormalFeedbackHandling = new AbnormalFeedbackHandling();
                        abnormalFeedbackHandling.setAbnormalFeedbackHandlingFormNumber(namingRuleService.getNameCode(NamingRuleCodeEnum.ABNORMAL_FEEDBACK_HANDLING.name(), 1, null).get(0));
                        abnormalFeedbackHandling.setAnalyzeAssociatedDocumentNumber(modelObject.getGeneralCode());
                        abnormalFeedbackHandling.setAbnormalDescription(modelObject.getAbnormalDescription());
                        abnormalFeedbackHandling.setSubmissionTime(modelObject.getSubmissionTime());
                        abnormalFeedbackHandling.setDiscoveredPersonId(modelObject.getDiscoveredPersonId());
                        UserForInsTaskMTO userForInsTaskMTO = fabosJsonDao.getById(UserForInsTaskMTO.class, modelObject.getDiscoveredPersonId());
                        abnormalFeedbackHandling.setDiscoveredPersonName(userForInsTaskMTO == null ? null : userForInsTaskMTO.getName());
                        abnormalFeedbackHandling.setAbnormalAttachments(modelObject.getAbnormalAttachments());
                        fabosJsonDao.mergeAndFlush(abnormalFeedbackHandling);
                    }
                }
            }
            fabosJsonDao.mergeAndFlush(inventorySamplingTask);
        }
        return "alert(操作成功)";
    }
}
