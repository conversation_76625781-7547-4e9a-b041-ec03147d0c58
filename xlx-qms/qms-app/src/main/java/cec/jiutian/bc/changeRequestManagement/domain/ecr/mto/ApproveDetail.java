package cec.jiutian.bc.changeRequestManagement.domain.ecr.mto;

import cec.jiutian.bc.changeRequestManagement.enums.ChangeLevel;
import cec.jiutian.bc.changeRequestManagement.enums.ChangeType;
import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowBaseOperation;
import jakarta.persistence.Column;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@FabosJson(
        name = "审批详情",
        power = @Power(add = false, edit = false, delete = false, importable = false, export = false),
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "1 == 1",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.HIDE
                ),
                @RowBaseOperation(
                        code = "add",
                        ifExpr = "1 == 1",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.HIDE
                ),
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "1 == 1",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.HIDE
                )
        }
)
@TemplateType(type = "multiTable")
public class ApproveDetail extends BaseModel {
    @FabosJsonField(
            views = @View(title = "变更主题"),
            edit = @Edit(
                    readonly = @Readonly,
                    title = "变更主题",
                    inputType = @InputType(length = 20),
                    search = @Search(vague = true)
            )
    )
    @Column(name = "change_subject", length = 20)
    private String changeSubject;

    @FabosJsonField(
            views = @View(title = "变更类型"),
            edit = @Edit(
                    readonly = @Readonly,
                    title = "变更类型",
                    inputType = @InputType(length = 20),
                    search = @Search,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ChangeType.class)
            )
    )
    @Column(name = "change_type", length = 20)
    private String changeType;

    @FabosJsonField(
            views = @View(title = "变更级别"),
            edit = @Edit(
                    readonly = @Readonly,
                    title = "变更级别",
                    inputType = @InputType(length = 20),
                    search = @Search,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ChangeLevel.class)
            )
    )
    @Column(name = "change_level", length = 20)
    private String changeLevel;

    @FabosJsonField(
            views = @View(title = "变更原因"),
            edit = @Edit(
                    readonly = @Readonly,
                    type = EditType.TEXTAREA,
                    title = "变更原因",
                    inputType = @InputType(length = 100)
            )
    )
    @Column(name = "change_reason", length = 100)
    private String changeReason;

    @FabosJsonField(
            views = @View(title = "审批进度", column = "taskName", type = ViewType.TABLE_VIEW),
            edit = @Edit(
                    readonly = @Readonly,
                    title = "审批进度",
                    type = EditType.TAB_TABLE_ADD,
                    allowAddMultipleRows = true,
                    referenceTableType = @ReferenceTableType(label = "taskName")
            )
    )
    private List<ApproveTaskDetail> approveTaskDetails;
}
