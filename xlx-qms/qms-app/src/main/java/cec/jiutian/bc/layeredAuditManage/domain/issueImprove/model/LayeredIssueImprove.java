package cec.jiutian.bc.layeredAuditManage.domain.issueImprove.model;


import cec.jiutian.bc.layeredAuditManage.domain.issueImprove.handler.LayeredIssueImproveAnalyzeHandler;
import cec.jiutian.bc.layeredAuditManage.domain.issueImprove.handler.LayeredIssueImproveCompleteHandler;
import cec.jiutian.bc.layeredAuditManage.domain.issueImprove.handler.LayeredIssueImproveExecHandler;
import cec.jiutian.bc.layeredAuditManage.domain.issueImprove.handler.LayeredIssueImproveVerifyHandler;
import cec.jiutian.bc.layeredAuditManage.domain.layeredAuditTemplate.model.LayeredAuditTemplate;
import cec.jiutian.bc.layeredAuditManage.enumeration.AuditLevelStatusEnum;
import cec.jiutian.bc.layeredAuditManage.enumeration.LayeredIssueImproveStatusEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.*;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowOperation;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Table(name = "qms_sam_layered_issue_improve", uniqueConstraints = @UniqueConstraint(columnNames = "generalCode"))
@Entity
@Getter
@Setter
@FabosJson(
        name = "问题改善",
        orderBy = "LayeredIssueImprove.createTime desc",
        power = @Power(viewDetails = false, add = false, edit = false, delete = false, export = false),
        //dataProxy = IssueImproveDataProxy.class,
        rowOperation = {
                @RowOperation(
                        title = "原因分析",
                        code = "LayeredIssueImprove@ANALYZE",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        operationHandler = LayeredIssueImproveAnalyzeHandler.class,
                        fabosJsonClass = LayeredIssueImproveAnalyze.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "LayeredIssueImprove@ANALYZE"
                        ),
                        ifExpr = "selectedItems[0].status != 'PENDING_EXECUTION'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                    title = "执行",
                    code = "LayeredIssueImprove@EXEC",
                    mode = RowOperation.Mode.HEADER,
                    type = RowOperation.Type.POPUP,
                    submitMethod = RowOperation.SubmitMethod.HANDLER,
                    popupType = RowOperation.PopupType.FORM,
                    operationHandler = LayeredIssueImproveExecHandler.class,
                    fabosJsonClass = LayeredIssueImproveExec.class,
                    show = @ExprBool(
                            exprHandler = UserRowOperationExprHandler.class,
                            params = "LayeredIssueImprove@EXEC"
                    ),
                    ifExpr = "selectedItems[0].status != 'EXECUTING'",
                    ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "完成",
                        code = "LayeredIssueImprove@COMPLETE",
                        mode = RowOperation.Mode.HEADER,
                        operationHandler = LayeredIssueImproveCompleteHandler.class,
                        callHint = "请确认是否完成？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "LayeredIssueImprove@COMPLETE"
                        ),

                        ifExpr = "selectedItems[0].status != 'EXECUTED'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "结果验证",
                        code = "LayeredIssueImprove@VERIFY",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        operationHandler = LayeredIssueImproveVerifyHandler.class,
                        fabosJsonClass = LayeredIssueImproveVerify.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "LayeredIssueImprove@VERIFY"
                        ),
                        ifExpr = "selectedItems[0].status != 'PENDING_VERIFY'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                )
        }
)
@TemplateType(type = "multiTable")
public class LayeredIssueImprove extends MetaModel {

    @FabosJsonField(
            views = @View(title = "问题改善任务单号"),
            edit = @Edit(title = "问题改善任务单号", search = @Search(vague = true))
    )
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "关联审核模板", column = "templateName"),
            edit = @Edit(title = "关联审核模板",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "templateName")
            )
    )
    @ManyToOne
    @JoinColumn(foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private LayeredAuditTemplate layeredAuditTemplate;

    // 级别
    @FabosJsonField(
            views = @View(title = "级别"),
            edit = @Edit(title = "级别", type = EditType.CHOICE, show = false,
                    choiceType = @ChoiceType(fetchHandler = AuditLevelStatusEnum.class))
    )
    private String auditLevel;

    // 状态
    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态", type = EditType.CHOICE, show = false,
                    choiceType = @ChoiceType(fetchHandler = LayeredIssueImproveStatusEnum.class))
    )
    private String status;

    //问题详情
    @FabosJsonField(
            views = @View(title = "问题详情", type = ViewType.TABLE_VIEW),
            edit = @Edit(title = "问题详情", type = EditType.TAB_TABLE_ADD)
    )
    @JoinColumn(name = "layered_issue_improve_id")
    @OneToMany(cascade = CascadeType.ALL)
    private List<IssueItemDetail> issueItemDetailList;

    @FabosJsonField(
            views = @View(title = "原因分析", type = ViewType.TABLE_VIEW),
            edit = @Edit(title = "原因分析", type = EditType.TAB_TABLE_ADD)
    )
    @JoinColumn(name = "layered_issue_improve_id")
    @OneToMany(cascade = CascadeType.ALL)
    private List<LayeredIssueImproveAnalyze> layeredIssueImproveAnalyzeList;

    @FabosJsonField(
            views = @View(title = "执行详情", type = ViewType.TABLE_VIEW),
            edit = @Edit(title = "执行详情", type = EditType.TAB_TABLE_ADD)
    )
    @JoinColumn(name = "layered_issue_improve_id")
    @OneToMany(cascade = CascadeType.ALL)
    private List<LayeredIssueImproveExec> layeredIssueImproveExecList;

    @FabosJsonField(
            views = @View(title = "验证详情", type = ViewType.TABLE_VIEW),
            edit = @Edit(title = "验证详情", type = EditType.TAB_TABLE_ADD)
    )
    @JoinColumn(name = "layered_issue_improve_id")
    @OneToMany(cascade = CascadeType.ALL)
    private List<LayeredIssueImproveVerify> layeredIssueImproveVerifyList;
}
