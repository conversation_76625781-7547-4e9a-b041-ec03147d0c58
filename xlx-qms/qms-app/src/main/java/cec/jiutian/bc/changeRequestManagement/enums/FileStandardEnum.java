package cec.jiutian.bc.changeRequestManagement.enums;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

public class FileStandardEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum data : Enum.values()) {
            list.add(new VLModel(data.name(), data.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {

        //DFMEA
        DFMEA("DFMEA"),
        //PFMEA
        PFMEA("PFMEA"),
        //特殊特性清单
        SPECIAL_CHARACTERISTICS("特殊特性清单"),
        //过程流程图
        PROCESS_FLOW_CHART("过程流程图"),
        //控制计划
        CONTROL_PLAN("控制计划"),
        //作业标准
        TEST_STANDARD("检验标准"),
        //检验标准
        WORK_STANDARD("作业标准"),
        //设备/量具资料
        EQUIPMENT_MATERIAL_STANDARD("设备/量具资料"),
        //材料清单BOM
        MATERIAL_LIST_BOM("材料清单BOM"),
        // 包装方案
        PACKAGING_SCHEME("包装方案"),
        //PSW
        PSW("PSW"),
        // 其它
        OTHER("其它"),
        ;

        private final String value;

    }

}
