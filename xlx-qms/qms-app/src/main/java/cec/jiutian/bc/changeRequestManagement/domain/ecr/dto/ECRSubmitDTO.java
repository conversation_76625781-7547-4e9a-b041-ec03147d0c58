package cec.jiutian.bc.changeRequestManagement.domain.ecr.dto;

import cec.jiutian.bc.changeRequestManagement.domain.ecr.mto.UserForApproveMTO;
import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ReferenceTableType;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@FabosJson(
        name = "变更申请提交"
)
public class ECRSubmitDTO extends BaseModel {

    @FabosJsonField(
            views = @View(title = "审批人", column = "name"),
            edit = @Edit(
                    title = "选择审批人",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"))
    )
    private UserForApproveMTO approveMTO;
}
