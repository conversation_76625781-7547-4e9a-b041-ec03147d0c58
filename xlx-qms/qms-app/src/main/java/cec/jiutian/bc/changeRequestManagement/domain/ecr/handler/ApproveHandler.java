package cec.jiutian.bc.changeRequestManagement.domain.ecr.handler;

import cec.jiutian.bc.changeRequestManagement.domain.ecr.event.ApproveTaskEvent;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.model.ApproveTask;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.mto.ApproveMTO;
import cec.jiutian.bc.changeRequestManagement.enums.ApproveTaskStatusEnum;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import jakarta.persistence.LockModeType;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Component
public class ApproveHandler implements OperationHandler<ApproveTask, ApproveMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private ApproveTaskEvent approveTaskEvent;

    @Override
    @Transactional
    public String exec(List<ApproveTask> data, ApproveMTO modelObject, String[] param) {
        if (CollectionUtils.isEmpty(data)) {
            throw new RuntimeException("数据异常");
        }
        String userName = UserContext.getUserName();
        String userId = UserContext.getUserId();
        if (StringUtils.isBlank(userId) || StringUtils.isBlank(userName)) {
            throw new ServiceException("用户未登录");
        }

        ApproveTask approveTask = fabosJsonDao.getEntityManager().find(ApproveTask.class, data.get(0).getId(), LockModeType.PESSIMISTIC_WRITE);
        if (!ApproveTaskStatusEnum.Enum.WAIT_APPROVE.name().equals(approveTask.getStatus())) {
            return "alert('请刷新页面，该任务已被处理')";
        }
        approveTask.setStatus(ApproveTaskStatusEnum.Enum.COMPLETE.name());
        approveTask.setResult(modelObject.getResult());
        approveTask.setExplain(modelObject.getExplain());
        approveTask.setAttachment(modelObject.getAttachment());
        approveTask.setApproveTime(new Date());
        approveTask.setOperator(userName);
        approveTask.setOperatorId(userId);
        fabosJsonDao.updateAndFlush(approveTask);
        approveTaskEvent.onEvent(approveTask);
        return "执行成功";
    }


}
