package cec.jiutian.bc.changeRequestManagement.domain.ecn.handler;

import cec.jiutian.bc.changeRequestManagement.domain.ecn.model.ChangeRequestExecute;
import cec.jiutian.bc.changeRequestManagement.domain.ecn.mto.ECNApproveDetail;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.model.ApproveTask;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.mto.ApproveTaskDetail;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.repository.ApproveTaskRepository;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class ECNApproveDetailViewHandler implements OperationHandler<ChangeRequestExecute, ECNApproveDetail> {

    @Resource
    private ApproveTaskRepository approveTaskRepository;



    @Override
    public ECNApproveDetail fabosJsonFormValue(List<ChangeRequestExecute> data, ECNApproveDetail fabosJsonForm, String[] param) {

        if (CollectionUtils.isEmpty(data) || data.get(0).getId() == null) {
            throw new ServiceException("数据异常");
        }
        BeanUtils.copyProperties(data.get(0), fabosJsonForm);
        List<ApproveTask> approveTasks = approveTaskRepository.findByBusinessKeyOrderByCreateDate(data.get(0).getId());
        if (CollectionUtils.isEmpty(approveTasks)) {
            throw new ServiceException("未查询到审批信息");
        }
        ArrayList<ApproveTaskDetail> details = new ArrayList<>(approveTasks.size());
        for (ApproveTask approveTask : approveTasks) {
            details.add(ApproveTaskDetail.create(approveTask));
        }
        fabosJsonForm.setApproveTaskDetails(details);
        return fabosJsonForm;
    }
}
