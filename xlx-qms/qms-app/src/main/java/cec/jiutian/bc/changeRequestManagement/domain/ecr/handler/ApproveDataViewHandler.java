package cec.jiutian.bc.changeRequestManagement.domain.ecr.handler;

import cec.jiutian.bc.changeRequestManagement.domain.ecr.model.ApproveTask;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.mto.ChangeRequestView;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class ApproveDataViewHandler implements OperationHandler<ApproveTask, ChangeRequestView> {

    @Resource
    private FabosJsonDao fabosJsonDao;


    @Override
    public ChangeRequestView fabosJsonFormValue(List<ApproveTask> data, ChangeRequestView fabosJsonForm, String[] param) {
        if (CollectionUtils.isEmpty(data)) {
            throw new ServiceException("数据异常");
        }
        ChangeRequestView changeRequestView = fabosJsonDao.findById(ChangeRequestView.class, data.get(0).getBusinessKey());
        return changeRequestView;
    }
}
