package cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.handler;

import cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.model.AccidentReportTask;
import cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.mto.AccidentReportTaskCreateMTO;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/29
 * @description
 */
@Component
public class AccidentReportTaskModifyHandler implements OperationHandler<AccidentReportTask, AccidentReportTaskCreateMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<AccidentReportTask> data, AccidentReportTaskCreateMTO modelObject, String[] param) {
        if (modelObject != null) {
            AccidentReportTask model = data.get(0);
            BeanUtil.copyProperties(modelObject, model, "createBy", "createTime");
            fabosJsonDao.mergeAndFlush(model);
        }
        return "alert(操作成功)";
    }

    @Override
    public AccidentReportTaskCreateMTO fabosJsonFormValue(List<AccidentReportTask> data, AccidentReportTaskCreateMTO fabosJsonForm, String[] param) {
        AccidentReportTask model = data.get(0);
        BeanUtil.copyProperties(model, fabosJsonForm);
        return fabosJsonForm;
    }
}
