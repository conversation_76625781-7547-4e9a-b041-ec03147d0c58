package cec.jiutian.bc.productExamineManagement.domain.examineYearlyPlan.model;

import cec.jiutian.bc.mto.SpecificationManageMTO;
import cec.jiutian.bc.productExamineManagement.domain.examineYearlyPlan.handler.ProductExamineYearPLanPublishOperationHandler;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DateType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.RowBaseOperation;
import cec.jiutian.view.type.RowOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/5/16
 * @description TODO
 */
@FabosJson(
        name = "审核年度计划",
        orderBy = "ProductExamineYearlyPlan.createTime desc",
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "businessState !='EDIT'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "businessState !='EDIT'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
        },
        rowOperation = {
                @RowOperation(
                        title = "发布",
                        code = "ProductExamineYearlyPlan@PUBLISH",
                        operationHandler = ProductExamineYearPLanPublishOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "是否确定操作？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "ProductExamineYearlyPlan@PUBLISH"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "businessState != 'EDIT'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
        }
)
@Table(name = "qms_pem_product_examine_yearly_plan"
)
@Entity
@Getter
@Setter
public class ProductExamineYearlyPlan extends MetaModel {

    @FabosJsonField(
            views = @View(title = "审核计划名称"),
            edit = @Edit(title = "审核计划名称",notNull = true)
    )
    private String planName;

    @FabosJsonField(
            views = @View(title = "审核目的"),
            edit = @Edit(title = "审核目的",notNull = true)
    )
    private String examinePurpose;

    @FabosJsonField(
            views = @View(title = "审核范围"),
            edit = @Edit(title = "审核范围",notNull = true)
    )
    private String examineRange;

    @FabosJsonField(
            views = @View(title = "审核依据"),
            edit = @Edit(title = "审核依据",notNull = true)
    )
    private String examineBasis;

    @FabosJsonField(
            views = @View(title = "产品编码", column = "code"),
            edit = @Edit(title = "产品编码", notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    filter = @Filter(value = "validateFlag = 'Y' and specificationCode like '02%'"),
                    referenceTableType = @ReferenceTableType(id = "id", label = "code")
            )
    )
    @ManyToOne
    @JoinColumn(name = "material_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private SpecificationManageMTO material;

    @FabosJsonField(
            views = @View(title = "物资Id",show = false),
            edit = @Edit(title = "物资Id",
                    show = false
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "material", beFilledBy = "id"))
    )
    @Column(name = "material_id", insertable = false, updatable = false)
    private String materialId;

    @FabosJsonField(
            views = @View(title = "产品名称",show = false),
            edit = @Edit(title = "产品名称", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "material", beFilledBy = "name"))
    )
    private String materialName;

    @FabosJsonField(
            views = @View(title = "产品规格"),
            edit = @Edit(title = "产品规格"),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "material", beFilledBy = "type"))
    )
    private String materialSpecification;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",readonly = @Readonly,type = EditType.CHOICE,
                    defaultVal = "EDIT",
                    choiceType = @ChoiceType(fetchHandler = OrderCurrentStateEnum.class)),
            dynamicField = @DynamicField(passive = true)
    )
    private String businessState;

    @FabosJsonField(
            views = @View(title = "计划日期"),
            edit = @Edit(title = "计划日期",
                    notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date planDate;

    @FabosJsonField(
            views = @View(title = "实际完成日期"),
            edit = @Edit(title = "实际完成日期",
                    show = false,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date actualDate;
}
