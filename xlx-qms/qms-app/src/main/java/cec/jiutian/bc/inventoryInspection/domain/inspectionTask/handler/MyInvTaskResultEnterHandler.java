package cec.jiutian.bc.inventoryInspection.domain.inspectionTask.handler;

import cec.jiutian.bc.inventoryInspection.domain.inspectionTask.model.MyInventoryInspectionTask;
import cec.jiutian.bc.inventoryInspection.domain.inspectionTask.mto.InventoryTaskResultEnterDetailMTO;
import cec.jiutian.bc.inventoryInspection.domain.inspectionTask.mto.MyInvTaskResultEnterMTO;
import cec.jiutian.bc.modeler.domain.inspectionItem.model.InspectionItem;
import cec.jiutian.bc.modeler.enumration.InspectionResultEnum;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/4/9
 * @description TODO
 */
@Component
public class MyInvTaskResultEnterHandler implements OperationHandler<MyInventoryInspectionTask, MyInvTaskResultEnterMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<MyInventoryInspectionTask> data, MyInvTaskResultEnterMTO modelObject, String[] param) {
        if (modelObject != null) {
            MyInventoryInspectionTask myInventoryInspectionTask = data.get(0);
            myInventoryInspectionTask.setInspectionResult(modelObject.getInspectionResult());
            myInventoryInspectionTask.setUnqualifiedLevel(modelObject.getUnqualifiedLevel());
            myInventoryInspectionTask.getStandardDetailList().forEach(detail -> {
                List<InventoryTaskResultEnterDetailMTO> rdetailMTOS = modelObject.getDetailList().stream().filter(rdetail -> rdetail.getInspectItemId().equals(detail.getItemId())).toList();
                if (CollectionUtils.isNotEmpty(detail.getInventoryInspectionStandardItemTargetList())) {
                    detail.getInventoryInspectionStandardItemTargetList().forEach(target -> {
                        Optional<InventoryTaskResultEnterDetailMTO> optional = rdetailMTOS.stream().filter(resultDetail -> resultDetail.getTargetId().equals(target.getTargetId()) && resultDetail.getInspectionValueType().equals(target.getInspectionValueType())).findFirst();
                        if (optional.isPresent()) {
                            InventoryTaskResultEnterDetailMTO resultEnterDetailMTO = optional.get();
                            target.setResultValue(resultEnterDetailMTO.getResultValue());
                            target.setTargetResult(resultEnterDetailMTO.getInspectionItemResult());
                        }
                    });
                }
                if (CollectionUtils.isNotEmpty(rdetailMTOS)) {
                    if (rdetailMTOS.stream().anyMatch(resultDetail -> InspectionResultEnum.Enum.UNQUALIFIED.name().equals(resultDetail.getInspectionItemResult()))) {
                        detail.setInspectionResult(InspectionResultEnum.Enum.UNQUALIFIED.name());
                    }else {
                        detail.setInspectionResult(InspectionResultEnum.Enum.QUALIFIED.name());
                    }
                }
            });
            fabosJsonDao.mergeAndFlush(myInventoryInspectionTask);
        }
        return "alert(操作成功)";
    }

    @Override
    public MyInvTaskResultEnterMTO fabosJsonFormValue(List<MyInventoryInspectionTask> data, MyInvTaskResultEnterMTO fabosJsonForm, String[] param) {
        MyInventoryInspectionTask myProcessInspectionTask = data.get(0);
        BeanUtil.copyProperties(myProcessInspectionTask, fabosJsonForm);
        if (CollectionUtils.isEmpty(myProcessInspectionTask.getStandardDetailList())) {
            throw new ServiceException("检验项目为空");
        }
        List<InventoryTaskResultEnterDetailMTO> detailMTOList = new ArrayList<>();
        myProcessInspectionTask.getStandardDetailList().forEach(item -> {
            if (item.getItemId() == null) {
                throw new ServiceException("检验项为空");
            }
            InspectionItem inspectionItem = fabosJsonDao.getById(InspectionItem.class, item.getItemId());
            if (inspectionItem == null || CollectionUtils.isEmpty(inspectionItem.getInspectionItemTargetList())) {
                throw new ServiceException("检验项指标为空");
            }
            item.getInventoryInspectionStandardItemTargetList().forEach(target -> {
                InventoryTaskResultEnterDetailMTO taskResultEnterDetailMTO = new InventoryTaskResultEnterDetailMTO();
                taskResultEnterDetailMTO.setTaskCode(myProcessInspectionTask.getGeneralCode());
                taskResultEnterDetailMTO.setTargetId(target.getTargetId());
                taskResultEnterDetailMTO.setInspectItemId(inspectionItem.getId());
                taskResultEnterDetailMTO.setName(inspectionItem.getName());
                taskResultEnterDetailMTO.setFeature(inspectionItem.getFeature());
                taskResultEnterDetailMTO.setItemType(inspectionItem.getItemType());
                taskResultEnterDetailMTO.setTargetName(target.getName());
                taskResultEnterDetailMTO.setInspectionValueType(target.getInspectionValueType());
                taskResultEnterDetailMTO.setComparisonMethod(target.getComparisonMethod());
                taskResultEnterDetailMTO.setUnit(target.getUnit());
                taskResultEnterDetailMTO.setStandardValue(target.getStandardValue());
                taskResultEnterDetailMTO.setLowerValue(target.getLowerValue());
                taskResultEnterDetailMTO.setUpperValue(target.getUpperValue());
                taskResultEnterDetailMTO.setResultValue(target.getResultValue());
                taskResultEnterDetailMTO.setInspectionItemResult(target.getTargetResult());
                detailMTOList.add(taskResultEnterDetailMTO);
            });
        });
        fabosJsonForm.setDetailList(detailMTOList);

        return fabosJsonForm;
    }
}
