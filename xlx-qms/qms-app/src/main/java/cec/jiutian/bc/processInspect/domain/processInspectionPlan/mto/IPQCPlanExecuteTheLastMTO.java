package cec.jiutian.bc.processInspect.domain.processInspectionPlan.mto;

import cec.jiutian.bc.processInspect.domain.processInspectionPlan.handler.IPQCPlanExecuteTheLastReferenceAddHandler;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.ReferenceAddType;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToMany;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@FabosJson(
        name = "检验计划详情执行末检MTO"
)
@Entity
@Getter
@Setter
@TemplateType(type = "multiTable")
public class IPQCPlanExecuteTheLastMTO extends MetaModel {

    @FabosJsonField(
            views = @View(title = "编号"),
            edit = @Edit(title = "编号", readonly = @Readonly)
    )
    private String generalCode;

    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumn(name = "lab_id")
    @FabosJsonField(
            views = @View(title = "计划详情", type = ViewType.TABLE_VIEW, extraPK = "id", column = "operationName"),
            edit = @Edit(title = "计划详情", type = EditType.TAB_REFER_ADD),
            referenceAddType = @ReferenceAddType(referenceClass = "ProcessInspectionPlanDetail",
                    filter = "inspectionPlan.id = '${id}' and type = 'last' and ProcessInspectionPlanDetail.currentState = 'EDIT'",
                    referenceAddHandler = IPQCPlanExecuteTheLastReferenceAddHandler.class)
    )
    private List<ProcessInspectionPlanDetailMTO> detailList;
}
