package cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.handler;

import cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.enumration.ArmBusinessStatusEnum;
import cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.model.AccidentReportTask;
import cec.jiutian.bc.accidentReportManagement.enumeration.ArmMeasureStatusEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/29
 * @description
 */
@Component
public class AccidentReportTaskAnalySubmitHandler implements OperationHandler<AccidentReportTask, Void> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Transactional
    @Override
    public String exec(List<AccidentReportTask> data, Void modelObject, String[] param) {
        AccidentReportTask model = data.get(0);
        model.setBusinessStatus(ArmBusinessStatusEnum.Enum.UNDER_DISPOSAL.name());
        model.setTemporaryMeasureStatus(ArmMeasureStatusEnum.Enum.NOT_FILLED_IN.name());
        model.setPreventMeasureStatus(ArmMeasureStatusEnum.Enum.NOT_FILLED_IN.name());
        fabosJsonDao.update(model);
        return null;
    }
}
