package cec.jiutian.bc.inventoryInspection.domain.inspectionTask.handler;

import cec.jiutian.bc.generalModeler.util.StringUtils;
import cec.jiutian.bc.inventoryInspection.domain.inspectionTask.model.InventoryInspectionStandardDetail;
import cec.jiutian.bc.inventoryInspection.domain.inspectionTask.model.MyInventoryInspectionTask;
import cec.jiutian.bc.modeler.enumration.TaskBusinessStateEnum;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/28
 * @description TODO
 */
@Component
public class InventoryInspectionResultCommitOperationHand<PERSON> implements OperationHandler<MyInventoryInspectionTask, Void> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<MyInventoryInspectionTask> data, Void modelObject, String[] param) {
        MyInventoryInspectionTask myInventoryInspectionTask = data.get(0);
        List<InventoryInspectionStandardDetail> standardDetailList = myInventoryInspectionTask.getStandardDetailList().stream().filter(detail -> StringUtils.isEmpty(detail.getInspectionResult())).toList();
        if (CollectionUtils.isNotEmpty(standardDetailList)) {
            throw new FabosJsonApiErrorTip("检验项检验结果未全部录入，请确认");
        }else {
            myInventoryInspectionTask.setBusinessState(TaskBusinessStateEnum.Enum.INSPECT_FINISH.name());
            fabosJsonDao.mergeAndFlush(myInventoryInspectionTask);
        }

        return "alert('操作成功')";
    }
}
