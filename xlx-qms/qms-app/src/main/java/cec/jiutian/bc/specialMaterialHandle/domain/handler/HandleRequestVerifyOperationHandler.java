package cec.jiutian.bc.specialMaterialHandle.domain.handler;

import cec.jiutian.bc.specialMaterialHandle.domain.model.SpecialMaterialHandleRequest;
import cec.jiutian.bc.specialMaterialHandle.enumeration.SpecialMaterialBusinessStatusEnum;
import cec.jiutian.bc.specialMaterialHandle.mto.HandleRequestVerifyMTO;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/26
 * @description TODO
 */
@Component
public class HandleRequestVerifyOperationHandler implements OperationHandler<SpecialMaterialHandleRequest, HandleRequestVerifyMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<SpecialMaterialHandleRequest> data, HandleRequestVerifyMTO modelObject, String[] param) {
        SpecialMaterialHandleRequest specialMaterialHandleRequest = data.get(0);
        specialMaterialHandleRequest.setBusinessState(SpecialMaterialBusinessStatusEnum.Enum.COMPLETED.name());
        fabosJsonDao.mergeAndFlush(specialMaterialHandleRequest);
        return "alert('操作成功')";
    }
}
