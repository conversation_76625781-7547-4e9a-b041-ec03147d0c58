package cec.jiutian.bc.qualityExceptionManagement.port.client;

import cec.jiutian.api.remoteCallLog.dto.RemoteCallResult;
import cec.jiutian.bc.qualityExceptionManagement.port.dto.ProductInfoDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

@Component
@FeignClient(name = "api-business-batch")
public interface BatchSerialApiFeignClient {

    @PostMapping("/remote/getProductInfoBySerialNumber")
    ProductInfoDTO getProductInfoBySerialNumber(@RequestParam("serialNumber") String serialNumber);
}
