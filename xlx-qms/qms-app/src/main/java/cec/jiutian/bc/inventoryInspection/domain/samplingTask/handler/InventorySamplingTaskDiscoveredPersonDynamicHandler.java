package cec.jiutian.bc.inventoryInspection.domain.samplingTask.handler;

import cec.jiutian.bc.inventoryInspection.domain.samplingTask.model.InventorySamplingTaskReceiveOpr;
import cec.jiutian.bc.inventoryInspection.domain.samplingTask.model.InventorySamplingTaskReceiveOprDetail;
import cec.jiutian.bc.materialInspect.domain.publicInspectionTask.mto.UserForInsTaskMTO;
import cec.jiutian.bc.materialInspect.domain.samplingTask.model.SamplingTask;
import cec.jiutian.bc.materialInspect.enumeration.InspectionResultEnum;
import cec.jiutian.bc.modeler.enumration.TaskBusinessStateEnum;
import cec.jiutian.bc.processInspect.domain.publicInspectionTask.mto.ProcessUserForInsTaskMTO;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.DependFiled;
import cn.hutool.core.bean.BeanUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：2025/7/4
 * @description：
 */
@Component
public class InventorySamplingTaskDiscoveredPersonDynamicHandler implements DependFiled.DynamicHandler<InventorySamplingTaskReceiveOpr> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public Map<String, Object> handle(InventorySamplingTaskReceiveOpr inventorySamplingTaskReceiveOpr) {
        Map<String, Object> result = new HashMap<>();
        if (StringUtils.isNotEmpty(inventorySamplingTaskReceiveOpr.getAppearanceInspect())) {
            if (inventorySamplingTaskReceiveOpr.getAppearanceInspect().equals(InspectionResultEnum.Enum.UNQUALIFIED.name())
                    && inventorySamplingTaskReceiveOpr.getDiscoveredPerson() == null) {
                result.put("discoveredPersonName", "");
            }
        }
        return result;
    }
}
