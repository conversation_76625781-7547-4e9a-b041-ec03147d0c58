package cec.jiutian.bc.samplingTaskManagement.domain.otherSamplingTask.handler;

import cec.jiutian.bc.processInspect.domain.publicInspectionTask.mto.ProcessUserForInsTaskMTO;
import cec.jiutian.bc.samplingTaskManagement.domain.otherSamplingTask.model.OtherSamplingTask;
import cec.jiutian.bc.samplingTaskManagement.domain.otherSamplingTask.model.OtherSamplingTaskOpr;
import cec.jiutian.bc.samplingTaskManagement.domain.otherSamplingTask.model.OtherSamplingTaskOprDetail;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.DependFiled;
import cn.hutool.core.bean.BeanUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/5/12
 * @description TODO
 */
@Component
public class OtherSamplingTaskOprDynamicHandler implements DependFiled.DynamicHandler<OtherSamplingTaskOpr> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public Map<String, Object> handle(OtherSamplingTaskOpr otherSamplingTaskOpr) {
        Map<String, Object> result = new HashMap<>();
        if (StringUtils.isNotEmpty(otherSamplingTaskOpr.getGeneralCode())) {
            OtherSamplingTask condition = new OtherSamplingTask();
            condition.setGeneralCode(otherSamplingTaskOpr.getGeneralCode());
            OtherSamplingTask otherSamplingTask = fabosJsonDao.selectOne(condition);
            if (otherSamplingTask == null) {
                throw new FabosJsonApiErrorTip("未查询到取样单，请确认");
            }
            result.put("id",otherSamplingTask.getId());
            result.put("inspectionTaskCode",otherSamplingTask.getInspectionTaskCode());
            result.put("otherSamplingTaskCode",otherSamplingTask.getGeneralCode());
            result.put("materialCode", otherSamplingTask.getMaterialCode());
            result.put("materialName", otherSamplingTask.getMaterialName());
            result.put("originLotId", otherSamplingTask.getOriginLotId());
            result.put("allInspectionItemQuantity", otherSamplingTask.getAllInspectionItemQuantity());
            result.put("unit", otherSamplingTask.getUnit());
            result.put("inspectionType", otherSamplingTask.getInspectionType());
            result.put("samplingPoint",otherSamplingTask.getSamplePoint());
            result.put("sendInspectPoint", otherSamplingTask.getSendPoint());
            result.put("sendSamplePersonId", otherSamplingTask.getSendSamplePersonId());
            result.put("sendSamplePerson", otherSamplingTask.getSendSamplePerson());
            result.put("sendSampleDate", otherSamplingTask.getSendSampleDate());
            result.put("businessState", otherSamplingTask.getBusinessState());
            if (otherSamplingTask.getSendSamplePersonId()!= null) {
                ProcessUserForInsTaskMTO discoveredPerson = fabosJsonDao.findById(ProcessUserForInsTaskMTO.class, otherSamplingTask.getSendSamplePersonId());
                if (discoveredPerson != null) {
                    result.put("user", discoveredPerson);
                } else {
                    throw new FabosJsonApiErrorTip("未查到送样人数据，请确认");
                }
            }
            if (CollectionUtils.isNotEmpty(otherSamplingTask.getDetailList())) {
                List<OtherSamplingTaskOprDetail> detailList = new ArrayList<>();
                otherSamplingTask.getDetailList().forEach(otherSamplingTaskDetail -> {
                    OtherSamplingTaskOprDetail oprDetail = new OtherSamplingTaskOprDetail();
                    BeanUtil.copyProperties(otherSamplingTaskDetail, oprDetail);
                    detailList.add(oprDetail);
                });
                result.put("detailList", detailList);
            }
        }
        return result;
    }
}
