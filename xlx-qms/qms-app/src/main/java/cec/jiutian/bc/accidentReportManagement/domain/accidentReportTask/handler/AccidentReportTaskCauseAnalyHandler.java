package cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.handler;

import cec.jiutian.bc.accidentReportManagement.enumeration.ArmProgressEnum;
import cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.model.AccidentReportTask;
import cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.mto.AccidentReportTaskCauseAnalyMTO;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/30
 * @description TODO
 */
@Component
public class AccidentReportTaskCauseAnalyHandler implements OperationHandler<AccidentReportTask, AccidentReportTaskCauseAnalyMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<AccidentReportTask> data, AccidentReportTaskCauseAnalyMTO modelObject, String[] param) {
        modelObject.setAnalyticalStatus(ArmProgressEnum.Enum.RUN_SUBMIT.name());

        AccidentReportTask model = data.get(0);
        BeanUtil.copyProperties(modelObject, model, "createBy", "createTime");
        fabosJsonDao.mergeAndFlush(model);
        return "alert(操作成功)";
    }

    @Override
    public AccidentReportTaskCauseAnalyMTO fabosJsonFormValue(List<AccidentReportTask> data, AccidentReportTaskCauseAnalyMTO fabosJsonForm, String[] param) {
        AccidentReportTask model = data.get(0);
        BeanUtil.copyProperties(model, fabosJsonForm);
        return fabosJsonForm;
    }
}
