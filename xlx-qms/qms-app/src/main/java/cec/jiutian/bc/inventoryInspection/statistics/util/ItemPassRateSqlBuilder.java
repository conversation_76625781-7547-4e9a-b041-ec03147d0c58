package cec.jiutian.bc.inventoryInspection.statistics.util;

import cec.jiutian.bc.inventoryInspection.statistics.ao.StatisticsCommonParam;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

public class ItemPassRateSqlBuilder {

    public static String buildSql(StatisticsCommonParam param) {
        StringBuilder sql = new StringBuilder(
                "SELECT * FROM ( SELECT \n" +
                        " inspection_item_id AS \"检测项ID\", \n" +
                        " item_name AS \"检测项名称\", \n"
        );

        // 根据计算维度选择统计方式（数量 vs 重量）
        if ("0".equals(param.getCalculateDimension())) {
            // 按批次数量计算
            sql.append(" COUNT(inspection_item_id) AS \"生产数量\", \n")
                    .append(" COALESCE(SUM(CASE WHEN inspection_result = 'UNQUALIFIED' THEN 1 END), 0) AS \"不良数量\", \n")
                    .append(" COALESCE(SUM(CASE WHEN inspection_result = 'QUALIFIED' THEN 1 END), 0) AS \"合格数量\", \n");
        } else {
            // 按批次重量计算
            sql.append(" COALESCE(SUM(output_quantity), 0) AS \"生产重量\", \n")
                    .append(" COALESCE(SUM(CASE WHEN inspection_result = 'UNQUALIFIED' THEN output_quantity END), 0) AS \"不良重量\", \n")
                    .append(" COALESCE(SUM(CASE WHEN inspection_result = 'QUALIFIED' THEN  output_quantity END), 0) AS \"合格重量\", \n");
        }

        // ======================= 动态合格率计算 =======================
        sql.append(" ROUND( \n")
                .append(" COALESCE( \n")
                .append(" COALESCE(SUM(CASE WHEN inspection_result = 'QUALIFIED' THEN ");

        // 分子处理（合格数量/重量）
        if ("0".equals(param.getCalculateDimension())) {
            sql.append("1");
        } else {
            sql.append(" output_quantity");
        }

        sql.append(" END), 0) ::::NUMERIC \n")
                .append(" / NULLIF(");

        // 分母处理（总数量/重量）
        if ("0".equals(param.getCalculateDimension())) {
            sql.append("COUNT(inspection_item_id)");
        } else {
            sql.append("COALESCE(SUM( output_quantity), 0)");
        }

        sql.append(", 0), \n")
                .append(" 0 \n")
                .append("), 4 \n")
                .append(") AS \"qualified_rate\", \n")
                .append(" 0.5 AS \"目标\" \n")
                .append("FROM ( \n")
                .append(" SELECT * \n")
                .append(" FROM ( \n")
                .append(" SELECT \n")
                .append(" itd.ID, \n")
                .append(" itd.inspection_item_id, \n")
                .append(" itd.item_name, \n")
                .append(" itd.inspection_result, \n")
                .append(" it.actual_lot_serial_id \n")
                .append(" FROM pi_inspection_task_detail itd \n")
                .append(" LEFT JOIN pi_inspection_task it ON itd.inspection_task_id = it.ID \n")
                .append(" WHERE itd.inspection_result IS NOT NULL \n")
                .append(" AND it.actual_lot_serial_id IS NOT NULL \n")
                .append(") ins \n")
                .append(" LEFT JOIN ( \n")
                .append(" SELECT \n")
                .append(" serial_number, \n")
                .append(" output_quantity \n")
                .append(" FROM batch_serial bs \n")
                .append(" LEFT JOIN ms_spcfcn fcn ON bs.material_code = fcn.spcfcn_cd \n")
                .append(" LEFT JOIN ms_spcdct dct ON fcn.spcdct_cd = dct.spcdct_cd \n")
                .append(" LEFT JOIN mo_fctry_ara fa ON bs.workshop_id = fa.ID \n")
                .append(" WHERE output_quantity > 0 \n")
                .append(" AND output_time IS NOT NULL \n")
                .append(" AND process_code = 'CP' \n");

        // 1. 产出类型条件
        if (StringUtils.isNotBlank(param.getProduceType())) {
            sql.append(" AND bs.rework_flag = '").append(param.getProduceType()).append("' \n");
        }

        // 2. 时间范围条件
        if ("1".equals(param.getIsDiyTime())) {
            TimeRangeCalculator.TimeRange range = TimeRangeCalculator.calculateTimeRange(
                    param.getIsDiyTime(),
                    param.getStartTime(),
                    param.getEndTime(),
                    param.getQuery()
            );
            sql.append(" AND output_time BETWEEN TO_TIMESTAMP('")
                    .append(range.getFormattedStart())
                    .append("', 'YYYY-MM-DD HH24:MI:SS') AND TO_TIMESTAMP('")
                    .append(range.getFormattedEnd())
                    .append("', 'YYYY-MM-DD HH24:MI:SS') \n");
        }

        // 3. 物料类型条件
        if (CollectionUtils.isNotEmpty(param.getSpcdctCodes())) {
            sql.append(" AND dct.spcdct_cd IN (");
            param.getSpcdctCodes().forEach(code -> sql.append("'").append(code).append("',"));
            sql.deleteCharAt(sql.length() - 1); // 移除最后一个逗号
            sql.append(") \n");
        }
        // 4. 物料编码条件
        else if (CollectionUtils.isNotEmpty(param.getMaterialCodes())) {
            sql.append(" AND material_code IN (");
            param.getMaterialCodes().forEach(code -> sql.append("'").append(code).append("',"));
            sql.deleteCharAt(sql.length() - 1);
            sql.append(") \n");
        }

        // 5. 组织架构条件（产线 > 车间 > 工厂）
        if (CollectionUtils.isNotEmpty(param.getLineIds())) {
            sql.append(" AND production_line_id IN (");
            param.getLineIds().forEach(id -> sql.append("'").append(id).append("',"));
            sql.deleteCharAt(sql.length() - 1);
            sql.append(") \n");
        } else if (CollectionUtils.isNotEmpty(param.getWorkshopIds())) {
            sql.append(" AND workshop_id IN (");
            param.getWorkshopIds().forEach(id -> sql.append("'").append(id).append("',"));
            sql.deleteCharAt(sql.length() - 1);
            sql.append(") \n");
        } else if (CollectionUtils.isNotEmpty(param.getFactoryIds())) {
            sql.append(" AND workshop_id IN ( \n")
                    .append(" SELECT id FROM mo_fctry_ara WHERE pid IN (");
            param.getFactoryIds().forEach(id -> sql.append("'").append(id).append("',"));
            sql.deleteCharAt(sql.length() - 1);
            sql.append(") \n) \n");
        }

        // 6. 检测项/特性条件
        if (CollectionUtils.isNotEmpty(param.getItemIds()) ||
                CollectionUtils.isNotEmpty(param.getFeatures())) {

            sql.append(" AND EXISTS ( \n")
                    .append(" SELECT 1 FROM pi_inspection_task pt \n")
                    .append(" JOIN pi_inspection_task_detail ptd ON pt.id = ptd.inspection_task_id \n")
                    .append(" WHERE pt.actual_lot_serial_id = bs.serial_number \n");

            if (CollectionUtils.isNotEmpty(param.getItemIds())) {
                sql.append(" AND ptd.item_id IN (");
                param.getItemIds().forEach(id -> sql.append("'").append(id).append("',"));
                sql.deleteCharAt(sql.length() - 1);
                sql.append(") \n");
            } else if (CollectionUtils.isNotEmpty(param.getFeatures())) {
                sql.append(" AND ptd.item_id IN ( \n")
                        .append(" SELECT bi.id FROM bd_inspection_item bi \n")
                        .append(" WHERE bi.feature IN (");
                param.getFeatures().forEach(feature -> sql.append("'").append(feature).append("',"));
                sql.deleteCharAt(sql.length() - 1);
                sql.append(") \n) \n");
            }
            sql.append(") \n");
        }

        // 关闭子查询
        sql.append(
                ") batch ON ins.actual_lot_serial_id = batch.serial_number \n" +
                        ") workshop_base_data \n" +
                        "GROUP BY inspection_item_id, item_name \n" +
                        "ORDER BY inspection_item_id ASC"
        ).append(") res ORDER BY qualified_rate");

        return sql.toString();
    }
}