package cec.jiutian.bc.productReturnInspection.domain.publicInspectionTask.handler;

import cec.jiutian.bc.productReturnInspection.domain.publicInspectionTask.mto.ProductReturnInsItemTargetMTO;
import cec.jiutian.bc.productReturnInspection.enumeration.InsItemResEnum;
import cec.jiutian.view.DependFiled;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class ProductReturnResultJudgeHandler implements DependFiled.DynamicHandler<ProductReturnInsItemTargetMTO> {

    @Override
    public Map<String, Object> handle(ProductReturnInsItemTargetMTO productReturnInsItemTargetMTO) {
        if (productReturnInsItemTargetMTO == null || productReturnInsItemTargetMTO.getResultValue() == null) {
            return Map.of();
        }
        HashMap<String, Object> res = new HashMap<>();
        String value = productReturnInsItemTargetMTO.getResultValue();
        if (StringUtils.isBlank(value)) {
            value = "0.0";
        }
        Double resultValue = Double.valueOf(value);
        Double lowerValue = productReturnInsItemTargetMTO.getLowerValue();
        Double upperValue = productReturnInsItemTargetMTO.getUpperValue();
        if (resultValue < lowerValue || resultValue > upperValue) {
            res.put("judge", InsItemResEnum.FAIL.name());
            return res;
        }else {
            res.put("judge", InsItemResEnum.PASS.name());
            return res;
        }

    }
}
