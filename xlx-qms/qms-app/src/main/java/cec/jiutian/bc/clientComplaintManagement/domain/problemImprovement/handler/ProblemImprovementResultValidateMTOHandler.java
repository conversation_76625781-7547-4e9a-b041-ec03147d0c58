package cec.jiutian.bc.clientComplaintManagement.domain.problemImprovement.handler;

import cec.jiutian.bc.clientComplaintManagement.domain.clientComplaintFeedback.enumration.ClientComplaintFeedbackStatusEnum;
import cec.jiutian.bc.clientComplaintManagement.domain.clientComplaintFeedback.model.ClientComplaintFeedback;
import cec.jiutian.bc.clientComplaintManagement.domain.problemImprovement.model.ProblemImprovement;
import cec.jiutian.bc.clientComplaintManagement.domain.problemImprovement.mto.ProblemImprovementResultValidateMTO;
import cec.jiutian.bc.clientComplaintManagement.enumeration.ProblemImprovementStatusEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/28
 * @description TODO
 */
@Component
public class ProblemImprovementResultValidateMTOHandler implements OperationHandler<ProblemImprovement, ProblemImprovementResultValidateMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<ProblemImprovement> data, ProblemImprovementResultValidateMTO modelObject, String[] param) {
        modelObject.setBusinessStatus(ProblemImprovementStatusEnum.Enum.COMPLETE.name());
        ProblemImprovement model = data.get(0);
        BeanUtil.copyProperties(modelObject, model, "createBy", "createTime");
        fabosJsonDao.mergeAndFlush(model);

        ProblemImprovement condition = new ProblemImprovement();
        condition.setClientComplaintFeedback(modelObject.getClientComplaintFeedback());
        List<ProblemImprovement> problemImprovementList = fabosJsonDao.select(condition);
        for (ProblemImprovement problemImprovement : problemImprovementList) {
            if (!problemImprovement.getBusinessStatus().equals(ProblemImprovementStatusEnum.Enum.COMPLETE.name())) {
                return "alert(操作成功)";
            }
        }
        ClientComplaintFeedback clientComplaintFeedback = fabosJsonDao.findById(ClientComplaintFeedback.class, modelObject.getClientComplaintFeedback().getId());
        clientComplaintFeedback.setBusinessStatus(ClientComplaintFeedbackStatusEnum.Enum.COMPLETED.name());
        fabosJsonDao.mergeAndFlush(clientComplaintFeedback);
        return "alert(操作成功)";
    }

    @Override
    public ProblemImprovementResultValidateMTO fabosJsonFormValue(List<ProblemImprovement> data, ProblemImprovementResultValidateMTO fabosJsonForm, String[] param) {
        ProblemImprovement model = data.get(0);
        BeanUtil.copyProperties(model, fabosJsonForm);
        return fabosJsonForm;
    }
}
