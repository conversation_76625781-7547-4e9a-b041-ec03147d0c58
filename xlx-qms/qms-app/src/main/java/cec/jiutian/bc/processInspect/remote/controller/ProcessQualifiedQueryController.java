package cec.jiutian.bc.processInspect.remote.controller;

import cec.jiutian.bc.ao.QueryAO;
import cec.jiutian.bc.dto.ChartData;
import cec.jiutian.bc.mto.ReportResponseResult;
import cec.jiutian.bc.processInspect.service.ProcessQualifiedService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.bind.annotation.*;

import java.net.URLEncoder;

@RequestMapping("/processQualified")
@RestController
public class ProcessQualifiedQueryController {

    @Resource
    private ProcessQualifiedService processQualifiedService;

    @PostMapping("/getOperationQualifiedChart")
    public ReportResponseResult<ChartData> getOperationQualifiedChart(@RequestBody QueryAO queryAO) {
        return processQualifiedService.getOperationQualifiedChart(queryAO);
    }

    @PostMapping("/getFactoryQualifiedChart")
    public ReportResponseResult<ChartData> getFactoryQualifiedChart(@RequestBody QueryAO queryAO) {
        return processQualifiedService.getFactoryQualifiedChart(queryAO);
    }

    @PostMapping("/getWorkshopQualifiedChart")
    public ReportResponseResult<ChartData> getWorkshopQualifiedChart(@RequestBody QueryAO queryAO) {
        return processQualifiedService.getWorkshopQualifiedChart(queryAO);
    }

    @PostMapping("/getProductionLineQualifiedChart")
    public ReportResponseResult<ChartData> getProductionLineQualifiedChart(@RequestBody QueryAO queryAO) {
        return processQualifiedService.getProductionLineQualifiedChart(queryAO);
    }

    @PostMapping("/getSpcdctCdQualifiedChart")
    public ReportResponseResult<ChartData> getSpcdctCdQualifiedChart(@RequestBody QueryAO queryAO) {
        return processQualifiedService.getSpcdctCdQualifiedChart(queryAO);
    }

    @PostMapping("/getMaterialCodeQualifiedChart")
    public ReportResponseResult<ChartData> getMaterialCodeQualifiedChart(@RequestBody QueryAO queryAO) {
        return processQualifiedService.getMaterialCodeQualifiedChart(queryAO);
    }

    @PostMapping("/getProcessTrendChart")
    public ReportResponseResult<ChartData> getProcessTrendChart(@RequestBody QueryAO queryAO) {
        return processQualifiedService.getProcessTrendChart(queryAO);
    }

    @PostMapping("/getProcessQualifiedTrendChart")
    public ReportResponseResult<ChartData> getProcessQualifiedTrendChart(@RequestBody QueryAO queryAO) {
        return processQualifiedService.getProcessQualifiedTrendChart(queryAO);
    }

    @PostMapping("/getMaterialCodeQualifiedParetoChart")
    public ReportResponseResult<ChartData> getMaterialCodeQualifiedParetoChart(@RequestBody QueryAO queryAO) {
        return processQualifiedService.getMaterialCodeQualifiedParetoChart(queryAO);
    }

    @PostMapping("/getInspectionQualifiedParetoChart")
    public ReportResponseResult<ChartData> getInspectionQualifiedParetoChart(@RequestBody QueryAO queryAO) {
        return processQualifiedService.getInspectionQualifiedParetoChart(queryAO);
    }

    @PostMapping("{model}/exportReport")
    public void generateCMKReport(@PathVariable String model, @RequestBody QueryAO queryAO, HttpServletResponse response) {
        try {
            String fileName = "制程报表导出文件.xlsx";
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" +
                    URLEncoder.encode(fileName, "UTF-8"));

            processQualifiedService.exportReport(queryAO, model, response);
        }catch (Exception e) {
            e.printStackTrace();
        }
    }
}
