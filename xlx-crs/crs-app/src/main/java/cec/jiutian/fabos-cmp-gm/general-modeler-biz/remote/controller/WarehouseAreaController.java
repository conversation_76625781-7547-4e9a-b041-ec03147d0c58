package cec.jiutian.bc.generalModeler.remote.controller;

import cec.jiutian.bc.generalModeler.service.WarehouseAreaService;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.core.view.fabosJson.view.FabosJsonApiModel;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class WarehouseAreaController {

    @Resource
    private FabosJsonDao FabosJsonDao;
    private WarehouseAreaService warehouseAreaService;

    /**
     * 仓库冻结
     *
     * @param warehouseId
     * @return
     */
    @PostMapping({"/warehouseArea/warehouseHold"})
    public FabosJsonApiModel warehouseHold(@RequestParam String warehouseId) {
        return warehouseAreaService.warehouseHold(warehouseId);
    }

    @PostMapping({"/warehouseArea/warehouseUnHold"})
    public FabosJsonApiModel warehouseUnHold(@RequestParam String warehouseId) {
        return warehouseAreaService.warehouseUnHold(warehouseId);
    }
}