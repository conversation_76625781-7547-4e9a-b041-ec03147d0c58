package cec.jiutian.bc.generalModeler.remote.rpc;

import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRule;
import cec.jiutian.bc.generalModeler.rpc.NamingRuleRpc;
import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.core.frame.annotation.FabosCustomizedService;
import cec.jiutian.view.FabosJsonFunction;
import cec.jiutian.view.config.Comment;
import jakarta.annotation.Resource;

import javax.ws.rs.core.MultivaluedMap;
import java.util.List;
import java.util.Map;

//@DubboService
@FabosCustomizedService(NamingRule.class)
public class NamingRuleRpcImpl implements NamingRuleRpc {
    @Resource
    NamingRuleService namingRuleService;

    @Override
    @Comment("根据命名规则生成编码，参数为：1、命名规则编码，2、需生成的个数，3、变量表")
    @FabosJsonFunction(displayName = "根据命名规则生成编码", argumentsDescription = "1、命名规则编码，2、需生成的个数，3、变量表", returnTypeDescription = "获取到的编码List")
    public List<String> getNameCode(MultivaluedMap<String, String> formParams) {
        String ruleCode = formParams.getFirst("ruleCode").toString();
        int num = Integer.parseInt(formParams.getFirst("num").toString());
        Map<String, String> variableMap = null;
        return namingRuleService.getNameCode(ruleCode, num, variableMap);
    }
}
