package cec.jiutian.bc.generalModeler.domain.measureUnit.util;

import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.Expression;

import java.util.Map;

/**
 * <AUTHOR>
 * @date ：2024/10/25 9:19
 * @description：
 */
public class CalculateUtil {

    public static Object generalExpressionCalculate(Expression expression, Map<String, Object> data) {
        return expression.execute(data);
    }

    public static Expression compile(String expression) {
        return AviatorEvaluator.compile(expression);
    }
}
