package cec.jiutian.bc.alarm.domain.exceptionManage.model;


import cec.jiutian.bc.alarm.domain.exceptionManage.handler.MonitorRuleDetailHandler;
import cec.jiutian.bc.alarm.domain.exceptionManage.handler.NoticeGroupUserHandler;
import cec.jiutian.bc.alarm.domain.exceptionManage.proxy.NoticeGroupProxy;
import cec.jiutian.bc.chartControl.domain.chartControl.model.ChartDefinition;
import cec.jiutian.bc.generalModeler.domain.warehouseArea.model.Warehouse;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.ReferenceAddType;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@FabosJson(
        name = "监控项目管理",
        orderBy = "MonitorItem.createTime desc",
        power = @Power(print = false)
)
@Table(name = "monitor_item", uniqueConstraints = {
        @UniqueConstraint(columnNames = {"monitorItemCode"})
})
@Entity
@Getter
@Setter
@TemplateType(type = "multiTable")
public class MonitorItem extends MetaModel {
    @FabosJsonField(
            views = @View(title = "监控项目编码"),
            edit = @Edit(title = "监控项目编码", notNull = true,
                    search = @Search, readonly = @Readonly(add = false, edit = true),
                    inputType = @InputType(length = 20))
    )
    private String monitorItemCode;

    @FabosJsonField(
            views = @View(title = "监控项目名称"),
            edit = @Edit(title = "监控项目名称", notNull = true,
                    search = @Search,
                    inputType = @InputType(length = 20))
    )
    private String monitorItemName;

    @FabosJsonField(
            views = @View(title = "监控规则", column = "monitorRuleName"),
            edit = @Edit(title = "监控规则",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "monitorRuleName"),
                    allowAddMultipleRows = false,
                    filter = @Filter(value = "MonitorRule.enableFlag")
            )
    )
    @ManyToOne
    private MonitorRule monitorRule;

    @FabosJsonField(
            views = @View(title = "控制图", column = "chartName"),
            edit = @Edit(title = "控制图",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "chartName"),
                    allowAddMultipleRows = false
            )
    )
    @ManyToOne
    private ChartDefinition chartDefinition;

    @FabosJsonField(
            views = @View(title = "通知组", column = "noticeGroupName"),
            edit = @Edit(title = "通知组",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "noticeGroupName"),
                    allowAddMultipleRows = false,
                    filter = @Filter(value = "NoticeGroup.enableFlag")
            )
    )
    @ManyToOne
    private NoticeGroup noticeGroup;

    @FabosJsonField(
            views = @View(title = "是否启用"),
            edit = @Edit(title = "是否启用", notNull = true,
                    search = @Search,type = EditType.BOOLEAN)
    )
    private Boolean enableFlag;
}
