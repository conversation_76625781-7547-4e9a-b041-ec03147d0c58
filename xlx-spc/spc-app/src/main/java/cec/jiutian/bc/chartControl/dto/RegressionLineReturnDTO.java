/** 
 * projectName: fabos-spc-server 
 * fileName: RegressionLineReturnDTO.java 
 * packageName: cec.jiutian.mes.spc.dto
 * date: 2020年3月12日下午4:23:26 
 * copyright(c) 2020,cecjt
 */

package cec.jiutian.bc.chartControl.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(value = "RegressionLineReturnDTO")
public class RegressionLineReturnDTO implements Serializable{

	/**   
	 * @Fields serialVersionUID : TODO(用一句话描述这个变量表示什么)   
	 */  
	
	private static final long serialVersionUID = 3178799282102771609L;
	
	/**
	 * 截距b ( y=ax+b )
	 */
	@ApiModelProperty(value = "截距b ( y=ax+b )" , required = false)
	private float intercept;
	
	/**
	 * 斜率a
	 */
	@ApiModelProperty(value = "斜率a" , required = false)
	private float coefficient;
	
	/**
	 * 误差
	 */
	@ApiModelProperty(value = "误差" , required = false)
	private double errorValue;
	
	/**
	 * X,Y坐标点
	 */
	@ApiModelProperty(value = "X,Y坐标点" , required = false)
	private List<DataPoint> DataPointList;
	
	/**
	 * X轴上报原始数据集合
	 */
	@ApiModelProperty(value = "X轴上报原始数据集合" , required = false)
	private List<EquipmentAnalysisDimension> xEquipmentAnalysisDimensionList;
	
	/**
	 * y轴上报原始数据集合
	 */
	@ApiModelProperty(value = "y轴上报原始数据集合" , required = false)
	private List<EquipmentAnalysisDimension> yEquipmentAnalysisDimensionList;
}
