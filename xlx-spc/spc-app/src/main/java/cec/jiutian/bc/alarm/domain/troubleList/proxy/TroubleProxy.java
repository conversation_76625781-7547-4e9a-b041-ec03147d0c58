package cec.jiutian.bc.alarm.domain.troubleList.proxy;

import cec.jiutian.bc.alarm.domain.troubleList.model.Trouble;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.enumeration.TroubleStateEnum;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/*
 *
 * <AUTHOR>
 * @date 2025/4/9 11:15
 */
@Service
public class TroubleProxy implements DataProxy<Trouble> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public void beforeAdd(Trouble trouble) {
        if(trouble != null) {
            if(trouble.getChartControl() == null) {
                throw new FabosJsonApiErrorTip("控制图名称不能为空, 请检查!");
            }
            if(trouble.getExceptionDetail() == null) {
                throw new FabosJsonApiErrorTip("异常详情不能为空, 请检查!");
            }
            if(trouble.getExceptionType() == null) {
                throw new FabosJsonApiErrorTip("异常类型不能为空, 请检查!");
            }
            if(trouble.getExceptionDate() == null) {
                throw new FabosJsonApiErrorTip("异常时间不能为空, 请检查!");
            }
            if(trouble.getDescription() == null) {
                throw new FabosJsonApiErrorTip("问题描述不能为空, 请检查!");
            }
            trouble.setCurrentState(TroubleStateEnum.Enum.WAIT_PUBLISH.name());
        }
    }

    @Override
    public void beforeUpdate(Trouble trouble) {
        if(trouble != null) {
            if(trouble.getChartControl() == null) {
                throw new FabosJsonApiErrorTip("控制图名称不能为空, 请检查!");
            }
            if(trouble.getExceptionDetail() == null) {
                throw new FabosJsonApiErrorTip("异常详情不能为空, 请检查!");
            }
            if(trouble.getExceptionType() == null) {
                throw new FabosJsonApiErrorTip("异常类型不能为空, 请检查!");
            }
            if(trouble.getExceptionDate() == null) {
                throw new FabosJsonApiErrorTip("异常时间不能为空, 请检查!");
            }
            if(trouble.getDescription() == null) {
                throw new FabosJsonApiErrorTip("问题描述不能为空, 请检查!");
            }
            trouble.setCurrentState(TroubleStateEnum.Enum.WAIT_PUBLISH.name());
        }
    }
}
