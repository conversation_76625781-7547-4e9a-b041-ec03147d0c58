package cec.jiutian.bc.remote.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年05月17日 14:27
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OocNumWholeResp {

    List<OocNumWhole> oocNumWholeList;

    //生成的时间序列
    List<String> sequence;
}
