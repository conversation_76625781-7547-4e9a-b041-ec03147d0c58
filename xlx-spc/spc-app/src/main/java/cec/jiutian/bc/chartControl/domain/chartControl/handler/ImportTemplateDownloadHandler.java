package cec.jiutian.bc.chartControl.domain.chartControl.handler;


import cec.jiutian.bc.chartControl.domain.chartControl.model.CacheData;
import cec.jiutian.util.common.ExcelTemplateGenerator;
import cec.jiutian.view.fun.OperationHandler;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.util.List;

@Component
public class ImportTemplateDownloadHandler implements OperationHandler<CacheData, Void> {

    @Override
    public DownloadableFile fileOperator(List<CacheData> cacheDataList, String[] param) {
        ByteArrayOutputStream byteArrayOutputStream = null;
        try {
            byteArrayOutputStream = ExcelTemplateGenerator.generateTemplateStream(CacheData.class);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return new DownloadableFile(byteArrayOutputStream,"分析型控制图导入模板.xlsx");
    }
}
