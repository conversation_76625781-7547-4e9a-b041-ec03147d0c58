package cec.jiutian.bc.mto;

import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.View;
import cec.jiutian.view.type.Power;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025年06月24日 16:57
 */
@FabosJson(
        name = "首页设置",
        orderBy = "HomePageSetting.createTime desc",
        power = @Power(importable = false, export = false, add = false, print = false, edit = false, delete = false)
)
@Table(name = "home_page_setting",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"id"})
        }
)
@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TemplateType(type = "usual")
public class HomePageSetting extends MetaModel {

    @FabosJsonField(
            views = @View(title = "行数", show = true),
            edit = @Edit(title = "行数", show = true)
    )
    private Integer rowCount;

    @FabosJsonField(
            views = @View(title = "列数", show = true),
            edit = @Edit(title = "列数", show = true)
    )
    private Integer columnCount;

    @FabosJsonField(
            views = @View(title = "总数", show = true),
            edit = @Edit(title = "总数", show = true)
    )
    private Integer totalCount;
}
