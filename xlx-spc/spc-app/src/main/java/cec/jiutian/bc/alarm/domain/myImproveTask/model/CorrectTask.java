package cec.jiutian.bc.alarm.domain.myImproveTask.model;

import cec.jiutian.bc.alarm.domain.myImproveTask.proxy.CorrectTaskProxy;
import cec.jiutian.bc.mto.OrgMTO;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.frame.module.MetaUser;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.enumeration.FinishFlagEnum;
import cec.jiutian.enumeration.VerifyResultEnum;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.ReferenceTreeType;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.AttachmentType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DateType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowBaseOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Filters;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025年05月28日 16:57
 */
@FabosJson(
        name = "整改任务",
        orderBy = "CorrectTask.createTime desc",
        power = @Power(importable = false, export = false, add = false, print = false, edit = false, delete = false),
        dataProxy = CorrectTaskProxy.class,
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "add",
                        ifExpr = "1 == 1",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.HIDE
                ),
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "1 == 1",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.HIDE
                ),
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "1 == 1",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.HIDE
                )
        }
)
@Table(name = "correct_task",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"id"})
        }
)
@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TemplateType(type = "usual")
public class CorrectTask extends MetaModel {

    @FabosJsonField(
            views = @View(title = "整改措施id", show = false),
            edit = @Edit(title = "整改措施id", show = false)
    )
    private String improveMethodId;

    @FabosJsonField(
            views = @View(title = "整改措施"),
            edit = @Edit(title = "整改措施", notNull = true, search = @Search(vague = true), inputType = @InputType(length = 80))
    )
    private String improveMethodName;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "责任部门", column = "name", type = ViewType.TABLE_FORM),
            edit = @Edit(title = "责任部门", type = EditType.REFERENCE_TABLE, allowAddMultipleRows = false,
                    referenceTreeType = @ReferenceTreeType(pid = "parentOrg.id"), search = @Search(), placeHolder = "请输入", notNull = true)
    )
    private OrgMTO departmentResponsibleFor;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "责任人", column = "name", type = ViewType.TABLE_FORM),
            edit = @Edit(title = "责任人",
                    filter = @Filter(value = "MetaUser.state = 'Y'"),
                    notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"/*, type = ReferenceTableType.SelectShowTypeMTO.DROPDOWN_TABLE*/))
    )
    @Filters( {
            @org.hibernate.annotations.Filter(name="demoFilterCorrectTask", condition="person_responsible_for_id=:user_id"),
    } )
    private MetaUser personResponsibleFor;

    @FabosJsonField(
            views = @View(title = "责任人id", show = false),
            edit = @Edit(title = "责任人id", show = false)
    )
    private String responsiblePersonId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "纳期", type =  ViewType.DATE_TIME),
            edit = @Edit(title = "纳期", notNull = true, search = @Search(vague = true),
                    type = EditType.DATE,
                    readonly = @Readonly(add = false, edit = false),
                    dateType = @DateType(type = DateType.Type.DATE_TIME))
    )
    private Date acceptDate;

    @FabosJsonField(
            views = @View(title = "是否完成"),
            edit = @Edit(title = "是否完成",show = false,search = @Search(),type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = FinishFlagEnum.class))
    )
    private String finishFlag;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "完成日期", type =  ViewType.DATE_TIME),
            edit = @Edit(title = "完成日期", notNull = true, search = @Search(vague = true),
                    type = EditType.DATE,
                    readonly = @Readonly(add = false, edit = false),
                    dateType = @DateType(type = DateType.Type.DATE_TIME))
    )
    private Date finishDate;

    @FabosJsonField(
            views = @View(title = "完成佐证材料"),
            edit = @Edit(title = "完成佐证材料", type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType)
    )
    private String finishAttachment;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "验证人", column = "name", type = ViewType.TABLE_FORM),
            edit = @Edit(title = "验证人",
                    filter = @Filter(value = "MetaUser.state = 'Y'"),
                    notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"/*, type = ReferenceTableType.SelectShowTypeMTO.DROPDOWN_TABLE*/))
    )
    private MetaUser verifyPerson;

    @FabosJsonField(
            views = @View(title = "验证结果"),
            edit = @Edit(title = "验证结果",show = false,search = @Search(),type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = VerifyResultEnum.class))
    )
    private String verifyResult;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "验证日期", type =  ViewType.DATE_TIME),
            edit = @Edit(title = "验证日期", notNull = true, search = @Search(vague = true),
                    type = EditType.DATE,
                    readonly = @Readonly(add = false, edit = false),
                    dateType = @DateType(type = DateType.Type.DATE_TIME))
    )
    private Date verifyDate;

    @FabosJsonField(
            views = @View(title = "验证佐证材料"),
            edit = @Edit(title = "验证佐证材料", type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType)
    )
    private String verifyAttachment;

    @FabosJsonField(
            views = {
                    @View(title = "我的整改任务", column = "exceptionDetail")
            },
            edit = @Edit(title = "我的整改任务", type = EditType.REFERENCE_TABLE, show = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "exceptionDetail"))
    )
    @ManyToOne
    @JsonIgnoreProperties("correctTaskList")
    private MyCorrectTask myCorrectTask;
}
