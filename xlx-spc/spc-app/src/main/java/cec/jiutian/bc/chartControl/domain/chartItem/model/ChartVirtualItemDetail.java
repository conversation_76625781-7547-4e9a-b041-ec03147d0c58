package cec.jiutian.bc.chartControl.domain.chartItem.model;

import cec.jiutian.bc.chartControl.domain.chartItem.proxy.ChartVirtualItemDetailProxy;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import lombok.Getter;
import lombok.Setter;

/**
 * 图表虚拟参数详情
 */
@FabosJson(
        name = "图表虚拟参数详情",
        orderBy = "ChartVirtualItemDetail.updateTime desc",
        dataProxy = ChartVirtualItemDetailProxy.class
)
@Entity
@Getter
@Setter
@TemplateType(type = "multiTable")
public class ChartVirtualItemDetail extends MetaModel {

    private static final long serialVersionUID = 1L;

    /**
     * 图表项名称
     */
    @Id
    @Column(name = "CHRT_ITM_NM")
    @View(title = "图表项名称")
    @Edit(title = "图表项名称", type = EditType.input_text, placeHolder = "请输入")
    private String chartItemName;

    /**
     * 公式中参数名(a,b,c)
     */
    @Id
    @Column(name = "FRML_PRMTR_NM")
    @View(title = "公式参数名")
    @Edit(title = "公式参数名", type = EditType.input_text, placeHolder = "请输入")
    private String formulaParameterName;

    /**
     * 公式中参数对应的图表项名
     */
    @View(title = "公式图表项名")
    @Edit(title = "公式图表项名", type = EditType.input_text, placeHolder = "请输入")
    private String formulaChartItemName;

    /**
     * 是否可用
     */
    @View(title = "是否可用")
    @Edit(title = "是否可用", type = EditType.input_text, placeHolder = "请输入")
    private String validFlag;

}