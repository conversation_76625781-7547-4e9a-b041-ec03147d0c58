package cec.jiutian.bc.chartControl.domain.chartControl.service;

import cec.jiutian.bc.chartControl.domain.chartControl.model.AnalyticalChartDefinition;
import cec.jiutian.bc.chartControl.domain.chartControl.model.CacheData;
import cec.jiutian.bc.chartControl.enumeration.AnalyticalChartOperateStateEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@Service
@Transactional
public class AnalyticalChartDefinitionService {
    private final FabosJsonDao fabosJsonDao;

    public AnalyticalChartDefinitionService(FabosJsonDao fabosJsonDao) {
        this.fabosJsonDao = fabosJsonDao;
    }

    public void importCacheData(List<CacheData> cacheList,String id) {
        if (CollectionUtils.isNotEmpty(cacheList)) {
            AnalyticalChartDefinition analyticalChartDefinition = fabosJsonDao.findById(AnalyticalChartDefinition.class, id);
            for (CacheData cacheData : cacheList) {
                cacheData.setAnalyticalChartDefinition(analyticalChartDefinition);
            }
            analyticalChartDefinition.setOperateState(AnalyticalChartOperateStateEnum.Enum.Analyzing.name());
            fabosJsonDao.update(analyticalChartDefinition);
            fabosJsonDao.insertBatch(cacheList);
        }
    }
}
