package cec.jiutian.bc.alarm.domain.troubleList.handler;

import cec.jiutian.bc.alarm.domain.myImproveTask.model.CorrectTask;
import cec.jiutian.bc.alarm.domain.myImproveTask.model.MyCorrectTask;
import cec.jiutian.bc.alarm.domain.troubleList.model.Trouble;
import cec.jiutian.bc.ecs.enums.MessageWayEnum;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.common.util.ListUtils;
import cec.jiutian.common.util.StringUtils;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.core.frame.module.MetaUser;
import cec.jiutian.enumeration.FinishFlagEnum;
import cec.jiutian.enumeration.TroubleStateEnum;
import cec.jiutian.enumeration.VerifyResultEnum;
import cec.jiutian.util.message.SendGroupOrPersonalMessageUtil;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class TroubleVerifySubmitHandler implements OperationHandler<Trouble, Void> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private SendGroupOrPersonalMessageUtil sendGroupOrPersonalMessageUtil;

    @Override
    public String exec(List<Trouble> data, Void modelObject, String[] param) {
        if(!CollectionUtils.isEmpty(data)) {
            Trouble trouble = data.get(0);
            if(!TroubleStateEnum.Enum.VERIFIED.name().equals(trouble.getCurrentState())) {
                throw new FabosJsonApiErrorTip("单据不是待验证状态, 不能进行验证提交操作, 请检查!");
            }
            if(CollectionUtils.isEmpty(trouble.getImproveMethodList())) {
                throw new FabosJsonApiErrorTip("单据未添加任何整改措施, 不能进行验证提交操作, 请检查!");
            }
            for(int i = 0; i < trouble.getImproveMethodList().size(); i++) {
                if(StringUtils.isEmpty(trouble.getImproveMethodList().get(i).getVerifyResult())) {
                    throw new FabosJsonApiErrorTip(String.format("单据的第%d条整改措施未录入验证结果, 不能进行验证提交操作, 请检查!", i + 1));
                }
                if(trouble.getImproveMethodList().get(i).getVerifyDate() == null) {
                    throw new FabosJsonApiErrorTip(String.format("单据的第%d条整改措施未录入验证日期, 不能进行验证提交操作, 请检查!", i + 1));
                }
            }
            if(trouble.getImproveMethodList().stream().filter(item -> VerifyResultEnum.Enum.NOT_PASS.name().equals(item.getVerifyResult())).count() > 0) {
                trouble.setCurrentState(TroubleStateEnum.Enum.RUNNING.name());
                MyCorrectTask myCorrectTask = fabosJsonDao.selectOne(MyCorrectTask.builder().troubleId(trouble.getId()).build());
                if(myCorrectTask != null) {
                    myCorrectTask.setCurrentState(TroubleStateEnum.Enum.WAIT_RUN.name());
                    fabosJsonDao.mergeAndFlush(myCorrectTask);
                }
            } else {
                trouble.setCurrentState(TroubleStateEnum.Enum.FINISH.name());
                MyCorrectTask myCorrectTask = fabosJsonDao.selectOne(MyCorrectTask.builder().troubleId(trouble.getId()).build());
                if(myCorrectTask != null) {
                    myCorrectTask.setCurrentState(TroubleStateEnum.Enum.FINISH.name());
                    fabosJsonDao.mergeAndFlush(myCorrectTask);
                }
            }
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            MetaUser verifyPerson = new MetaUser(UserContext.get());
            ListUtils.forEach(trouble.getImproveMethodList(), item -> {
                item.setVerifyPerson(verifyPerson);
                if(VerifyResultEnum.Enum.PASS.name().equals(item.getVerifyResult())) {
                    item.setCurrentState(TroubleStateEnum.Enum.FINISH.name());
                    CorrectTask correctTask = fabosJsonDao.selectOne(CorrectTask.builder().improveMethodId(item.getId()).build());
                    if(correctTask != null) {
                        correctTask.setVerifyAttachment(item.getVerifyAttachment());
                        correctTask.setFinishAttachment(item.getFinishAttachment());
                        correctTask.setFinishFlag(FinishFlagEnum.Enum.FINISHED.name());
                        correctTask.setFinishDate(item.getFinishDate());
                        correctTask.setVerifyPerson(verifyPerson);
                        correctTask.setVerifyResult(VerifyResultEnum.Enum.PASS.name());
                        correctTask.setVerifyDate(new Date());
                        fabosJsonDao.mergeAndFlush(correctTask);
                    }
                } else {
                    item.setCurrentState(TroubleStateEnum.Enum.WAIT_RUN.name());
                    CorrectTask correctTask = fabosJsonDao.selectOne(CorrectTask.builder().improveMethodId(item.getId()).build());
                    if(correctTask != null) {
                        correctTask.setVerifyAttachment(item.getVerifyAttachment());
                        correctTask.setFinishAttachment(item.getFinishAttachment());
                        correctTask.setFinishFlag(FinishFlagEnum.Enum.NOT_FINISHED.name());
                        correctTask.setFinishDate(null);
                        correctTask.setVerifyPerson(verifyPerson);
                        correctTask.setVerifyResult(VerifyResultEnum.Enum.NOT_PASS.name());
                        correctTask.setVerifyDate(new Date());
                        fabosJsonDao.mergeAndFlush(correctTask);
                        if(correctTask.getPersonResponsibleFor() != null && StringUtils.isNotBlank(correctTask.getPersonResponsibleFor().getPhoneNumber())) {
                            try {
                                sendGroupOrPersonalMessageUtil.sendPersonMessage("您有一条整改任务的整改措施验证未通过, 请即时处理",
                                        String.format("整改措施: %s, 纳期: %s", item.getImproveMethodName(), sdf.format(item.getAcceptDate())), null,
                                        MessageWayEnum.App, correctTask.getPersonResponsibleFor().getPhoneNumber()
                                );
                                sendGroupOrPersonalMessageUtil.sendPersonMessage("您有一条整改任务的整改措施验证未通过, 请即时处理",
                                        String.format("整改措施: %s, 纳期: %s", item.getImproveMethodName(), sdf.format(item.getAcceptDate())), null,
                                        MessageWayEnum.WeChat, correctTask.getPersonResponsibleFor().getPhoneNumber()
                                );
                            } catch (Exception e) {
                                log.error("发送消息失败: {}", e.getMessage());
                            }
                        }
                    }
                }
            });
            fabosJsonDao.mergeAndFlush(trouble);
        }
        return "alert(操作成功)";
    }
}
