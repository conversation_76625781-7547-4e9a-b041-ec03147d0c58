package cec.jiutian.bc.capability.domain.calculate.model;

import cec.jiutian.bc.capability.domain.calculate.proxy.CalculateCycleProxy;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.DateType;
import cec.jiutian.view.field.edit.InputType;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Data;

import java.sql.Timestamp;

/**
 * 计算周期实体类
 * 该类用于表示计算周期的相关信息，包括更新频率、更新次数、下次更新时间、上次更新时间等。
 */
@FabosJson(
        name = "计算周期",
        orderBy = "CalculateCycle.updateTime desc",
        dataProxy = CalculateCycleProxy.class
)

@Table(
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"calculateDataType", "calculateTerm"})
        }
)
@Entity
@Data
@TemplateType(type = "multiTable")
public class CalculateCycle extends MetaModel{
    /**
     * 计算类型
     */
    @FabosJsonField(
            views = @View(title = "计算类型"),
            edit = @Edit(title = "计算类型", type = EditType.input_text, placeHolder = "请输入")
    )
    private String calculateDataType;

    /**
     * 时间戳格式的计算序列号
     */
    @FabosJsonField(
            views = @View(title = "计算周期"),
            edit = @Edit(title = "计算周期", type = EditType.input_text, placeHolder = "请输入")
    )
    private String calculateTerm;

    /**
     * 更新频率
     */
    @FabosJsonField(
            views = @View(title = "更新频率"),
            edit = @Edit(title = "更新频率", type = EditType.input_text, placeHolder = "请输入")
    )
    private String updateFrequency;

    /**
     * 更新次数
     */
    @FabosJsonField(
            views = @View(title = "更新次数"),
            edit = @Edit(title = "更新次数", type = EditType.input_text, placeHolder = "请输入")
    )
    private String cycleCount;

    /**
     * 下次更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "下次更新时间"),
            edit = @Edit(title = "下次更新时间", show = true, type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE_TIME),
                    inputType = @InputType(length = 40))
    )
    private Timestamp nextUpdateTime;

    /**
     * 上次更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "上次更新时间"),
            edit = @Edit(title = "上次更新时间", show = true, type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE_TIME),
                    inputType = @InputType(length = 40))
    )
    private Timestamp lastUpdateTime;

}