package cec.jiutian.bc.remote.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025年05月17日 14:27
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OocNumWholeForProductionLine {

    //id
    private String id;

    //父级id
    private String parentFactoryId;

    //产线
    private String productionLineName;

    //正常运行数量
    private Integer normalRunningNum;

    //未运行数量
    private Integer notRunningNum;

    //停止数量
    private Integer stopNum;

    //总数
    private Integer totalNum;
}
