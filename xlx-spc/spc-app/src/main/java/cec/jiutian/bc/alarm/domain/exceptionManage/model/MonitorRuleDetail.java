package cec.jiutian.bc.alarm.domain.exceptionManage.model;

import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ReferenceTableType;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Table(name = "monitor_rule_detail")
@FabosJson(
        name = "通知规则",
        orderBy = "MonitorRuleDetail.createTime desc"
)
@Entity
@Getter
@Setter
public class MonitorRuleDetail extends MetaModel {
    @FabosJsonField(
            views = @View(title = "通知规则详情"),
            edit = @Edit(title = "通知规则详情", readonly = @Readonly)
    )
    private String noticeRule;

    private String ruleId;

    @FabosJsonField(
            views = {
                    @View(title = "监控规则名称", column = "monitorRuleName")
            },
            edit = @Edit(title = "监控规则名称", type = EditType.REFERENCE_TABLE, show = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "monitorRuleName"))
    )
    @ManyToOne
    @JsonIgnoreProperties("monitorRuleDetailList")
    private MonitorRule monitorRule;
}
