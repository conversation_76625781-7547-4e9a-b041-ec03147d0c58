package cec.jiutian.bc.chartControl.domain.chartControl.model;


import cec.jiutian.bc.baseData.domain.user.domain.UserView;
import cec.jiutian.bc.chartControl.domain.chartControl.handler.ChartDefinitionStopOprHandler;
import cec.jiutian.bc.chartControl.enumeration.ReasonCategoryEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.CreatedDate;

import java.time.LocalDateTime;

@FabosJson(
        name = "变更履历",
        orderBy = "ChangeHistory.createTime desc",
        power = @Power(export = false,delete = false, add = false,edit = false),
        rowOperation = {
                @RowOperation(
                        title = "查看控制图",
                        code = "ChangeHistory@VIEW",
                        operationHandler = ChartDefinitionStopOprHandler.class,
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        popupType = RowOperation.PopupType.FORM,
                        submitMethod = RowOperation.SubmitMethod.NONE,
                        menuButtonType = RowOperation.MenuButtonTypeEnum.MIX,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "ChangeHistory@VIEW"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE
                )
        }
)
@Table(
        name = "change_history"
)
@Entity
@Getter
@Setter
public class ChangeHistory extends MetaModel {


    @FabosJsonField(
            views = {
                    @View(title = "分析控制图id", column = "chartCode")
            },
            edit = @Edit(title = "分析控制图id", type = EditType.REFERENCE_TABLE, show = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "chartCode"))
    )
    @ManyToOne
    @JoinColumn(name = "control_chart_id")
    private AnalyticalChartDefinition analyticalChartDefinition;

    /**
     * 变更时间
     */
    @FabosJsonField(
            views = @View(title = "变更时间"),
            edit = @Edit(title = "变更时间")
    )
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "Asia/Shanghai"
    )
    @CreatedDate
    private LocalDateTime changeTime;


    @ManyToOne(fetch = FetchType.EAGER)
    @FabosJsonField(
            views = @View(title = "变更人", column = "name"),
            edit = @Edit(title = "变更人",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    allowAddMultipleRows = false
            )
    )
    private UserView changeUser;

    @FabosJsonField(
            views = @View(title = "原因分类"),
            edit = @Edit(title = "原因分类", type = EditType.CHOICE,
                    notNull = true,
                    search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = ReasonCategoryEnum.class)
            )
    )
    private String reasonCategory;

    @FabosJsonField(
            views = @View(title = "原因"),
            edit = @Edit(title = "原因")
    )
    private String reason;

    @FabosJsonField(
            views = @View(title = "次数"),
            edit = @Edit(title = "次数")
    )
    private Double number;

    /**
     * 上控制限
     */
    @FabosJsonField(
            views = @View(title = "上控制限"),
            edit = @Edit(title = "上控制限", type = EditType.NUMBER, placeHolder = "请输入数字")
    )
    private Double upperControlLimit;

    /**
     * 下控制限
     */
    @FabosJsonField(
            views = @View(title = "下控制限"),
            edit = @Edit(title = "下控制限", type = EditType.NUMBER, placeHolder = "请输入数字")
    )
    private Double lowerControlLimit;

    /**
     * 内控线下限
     */
    @FabosJsonField(
            views = @View(title = "内控线下限"),
            edit = @Edit(title = "内控线下限", type = EditType.NUMBER, placeHolder = "请输入数字")
    )
    private Double controlLineLower;

    /**
     * 内控线上限
     */
    @FabosJsonField(
            views = @View(title = "内控线上限"),
            edit = @Edit(title = "内控线上限", type = EditType.NUMBER, placeHolder = "请输入数字")
    )
    private Double controlLineUpper;


    /**
     * 规格上限
     */
    @FabosJsonField(
            views = @View(title = "规格上限"),
            edit = @Edit(title = "规格上限", notNull = true, type = EditType.NUMBER, placeHolder = "请输入数字")
    )
    private Double upperSpecLimit;

    /**
     * 规格下限
     */
    @FabosJsonField(
            views = @View(title = "规格下限"),
            edit = @Edit(title = "规格下限", notNull = true, type = EditType.NUMBER, placeHolder = "请输入数字")
    )
    private Double lowerSpecLimit;


    @FabosJsonField(
            views = @View(title = "Cpk目标"),
            edit = @Edit(title = "Cpk目标", type = EditType.NUMBER, placeHolder = "请输入数字")
    )
    private Double cpkTarget;

    @FabosJsonField(
            views = @View(title = "COV目标"),
            edit = @Edit(title = "COV目标", type = EditType.NUMBER, placeHolder = "请输入数字")
    )
    private Double covTarget;

    @FabosJsonField(
            views = @View(title = "Mean偏移目标"),
            edit = @Edit(title = "Mean偏移目标", type = EditType.NUMBER, placeHolder = "请输入数字")
    )
    private Double meanShiftTarget;
}