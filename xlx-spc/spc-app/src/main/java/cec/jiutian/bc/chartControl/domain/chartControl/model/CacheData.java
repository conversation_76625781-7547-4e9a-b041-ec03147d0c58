package cec.jiutian.bc.chartControl.domain.chartControl.model;

import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.excel.annotation.Excel;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.type.Power;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@FabosJson(
        name = "缓存数据",
        orderBy = "CacheData.createTime desc",
        power = @Power(export = false,delete = false, add = false,edit = false)
)
@Table(
        name = "cache_data"
)
@Entity
@Getter
@Setter
public class CacheData extends MetaModel {


    @FabosJsonField(
            views = {
                    @View(title = "分析控制图id", column = "chartCode")
            },
            edit = @Edit(title = "分析控制图id", type = EditType.REFERENCE_TABLE, show = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "chartCode"))
    )
    @ManyToOne
    @JoinColumn(name = "control_chart_id")
    private AnalyticalChartDefinition analyticalChartDefinition;

    /**
     * 子组序号
     */
    @FabosJsonField(
            views = @View(title = "子组序号"),
            edit = @Edit(title = "子组序号")
    )
    @Excel(name = "子组序号")
    private String subgroupNumber;


    /**
     * 组内序号
     */
    @FabosJsonField(
            views = @View(title = "组内序号"),
            edit = @Edit(title = "组内序号")
    )
    @Excel(name = "组内序号")
    private String groupNumber;

    /**
     * 时间
     */
    @FabosJsonField(
            views = @View(title = "时间"),
            edit = @Edit(title = "时间")
    )
    @Excel(name = "时间")
    private String time;

    @FabosJsonField(
            views = @View(title = "批次"),
            edit = @Edit(title = "批次")
    )
    @Excel(name = "批次")
    private String lotNumber;

    @FabosJsonField(
            views = @View(title = "数据值"),
            edit = @Edit(title = "数据值")
    )
    @Excel(name = "数据值")
    private Double dataValue;
}
