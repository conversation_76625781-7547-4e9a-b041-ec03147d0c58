package cec.jiutian.util.capability;

import cec.jiutian.bc.capability.domain.calculate.model.CapabilityData;
import cec.jiutian.bc.spc.engine.capability.CapabilityBig;
import cec.jiutian.bc.spc.engine.controllimit.impl.factor.FactorHolder;
import cec.jiutian.bc.spc.engine.controllimit.impl.factor.FactorType;
import cec.jiutian.bc.spc.engine.model.SpecBig;
import cec.jiutian.bc.spc.engine.model.SpecLimitType;
import cec.jiutian.util.common.CommonUtil;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
/**
 * CapabilityCalculateUtil.java
 *
 * <p>该类用于计算统计过程控制（SPC）中的各种能力指数，包括CP、CPK、PP、PPK等。
 * 它根据不同的规格类型（双边、单边上限、单边下限）和提供的统计参数，计算相应的
 * 能力指数，并返回一个包含这些指数的CapabilityBig对象。</p>
 *
 * <p>主要功能包括：</p>
 * <ul>
 *     <li>根据类型（"R", "S", "T"）和规格计算标准差（cpSigma）。</li>
 *      "R"：基于极差（Range）计算标准差。
 *      "S"：基于标准差（Standard Deviation）直接计算。
 *      "T"：综合调整标准差，结合标准差、极差和平均值进行更复杂的计算。
 *     <li>计算双边规格限下的能力指数（doBothCalculate）。</li>
 *     <li>计算单边上限规格限下的能力指数（doUpperCalculate）。</li>
 *     <li>计算单边下限规格限下的能力指数（doLowerCalculate）。</li>
 *     <li>创建并返回包含计算结果的CapabilityData对象（CreateCapabilityData）。</li>
 * </ul>
 *
 * <p>该类依赖于FactorHolder来获取特定子组大小和因子类型对应的因子值，这些因子
 * 用于标准差的计算。</p>
 *
 * <p>所有计算结果都使用BigDecimal进行高精度计算，以确保准确性。</p>
 *
 */
@Component
public class CapabilityCalculateUtil {
    // 因子持有器，用于获取计算所需的因子值
    protected FactorHolder factorHolder;

    // 常量定义
    private static final BigDecimal T = BigDecimal.valueOf(3L);  // 3倍标准差
    private static final BigDecimal T2 = BigDecimal.valueOf(6L); // 6倍标准差
    private static final BigDecimal TWO = BigDecimal.valueOf(2L); // 2倍
    private static final int decimalScale = 10; // 小数精度
    private static Log log = LogFactory.getLog(CapabilityCalculateUtil.class);

    @Autowired
    public void setFactorHolder(FactorHolder factorHolder) {
        this.factorHolder = factorHolder;
    }
    /**
     * 根据不同的类型和规格计算能力指数。
     * 支持 "R", "S", "T" 三种类型的计算，并根据规格限类型（双边、单边上限、单边下限）调用对应的方法。
     * R:  基于极差（Range）计算标准差。常用于控制图（如 Xbar-R 图）中快速估算
     * S：样本标准差，
     * T:总体标准差
     *
     * @param type 类型标识，如 "R", "S", "T"
     * @param spec 规格对象
     * @param subGroupSize 子组大小
     * @param dataCount 数据点数量
     * @param siteTotalCount 站点总数
     * @param sumOfSum 数据总和
     * @param sumOfSquareSum 平方和
     * @param xbarAvg 平均值
     * @param sAvg 标准差
     * @param rAvg 极差
     * @param sumAvg 总平均值
     * @return 计算得到的能力指数对象
     */
    public CapabilityBig calculate(String type, SpecBig spec, int subGroupSize, int dataCount, int siteTotalCount, BigDecimal sumOfSum, BigDecimal sumOfSquareSum, BigDecimal xbarAvg, BigDecimal sAvg, BigDecimal rAvg, BigDecimal sumAvg) {
        if (dataCount <= 1) {
            return CapabilityBig.newDefaultCapability();
        }

        CapabilityBig capability = null;
        BigDecimal cpSigma = null;

        if (type.equalsIgnoreCase("R")) {
            int size = subGroupSize;
            if (subGroupSize == 1) {
                size = subGroupSize + 1;
            }
            if (rAvg != null)
                cpSigma = getCPSigmaR(size, rAvg);
        } else if (type.equalsIgnoreCase("S")) {
            if (sAvg != null)
                cpSigma = getCPSigma(subGroupSize, dataCount, siteTotalCount, sAvg);
        } else if (type.equalsIgnoreCase("T")) {

            if ((sAvg != null) && (rAvg != null) && (xbarAvg != null)) {
                sAvg = getSAvg(sAvg, rAvg, xbarAvg);
                cpSigma = getCPSigma(subGroupSize, dataCount, siteTotalCount, sAvg);
            }
        }

        if (spec.getSpecLimitType() == SpecLimitType.Both) {
            capability = doBothCalculate(cpSigma, spec, subGroupSize, siteTotalCount, sumOfSum, sumOfSquareSum, xbarAvg, sAvg, sumAvg);
        } else if (spec.getSpecLimitType() == SpecLimitType.UpperOnly) {
            capability = doUpperCalculate(cpSigma, spec, subGroupSize, siteTotalCount, sumOfSum, sumOfSquareSum, xbarAvg, sAvg, sumAvg);
        } else if (spec.getSpecLimitType() == SpecLimitType.LowerOnly) {
            capability = doLowerCalculate(cpSigma, spec, subGroupSize, siteTotalCount, sumOfSum, sumOfSquareSum, xbarAvg, sAvg, sumAvg);
        } else {
            capability = CapabilityBig.newDefaultCapability();
        }

        return capability;
    }
    /**
     * 计算双边规格限下的能力指数。
     * 包括 CP、CPK、PP、PPK 等指数的计算。
     *
     * @param sigma 标准差
     * @param spec 规格对象
     * @param subGroupSize 子组大小
     * @param siteTotalCount 站点总数
     * @param sumOfSum 数据总和
     * @param sumOfSquareSum 平方和
     * @param xbarAvg 平均值
     * @param sAvg 标准差
     * @param sumAvg 总平均值
     * @return 计算得到的能力指数对象
     */
    public CapabilityBig doBothCalculate(BigDecimal sigma, SpecBig spec, int subGroupSize, int siteTotalCount, BigDecimal sumOfSum, BigDecimal sumOfSquareSum, BigDecimal xbarAvg, BigDecimal sAvg, BigDecimal sumAvg) {


        BigDecimal usl = spec.getUpperSpec();
        BigDecimal target = spec.getTarget();
        BigDecimal lsl = spec.getLowerSpec();

        BigDecimal cp = null;
        BigDecimal cpk = null;
        BigDecimal cpSigma = sigma;

        BigDecimal tau = null;
        BigDecimal cpm = null;

        BigDecimal pp = null;
        BigDecimal ppk = null;
        BigDecimal ppSigma = null;

        if ((usl != null) && (lsl != null) && (target == null)) {
            target = usl.add(lsl).divide(TWO, decimalScale, 4);
        }

        if ((sumOfSum != null) && (sumOfSquareSum != null)) {
            ppSigma = getPPSigma(siteTotalCount, sumOfSum, sumOfSquareSum);
        }

        if (cpSigma != null) {
            cp = usl.subtract(lsl).divide(cpSigma.multiply(T2), decimalScale, 4);
            cpk = usl.subtract(xbarAvg).divide(cpSigma.multiply(T), decimalScale, 4).min(xbarAvg.subtract(lsl).divide(cpSigma.multiply(T), decimalScale, 4));
        }
        if ((target != null) && (cpSigma != null) && (xbarAvg != null))
            tau = getTau(target, cpSigma, xbarAvg);
        if ((usl != null) && (lsl != null) && (tau != null) &&
                (tau != BigDecimal.ZERO)) {
            cpm = usl.subtract(lsl).divide(tau.multiply(T2), decimalScale, 4);
        }
        if (ppSigma != null) {
            pp = usl.subtract(lsl).divide(ppSigma.multiply(T2), decimalScale, 4);
            BigDecimal A = usl.subtract(sumAvg.divide(BigDecimal.valueOf(subGroupSize), decimalScale, 4)).divide(ppSigma.multiply(T), decimalScale, 4);
            BigDecimal B = sumAvg.divide(BigDecimal.valueOf(subGroupSize), decimalScale, 4).subtract(lsl).divide(ppSigma.multiply(T), decimalScale, 4);
            ppk = A.min(B);
        } else if ((ppSigma == null) && (cpSigma != null)) {
            ppSigma = cpSigma;
            pp = usl.subtract(lsl).divide(cpSigma.multiply(T2), decimalScale, 4);
            ppk = usl.subtract(sumAvg.divide(BigDecimal.valueOf(subGroupSize), decimalScale, 4)).divide(ppSigma.multiply(T), decimalScale, 4).min(sumAvg.divide(BigDecimal.valueOf(subGroupSize), decimalScale, 4).subtract(lsl).divide(ppSigma.multiply(T), decimalScale, 4));
        }

        CapabilityBig capability =
                new CapabilityBig(cp, cpk, cpSigma, tau, cpm, pp, ppk, ppSigma);
        return capability;
    }
    /**
     * 计算单边上限规格限下的能力指数。
     * 包括 CP、CPK、PP、PPK 等指数的计算。
     *
     * @param sigma 标准差
     * @param spec 规格对象
     * @param subGroupSize 子组大小
     * @param siteTotalCount 站点总数
     * @param sumOfSum 数据总和
     * @param sumOfSquareSum 平方和
     * @param xbarAvg 平均值
     * @param sAvg 标准差
     * @param sumAvg 总平均值
     * @return 计算得到的能力指数对象
     */
    public CapabilityBig doUpperCalculate(BigDecimal sigma, SpecBig spec, int subGroupSize, int siteTotalCount, BigDecimal sumOfSum, BigDecimal sumOfSquareSum, BigDecimal xbarAvg, BigDecimal sAvg, BigDecimal sumAvg) {


        BigDecimal usl = spec.getUpperSpec();
        BigDecimal target = spec.getTarget();


        BigDecimal cp = null;
        BigDecimal cpk = null;
        BigDecimal cpSigma = sigma;

        BigDecimal tau = null;
        BigDecimal cpm = null;

        BigDecimal pp = null;
        BigDecimal ppk = null;
        BigDecimal ppSigma = null;

        if ((sumOfSum != null) && (sumOfSquareSum != null)) {
            ppSigma = getPPSigma(siteTotalCount, sumOfSum, sumOfSquareSum);
        }

        if (cpSigma != null) {
            cp = usl.subtract(xbarAvg).divide(cpSigma.multiply(T), decimalScale, 4);
            cpk = cp;
        }
        if ((target != null) && (cpSigma != null) && (xbarAvg != null)) {
            tau = getTau(target, cpSigma, xbarAvg);
        }
        if ((usl != null) && (xbarAvg != null) && (tau != null) &&
                (tau != BigDecimal.ZERO)) {
            cpk = usl.subtract(xbarAvg).divide(tau.multiply(T), decimalScale, 4);
        }

        if ((ppSigma != BigDecimal.ZERO) && (ppSigma != null)) {
            pp = usl.subtract(sumAvg.divide(BigDecimal.valueOf(subGroupSize), decimalScale, 4)).divide(ppSigma.multiply(T), decimalScale, 4);
            ppk = pp;
        } else if ((ppSigma == null) && (cpSigma != null)) {
            ppSigma = cpSigma;
            pp = usl.subtract(sumAvg.divide(BigDecimal.valueOf(subGroupSize), decimalScale, 4)).divide(ppSigma.multiply(T), decimalScale, 4);
            ppk = pp;
        }

        CapabilityBig capability =
                new CapabilityBig(cp, cpk, cpSigma, tau, cpm, pp, ppk, ppSigma);
        return capability;
    }
    /**
     * 计算单边下限规格限下的能力指数。
     * 包括 CP、CPK、PP、PPK 等指数的计算。
     *
     * @param sigma 标准差
     * @param spec 规格对象
     * @param subGroupSize 子组大小
     * @param siteTotalCount 站点总数
     * @param sumOfSum 数据总和
     * @param sumOfSquareSum 平方和
     * @param xbarAvg 平均值
     * @param sAvg 标准差
     * @param sumAvg 总平均值
     * @return 计算得到的能力指数对象
     */
    public CapabilityBig doLowerCalculate(BigDecimal sigma, SpecBig spec, int subGroupSize, int siteTotalCount, BigDecimal sumOfSum, BigDecimal sumOfSquareSum, BigDecimal xbarAvg, BigDecimal sAvg, BigDecimal sumAvg) {


        BigDecimal target = spec.getTarget();
        BigDecimal lsl = spec.getLowerSpec();

        BigDecimal cp = null;
        BigDecimal cpk = null;
        BigDecimal cpSigma = sigma;

        BigDecimal tau = null;
        BigDecimal cpm = null;

        BigDecimal pp = null;
        BigDecimal ppk = null;
        BigDecimal ppSigma = null;

        if ((sumOfSum != null) && (sumOfSquareSum != null)) {
            ppSigma = getPPSigma(siteTotalCount, sumOfSum, sumOfSquareSum);
        }

        if (cpSigma != null) {
            cp = xbarAvg.subtract(lsl).divide(cpSigma.multiply(T), decimalScale, 4);
            cpk = cp;
        }
        if ((target != null) && (cpSigma != null) && (xbarAvg != null))
            tau = getTau(target, cpSigma, xbarAvg);
        if ((lsl != null) && (xbarAvg != null) && (tau != null) &&
                (tau != BigDecimal.ZERO)) {
            cpk = xbarAvg.subtract(lsl).divide(tau.multiply(T), decimalScale, 4);
        }

        if ((ppSigma != BigDecimal.ZERO) && (ppSigma != null)) {
            pp = sumAvg.divide(BigDecimal.valueOf(subGroupSize), decimalScale, 4).subtract(lsl).divide(ppSigma.multiply(T), decimalScale, 4);
            ppk = pp;
        } else if ((ppSigma == null) && (cpSigma == null)) {
            ppSigma = cpSigma;
            pp = sumAvg.divide(BigDecimal.valueOf(subGroupSize), decimalScale, 4).subtract(lsl).divide(ppSigma.multiply(T), decimalScale, 4);
            ppk = pp;
        }

        CapabilityBig capability =
                new CapabilityBig(cp, cpk, cpSigma, tau, cpm, pp, ppk, ppSigma);
        return capability;
    }
    /**
     * 根据规格计算能力指数。
     * 根据规格限类型（双边、单边上限、单边下限）调用对应的方法。
     *
     * @param spec 规格对象
     * @param subGroupSize 子组大小
     * @param dataCount 数据点数量
     * @param sumOfSum 数据总和
     * @param sumOfSquareSum 平方和
     * @param xbarAvg 平均值
     * @param sAvg 标准差
     * @param sumAvg 总平均值
     * @return 计算得到的能力指数对象
     */
    public CapabilityBig calculate(SpecBig spec, int subGroupSize, int dataCount, BigDecimal sumOfSum, BigDecimal sumOfSquareSum, BigDecimal xbarAvg, BigDecimal sAvg, BigDecimal sumAvg) {
        if (dataCount <= 1) {
            return CapabilityBig.newDefaultCapability();
        }

        CapabilityBig capability = null;

        if (spec.getSpecLimitType() == SpecLimitType.Both) {
            capability = doBothCalculate(spec, subGroupSize, dataCount, sumOfSum, sumOfSquareSum, xbarAvg, sAvg, sumAvg);
        } else if (spec.getSpecLimitType() == SpecLimitType.UpperOnly) {
            capability = doUpperCalculate(spec, subGroupSize, dataCount, sumOfSum, sumOfSquareSum, xbarAvg, sAvg, sumAvg);
        } else if (spec.getSpecLimitType() == SpecLimitType.LowerOnly) {
            capability = doLowerCalculate(spec, subGroupSize, dataCount, sumOfSum, sumOfSquareSum, xbarAvg, sAvg, sumAvg);
        } else {
            capability = CapabilityBig.newDefaultCapability();
        }

        return capability;
    }
    /**
     * 计算双边规格限下的能力指数。
     * 包括 CP、CPK、PP、PPK 等指数的计算。
     *
     * @param spec 规格对象
     * @param subGroupSize 子组大小
     * @param dataCount 数据点数量
     * @param sumOfSum 数据总和
     * @param sumOfSquareSum 平方和
     * @param xbarAvg 平均值
     * @param sAvg 标准差
     * @param sumAvg 总平均值
     * @return 计算得到的能力指数对象
     */
    public CapabilityBig doBothCalculate(SpecBig spec, int subGroupSize, int dataCount, BigDecimal sumOfSum, BigDecimal sumOfSquareSum, BigDecimal xbarAvg, BigDecimal sAvg, BigDecimal sumAvg) {


        BigDecimal usl = spec.getUpperSpec();
        BigDecimal target = spec.getTarget();
        BigDecimal lsl = spec.getLowerSpec();

        BigDecimal cp = null;
        BigDecimal cpk = null;
        BigDecimal cpSigma = null;

        BigDecimal tau = null;
        BigDecimal cpm = null;

        BigDecimal pp = null;
        BigDecimal ppk = null;
        BigDecimal ppSigma = null;

        if (sAvg != null)
            cpSigma = getCPSigma(subGroupSize, sAvg);
        if ((sumOfSum != null) && (sumOfSquareSum != null)) {
            ppSigma = getPPSigma(subGroupSize, dataCount, sumOfSum, sumOfSquareSum);
        }

        if (cpSigma != null) {
            cp = usl.subtract(lsl).divide(cpSigma.multiply(T2), decimalScale, 4);
            cpk = usl.subtract(xbarAvg).divide(cpSigma.multiply(T), decimalScale, 4).min(xbarAvg.subtract(lsl).divide(cpSigma.multiply(T), decimalScale, 4));
        }
        if ((target != null) && (cpSigma != null) && (xbarAvg != null))
            tau = getTau(target, cpSigma, xbarAvg);
        if ((usl != null) && (lsl != null) && (tau != null) &&
                (tau != BigDecimal.ZERO)) {
            cpm = usl.subtract(lsl).divide(tau.multiply(T2), decimalScale, 4);
        }
        if (ppSigma != null) {
            pp = usl.subtract(lsl).divide(ppSigma.multiply(T2), decimalScale, 4);
            BigDecimal A = usl.subtract(sumAvg.divide(BigDecimal.valueOf(subGroupSize), decimalScale, 4)).divide(ppSigma.multiply(T), decimalScale, 4);
            BigDecimal B = sumAvg.divide(BigDecimal.valueOf(subGroupSize), decimalScale, 4).subtract(lsl).divide(ppSigma.multiply(T), decimalScale, 4);
            ppk = A.min(B);
        } else if ((ppSigma == null) && (cpSigma != null)) {
            ppSigma = cpSigma;
            pp = usl.subtract(lsl).divide(cpSigma.multiply(T2), decimalScale, 4);
            ppk = usl.subtract(sumAvg.divide(BigDecimal.valueOf(subGroupSize), decimalScale, 4)).divide(ppSigma.multiply(T), decimalScale, 4).min(sumAvg.divide(BigDecimal.valueOf(subGroupSize), decimalScale, 4).subtract(lsl).divide(ppSigma.multiply(T), decimalScale, 4));
        }

        CapabilityBig capability =
                new CapabilityBig(cp, cpk, cpSigma, tau, cpm, pp, ppk, ppSigma);
        return capability;
    }
    /**
     * 计算单边上限规格限下的能力指数。
     * 包括 CP、CPK、PP、PPK 等指数的计算。
     *
     * @param spec 规格对象
     * @param subGroupSize 子组大小
     * @param dataCount 数据点数量
     * @param sumOfSum 数据总和
     * @param sumOfSquareSum 平方和
     * @param xbarAvg 平均值
     * @param sAvg 标准差
     * @param sumAvg 总平均值
     * @return 计算得到的能力指数对象
     */
    public CapabilityBig doUpperCalculate(SpecBig spec, int subGroupSize, int dataCount, BigDecimal sumOfSum, BigDecimal sumOfSquareSum, BigDecimal xbarAvg, BigDecimal sAvg, BigDecimal sumAvg) {


        BigDecimal usl = spec.getUpperSpec();
        BigDecimal target = spec.getTarget();


        BigDecimal cp = null;
        BigDecimal cpk = null;
        BigDecimal cpSigma = null;

        BigDecimal tau = null;
        BigDecimal cpm = null;

        BigDecimal pp = null;
        BigDecimal ppk = null;
        BigDecimal ppSigma = null;

        if (sAvg != null)
            cpSigma = getCPSigma(subGroupSize, sAvg);
        if ((sumOfSum != null) && (sumOfSquareSum != null)) {
            ppSigma = getPPSigma(subGroupSize, dataCount, sumOfSum, sumOfSquareSum);
        }

        if (cpSigma != null) {
            cp = usl.subtract(xbarAvg).divide(cpSigma.multiply(T), decimalScale, 4);
            cpk = cp;
        }
        if ((target != null) && (cpSigma != null) && (xbarAvg != null))
            tau = getTau(target, cpSigma, xbarAvg);
        if ((usl != null) && (xbarAvg != null) && (tau != null) &&
                (tau != BigDecimal.ZERO)) {
            cpk = usl.subtract(xbarAvg).divide(tau.multiply(T), decimalScale, 4);
        }

        if ((ppSigma != BigDecimal.ZERO) && (!ppSigma.equals(null))) {
            pp = usl.subtract(sumAvg.divide(BigDecimal.valueOf(subGroupSize), decimalScale, 4)).divide(ppSigma.multiply(T), decimalScale, 4);
            ppk = pp;
        } else if ((ppSigma == null) && (cpSigma != null)) {
            ppSigma = cpSigma;
            pp = usl.subtract(sumAvg.divide(BigDecimal.valueOf(subGroupSize), decimalScale, 4)).divide(ppSigma.multiply(T), decimalScale, 4);
            ppk = pp;
        }

        CapabilityBig capability =
                new CapabilityBig(cp, cpk, cpSigma, tau, cpm, pp, ppk, ppSigma);
        return capability;
    }
    /**
     * 计算单边下限规格限下的能力指数。
     * 包括 CP、CPK、PP、PPK 等指数的计算。
     *
     * @param spec 规格对象
     * @param subGroupSize 子组大小
     * @param dataCount 数据点数量
     * @param sumOfSum 数据总和
     * @param sumOfSquareSum 平方和
     * @param xbarAvg 平均值
     * @param sAvg 标准差
     * @param sumAvg 总平均值
     * @return 计算得到的能力指数对象
     */
    public CapabilityBig doLowerCalculate(SpecBig spec, int subGroupSize, int dataCount, BigDecimal sumOfSum, BigDecimal sumOfSquareSum, BigDecimal xbarAvg, BigDecimal sAvg, BigDecimal sumAvg) {


        BigDecimal target = spec.getTarget();
        BigDecimal lsl = spec.getLowerSpec();

        BigDecimal cp = null;
        BigDecimal cpk = null;
        BigDecimal cpSigma = null;

        BigDecimal tau = null;
        BigDecimal cpm = null;

        BigDecimal pp = null;
        BigDecimal ppk = null;
        BigDecimal ppSigma = null;

        if (sAvg != null)
            cpSigma = getCPSigma(subGroupSize, sAvg);
        if ((sumOfSum != null) && (sumOfSquareSum != null)) {
            ppSigma = getPPSigma(subGroupSize, dataCount, sumOfSum, sumOfSquareSum);
        }

        if (cpSigma != null) {
            cp = xbarAvg.subtract(lsl).divide(cpSigma.multiply(T), decimalScale, 4);
            cpk = cp;
        }
        if ((target != null) && (cpSigma != null) && (xbarAvg != null))
            tau = getTau(target, cpSigma, xbarAvg);
        if ((lsl != null) && (xbarAvg != null) && (tau != null) &&
                (tau != BigDecimal.ZERO)) {
            cpk = xbarAvg.subtract(lsl).divide(tau.multiply(T), decimalScale, 4);
        }

        if ((ppSigma != BigDecimal.ZERO) && (ppSigma != null)) {
            pp = sumAvg.divide(BigDecimal.valueOf(subGroupSize), decimalScale, 4).subtract(lsl).divide(ppSigma.multiply(T), decimalScale, 4);
            ppk = pp;
        } else if ((ppSigma == null) && (cpSigma != null)) {
            ppSigma = cpSigma;
            pp = sumAvg.divide(BigDecimal.valueOf(subGroupSize), decimalScale, 4).subtract(lsl).divide(ppSigma.multiply(T), decimalScale, 4);
            ppk = pp;
        }

        CapabilityBig capability =
                new CapabilityBig(cp, cpk, cpSigma, tau, cpm, pp, ppk, ppSigma);
        return capability;
    }
    /**
     * 根据极差 R 和子组大小计算标准差。
     *
     * @param subGroupSize 子组大小
     * @param rAvg 极差
     * @return 标准差
     */
    BigDecimal getCPSigmaR(int subGroupSize, BigDecimal rAvg) {
        BigDecimal sigma = null;

        FactorHolder factorH = new FactorHolder();
        if (factorH.getFactor(FactorType.D2, subGroupSize) > 0.0D) {
            sigma = rAvg.divide(BigDecimal.valueOf(factorH.getFactor(FactorType.D2, subGroupSize)), decimalScale, 4);
        }
        return sigma;
    }
    /**
     * 根据标准差 S 和子组大小计算标准差。
     *
     * @param subGroupSize 子组大小
     * @param sAvg 标准差
     * @return 标准差
     */
    BigDecimal getCPSigma(int subGroupSize, BigDecimal sAvg) {
        BigDecimal sigma = null;

        FactorHolder factorH = new FactorHolder();
        if (factorH.getFactor(FactorType.C4, subGroupSize) > 0.0D) {
            sigma = sAvg.divide(BigDecimal.valueOf(factorH.getFactor(FactorType.C4, subGroupSize)), decimalScale, 4);
        }
        return sigma;
    }

    /**
     * 根据标准差 S、数据点数量、站点总数和子组大小计算标准差。
     *
     * @param subGroupSize 子组大小
     * @param dataCount 数据点数量
     * @param siteTotalCount 站点总数
     * @param sAvg 标准差
     * @return 标准差
     */
    BigDecimal getCPSigma(int subGroupSize, int dataCount, int siteTotalCount, BigDecimal sAvg) {
        BigDecimal sigma = null;

        FactorHolder factorH = new FactorHolder();
        if (siteTotalCount > 25) {
            sigma = sAvg;
        } else if (factorH.getFactor(FactorType.C4, subGroupSize) > 0.0D) {
            sigma = sAvg.divide(BigDecimal.valueOf(factorH.getFactor(FactorType.C4, siteTotalCount - dataCount + 1)), decimalScale, 4);
        }

        return sigma;
    }
    /**
     * 调整标准差 S 的计算。
     *
     * @param sAvg 标准差
     * @param rAvg 极差
     * @param xAvg 平均值
     * @return 调整后的标准差
     */
    BigDecimal getSAvg(BigDecimal sAvg, BigDecimal rAvg, BigDecimal xAvg) {
        BigDecimal value = null;


        if (xAvg != null) {
            value = sAvg.add(rAvg.multiply(TWO).divide(xAvg, decimalScale, 4).multiply(sAvg));
        } else {
            value = sAvg;
        }

        return value;
    }
    /**
     * 根据平方和和数据总和计算过程性能标准差 PP。有分组
     *
     * @param subGroupSize 子组大小
     * @param dataCount 数据点数量
     * @param sumOfSum 数据总和
     * @param sumOfSquareSum 平方和
     * @return 过程性能标准差
     */
    BigDecimal getPPSigma(int subGroupSize, int dataCount, BigDecimal sumOfSum, BigDecimal sumOfSquareSum) {
        BigDecimal PPSigma = null;
        BigDecimal bSubGroupSize = new BigDecimal(subGroupSize);
        BigDecimal bDataCount = new BigDecimal(dataCount);


        BigDecimal temp = sumOfSquareSum.multiply(bDataCount.multiply(bSubGroupSize)).subtract(sumOfSum.multiply(sumOfSum)).divide(
                bDataCount.multiply(bSubGroupSize).multiply(bDataCount.multiply(bSubGroupSize).subtract(BigDecimal.valueOf(1L))), decimalScale, 4);
        BigDecimal temp1 = sumOfSquareSum.multiply(bDataCount.multiply(bSubGroupSize)).subtract(sumOfSum.multiply(sumOfSum)).divide(
                bDataCount.multiply(bSubGroupSize).multiply(bDataCount.multiply(bSubGroupSize).multiply(BigDecimal.valueOf(-1L))), decimalScale, 4);
        if (temp.compareTo(BigDecimal.ZERO) == 1) {
            PPSigma = this.sqrt(temp, decimalScale);
        } else if (temp1.compareTo(BigDecimal.ZERO) == 1) {
            PPSigma = this.sqrt(temp1, decimalScale);
        }

        return PPSigma;
    }
    /**
     * 根据平方和和数据总和计算过程性能标准差 PP。无分组
     *
     * @param siteTotalCount 站点总数
     * @param sumOfSum 数据总和
     * @param sumOfSquareSum 平方和
     * @return 过程性能标准差
     */
    BigDecimal getPPSigma(int siteTotalCount, BigDecimal sumOfSum, BigDecimal sumOfSquareSum) {
        BigDecimal PPSigma = null;
        BigDecimal bSiteTotalCount = new BigDecimal(siteTotalCount);


        BigDecimal temp = sumOfSquareSum.multiply(bSiteTotalCount).subtract(sumOfSum.multiply(sumOfSum)).divide(
                bSiteTotalCount.multiply(bSiteTotalCount.subtract(BigDecimal.valueOf(1L))), decimalScale, 4);
        BigDecimal temp1 = sumOfSquareSum.multiply(bSiteTotalCount).subtract(sumOfSum.multiply(sumOfSum)).divide(
                bSiteTotalCount.multiply(bSiteTotalCount.multiply(BigDecimal.valueOf(-1L))), decimalScale, 4);
        if (temp.compareTo(BigDecimal.ZERO) == 1) {
            PPSigma = this.sqrt(temp, decimalScale);
        } else if (temp1.compareTo(BigDecimal.ZERO) == 1) {
            PPSigma = this.sqrt(temp1, decimalScale);
        }

        return PPSigma;
    }

    /**
     * 计算 Tau 值。
     *
     * @param target 目标值
     * @param cpSigma 标准差
     * @param xbarAvg 平均值
     * @return Tau 值
     */
    BigDecimal getTau(BigDecimal target, BigDecimal cpSigma, BigDecimal xbarAvg) {
        BigDecimal Tau = null;

        Tau = this.sqrt(cpSigma.multiply(cpSigma).add(xbarAvg.subtract(target).multiply(xbarAvg.subtract(target))), decimalScale);
        return Tau;
    }
    /**
     * 计算平方根。
     *
     * @param value 输入值
     * @param SCALE 精度
     * @return 平方根值
     */
    public BigDecimal sqrt(BigDecimal value, int SCALE) {
        BigDecimal temp = new BigDecimal("0");
        BigDecimal square = new BigDecimal(Math.sqrt(value.doubleValue()));
        while (!temp.equals(square)) {
            temp = square;
            square = value.divide(temp, SCALE, 4);
            square = square.add(temp);
            square = square.divide(BigDecimal.valueOf(2L), SCALE, 4);
        }

        return square;
    }
    /**
     * 创建并返回包含计算结果的 CapabilityData 对象。
     *
     * @param type 类型标识，如 "R", "S", "T"
     * @param spec 规格对象
     * @param subGroupSize 子组大小
     * @param dataCount 数据点数量
     * @param siteCountSum 站点总数
     * @param sumOfSum 数据总和
     * @param sumOfSquareSum 平方和
     * @param xbarAvg 平均值
     * @param sAvg 标准差
     * @param rAvg 极差
     * @param sumAvg 总平均值
     * @return 包含计算结果的 CapabilityData 对象
     */
    public CapabilityData CreateCapabilityData(String type, SpecBig spec, int subGroupSize, int dataCount, int siteCountSum,
                                               BigDecimal sumOfSum, BigDecimal sumOfSquareSum, BigDecimal xbarAvg, BigDecimal sAvg, BigDecimal rAvg, BigDecimal sumAvg) {
        CapabilityData capabilityData = new CapabilityData();
        log.info("CreateCapabilityData function =" + type + ", " + subGroupSize + ", " + dataCount + ", " + siteCountSum + ", " + sumOfSum + ", " + sumOfSquareSum + ", " + xbarAvg + ", " + sAvg + ", " + rAvg + "," + sumAvg);

        CapabilityBig capability = this.calculate(type, spec, subGroupSize, dataCount, siteCountSum, sumOfSum, sumOfSquareSum, xbarAvg, sAvg, rAvg, sumAvg);

        if (CommonUtil.hasValue(capability.getCp()))
            capabilityData.setCp(CommonUtil.getPlainNumeric(CommonUtil.roundValue(capability.getCp())));
        if (CommonUtil.hasValue(capability.getCpk()))
            capabilityData.setCpk(CommonUtil.getPlainNumeric(CommonUtil.roundValue(capability.getCpk())));
        if (CommonUtil.hasValue(capability.getCpm()))
            capabilityData.setCpm(CommonUtil.getPlainNumeric(CommonUtil.roundValue(capability.getCpm())));
        if (CommonUtil.hasValue(capability.getPp()))
            capabilityData.setPp(CommonUtil.getPlainNumeric(CommonUtil.roundValue(capability.getPp())));
        if (CommonUtil.hasValue(capability.getPpk()))
            capabilityData.setPpk(CommonUtil.getPlainNumeric(CommonUtil.roundValue(capability.getPpk())));

        return capabilityData;
    }
}
