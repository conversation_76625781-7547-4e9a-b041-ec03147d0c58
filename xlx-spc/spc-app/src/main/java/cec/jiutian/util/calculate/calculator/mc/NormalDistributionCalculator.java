//package cec.jiutian.util.calculate.calculator.mc;
//
//import javafx.application.Application;
//import javafx.scene.Scene;
//import javafx.scene.chart.LineChart;
//import javafx.scene.chart.NumberAxis;
//import javafx.scene.chart.XYChart;
//import javafx.scene.control.Button;
//import javafx.scene.control.Label;
//import javafx.scene.control.TextField;
//import javafx.scene.layout.BorderPane;
//import javafx.scene.layout.HBox;
//import javafx.scene.layout.VBox;
//import javafx.stage.Stage;
//
//public class NormalDistributionCalculator extends Application {
//
//    private TextField meanField, stdDevField, pointsField;
//    private LineChart<Number, Number> lineChart;
//    private XYChart.Series<Number, Number> series;
//
//    public static void main(String[] args) {
//        launch(args);
//    }
//
//    @Override
//    public void start(Stage primaryStage) {
//        primaryStage.setTitle("正态分布图计算器");
//
//        // 创建输入控件
//        Label meanLabel = new Label("均值 (μ):");
//        meanField = new TextField("0");
//
//        Label stdDevLabel = new Label("标准差 (σ):");
//        stdDevField = new TextField("1");
//
//        Label pointsLabel = new Label("绘图点个数:");
//        pointsField = new TextField("100");
//
//        Button calculateButton = new Button("生成分布图");
//        calculateButton.setOnAction(e -> calculateNormalDistribution());
//
//        HBox inputBox = new HBox(10);
//        inputBox.getChildren().addAll(
//                new VBox(5, meanLabel, meanField),
//                new VBox(5, stdDevLabel, stdDevField),
//                new VBox(5, pointsLabel, pointsField),
//                calculateButton
//        );
//
//        // 创建图表
//        final NumberAxis xAxis = new NumberAxis();
//        final NumberAxis yAxis = new NumberAxis();
//        xAxis.setLabel("X");
//        yAxis.setLabel("概率密度");
//
//        lineChart = new LineChart<>(xAxis, yAxis);
//        lineChart.setTitle("正态分布曲线");
//        lineChart.setCreateSymbols(false);
//
//        series = new XYChart.Series<>();
//        series.setName("正态分布");
//        lineChart.getData().add(series);
//
//        // 布局
//        BorderPane root = new BorderPane();
//        root.setTop(inputBox);
//        root.setCenter(lineChart);
//
//        Scene scene = new Scene(root, 800, 600);
//        primaryStage.setScene(scene);
//        primaryStage.show();
//
//        // 初始化图表
//        calculateNormalDistribution();
//    }
//
//    private void calculateNormalDistribution() {
//        try {
//            // 获取用户输入
//            double mean = Double.parseDouble(meanField.getText());
//            double stdDev = Double.parseDouble(stdDevField.getText());
//            int points = Integer.parseInt(pointsField.getText());
//
//            if (stdDev <= 0) {
//                throw new IllegalArgumentException("标准差必须大于0");
//            }
//
//            if (points < 2) {
//                throw new IllegalArgumentException("绘图点个数至少为2");
//            }
//
//            // 清除旧数据
//            series.getData().clear();
//
//            // 计算绘图范围（通常取均值±4σ）
//            double range = 4 * stdDev;
//            double minX = mean - range;
//            double maxX = mean + range;
//            double step = (maxX - minX) / (points - 1);
//
//            // 计算正态分布曲线上的点
//            for (int i = 0; i < points; i++) {
//                double x = minX + i * step;
//                double y = normalDistributionPDF(x, mean, stdDev);
//                series.getData().add(new XYChart.Data<>(x, y));
//            }
//
//        } catch (NumberFormatException e) {
//            System.err.println("输入格式错误: " + e.getMessage());
//        } catch (IllegalArgumentException e) {
//            System.err.println("参数错误: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 计算正态分布的概率密度函数值
//     * @param x 输入值
//     * @param mean 均值
//     * @param stdDev 标准差
//     * @return 概率密度值
//     */
//    private double normalDistributionPDF(double x, double mean, double stdDev) {
//        return (1.0 / (stdDev * Math.sqrt(2 * Math.PI))) *
//                Math.exp(-0.5 * Math.pow((x - mean) / stdDev, 2));
//    }
//}
