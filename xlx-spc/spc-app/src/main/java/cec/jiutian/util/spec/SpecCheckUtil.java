package cec.jiutian.util.spec;

import java.util.Objects;

public class SpecCheckUtil {
    public static boolean  checkSpecLimit(Double usl,Double lsl, double value)
    {
        boolean specOut = false;

        if(Objects.nonNull(usl)){
            if(value > usl){
                specOut = true;
            }
        }
        if(Objects.nonNull(lsl)){
            if(value < lsl){
                specOut = true;
            }
        }

        return specOut;
    }
}
