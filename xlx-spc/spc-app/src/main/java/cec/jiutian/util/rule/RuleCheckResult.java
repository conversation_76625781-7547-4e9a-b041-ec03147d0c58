package cec.jiutian.util.rule;

import cec.jiutian.bc.spc.engine.model.Data;
import cec.jiutian.bc.chartControl.enumeration.ChartTypeEnum;
import com.google.common.collect.Lists;
import org.springframework.util.Assert;

import java.util.Collections;
import java.util.List;

public class RuleCheckResult {
    private boolean ruleOut = false;
    private ChartTypeEnum.Enum controlChartType;
    private RuleInfo ruleInfo;
    private List<Data<?>> testDatas = Lists.newArrayList();
    private List<Data<?>> ruleoutDatas = Lists.newArrayList();

    public  RuleCheckResult( RuleInfo ruleInfo)
    {
        Assert.notNull(ruleInfo, "The ruleInfo object must not be null");
        ruleOut = false;
        this.ruleInfo = ruleInfo;
    }
    
    public ChartTypeEnum.Enum getControlChartType() {
         return controlChartType;
    }

    public RuleInfo getRuleInfo() {
         return ruleInfo;
    }

    public List<Data<?>> getTestDatas() {
         return testDatas;
    }

    public List<Data<?>> getRuleoutDatas() {
         return ruleoutDatas;
    }
    public boolean isRuleOut() {
        return ruleOut;
    }

    public void addTestData(Data<?> data)
    {
        getTestDatas().add(data);
    }

    public void addRuleOutData(Data<?> data) {
        getRuleoutDatas().add(data);
    }
       public void ruleOut() {
             ruleOut = true;
             reverseTestDatas();
             reverseRuleoutDatas();
       }
    
       private void reverseTestDatas() {
             Collections.reverse(getTestDatas());
           }
    
       private void reverseRuleoutDatas() {
             Collections.reverse(getRuleoutDatas());
           }
    
       public void ruleIn() {
             ruleOut = false;
        
             reverseTestDatas();
             reverseRuleoutDatas();
           }
       public void allDataRuleOut() {
             getRuleoutDatas().addAll(getTestDatas());
        }
    
       public void clear()
       {
             controlChartType = null;
             ruleInfo = null;
        
             testDatas.clear();
             ruleoutDatas.clear();
        
             testDatas = null;
             ruleoutDatas = null;
       }
}
