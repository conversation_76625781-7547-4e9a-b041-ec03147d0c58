package cec.jiutian.util.rule;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 控制图规则枚举
 */
public class RuleTypeEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum data : Enum.values()) {
            list.add(new VLModel(data.name(), data.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {

//
//        /*
//        1个点超出规格线
//         */
//        OutOfLimit("超出限制"),
//        /*
//
//        */
//        InLimit("在限制内"),
//        /*
//
//         */
//        AboveOrBelowLimit("高于或低于限制"),
//        /*
//
//         */
//        AboveAndBelowLimit("高于且低于限制"),
//        /*
//
//         */
//        AboveLimit("高于限制"),
//        /*
//
//         */
//        BelowLimit("低于限制"),
//        /*
//            N个点中有M个点大于标准线，或小于标准线
//         */
//        AboveOrBelowTarget("高于或低于目标"),
//        AboveTarget("高于目标"),
//        BelowTarget("低于目标"),
//        /*
//            连续N个点全部递增或递减
//         */
//        Linear("线性"),
        /*
            连续N个点上下交错
         */
//        Oscillatory("振荡"),
        rule1("判异准则1：一个点远离中心线超过{N}倍标准差"),
        rule2("判异准则2：连续{N}个点在同一侧"),
        rule3("判异准则3：连续{N}个点持续上升或下降"),
        rule4("判异准则4：连续{N}个点交互升降"),
        rule5("判异准则5：连续{N}个点,有{N}-1个点远离中心线2倍标准差"),
        rule6("判异准则6：连续{N}个点,有{N}-1个点远离中心线1倍标准差"),
        rule7("判异准则7：连续{N}个点在中心线一倍标准差内"),
        rule8("判异准则8：连续{N}个点在中心线一倍标准差外"),
        rule9("判异准则9：连续{N}个点,远离中心线{M}倍标准差"),
        rule10("判异准则10：连续{N}个点，有至少{J}个点远离中心线{M}倍标准差"),
//        rule11("1个点超出规格限"),
        ;
        private final String value;

    }
}
