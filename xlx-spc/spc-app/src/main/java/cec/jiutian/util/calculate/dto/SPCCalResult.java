package cec.jiutian.util.calculate.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
/*
结果数据类，公共
 */
@Data
@AllArgsConstructor
@NoArgsConstructor(force = true)
public class SPCCalResult {
    protected double usl;//上规格限
    protected double lsl;//下规格限
    protected double lotCnt;//批次数量（样本组数）
    protected double grandMean;// 样本总体均值
    protected double cSigma; // 组内(Sigma)
    protected double pSigma; // 总(Sigma)

    protected double cp;
    protected double cpk;
    protected double cpl;
    protected double cpu;

    protected double pp;
    protected double ppk;
    protected double ppl;
    protected double ppu;

    protected double processShift; // 过程偏移百分比

    protected double meanShift; // 均值偏移百分比
    protected double cov ; // 变异系数COV (Coefficient of Variation)

    protected double stabilityIndex; //稳定性指标 PSigma/CSigma

}
