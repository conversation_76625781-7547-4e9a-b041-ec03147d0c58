package cec.jiutian.dowork.service;

import cec.jiutian.bc.chartControl.domain.DCData.DCData;
import cec.jiutian.bc.chartControl.domain.DCData.DCDataResult;
import cec.jiutian.bc.chartControl.domain.chartAlarm.model.AlarmData;
import cec.jiutian.bc.chartControl.domain.chartControl.model.ChartControl;
import cec.jiutian.bc.chartControl.domain.chartControl.model.ChartControlDataResult;
import cec.jiutian.bc.chartControl.domain.chartControl.model.ChartDefinition;
import cec.jiutian.bc.chartControl.domain.controlRule.model.ControlRule;
import cec.jiutian.bc.chartControl.dto.SpcAlarmCountDTO;
import cec.jiutian.bc.chartControl.dto.SpcAlarmCountListDTO;
import cec.jiutian.bc.chartControl.dto.SpcTrendDTO;
import cec.jiutian.bc.chartControl.dto.SpcTrendListDTO;
import cec.jiutian.bc.chartControl.dto.ViewChartDTO;
import cec.jiutian.bc.chartControl.dto.ViewChartDataDTO;
import cec.jiutian.bc.chartControl.dto.ViewSubChartDTO;
import cec.jiutian.bc.chartControl.dto.query.ViewChartQueryDTO;
import cec.jiutian.bc.chartControl.enumeration.AlarmTypeEnum;
import cec.jiutian.bc.chartControl.enumeration.ChartMainSubTypeEnum;
import cec.jiutian.bc.dto.ChartControlSettingDTO;
import cec.jiutian.bc.dto.HomePageSettingDTO;
import cec.jiutian.bc.mto.ChartControlSetting;
import cec.jiutian.bc.mto.HomePageSetting;
import cec.jiutian.bc.spc.engine.model.ControlLimit;
import cec.jiutian.bc.spc.engine.model.Data;
import cec.jiutian.common.util.BeanUtils;
import cec.jiutian.common.util.NumberUtil;
import cec.jiutian.common.util.StringUtils;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.dowork.dto.ItemDTO;
import cec.jiutian.dowork.dto.SpcProcessMsgDTO;
import cec.jiutian.dowork.service.modelService.ChartControlService;
import cec.jiutian.dowork.service.modelService.ControlDataService;
import cec.jiutian.dowork.service.modelService.ControlRuleService;
import cec.jiutian.util.calculate.dto.NormalDistributionChart;
import cec.jiutian.util.calculate.dto.NormalDistributionPoint;
import cec.jiutian.util.calculate.dto.SPCCalResult;
import cec.jiutian.util.calculate.proxy.SpcCalculatorProxy;
import cec.jiutian.util.chart.DCDataTypeEnum;
import cec.jiutian.util.common.ConstantMap;
import cec.jiutian.util.common.ObjectMapperProxy;
import cec.jiutian.util.rule.RuleCheckResult;
import cec.jiutian.util.rule.RuleCheckUtil;
import cec.jiutian.util.rule.RuleInfo;
import cec.jiutian.util.spec.SpecCheckUtil;
import cec.jiutian.util.time.TimeStampUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@Transactional
public class ChartService {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private ChartControlService chartControlService;

    @Resource
    private ControlRuleService controlRuleService;

    @Resource
    private ControlDataService controlDataService;

    /*
    规则校验，对每个控制图子图进行校验
    1：遍历控制图子图
    2：找到当前子图所定义的规则
    3：遍历规则
    4：找到规则所需的数据点个数
    5：查询到指定个数的数据点
    6：对数据点按规则进行校验
     */
    public void ruleCheck(List<ChartControl> chartDefinitionList, DCData dcData) {
        if (CollectionUtils.isEmpty(chartDefinitionList)) {
            log.error(" chartControlList 为空");
            return;
        }
        try {
            List<AlarmData> alarmDataList = new ArrayList<>();
            for (ChartControl chartControl : chartDefinitionList) {
                List<ControlRule> rules = chartControl.getRules();
                if (CollectionUtils.isEmpty(rules)) {
                    continue;
                }
                for (ControlRule rule : rules) {
                    // 获取当前规则的参数，找到需要采样的数据点个数
                    int sampleCount = controlRuleService.genSampleCount(rule);
                    List<Data<?>> datas = chartControlService.getBySampleCount(chartControl, sampleCount);
                    // 初始化RuleInfo
                    RuleInfo ruleInfo = controlRuleService.initRuleInfo(rule);
                    // 没有计算控制限则跳过
                    if (StringUtils.isEmpty(chartControl.getUpperControlLimit())  || StringUtils.isEmpty(chartControl.getLowerControlLimit())){
                        continue;
                    }
                    ControlLimit controlLimit = new ControlLimit(
                            Double.parseDouble(chartControl.getUpperControlLimit())
                            , Double.parseDouble(chartControl.getCentralControlLine())
                            , Double.parseDouble(chartControl.getLowerControlLimit())
                    );
                    RuleCheckResult result = new RuleCheckResult(ruleInfo);
                    // 校验规则
                    RuleCheckUtil.doRuleCheck(ruleInfo, datas, controlLimit, result);
                    // 有异常则生成AlarmData
                    if (result.isRuleOut()) {
                        AlarmData alarmData = new AlarmData();
                        alarmData.setTimekey(dcData.getTimekey());
                        alarmData.setChartDef(chartControl.getChartDefinition());
                        alarmData.setChartControl(chartControl);
                        alarmData.setChartControlType(chartControl.getChartDefinition().getControlChartType());
                        alarmData.setAlarmType(AlarmTypeEnum.Enum.OOR.name());
                        alarmDataList.add(alarmData);
                        // 通过rultoutdatas。将违反的规则加入到对应的控制图数据点中
                        setRuleToResult(result.getRuleoutDatas(), rule);
                    }
                }
            }
            fabosJsonDao.insertBatch(alarmDataList);
        } catch (Exception e) {
            log.error("规则校验异常", e);
        }
    }

    /*
    为违反规则的点设置违反的对应规则
     */
    private void setRuleToResult(List<Data<?>> ruleoutDatas, ControlRule rule) {
        if (CollectionUtils.isEmpty(ruleoutDatas)) {
            return;
        }
        List<String> ids = (List<String>) ruleoutDatas.stream().map(Data::getId).toList();
        List<ChartControlDataResult> chartControlDataResultList =
                fabosJsonDao.queryEntityList(ChartControlDataResult.class, "where id in (:ids)", Map.of("ids", ids));
        if (CollectionUtils.isEmpty(chartControlDataResultList)) {
            return;
        }
        for (ChartControlDataResult chartControlDataResult : chartControlDataResultList) {
            List<ControlRule> rules = chartControlDataResult.getOorList();
            if (CollectionUtils.isEmpty(rules)) {
                rules.add(rule);
            } else if (!rules.stream().map(ControlRule::getId).toList().contains(rule.getId())) {
                rules.add(rule);
            }
            chartControlDataResult.setOorList(rules);
        }
        fabosJsonDao.updateBatchById(chartControlDataResultList);
    }

    /*
    控制限校验，根据chartDefine来进行校验
     */
    public void specCheck(List<ChartControl> chartControlList, DCData dcData, List<DCDataResult> rawDcDataResultList) {
        if (!checkChartControlAndData(chartControlList, rawDcDataResultList)) {
            return;
        }
        List<AlarmData> alarmDataList = new ArrayList<>();
        List<ChartDefinition> chartDefList = chartControlList.stream().map(ChartControl::getChartDefinition).toList();
        // chartDefList去重
        chartDefList = chartDefList.stream().collect(Collectors.groupingBy(ChartDefinition::getChartName)).values().stream().flatMap(List::stream).toList();
        //根据每个控制图做控制限校验
        for (ChartDefinition chartDefinition : chartDefList) {
            String uslStr = chartDefinition.getUpperSpecLimit();
            String lslStr = chartDefinition.getLowerSpecLimit();
            if (StringUtils.isBlank(uslStr) || StringUtils.isBlank(lslStr)) {
                continue;
            }
            double usl = Double.parseDouble(chartDefinition.getUpperSpecLimit());
            double lsl = Double.parseDouble(chartDefinition.getLowerSpecLimit());
            for (DCDataResult dcDataResult : rawDcDataResultList) {
                double value = Double.parseDouble(dcDataResult.getResult());
                if (SpecCheckUtil.checkSpecLimit(usl, lsl, value)) {
                    // 生成AlarmData
                    AlarmData alarmData = new AlarmData();
                    alarmData.setTimekey(dcData.getTimekey());
                    alarmData.setChartDef(chartDefinition);
                    alarmData.setChartControlType(chartDefinition.getControlChartType());
                    alarmData.setAlarmType(AlarmTypeEnum.Enum.OOS.name());
                    alarmData.setResultValue(value);
                    alarmDataList.add(alarmData);
                }
            }
        }
        if (CollectionUtils.isEmpty(alarmDataList)) {
            return;
        }
        fabosJsonDao.insertBatch(alarmDataList);
    }

    public List<ChartControl> getChartControlByItemNames(SpcProcessMsgDTO spcProcessMsgDTO) {
        List<String> itemNames = spcProcessMsgDTO.getItemList().stream().map(ItemDTO::getItemName).toList();
        itemNames = new ArrayList<>(new LinkedHashSet<>(itemNames));
        return chartControlService.getChartControlByItemNames(itemNames);
    }


    /*
    数据点位查询，分为2种一种根据时间查询。一种根据数据个数查询，个数是针对DCDataID
    1：一种根据数据个数查询
     */
    public ViewChartDTO queryChart(ViewChartQueryDTO queryDTO) {
        String sql = "select cdr.result,cdr.timekey,cdr.site_name as siteName, cdr.sub_chart_name AS subChartName" +
                " ,cdr.factory_lot_identifier AS factoryLotIdentifier" +
                " ,cc.upper_spec_limit as upperSpecLimit , cc.target_spec as targetSpec, cc.lower_spec_limit as lowerSpecLimit" +
                " ,cc.upper_control_limit as upperControlLimit, cc.central_control_line as centralControlLine, cc.lower_control_limit as lowerControlLimit " +
                " , STRING_AGG(cr.control_rule_description, ',') AS descriptions" +
                " from chart_control_data_result cdr " +
                " join chart_control cc on cc.chart_name = cdr.chart_name and cc.sub_chart_name = cdr.sub_chart_name" +
                " left join OOR_RELATION oor on oor.chart_control_data_result_id = cdr.id" +
                " left join control_rule cr on oor.control_rule_id = cr.id "  +
                " where cdr.chart_name = :chartName";
        if (Objects.isNull(queryDTO.getLimitCount())) {


            sql += (" and CDR.timekey > :startTime" +
                    " and CDR.timekey < :endTime");
            sql +=(  " GROUP BY cdr.result, " +
                    " cdr.timekey," +
                    " cdr.site_name," +
                    " cdr.sub_chart_name," +
                    " cdr.factory_lot_identifier," +
                    " cc.upper_spec_limit," +
                    " cc.target_spec," +
                    " cc.lower_spec_limit," +
                    " cc.upper_control_limit," +
                    " cc.central_control_line," +
                    " cc.lower_control_limit");
        } else if (Objects.nonNull(queryDTO.getLimitCount())) {
            sql +=(  " GROUP BY cdr.result, " +
                    " cdr.timekey," +
                    " cdr.site_name," +
                    " cdr.sub_chart_name," +
                    " cdr.factory_lot_identifier," +
                    " cc.upper_spec_limit," +
                    " cc.target_spec," +
                    " cc.lower_spec_limit," +
                    " cc.upper_control_limit," +
                    " cc.central_control_line," +
                    " cc.lower_control_limit");
            sql += (
                    " order by CDR.timekey desc" +
                            " limit :limitCount"
            );
        }

        // 若chartName为空，则返回所有值都为0
        if (StringUtils.isBlank(queryDTO.getChartName())) {
            ViewChartDTO result = new ViewChartDTO(
                    new ViewSubChartDTO("", "main", ChartMainSubTypeEnum.Enum.Main.name(), List.of(new ViewChartDataDTO(0, "", "", 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, "", "", null))),
                    new ViewSubChartDTO("", "main", ChartMainSubTypeEnum.Enum.Sub.name(), List.of(new ViewChartDataDTO(0, "", "", 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, "", "", null)))
            );
            return result;
        }
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("chartName", queryDTO.getChartName());
        paramMap.put("startTime", queryDTO.getStartTime());
        paramMap.put("endTime", queryDTO.getEndTime());
        paramMap.put("limitCount", queryDTO.getLimitCount());
        List<Map<String, Object>> controlDataListMap = fabosJsonDao.getNamedParameterJdbcTemplate().queryForList(sql, paramMap);
        List<ViewChartDataDTO> viewChartDataList = new ArrayList<>();
        for (Map<String, Object> map : controlDataListMap) {
            ViewChartDataDTO viewChartDataDTO;
            viewChartDataDTO = ObjectMapperProxy.getObjectMapper().convertValue(map, ViewChartDataDTO.class);
            String descriptions = (String) map.get("descriptions");
            if(Objects.nonNull( descriptions)){
                viewChartDataDTO.setOORList(Arrays.asList(descriptions.split(",")));
            }
            viewChartDataList.add(viewChartDataDTO);
        }
        // 构造成前端展示格式
        ViewChartDTO viewChartDTO = new ViewChartDTO();
        Map<String, List<ViewChartDataDTO>> viewChartDataMap = viewChartDataList.stream().collect(Collectors.groupingBy(ViewChartDataDTO::getSubChartName));
        viewChartDataMap.forEach((subChartName, viewChartDataDTOList) -> {
            if (ConstantMap.subChartType.contains(subChartName)) {
                viewChartDTO.setSubChart(new ViewSubChartDTO(queryDTO.getChartName(), subChartName, ChartMainSubTypeEnum.Enum.Sub.name(), viewChartDataDTOList));
            } else {
                viewChartDTO.setMainChart(new ViewSubChartDTO(queryDTO.getChartName(), subChartName, ChartMainSubTypeEnum.Enum.Main.name(), viewChartDataDTOList));
            }
        });
        return viewChartDTO;
    }

    // 根据查询条件，查询数据并计算过程能力
    public SPCCalResult calCapability(ViewChartQueryDTO queryDTO) {
        ChartDefinition chartDefinition = chartControlService.getChartDefinitionByChartName(queryDTO.getChartName());
        if (Objects.isNull(chartDefinition)) {
            throw new FabosJsonApiErrorTip("控制图：" + queryDTO.getChartName() + "不存在");
        }
        //1: 查询原始DCData
        List<double[]> subgroups = this.getSubgroupsByDCData(queryDTO);
        //2: 计算过程能力并返回
        return SpcCalculatorProxy.getSpcCalculator(chartDefinition.getControlChartType()).calculateAll(
                subgroups, Double.parseDouble(chartDefinition.getUpperSpecLimit()), Double.parseDouble(chartDefinition.getLowerSpecLimit()));
    }

    private List<double[]> getSubgroupsByDCData(ViewChartQueryDTO queryDTO) {
        List<Map<String, Object>> controlDataListMap = this.getDCData(queryDTO);
        if (CollectionUtils.isEmpty(controlDataListMap)) {
            return new ArrayList<>();
        }
        // controlDataListMap 以dcDataId分组并将result生成List<double[]>
        List<double[]> subgroups = controlDataListMap.stream().collect(Collectors.groupingBy(
                map -> map.get("dcDataId"))).entrySet().stream().map(
                entry -> entry.getValue().stream().mapToDouble
                        (map -> Double.parseDouble(map.get("result").toString())).toArray()).toList();
        return subgroups;
    }

    /*
    根据图名和时间范围查询或者样本取样次数原始DCData
     */
    private List<Map<String, Object>> getDCData(ViewChartQueryDTO queryDTO) {
        String sql = " ";
        // 基于时间范围查询
        if (Objects.isNull(queryDTO.getLimitCount())) {
            sql = "select ddr.result,ddr.timekey,ddr.site_name as siteName,ddr.dc_data_id AS dcDataId " +
                    " from dc_data_result ddr " +
                    " join chart_item ci on ci.chart_item_name = ddr.item_name" +
                    " join chart_definition cd on cd.chart_item_id = ci.id" +
                    " where ddr.data_type = :dataType" +
                    " and cd.chart_name = :chartName" +
                    " and ddr.timekey > :startTime" +
                    " and ddr.timekey < :endTime";

        }
        // 基于样本取样次数查询
        else if (Objects.nonNull(queryDTO.getLimitCount())) {
            sql = "select ddr.result,ddr.timekey,ddr.site_name as siteName,ddr.dc_data_id AS dcDataId " +
                    " from dc_data_result ddr " +
                    " where ddr.dc_data_id in  " +
                    " ( select dd.id from dc_data dd " +
                    " join chart_definition cd on cd.chart_item_id = dd.chart_item_id" +
                    " where cd.chart_name = :chartName" +
                    "  order by dd.timekey desc" +
                    " limit :limitCount " +
                    "  )" +
                    "AND ddr.data_type = :dataType";
        }
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("dataType", DCDataTypeEnum.Enum.Raw.name());
        paramMap.put("chartName", queryDTO.getChartName());
        paramMap.put("startTime", queryDTO.getStartTime());
        paramMap.put("endTime", queryDTO.getEndTime());
        paramMap.put("limitCount", queryDTO.getLimitCount());
        List<Map<String, Object>> controlDataListMap = fabosJsonDao.getNamedParameterJdbcTemplate().queryForList(sql, paramMap);
        if (CollectionUtils.isEmpty(controlDataListMap)) {
            return new ArrayList<>();
        }
        return controlDataListMap;
    }

    /*
     * 计算过程趋势
     * 1:  查询原始DCData
     * 2:  根据周期类型分组获取子组
     * 3:  计算过程趋势并返回
     */
    public SpcTrendListDTO calTrend(ViewChartQueryDTO queryDTO) {
        ChartDefinition chartDefinition = checkChartDef(queryDTO.getChartName());
        List<Map<String, Object>> controlDataListMap = this.getDCData(queryDTO);
        if (CollectionUtils.isEmpty(controlDataListMap)) {
            return null;
        }
        SpcTrendListDTO result = new SpcTrendListDTO();
        List<SpcTrendDTO> spcTrendDTOList = new ArrayList<>();
        // 1:根据不同的term,将controlDataListMap 先以timekey的不同位数分组
        int timkeySubLength = ConstantMap.TermTypeToSubLengthMap.get(queryDTO.getTermType());
        Map<String, List<Map<String, Object>>> controlDataListMapByTimekey = controlDataListMap.stream().collect(Collectors.groupingBy(map -> map.get("timekey").toString().substring(0, timkeySubLength)));
        for (String timekey : controlDataListMapByTimekey.keySet()) {
            try {
                List<Map<String, Object>> controlDataList = controlDataListMapByTimekey.get(timekey);
                // 2:controlDataListMap 以dcDataId分组并将result生成List<double[]>
                List<double[]> subgroups = controlDataList.stream().collect(Collectors.groupingBy(
                        map -> map.get("dcDataId"))).values().stream().map(
                        maps -> maps.stream().mapToDouble
                                (map -> Double.parseDouble(map.get("result").toString())).toArray()).toList();
                // 3:计算趋势
                SPCCalResult spcCalResult = SpcCalculatorProxy.getSpcCalculator(chartDefinition.getControlChartType()).calculateTrend(
                        subgroups, Double.parseDouble(chartDefinition.getUpperSpecLimit()), Double.parseDouble(chartDefinition.getLowerSpecLimit()));
                SpcTrendDTO spcTrendDTO = new SpcTrendDTO();
                BeanUtils.copyNotEmptyProperties(spcCalResult, spcTrendDTO);
                spcTrendDTO.setCovTarget(NumberUtil.getNumberByString(chartDefinition.getCovTarget(), Double.class));
                spcTrendDTO.setCpkTarget(NumberUtil.getNumberByString(chartDefinition.getCpkTarget(), Double.class));
                spcTrendDTO.setMeanShiftTarget(NumberUtil.getNumberByString(chartDefinition.getMeanShiftTarget(), Double.class));
                spcTrendDTO.setTerm(timekey);
                spcTrendDTOList.add(spcTrendDTO);
            } catch (Exception e) {
                log.error("计算过程趋势异常", e.getMessage());
                continue;
            }
        }
        result.setSpcTrendDTOList(spcTrendDTOList);
        return result;
    }

    /**
     * 根据控制图名称分别获取主图和子图的异常数量
     * 1: 查询主图
     * 2：查询子图
     */
    public SpcAlarmCountListDTO alarmCount(ViewChartQueryDTO queryDTO) {
        ChartDefinition chartDefinition = checkChartDef(queryDTO.getChartName());
        // 根据不同的term,统计AlarmData数量，生成这个SQL
        List<SpcAlarmCountDTO> mainAlarm = this.queryAlarmData(queryDTO, ChartMainSubTypeEnum.Enum.Main.name());
        List<SpcAlarmCountDTO> subAlarm = this.queryAlarmData(queryDTO, ChartMainSubTypeEnum.Enum.Sub.name());
        // 如果List为空着构造默认显示，防止报表前端报错
        String nowTerm = TimeStampUtil.getCurrentTime(TimeStampUtil.FORMAT_DAY);
        if (CollectionUtils.isEmpty(mainAlarm)) {
            mainAlarm = Arrays.asList(new SpcAlarmCountDTO(nowTerm, 0));
        }
        if (CollectionUtils.isEmpty(subAlarm)) {
            subAlarm = Arrays.asList(new SpcAlarmCountDTO(nowTerm, 0));
        }
        SpcAlarmCountListDTO result = new SpcAlarmCountListDTO(mainAlarm, subAlarm);
        return result;
    }

    /*
    // 根据不同的term,统计AlarmData数量
     */
    private List<SpcAlarmCountDTO> queryAlarmData(ViewChartQueryDTO queryDTO, String chartMainSubType) {
        String sql = "SELECT" +
                "    LEFT(A.timekey, :timkeySubLength) AS term," +
                "    COUNT(A.*) AS count" +
                " FROM" +
                "    alarm_data A" +
                " join chart_control CC on CC.id = A.chart_control_id" +
                " join chart_definition cd on cd.id = cc.chart_definition_id" +
                " where cd.chart_name = :chartName" +
                " and cc.chart_main_sub_type = :chartMainSubType";
        if (Objects.isNull(queryDTO.getLimitCount())) {
            sql += (" and A.timekey > :startTime" +
                    " and A.timekey < :endTime" +
                    " GROUP BY" +
                    "    term" +
                    " ORDER BY term"
            );
        } else if (Objects.nonNull(queryDTO.getLimitCount())) {
            sql += (
                    " group by term " +
                            " order by term " +
                            " limit :limitCount"
            );
        }
        int timkeySubLength = ConstantMap.TermTypeToSubLengthMap.get(queryDTO.getTermType());
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("timkeySubLength", timkeySubLength);
        paramMap.put("chartName", queryDTO.getChartName());
        paramMap.put("startTime", queryDTO.getStartTime());
        paramMap.put("endTime", queryDTO.getEndTime());
        paramMap.put("chartMainSubType", chartMainSubType);
        paramMap.put("limitCount", queryDTO.getLimitCount());
        List<Map<String, Object>> controlDataListMap = fabosJsonDao.getNamedParameterJdbcTemplate().queryForList(sql, paramMap);
        List<SpcAlarmCountDTO> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(controlDataListMap)) {
            return result;
        }
        for (Map<String, Object> map : controlDataListMap) {
            SpcAlarmCountDTO spcAlarmCountDTO = new SpcAlarmCountDTO();
            spcAlarmCountDTO = ObjectMapperProxy.getObjectMapper().convertValue(map, spcAlarmCountDTO.getClass());
            result.add(spcAlarmCountDTO);
        }
        return result;
    }

    public ChartDefinition checkChartDef(String chartName) {
        ChartDefinition chartDefinition = chartControlService.getChartDefinitionByChartName(chartName);
        if (Objects.isNull(chartDefinition)) {
            throw new FabosJsonApiErrorTip("控制图：" + chartName + "不存在");
        }
        return chartDefinition;
    }

    public NormalDistributionChart calNormalDis(ViewChartQueryDTO queryDTO) {
        ChartDefinition chartDefinition = chartControlService.getChartDefinitionByChartName(queryDTO.getChartName());
        if (Objects.isNull(chartDefinition)) {
            throw new FabosJsonApiErrorTip("控制图：" + queryDTO.getChartName() + "不存在");
        }
        //1: 查询原始DCData
        List<double[]> subgroups = this.getSubgroupsByDCData(queryDTO);
        //2: 计算过程能力并返回
        Double usl = Double.parseDouble(chartDefinition.getUpperSpecLimit());
        Double lsl = Double.parseDouble(chartDefinition.getLowerSpecLimit());
        NormalDistributionChart chart = SpcCalculatorProxy.getSpcCalculator(chartDefinition.getControlChartType()).calculateNormalDis(
                subgroups, usl, lsl);
        // 配合前端展示，将USL和LSL视为两个点加在全局正态图中
        NormalDistributionPoint uslPoint = new NormalDistributionPoint(usl, 0,0, 0);
        NormalDistributionPoint lslPoint = new NormalDistributionPoint(lsl, 0,0, 0);
/*        List<NormalDistributionPoint> overallNormalDisChart = chart.getOverallNormalDisChart();
        if (CollectionUtils.isEmpty(overallNormalDisChart)) {
            return chart;
        }
        overallNormalDisChart.add(uslPoint);
        overallNormalDisChart.add(lslPoint);*/
        return chart;
    }

    /*
    校验控制限，根据子图来计算
     */
    public void controlLimitCheck(List<ChartControl> chartControlList, DCData dcData, List<DCDataResult> deriveDcDataResultList) {
        if (!checkChartControlAndData(chartControlList, deriveDcDataResultList)) {
            return;
        }
        List<AlarmData> alarmDataList = new ArrayList<>();
        //根据每个控制图做控制限校验
        for (ChartControl chartControl : chartControlList) {
            String uclStr = chartControl.getUpperControlLimit();
            String lclStr = chartControl.getLowerControlLimit();
            if (StringUtils.isBlank(uclStr) || StringUtils.isBlank(lclStr)) {
                log.error("控制图：" + chartControl.getChartName() + "控制限为空");
                continue;
            }
            double ucl = Double.parseDouble(chartControl.getUpperControlLimit());
            double lcl = Double.parseDouble(chartControl.getLowerControlLimit());
            for (DCDataResult dcDataResult : deriveDcDataResultList) {
                double value = Double.parseDouble(dcDataResult.getResult());
                if (SpecCheckUtil.checkSpecLimit(ucl, lcl, value)) {
                    // 生成AlarmData
                    AlarmData alarmData = new AlarmData();
                    alarmData.setTimekey(dcData.getTimekey());
                    alarmData.setChartDef(chartControl.getChartDefinition());
                    alarmData.setChartControl(chartControl);
                    alarmData.setChartControlType(chartControl.getChartDefinition().getControlChartType());
                    alarmData.setAlarmType(AlarmTypeEnum.Enum.OOC.name());
                    alarmData.setResultValue(value);
                    alarmDataList.add(alarmData);
                }
            }
        }
        if (CollectionUtils.isEmpty(alarmDataList)) {
            return;
        }
        fabosJsonDao.insertBatch(alarmDataList);
    }

    public Boolean checkChartControlAndData(List<ChartControl> chartControlList, List<DCDataResult> deriveDcDataResultList) {
        if (CollectionUtils.isEmpty(chartControlList)) {
            log.error(" chartControlList 为空");
            return false;
        }
        if (CollectionUtils.isEmpty(deriveDcDataResultList)) {
            log.error(" dcDataResultList 为空");
            return false;
        }
        return true;
    }

    public HomePageSetting saveHomePageSetting(HomePageSettingDTO homePageSettingDTO) {
        if (StringUtils.isNotBlank(homePageSettingDTO.getId())) {
            HomePageSetting homePageSetting = fabosJsonDao.findById(HomePageSetting.class, homePageSettingDTO.getId());
            if (homePageSetting != null) {
                homePageSetting.setRowCount(homePageSettingDTO.getRowCount());
                homePageSetting.setColumnCount(homePageSettingDTO.getColumnCount());
                homePageSetting.setTotalCount(homePageSettingDTO.getTotalCount());
                return fabosJsonDao.mergeAndFlush(homePageSetting);
            } else {
                throw new FabosJsonApiErrorTip("记录不存在!");
            }
        } else {
            return fabosJsonDao.mergeAndFlush(
                    HomePageSetting.builder()
                            .rowCount(homePageSettingDTO.getRowCount())
                            .columnCount(homePageSettingDTO.getColumnCount())
                            .totalCount(homePageSettingDTO.getTotalCount())
                            .build()
            );
        }
    }

    public ChartControlSetting mergeChartControlSetting(ChartControlSettingDTO chartControlSettingDTO) {
        if (StringUtils.isNotBlank(chartControlSettingDTO.getId())) {
            ChartControlSetting chartControlSetting = fabosJsonDao.findById(ChartControlSetting.class, chartControlSettingDTO.getId());
            if (chartControlSetting != null) {
                chartControlSetting.setTitle(chartControlSettingDTO.getTitle());
                chartControlSetting.setChartDefinitionId(chartControlSettingDTO.getChartDefinitionId());
                chartControlSetting.setViewChartType(chartControlSettingDTO.getViewChartType());
                chartControlSetting.setRefreshInterval(chartControlSettingDTO.getRefreshInterval());
                chartControlSetting.setLatestPointCount(chartControlSettingDTO.getLatestPointCount());
                return fabosJsonDao.mergeAndFlush(chartControlSetting);
            } else {
                throw new FabosJsonApiErrorTip("记录不存在!");
            }
        } else {
            return fabosJsonDao.mergeAndFlush(
                    ChartControlSetting.builder()
                            .title(chartControlSettingDTO.getTitle())
                            .chartDefinitionId(chartControlSettingDTO.getChartDefinitionId())
                            .viewChartType(chartControlSettingDTO.getViewChartType())
                            .refreshInterval(chartControlSettingDTO.getRefreshInterval())
                            .latestPointCount(chartControlSettingDTO.getLatestPointCount())
                            .build()
            );
        }
    }
}
