package cec.jiutian.dowork.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 派生数据接收DTO,计算控制限和能力指数时使用
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ControlDataReceiveDTO {
    /**
     * 图名
     */
    @ApiModelProperty(value = "图名" , required = false)
    private String chartName;

    /**
     * 子图名
     */
    private String subChartName;

    /**
     * XBAR的值
     */
    private Double XBAR;

    /**
     * MAX的值
     */
    private Double MAX;

    /**
     * MIN的值
     */
    private Double MIN;

    /**
     * R的值
     */
    private Double R;

    /**
     * S
     */
    private Double S;

    /**
     * U1的值
     */
    private Double U1;

    /**
     * U2的值
     */
    private Double U2;

    /**
     * I的值
     */
    private Double I;

    /**
     * MR的值
     */
    private Double MR;

    /**
     * 平方和的值
     */
    private Double squarSum;

    /**
     * 记录条数
     */
    private Double resultCount;

    /**
     * 样本容量
     */
    private Double subGroupSize;

    /**
     * 所有样本总的值
     */
    private Double sum;

    /**
     * 总的平均
     */
    private Double sumAvg;

    /**
     * 总的点数
     */
    private Double siteCountSum;


}
