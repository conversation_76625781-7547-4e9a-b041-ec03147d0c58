package cec.jiutian.dowork.engine;

import cec.jiutian.bc.capability.domain.calculate.model.CapabilityData;
import cec.jiutian.bc.chartControl.domain.controlData.model.ControlLimitData;
import cec.jiutian.bc.spc.engine.capability.CapabilityBig;
import cec.jiutian.bc.spc.engine.capability.impl.MCCapabilityCalculatorBig;
import cec.jiutian.bc.spc.engine.controllimit.ControlChartControlLimit;
import cec.jiutian.bc.spc.engine.controllimit.impl.*;
import cec.jiutian.bc.spc.engine.model.*;
import cec.jiutian.bc.spc.engine.model.statistics.*;
import cec.jiutian.util.common.CommonUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;


@Component
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Slf4j
public class SpcCalculator {
	
	private AbstractControlLimitCalculator limit;
	
	public AbstractStatisticsData datas;
	
	
	private String converting(String original){
		
		return original.substring(0, 1).toLowerCase() + original.substring(1) + "ControlChart";
	
	}
	
	
	/**
	 * 创建能力数据对象
	 * 该方法根据给定的参数计算过程能力指标，如CP、CPK等，并将这些指标封装到CapabilityData对象中
	 *
	 * @param type 能力类型，用于区分不同的计算场景
	 * @param spec 规格对象，包含规格上限和下限等信息
	 * @param subGroupSize 子组大小，用于计算过程能力
	 * @param dataCount 数据总数，用于计算过程能力
	 * @param siteCountSum 工位总数，可能用于特定的计算场景
	 * @param sumOfSum 所有数据的总和，用于计算过程能力
	 * @param sumOfSquareSum 所有数据平方的总和，用于计算过程能力
	 * @param xbarAvg Xbar的平均值，即所有子组平均值的平均
	 * @param sAvg 标准差的平均值，用于计算过程能力
	 * @param rAvg 极差的平均值，用于计算过程能力
	 * @param sumAvg 总和的平均值，可能用于特定的计算场景
	 * @return 返回一个包含计算出的能力指标的CapabilityData对象
	 */
	public CapabilityData CreateCapabilityData(String type, SpecBig spec, int subGroupSize, int dataCount, int siteCountSum,
											   BigDecimal sumOfSum, BigDecimal sumOfSquareSum, BigDecimal xbarAvg, BigDecimal sAvg, BigDecimal rAvg, BigDecimal sumAvg) {
		// 创建CapabilityData对象，用于存储计算结果
		CapabilityData result = new CapabilityData();
		// 创建能力计算器对象
		MCCapabilityCalculatorBig calc = new MCCapabilityCalculatorBig();
//		 CapabilityBig capability = calc.calculate(spec, subGroupSize, dataCount, sumOfSum, sumOfSquareSum, xbarAvg, sAvg, sumAvg);

		// 记录日志，跟踪函数调用参数
		log.info("CreateCapabilityData function ="+type+", "+subGroupSize+", "+dataCount+", "+siteCountSum+", "+sumOfSum+", "+sumOfSquareSum+", "+xbarAvg+", "+sAvg+", "+rAvg+","+sumAvg);

		// 根据提供的参数计算能力指标
		CapabilityBig capability = calc.calculate(type, spec, subGroupSize, dataCount, siteCountSum, sumOfSum, sumOfSquareSum, xbarAvg, sAvg, rAvg, sumAvg);

		// 根据计算结果，设置CapabilityData对象的属性
		if (CommonUtil.hasValue(capability.getCp()))
			result.setCp(CommonUtil.getPlainNumeric(CommonUtil.roundValue(capability.getCp())));
		if (CommonUtil.hasValue(capability.getCpk()))
			result.setCpk(CommonUtil.getPlainNumeric(CommonUtil.roundValue(capability.getCpk())));
		if (CommonUtil.hasValue(capability.getCpm()))
			result.setCpm(CommonUtil.getPlainNumeric(CommonUtil.roundValue(capability.getCpm())));
		if (CommonUtil.hasValue(capability.getPp()))
			result.setPp(CommonUtil.getPlainNumeric(CommonUtil.roundValue(capability.getPp())));
		if (CommonUtil.hasValue(capability.getPpk()))
			result.setPpk(CommonUtil.getPlainNumeric(CommonUtil.roundValue(capability.getPpk())));
		return result;
	}


	/**
	 * 创建控制限制数据对象，适用于 Xbar, R, S, X, mR, MR, LS, U1, U2 等图
	 * 该方法根据提供的参数计算控制图的控制限制，并将这些限制设置到ControlLimitData对象中
	 *
	 * @param controlChartType 控制图类型，用于标识控制图的种类
	 * @param chartName 控制图名称，支持的名称有Xbar, R, S, X, mR, MR, LS, U1, U2等
	 * @param subGroupSize 子组大小，用于计算控制限制
	 * @param xbar 子组平均值
	 * @param max 最大值
	 * @param min 最小值
	 * @param R 极差
	 * @param S 标准差
	 * @param U1 上限1
	 * @param U2 上限2
	 * @param I 个体值
	 * @param mR 移动极差
	 * @return ControlLimitData对象，包含计算出的控制限制数据
	 */
	public ControlLimitData CreateControlLimitData(String controlChartType, String chartName, int subGroupSize,
												   BigDecimal xbar, BigDecimal max, BigDecimal min, BigDecimal R, BigDecimal S, BigDecimal U1, BigDecimal U2, BigDecimal I, BigDecimal mR) {
		ControlLimitData result = new ControlLimitData();
		MCControlLimitCalculator calc = new MCControlLimitCalculator();
		
		ControlLimitBig controlLimits;
		controlLimits = calc.doCalculate(chartName, subGroupSize, xbar, max, min, R, S, U1, U2, I, mR);
		result.setControlChartType(controlChartType);
		result.setChartName(chartName);
		
		if (CommonUtil.hasValue(controlLimits.getCenter()))
			result.setCentralControlLine(CommonUtil.getPlainNumeric(CommonUtil.roundValue(controlLimits.getCenter())));
		if (CommonUtil.hasValue(controlLimits.getLower())) 
			result.setLowerControlLimit(CommonUtil.getPlainNumeric(CommonUtil.roundValue(controlLimits.getLower())));
		if (CommonUtil.hasValue(controlLimits.getUpper())) 
			result.setUpperControlLimit(CommonUtil.getPlainNumeric(CommonUtil.roundValue(controlLimits.getUpper())));
		
		return result;
	}
	
	/**
	 * 创建控制限缺陷数据，适用于 P, U, NP, C 等图
	 *
	 * 根据控制图类型、图表名称、子组大小和缺陷平均值来创建控制限数据
	 * 这个方法主要用于质量控制中，用来定义产品或服务的缺陷控制限
	 *
	 * @param controlChartType 控制图类型，指明所使用的控制图种类
	 * @param chartName 图表名称，用于标识控制图
	 * @param subGroupSize 子组大小，表示每次抽样检验的产品数量
	 * @param defecAvg 缺陷平均值，表示抽样检验中发现的平均缺陷数
	 * @return ControlLimitData 返回一个包含控制限数据的对象
	 */
	public ControlLimitData CreateControlLimitDefecData(String controlChartType,String chartName, int subGroupSize, double defecAvg) {
		ControlLimitData result = new ControlLimitData();
		MCControlLimitCalculator calc = new MCControlLimitCalculator();
		
		ControlLimit controlLimits;
		controlLimits = calc.doCalculateDefec(chartName, subGroupSize, defecAvg);
		result.setControlChartType(controlChartType);
		result.setChartName(chartName);
		
		if (CommonUtil.hasValue(controlLimits.getCenter()))
			result.setCentralControlLine(CommonUtil.getPlainNumeric(CommonUtil.roundValue(controlLimits.getCenter())));
		if (CommonUtil.hasValue(controlLimits.getLower())) 
			result.setLowerControlLimit(CommonUtil.getPlainNumeric(CommonUtil.roundValue(controlLimits.getLower())));
		if (CommonUtil.hasValue(controlLimits.getUpper())) 
			result.setUpperControlLimit(CommonUtil.getPlainNumeric(CommonUtil.roundValue(controlLimits.getUpper())));
		
		return result;
	}
	
	public void init(String chartName, String controlChartType){
		
				
		if(controlChartType.equals(ControlChartType.XbarR)){
			limit = new XbarRControlLimitCalculator();
			datas = new XbarRStatisticsData();
		}else if(controlChartType.equals(ControlChartType.Xbar)){
			limit = new XbarControlLimitCalculator();
			datas = new XbarSStatisticsData();
		}else if(controlChartType.equals(ControlChartType.R)){
			limit = new RControlLimitCalculator();
			datas = new RStatisticsData();
		}else if(controlChartType.equals(ControlChartType.XbarS)){
			limit = new XbarSControlLimitCalculator();
			datas = new XbarSStatisticsData();
		}else if(controlChartType.equals(ControlChartType.S)){
			limit = new XbarSControlLimitCalculator();
			datas = new SStatisticsData();
		}else if(controlChartType.equals(ControlChartType.XmR)){
			limit = new XmRControlLimitCalculator();
			datas = new XmRStatisticsData();
		}else if(controlChartType.equals(ControlChartType.X)){
			limit = new XControlLimitCalculator();
			datas = new XStatisticsData();
		}else if(controlChartType.equals(ControlChartType.mR)){
			limit = new mRControlLimitCalculator();
			datas = new mRStatisticsData();
		}else if(controlChartType.equals(ControlChartType.XbarMR)){
			limit = new XbarMRControlLimitCalculator();
			datas = new XbarMRStatisticsData();
		}else if(controlChartType.equals(ControlChartType.MR)){
			limit = new MmRControlLimitCalculator();
			datas = new TempStatisticsData();
		}else if(controlChartType.equals(ControlChartType.LSR)){
			limit = new LSRControlLimitCalculator();
			datas = new LSRStatisticsData();
		}else if(controlChartType.equals(ControlChartType.LS)){
			limit = new LSRControlLimitCalculator();
			datas = new LSRStatisticsData();
		}else if(controlChartType.equals(ControlChartType.P)){
			limit = new PControlLimitCalculator();
			datas = new PStatisticsData();
		}else if(controlChartType.equals(ControlChartType.U)){
			limit = new UControlLimitCalculator();
			datas = new UStatisticsData();
		}else if(controlChartType.equals(ControlChartType.NP)){
			limit = new NPControlLimitCalculator();
			datas = new NPStatisticsData();
		}else if(controlChartType.equals(ControlChartType.C)){
			limit = new CControlLimitCalculator();
			datas = new CStatisticsData();
		}else{
			log.info("undefine chart : " + this.converting(controlChartType));
		}
	}
	
	public ControlChartControlLimit doCalculator(int subGroupSize, List<Data<?>> data){
		datas.addAll(data);
		return limit.calculate(subGroupSize, datas);
	}
}
