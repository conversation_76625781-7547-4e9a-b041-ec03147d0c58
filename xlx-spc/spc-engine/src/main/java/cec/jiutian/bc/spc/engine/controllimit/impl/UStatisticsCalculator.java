package cec.jiutian.bc.spc.engine.controllimit.impl;

import cec.jiutian.bc.spc.engine.model.statistics.UData;

import java.util.Iterator;
import java.util.List;

class UStatisticsCalculator {
    double count;
    double sumOfDefectsPerUnit;

    UStatisticsCalculator(List<UData> datas) {
        this.count = (double)datas.size();

        UData data;
        for(Iterator var3 = datas.iterator(); var3.hasNext(); this.sumOfDefectsPerUnit += data.getDefectsPerUnit()) {
            data = (UData)var3.next();
        }

    }

    double Ubar() {
        return this.sumOfDefectsPerUnit / this.count;
    }
}
