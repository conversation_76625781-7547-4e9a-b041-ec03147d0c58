package cec.jiutian.bc.spc.engine.infra;

import cec.jiutian.bc.spc.engine.capability.CapabilityCalculator;
import cec.jiutian.bc.spc.engine.model.ControlChartType;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.BeanFactoryUtils;
import org.springframework.beans.factory.ListableBeanFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Iterator;
import java.util.Map;

@Component
public class CapabilityCalculatorHolder {
    private Map<ControlChartType, CapabilityCalculator<?>> calcMap = Maps.newHashMap();

    @Autowired
    public CapabilityCalculatorHolder(ListableBeanFactory beanFactory) {
        Map<String, CapabilityCalculator> calcBeans = BeanFactoryUtils.beansOfTypeIncludingAncestors(beanFactory, CapabilityCalculator.class);
        Iterator var4 = calcBeans.values().iterator();

        while(var4.hasNext()) {
            CapabilityCalculator<?> calc = (CapabilityCalculator)var4.next();
            this.calcMap.put(calc.controlChartType(), calc);
        }

    }

    public CapabilityCalculator getCapabilityCalculator(ControlChartType type) throws IllegalArgumentException {
        if (!this.calcMap.containsKey(type)) {
            throw new IllegalArgumentException(type + " is not supported");
        } else {
            return (CapabilityCalculator)this.calcMap.get(type);
        }
    }
}
