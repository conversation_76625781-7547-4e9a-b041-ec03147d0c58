package cec.jiutian.bc.spc.engine.controllimit.impl;


import cec.jiutian.bc.spc.engine.model.statistics.NPData;

import java.util.Iterator;
import java.util.List;

class NPStatisticsCalculator {
    double count;
    double subGroupSize;
    double sumOfDefectiveCount;

    NPStatisticsCalculator(int subGroupSize, List<NPData> datas) {
        this.count = (double)datas.size();
        this.subGroupSize = (double)subGroupSize;

        NPData data;
        for(Iterator var4 = datas.iterator(); var4.hasNext(); this.sumOfDefectiveCount += data.getDefectiveCount()) {
            data = (NPData)var4.next();
        }

    }

    double NPbar() {
        return this.sumOfDefectiveCount / this.count;
    }

    double Pbar() {
        return this.sumOfDefectiveCount / (this.count * this.subGroupSize);
    }
}

