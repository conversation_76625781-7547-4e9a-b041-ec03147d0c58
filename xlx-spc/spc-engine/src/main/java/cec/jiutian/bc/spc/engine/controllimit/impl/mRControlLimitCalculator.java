package cec.jiutian.bc.spc.engine.controllimit.impl;


import cec.jiutian.bc.spc.engine.controllimit.ControlChartControlLimit;
import cec.jiutian.bc.spc.engine.controllimit.impl.factor.FactorType;
import cec.jiutian.bc.spc.engine.model.ControlChartType;
import cec.jiutian.bc.spc.engine.model.ControlLimit;
import cec.jiutian.bc.spc.engine.model.StatisticsData;
import cec.jiutian.bc.spc.engine.model.statistics.mRData;
import org.springframework.stereotype.Component;

import java.util.Iterator;
import java.util.List;

@Component
public class mRControlLimitCalculator extends AbstractControlLimitCalculator<mRData> {
    public mRControlLimitCalculator() {
    }

    public ControlChartType controlChartType() {
        return ControlChartType.mR;
    }

    public ControlChartControlLimit doCalculate(int subGroupSize, StatisticsData<mRData> statsData) {
        ControlChartControlLimit controlLimits = new ControlChartControlLimit();
        if (statsData.size() <= 1) {
            controlLimits.addControlLimit(ControlChartType.mR, new ControlLimit(0.0, 0.0, 0.0));
            return controlLimits;
        } else {
            mRStatisticsCalculator stats = new mRStatisticsCalculator(statsData.getDatas());
            double mR_cl = stats.mRbar();
            double mR_ucl = stats.D4() * stats.mRbar();
            double mR_lcl = stats.D3() * stats.mRbar();
            controlLimits.addControlLimit(ControlChartType.mR, new ControlLimit(mR_ucl, mR_cl, mR_lcl));
            return controlLimits;
        }
    }

    class mRStatisticsCalculator {
        double count;
        double sumOfMovingRange;

        mRStatisticsCalculator(List<mRData> datas) {
            Iterator var4 = datas.iterator();

            while(var4.hasNext()) {
                mRData data = (mRData)var4.next();
                ++this.count;
                if (this.count != 1.0) {
                    this.sumOfMovingRange += data.getMovingRange();
                }
            }

        }

        double mRbar() {
            return this.sumOfMovingRange / (this.count - 1.0);
        }

        double D4() {
            return mRControlLimitCalculator.this.factorHolder.getFactor(FactorType.D4, 2);
        }

        double D3() {
            return mRControlLimitCalculator.this.factorHolder.getFactor(FactorType.D3, 2);
        }
    }
}

