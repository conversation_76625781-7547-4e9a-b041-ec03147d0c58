package cec.jiutian.bc.spc.engine.controllimit.impl;


import cec.jiutian.bc.spc.engine.controllimit.impl.factor.FactorHolder;
import cec.jiutian.bc.spc.engine.controllimit.impl.factor.FactorType;
import cec.jiutian.bc.spc.engine.model.ControlLimit;
import cec.jiutian.bc.spc.engine.model.ControlLimitBig;
import cec.jiutian.bc.spc.engine.util.DoubleCaculate;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
@Data
@AllArgsConstructor
@NoArgsConstructor
@Component
public class MCControlLimitCalculator2<T extends Number> {
    protected FactorHolder factorHolder;
    private T Xbar;
    private T Max;
    private T Min;
    private T R;
    private T S;
    private T U1;
    private T U2;
    private T I;
    private T MR;
    private T Target;

    public ControlLimitBig doCalculate(String ControlLimitType, String chartName, int subGroupSize, T Xbar, T Max, T Min, T R, T S, T U1, T U2, T I, T MR, T Target) {
        MCStatisticsCalculator stats = new MCStatisticsCalculator(subGroupSize, chartName);
        BigDecimal cl = new BigDecimal(0.0);
        BigDecimal ucl = new BigDecimal(0.0);
        BigDecimal lcl = new BigDecimal(0.0);
        ControlLimitBig controlLimits;
        if (chartName.equalsIgnoreCase("Xbar")) {
            cl = BigDecimal.valueOf((Long)Xbar);
            ucl = BigDecimal.valueOf((Long)Xbar).add(BigDecimal.valueOf((Long)S).multiply(BigDecimal.valueOf(stats.factorUCL())));
            lcl = BigDecimal.valueOf((Long)Xbar).subtract(BigDecimal.valueOf((Long)S).multiply(BigDecimal.valueOf(stats.factorLCL())));
            controlLimits = new ControlLimitBig(ucl, cl, lcl);
        } else if (chartName.equalsIgnoreCase("R")) {
            cl = BigDecimal.valueOf((Long)R);
            ucl = BigDecimal.valueOf((Long)R).multiply(BigDecimal.valueOf(stats.factorUCL()));
            lcl = BigDecimal.valueOf((Long)R).multiply(BigDecimal.valueOf(stats.factorLCL()));
            controlLimits = new ControlLimitBig(ucl, cl, lcl);
        } else if (chartName.equalsIgnoreCase("S")) {
            cl = BigDecimal.valueOf((Long)S);
            ucl = BigDecimal.valueOf((Long)S).multiply(BigDecimal.valueOf(stats.factorUCL()));
            lcl = BigDecimal.valueOf((Long)S).multiply(BigDecimal.valueOf(stats.factorLCL()));
            controlLimits = new ControlLimitBig(ucl, cl, lcl);
        } else if (!chartName.equalsIgnoreCase("X") && !chartName.equalsIgnoreCase("I")) {
            if (chartName.equals("mR")) {
                cl = BigDecimal.valueOf((Long)MR);
                ucl = BigDecimal.valueOf((Long)MR).multiply(BigDecimal.valueOf(stats.factorUCL()));
                lcl = BigDecimal.valueOf((Long)MR).multiply(BigDecimal.valueOf(stats.factorLCL()));
                controlLimits = new ControlLimitBig(ucl, cl, lcl);
            } else if (chartName.equals("MR")) {
                cl = BigDecimal.valueOf((Long)MR);
                ucl = BigDecimal.valueOf((Long)MR).multiply(BigDecimal.valueOf(stats.factorUCL()));
                lcl = BigDecimal.valueOf((Long)MR).multiply(BigDecimal.valueOf(stats.factorLCL()));
                controlLimits = new ControlLimitBig(ucl, cl, lcl);
            } else if (!chartName.equalsIgnoreCase("LS") && !chartName.equalsIgnoreCase("Min") && !chartName.equalsIgnoreCase("Max")) {
                if (chartName.equalsIgnoreCase("U1")) {
                    cl = BigDecimal.valueOf((Long)U1);
                    ucl = BigDecimal.valueOf((Long)U1).multiply(BigDecimal.valueOf(stats.factorUCL()));
                    lcl = BigDecimal.valueOf((Long)U1).multiply(BigDecimal.valueOf(stats.factorLCL()));
                    controlLimits = new ControlLimitBig(ucl, cl, lcl);
                } else if (chartName.equalsIgnoreCase("U2")) {
                    cl = BigDecimal.valueOf((Long)U2);
                    ucl = BigDecimal.valueOf((Long)U2).multiply(BigDecimal.valueOf(stats.factorUCL()));
                    lcl = BigDecimal.valueOf((Long)U2).multiply(BigDecimal.valueOf(stats.factorLCL()));
                    controlLimits = new ControlLimitBig(ucl, cl, lcl);
                } else {
                    controlLimits = new ControlLimitBig(ucl, cl, lcl);
                }
            } else {
                cl = BigDecimal.valueOf((Long)Max).add(BigDecimal.valueOf((Long)Min)).divide(BigDecimal.valueOf(2.0));
                ucl = cl.add(BigDecimal.valueOf((Long)R).multiply(BigDecimal.valueOf(stats.factorUCL())));
                lcl = cl.subtract(BigDecimal.valueOf((Long)R).multiply(BigDecimal.valueOf(stats.factorLCL())));
                controlLimits = new ControlLimitBig(ucl, cl, lcl);
            }
        } else {
            cl = BigDecimal.valueOf((Long)I);
            ucl = BigDecimal.valueOf((Long)I).add(BigDecimal.valueOf((Long)S).multiply(BigDecimal.valueOf(stats.factorUCL())));
            lcl = BigDecimal.valueOf((Long)I).subtract(BigDecimal.valueOf((Long)S).multiply(BigDecimal.valueOf(stats.factorLCL())));
            controlLimits = new ControlLimitBig(ucl, cl, lcl);
        }

        return controlLimits;
    }

    public ControlLimit doCalculateN(String ControlLimitType, String chartName, int subGroupSize, T Xbar, T Max, T Min, T R, T S, T U1, T U2, T I, T MR) {
        MCStatisticsCalculator stats = new MCStatisticsCalculator(subGroupSize, chartName);
        Double cl = 0.0;
        Double ucl = 0.0;
        Double lcl = 0.0;
        ControlLimit controlLimits;
        if (chartName.equalsIgnoreCase("Xbar")) {
            cl = (Double)Xbar;
            ucl = (Double)Xbar + stats.factorUCL() * (Double)S;
            lcl = (Double)Xbar - stats.factorLCL() * (Double)S;
            controlLimits = new ControlLimit(ucl, cl, lcl);
        } else if (chartName.equalsIgnoreCase("R")) {
            cl = (Double)R;
            ucl = stats.factorUCL() * (Double)R;
            lcl = stats.factorLCL() * (Double)R;
            controlLimits = new ControlLimit(ucl, cl, lcl);
        } else if (chartName.equalsIgnoreCase("S")) {
            cl = (Double)S;
            ucl = stats.factorUCL() * (Double)S;
            lcl = stats.factorLCL() * (Double)S;
            controlLimits = new ControlLimit(ucl, cl, lcl);
        } else if (!chartName.equalsIgnoreCase("X") && !chartName.equalsIgnoreCase("I")) {
            if (chartName.equals("mR")) {
                cl = (Double)MR;
                ucl = stats.factorUCL() * (Double)MR;
                lcl = stats.factorLCL() * (Double)MR;
                controlLimits = new ControlLimit(ucl, cl, lcl);
            } else if (chartName.equals("MR")) {
                cl = (Double)MR;
                ucl = stats.factorUCL() * (Double)MR;
                lcl = stats.factorLCL() * (Double)MR;
                controlLimits = new ControlLimit(ucl, cl, lcl);
            } else if (!chartName.equalsIgnoreCase("LS") && !chartName.equalsIgnoreCase("Min") && !chartName.equalsIgnoreCase("Max")) {
                if (chartName.equalsIgnoreCase("U1")) {
                    cl = (Double)U1;
                    ucl = stats.factorUCL() * (Double)U1;
                    lcl = stats.factorLCL() * (Double)U1;
                    controlLimits = new ControlLimit(ucl, cl, lcl);
                } else if (chartName.equalsIgnoreCase("U2")) {
                    cl = (Double)U2;
                    ucl = stats.factorUCL() * (Double)U2;
                    lcl = stats.factorLCL() * (Double)U2;
                    controlLimits = new ControlLimit(ucl, cl, lcl);
                } else {
                    controlLimits = new ControlLimit(ucl, cl, lcl);
                }
            } else {
                cl = ((Double)Max + (Double)Min) / 2.0;
                ucl = cl + stats.factorUCL() * (Double)R;
                lcl = cl - stats.factorLCL() * (Double)R;
                controlLimits = new ControlLimit(ucl, cl, lcl);
            }
        } else {
            cl = (Double)I;
            ucl = (Double)I + stats.factorUCL() * (Double)S;
            lcl = (Double)I - stats.factorLCL() * (Double)S;
            controlLimits = new ControlLimit(ucl, cl, lcl);
        }

        return controlLimits;
    }

    public ControlLimit doCalculate(String ControlLimitType, String chartName, int subGroupSize, T Xbar, T Max, T Min, T R, T S, T U1, T U2, T I, T MR) {
        MCStatisticsCalculator stats = new MCStatisticsCalculator(subGroupSize, chartName);
        Double cl = 0.0;
        Double ucl = 0.0;
        Double lcl = 0.0;
        DoubleCaculate cal = new DoubleCaculate();
        ControlLimit controlLimits;
        if (chartName.equalsIgnoreCase("Xbar")) {
            cl = (Double)Xbar;
            ucl = cal.safelyAdd((Double)Xbar, cal.safelyMultiply(stats.factorUCL(), (Double)S));
            lcl = cal.safelySubtract((Double)Xbar, cal.safelyMultiply(stats.factorLCL(), (Double)S));
            controlLimits = new ControlLimit(ucl, cl, lcl);
        } else if (chartName.equalsIgnoreCase("R")) {
            cl = (Double)R;
            ucl = cal.safelyMultiply(stats.factorUCL(), (Double)R);
            lcl = cal.safelyMultiply(stats.factorLCL(), (Double)R);
            controlLimits = new ControlLimit(ucl, cl, lcl);
        } else if (chartName.equalsIgnoreCase("S")) {
            cl = (Double)S;
            ucl = cal.safelyMultiply(stats.factorUCL(), (Double)S);
            lcl = cal.safelyMultiply(stats.factorLCL(), (Double)S);
            controlLimits = new ControlLimit(ucl, cl, lcl);
        } else if (!chartName.equalsIgnoreCase("X") && !chartName.equalsIgnoreCase("I")) {
            if (chartName.equals("mR")) {
                cl = (Double)MR;
                ucl = cal.safelyMultiply(stats.factorUCL(), (Double)MR);
                lcl = cal.safelyMultiply(stats.factorLCL(), (Double)MR);
                controlLimits = new ControlLimit(ucl, cl, lcl);
            } else if (chartName.equals("MR")) {
                cl = (Double)MR;
                ucl = cal.safelyMultiply(stats.factorUCL(), (Double)MR);
                lcl = cal.safelyMultiply(stats.factorLCL(), (Double)MR);
                controlLimits = new ControlLimit(ucl, cl, lcl);
            } else if (!chartName.equalsIgnoreCase("LS") && !chartName.equalsIgnoreCase("Min") && !chartName.equalsIgnoreCase("Max")) {
                if (chartName.equalsIgnoreCase("U1")) {
                    cl = (Double)U1;
                    ucl = cal.safelyMultiply(stats.factorUCL(), (Double)U1);
                    lcl = cal.safelyMultiply(stats.factorLCL(), (Double)U1);
                    controlLimits = new ControlLimit(ucl, cl, lcl);
                } else if (chartName.equalsIgnoreCase("U2")) {
                    cl = (Double)U2;
                    ucl = cal.safelyMultiply(stats.factorUCL(), (Double)U2);
                    lcl = cal.safelyMultiply(stats.factorLCL(), (Double)U2);
                    controlLimits = new ControlLimit(ucl, cl, lcl);
                } else {
                    controlLimits = new ControlLimit(ucl, cl, lcl);
                }
            } else {
                cl = cal.safelyDivide(cal.safelyAdd((Double)Max, (Double)Min), 2.0);
                ucl = cal.safelyAdd(cl, cal.safelyMultiply(stats.factorUCL(), (Double)R));
                lcl = cal.safelySubtract(cl, cal.safelyMultiply(stats.factorLCL(), (Double)R));
                controlLimits = new ControlLimit(ucl, cl, lcl);
            }
        } else {
            cl = (Double)I;
            ucl = cal.safelyAdd((Double)I, cal.safelyMultiply(stats.factorUCL(), (Double)S));
            lcl = cal.safelySubtract((Double)I, cal.safelyMultiply(stats.factorLCL(), (Double)S));
            controlLimits = new ControlLimit(ucl, cl, lcl);
        }

        return controlLimits;
    }

    public ControlLimitBig doCalculate(String chartName, int subGroupSize, BigDecimal Xbar, BigDecimal Max, BigDecimal Min, BigDecimal R, BigDecimal S, BigDecimal U1, BigDecimal U2, BigDecimal I, BigDecimal MR) {
        MCStatisticsCalculator stats = new MCStatisticsCalculator(subGroupSize, chartName);
        BigDecimal cl = new BigDecimal(0.0);
        BigDecimal ucl = new BigDecimal(0.0);
        BigDecimal lcl = new BigDecimal(0.0);
        ControlLimitBig controlLimits;
        if (chartName.equalsIgnoreCase("Xbar")) {
            ucl = Xbar.add(S.multiply(BigDecimal.valueOf(stats.factorUCL())));
            lcl = Xbar.subtract(S.multiply(BigDecimal.valueOf(stats.factorLCL())));
            controlLimits = new ControlLimitBig(ucl, Xbar, lcl);
        } else if (chartName.equalsIgnoreCase("R")) {
            ucl = R.multiply(BigDecimal.valueOf(stats.factorUCL()));
            lcl = R.multiply(BigDecimal.valueOf(stats.factorLCL()));
            controlLimits = new ControlLimitBig(ucl, R, lcl);
        } else if (chartName.equalsIgnoreCase("S")) {
            ucl = S.multiply(BigDecimal.valueOf(stats.factorUCL()));
            lcl = S.multiply(BigDecimal.valueOf(stats.factorLCL()));
            controlLimits = new ControlLimitBig(ucl, S, lcl);
        } else if (!chartName.equalsIgnoreCase("X") && !chartName.equalsIgnoreCase("I")) {
            if (chartName.equals("mR")) {
                ucl = MR.multiply(BigDecimal.valueOf(stats.factorUCL()));
                lcl = MR.multiply(BigDecimal.valueOf(stats.factorLCL()));
                controlLimits = new ControlLimitBig(ucl, MR, lcl);
            } else if (chartName.equals("MR")) {
                ucl = MR.multiply(BigDecimal.valueOf(stats.factorUCL()));
                lcl = MR.multiply(BigDecimal.valueOf(stats.factorLCL()));
                controlLimits = new ControlLimitBig(ucl, MR, lcl);
            } else if (!chartName.equalsIgnoreCase("LS") && !chartName.equalsIgnoreCase("Min") && !chartName.equalsIgnoreCase("Max")) {
                if (chartName.equalsIgnoreCase("U1")) {
                    ucl = U1.multiply(BigDecimal.valueOf(stats.factorUCL()));
                    lcl = U1.multiply(BigDecimal.valueOf(stats.factorLCL()));
                    controlLimits = new ControlLimitBig(ucl, U1, lcl);
                } else if (chartName.equalsIgnoreCase("U2")) {
                    ucl = U2.multiply(BigDecimal.valueOf(stats.factorUCL()));
                    lcl = U2.multiply(BigDecimal.valueOf(stats.factorLCL()));
                    controlLimits = new ControlLimitBig(ucl, U2, lcl);
                } else {
                    controlLimits = new ControlLimitBig(ucl, cl, lcl);
                }
            } else {
                cl = Max.add(Min).divide(BigDecimal.valueOf(2.0));
                ucl = cl.add(R.multiply(BigDecimal.valueOf(stats.factorUCL())));
                lcl = cl.subtract(R.multiply(BigDecimal.valueOf(stats.factorLCL())));
                controlLimits = new ControlLimitBig(ucl, cl, lcl);
            }
        } else {
            ucl = I.add(S.multiply(BigDecimal.valueOf(stats.factorUCL())));
            lcl = I.subtract(S.multiply(BigDecimal.valueOf(stats.factorLCL())));
            controlLimits = new ControlLimitBig(ucl, I, lcl);
        }

        return controlLimits;
    }

    public ControlLimit doCalculateN(String chartName, int subGroupSize, double Xbar, double Max, double Min, double R, double S, double U1, double U2, double I, double MR) {
        MCStatisticsCalculator stats = new MCStatisticsCalculator(subGroupSize, chartName);
        double cl = 0.0;
        double ucl = 0.0;
        double lcl = 0.0;
        ControlLimit controlLimits;
        if (chartName.equalsIgnoreCase("Xbar")) {
            ucl = Xbar + stats.factorUCL() * S;
            lcl = Xbar - stats.factorLCL() * S;
            controlLimits = new ControlLimit(ucl, Xbar, lcl);
        } else if (chartName.equalsIgnoreCase("R")) {
            ucl = stats.factorUCL() * R;
            lcl = stats.factorLCL() * R;
            controlLimits = new ControlLimit(ucl, R, lcl);
        } else if (chartName.equalsIgnoreCase("S")) {
            ucl = stats.factorUCL() * S;
            lcl = stats.factorLCL() * S;
            controlLimits = new ControlLimit(ucl, S, lcl);
        } else if (!chartName.equalsIgnoreCase("X") && !chartName.equalsIgnoreCase("I")) {
            if (chartName.equals("mR")) {
                ucl = stats.factorUCL() * MR;
                lcl = stats.factorLCL() * MR;
                controlLimits = new ControlLimit(ucl, MR, lcl);
            } else if (chartName.equals("MR")) {
                ucl = stats.factorUCL() * MR;
                lcl = stats.factorLCL() * MR;
                controlLimits = new ControlLimit(ucl, MR, lcl);
            } else if (!chartName.equalsIgnoreCase("LS") && !chartName.equalsIgnoreCase("Min") && !chartName.equalsIgnoreCase("Max")) {
                if (chartName.equalsIgnoreCase("U1")) {
                    ucl = stats.factorUCL() * U1;
                    lcl = stats.factorLCL() * U1;
                    controlLimits = new ControlLimit(ucl, U1, lcl);
                } else if (chartName.equalsIgnoreCase("U2")) {
                    ucl = stats.factorUCL() * U2;
                    lcl = stats.factorLCL() * U2;
                    controlLimits = new ControlLimit(ucl, U2, lcl);
                } else {
                    controlLimits = new ControlLimit(ucl, cl, lcl);
                }
            } else {
                cl = (Max + Min) / 2.0;
                ucl = cl + stats.factorUCL() * R;
                lcl = cl - stats.factorLCL() * R;
                controlLimits = new ControlLimit(ucl, cl, lcl);
            }
        } else {
            ucl = I + stats.factorUCL() * S;
            lcl = I - stats.factorLCL() * S;
            controlLimits = new ControlLimit(ucl, I, lcl);
        }

        return controlLimits;
    }

    public ControlLimit doCalculate(String chartName, int subGroupSize, double Xbar, double Max, double Min, double R, double S, double U1, double U2, double I, double MR) {
        MCStatisticsCalculator stats = new MCStatisticsCalculator(subGroupSize, chartName);
        double cl = 0.0;
        double ucl = 0.0;
        double lcl = 0.0;
        DoubleCaculate cal = new DoubleCaculate();
        ControlLimit controlLimits;
        if (chartName.equalsIgnoreCase("Xbar")) {
            ucl = cal.safelyAdd(Xbar, cal.safelyMultiply(stats.factorUCL(), S));
            lcl = cal.safelySubtract(Xbar, cal.safelyMultiply(stats.factorLCL(), S));
            controlLimits = new ControlLimit(ucl, Xbar, lcl);
        } else if (chartName.equalsIgnoreCase("R")) {
            ucl = cal.safelyMultiply(stats.factorUCL(), R);
            lcl = cal.safelyMultiply(stats.factorLCL(), R);
            controlLimits = new ControlLimit(ucl, R, lcl);
        } else if (chartName.equalsIgnoreCase("S")) {
            ucl = cal.safelyMultiply(stats.factorUCL(), S);
            lcl = cal.safelyMultiply(stats.factorLCL(), S);
            controlLimits = new ControlLimit(ucl, S, lcl);
        } else if (!chartName.equalsIgnoreCase("X") && !chartName.equalsIgnoreCase("I")) {
            if (chartName.equals("mR")) {
                ucl = cal.safelyMultiply(stats.factorUCL(), MR);
                lcl = cal.safelyMultiply(stats.factorLCL(), MR);
                controlLimits = new ControlLimit(ucl, MR, lcl);
            } else if (chartName.equals("MR")) {
                ucl = cal.safelyMultiply(stats.factorUCL(), MR);
                lcl = cal.safelyMultiply(stats.factorLCL(), MR);
                controlLimits = new ControlLimit(ucl, MR, lcl);
            } else if (!chartName.equalsIgnoreCase("LS") && !chartName.equalsIgnoreCase("Min") && !chartName.equalsIgnoreCase("Max")) {
                if (chartName.equalsIgnoreCase("U1")) {
                    ucl = cal.safelyMultiply(stats.factorUCL(), U1);
                    lcl = cal.safelyMultiply(stats.factorLCL(), U1);
                    controlLimits = new ControlLimit(ucl, U1, lcl);
                } else if (chartName.equalsIgnoreCase("U2")) {
                    ucl = cal.safelyMultiply(stats.factorUCL(), U2);
                    lcl = cal.safelyMultiply(stats.factorLCL(), U2);
                    controlLimits = new ControlLimit(ucl, U2, lcl);
                } else {
                    controlLimits = new ControlLimit(ucl, cl, lcl);
                }
            } else {
                cl = cal.safelyDivide(cal.safelyAdd(Max, Min), 2.0);
                ucl = cal.safelyAdd(cl, cal.safelyMultiply(stats.factorUCL(), R));
                lcl = cal.safelySubtract(cl, cal.safelyMultiply(stats.factorLCL(), R));
                controlLimits = new ControlLimit(ucl, cl, lcl);
            }
        } else {
            ucl = cal.safelyAdd(I, cal.safelyMultiply(stats.factorUCL(), S));
            lcl = cal.safelySubtract(I, cal.safelyMultiply(stats.factorLCL(), S));
            controlLimits = new ControlLimit(ucl, I, lcl);
        }

        return controlLimits;
    }

    public ControlLimit doCalculateDefec(String chartName, int subGroupSize, double defecAvg) {
        DoubleCaculate cal = new DoubleCaculate();
        double cl = 0.0;
        double ucl = 0.0;
        double lcl = 0.0;
        double factor = 0.0;
        ControlLimit controlLimits;
        if (chartName.equalsIgnoreCase("P")) {
            factor = Math.sqrt(cal.safelyMultiply(defecAvg, cal.safelyDivide(cal.safelySubtract(1.0, defecAvg), (double)subGroupSize)));
            ucl = cal.safelyAdd(defecAvg, cal.safelyMultiply(3.0, factor));
            lcl = cal.safelySubtract(defecAvg, cal.safelyMultiply(3.0, factor));
            controlLimits = new ControlLimit(ucl, defecAvg, lcl);
        } else if (chartName.equalsIgnoreCase("U")) {
            factor = Math.sqrt(cal.safelyDivide(defecAvg, (double)subGroupSize));
            ucl = cal.safelyAdd(defecAvg, cal.safelyMultiply(3.0, factor));
            lcl = cal.safelySubtract(defecAvg, cal.safelyMultiply(3.0, factor));
            controlLimits = new ControlLimit(ucl, defecAvg, lcl);
        } else if (chartName.equalsIgnoreCase("NP")) {
            factor = Math.sqrt(cal.safelyMultiply(defecAvg, cal.safelySubtract(1.0, cal.safelyDivide(cal.safelyDivide(defecAvg, (double)subGroupSize), (double)subGroupSize))));
            ucl = cal.safelyAdd(defecAvg, cal.safelyMultiply(3.0, factor));
            lcl = cal.safelySubtract(defecAvg, cal.safelyMultiply(3.0, factor));
            controlLimits = new ControlLimit(ucl, defecAvg, lcl);
        } else if (chartName.equalsIgnoreCase("C")) {
            factor = Math.sqrt(defecAvg);
            ucl = cal.safelyAdd(defecAvg, cal.safelyMultiply(3.0, factor));
            lcl = cal.safelySubtract(defecAvg, cal.safelyMultiply(3.0, factor));
            controlLimits = new ControlLimit(ucl, defecAvg, lcl);
        } else {
            controlLimits = new ControlLimit(ucl, cl, lcl);
        }

        return controlLimits;
    }
    class MCStatisticsCalculator {
        int subGroupSize;
        String chartName;

        MCStatisticsCalculator(int subGroupSize, String chartName) {
            this.subGroupSize = subGroupSize;
            this.chartName = chartName;
        }

        double factorUCL() {
            FactorHolder factorH = new FactorHolder();
            double factor = 0.0;
            if (this.chartName.equalsIgnoreCase("Xbar")) {
                factor = factorH.getFactor(FactorType.A3, this.subGroupSize);
            } else if (this.chartName.equalsIgnoreCase("R")) {
                factor = factorH.getFactor(FactorType.D4, this.subGroupSize);
            } else if (this.chartName.equalsIgnoreCase("S")) {
                factor = factorH.getFactor(FactorType.B4, this.subGroupSize);
            } else if (!this.chartName.equalsIgnoreCase("X") && !this.chartName.equalsIgnoreCase("I")) {
                if (this.chartName.equalsIgnoreCase("mR")) {
                    factor = factorH.getFactor(FactorType.D4, this.subGroupSize);
                } else if (this.chartName.equalsIgnoreCase("MR")) {
                    factor = factorH.getFactor(FactorType.D4, this.subGroupSize);
                } else if (!this.chartName.equalsIgnoreCase("LS") && !this.chartName.equalsIgnoreCase("Min") && !this.chartName.equalsIgnoreCase("Max")) {
                    if (this.chartName.equalsIgnoreCase("U1")) {
                        factor = factorH.getFactor(FactorType.B4, this.subGroupSize);
                    } else if (this.chartName.equalsIgnoreCase("U2")) {
                        factor = factorH.getFactor(FactorType.B4, this.subGroupSize);
                    }
                } else {
                    factor = factorH.getFactor(FactorType.A9, this.subGroupSize);
                }
            } else {
                factor = 3.0;
            }

            return factor;
        }

        double factorLCL() {
            FactorHolder factorH = new FactorHolder();
            double factor = 0.0;
            if (this.chartName.equalsIgnoreCase("Xbar")) {
                factor = factorH.getFactor(FactorType.A3, this.subGroupSize);
            } else if (this.chartName.equalsIgnoreCase("R")) {
                factor = factorH.getFactor(FactorType.D3, this.subGroupSize);
            } else if (this.chartName.equalsIgnoreCase("S")) {
                factor = factorH.getFactor(FactorType.B3, this.subGroupSize);
            } else if (!this.chartName.equalsIgnoreCase("X") && !this.chartName.equalsIgnoreCase("I")) {
                if (this.chartName.equalsIgnoreCase("mR")) {
                    factor = factorH.getFactor(FactorType.D3, this.subGroupSize);
                } else if (this.chartName.equalsIgnoreCase("MR")) {
                    factor = factorH.getFactor(FactorType.D3, this.subGroupSize);
                } else if (!this.chartName.equalsIgnoreCase("LS") && !this.chartName.equalsIgnoreCase("Min") && !this.chartName.equalsIgnoreCase("Max")) {
                    if (this.chartName.equalsIgnoreCase("U1")) {
                        factor = factorH.getFactor(FactorType.B3, this.subGroupSize);
                    } else if (this.chartName.equalsIgnoreCase("U2")) {
                        factor = factorH.getFactor(FactorType.B3, this.subGroupSize);
                    }
                } else {
                    factor = factorH.getFactor(FactorType.A9, this.subGroupSize);
                }
            } else {
                factor = 3.0;
            }

            return factor;
        }
    }
}

