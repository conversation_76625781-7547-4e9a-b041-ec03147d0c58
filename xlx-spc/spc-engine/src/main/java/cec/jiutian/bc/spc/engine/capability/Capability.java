package cec.jiutian.bc.spc.engine.capability;

//import lombok.AllArgsConstructor;
//import lombok.Data;
//import lombok.NoArgsConstructor;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class Capability {
    private Double cp;
    private Double cpk;
    private Double cpSigma;
    private Double tau;
    private Double cpm;
    private Double pp;
    private Double ppk;
    private Double ppSigma;

    public static Capability newDefaultCapability() {
        Capability capability = new Capability((Double)null, (Double)null, (Double)null, (Double)null, (Double)null, (Double)null, (Double)null, (Double)null);
        return capability;
    }

    public String toString() {
        return "Capability [cp=" + this.cp + ", cpk=" + this.cpk + ", cpSigma=" + this.cpSigma + ", tau=" + this.tau + ", cpm=" + this.cpm + ", pp=" + this.pp + ", ppk=" + this.ppk + ", ppSigma=" + this.ppSigma + "]";
    }
}

