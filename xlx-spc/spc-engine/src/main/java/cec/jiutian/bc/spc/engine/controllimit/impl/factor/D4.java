package cec.jiutian.bc.spc.engine.controllimit.impl.factor;

class D4 implements Factor {
    private final double[] D4_FACTORS = new double[]{0.0, 0.0, 3.267, 2.575, 2.282, 2.114, 2.004, 1.924, 1.864, 1.816, 1.777, 1.744, 1.717, 1.693, 1.672, 1.653, 1.637, 1.622, 1.609, 1.596, 1.585, 1.575, 1.565, 1.557, 1.548, 1.541};

    D4() {
    }

    public double factor(int subGroupSize) {
        return subGroupSize <= 25 ? this.D4_FACTORS[subGroupSize] : this.D4_FACTORS[25];
    }
}
