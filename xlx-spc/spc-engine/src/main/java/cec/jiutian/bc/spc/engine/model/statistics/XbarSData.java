package cec.jiutian.bc.spc.engine.model.statistics;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class XbarSData extends MeasuableData {
    private double avg;
    private double std;
    private double range;

    public XbarSData(double avg, double std) {
        this.avg = avg;
        this.std = std;
    }

}
