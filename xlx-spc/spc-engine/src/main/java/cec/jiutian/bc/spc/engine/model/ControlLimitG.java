package cec.jiutian.bc.spc.engine.model;


public class ControlLimitG<T extends Number> {
    private T upper;
    private T center;
    private T lower;

    public ControlLimitG(T upper, T center, T lower) {
        this.upper = upper;
        this.center = center;
        this.lower = lower;
    }

    public String toString() {
        return "ControlLimit [upper=" + this.upper + ", center=" + this.center + ", lower=" + this.lower + "]";
    }

    public T getUpper() {
        return this.upper;
    }

    public T getCenter() {
        return this.center;
    }

    public T getLower() {
        return this.lower;
    }
}

