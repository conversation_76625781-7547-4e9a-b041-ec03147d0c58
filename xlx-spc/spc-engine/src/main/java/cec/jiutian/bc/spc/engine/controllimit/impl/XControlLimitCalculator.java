package cec.jiutian.bc.spc.engine.controllimit.impl;


import cec.jiutian.bc.spc.engine.controllimit.ControlChartControlLimit;
import cec.jiutian.bc.spc.engine.model.ControlChartType;
import cec.jiutian.bc.spc.engine.model.ControlLimit;
import cec.jiutian.bc.spc.engine.model.StatisticsData;
import cec.jiutian.bc.spc.engine.model.statistics.XData;
import org.springframework.stereotype.Component;

import java.util.Iterator;
import java.util.List;

@Component
public class XControlLimitCalculator extends AbstractControlLimitCalculator<XData> {
    public XControlLimitCalculator() {
    }

    public ControlChartType controlChartType() {
        return ControlChartType.X;
    }

    public ControlChartControlLimit doCalculate(int subGroupSize, StatisticsData<XData> statsData) {
        ControlChartControlLimit controlLimits = new ControlChartControlLimit();
        if (statsData.size() <= 1) {
            controlLimits.addControlLimit(ControlChartType.X, new ControlLimit(0.0, 0.0, 0.0));
            return controlLimits;
        } else {
            XStatisticsCalculator stats = new XStatisticsCalculator(statsData.getDatas());
            double x_cl = stats.Xbar();
            double x_ucl = stats.Xbar() + 3.0 * stats.Stddev();
            double x_lcl = stats.Xbar() - 3.0 * stats.Stddev();
            controlLimits.addControlLimit(ControlChartType.X, new ControlLimit(x_ucl, x_cl, x_lcl));
            return controlLimits;
        }
    }

    class XStatisticsCalculator {
        double count;
        double sumOfX;
        double sumOfSquareX;

        XStatisticsCalculator(List<XData> datas) {
            XData data;
            for(Iterator var4 = datas.iterator(); var4.hasNext(); this.sumOfSquareX += data.getX() * data.getX()) {
                data = (XData)var4.next();
                ++this.count;
                this.sumOfX += data.getX();
            }

        }

        double Xbar() {
            return this.sumOfX / this.count;
        }

        double Stddev() {
            return Math.sqrt((this.count * this.sumOfSquareX - this.sumOfX * this.sumOfX) / (this.count * (this.count - 1.0)));
        }
    }
}

