package cec.jiutian.bc.spc.engine.capability.impl;


import cec.jiutian.bc.spc.engine.calculate.BigDecimalCalculate;
import cec.jiutian.bc.spc.engine.capability.CapabilityBig;
import cec.jiutian.bc.spc.engine.controllimit.impl.factor.FactorHolder;
import cec.jiutian.bc.spc.engine.controllimit.impl.factor.FactorType;
import cec.jiutian.bc.spc.engine.exception.SpcAssert;
import cec.jiutian.bc.spc.engine.model.SpecBig;
import cec.jiutian.bc.spc.engine.model.SpecLimitType;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;

public class MCCapabilityCalculatorBig {
    protected FactorHolder factorHolder;
    private static final BigDecimal T = BigDecimal.valueOf(3L);
    private static final BigDecimal T2 = BigDecimal.valueOf(6L);
    private static final BigDecimal TWO = BigDecimal.valueOf(2L);

    public MCCapabilityCalculatorBig() {
    }

    @Autowired
    public void setFactorHolder(FactorHolder factorHolder) {
        SpcAssert.notNull(factorHolder);
        this.factorHolder = factorHolder;
    }

    public CapabilityBig calculate(String type, SpecBig spec, int subGroupSize, int dataCount, int siteTotalCount, BigDecimal sumOfSum, BigDecimal sumOfSquareSum, BigDecimal xbarAvg, BigDecimal sAvg, BigDecimal rAvg, BigDecimal sumAvg) {
        if (dataCount <= 1) {
            return CapabilityBig.newDefaultCapability();
        } else {
            CapabilityBig capability = null;
            BigDecimal cpSigma = null;
            if (type.equalsIgnoreCase("R")) {
                int size = subGroupSize;
                if (subGroupSize == 1) {
                    size = subGroupSize + 1;
                }

                if (rAvg != null) {
                    cpSigma = this.getCPSigmaR(size, rAvg);
                }
            } else if (type.equalsIgnoreCase("S")) {
                if (sAvg != null) {
                    cpSigma = this.getCPSigma(subGroupSize, dataCount, siteTotalCount, sAvg);
                }
            } else if (type.equalsIgnoreCase("T") && sAvg != null && rAvg != null && xbarAvg != null) {
                sAvg = this.getSAvg(sAvg, rAvg, xbarAvg);
                cpSigma = this.getCPSigma(subGroupSize, dataCount, siteTotalCount, sAvg);
            }

            if (spec.getSpecLimitType() == SpecLimitType.Both) {
                capability = this.doBothCalculate(cpSigma, spec, subGroupSize, siteTotalCount, sumOfSum, sumOfSquareSum, xbarAvg, sAvg, sumAvg);
            } else if (spec.getSpecLimitType() == SpecLimitType.UpperOnly) {
                capability = this.doUpperCalculate(cpSigma, spec, subGroupSize, siteTotalCount, sumOfSum, sumOfSquareSum, xbarAvg, sAvg, sumAvg);
            } else if (spec.getSpecLimitType() == SpecLimitType.LowerOnly) {
                capability = this.doLowerCalculate(cpSigma, spec, subGroupSize, siteTotalCount, sumOfSum, sumOfSquareSum, xbarAvg, sAvg, sumAvg);
            } else {
                capability = CapabilityBig.newDefaultCapability();
            }

            return capability;
        }
    }

    public CapabilityBig doBothCalculate(BigDecimal sigma, SpecBig spec, int subGroupSize, int siteTotalCount, BigDecimal sumOfSum, BigDecimal sumOfSquareSum, BigDecimal xbarAvg, BigDecimal sAvg, BigDecimal sumAvg) {
        BigDecimalCalculate bCal = new BigDecimalCalculate();
        BigDecimal usl = spec.getUpperSpec();
        BigDecimal target = spec.getTarget();
        BigDecimal lsl = spec.getLowerSpec();
        BigDecimal cp = null;
        BigDecimal cpk = null;
        BigDecimal tau = null;
        BigDecimal cpm = null;
        BigDecimal pp = null;
        BigDecimal ppk = null;
        BigDecimal ppSigma = null;
        if (usl != null && lsl != null && target == null) {
            target = usl.add(lsl).divide(TWO, bCal.getDecimalScale(), 4);
        }

        if (sumOfSum != null && sumOfSquareSum != null) {
            ppSigma = this.getPPSigma(siteTotalCount, sumOfSum, sumOfSquareSum);
        }

        if (sigma != null) {
            cp = usl.subtract(lsl).divide(sigma.multiply(T2), bCal.getDecimalScale(), 4);
            cpk = usl.subtract(xbarAvg).divide(sigma.multiply(T), bCal.getDecimalScale(), 4).min(xbarAvg.subtract(lsl).divide(sigma.multiply(T), bCal.getDecimalScale(), 4));
        }

        if (target != null && sigma != null && xbarAvg != null) {
            tau = this.getTau(target, sigma, xbarAvg);
        }

        if (usl != null && lsl != null && tau != null && tau != BigDecimal.ZERO) {
            cpm = usl.subtract(lsl).divide(tau.multiply(T2), bCal.getDecimalScale(), 4);
        }

        if (ppSigma != null) {
            pp = usl.subtract(lsl).divide(ppSigma.multiply(T2), bCal.getDecimalScale(), 4);
            BigDecimal A = usl.subtract(sumAvg.divide(BigDecimal.valueOf((long)subGroupSize), bCal.getDecimalScale(), 4)).divide(ppSigma.multiply(T), bCal.getDecimalScale(), 4);
            BigDecimal B = sumAvg.divide(BigDecimal.valueOf((long)subGroupSize), bCal.getDecimalScale(), 4).subtract(lsl).divide(ppSigma.multiply(T), bCal.getDecimalScale(), 4);
            ppk = A.min(B);
        } else if (ppSigma == null && sigma != null) {
            ppSigma = sigma;
            pp = usl.subtract(lsl).divide(sigma.multiply(T2), bCal.getDecimalScale(), 4);
            ppk = usl.subtract(sumAvg.divide(BigDecimal.valueOf((long)subGroupSize), bCal.getDecimalScale(), 4)).divide(sigma.multiply(T), bCal.getDecimalScale(), 4).min(sumAvg.divide(BigDecimal.valueOf((long)subGroupSize), bCal.getDecimalScale(), 4).subtract(lsl).divide(sigma.multiply(T), bCal.getDecimalScale(), 4));
        }

        CapabilityBig capability = new CapabilityBig(cp, cpk, sigma, tau, cpm, pp, ppk, ppSigma);
        return capability;
    }

    public CapabilityBig doUpperCalculate(BigDecimal sigma, SpecBig spec, int subGroupSize, int siteTotalCount, BigDecimal sumOfSum, BigDecimal sumOfSquareSum, BigDecimal xbarAvg, BigDecimal sAvg, BigDecimal sumAvg) {
        BigDecimalCalculate bCal = new BigDecimalCalculate();
        BigDecimal usl = spec.getUpperSpec();
        BigDecimal target = spec.getTarget();
        BigDecimal cp = null;
        BigDecimal cpk = null;
        BigDecimal tau = null;
        BigDecimal cpm = null;
        BigDecimal pp = null;
        BigDecimal ppk = null;
        BigDecimal ppSigma = null;
        if (sumOfSum != null && sumOfSquareSum != null) {
            ppSigma = this.getPPSigma(siteTotalCount, sumOfSum, sumOfSquareSum);
        }

        if (sigma != null) {
            cp = usl.subtract(xbarAvg).divide(sigma.multiply(T), bCal.getDecimalScale(), 4);
            cpk = cp;
        }

        if (target != null && sigma != null && xbarAvg != null) {
            tau = this.getTau(target, sigma, xbarAvg);
        }

        if (usl != null && xbarAvg != null && tau != null && tau != BigDecimal.ZERO) {
            cpk = usl.subtract(xbarAvg).divide(tau.multiply(T), bCal.getDecimalScale(), 4);
        }

        if (ppSigma != BigDecimal.ZERO && ppSigma != null) {
            pp = usl.subtract(sumAvg.divide(BigDecimal.valueOf((long)subGroupSize), bCal.getDecimalScale(), 4)).divide(ppSigma.multiply(T), bCal.getDecimalScale(), 4);
            ppk = pp;
        } else if (ppSigma == null && sigma != null) {
            ppSigma = sigma;
            pp = usl.subtract(sumAvg.divide(BigDecimal.valueOf((long)subGroupSize), bCal.getDecimalScale(), 4)).divide(sigma.multiply(T), bCal.getDecimalScale(), 4);
            ppk = pp;
        }

        CapabilityBig capability = new CapabilityBig(cp, cpk, sigma, tau, (BigDecimal)cpm, pp, ppk, ppSigma);
        return capability;
    }

    public CapabilityBig doLowerCalculate(BigDecimal sigma, SpecBig spec, int subGroupSize, int siteTotalCount, BigDecimal sumOfSum, BigDecimal sumOfSquareSum, BigDecimal xbarAvg, BigDecimal sAvg, BigDecimal sumAvg) {
        BigDecimalCalculate bCal = new BigDecimalCalculate();
        BigDecimal target = spec.getTarget();
        BigDecimal lsl = spec.getLowerSpec();
        BigDecimal cp = null;
        BigDecimal cpk = null;
        BigDecimal tau = null;
        BigDecimal cpm = null;
        BigDecimal pp = null;
        BigDecimal ppk = null;
        BigDecimal ppSigma = null;
        if (sumOfSum != null && sumOfSquareSum != null) {
            ppSigma = this.getPPSigma(siteTotalCount, sumOfSum, sumOfSquareSum);
        }

        if (sigma != null) {
            cp = xbarAvg.subtract(lsl).divide(sigma.multiply(T), bCal.getDecimalScale(), 4);
            cpk = cp;
        }

        if (target != null && sigma != null && xbarAvg != null) {
            tau = this.getTau(target, sigma, xbarAvg);
        }

        if (lsl != null && xbarAvg != null && tau != null && tau != BigDecimal.ZERO) {
            cpk = xbarAvg.subtract(lsl).divide(tau.multiply(T), bCal.getDecimalScale(), 4);
        }

        if (ppSigma != BigDecimal.ZERO && ppSigma != null) {
            pp = sumAvg.divide(BigDecimal.valueOf((long)subGroupSize), bCal.getDecimalScale(), 4).subtract(lsl).divide(ppSigma.multiply(T), bCal.getDecimalScale(), 4);
            ppk = pp;
        } else if (ppSigma == null && sigma == null) {
            ppSigma = sigma;
            pp = sumAvg.divide(BigDecimal.valueOf((long)subGroupSize), bCal.getDecimalScale(), 4).subtract(lsl).divide(sigma.multiply(T), bCal.getDecimalScale(), 4);
            ppk = pp;
        }

        CapabilityBig capability = new CapabilityBig(cp, cpk, sigma, tau, (BigDecimal)cpm, pp, ppk, ppSigma);
        return capability;
    }

    public CapabilityBig calculate(SpecBig spec, int subGroupSize, int dataCount, BigDecimal sumOfSum, BigDecimal sumOfSquareSum, BigDecimal xbarAvg, BigDecimal sAvg, BigDecimal sumAvg) {
        if (dataCount <= 1) {
            return CapabilityBig.newDefaultCapability();
        } else {
            CapabilityBig capability = null;
            if (spec.getSpecLimitType() == SpecLimitType.Both) {
                capability = this.doBothCalculate(spec, subGroupSize, dataCount, sumOfSum, sumOfSquareSum, xbarAvg, sAvg, sumAvg);
            } else if (spec.getSpecLimitType() == SpecLimitType.UpperOnly) {
                capability = this.doUpperCalculate(spec, subGroupSize, dataCount, sumOfSum, sumOfSquareSum, xbarAvg, sAvg, sumAvg);
            } else if (spec.getSpecLimitType() == SpecLimitType.LowerOnly) {
                capability = this.doLowerCalculate(spec, subGroupSize, dataCount, sumOfSum, sumOfSquareSum, xbarAvg, sAvg, sumAvg);
            } else {
                capability = CapabilityBig.newDefaultCapability();
            }

            return capability;
        }
    }

    public CapabilityBig doBothCalculate(SpecBig spec, int subGroupSize, int dataCount, BigDecimal sumOfSum, BigDecimal sumOfSquareSum, BigDecimal xbarAvg, BigDecimal sAvg, BigDecimal sumAvg) {
        BigDecimalCalculate bCal = new BigDecimalCalculate();
        BigDecimal usl = spec.getUpperSpec();
        BigDecimal target = spec.getTarget();
        BigDecimal lsl = spec.getLowerSpec();
        BigDecimal cp = null;
        BigDecimal cpk = null;
        BigDecimal cpSigma = null;
        BigDecimal tau = null;
        BigDecimal cpm = null;
        BigDecimal pp = null;
        BigDecimal ppk = null;
        BigDecimal ppSigma = null;
        if (sAvg != null) {
            cpSigma = this.getCPSigma(subGroupSize, sAvg);
        }

        if (sumOfSum != null && sumOfSquareSum != null) {
            ppSigma = this.getPPSigma(subGroupSize, dataCount, sumOfSum, sumOfSquareSum);
        }

        if (cpSigma != null) {
            cp = usl.subtract(lsl).divide(cpSigma.multiply(T2), bCal.getDecimalScale(), 4);
            cpk = usl.subtract(xbarAvg).divide(cpSigma.multiply(T), bCal.getDecimalScale(), 4).min(xbarAvg.subtract(lsl).divide(cpSigma.multiply(T), bCal.getDecimalScale(), 4));
        }

        if (target != null && cpSigma != null && xbarAvg != null) {
            tau = this.getTau(target, cpSigma, xbarAvg);
        }

        if (usl != null && lsl != null && tau != null && tau != BigDecimal.ZERO) {
            cpm = usl.subtract(lsl).divide(tau.multiply(T2), bCal.getDecimalScale(), 4);
        }

        if (ppSigma != null) {
            pp = usl.subtract(lsl).divide(ppSigma.multiply(T2), bCal.getDecimalScale(), 4);
            BigDecimal A = usl.subtract(sumAvg.divide(BigDecimal.valueOf((long)subGroupSize), bCal.getDecimalScale(), 4)).divide(ppSigma.multiply(T), bCal.getDecimalScale(), 4);
            BigDecimal B = sumAvg.divide(BigDecimal.valueOf((long)subGroupSize), bCal.getDecimalScale(), 4).subtract(lsl).divide(ppSigma.multiply(T), bCal.getDecimalScale(), 4);
            ppk = A.min(B);
        } else if (ppSigma == null && cpSigma != null) {
            ppSigma = cpSigma;
            pp = usl.subtract(lsl).divide(cpSigma.multiply(T2), bCal.getDecimalScale(), 4);
            ppk = usl.subtract(sumAvg.divide(BigDecimal.valueOf((long)subGroupSize), bCal.getDecimalScale(), 4)).divide(cpSigma.multiply(T), bCal.getDecimalScale(), 4).min(sumAvg.divide(BigDecimal.valueOf((long)subGroupSize), bCal.getDecimalScale(), 4).subtract(lsl).divide(cpSigma.multiply(T), bCal.getDecimalScale(), 4));
        }

        CapabilityBig capability = new CapabilityBig(cp, cpk, cpSigma, tau, cpm, pp, ppk, ppSigma);
        return capability;
    }

    public CapabilityBig doUpperCalculate(SpecBig spec, int subGroupSize, int dataCount, BigDecimal sumOfSum, BigDecimal sumOfSquareSum, BigDecimal xbarAvg, BigDecimal sAvg, BigDecimal sumAvg) {
        BigDecimalCalculate bCal = new BigDecimalCalculate();
        BigDecimal usl = spec.getUpperSpec();
        BigDecimal target = spec.getTarget();
        BigDecimal cp = null;
        BigDecimal cpk = null;
        BigDecimal cpSigma = null;
        BigDecimal tau = null;
        BigDecimal cpm = null;
        BigDecimal pp = null;
        BigDecimal ppk = null;
        BigDecimal ppSigma = null;
        if (sAvg != null) {
            cpSigma = this.getCPSigma(subGroupSize, sAvg);
        }

        if (sumOfSum != null && sumOfSquareSum != null) {
            ppSigma = this.getPPSigma(subGroupSize, dataCount, sumOfSum, sumOfSquareSum);
        }

        if (cpSigma != null) {
            cp = usl.subtract(xbarAvg).divide(cpSigma.multiply(T), bCal.getDecimalScale(), 4);
            cpk = cp;
        }

        if (target != null && cpSigma != null && xbarAvg != null) {
            tau = this.getTau(target, cpSigma, xbarAvg);
        }

        if (usl != null && xbarAvg != null && tau != null && tau != BigDecimal.ZERO) {
            cpk = usl.subtract(xbarAvg).divide(tau.multiply(T), bCal.getDecimalScale(), 4);
        }

        if (ppSigma != BigDecimal.ZERO && !ppSigma.equals((Object)null)) {
            pp = usl.subtract(sumAvg.divide(BigDecimal.valueOf((long)subGroupSize), bCal.getDecimalScale(), 4)).divide(ppSigma.multiply(T), bCal.getDecimalScale(), 4);
            ppk = pp;
        } else if (ppSigma == null && cpSigma != null) {
            ppSigma = cpSigma;
            pp = usl.subtract(sumAvg.divide(BigDecimal.valueOf((long)subGroupSize), bCal.getDecimalScale(), 4)).divide(cpSigma.multiply(T), bCal.getDecimalScale(), 4);
            ppk = pp;
        }

        CapabilityBig capability = new CapabilityBig(cp, cpk, cpSigma, tau, (BigDecimal)cpm, pp, ppk, ppSigma);
        return capability;
    }

    public CapabilityBig doLowerCalculate(SpecBig spec, int subGroupSize, int dataCount, BigDecimal sumOfSum, BigDecimal sumOfSquareSum, BigDecimal xbarAvg, BigDecimal sAvg, BigDecimal sumAvg) {
        BigDecimalCalculate bCal = new BigDecimalCalculate();
        BigDecimal target = spec.getTarget();
        BigDecimal lsl = spec.getLowerSpec();
        BigDecimal cp = null;
        BigDecimal cpk = null;
        BigDecimal cpSigma = null;
        BigDecimal tau = null;
        BigDecimal cpm = null;
        BigDecimal pp = null;
        BigDecimal ppk = null;
        BigDecimal ppSigma = null;
        if (sAvg != null) {
            cpSigma = this.getCPSigma(subGroupSize, sAvg);
        }

        if (sumOfSum != null && sumOfSquareSum != null) {
            ppSigma = this.getPPSigma(subGroupSize, dataCount, sumOfSum, sumOfSquareSum);
        }

        if (cpSigma != null) {
            cp = xbarAvg.subtract(lsl).divide(cpSigma.multiply(T), bCal.getDecimalScale(), 4);
            cpk = cp;
        }

        if (target != null && cpSigma != null && xbarAvg != null) {
            tau = this.getTau(target, cpSigma, xbarAvg);
        }

        if (lsl != null && xbarAvg != null && tau != null && tau != BigDecimal.ZERO) {
            cpk = xbarAvg.subtract(lsl).divide(tau.multiply(T), bCal.getDecimalScale(), 4);
        }

        if (ppSigma != BigDecimal.ZERO && ppSigma != null) {
            pp = sumAvg.divide(BigDecimal.valueOf((long)subGroupSize), bCal.getDecimalScale(), 4).subtract(lsl).divide(ppSigma.multiply(T), bCal.getDecimalScale(), 4);
            ppk = pp;
        } else if (ppSigma == null && cpSigma != null) {
            ppSigma = cpSigma;
            pp = sumAvg.divide(BigDecimal.valueOf((long)subGroupSize), bCal.getDecimalScale(), 4).subtract(lsl).divide(cpSigma.multiply(T), bCal.getDecimalScale(), 4);
            ppk = pp;
        }

        CapabilityBig capability = new CapabilityBig(cp, cpk, cpSigma, tau, (BigDecimal)cpm, pp, ppk, ppSigma);
        return capability;
    }

    BigDecimal getCPSigmaR(int subGroupSize, BigDecimal rAvg) {
        BigDecimal sigma = null;
        BigDecimalCalculate bCal = new BigDecimalCalculate();
        FactorHolder factorH = new FactorHolder();
        if (factorH.getFactor(FactorType.D2, subGroupSize) > 0.0) {
            sigma = rAvg.divide(BigDecimal.valueOf(factorH.getFactor(FactorType.D2, subGroupSize)), bCal.getDecimalScale(), 4);
        }

        return sigma;
    }

    BigDecimal getCPSigma(int subGroupSize, BigDecimal sAvg) {
        BigDecimal sigma = null;
        BigDecimalCalculate bCal = new BigDecimalCalculate();
        FactorHolder factorH = new FactorHolder();
        if (factorH.getFactor(FactorType.C4, subGroupSize) > 0.0) {
            sigma = sAvg.divide(BigDecimal.valueOf(factorH.getFactor(FactorType.C4, subGroupSize)), bCal.getDecimalScale(), 4);
        }

        return sigma;
    }

    BigDecimal getCPSigma(int subGroupSize, int dataCount, int siteTotalCount, BigDecimal sAvg) {
        BigDecimal sigma = null;
        BigDecimalCalculate bCal = new BigDecimalCalculate();
        FactorHolder factorH = new FactorHolder();
        if (siteTotalCount > 25) {
            sigma = sAvg;
        } else if (factorH.getFactor(FactorType.C4, subGroupSize) > 0.0) {
            sigma = sAvg.divide(BigDecimal.valueOf(factorH.getFactor(FactorType.C4, siteTotalCount - dataCount + 1)), bCal.getDecimalScale(), 4);
        }

        return sigma;
    }

    BigDecimal getSAvg(BigDecimal sAvg, BigDecimal rAvg, BigDecimal xAvg) {
        BigDecimal value = null;
        BigDecimalCalculate bCal = new BigDecimalCalculate();
        if (xAvg != null) {
            value = sAvg.add(rAvg.multiply(TWO).divide(xAvg, bCal.getDecimalScale(), 4).multiply(sAvg));
        } else {
            value = sAvg;
        }

        return value;
    }

    BigDecimal getPPSigma(int subGroupSize, int dataCount, BigDecimal sumOfSum, BigDecimal sumOfSquareSum) {
        BigDecimal PPSigma = null;
        BigDecimal bSubGroupSize = new BigDecimal(subGroupSize);
        BigDecimal bDataCount = new BigDecimal(dataCount);
        BigDecimalCalculate bCal = new BigDecimalCalculate();
        BigDecimal temp = sumOfSquareSum.multiply(bDataCount.multiply(bSubGroupSize)).subtract(sumOfSum.multiply(sumOfSum)).divide(bDataCount.multiply(bSubGroupSize).multiply(bDataCount.multiply(bSubGroupSize).subtract(BigDecimal.valueOf(1L))), bCal.getDecimalScale(), 4);
        BigDecimal temp1 = sumOfSquareSum.multiply(bDataCount.multiply(bSubGroupSize)).subtract(sumOfSum.multiply(sumOfSum)).divide(bDataCount.multiply(bSubGroupSize).multiply(bDataCount.multiply(bSubGroupSize).multiply(BigDecimal.valueOf(-1L))), bCal.getDecimalScale(), 4);
        if (temp.compareTo(BigDecimal.ZERO) == 1) {
            PPSigma = bCal.sqrt(temp, bCal.getDecimalScale());
        } else if (temp1.compareTo(BigDecimal.ZERO) == 1) {
            PPSigma = bCal.sqrt(temp1, bCal.getDecimalScale());
        }

        return PPSigma;
    }

    BigDecimal getPPSigma(int siteTotalCount, BigDecimal sumOfSum, BigDecimal sumOfSquareSum) {
        BigDecimal PPSigma = null;
        BigDecimal bSiteTotalCount = new BigDecimal(siteTotalCount);
        BigDecimalCalculate bCal = new BigDecimalCalculate();
        BigDecimal temp = sumOfSquareSum.multiply(bSiteTotalCount).subtract(sumOfSum.multiply(sumOfSum)).divide(bSiteTotalCount.multiply(bSiteTotalCount.subtract(BigDecimal.valueOf(1L))), bCal.getDecimalScale(), 4);
        BigDecimal temp1 = sumOfSquareSum.multiply(bSiteTotalCount).subtract(sumOfSum.multiply(sumOfSum)).divide(bSiteTotalCount.multiply(bSiteTotalCount.multiply(BigDecimal.valueOf(-1L))), bCal.getDecimalScale(), 4);
        if (temp.compareTo(BigDecimal.ZERO) == 1) {
            PPSigma = bCal.sqrt(temp, bCal.getDecimalScale());
        } else if (temp1.compareTo(BigDecimal.ZERO) == 1) {
            PPSigma = bCal.sqrt(temp1, bCal.getDecimalScale());
        }

        return PPSigma;
    }

    BigDecimal getTau(BigDecimal target, BigDecimal cpSigma, BigDecimal xbarAvg) {
        BigDecimalCalculate bCal = new BigDecimalCalculate();
        BigDecimal Tau = null;
        Tau = bCal.sqrt(cpSigma.multiply(cpSigma).add(xbarAvg.subtract(target).multiply(xbarAvg.subtract(target))), bCal.getDecimalScale());
        return Tau;
    }
}

