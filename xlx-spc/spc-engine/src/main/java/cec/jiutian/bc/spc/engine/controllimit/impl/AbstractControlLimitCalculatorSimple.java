package cec.jiutian.bc.spc.engine.controllimit.impl;


import cec.jiutian.bc.spc.engine.controllimit.ControlChartControlLimit;
import cec.jiutian.bc.spc.engine.controllimit.ControlLimitCalculatorSimple;
import cec.jiutian.bc.spc.engine.controllimit.impl.factor.FactorHolder;
import cec.jiutian.bc.spc.engine.exception.SpcAssert;
import org.springframework.beans.factory.annotation.Autowired;

public abstract class AbstractControlLimitCalculatorSimple<T> implements ControlLimitCalculatorSimple<T> {
    protected FactorHolder factorHolder;

    public AbstractControlLimitCalculatorSimple() {
    }

    @Autowired
    public void setFactorHolder(FactorHolder factorHolder) {
        SpcAssert.notNull(factorHolder);
        this.factorHolder = factorHolder;
    }

    public ControlChartControlLimit calculate(int subGroupSize, T statsData) {
        ControlChartControlLimit controlLimit = this.doCalculate(subGroupSize, statsData);
        return controlLimit;
    }

    protected abstract ControlChartControlLimit doCalculate(int var1, T var2);
}

