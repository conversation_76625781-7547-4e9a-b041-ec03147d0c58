package test;

import cec.jiutian.fc.excel.service.FabosExcelService;
import cec.jiutian.core.frame.module.Page;
import cec.jiutian.core.view.fabosJson.view.FabosJsonModel;
import cec.jiutian.core.view.fabosJson.view.FabosJsonFieldModel;
import cec.jiutian.view.field.View;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.Sheet;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 子表导出功能测试
 */
public class SubTableExportTest {

    @Mock
    private FabosJsonModel mockMainModel;
    
    @Mock
    private FabosJsonFieldModel mockFieldModel;
    
    @Mock
    private View mockView;
    
    private FabosExcelService excelService;
    
    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        excelService = new FabosExcelService();
    }

    @Test
    void testExportWithSubTables() {
        // 准备测试数据
        Page<Map<String, Object>> page = createTestPage();
        
        // 配置mock对象
        when(mockMainModel.getFabosJsonFieldModels()).thenReturn(Arrays.asList(mockFieldModel));
        when(mockFieldModel.getFabosJsonField()).thenReturn(createMockFabosJsonField());
        when(mockView.exportAsSubTable()).thenReturn(true);
        when(mockView.title()).thenReturn("测试子表");
        when(mockView.show()).thenReturn(true);
        when(mockView.export()).thenReturn(true);
        
        // 执行导出
        Workbook workbook = excelService.exportExcel(mockMainModel, page, true);
        
        // 验证结果
        assertNotNull(workbook);
        assertTrue(workbook.getNumberOfSheets() >= 1);
        
        // 验证主表sheet存在
        Sheet mainSheet = workbook.getSheetAt(0);
        assertNotNull(mainSheet);
        
        workbook.close();
    }

    @Test
    void testExportWithoutSubTables() {
        // 准备测试数据
        Page<Map<String, Object>> page = createTestPage();
        
        // 配置mock对象 - 不导出子表
        when(mockMainModel.getFabosJsonFieldModels()).thenReturn(Arrays.asList(mockFieldModel));
        when(mockFieldModel.getFabosJsonField()).thenReturn(createMockFabosJsonField());
        when(mockView.exportAsSubTable()).thenReturn(false);
        
        // 执行导出
        Workbook workbook = excelService.exportExcel(mockMainModel, page, false);
        
        // 验证结果 - 只有主表sheet
        assertNotNull(workbook);
        assertEquals(1, workbook.getNumberOfSheets());
        
        workbook.close();
    }

    @Test
    void testSubTableConfiguration() {
        // 测试View注解的新属性
        assertTrue(mockView.exportAsSubTable() || !mockView.exportAsSubTable());
        
        // 验证默认值
        View defaultView = createDefaultView();
        assertFalse(defaultView.exportAsSubTable()); // 默认应该是false
    }

    private Page<Map<String, Object>> createTestPage() {
        Page<Map<String, Object>> page = new Page<>();
        List<Map<String, Object>> dataList = new ArrayList<>();
        
        // 创建主表数据
        Map<String, Object> mainRecord = new HashMap<>();
        mainRecord.put("id", "1");
        mainRecord.put("mainName", "测试主记录");
        mainRecord.put("description", "测试描述");
        
        // 创建子表数据
        List<Map<String, Object>> subRecords = new ArrayList<>();
        Map<String, Object> subRecord1 = new HashMap<>();
        subRecord1.put("id", "sub1");
        subRecord1.put("subName", "子记录1");
        subRecord1.put("quantity", 10);
        subRecords.add(subRecord1);
        
        Map<String, Object> subRecord2 = new HashMap<>();
        subRecord2.put("id", "sub2");
        subRecord2.put("subName", "子记录2");
        subRecord2.put("quantity", 20);
        subRecords.add(subRecord2);
        
        mainRecord.put("subItems", subRecords);
        dataList.add(mainRecord);
        
        page.setList(dataList);
        page.setTotal(1L);
        
        return page;
    }

    private cec.jiutian.view.FabosJsonField createMockFabosJsonField() {
        return new cec.jiutian.view.FabosJsonField() {
            @Override
            public View[] views() {
                return new View[]{mockView};
            }
            
            @Override
            public cec.jiutian.view.field.Edit edit() {
                return null;
            }
            
            @Override
            public int sort() {
                return 1000;
            }
            
            @Override
            public cec.jiutian.view.config.KV[] params() {
                return new cec.jiutian.view.config.KV[0];
            }
            
            @Override
            public String customHqlField() {
                return "";
            }
            
            @Override
            public cec.jiutian.view.field.DynamicField dynamicField() {
                return null;
            }
            
            @Override
            public cec.jiutian.view.field.edit.ReferenceAddType referenceAddType() {
                return null;
            }
            
            @Override
            public cec.jiutian.view.field.edit.ReferenceGenerateType referenceGenerateType() {
                return null;
            }
            
            @Override
            public cec.jiutian.view.field.HideConditionByParent[] hideConditionByParent() {
                return new cec.jiutian.view.field.HideConditionByParent[0];
            }
            
            @Override
            public Class<? extends java.lang.annotation.Annotation> annotationType() {
                return cec.jiutian.view.FabosJsonField.class;
            }
        };
    }

    private View createDefaultView() {
        return new View() {
            @Override
            public String title() {
                return "默认视图";
            }
            
            @Override
            public String desc() {
                return "";
            }
            
            @Override
            public cec.jiutian.view.type.Tpl tpl() {
                return null;
            }
            
            @Override
            public String width() {
                return "";
            }
            
            @Override
            public String column() {
                return "";
            }
            
            @Override
            public String[] additionalColumns() {
                return new String[0];
            }
            
            @Override
            public cec.jiutian.view.field.ViewType type() {
                return cec.jiutian.view.field.ViewType.AUTO;
            }
            
            @Override
            public cec.jiutian.view.expr.ExprBool ifRender() {
                return null;
            }
            
            @Override
            public boolean show() {
                return true;
            }
            
            @Override
            public boolean sortable() {
                return false;
            }
            
            @Override
            public boolean export() {
                return true;
            }
            
            @Override
            public boolean exportAsSubTable() {
                return false; // 默认值
            }
            
            @Override
            public String className() {
                return "";
            }
            
            @Override
            public String template() {
                return "";
            }
            
            @Override
            public boolean toolTip() {
                return false;
            }
            
            @Override
            public boolean rowEdit() {
                return false;
            }
            
            @Override
            public int index() {
                return -1;
            }
            
            @Override
            public FixedType fixed() {
                return FixedType.none;
            }
            
            @Override
            public String extraModel() {
                return "";
            }
            
            @Override
            public String extraPK() {
                return "";
            }
            
            @Override
            public Class<? extends cec.jiutian.view.fun.TableFormParamsHandler> tableFormParamsHandler() {
                return cec.jiutian.view.fun.TableFormParamsHandler.class;
            }
            
            @Override
            public Class<? extends java.lang.annotation.Annotation> annotationType() {
                return View.class;
            }
        };
    }
}
