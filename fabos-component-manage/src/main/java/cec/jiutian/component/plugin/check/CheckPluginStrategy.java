package cec.jiutian.component.plugin.check;

import cec.jiutian.component.plugin.dto.Plugin;

/**
 * <AUTHOR>
 * @description:
 */
public interface CheckPluginStrategy {
    //    default Plugin getPlugin(String module, String version){
//        if(StringUtils.isBlank(module)){
//            return null;
//        }
//        if(StringUtils.isBlank(version)){
//            return null;
//        }
//        return BaseRedisCacheUtil.get(PluginConstant.PLUGIN_REDIS_KEY_PREFIX+module+PluginConstant.SEPARATOR+version,Plugin.class);
//    }
//    default List<String> getjarNameList(String module, String version){
//        if(StringUtils.isBlank(module)){
//            return null;
//        }
//        if(StringUtils.isBlank(version)){
//            return null;
//        }
//        String json = (String)BaseRedisCacheUtil.get(PluginConstant.JAR_REDIS_KEY_PREFIX + module + PluginConstant.SEPARATOR + version);
//        return JSON.parseArray(json, String.class);
//    }
    String type();

    boolean isTypeMatch(String type);

    void check(Plugin plugin);
}
