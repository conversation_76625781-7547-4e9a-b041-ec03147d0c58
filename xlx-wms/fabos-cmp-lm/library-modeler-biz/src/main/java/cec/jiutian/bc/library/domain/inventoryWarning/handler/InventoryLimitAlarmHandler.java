package cec.jiutian.bc.library.domain.inventoryWarning.handler;

import cec.jiutian.bc.library.domain.inventoryWarning.model.InventoryLimitAlarm;
import cec.jiutian.bc.library.service.AlarmService;
import cec.jiutian.view.fun.OperationHandler;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class InventoryLimitAlarmHandler implements OperationHandler<InventoryLimitAlarm,Void> {
    private final AlarmService alarmService;

    public InventoryLimitAlarmHandler(AlarmService alarmService) {
        this.alarmService = alarmService;
    }

    @Override
    public String exec(List<InventoryLimitAlarm> data, Void modelObject, String[] param) {
        alarmService.generateInventoryLimitAlarm();
        return null;
    }
}
