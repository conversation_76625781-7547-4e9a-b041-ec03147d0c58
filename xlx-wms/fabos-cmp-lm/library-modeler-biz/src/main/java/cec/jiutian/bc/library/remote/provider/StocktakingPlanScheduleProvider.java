package cec.jiutian.bc.library.remote.provider;

import cec.jiutian.bc.generalModeler.domain.warehouseArea.model.AreaTreeView;
import cec.jiutian.bc.generalModeler.enumeration.MaterialEnum;
import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.job.provider.IJobProvider;
import cec.jiutian.bc.library.domain.inventoryCountingTaskManage.model.CountingAreaTask;
import cec.jiutian.bc.library.domain.inventoryCountingTaskManage.model.CountingTaskManage;
import cec.jiutian.bc.library.domain.inventoryCountingTaskManage.model.MaterialCountingTaskDetail;
import cec.jiutian.bc.library.domain.stockTakingPlan.model.StocktakingPlan;
import cec.jiutian.bc.library.enumeration.InventoryCountingTaskStateEnum;
import cec.jiutian.bc.library.enumeration.StocktakingPlanProcessStateEnum;
import cec.jiutian.bc.library.service.HandleAreaService;
import cec.jiutian.core.frame.annotation.FabosCustomizedService;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.data.jpa.JpaCrud;
import cec.jiutian.meta.FabosJob;
import cn.hutool.core.date.DateUtil;
import jakarta.transaction.Transactional;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/13
 * @description 自动创建盘点任务
 */
@FabosCustomizedService(value = StocktakingPlan.class)
@Component
@Transactional
public class StocktakingPlanScheduleProvider implements IJobProvider {

    @Resource
    private JpaCrud jpaCrud;

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private NamingRuleService namingRuleService;

    @Resource
    private HandleAreaService handleAreaService;

    @FabosJob(comment = "自动创建盘点任务方法")
    @Override
    public String exec(String code, String param) {
        List<StocktakingPlan> stocktakingPlanList = fabosJsonDao.queryEntityList(StocktakingPlan.class);
        if (CollectionUtils.isNotEmpty(stocktakingPlanList)) {
            for (StocktakingPlan stocktakingPlan : stocktakingPlanList) {
                if (StocktakingPlanProcessStateEnum.Enum.active.name().equals(stocktakingPlan.getProcessState())) {
                    Date nowDate = DateUtil.date(new Date());
                    Date planExecuteDate = stocktakingPlan.getStocktakingExecuteDate();
                    Date planActiveDate = stocktakingPlan.getStocktakingActiveDate();
                    Date planEndDate = stocktakingPlan.getStocktakingEndDate();
                    // 是否达到盘点结束时间
                    if ((DateUtil.compare(planEndDate, nowDate, "yyyy-MM-dd")) < 0) {
                        stocktakingPlan.setProcessState(StocktakingPlanProcessStateEnum.Enum.inactive.name());
                    } else {
                        if (planActiveDate != null) {
                            // 盘点计划生效时，系统按照周期自动计算一次盘点计划下次任务下达日期。
                            // 如某盘点计划的计划开始日期为7月1日，周期为每月一次，若生效当日日期<7月1日，则下次任务下达日期为7月1日，
                            // 若生效当日日期>=7月1日，则下次任务下达日期为8月1日
                            int activeResult = DateUtil.compare(planActiveDate, planExecuteDate, "yyyy-MM-dd");
                            if (activeResult <= 0) {
                                if ((DateUtil.compare(planExecuteDate, nowDate, "yyyy-MM-dd")) == 0) {
                                    createStocktakingTask(stocktakingPlan);
                                    updateStartDate(stocktakingPlan);
                                }
                            } else {
                                updateStartDate(stocktakingPlan);
                            }
                        }
                    }
                }
            }
        }
        return null;
    }

    /**
     * 创建盘点任务
     *
     * @param stocktakingPlan
     */
    private void createStocktakingTask(StocktakingPlan stocktakingPlan) {
        CountingTaskManage countingTaskManage = new CountingTaskManage();
        countingTaskManage.setGeneralCode(String.valueOf(namingRuleService.getNameCode("CountingTaskManage", 1, null).get(0)));
        countingTaskManage.setStocktakingPlan(stocktakingPlan);
        countingTaskManage.setTaskName("盘点计划自动创建任务" + "_" + DateUtil.format(stocktakingPlan.getStocktakingStartDate(), "yyyy-MM-dd"));
        countingTaskManage.setTreeSet(stocktakingPlan.getTreeSet());
        countingTaskManage.setPlanStartDate(stocktakingPlan.getStocktakingExecuteDate());
        countingTaskManage.setPlanEndDate(stocktakingPlan.getStocktakingEndDate());
        countingTaskManage.setWorkMethod(stocktakingPlan.getWorkType());
        countingTaskManage.setOperationMethod(stocktakingPlan.getOperationType());
        countingTaskManage.setCountType(stocktakingPlan.getType());
        countingTaskManage.setPeriod(stocktakingPlan.getCycle());
        countingTaskManage.setPeriodUnit(stocktakingPlan.getCycleUnit());
        countingTaskManage.setSpotCheckFlag(stocktakingPlan.getWhetherDraw());
        countingTaskManage.setCheckNum(stocktakingPlan.getDrawLotCount());
        countingTaskManage.setState(InventoryCountingTaskStateEnum.OPEN.name());
        countingTaskManage.setTaskDesc("盘点计划自动创建任务" + "_" + DateUtil.format(stocktakingPlan.getStocktakingStartDate(), "yyyy-MM-dd"));
        List<CountingAreaTask> countingAreaTaskList = new ArrayList<>();
        for (AreaTreeView areaTreeView : stocktakingPlan.getTreeSet()) {
            handleAreaService.getCountingTask(areaTreeView, countingAreaTaskList);
        }
        countingTaskManage.setTasks(countingAreaTaskList);

        if (CollectionUtils.isNotEmpty(stocktakingPlan.getStockTakingPlanDetails())) {
            List<MaterialCountingTaskDetail> taskDetailList = new ArrayList<>();
            stocktakingPlan.getStockTakingPlanDetails().forEach(d -> {
                MaterialCountingTaskDetail materialCountingTaskDetail = new MaterialCountingTaskDetail();
                materialCountingTaskDetail.setMaterialId(d.getExtraModelId());
                materialCountingTaskDetail.setCode(d.getMaterialCode());
                materialCountingTaskDetail.setName(d.getMaterialName());
                materialCountingTaskDetail.setSpecification(d.getMaterialSpecification());
                materialCountingTaskDetail.setMeasureUnit(d.getMeasureUnit());
                materialCountingTaskDetail.setStockType(d.getStockType());
                materialCountingTaskDetail.setStockSubType(d.getStockSubType());
                materialCountingTaskDetail.setStandardNumber(d.getWl09());
                materialCountingTaskDetail.setBrandCode(d.getArticleNumber());
                materialCountingTaskDetail.setCurrentStatus(MaterialEnum.Enum.Y.name());
                taskDetailList.add(materialCountingTaskDetail);
            });
            countingTaskManage.setMaterials(taskDetailList);
        }
        fabosJsonDao.getEntityManager().merge(countingTaskManage);
        //jpaCrud.insert(stocktakingPlan);
    }

    /**
     * 执行定时任务后更新盘点计划下次开始时间
     *
     * @param stocktakingPlan
     */
    private void updateStartDate(StocktakingPlan stocktakingPlan) {
        Date startDate = stocktakingPlan.getStocktakingExecuteDate();
        int days = 0;
        switch (stocktakingPlan.getCycleUnit()) {
            case "year":
                days = Math.round(365 / (float) stocktakingPlan.getCycle());
                break;
            case "halfYear":
                days = Math.round(183 / (float) stocktakingPlan.getCycle());
                break;
            case "quarter":
                days = Math.round(90 / (float) stocktakingPlan.getCycle());
                break;
            case "month":
                days = Math.round(30 / (float) stocktakingPlan.getCycle());
                break;
            case "day":
                days = Math.round(1 / (float) stocktakingPlan.getCycle());
                break;
        }
        startDate = DateUtil.offsetDay(startDate, days);
        stocktakingPlan.setStocktakingExecuteDate(startDate);
        jpaCrud.update(stocktakingPlan);
    }

}
