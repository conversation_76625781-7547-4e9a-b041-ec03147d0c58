package cec.jiutian.bc.library.domain.stockRelocation.handler;

import cec.jiutian.bc.generalModeler.domain.warehouseArea.model.Warehouse;
import cec.jiutian.bc.library.domain.inventory.model.Inventory;
import cec.jiutian.bc.library.domain.stockRelocation.model.StockRelocation;
import cec.jiutian.bc.library.domain.stockRelocation.model.StockRelocationDetail;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.ReferenceAddType;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：2024/11/27 14:09
 * @description：
 */
@Component
public class RelocationReferenceAddHandler implements ReferenceAddType.ReferenceAddHandler<StockRelocation, Inventory> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public Map<String, Object> handle(StockRelocation stockRelocation, List<Inventory> inventories) {
        Map<String, Object> result = new HashMap<>();
        List<StockRelocationDetail> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(inventories)) {
            inventories.forEach(i -> {
                StockRelocationDetail stockRelocationDetail = new StockRelocationDetail();
                stockRelocationDetail.setLotSerialId(i.getLotSerialId());
                stockRelocationDetail.setSupplierLotSerialId(i.getSupplierLotSerialId());
                stockRelocationDetail.setMaterialCode(i.getMaterialCode());
                stockRelocationDetail.setMaterialName(i.getMaterialName());
                stockRelocationDetail.setBrandCode(i.getProductNo());
                stockRelocationDetail.setStockType(i.getStockType());
                stockRelocationDetail.setStockSubType(i.getStockSubType());
                stockRelocationDetail.setMaterialSpecification(i.getMaterialSpecification());
                stockRelocationDetail.setAccountUnit(i.getAccountingUnit());
                stockRelocationDetail.setInventoryId(i.getId());
                stockRelocationDetail.setWarehouseName(i.getWarehouseName());
//                stockRelocationDetail.setWarehouseId(i.getWarehouseId());
                stockRelocationDetail.setWarehouse(fabosJsonDao.findById(Warehouse.class, i.getWarehouseId()));
                stockRelocationDetail.setBlockName(i.getBlockName());
                stockRelocationDetail.setBlockId(i.getBlockId());
                stockRelocationDetail.setShelfName(i.getShelfName());
                stockRelocationDetail.setShelfId(i.getShelfId());
                stockRelocationDetail.setMaterialId(i.getMaterialId());
                stockRelocationDetail.setInventoryLotId(i.getInventoryLotId());
                list.add(stockRelocationDetail);
            });
            result.put("detailList", list);
        }
        return result;
    }
}
