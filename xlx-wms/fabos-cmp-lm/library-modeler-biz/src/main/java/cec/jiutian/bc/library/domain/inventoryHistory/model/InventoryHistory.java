package cec.jiutian.bc.library.domain.inventoryHistory.model;

import cec.jiutian.bc.library.domain.inventoryHistory.proxy.InventoryHistoryDataProxy;
import cec.jiutian.bc.library.enumeration.InventoryHistorySubTypeEnum;
import cec.jiutian.bc.library.enumeration.InventoryHistoryTypeEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.Search;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @description:
 */
@Table(name = "bwi_inventory_history")
@FabosJson(
        name = "库存履历",
        orderBy = "InventoryHistory.createTime desc",
        dataProxy = InventoryHistoryDataProxy.class
)
@Entity
@Getter
@Setter
public class InventoryHistory extends MetaModel {
    @FabosJsonField(
            views = @View(title = "库存批次"),
            edit = @Edit(title = "库存批次")
    )
    private String inventoryLotId;
    @FabosJsonField(
            views = @View(title = "类别"),
            edit = @Edit(title = "类别", type = EditType.CHOICE, search = @Search(vague = true), notNull = true,
                    choiceType = @ChoiceType(fetchHandler = InventoryHistoryTypeEnum.class))
    )
    private String historyType;
    @FabosJsonField(
            views = @View(title = "子类别"),
            edit = @Edit(title = "子类别", type = EditType.CHOICE, search = @Search(vague = true), notNull = true,
                    choiceType = @ChoiceType(fetchHandler = InventoryHistorySubTypeEnum.class))
    )
    private String historySubType;
    //履历描述
    @FabosJsonField(
            views = @View(title = "履历描述"),
            edit = @Edit(title = "履历描述")
    )
    @Column(columnDefinition = "text")
    private String historyDesc;
    //关联单据
    @FabosJsonField(
            views = @View(title = "关联单号"),
            edit = @Edit(title = "关联单号")
    )
    private String relationOrder;
    @FabosJsonField(
            views = @View(title = "关联单", show = false),
            edit = @Edit(title = "关联单", show = false)
    )
    private String relationOrderId;
    @FabosJsonField(
            views = @View(title = "关联模型"),
            edit = @Edit(title = "关联模型")
    )
    private String modelName;

}
