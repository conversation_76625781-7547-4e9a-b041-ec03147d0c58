package cec.jiutian.bc.library.domain.inventoryCountingTaskDetail.model;

import cec.jiutian.bc.library.domain.inventoryCountingTaskDetail.handler.*;
import cec.jiutian.bc.library.domain.inventoryCountingTaskDetail.proxy.MyCountingSubTaskDataProxy;
import cec.jiutian.bc.library.domain.inventoryCountingTaskManage.model.CountingAreaTask;
import cec.jiutian.bc.library.domain.inventoryCountingTaskManage.model.CountingTaskInventoryModel;
import cec.jiutian.bc.library.domain.inventoryCountingTaskManage.model.CountingTaskManage;
import cec.jiutian.bc.library.domain.stockTakingPlan.model.StocktakingPlan;
import cec.jiutian.bc.library.enumeration.CountTypeEnum;
import cec.jiutian.bc.library.enumeration.InventoryCountingTaskStateEnum;
import cec.jiutian.bc.library.enumeration.OperationMethodEnum;
import cec.jiutian.bc.library.enumeration.WorkMethodEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.HideConditionByParent;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowOperation;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;


@Setter
@Getter
@Entity
@Accessors(chain = true)
@Table(name = "my_counting_subtask")
@FabosJson(
        name = "我的盘点任务",
//        orderBy = "taskCode desc",
        orderBy = "updateTime desc",

        dataProxy = MyCountingSubTaskDataProxy.class,
        power = @Power(add = false, edit = false, delete = false),
        rowOperation = {
                @RowOperation(
                        code = "MyCountingSubTask@ENTERRES",
                        ifExpr = "selectedItems[0].state !='DOING'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = MyTaskResultEnterView.class,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        operationHandler = EnterCountingResultHandler.class,
                        title = "录入结果",
                        show = @ExprBool(
                                value = true,
                                params = "MyCountingSubTask@ENTERRES"
                        )
                ),
/*                @RowOperation(
                        code = "pdaStockCountingTaskInput",
                        mode = RowOperation.Mode.BUTTON,
                        type = RowOperation.Type.POPUP,
                        popupType = RowOperation.PopupType.FORM,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        operationHandler = EnterCountingResultHandler.class,
                        title = "PDA盘点数据上传",
                        show = @ExprBool(
                                value = true,
                                params = "MyCountingSubTask@ENTERRES"
                        )
                ),*/
/*                @RowOperation(
                        mode = RowOperation.Mode.HEADER,
                        code = "pdaStockCountingTaskExport",
                        ifExpr = "selectedItems[0].state !='DOING'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        callHint = "确定是否导入盘点任务到PDA中进行盘点？",
                        title = "开始PDA盘点",
                        show = @ExprBool(
                                value = true,
                                params = "MyCountingSubTask@ENTERRES"
                        )
                ),*/
                @RowOperation(
                        code = "MyCountingSubTask@EXCEPTIONADD",
                        ifExpr = "selectedItems[0].state !='DOING'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = MyTaskExceptionAddView.class,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        operationHandler = ExceptionInventoryAddHandler.class,
                        title = "异常批次录入",
                        show = @ExprBool(
                                value = true,
                                params = "MyCountingSubTask@EXCEPTIONADD"
                        )

                ),
                @RowOperation(
                        code = "MyCountingSubTask@SUBMITEXAMINE",
                        ifExpr = "selectedItems[0].state !='WAIT_AUDIT'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = SubmitExamineTaskView.class,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        operationHandler = SubmitExamineHandler.class,
                        title = "提交审核",
                        show = @ExprBool(
                                value = true,
                                params = "MyCountingSubTask@SUBMITEXAMINE"
                        )
                ),
/*                @RowOperation(
                        code = "MyCountingSubTask@PROGRESS",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = CountingTaskProgressView.class,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        operationHandler = CountingProgressHandler.class,
                        title = "盘点进度",
                        show = @ExprBool(
                                value = true,
                                params = "MyCountingSubTask@PROGRESS"
                        )
                ),*/
                @RowOperation(
                        operationHandler = StartCountingTaskHandler.class,
                        ifExpr = "state !='OPEN'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        mode = RowOperation.Mode.SINGLE,
                        code = "MyCountingSubTask@START",
                        title = "开始盘点",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "MyCountingSubTask@START"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
                @RowOperation(
                        operationHandler = EndCountingTaskHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        code = "MyCountingSubTask@END",
                        ifExpr = "state !='DOING'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        title = "结束盘点",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "MyCountingSubTask@END"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
        }
)
public class MyCountingSubTask extends MetaModel {


    @ManyToOne
    @JoinColumn(name = "task_id")
    @FabosJsonField(
            views = @View(title = "盘点任务名称", column = "taskName"),
            edit = @Edit(title = "盘点任务名称",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "taskName"),
                    allowAddMultipleRows = false
            )
    )
    private CountingTaskManage taskManage;

    @FabosJsonField(
            views = @View(title = "盘点任务单号"),

            edit = @Edit(title = "盘点任务单号",
                    search = @Search,
                    show = false)
    )
    private String taskCode;

    @ManyToOne
    @JoinColumn(foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @FabosJsonField(
            views = @View(title = "计划单号", column = "generalCode"),
            edit = @Edit(title = "计划单号",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode"),
                    allowAddMultipleRows = false
            )
    )
    private StocktakingPlan stocktakingPlan;

    @FabosJsonField(
            views = @View(title = "盘点类型"),
            edit = @Edit(title = "盘点类型",
                    search = @Search,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = CountTypeEnum.ChoiceFetch.class))
    )
    private String countType;

    //    作业方式:静态盘点/动态盘点
    @FabosJsonField(
            views = @View(title = "作业方式", show = false),
            edit = @Edit(title = "作业方式",
                    show = false,
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = WorkMethodEnum.ChoiceFetch.class))
    )
    private String workMethod;
    //    操作方式: 点盘/ 盲盘
    @FabosJsonField(
            views = @View(title = "盘点方式"),
            edit = @Edit(title = "盘点方式",
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = OperationMethodEnum.ChoiceFetch.class))
    )
    private String operationMethod;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",
                    show = false,
                    search = @Search,
                    readonly = @Readonly,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = InventoryCountingTaskStateEnum.ChoiceFetch.class)
            )
    )
    private String state;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @FabosJsonField(
            views = @View(title = "盘点区域",
                    type = ViewType.TABLE_VIEW),
            edit = @Edit(title = "盘点区域",
                    type = EditType.TAB_TABLE_ADD
            )
    )
    private List<CountingAreaTask> tasks;

    @FabosJsonField(
            views = @View(title = "是否抽盘", show = false),
            edit = @Edit(title = "是否抽盘",
                    show = false,
                    notNull = true,
                    readonly = @Readonly(edit = true, add = true),
                    defaultVal = "false"
            )
    )
    private Boolean spotCheckFlag;


    @FabosJsonField(
            views = @View(title = "抽盘数量", show = false),
            edit = @Edit(title = "抽盘数量",
                    show = false,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "spotCheckFlag == true"),
                    defaultVal = "0",
                    type = EditType.NUMBER,
                    numberType = @NumberType(min = 0, max = 999999999, precision = 3)
            )
    )
    private Integer checkNum;

    @ManyToOne
    @JoinColumn(name = "operator_id",foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @FabosJsonField(
            views = @View(title = "盘点执行人", column = "name"),
            edit = @Edit(title = "盘点执行人",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    allowAddMultipleRows = false
            )
    )
    private UserView operator;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "counter_id")
    @FabosJsonField(
            views = @View(title = "审核人员",
                    column = "name",
                    show = false
            ),
            edit = @Edit(title = "审核人员",
                    show = false,
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    allowAddMultipleRows = false
            )
    )
    private UserView counter;


    @OneToMany(fetch = FetchType.EAGER, orphanRemoval = true)
    @JoinColumn(name = "task_id")
    @FabosJsonField(
            views = @View(title = "盘点台账",
                    column = "name",
                    type = ViewType.TABLE_VIEW
            ),
            edit = @Edit(title = "盘点台账",
                    type = EditType.TAB_TABLE_ADD,
                    readonly = @Readonly,
                    allowAddMultipleRows = false),
            hideConditionByParent = {
                    @HideConditionByParent(
                            expr = "${operationMethod == 'BLIND'}",
                            fields = "availableQuantity,differenceQuantity",
                            scope = HideConditionByParent.ScopeEnum.BOTH
                    )
            }
    )
    private List<CountingTaskInventoryModel> inventories;

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
    @JoinColumn(name = "task_id")
    @FabosJsonField(
            views = @View(title = "异常批次",
                    type = ViewType.TABLE_VIEW),
            edit = @Edit(title = "异常批次",
                    readonly = @Readonly(add = false, edit = false),
                    referenceTableType = @ReferenceTableType,
                    type = EditType.TAB_TABLE_ADD
            )
    )
    private List<ExceptionInventoryModel> exceptionInventories;

    private Date startTime;

    private Date endTime;
}
