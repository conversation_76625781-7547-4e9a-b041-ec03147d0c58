package cec.jiutian.bc.library.domain.inventory.strategy.impl;

import cec.jiutian.bc.library.domain.inventory.model.InventoryLock;
import cec.jiutian.bc.library.domain.inventory.strategy.InventoryUnLockStrategy;
import cec.jiutian.bc.library.enumeration.AuditLockActionTypeEnum;
import cec.jiutian.bc.library.port.client.LibraryModelerClient;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description: 批次解锁处理调拨
 */
@Service
@Slf4j
public class OutboundOrderFlowStrategy implements InventoryUnLockStrategy {
    @Resource
    private LibraryModelerClient libraryModelerClient;

    @Override
    public boolean isTypeMatch(String type) {
        return Objects.equals(type, AuditLockActionTypeEnum.OutboundOrder.name());
    }

    @Override
    public void handle(InventoryLock lock) {
        log.info("批次解锁 处理领用出库：单号{},批次id：{}", lock.getRelationOrder(), lock.getInventoryId());
        libraryModelerClient.outboundOrderFlow(lock.getRelationOrder(), lock.getInventoryId());
    }
}
