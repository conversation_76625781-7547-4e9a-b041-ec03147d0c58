package cec.jiutian.bc.library.domain.pendingStocktakingTask.model;

import cec.jiutian.bc.generalModeler.enumeration.ProductEnum;
import cec.jiutian.bc.generalModeler.enumeration.StockTypeEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowBaseOperation;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/12/12
 * @description TODO
 */
@Table(
        name = "bws_pending_stocktaking_task_detail")
@FabosJson(
        name = "待审核盘点任务明细",
        orderBy = "PendingStocktakingTaskDetail.createTime desc",
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "add",
                        ifExpr = "1 == 1",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "1 == 1",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "1 == 1",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
        },
        power = @Power(add = false, edit = false, delete = false)
)
@Entity
@Getter
@Setter
public class PendingStocktakingTaskDetail extends MetaModel {

    @FabosJsonField(
            views = @View(title = "仓库"),
            edit = @Edit(title = "仓库", readonly = @Readonly)
    )
    private String warehouseName;

    @FabosJsonField(
            views = @View(title = "仓库", show = false),
            edit = @Edit(title = "仓库", show = false)
    )
    private String warehouseId;

    @FabosJsonField(
            views = @View(title = "库区"),
            edit = @Edit(title = "库区", readonly = @Readonly)
    )
    private String blockName;

    @FabosJsonField(
            views = @View(title = "库区/货架", show = false),
            edit = @Edit(title = "库区/货架", show = false)
    )
    private String blockId;

    @FabosJsonField(
            views = @View(title = "货位"),
            edit = @Edit(title = "货位", readonly = @Readonly)
    )
    private String shelfName;

    @FabosJsonField(
            views = @View(title = "货位", show = false),
            edit = @Edit(title = "货位", show = false)
    )
    private String shelfId;

    @FabosJsonField(
            views = @View(title = "库存批次id", show = false),
            edit = @Edit(title = "库存批次id", show = false)
    )
    private String inventoryId;

    @FabosJsonField(
            views = @View(title = "本厂批号"),
            edit = @Edit(title = "本厂批号", notNull = true,
                    search = @Search(vague = true),
                    inputType = @InputType(length = 20))
    )
    private String lotSerialId;

    @FabosJsonField(
            views = @View(title = "原厂批号", show = false),
            edit = @Edit(title = "原厂批号", notNull = true, show = false,
                    search = @Search(vague = true),
                    inputType = @InputType(length = 20))
    )
    private String supplierLotSerialId;

    @FabosJsonField(
            views = @View(title = "物资编码"),
            edit = @Edit(title = "物资编码", readonly = @Readonly(add = false, edit = true), notNull = true, search = @Search(vague = true))
    )
    private String materialCode;

    @FabosJsonField(
            views = @View(title = "物资分类"),
            edit = @Edit(title = "物资分类")
    )
    private String materialCategory;

    @FabosJsonField(
            views = @View(title = "物资名称"),
            edit = @Edit(title = "物资名称", notNull = true)
    )
    private String name;

    @FabosJsonField(
            views = @View(title = "规格"),
            edit = @Edit(title = "规格", notNull = false)
    )
    private String materialSpecification;

    @FabosJsonField(
            views = @View(title = "品号"),
            edit = @Edit(title = "品号", show = false)
    )
    private String articleNumber;

    @FabosJsonField(
            views = @View(title = "技术要求"),
            edit = @Edit(title = "技术要求", show = false)
    )
    private String wl09;

    @FabosJsonField(
            views = @View(title = "单位"),
            edit = @Edit(title = "单位", notNull = true)
    )
    private String measureUnit;

    @FabosJsonField(
            views = @View(title = "类别"),
            edit = @Edit(title = "类别", type = EditType.CHOICE, readonly = @Readonly,
                    choiceType = @ChoiceType(fetchHandler = StockTypeEnum.class))
    )
    private String stockType;
    @FabosJsonField(
            views = @View(title = "子类别"),
            edit = @Edit(title = "子类别", type = EditType.CHOICE, readonly = @Readonly,
                    choiceType = @ChoiceType(fetchHandler = ProductEnum.class))
    )
    private String stockSubType;

    @FabosJsonField(
            views = @View(title = "库存数量"),
            edit = @Edit(title = "库存数量", notNull = true,
                    numberType = @NumberType(min = 0, max = 999999, precision = 3))
    )
    private Double inventoryQuantity;

    @FabosJsonField(
            views = @View(title = "实盘差异数量"),
            edit = @Edit(title = "实盘差异数量", notNull = true,
                    numberType = @NumberType(min = -9999, max = 999999, precision = 3))
    )
    private Double differenceQuantity;

    @FabosJsonField(
            views = @View(title = "实盘数量"),
            edit = @Edit(title = "实盘数量", notNull = true,
                    numberType = @NumberType(min = 0, max = 999999, precision = 3))
    )
    private Double actualQuantity;
}
