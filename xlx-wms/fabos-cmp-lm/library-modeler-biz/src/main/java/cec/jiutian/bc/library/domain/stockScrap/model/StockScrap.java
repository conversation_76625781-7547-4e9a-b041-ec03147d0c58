package cec.jiutian.bc.library.domain.stockScrap.model;

import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleModel;
import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleNoExamineModel;
import cec.jiutian.bc.library.domain.stockScrap.handler.ScrapReferenceAddHandler;
import cec.jiutian.bc.library.domain.stockScrap.proxy.StockScrapFlowProxy;
import cec.jiutian.bc.library.domain.stockScrap.proxy.StockScrapProxy;
import cec.jiutian.bc.library.enumeration.NamingRuleCodeEnum;
import cec.jiutian.bc.library.enumeration.OrderCurrentStateEnum;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.ReferenceAddType;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.AttachmentType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowBaseOperation;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OrderBy;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：2024/11/27 18:41
 * @description：
 */
@Table(name = "bws_stock_scrap", uniqueConstraints = @UniqueConstraint(columnNames = "general_code"))
@FabosJson(
        name = "库存报废",
        orderBy = "StockScrap.createTime desc",
        dataProxy = StockScrapProxy.class,
        power = @Power(examine = true, examineDetails = true),
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "currentState!='EDIT' || examineStatus =='3' || examineStatus =='2' || examineStatus =='1'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "currentState!='EDIT' || examineStatus =='3' || examineStatus =='1'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "examine",
                        ifExpr = "examineStatus =='3' || examineStatus =='1' || examineStatus =='2'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "examineDetails",
                        ifExpr = "examineStatus =='0'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
        },
        flowCode = "StockScrap",
        flowProxy = StockScrapFlowProxy.class
)
@Entity
@Getter
@Setter
public class StockScrap extends NamingRuleNoExamineModel {

    @Override
    public String getNamingCode() {
        return NamingRuleCodeEnum.StockScrap.name();
    }

    @FabosJsonField(
            views = @View(title = "库存报废单名称"),
            edit = @Edit(title = "库存报废单名称", search = @Search(vague = true), type = EditType.input_text)
    )
    private String orderName;

    @FabosJsonField(
            views = @View(title = "当前状态"),
            edit = @Edit(title = "当前状态", type = EditType.CHOICE, search = @Search(vague = true), show = false,
                    choiceType = @ChoiceType(fetchHandler = OrderCurrentStateEnum.class))
    )
    private String currentState;

    @FabosJsonField(
            views = @View(title = "报废原因"),
            edit = @Edit(title = "报废原因", type = EditType.input_text, notNull = true)
    )
    private String scrapReason;


    @FabosJsonField(
            views = @View(title = "附件"),
            edit = @Edit(title = "附件",
                    type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(size = 5120, maxLimit = 10, type = AttachmentType.Type.BASE)
            )
    )
    private String attachment;

    @FabosJsonField(
            views = @View(title = "备注", toolTip = true),
            edit = @Edit(title = "备注", search = @Search(vague = true),
                    type = EditType.TEXTAREA,
                    inputType = @InputType(length = 200)
            )
    )
    private String notes;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = false)
    @JoinColumn(name = "stock_scrap_id")
    @OrderBy
    @FabosJsonField(
            edit = @Edit(title = "库存报废明细", type = EditType.TAB_REFER_ADD, notNull = true),
            referenceAddType = @ReferenceAddType(referenceClass = "Inventory",
                    queryCondition = "{\"lockType\": [\"NO_LOCK\"], \"stagingEscrowFlag\": \"N\", \"availableQuantity\":\"1,\", \"currentState\": [\"normal\", \"extended\", \"unqualified\"]}",
                    editable = {"scrapQuantity"},
                    referenceAddHandler = ScrapReferenceAddHandler.class),
            views = @View(title = "库存报废明细", type = ViewType.TABLE_VIEW, extraPK = "inventoryId")
    )
    private List<StockScrapDetail> detailList;
}
