package cec.jiutian.bc.library.domain.splitMergeBatch.handler;

import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.library.domain.inventory.model.Inventory;
import cec.jiutian.bc.library.domain.inventory.service.InventoryLockService;
import cec.jiutian.bc.library.domain.inventory.service.InventoryService;
import cec.jiutian.bc.library.domain.inventoryHistory.service.InventoryHistoryService;
import cec.jiutian.bc.library.domain.splitMergeBatch.model.SplitMergeBatch;
import cec.jiutian.bc.library.enumeration.InventoryHistorySubTypeEnum;
import cec.jiutian.bc.library.enumeration.LockTypeEnum;
import cec.jiutian.bc.library.enumeration.NamingRuleCodeEnum;
import cec.jiutian.bc.library.enumeration.OrderCurrentStateEnum;
import cec.jiutian.bc.library.enumeration.SplitMergeTypeEnum;
import cec.jiutian.common.util.BeanUtils;
import cec.jiutian.common.util.ObjectsUtils;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年03月06日 9:34
 */
@Component
public class SplitMergeBatchCompleteHandler implements OperationHandler<SplitMergeBatch, Void> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private InventoryService inventoryService;

    @Resource
    private InventoryLockService inventoryLockService;

    @Resource
    private InventoryHistoryService inventoryHistoryService;

    @Resource
    private NamingRuleService namingRuleService;

    @Override
    @Transactional
    public String exec(List<SplitMergeBatch> splitMergeBatchList, Void modelObject, String[] param) {
        if(!CollectionUtils.isEmpty(splitMergeBatchList)) {
            SplitMergeBatch splitMergeBatch = splitMergeBatchList.get(0);
            if(!OrderCurrentStateEnum.Enum.EXECUTE.name().equals(splitMergeBatch.getCurrentState())) {
                throw new FabosJsonApiErrorTip("单据状态不为执行中状态, 不能进行完成操作, 请检查!");
            }
            if(StringUtils.isBlank(splitMergeBatch.getTaskType())) {
                throw new FabosJsonApiErrorTip("任务类型不能为空!");
            }
            if(SplitMergeTypeEnum.Enum.Spilt.name().equals(splitMergeBatch.getTaskType())) {
                if(splitMergeBatch.getSourceInventory() == null) {
                    throw new FabosJsonApiErrorTip("来源批号不能为空!");
                }
//                if(splitMergeBatch.getSourceInventory().getAvailableQuantity() == null) {
//                    throw new FabosJsonApiErrorTip("来源批次可用数量不能为空!");
//                }
//                if(splitMergeBatch.getSourceInventory().getAvailableQuantity().floatValue() <= 0 ) {
//                    throw new FabosJsonApiErrorTip("来源批次可用数量不能小于等于零!");
//                }
                if(splitMergeBatch.getQuantity() == null) {
                    throw new FabosJsonApiErrorTip("目标批次数量不能为空!");
                }
                if(splitMergeBatch.getQuantity().floatValue() <= 0 ) {
                    throw new FabosJsonApiErrorTip("目标批次数量不能小于等于零!");
                }
//                if(splitMergeBatch.getSourceInventory().getAvailableQuantity().floatValue() < splitMergeBatch.getQuantity().floatValue()) {
//                    throw new FabosJsonApiErrorTip("来源批次可用数量不能小于目标批次数量!");
//                }
            } else if(SplitMergeTypeEnum.Enum.Merge.name().equals(splitMergeBatch.getTaskType())) {
                if(splitMergeBatch.getSourceInventory() == null) {
                    throw new FabosJsonApiErrorTip("来源批号不能为空!");
                }
//                if(splitMergeBatch.getSourceInventory().getAvailableQuantity() == null) {
//                    throw new FabosJsonApiErrorTip("来源批次可用数量不能为空!");
//                }
//                if(splitMergeBatch.getSourceInventory().getAvailableQuantity().floatValue() <= 0 ) {
//                    throw new FabosJsonApiErrorTip("来源批次可用数量不能小于等于零!");
//                }
                if(splitMergeBatch.getTargetInventory() == null) {
                    throw new FabosJsonApiErrorTip("目标批号不能为空!");
                }
                if(ObjectsUtils.nullSafeEquals(splitMergeBatch.getSourceInventory().getId(), splitMergeBatch.getTargetInventory().getId())) {
                    throw new FabosJsonApiErrorTip("不能选择两个相同的台账进行合批操作, 请检查!");
                }
                if(!ObjectsUtils.nullSafeEquals(splitMergeBatch.getSourceInventory().getMaterialId(), splitMergeBatch.getTargetInventory().getMaterialId())) {
                    throw new FabosJsonApiErrorTip("合批的物料信息必须保持一致, 请检查!");
                }
                if(!ObjectsUtils.nullSafeEquals(splitMergeBatch.getSourceInventory().getPrice(), splitMergeBatch.getTargetInventory().getPrice())) {
                    throw new FabosJsonApiErrorTip("合批的物料单价必须保持一致, 请检查!");
                }
            }
            splitMergeBatch.setCurrentState(OrderCurrentStateEnum.Enum.COMPLETE.name());
            fabosJsonDao.mergeAndFlush(splitMergeBatch);
            if(SplitMergeTypeEnum.Enum.Spilt.name().equals(splitMergeBatch.getTaskType())) {
                handleLockUnlockInventory(splitMergeBatch, false);
                Inventory inventoryAdd = new Inventory();
                Inventory sourceInventory = fabosJsonDao.findById(Inventory.class, splitMergeBatch.getSourceInventory().getId());
                if(sourceInventory != null) {
                    sourceInventory.setHandleLockQuantity(sourceInventory.getHandleLockQuantity() - splitMergeBatch.getQuantity());
                    sourceInventory.setAccountingUnitQuantity(sourceInventory.getAccountingUnitQuantity() - splitMergeBatch.getQuantity());
                    fabosJsonDao.mergeAndFlush(sourceInventory);
                }
                BeanUtils.copyNotEmptyProperties(sourceInventory, inventoryAdd);
                inventoryAdd.setId(null);
                inventoryAdd.setInventoryLotId(namingRuleService.getNameCode(NamingRuleCodeEnum.LotNo.name(), 1, null).get(0));
                inventoryAdd.setLotAllQuantity(splitMergeBatch.getQuantity());
                inventoryAdd.setAccountingUnitQuantity(splitMergeBatch.getQuantity());
                inventoryAdd.setAvailableQuantity(splitMergeBatch.getQuantity());
                inventoryAdd.setLockQuantity(0.00);
                inventoryAdd.setHandleLockQuantity(0.00);
                inventoryAdd.setLockType(LockTypeEnum.Enum.NO_LOCK.name());
                fabosJsonDao.insert(inventoryAdd);
                splitMergeBatch.setTargetInventory(inventoryAdd);
                fabosJsonDao.mergeAndFlush(splitMergeBatch);
            } else if(SplitMergeTypeEnum.Enum.Merge.name().equals(splitMergeBatch.getTaskType())) {
                handleLockUnlockInventory(splitMergeBatch, false);
                Inventory sourceInventory = fabosJsonDao.findById(Inventory.class, splitMergeBatch.getSourceInventory().getId());
                if(sourceInventory != null) {
                    sourceInventory.setAvailableQuantity(0.00);
//                    sourceInventory.setLotAllQuantity(0.00);
                    sourceInventory.setAccountingUnitQuantity(0.00);
                    sourceInventory.setHandleLockQuantity(sourceInventory.getHandleLockQuantity() - splitMergeBatch.getQuantity());
                    fabosJsonDao.mergeAndFlush(sourceInventory);
                }
                Inventory targetInventory = fabosJsonDao.findById(Inventory.class, splitMergeBatch.getTargetInventory().getId());
                if(targetInventory != null) {
                    targetInventory.setAvailableQuantity(targetInventory.getAvailableQuantity() + splitMergeBatch.getQuantity());
//                    targetInventory.setLotAllQuantity(targetInventory.getLotAllQuantity() + splitMergeBatch.getQuantity());
                    targetInventory.setAccountingUnitQuantity(targetInventory.getAccountingUnitQuantity() + splitMergeBatch.getQuantity());
                    fabosJsonDao.mergeAndFlush(targetInventory);
                }
            }
        }
        return "msg.success('操作成功!')";
    }

    //冻结解锁库存
    private void handleLockUnlockInventory(SplitMergeBatch splitMergeBatch, boolean isLock) {
        if(splitMergeBatch != null) {
            if(StringUtils.isNotBlank(splitMergeBatch.getId())) {
                if(isLock) {
                    if(SplitMergeTypeEnum.Enum.Spilt.name().equals(splitMergeBatch.getTaskType())) {
                        inventoryLockService.lockLotOne(splitMergeBatch.getSourceInventory().getId(), LockTypeEnum.Enum.LOCK);
                        inventoryHistoryService.library(InventoryHistorySubTypeEnum.Enum.Lock, null, splitMergeBatch.getSourceInventory().getInventoryLotId(), "拆批库存锁定");
                    } else if(SplitMergeTypeEnum.Enum.Merge.name().equals(splitMergeBatch.getTaskType())) {
                        inventoryLockService.lockLotOne(splitMergeBatch.getSourceInventory().getId(), LockTypeEnum.Enum.LOCK);
                        inventoryHistoryService.library(InventoryHistorySubTypeEnum.Enum.Lock, null, splitMergeBatch.getSourceInventory().getInventoryLotId(), "合批库存锁定");
                        inventoryLockService.lockLotOne(splitMergeBatch.getTargetInventory().getId(), LockTypeEnum.Enum.LOCK);
                        inventoryHistoryService.library(InventoryHistorySubTypeEnum.Enum.Lock, null, splitMergeBatch.getTargetInventory().getInventoryLotId(), "合批库存锁定");
                    }
                } else {
                    if(SplitMergeTypeEnum.Enum.Spilt.name().equals(splitMergeBatch.getTaskType())) {
                        inventoryLockService.unLockLotOne(splitMergeBatch.getSourceInventory().getId(), LockTypeEnum.Enum.LOCK);
                        inventoryHistoryService.library(InventoryHistorySubTypeEnum.Enum.Lock, null, splitMergeBatch.getSourceInventory().getInventoryLotId(), "拆批库存解锁");
                    } else if(SplitMergeTypeEnum.Enum.Merge.name().equals(splitMergeBatch.getTaskType())) {
                        inventoryLockService.unLockLotOne(splitMergeBatch.getSourceInventory().getId(), LockTypeEnum.Enum.LOCK);
                        inventoryHistoryService.library(InventoryHistorySubTypeEnum.Enum.Lock, null, splitMergeBatch.getSourceInventory().getInventoryLotId(), "合批库存解锁");
                        inventoryLockService.unLockLotOne(splitMergeBatch.getTargetInventory().getId(), LockTypeEnum.Enum.LOCK);
                        inventoryHistoryService.library(InventoryHistorySubTypeEnum.Enum.Lock, null, splitMergeBatch.getTargetInventory().getInventoryLotId(), "合批库存解锁");
                    }
                }
            }
        }
    }
}
