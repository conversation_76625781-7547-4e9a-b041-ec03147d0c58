package cec.jiutian.bc.library.domain.stockTransfer.mto;

import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Table(name = "stock_out_detail")
@FabosJson(
        name = "其他出库",
        orderBy = "RestStockOutDetail.createTime desc"
)
@Entity
@Getter
@Setter
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RestStockOutMTODetail extends MetaModel {

    @FabosJsonField(
            views = @View(title = "其他出库明细编号"),
            edit = @Edit(title = "其他出库明细编号", show = false,
                    readonly = @Readonly(add = false),
                    search = @Search(vague = true),
                    inputType = @InputType(length = 20))
    )
    private String stockOutDetailCode;

    @FabosJsonField(
            views = {
                    @View(title = "其他出库编号", column = "generalCode")
            },
            edit = @Edit(title = "其他出库编号", type = EditType.REFERENCE_TABLE, show = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode"))
    )
    @ManyToOne
    @JsonIgnoreProperties("restStockOutMTODetailList")
    @JoinColumn(name = "stock_out_id")
    private RestStockOutMTO restStockOutMTO;

    @FabosJsonField(
            views = @View(title = "其他出库申请明细编号",show = false),
            edit = @Edit(title = "其他出库申请明细编号",
                    show = false)
    )
    private String otherStockOutApplyDetailNumber;

    @FabosJsonField(
            views = @View(title = "物料主键id", show = false),
            edit = @Edit(title = "物料主键id", show = false)
    )
    private String materialId;

    @FabosJsonField(
            views = @View(title = "物料编码", show = false),
            edit = @Edit(title = "物料编码", show = false)
    )
    private String materialCode;

    @FabosJsonField(
            views = @View(title = "物料名称"),
            edit = @Edit(title = "物料名称", readonly = @Readonly)
    )
    private String materialName;

    @FabosJsonField(
            views = @View(title = "种类", show = false),
            edit = @Edit(title = "种类", readonly = @Readonly, show = false)
    )
    private String materialCategory;

    @FabosJsonField(
            views = @View(title = "种类"),
            edit = @Edit(title = "种类", readonly = @Readonly)
    )
    private String materialCategoryName;

    @FabosJsonField(
            views = @View(title = "规格"),
            edit = @Edit(title = "规格", readonly = @Readonly)
    )
    private String materialSpecification;

    @FabosJsonField(
            views = @View(title = "来料批号"),
            edit = @Edit(title = "来料批号", readonly = @Readonly)
    )
    private String materialLotIdentifier;

    @FabosJsonField(
            views = @View(title = "本厂批号"),
            edit = @Edit(title = "本厂批号", readonly = @Readonly)
    )
    private String factoryLotIdentifier;

    @FabosJsonField(
            views = @View(title = "出库数量"),
            edit = @Edit(title = "出库数量", notNull = true,
                    numberType = @NumberType(min = 0, max = 9999999, precision = 3))
    )
    private Double quantity;

    @FabosJsonField(
            views = @View(title = "单位"),
            edit = @Edit(title = "单位", readonly = @Readonly)
    )
    private String unit;

    @FabosJsonField(
            views = @View(title = "仓库名称"),
            edit = @Edit(title = "仓库名称", readonly = @Readonly)
    )
    private String warehouseName;

    @FabosJsonField(
            views = @View(title = "仓库", show = false),
            edit = @Edit(title = "仓库", show = false)
    )
    private String warehouseId;

    @FabosJsonField(
            views = @View(title = "库区名称"),
            edit = @Edit(title = "库区名称", readonly = @Readonly)
    )
    private String blockName;

    @FabosJsonField(
            views = @View(title = "库区", show = false),
            edit = @Edit(title = "库区", show = false)
    )
    private String blockId;

    @FabosJsonField(
            views = @View(title = "库位名称"),
            edit = @Edit(title = "库位名称", readonly = @Readonly)
    )
    private String shelfName;

    @FabosJsonField(
            views = @View(title = "库位", show = false),
            edit = @Edit(title = "库位", show = false)
    )
    private String shelfId;

    @FabosJsonField(
            views = @View(title = "备注"),
            edit = @Edit(title = "备注",
                    inputType = @InputType(length = 200))
    )
    private String remark;

    @FabosJsonField(
            views = @View(title = "是否出库",show = false),
            edit = @Edit(title = "是否出库", show = false)
    )
    private String stockOutFlag = "N";

    @FabosJsonField(
            views = @View(title = "物料批次id", show = false),
            edit = @Edit(title = "物料批次id", show = false)
    )
    private String inventoryId;
}
