package cec.jiutian.bc.library.domain.inventoryWarning.handler;

import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.library.domain.inventoryIqc.model.InventoryIqc;
import cec.jiutian.bc.library.domain.inventoryWarning.model.OverDullInventoryWarning;
import cec.jiutian.bc.library.enumeration.InspectTypeEnum;
import cec.jiutian.bc.library.enumeration.IqcCurrentStateEnum;
import cec.jiutian.bc.library.enumeration.NamingRuleCodeEnum;
import cec.jiutian.common.util.BeanUtils;
import cec.jiutian.data.jpa.JpaCrud;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Component
public class InventoryWarningIqcCreateOperationHandler implements OperationHandler<OverDullInventoryWarning, Void> {

    @Resource
    private NamingRuleService namingRuleService;

    @Resource
    private JpaCrud jpaCrud;

    @Override
    public String exec(List<OverDullInventoryWarning> data, Void modelObject, String[] param) {
        if (!CollectionUtils.isEmpty(data)) {
            data.stream().forEach(e -> {
                InventoryIqc inventoryIqc = new InventoryIqc();
                BeanUtils.copyProperties(e, inventoryIqc);
                inventoryIqc.setInspectType(InspectTypeEnum.Enum.OverDullIqc.name());
                inventoryIqc.setQuantity(e.getAccountingUnitQuantity());
                inventoryIqc.setId(null);
                inventoryIqc.setCurrentState(IqcCurrentStateEnum.Enum.EDIT.name());
                inventoryIqc.setNumber(namingRuleService.getNameCode(NamingRuleCodeEnum.InventoryIqc.name(), 1, null).get(0));
                jpaCrud.insert(inventoryIqc);
            });
        }
        return null;
    }
}
