package cec.jiutian.bc.library.domain.inventoryIqc.model;

import cec.jiutian.bc.generalModeler.enumeration.ProductEnum;
import cec.jiutian.bc.generalModeler.enumeration.StockTypeEnum;
import cec.jiutian.bc.generalModeler.enumeration.YesOrNoEnum;
import cec.jiutian.bc.library.domain.inventory.model.Inventory;
import cec.jiutian.bc.library.domain.inventoryIqc.proxy.OverDullInventoryIqcCreateProxy;
import cec.jiutian.bc.library.enumeration.InspectTypeEnum;
import cec.jiutian.bc.library.enumeration.InventoryManagementTypeEnum;
import cec.jiutian.bc.library.enumeration.IqcCurrentStateEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DateType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@FabosJson(
        name = "超期请验创建",
        dataProxy = OverDullInventoryIqcCreateProxy.class,
        orderBy = "OverDullInventoryIqcCreate.createTime desc"
)
@Table(name = "bwi_inventory_iqc_request",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"number"})
        }
)
@Entity
@Getter
@Setter
public class OverDullInventoryIqcCreate extends MetaModel {

    @FabosJsonField(
            views = @View(title = "请验单号"),
            edit = @Edit(title = "请验单号", search = @Search(vague = true), show = false
            )
    )
    private String number;

    @FabosJsonField(
            views = @View(title = "单据状态"),
            edit = @Edit(title = "单据状态", type = EditType.CHOICE, readonly = @Readonly, show = false,
                    choiceType = @ChoiceType(fetchHandler = IqcCurrentStateEnum.class)
            )
    )
    private String currentState;

    @FabosJsonField(
            views = @View(title = "检验方式"),
            edit = @Edit(title = "检验方式", readonly = @Readonly, show = false, type = EditType.CHOICE, search = @Search(vague = true),
                    choiceType = @ChoiceType(fetchHandler = InspectTypeEnum.class))
    )
    private String inspectType;

    @FabosJsonField(
            views = @View(title = "库存批次", column = "inventoryLotId"),
            edit = @Edit(title = "库存批次",
                    readonly = @Readonly(add = false),
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "inventoryLotId"),
                    filter = @Filter(value = "(allowRetestsCount is not null and allowRetestsCount > 0) ")
            )
    )
    @ManyToOne
    @Transient
    private Inventory inventory;

    @FabosJsonField(
            views = @View(title = "库存批次"),
            edit = @Edit(title = "库存批次", show = false, readonly = @Readonly,
                    search = @Search(vague = true),
                    inputType = @InputType(length = 20)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inventory", beFilledBy = "inventoryLotId"))
    )
    private String inventoryLotId;

    @FabosJsonField(
            views = @View(title = "本厂批号"),
            edit = @Edit(title = "本厂批号", readonly = @Readonly,
                    search = @Search(vague = true),
                    inputType = @InputType(length = 20)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inventory", beFilledBy = "lotSerialId"))
    )
    //本厂批号号可重复  如果需要关联到一条唯一记录请使用库存批次
    private String lotSerialId;

    @FabosJsonField(
            views = @View(title = "原炉/原厂批号"),
            edit = @Edit(title = "原炉/原厂批号", readonly = @Readonly,
                    search = @Search(vague = true)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inventory", beFilledBy = "supplierLotSerialId"))
    )
    private String supplierLotSerialId;

    @FabosJsonField(
            views = @View(title = "存货管理类型"),
            edit = @Edit(title = "存货管理类型", type = EditType.CHOICE, readonly = @Readonly,
                    choiceType = @ChoiceType(fetchHandler = InventoryManagementTypeEnum.class)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inventory", beFilledBy = "inventoryManagementType"))
    )
    private String inventoryManagementType;

    @FabosJsonField(
            views = @View(title = "供应商", column = "supplierName"),
            edit = @Edit(title = "供应商", readonly = @Readonly
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inventory", beFilledBy = "supplierName"))
    )
    private String supplierName;

    @FabosJsonField(
            views = @View(title = "物资编号"),
            edit = @Edit(title = "物资编号", search = @Search(vague = true), readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inventory", beFilledBy = "materialCode")
            )
    )
    private String materialCode;

    @FabosJsonField(
            views = @View(title = "物资名称"),
            edit = @Edit(title = "物资名称", search = @Search, readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inventory", beFilledBy = "materialName"))

    )
    private String materialName;

    @FabosJsonField(
            views = @View(title = "规格"),
            edit = @Edit(title = "规格", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inventory", beFilledBy = "materialSpecification"))
    )
    private String materialSpecification;

    @FabosJsonField(
            views = @View(title = "单位"),
            edit = @Edit(title = "单位", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inventory", beFilledBy = "accountingUnit"))
    )
    private String accountingUnit;

    @FabosJsonField(
            views = @View(title = "品号"),
            edit = @Edit(title = "品号", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inventory", beFilledBy = "productNo"))

    )
    private String productNo;

    @FabosJsonField(
            views = @View(title = "类别"),
            edit = @Edit(title = "类别", type = EditType.CHOICE, readonly = @Readonly,
                    choiceType = @ChoiceType(fetchHandler = StockTypeEnum.class)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inventory", beFilledBy = "stockType"))
    )
    private String stockType;
    @FabosJsonField(
            views = @View(title = "子类别"),
            edit = @Edit(title = "子类别", type = EditType.CHOICE, readonly = @Readonly,
                    choiceType = @ChoiceType(fetchHandler = ProductEnum.class)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inventory", beFilledBy = "stockSubType"))
    )
    private String stockSubType;

    @FabosJsonField(
            views = @View(title = "合格证号"),
            edit = @Edit(title = "合格证号",
                    readonly = @Readonly,
                    inputType = @InputType(length = 20)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inventory", beFilledBy = "certificateNumber"))
    )
    private String certificateNumber;

    @FabosJsonField(
            views = @View(title = "原厂合格证号"),
            edit = @Edit(title = "原厂合格证号",
                    readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inventory", beFilledBy = "supplierCertificateNumber"))
    )

    private String supplierCertificateNumber;

    @FabosJsonField(
            views = @View(title = "生产单位"),
            edit = @Edit(title = "生产单位", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inventory", beFilledBy = "production"))
    )
    private String production;

    @FabosJsonField(
            views = @View(title = "技术要求"),
            edit = @Edit(title = "技术要求", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inventory", beFilledBy = "technicalRequirements"))
    )
    private String technicalRequirements;


    @FabosJsonField(
            views = @View(title = "本厂批数量"),
            edit = @Edit(title = "本厂批数量", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inventory", beFilledBy = "lotAllQuantity"))
    )
    private Double lotAllQuantity;

    @FabosJsonField(
            views = @View(title = "台账总量"),
            edit = @Edit(title = "台账总量", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inventory", beFilledBy = "accountingUnitQuantity"))
    )
    //
    private Double accountingUnitQuantity;

    @FabosJsonField(
            views = @View(title = "请验数量"),
            edit = @Edit(title = "请验数量", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inventory", beFilledBy = "accountingUnitQuantity"))
    )
    private Double quantity;

    @FabosJsonField(
            views = @View(title = "暂存代管标记"),
            edit = @Edit(title = "暂存代管标记", type = EditType.CHOICE,
                    search = @Search, readonly = @Readonly,
                    choiceType = @ChoiceType(fetchHandler = YesOrNoEnum.class)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inventory", beFilledBy = "stagingEscrowFlag"))
    )
    private String stagingEscrowFlag;

    @FabosJsonField(
            views = @View(title = "非正常库存标记"),
            edit = @Edit(title = "非正常库存标记", type = EditType.CHOICE, readonly = @Readonly,
                    choiceType = @ChoiceType(fetchHandler = YesOrNoEnum.class)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inventory", beFilledBy = "noNormalFlag"))
    )
    //为Y表示不正常 N表示正常
    private String noNormalFlag;

    @FabosJsonField(
            views = @View(title = "生产退回标记"),
            edit = @Edit(title = "生产退回标记", type = EditType.CHOICE, readonly = @Readonly,
                    choiceType = @ChoiceType(fetchHandler = YesOrNoEnum.class)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inventory", beFilledBy = "productionReturnFlag"))
    )
    //退料库写入JNGY-547
    private String productionReturnFlag;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "生产日期", type = ViewType.DATE),
            edit = @Edit(title = "生产日期", readonly = @Readonly,
                    dateType = @DateType(type = DateType.Type.DATE)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inventory", beFilledBy = "manufactureDate"))
    )
    private Date manufactureDate;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "使用期限", type = ViewType.DATE),
            edit = @Edit(title = "使用期限", readonly = @Readonly,
                    dateType = @DateType(type = DateType.Type.DATE)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inventory", beFilledBy = "useDeadlineDate"))
    )
    private Date useDeadlineDate;

    @FabosJsonField(
            views = @View(title = "用途"),
            edit = @Edit(title = "用途", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inventory", beFilledBy = "purpose"))
    )
    //多个逗号分割
    private String purpose;

}
