package cec.jiutian.bc.library.domain.stockTransfer.mto;

import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.FabosJsonI18n;
import cec.jiutian.view.ReferenceTreeType;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.constant.AnnotationConst;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Tree;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Entity
@Table(name = "fd_org")
@FabosJson(
        name = "部门管理",
        tree = @Tree(pid = "parentOrg.id", expandLevel = 5),
        orderBy = "OrgMTO.sort asc"
)
@FabosJsonI18n
@Getter
@Setter
@NoArgsConstructor
@TemplateType(type = "treeForm")
public class OrganizationMTO extends MetaModel {

    @Column(length = AnnotationConst.CODE_LENGTH, unique = true)
    @FabosJsonField(
            views = @View(title = "部门编码", sortable = true),
            edit = @Edit(title = "部门编码", notNull = true, search = @Search(vague = true))
    )
    private String code;

    @FabosJsonField(
            views = @View(title = "部门名称", sortable = true),
            edit = @Edit(title = "部门名称", notNull = true, search = @Search(vague = true))
    )
    private String name;

    @ManyToOne
    @FabosJsonField(
            edit = @Edit(
                    title = "上级部门",
                    type = EditType.REFERENCE_TREE,
                    referenceTreeType = @ReferenceTreeType(pid = "parentOrg.id", expandLevel = 3)
            )
    )
    private OrganizationMTO parentOrg;

    @FabosJsonField(
            edit = @Edit(
                    title = "显示顺序"
            )
    )
    private Integer sort;

/*    @OneToMany(fetch = FetchType.EAGER)
    @JoinColumn(name = "user_id")
    @FabosJsonField(
            views = @View(title = "成员", export = false),
            edit = @Edit(
                    title = "成员",
                    readonly = @Readonly(add = false,edit = false),
                    type = EditType.TAB_REFER_ADD,
                    referenceTableType = @ReferenceTableType
            )
    )
    private List<UserForOrgViewMTO> users;*/
}