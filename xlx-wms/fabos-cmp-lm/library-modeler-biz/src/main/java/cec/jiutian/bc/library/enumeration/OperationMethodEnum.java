package cec.jiutian.bc.library.enumeration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.Getter;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Getter
public enum OperationMethodEnum {
    //    操作方式: 明盘/ 暗盘
    POINT("明盘"),
    BLIND("暗盘");

    private final String method;

    OperationMethodEnum(String method) {
        this.method = method;
    }

    public static class ChoiceFetch implements ChoiceFetchHandler {
        @Override
        public List<VLModel> fetch(String[] params) {
            return Stream.of(OperationMethodEnum.values()).map(methodEnum ->
                    new VLModel(methodEnum.name(), methodEnum.getMethod())).collect(Collectors.toList());
        }

    }
}
