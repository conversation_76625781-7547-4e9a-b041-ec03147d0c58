package cec.jiutian.bc.library.domain.stockTakingPlan.operationHandler;

import cec.jiutian.bc.library.domain.stockTakingPlan.model.StocktakingPlan;
import cec.jiutian.bc.library.domain.stockTakingPlan.model.StocktakingPlanWithMaterial;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/11
 * @description TODO
 */
@Component
public class EditByMaterialOperationHandler implements OperationHandler<StocktakingPlan, StocktakingPlanWithMaterial> {

    @Override
    public StocktakingPlanWithMaterial fabosJsonFormValue(List<StocktakingPlan> data, StocktakingPlanWithMaterial fabosJsonForm, String[] param) {
        StocktakingPlan stocktakingPlan = data.get(0);
        BeanUtil.copyProperties(stocktakingPlan, fabosJsonForm);
        return fabosJsonForm;
    }
}
