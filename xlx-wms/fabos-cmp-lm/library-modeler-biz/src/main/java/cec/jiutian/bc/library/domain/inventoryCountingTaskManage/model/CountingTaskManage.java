package cec.jiutian.bc.library.domain.inventoryCountingTaskManage.model;

import cec.jiutian.bc.generalModeler.domain.warehouseArea.model.AreaTreeView;
import cec.jiutian.bc.library.domain.inventoryCountingTaskDetail.model.UserView;
import cec.jiutian.bc.library.domain.inventoryCountingTaskManage.handler.CloseCountingTaskHandler;
import cec.jiutian.bc.library.domain.inventoryCountingTaskManage.handler.CountingTaskDetailAddHandler;
import cec.jiutian.bc.library.domain.inventoryCountingTaskManage.handler.CountingTaskDistributeHandler;
import cec.jiutian.bc.library.domain.inventoryCountingTaskManage.handler.OpenCountingTaskHandler;
import cec.jiutian.bc.library.domain.inventoryCountingTaskManage.proxy.CountingTaskDataProxy;
import cec.jiutian.bc.library.domain.stockTakingPlan.model.StocktakingPlan;
import cec.jiutian.bc.library.enumeration.CountTypeEnum;
import cec.jiutian.bc.library.enumeration.InventoryCountingTaskStateEnum;
import cec.jiutian.bc.library.enumeration.OperationMethodEnum;
import cec.jiutian.bc.library.enumeration.PeriodUnitEnum;
import cec.jiutian.bc.library.enumeration.WorkMethodEnum;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.ReferenceAddType;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DateType;
import cec.jiutian.view.field.edit.DependFieldDisplay;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.field.edit.TabTableReferType;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowBaseOperation;
import cec.jiutian.view.type.RowOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.CascadeType;
import jakarta.persistence.ConstraintMode;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.ForeignKey;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@FabosJson(
        name = "盘点任务",
        orderBy = "CountingTaskManage.createTime desc",
        dataProxy = CountingTaskDataProxy.class,
        power = @Power(add = false, export = false, delete = false),
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "state != 'OPEN'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "state =='CLOSE' || state == 'AUDIT' || state == 'CLOSE'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
        },
        rowOperation = {
                @RowOperation(
                        code = "CountingTaskManage@COUNTINGTASKWITHAREA",
                        mode = RowOperation.Mode.BUTTON,
                        type = RowOperation.Type.POPUP,
                        popupType = RowOperation.PopupType.FORM,
                        submitMethod = RowOperation.SubmitMethod.ADD,
                        fabosJsonClass = CountingTaskWithAreaView.class,
                        title = "区域盘点",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "CountingTaskManage@COUNTINGTASKWITHAREA"
                        )
                ),
                @RowOperation(
                        code = "CountingTaskManage@COUNTINGTASKWITHMATERIAL",
                        mode = RowOperation.Mode.BUTTON,
                        type = RowOperation.Type.POPUP,
                        popupType = RowOperation.PopupType.FORM,
                        submitMethod = RowOperation.SubmitMethod.ADD,
                        fabosJsonClass = CountingTaskWithMaterialView.class,
                        title = "存货编码盘",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "CountingTaskManage@COUNTINGTASKWITHMATERIAL"
                        )
                ),
                @RowOperation(
                        code = "CountingTaskManage@DISTRIBUTE",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        ifExpr = "selectedItems[0].state !='OPEN'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = DistributeCountingTaskView.class,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        operationHandler = CountingTaskDistributeHandler.class,
                        title = "分配任务",
                        show = @ExprBool(
                                value = true,
                                params = "CountingTaskManage@DISTRIBUTE"
                        )
                ),
                @RowOperation(
                        operationHandler = OpenCountingTaskHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        code = "CountingTaskManage@OPEN",
                        callHint = "确定是否要释放任务？",
                        title = "发布",
                        show = @ExprBool(
                                value = true,
                                params = "CountingTaskManage@OPEN"
                        ),
                        ifExpr = "state != 'OPEN'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        operationHandler = CloseCountingTaskHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        code = "CountingTaskManage@CLOSE",
//                        type = RowOperation.Type.POPUP,
                        callHint = "确定是否要关闭任务？",
                        title = "完成",
                        ifExpr = "state == 'COMPLETE' || state == 'CLOSE'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
                        show = @ExprBool(
                                value = true,
                                params = "CountingTaskManage@CLOSE"
                        )
                )
        }
)
@Table(name = "counting_task_manage")
@Entity
@Getter
@Setter
@TemplateType(type = "multiTable")
public class CountingTaskManage extends TaskManageNamingRuleModel {

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "计划单号", column = "generalCode"),
            edit = @Edit(title = "计划单号",
                    readonly = @Readonly(add = false, edit = true),
                    type = EditType.REFERENCE_TABLE,
//                    notNull = true,
//                    filter = @Filter(value = "processState=='active'"),
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode"),
                    allowAddMultipleRows = false
            )
    )
    private StocktakingPlan stocktakingPlan;

    @ManyToOne(fetch = FetchType.EAGER)
    @FabosJsonField(
            views = @View(title = "审核人员", column = "name"),
            edit = @Edit(title = "审核人员",
                    notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    allowAddMultipleRows = false
            )
    )
    private UserView counter;

    //任务名称
    @FabosJsonField(
            views = @View(title = "任务名称"),
            edit = @Edit(title = "任务名称",
                    notNull = true,
                    search = @Search(vague = true),
                    inputType = @InputType(length = 50))
    )
    private String taskName;

    @FabosJsonField(
            views = @View(title = "盘点类型"),
            edit = @Edit(title = "盘点类型",
                    readonly = @Readonly(add = false, edit = true),
                    search = @Search,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = CountTypeEnum.ChoiceFetch.class))
    )
    private String countType;

    @FabosJsonField(
            views = @View(title = "周期"),
            edit = @Edit(title = "周期",
                    notNull = true,
                    type = EditType.NUMBER,
                    defaultVal = "0",
                    numberType = @NumberType(min = 0, max = 999999)
            )
    )
    private Integer period;

    @FabosJsonField(
            views = @View(title = "周期单位"),
            edit = @Edit(title = "周期单位",
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = PeriodUnitEnum.ChoiceFetch.class))
    )
    private String periodUnit;

    //    计划开始执行日期
    @FabosJsonField(
            views = @View(title = "计划开始执行日期"
                    , type = ViewType.DATE),
            edit = @Edit(title = "计划开始执行日期",
                    notNull = true,
                    type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE))
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date planStartDate;

    //    计划结束执行日期
    @FabosJsonField(
            views = @View(title = "计划结束执行日期"
                    , type = ViewType.DATE),
            edit = @Edit(title = "计划结束执行日期",
                    notNull = true,
                    type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE))
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date planEndDate;
    //    作业方式:静态盘点/动态盘点
    @FabosJsonField(
            views = @View(title = "作业方式", show = false),
            edit = @Edit(title = "作业方式",
                    show = false,
                    defaultVal = "STATIC",
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = WorkMethodEnum.ChoiceFetch.class))
    )
    private String workMethod;
    //    操作方式: 点盘/ 盲盘
    @FabosJsonField(
            views = @View(title = "盘点方式"),
            edit = @Edit(title = "盘点方式",
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = OperationMethodEnum.ChoiceFetch.class))
    )
    private String operationMethod;

    @FabosJsonField(
            views = @View(title = "是否抽盘", show = false),
            edit = @Edit(title = "是否抽盘",show = false,
                    notNull = true,
                    defaultVal = "false"
            )
    )
    private Boolean spotCheckFlag;


    @FabosJsonField(
            views = @View(title = "抽盘数量", show = false),
            edit = @Edit(title = "抽盘数量", show = false,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "spotCheckFlag == true"),
                    defaultVal = "0",
                    type = EditType.NUMBER,
                    numberType = @NumberType(min = 0, max = 999999999)
            )
    )
    private Integer checkNum;

    //    计划描述
    @FabosJsonField(
            views = @View(title = "任务描述"),
            edit = @Edit(title = "任务描述",
                    inputType = @InputType(length = 100),
                    type = EditType.TEXTAREA)
    )
    private String taskDesc;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",
                    show = false,
                    search = @Search,
                    readonly = @Readonly,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = InventoryCountingTaskStateEnum.ChoiceFetch.class)
            )
    )
    private String state;

    @ManyToMany
    @JoinTable(
            name = "counting_task_area_tree",  //中间表表名
            inverseForeignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT),
            joinColumns = @JoinColumn(name = "task_id", referencedColumnName = "id"),
            inverseJoinColumns = @JoinColumn(name = "tree_id", referencedColumnName = "id"))
    @FabosJsonField(
            views = @View(title = "区域信息", show = false),
            edit = @Edit(title = "区域信息", type = EditType.TAB_TABLE_REFER,
                    tabTableReferType = @TabTableReferType(type = TabTableReferType.SelectShowTypeMTM.POPUP_TABLE_TREE, treeSelectMode = TabTableReferType.SelectMode.Mode1)
            )
    )
    private List<AreaTreeView> treeSet;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.EAGER)
    @JoinColumn(name = "counting_task_id")
    @FabosJsonField(
            views = @View(title = "盘点物资",
                    type = ViewType.TABLE_VIEW,
                    extraPK = "materialId"),
            edit = @Edit(title = "盘点物资",
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "countType == 'SPECIFIC'"),
                    type = EditType.TAB_REFER_ADD,
                    allowAddMultipleRows = true,
                    queryCondition = "{\"lockState\": \"Normal\"}"
            ),
            referenceAddType = @ReferenceAddType(referenceClass = "MaterialMTO",
                    referenceAddHandler = CountingTaskDetailAddHandler.class)
    )
    private List<MaterialCountingTaskDetail> materials;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "counting_task_id")
    @FabosJsonField(
            views = @View(title = "盘点区域", show = false,
                    type = ViewType.TABLE_VIEW),
            edit = @Edit(title = "盘点区域",
                    type = EditType.TAB_TABLE_ADD
            ),
            referenceAddType = @ReferenceAddType(referenceClass = "CountingAreaTask")
    )
    private List<CountingAreaTask> tasks;

}
