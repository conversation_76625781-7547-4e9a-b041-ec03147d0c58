package cec.jiutian.bc.library.domain.inventoryCountingTaskDetail.handler;

import cec.jiutian.bc.library.domain.inventoryCountingTaskDetail.model.ExceptionInventoryModel;
import cec.jiutian.bc.library.domain.inventoryCountingTaskDetail.model.InventoryViewForExamine;
import cec.jiutian.bc.library.domain.inventoryCountingTaskDetail.model.MyCountingSubTask;
import cec.jiutian.bc.library.domain.inventoryCountingTaskDetail.model.SubmitExamineTaskView;
import cec.jiutian.bc.library.domain.inventoryCountingTaskDetail.model.SubmitExceptionInventoryView;
import cec.jiutian.bc.library.domain.inventoryCountingTaskDetail.model.UserView;
import cec.jiutian.bc.library.domain.inventoryCountingTaskManage.model.CountingAreaTask;
import cec.jiutian.bc.library.domain.inventoryCountingTaskManage.model.CountingTaskInventoryModel;
import cec.jiutian.bc.library.domain.pendingStocktakingTask.model.DrawTaskDetail;
import cec.jiutian.bc.library.domain.pendingStocktakingTask.model.OutsideLotDetail;
import cec.jiutian.bc.library.domain.pendingStocktakingTask.model.PendingStocktakingTask;
import cec.jiutian.bc.library.domain.pendingStocktakingTask.model.PendingStocktakingTaskDetail;
import cec.jiutian.bc.library.enumeration.InventoryCountingTaskStateEnum;
import cec.jiutian.bc.library.enumeration.LotStateEnum;
import cec.jiutian.bc.library.enumeration.PendingStocktakingTaskProcessStateEnum;
import cec.jiutian.common.util.BeanUtils;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Component
@Transactional
public class SubmitExamineHandler implements OperationHandler<MyCountingSubTask, SubmitExamineTaskView> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<MyCountingSubTask> data, SubmitExamineTaskView modelObject, String[] param) {
        MyCountingSubTask myCountingSubTask = data.get(0);
        if (!InventoryCountingTaskStateEnum.WAIT_AUDIT.name().equals(myCountingSubTask.getState())) {
            throw new FabosJsonApiErrorTip("非待审核状态,不能录提交审核：" + myCountingSubTask.getState());
        }
        myCountingSubTask = fabosJsonDao.merge(myCountingSubTask);
        myCountingSubTask.setState(InventoryCountingTaskStateEnum.AUDIT.name());
        PendingStocktakingTask pendingStocktakingTask = createPendingStocktakingTask(myCountingSubTask);
        setPendingStocktakingTaskDetailList(myCountingSubTask, pendingStocktakingTask);
        setOutsideLotDetailList(modelObject, pendingStocktakingTask);
        if (myCountingSubTask.getTaskManage().getSpotCheckFlag()) {
            setDrawTaskDetailList(myCountingSubTask, pendingStocktakingTask);
        }
        fabosJsonDao.saveOrUpdate(pendingStocktakingTask);
        return "提交成功";
    }

    private void setOutsideLotDetailList(SubmitExamineTaskView myCountingSubTask, PendingStocktakingTask pendingTask) {
        List<SubmitExceptionInventoryView> exceptionInventories = myCountingSubTask.getExceptionInventories();
        if (CollectionUtils.isEmpty(exceptionInventories)) {
            return;
        }
        ArrayList<OutsideLotDetail> outsideLotDetails = new ArrayList<>();
        for (SubmitExceptionInventoryView exceptionInventory : exceptionInventories) {
            OutsideLotDetail outsideLotDetail = new OutsideLotDetail();
            outsideLotDetail.setInventoryLotId(exceptionInventory.getInventoryLotId());
            outsideLotDetail.setLotSerialId(exceptionInventory.getLotSerialId());
            outsideLotDetail.setBlock(exceptionInventory.getBlock());
            outsideLotDetail.setArticleNumber(exceptionInventory.getArticleNumber());
            outsideLotDetail.setMaterial(exceptionInventory.getMaterial());
            outsideLotDetail.setName(exceptionInventory.getName());
            outsideLotDetail.setInventoryQuantity(exceptionInventory.getInventoryQuantity());
            outsideLotDetail.setShelf(exceptionInventory.getShelf());
            outsideLotDetail.setManufactureDate(exceptionInventory.getManufactureDate());
            outsideLotDetail.setEffectiveDate(exceptionInventory.getEffectiveDate());
            outsideLotDetail.setWarehouse(exceptionInventory.getWarehouse());
            outsideLotDetail.setMeasureUnit(exceptionInventory.getMeasureUnit());
            outsideLotDetail.setWl09(exceptionInventory.getWl09());
            outsideLotDetail.setMaterialCategory(exceptionInventory.getMaterialCategory());
            outsideLotDetail.setArticleNumber(exceptionInventory.getArticleNumber());
            outsideLotDetail.setMaterialSpecification(exceptionInventory.getMaterialSpecification());
            outsideLotDetail.setProcessState(LotStateEnum.Enum.normal.name());
            outsideLotDetail.setCertificateNumber(exceptionInventory.getCertificateNumber());
            outsideLotDetail.setSupplierCertificateNumber(exceptionInventory.getSupplierCertificateNumber());
            outsideLotDetail.setPrice(exceptionInventory.getPrice());
            outsideLotDetails.add(outsideLotDetail);
        }
        pendingTask.setOutsideLotDetailList(outsideLotDetails);
    }

    private void setPendingStocktakingTaskDetailList(MyCountingSubTask myCountingSubTask, PendingStocktakingTask pendingTask) {
        List<CountingTaskInventoryModel> inventories = myCountingSubTask.getInventories();
        if (CollectionUtils.isEmpty(inventories)) {
            return;
        }
        ArrayList<PendingStocktakingTaskDetail> pendingStocktakingTasks = new ArrayList<>();
        for (CountingTaskInventoryModel inventoryModel : inventories) {
            PendingStocktakingTaskDetail detail = new PendingStocktakingTaskDetail();

            detail.setInventoryId(inventoryModel.getInventoryId());
            detail.setWarehouseName(inventoryModel.getWarehouseName());
            detail.setName(inventoryModel.getMaterialName());
            detail.setArticleNumber(inventoryModel.getProductNo());
            detail.setWarehouseId(inventoryModel.getWarehouseId());
            detail.setBlockName(inventoryModel.getBlockName());
            detail.setBlockId(inventoryModel.getBlockId());
            detail.setShelfId(inventoryModel.getShelfId());
            detail.setShelfName(inventoryModel.getShelfName());
            detail.setLotSerialId(inventoryModel.getLotSerialId());
            detail.setMaterialCode(inventoryModel.getMaterialCode());
            detail.setMaterialCategory(inventoryModel.getMaterialSpecification());
            detail.setInventoryQuantity(inventoryModel.getAvailableQuantity());
            detail.setDifferenceQuantity(inventoryModel.getDifferenceQuantity());
            detail.setActualQuantity(inventoryModel.getActualQuantity());
            detail.setMeasureUnit(inventoryModel.getAccountingUnit());
            detail.setStockType(inventoryModel.getStockType());
            detail.setStockSubType(inventoryModel.getStockSubType());
            pendingStocktakingTasks.add(detail);
        }
        pendingTask.setPendingStocktakingTaskDetailList(pendingStocktakingTasks);
    }

    private void setDrawTaskDetailList(MyCountingSubTask myCountingSubTask, PendingStocktakingTask pendingTask) {
        List<PendingStocktakingTaskDetail> pendingStocktakingTaskDetailList = pendingTask.getPendingStocktakingTaskDetailList();
        if (CollectionUtils.isEmpty(pendingStocktakingTaskDetailList)) {
            return;
        }
        Integer checkNum = myCountingSubTask.getTaskManage().getCheckNum();
        if (checkNum > pendingStocktakingTaskDetailList.size()) {
            ArrayList<DrawTaskDetail> drawTaskDetails = new ArrayList<>();
            for (PendingStocktakingTaskDetail detail : pendingStocktakingTaskDetailList) {
                DrawTaskDetail drawTaskDetail = new DrawTaskDetail();
                UserView counter = myCountingSubTask.getCounter();
//                MetaUser metaUser = fabosJsonDao.findById(MetaUser.class, counter.getId());
                drawTaskDetail.setOperator(counter);
                BeanUtils.copyProperties(detail, drawTaskDetail);
                drawTaskDetails.add(drawTaskDetail);
            }
            pendingTask.setDrawTaskDetailList(drawTaskDetails);
        }
        Collections.shuffle(pendingStocktakingTaskDetailList);
        ArrayList<DrawTaskDetail> drawTaskDetails = new ArrayList<>();
        for (int i = 0; i < checkNum; i++) {
            PendingStocktakingTaskDetail detail = pendingStocktakingTaskDetailList.get(i);
            DrawTaskDetail drawTaskDetail = new DrawTaskDetail();
            BeanUtils.copyProperties(detail, drawTaskDetail);
            drawTaskDetails.add(drawTaskDetail);
        }
        pendingTask.setDrawTaskDetailList(drawTaskDetails);
    }


    private PendingStocktakingTask createPendingStocktakingTask(MyCountingSubTask myCountingSubTask) {
        PendingStocktakingTask pendingTask = new PendingStocktakingTask();
        pendingTask.setGeneralCode(myCountingSubTask.getTaskCode());
        pendingTask.setOperator(myCountingSubTask.getOperator());
        pendingTask.setOrderName(myCountingSubTask.getTaskManage().getTaskName());
        pendingTask.setProcessState(PendingStocktakingTaskProcessStateEnum.Enum.examining.name());
        pendingTask.setType(myCountingSubTask.getCountType());
        if (myCountingSubTask.getStocktakingPlan() != null) {
            pendingTask.setStocktakingStartDate(myCountingSubTask.getStocktakingPlan().getStocktakingActiveDate());
            pendingTask.setStocktakingEndDate(myCountingSubTask.getStocktakingPlan().getStocktakingEndDate());
        } else {
            pendingTask.setStocktakingStartDate(myCountingSubTask.getStartTime());
            Date date = new Date();
            pendingTask.setStocktakingEndDate(date);
            myCountingSubTask.setEndTime(date);
        }
        pendingTask.setWorkType(myCountingSubTask.getWorkMethod());
        pendingTask.setOperationType(myCountingSubTask.getOperationMethod());
        pendingTask.setWhetherDraw(myCountingSubTask.getSpotCheckFlag());
        pendingTask.setDrawLotCount(myCountingSubTask.getCheckNum());
        return pendingTask;
    }

    @Override
    public SubmitExamineTaskView fabosJsonFormValue(List<MyCountingSubTask> data, SubmitExamineTaskView fabosJsonForm, String[] param) {
        MyCountingSubTask myCountingSubTask = data.get(0);
        BeanUtils.copyProperties(myCountingSubTask, fabosJsonForm);
        List<CountingTaskInventoryModel> inventories = myCountingSubTask.getInventories();
        if (CollectionUtils.isNotEmpty(inventories)) {
            List<InventoryViewForExamine> viewForExamines = new ArrayList<>();
            for (CountingTaskInventoryModel inventory : inventories) {
                InventoryViewForExamine inventoryViewForExamine = new InventoryViewForExamine();
                BeanUtils.copyProperties(inventory, inventoryViewForExamine);
                viewForExamines.add(inventoryViewForExamine);
            }
            fabosJsonForm.setInventories(viewForExamines);
        }
        List<ExceptionInventoryModel> exceptionInventories = myCountingSubTask.getExceptionInventories();
        if (CollectionUtils.isNotEmpty(exceptionInventories)) {
            List<SubmitExceptionInventoryView> exceptionInventories1 = new ArrayList<>();
            for (ExceptionInventoryModel exceptionInventory : exceptionInventories) {
                SubmitExceptionInventoryView submitExceptionInventoryView = new SubmitExceptionInventoryView();
                BeanUtils.copyProperties(exceptionInventory, submitExceptionInventoryView);
                exceptionInventories1.add(submitExceptionInventoryView);
            }
            fabosJsonForm.setExceptionInventories(exceptionInventories1);
        }
        List<CountingAreaTask> tasks = myCountingSubTask.getTasks();
        return OperationHandler.super.fabosJsonFormValue(data, fabosJsonForm, param);
    }
}
