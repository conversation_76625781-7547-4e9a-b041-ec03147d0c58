package cec.jiutian.bc.library.domain.stockTransfer.model;

import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleModel;
import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleNoExamineModel;
import cec.jiutian.bc.generalModeler.domain.warehouseArea.model.Warehouse;
import cec.jiutian.bc.library.domain.stockTransfer.handler.StockTransferCompleteHandler;
import cec.jiutian.bc.library.domain.stockTransfer.handler.StockTransferDetailAddHandler;
import cec.jiutian.bc.library.domain.stockTransfer.handler.StockTransferReleaseHandler;
import cec.jiutian.bc.library.domain.stockTransfer.handler.TransferOutWarehouseChangeHandler;
import cec.jiutian.bc.library.domain.stockTransfer.proxy.StockTransferProxy;
import cec.jiutian.bc.library.enumeration.NamingRuleCodeEnum;
import cec.jiutian.bc.library.enumeration.OrderCurrentStateEnum;
import cec.jiutian.bc.library.enumeration.WarehouseCategoryEnum;
import cec.jiutian.core.frame.module.MetaUser;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.ReferenceAddType;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowBaseOperation;
import cec.jiutian.view.type.RowOperation;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年03月18日 9:12
 */
@Table(name = "bws_stock_transfer",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@FabosJson(
        name = "调拨管理",
        orderBy = "StockTransfer.createTime desc",
        dataProxy = StockTransferProxy.class,
        power = @Power(add = true, importable = false, export = false, print = false, examine = false, examineDetails = false),
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "currentState != 'EDIT'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "currentState != 'EDIT'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                )
        },
        rowOperation = {
            @RowOperation(
                    title = "发布",
                    code = "StockTransfer@RELEASE",
                    mode = RowOperation.Mode.SINGLE,
                    operationHandler = StockTransferReleaseHandler.class,
                    ifExpr = "currentState!='EDIT'",
                    ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
                    callHint = "是否确定操作？",
                    show = @ExprBool(
                            exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                            params = "StockTransfer@RELEASE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                    )
            ),
            @RowOperation(
                    title = "完成",
                    code = "StockTransfer@COMPLETE",
                    mode = RowOperation.Mode.SINGLE,
                    operationHandler = StockTransferCompleteHandler.class,
                    ifExpr = "currentState!='EXECUTE'",
                    ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
                    callHint = "是否确定操作？",
                    show = @ExprBool(
                            exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                            params = "StockTransfer@COMPLETE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                    )
            )
    }
)
@Entity
@TemplateType(type = "multiTable")
@Builder
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class StockTransfer extends NamingRuleNoExamineModel {

    @Override
    public String getNamingCode() {
        return NamingRuleCodeEnum.StockTransfer.name();
    }

    @FabosJsonField(
            views = @View(title = "仓库类型"),
            edit = @Edit(title = "仓库类型", notNull = true,
                    search = @Search, type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = WarehouseCategoryEnum.class))
    )
    private String warehouseCategory;

    @FabosJsonField(
            views = @View(title = "调出仓库", column = "name"),
            edit = @Edit(title = "调出仓库",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    filter = @Filter(value = "Warehouse.lockState = 'Normal'"),
                    allowAddMultipleRows = false,
                    queryCondition = "{\"type\": \"${warehouseCategory}\"}"
            )
    )
    @ManyToOne
    @JoinColumn(name = "transfer_out_warehouse_id")
    private Warehouse transferOutWarehouse;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "调出人", column = "name", type = ViewType.TABLE_FORM),
            edit = @Edit(title = "调出人",
                    filter = @Filter(value = "MetaUser.state = 'Y'"),
                    notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"/*, type = ReferenceTableType.SelectShowTypeMTO.DROPDOWN_TABLE*/))
    )
    private MetaUser transferOutPerson;

    @FabosJsonField(
            views = @View(title = "接收仓库", column = "name"),
            edit = @Edit(title = "接收仓库",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    filter = @Filter(value = "Warehouse.lockState = 'Normal'"),
                    allowAddMultipleRows = false,
                    queryCondition = "{\"type\": \"${warehouseCategory}\"}"
            )
    )
    @ManyToOne
    @JoinColumn(name = "transfer_in_warehouse_id")
    private Warehouse transferInWarehouse;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "接收人", column = "name", type = ViewType.TABLE_FORM),
            edit = @Edit(title = "接收人",
                    filter = @Filter(value = "MetaUser.state = 'Y'"),
                    notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"/*, type = ReferenceTableType.SelectShowTypeMTO.DROPDOWN_TABLE*/))
    )
    private MetaUser transferInPerson;

    @FabosJsonField(
            views = @View(title = "当前状态"),
            edit = @Edit(title = "当前状态", type = EditType.CHOICE, show = false,
                    search = @Search(),
                    choiceType = @ChoiceType(
                            fetchHandler = OrderCurrentStateEnum.class
                    )
            )
    )
    private String currentState;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "stock_transfer_id")
    @FabosJsonField(
            views = @View(title = "调拨管理明细",
                    type = ViewType.TABLE_VIEW,
                    column = "lotSerialId"),
            edit = @Edit(title = "调拨管理明细",
                    type = EditType.TAB_REFER_ADD,
                    allowAddMultipleRows = true
            ),
            referenceAddType = @ReferenceAddType(referenceClass = "Inventory",
                    queryCondition = "{\"currentState\": [\"normal\"],\"warehouseId\": \"${transferOutWarehouse.id}\",\"lockType\": [\"NO_LOCK\"],\"stagingEscrowFlag\": \"N\"}",
                    editable = {"quantity", "warehouseBlock", "warehouseShelf"},
                    filter = "Inventory.availableQuantity > 0",
                    referenceAddHandler = StockTransferDetailAddHandler.class),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = {"transferOutWarehouse", "transferInWarehouse"},
                    dynamicHandler = TransferOutWarehouseChangeHandler.class))
    )
    private List<StockTransferDetail> stockTransferDetailList;
}
