package cec.jiutian.bc.library.domain.inventoryCountingTaskDetail.model;

import cec.jiutian.bc.library.domain.inventoryCountingTaskManage.model.CountingTaskManage;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.type.Power;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

@Setter
@Getter
@Entity
@Accessors(chain = true)
@Table(name = "my_counting_subtask")
@FabosJson(
        name = "异常批次录入视图",
        orderBy = "MyTaskExceptionAddView.createTime desc",

        power = @Power(add = false, delete = false)

)
public class MyTaskExceptionAddView extends MetaModel {

    @ManyToOne
    @JoinColumn(name = "task_id")
    @FabosJsonField(
            views = @View(title = "盘点任务名称", column = "taskName"),
            edit = @Edit(title = "盘点任务名称",
                    readonly = @Readonly,
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "taskName"),
                    allowAddMultipleRows = false
            )
    )
    private CountingTaskManage taskManage;

    @FabosJsonField(
            views = @View(title = "盘点任务单号"),
            edit = @Edit(title = "盘点任务单号",
                    readonly = @Readonly
            )
    )
    private String taskCode;

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
    @JoinColumn(name = "task_id")
    @FabosJsonField(
            views = @View(title = "异常批次",
                    type = ViewType.TABLE_VIEW),
            edit = @Edit(title = "异常批次",
                    referenceTableType = @ReferenceTableType,
                    type = EditType.TAB_TABLE_ADD
            )
    )
    private List<ExceptionInventoryModel> exceptionInventories;

}
