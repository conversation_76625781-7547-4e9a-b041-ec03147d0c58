package cec.jiutian.bc.library.domain.openingInventory.model;

import cec.jiutian.bc.generalModeler.enumeration.WarehouseShelfTypeEnum;
import cec.jiutian.bc.generalModeler.enumeration.WarehouseStateEnum;
import cec.jiutian.bc.generalModeler.enumeration.YesOrNoEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DependFieldDisplay;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.Search;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@FabosJson(
        name = "货位",
        orderBy = "WarehouseShelfView.createTime desc"
)
@Table(name = "mos_warehouse_shelf")
@Entity
@Getter
@Setter
public class WarehouseShelfView extends MetaModel {
    @FabosJsonField(
            views = @View(title = "货位编码"),
            edit = @Edit(title = "货位编码", notNull = true, search = @Search(vague = true),
                    readonly = @Readonly(add = false, edit = true), inputType = @InputType(length = 40))
    )
    private String code;

    @FabosJsonField(
            views = @View(title = "货位名称"),
            edit = @Edit(title = "货位名称", notNull = true, search = @Search(vague = true),
                    readonly = @Readonly(add = false, edit = true), inputType = @InputType(length = 40))
    )
    private String name;


    @FabosJsonField(
            views = @View(title = "描述"),
            edit = @Edit(title = "描述", type = EditType.input_text)
    )
    private String description;

    @FabosJsonField(
            views = @View(title = "库区类型"),
            edit = @Edit(title = "库区类型", notNull = true, type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = WarehouseShelfTypeEnum.class)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "warehouseBlock", beFilledBy = "type"))
    )
    private String type;

    @FabosJsonField(
            views = @View(title = "货架层数"),
            edit = @Edit(title = "货架层数", numberType = @NumberType(maxExpr = "${warehouseBlock.rowQuantity}"),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "type == 'Stereo'"))
    )
    // 立库货位填写 todo 关联库区类型 同时以库区的行数为最大值
    private Integer blockRow;

    @FabosJsonField(
            views = @View(title = "货架列数"),
            edit = @Edit(title = "货架列数", type = EditType.NUMBER, numberType = @NumberType(maxExpr = "${warehouseBlock.columnQuantity}"),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "type == 'Stereo'"))
    )
    // 立库货位填写 todo 关联库区类型 同时以库区的列数为最大值
    private Integer blockColumn;

    @FabosJsonField(
            views = @View(title = "盘点锁"),
            edit = @Edit(title = "盘点锁", show = false, type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = WarehouseStateEnum.class))
    )
    private String lockState;

    @FabosJsonField(
            views = @View(title = "货位是否已满"),
            edit = @Edit(title = "货位是否已满", notNull = true, type = EditType.CHOICE,
                    defaultVal = "N",
                    choiceType = @ChoiceType(fetchHandler = YesOrNoEnum.class))
    )
    private String fullFlag;

    @FabosJsonField(
            views = @View(title = "是否多批次混放"),
            edit = @Edit(title = "是否多批次混放", notNull = true, type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = YesOrNoEnum.class),
                    defaultVal = "Y")
    )
    private String multipleLotFlag;

    @FabosJsonField(
            views = @View(title = "是否多规格混放"),
            edit = @Edit(title = "是否多规格混放", notNull = true, type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = YesOrNoEnum.class),
                    defaultVal = "Y")
    )
    private String multipleSpecificationFlag;

    @FabosJsonField(
            views = @View(title = "库区id", show = false),
            edit = @Edit(title = "库区id", show = false)
    )
    @Column(name = "warehouse_block_id")
    private String warehouseBlockId;


    @FabosJsonField(
            views = @View(title = "备注"),
            edit = @Edit(title = "备注", type = EditType.TEXTAREA)
    )
    private String remark;

}
