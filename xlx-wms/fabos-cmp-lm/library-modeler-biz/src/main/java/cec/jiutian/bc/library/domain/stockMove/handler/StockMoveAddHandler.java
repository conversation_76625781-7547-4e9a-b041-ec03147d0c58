package cec.jiutian.bc.library.domain.stockMove.handler;

import cec.jiutian.bc.library.domain.stockMove.model.StockMove;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.core.frame.module.MetaUser;
import cec.jiutian.view.fun.OperationHandler;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class StockMoveAddHandler implements OperationHandler<StockMove, StockMove> {

    @Override
    public StockMove fabosJsonFormValue(List<StockMove> data, StockMove fabosJsonForm, String[] param) {
        MetaUser currentUser = new MetaUser(UserContext.get());
        StockMove stockMove = new StockMove();
        stockMove.setAssignerOut(currentUser);
        return stockMove;
    }
}
