package cec.jiutian.bc.library.enumeration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.Getter;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Getter
public enum CountTypeEnum {

    ALL("区域盘"),
    SPECIFIC("存货编码盘"),
    ;
    private final String type;

    CountTypeEnum(String type) {
        this.type = type;
    }

    public static class ChoiceFetch implements ChoiceFetchHandler {
        @Override
        public List<VLModel> fetch(String[] params) {
            return Stream.of(CountTypeEnum.values()).map(countTypeEnum ->
                    new VLModel(countTypeEnum.name(), countTypeEnum.getType())).collect(Collectors.toList());
        }

    }
}
