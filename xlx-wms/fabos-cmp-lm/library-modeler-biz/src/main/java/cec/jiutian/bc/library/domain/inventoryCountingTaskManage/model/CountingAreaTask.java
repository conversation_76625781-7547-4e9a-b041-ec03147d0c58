package cec.jiutian.bc.library.domain.inventoryCountingTaskManage.model;


import cec.jiutian.bc.library.domain.inventoryCountingTaskDetail.model.UserView;
import cec.jiutian.bc.library.enumeration.InventoryCountingTaskStateEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.SubTableField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowBaseOperation;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;


@FabosJson(
        name = "盘点任务明细",
        orderBy = "CountingAreaTask.createTime desc",
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "add",
                        ifExpr = "1 == 1",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "1 == 1",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.HIDE
                ),
        },
        power = @Power(add = false, delete = false)
)
@Table(name = "counting_area_task")
@Entity
@Getter
@Setter
public class CountingAreaTask extends MetaModel {

    @FabosJsonField(
            views = @View(title = "区域id", show = false),
            edit = @Edit(title = "区域id", show = false)
    )
    private String areaId;

    @FabosJsonField(
            views = @View(title = "区域名称"),
            edit = @Edit(title = "区域名称",
                    readonly = @Readonly(add = true, edit = true),
                    search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    @SubTableField
    private String name;

    @FabosJsonField(
            views = @View(title = "仓库", show = false),
            edit = @Edit(title = "仓库", show = false)
    )
    private String warehouseId;
    @FabosJsonField(
            views = @View(title = "仓库"),
            edit = @Edit(title = "仓库",
                    readonly = @Readonly(add = true, edit = true),
                    search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    @SubTableField
    private String warehouseName;

    @FabosJsonField(
            views = @View(title = "库区", show = false),
            edit = @Edit(title = "库区", show = false)
    )
    private String blockId;
    @FabosJsonField(
            views = @View(title = "库区/货架"),
            edit = @Edit(title = "库区/货架",
                    readonly = @Readonly(add = true, edit = true),
                    search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    @SubTableField
    private String blockName;

    @FabosJsonField(
            views = @View(title = "货位", show = false),
            edit = @Edit(title = "货位", show = false)
    )
    private String shelfId;
    @FabosJsonField(
            views = @View(title = "货位"),
            edit = @Edit(title = "货位",
                    readonly = @Readonly(add = true, edit = true),
                    search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    @SubTableField
    private String shelfName;

    @FabosJsonField(
            edit = @Edit(title = "级别", show = false)
    )
    @SubTableField
    private String level;

    @FabosJsonField(
            edit = @Edit(title = "上级id", show = false)
    )
    @SubTableField
    private String parentId;

    @FabosJsonField(
            views = @View(title = "盘点状态"),
            edit = @Edit(title = "盘点状态",
                    readonly = @Readonly(add = true, edit = true),
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = {InventoryCountingTaskStateEnum.ChoiceFetch.class})
            )
    )
    @SubTableField
    private String state;

    @ManyToOne
    @JoinColumn(name = "operator_id")
    @FabosJsonField(
            views = @View(title = "盘点人员", column = "name"),
            edit = @Edit(title = "盘点人员",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    allowAddMultipleRows = false
            )
    )
    @SubTableField
    private UserView operator;
}
