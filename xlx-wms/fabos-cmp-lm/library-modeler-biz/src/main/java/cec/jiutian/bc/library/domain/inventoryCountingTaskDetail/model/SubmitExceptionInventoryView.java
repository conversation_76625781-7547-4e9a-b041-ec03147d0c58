package cec.jiutian.bc.library.domain.inventoryCountingTaskDetail.model;

import cec.jiutian.bc.generalModeler.enumeration.ProductEnum;
import cec.jiutian.bc.generalModeler.enumeration.StockTypeEnum;
import cec.jiutian.bc.library.domain.inventoryCountingTaskDetail.handler.ExceptionOutsideLotCodeGenerateHandler;
import cec.jiutian.bc.library.domain.pendingStocktakingTask.dto.BlockMTO;
import cec.jiutian.bc.library.domain.pendingStocktakingTask.dto.ShelfMTO;
import cec.jiutian.bc.library.domain.pendingStocktakingTask.dto.WarehouseMTO;
import cec.jiutian.bc.library.domain.stockTakingPlan.dto.MaterialMTO;
import cec.jiutian.bc.library.enumeration.LotStateEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DateType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowBaseOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.ConstraintMode;
import jakarta.persistence.Entity;
import jakarta.persistence.ForeignKey;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;

@Entity
@Getter
@Setter
@Accessors(chain = true)
@Table(name = "exception_inventory_model")
@FabosJson(
        name = "异常台账录入信息",
        power = @Power(add = false, edit = false, importable = false),
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "1 == 1",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.HIDE
                ),
                @RowBaseOperation(
                        code = "add",
                        ifExpr = "1 == 1",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.HIDE
                ),
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "1 == 1",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.HIDE
                ),
        }
)
public class SubmitExceptionInventoryView extends MetaModel {
    @FabosJsonField(
            views = @View(title = "库存批次"),
            edit = @Edit(title = "库存批次", notNull = true),
            dynamicField = @DynamicField(
                    dependFiled = @DependFiled(buttonName = "生成", changeBy = {"id"},
                            dynamicHandler = ExceptionOutsideLotCodeGenerateHandler.class))
    )
    private String inventoryLotId;

    @FabosJsonField(
            views = @View(title = "本厂批号"),
            edit = @Edit(title = "本厂批号", notNull = true,
                    search = @Search(vague = true),
                    inputType = @InputType(length = 20))
    )
    private String lotSerialId;

    @ManyToOne
    @JoinColumn(name = "warehouse_id")
    @FabosJsonField(
            views = @View(title = "仓库", column = "name"),
            edit = @Edit(title = "仓库",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(),
                    allowAddMultipleRows = false
            )
    )
    private WarehouseMTO warehouse;

    @ManyToOne
    @JoinColumn(name = "block_id")
    @FabosJsonField(
            views = @View(title = "库区", column = "name"),
            edit = @Edit(title = "库区",
                    queryCondition = "{\"warehouseId\": \"${warehouse.id}\"}",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(),
                    allowAddMultipleRows = false
            )
    )
    private BlockMTO block;

    @ManyToOne
    @JoinColumn(name = "shelf_id")
    @FabosJsonField(
            views = @View(title = "货位", column = "name"),
            edit = @Edit(title = "货位",
                    queryCondition = "{\"blockId\": \"${block.id}\"}",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(),
                    allowAddMultipleRows = false
            )
    )
    private ShelfMTO shelf;

    @ManyToOne
    @JoinColumn(name = "stock_view_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @FabosJsonField(
            views = @View(title = "物资编码", column = "code"),
            edit = @Edit(title = "物资",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(),
                    allowAddMultipleRows = false
            )
    )
    private MaterialMTO material;

    @FabosJsonField(
            views = @View(title = "物资分类"),
            edit = @Edit(title = "物资分类",
                    readonly = @Readonly(add = true, edit = true)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "material", beFilledBy = "materialCategory"))
    )
    private String materialCategory;

    @FabosJsonField(
            views = @View(title = "物资名称"),
            edit = @Edit(title = "物资名称",
                    readonly = @Readonly(add = true, edit = true),
                    notNull = true),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "material", beFilledBy = "name"))
    )
    private String name;

    @FabosJsonField(
            views = @View(title = "库存状态"),
            edit = @Edit(title = "库存状态", type = EditType.CHOICE, show = false,
                    search = @Search(),
                    choiceType = @ChoiceType(
                            fetchHandler = LotStateEnum.class
                    )
            )
    )
    private String processState;

    @FabosJsonField(
            views = @View(title = "规格"),
            edit = @Edit(title = "规格",
                    readonly = @Readonly(add = true, edit = true)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "material", beFilledBy = "specification"))
    )
    private String materialSpecification;

    @FabosJsonField(
            views = @View(title = "品号", show = false),
            edit = @Edit(title = "品号",
                    readonly = @Readonly(add = true, edit = true),
                    show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "material", beFilledBy = "articleNumber"))
    )
    private String articleNumber;

    @FabosJsonField(
            views = @View(title = "技术要求"),
            edit = @Edit(title = "技术要求",
                    readonly = @Readonly(add = true, edit = true),
                    show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "material", beFilledBy = "standardNumber"))
    )
    private String wl09;

    @FabosJsonField(
            views = @View(title = "单位"),
            edit = @Edit(title = "单位",
                    readonly = @Readonly(add = true, edit = true)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "material", beFilledBy = "measureUnit"))
    )
    private String measureUnit;

    @FabosJsonField(
            views = @View(title = "类别"),
            edit = @Edit(title = "类别", type = EditType.CHOICE,
                    readonly = @Readonly,
                    choiceType = @ChoiceType(fetchHandler = StockTypeEnum.class)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "material", beFilledBy = "stockType"))
    )
    private String stockType;
    @FabosJsonField(
            views = @View(title = "子类别"),
            edit = @Edit(title = "子类别",
                    readonly = @Readonly,
                    choiceType = @ChoiceType(fetchHandler = ProductEnum.class)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "material", beFilledBy = "stockSubType"))
    )
    private String stockSubType;

    @FabosJsonField(
            views = @View(title = "库存数量"),
            edit = @Edit(title = "库存数量", notNull = true,
                    numberType = @NumberType(min = 0, max = 999999, precision = 3))
    )
    private Double inventoryQuantity;

    @FabosJsonField(
            views = @View(title = "合格证号"),
            edit = @Edit(title = "合格证号",
                    inputType = @InputType(length = 20))
    )
    private String certificateNumber;
    @FabosJsonField(
            views = @View(title = "原厂合格证号"),
            edit = @Edit(title = "原厂合格证号",
                    inputType = @InputType(length = 20))
    )
    private String supplierCertificateNumber;

    @FabosJsonField(
            views = @View(title = "单价"),
            edit = @Edit(title = "单价")
    )
    private Double price;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "生产日期", type = ViewType.DATE),
            edit = @Edit(title = "生产日期", notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE))
    )
    private Date manufactureDate;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "有效日期", type = ViewType.DATE),
            edit = @Edit(title = "有效日期", notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE))
    )
    private Date effectiveDate;

}
