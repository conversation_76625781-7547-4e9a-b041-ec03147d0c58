package cec.jiutian.bc.library.remote.controller;

import cec.jiutian.bc.library.domain.inventory.service.InventoryService;
import cec.jiutian.bc.library.remote.dto.InventoryPdaQueryDTO;
import cec.jiutian.bc.library.remote.dto.InventoryPdaResultDTO;
import cec.jiutian.core.frame.module.R;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @description:
 */
@RestController
@RequestMapping("/inventory")
public class InventoryController {
    @Resource
    private InventoryService inventoryService;

    @PostMapping("/list")
    public R<List<InventoryPdaResultDTO>> list(@RequestBody InventoryPdaQueryDTO dto) {
        return R.ok(inventoryService.list(dto));
    }
}
