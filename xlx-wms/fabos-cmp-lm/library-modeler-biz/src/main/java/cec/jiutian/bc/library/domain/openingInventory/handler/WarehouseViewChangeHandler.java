package cec.jiutian.bc.library.domain.openingInventory.handler;

import cec.jiutian.bc.generalModeler.domain.warehouseArea.model.WarehouseBlock;
import cec.jiutian.bc.generalModeler.domain.warehouseArea.model.WarehouseShelf;
import cec.jiutian.bc.library.domain.openingInventory.model.OpeningInventory;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.DependFiled;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Component
public class WarehouseViewChangeHandler implements DependFiled.DynamicHandler<OpeningInventory> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public Map<String, Object> handle(OpeningInventory openingInventory) {
        Map<String, Object> result = new HashMap<>();
        if(openingInventory != null && openingInventory.getWarehouse() == null) {
            WarehouseBlock warehouseBlock = new WarehouseBlock();
            warehouseBlock.setId("");
            WarehouseShelf warehouseShelf = new WarehouseShelf();
            warehouseShelf.setId("");
            result.put("warehouseBlock", warehouseBlock);
            result.put("warehouseShelf", warehouseShelf);
        }
        return result;
    }
}
