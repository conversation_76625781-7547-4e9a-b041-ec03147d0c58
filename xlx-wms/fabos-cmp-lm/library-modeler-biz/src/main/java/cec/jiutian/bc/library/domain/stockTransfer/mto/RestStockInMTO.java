package cec.jiutian.bc.library.domain.stockTransfer.mto;

import cec.jiutian.bc.generalModeler.domain.warehouseArea.model.Warehouse;
import cec.jiutian.bc.generalModeler.handler.NamingRuleGenerateHandler;
import cec.jiutian.bc.library.enumeration.StockInCategoryEnum;
import cec.jiutian.bc.library.enumeration.WarehouseCategoryEnum;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.frame.module.MetaUser;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.ReferenceTreeType;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.config.Comment;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import java.util.List;

@FabosJson(
        name = "其他入库",
        orderBy = "RestStockIn.createTime desc",
        filter = @Filter(value = "RestStockIn.type in ('ShipLot', 'Allocate', 'InventoryProfit', 'Sample')"),
        power = @Power(export = false, print = false, edit = false, delete = false, add = false)
)
@Table(name = "purchase_stock_in")
@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TemplateType(type = "multiTable")
public class RestStockInMTO extends MetaModel {

    @FabosJsonField(
            views = @View(title = "类型"),
            edit = @Edit(title = "类型", readonly = @Readonly,
                    search = @Search, type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = StockInCategoryEnum.class))
    )
    private String type;

    @FabosJsonField(
            views = @View(title = "仓库类型"),
            edit = @Edit(title = "仓库类型", notNull = true,
                    search = @Search, type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = WarehouseCategoryEnum.class))
    )
    private String warehouseCategory;

    @FabosJsonField(
            views = @View(title = "来源单号"),
            edit = @Edit(title = "来源单号")
    )
    private String originCode;

    @FabosJsonField(
            views = @View(title = "仓库", column = "name"),
            edit = @Edit(title = "仓库",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    filter = @Filter(value = "Warehouse.lockState = 'Normal'"),
                    allowAddMultipleRows = false,
                    queryCondition = "{\"type\": \"${warehouseCategory}\"}"
            )
    )
    @ManyToOne
    @JoinColumn(name = "warehouse_id")
    private Warehouse warehouse;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "申购部门", column = "name", type = ViewType.TABLE_FORM),
            edit = @Edit(title = "申购部门", type = EditType.REFERENCE_TABLE, allowAddMultipleRows = false,
                    referenceTreeType = @ReferenceTreeType(pid = "parentOrg.id"), search = @Search(), placeHolder = "请选择", notNull = true)
    )
    private OrganizationMTO org;

    @FabosJsonField(
            views = @View(title = "申购人", column = "name"),
            edit = @Edit(title = "申购人",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    filter = @Filter(value = "User.state = 'Y'"),
                    allowAddMultipleRows = false
            )
    )
    @ManyToOne
    @JoinColumn(name = "user_id")
    private MetaUser user;

    @FabosJsonField(
            views = @View(title = "当前状态"),
            edit = @Edit(title = "当前状态", show = false, type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = OrderCurrentStateEnum.class))
    )
    private String currentState;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.EAGER)
    @JoinColumn(name = "purchase_stock_in_id")
    @FabosJsonField(
            views = @View(title = "其他入库明细", type = ViewType.TABLE_VIEW),
            edit = @Edit(title = "其他入库明细", type = EditType.TAB_REFERENCE_GENERATE)
    )
    private List<RestStockInMTODetail> restStockInMTODetailList;

    @Comment("业务单据编码编号")
    @Column(unique = true)
    @FabosJsonField(
            views = @View(title = "单号", index = -9),
            edit = @Edit(title = "单号", readonly = @Readonly(add = false, edit = true),
                    notNull = true, search = @Search(vague = true), inputType = @InputType(length = 40))
            ,
            dynamicField = @DynamicField(
                    dependFiled = @DependFiled(buttonName = "生成", changeBy = {"id"},
                            dynamicHandler = NamingRuleGenerateHandler.class)
            )
    )
    private String generalCode;

}
