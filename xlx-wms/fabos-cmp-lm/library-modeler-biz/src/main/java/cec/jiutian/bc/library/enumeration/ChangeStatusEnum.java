package cec.jiutian.bc.library.enumeration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.Getter;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Getter
public enum ChangeStatusEnum {
    pending("待变更"),
    success("成功"),
    fail("失败"),
    ;

    private final String value;

    ChangeStatusEnum(String value) {
        this.value = value;
    }

    public static class ChoiceFetch implements ChoiceFetchHandler {
        @Override
        public List<VLModel> fetch(String[] params) {
            return Stream.of(ChangeStatusEnum.values()).map(statusEnum ->
                    new VLModel(statusEnum.name(), statusEnum.getValue())).collect(Collectors.toList());
        }

    }
}
