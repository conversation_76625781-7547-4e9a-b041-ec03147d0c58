package cec.jiutian.bc.library.domain.inventoryWarning.dto;

import cec.jiutian.bc.library.domain.inventory.model.Inventory;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
public class InventoryWarningDTO implements Serializable {

    private String inventoryWarningType;

    private Inventory item;

    // Constructor for single inventory
    public InventoryWarningDTO(String inventoryWarningType, Inventory inventory) {
        this.inventoryWarningType = inventoryWarningType;
        this.item = inventory;
    }

}
