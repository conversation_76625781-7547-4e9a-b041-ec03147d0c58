package cec.jiutian.bc.library.domain.stockTakingPlan.dto;

import cec.jiutian.bc.generalModeler.enumeration.MaterialEnum;
import cec.jiutian.bc.generalModeler.enumeration.ProductEnum;
import cec.jiutian.bc.generalModeler.enumeration.StockTypeEnum;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Formula;

/**
 * <AUTHOR>
 * @date 2024/12/12
 * @description TODO
 */
@FabosJson(
        name = "物资主数据MTO",
        filter = @Filter(value = "MaterialMTO.currentStatus = 'Y'")
)
@Entity
@Getter
@Setter
@Table(name = "stock_view")
public class MaterialMTO {

    @Id
    @FabosJsonField(
            views = @View(title = "id", show = false),
            edit = @Edit(title = "id")
    )
    private String id;

    @FabosJsonField(
            views = @View(title = "编码"),
            edit = @Edit(title = "编码", readonly = @Readonly(add = false, edit = true), notNull = true, search = @Search(vague = true))
    )
    private String code;

    @FabosJsonField(
            views = @View(title = "物资名称"),
            edit = @Edit(title = "物资名称", notNull = true)
    )
    private String name;

    @FabosJsonField(
            views = @View(title = "规格"),
            edit = @Edit(title = "规格", notNull = false)
    )
    private String specification;

    @FabosJsonField(
            views = @View(title = "品号"),
            edit = @Edit(title = "品号", show = false)
    )
    private String brandCode;

    @FabosJsonField(
            views = @View(title = "技术要求"),
            edit = @Edit(title = "技术要求", show = false)
    )
    private String standardNumber;

    @Formula("(select t2.unit_Chn_name from stock_view t1, measure_unit t2 where t1.account_unit_id = t2.id and t1.id = id)")
    @FabosJsonField(
            views = @View(title = "单位"),
            edit = @Edit(title = "单位", notNull = true)
    )
    private String measureUnit;

    @Formula("(select t2.name from stock_view t1, stock_category_view t2 where t1.stock_category_id = t2.id and t1.id = id)")
    @FabosJsonField(
            views = @View(title = "物资类别"),
            edit = @Edit(title = "物资类别")
    )
    private String materialCategory;

    @FabosJsonField(
            views = @View(title = "类别"),
            edit = @Edit(title = "类别", type = EditType.CHOICE, readonly = @Readonly,
                    choiceType = @ChoiceType(fetchHandler = StockTypeEnum.class))
    )
    private String stockType;
    @FabosJsonField(
            views = @View(title = "子类别"),
            edit = @Edit(title = "子类别", type = EditType.CHOICE, readonly = @Readonly,
                    choiceType = @ChoiceType(fetchHandler = ProductEnum.class))
    )
    private String stockSubType;

    @FabosJsonField(
            views = @View(title = "当前状态", show = false),
            edit = @Edit(title = "当前状态", show = false, type = EditType.CHOICE,
                    search = @Search(),
                    choiceType = @ChoiceType(
                            fetchHandler = MaterialEnum.class
                    )
            )
    )
    private String currentStatus;
}
