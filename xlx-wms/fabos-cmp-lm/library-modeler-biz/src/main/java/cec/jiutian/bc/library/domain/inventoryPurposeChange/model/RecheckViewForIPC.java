package cec.jiutian.bc.library.domain.inventoryPurposeChange.model;

import cec.jiutian.bc.generalModeler.domain.material.model.StockView;
import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleModel;
import cec.jiutian.bc.generalModeler.enumeration.ProductEnum;
import cec.jiutian.bc.generalModeler.enumeration.StockTypeEnum;
import cec.jiutian.bc.library.domain.inventory.model.Inventory;
import cec.jiutian.bc.library.domain.inventoryPurposeChange.handler.InventoryPurposeChangeViewAddHandler;
import cec.jiutian.bc.library.enumeration.ChangeStatusEnum;
import cec.jiutian.bc.library.enumeration.DellStatusEnum;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.ReferenceAddType;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.AttachmentType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import jakarta.persistence.CascadeType;
import jakarta.persistence.ConstraintMode;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.ForeignKey;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OrderBy;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Table(name = "inventory_purpose_change")
@FabosJson(
        name = "用途变更发起复验视图",
        orderBy = "RecheckViewForIPC.createTime desc",
        power = @Power(add = false, delete = false)
)
@Entity
@Getter
@Setter
public class RecheckViewForIPC extends NamingRuleModel {

    @FabosJsonField(
            views = @View(title = "物资名称", show = false, column = "materialName"),
            edit = @Edit(title = "物资名称",
                    queryCondition = "{\"lockType\": [\"NO_LOCK\"]}",
                    filter = @Filter(value = "Inventory.stockType == 'MATERIAL' or (Inventory.stockType == 'PRODUCT' and Inventory.stockSubType == 'SEMIPRODUCT')"),
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(label = "materialName")
            )
    )
    @ManyToOne
    private Inventory inventory;

    @FabosJsonField(
            views = @View(title = "物资名称"),
            edit = @Edit(title = "物资名称", search = @Search(vague = true),
                    show = false, readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inventory", beFilledBy = "materialName"))
    )
    private String materialName;

    @FabosJsonField(
            views = @View(title = "物资编码"),
            edit = @Edit(title = "物资编码", search = @Search(vague = true), readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inventory", beFilledBy = "materialCode"))
    )
    private String materialCode;

    @FabosJsonField(
            views = @View(title = "规格"),
            edit = @Edit(title = "规格", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inventory", beFilledBy = "materialSpecification"))
    )
    private String materialSpecification;

    @FabosJsonField(
            views = @View(title = "单位"),
            edit = @Edit(title = "单位", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inventory", beFilledBy = "accountingUnit"))
    )
    private String accountUnit;

    @FabosJsonField(
            views = @View(title = "类别"),
            edit = @Edit(title = "类别", type = EditType.CHOICE, readonly = @Readonly,
                    choiceType = @ChoiceType(fetchHandler = StockTypeEnum.class)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inventory", beFilledBy = "stockType"))
    )
    private String stockType;
    @FabosJsonField(
            views = @View(title = "子类别"),
            edit = @Edit(title = "子类别", type = EditType.CHOICE, readonly = @Readonly,
                    choiceType = @ChoiceType(fetchHandler = ProductEnum.class)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inventory", beFilledBy = "stockSubType"))
    )
    private String stockSubType;

    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(
            name = "inventory_purpose_change_product",  //中间表表名
            inverseForeignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT),
            joinColumns = @JoinColumn(name = "inventory_purpose_change_id", referencedColumnName = "id"),
            inverseJoinColumns = @JoinColumn(name = "product_id", referencedColumnName = "id"))
    @FabosJsonField(
            views = @View(title = "变更后用途", column = "name", show = false),
            edit = @Edit(title = "变更后用途",
                    notNull = true,
                    type = EditType.TAB_TABLE_REFER,
                    referenceTableType = @ReferenceTableType,
                    filter = @Filter(value = "StockView.stockType = 'PRODUCT' and StockView.stockSubType = 'FINALPRODUCT'")
            )
    )
    private List<StockView> products;

    @FabosJsonField(
            views = @View(title = "变更后用途"),
            edit = @Edit(title = "变更后用途",
                    readonly = @Readonly(add = false, edit = true),
                    show = false,
                    search = @Search(vague = true))
    )
    private String newPurpose;

    @FabosJsonField(
            views = @View(title = "变更状态", show = true),
            edit = @Edit(title = "变更状态", defaultVal = "pending", show = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ChangeStatusEnum.ChoiceFetch.class))
    )
    private String changeStatus;

    @FabosJsonField(
            views = @View(title = "处理状态"),
            edit = @Edit(title = "处理状态", defaultVal = "pending", show = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = DellStatusEnum.ChoiceFetch.class))
    )
    private String dellStatus;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = false)
    @JoinColumn(name = "inventory_view_id")
    @OrderBy
    @FabosJsonField(
            edit = @Edit(title = "变更批次明细",
                    type = EditType.TAB_REFER_ADD
                    //filter = @Filter(value = "Inventory.currentStatus = in ('normal','extended','unqualified')")
            ),
            referenceAddType = @ReferenceAddType(referenceClass = "Inventory",
                    queryCondition = "{\"lockType\": [\"NO_LOCK\"], \"stagingEscrowFlag\": \"N\", \"materialName\": \"${inventory.materialName}\", \"availableQuantity\":\"1,\" ,\"currentState\": [\"normal\", \"extended\", \"unqualified\"]}",
                    referenceAddHandler = InventoryPurposeChangeViewAddHandler.class),
            views = @View(title = "变更批次明细", type = ViewType.TABLE_VIEW, extraPK = "inventoryId")
    )
    private List<InventoryPurposeChangeView> inventoryViewList;

    @FabosJsonField(
            views = @View(title = "附件"),
            edit = @Edit(title = "附件",
                    type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(size = 5120, maxLimit = 10, type = AttachmentType.Type.BASE)
            )
    )
    private String attachment;

    @FabosJsonField(
            views = @View(title = "备注", toolTip = true),
            edit = @Edit(title = "备注", search = @Search(vague = true),
                    type = EditType.TEXTAREA,
                    inputType = @InputType(length = 200)
            )
    )
    private String notes;

    @Override
    public String getNamingCode() {
        return "InventoryPurposeChange";
    }
}
