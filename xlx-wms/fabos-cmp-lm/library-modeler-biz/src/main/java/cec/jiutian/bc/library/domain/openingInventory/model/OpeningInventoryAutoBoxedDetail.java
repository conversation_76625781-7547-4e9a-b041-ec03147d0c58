package cec.jiutian.bc.library.domain.openingInventory.model;

import cec.jiutian.bc.generalModeler.domain.material.model.MaterialCategory;
import cec.jiutian.bc.generalModeler.domain.measureUnit.model.MeasureUnit;
import cec.jiutian.bc.generalModeler.enumeration.WarehouseTypeEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.ReferenceTreeType;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowBaseOperation;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025年07月02日 16:11
 */
@FabosJson(
        name = "期初库存自动装箱明细",
        orderBy = "OpeningInventoryAutoBoxedDetail.createTime desc",
        power = @Power(importable = false, add = false),
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "1 == 1",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.HIDE
                ),
                @RowBaseOperation(
                        code = "add",
                        ifExpr = "1 == 1",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.HIDE
                ),
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "1 == 1",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.HIDE
                )
        }
)
@Table(name = "bwi_opening_inventory_auto_boxed_detail",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"id"})
        }
)
@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TemplateType(type = "usual")
public class OpeningInventoryAutoBoxedDetail extends MetaModel {

    @FabosJsonField(
            views = @View(title = "物料主键id", show = false),
            edit = @Edit(title = "物料主键id", show = false)
    )
    private String materialId;

    @FabosJsonField(
            views = @View(title = "物料编码", show = true),
            edit = @Edit(title = "物料编码", show = false)
    )
    private String materialCode;

    @FabosJsonField(
            views = @View(title = "物料名称", show = true),
            edit = @Edit(title = "物料名称", readonly = @Readonly(add = false, edit = false), show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "arrivalRegistrationMaterialLotDTO", beFilledBy = "materialName"))
    )
    private String materialName;

    @FabosJsonField(
            views = @View(title = "型号规格", show = true),
            edit = @Edit(title = "型号规格", show = false)
    )
    private String materialSpecification;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "种类", column = "code", show = true),
            edit = @Edit(title = "种类", readonly = @Readonly(add = false, edit = true),
                    show = false,
                    type = EditType.REFERENCE_TREE,
                    referenceTreeType = @ReferenceTreeType(pid = "parentCategory.id"),
                    search = @Search())
    )
    private MaterialCategory materialCategory;

    @FabosJsonField(
            views = @View(title = "来料批号", show = true),
            edit = @Edit(title = "来料批号", show = false)
    )
    private String inventoryLotId;

    @FabosJsonField(
            views = @View(title = "本厂批号", show = true),
            edit = @Edit(title = "批次号", show = false)
    )
    private String lotSerialId;

    /*
        箱次编码= 批次号+箱号
     */
    @FabosJsonField(
            views = @View(title = "箱次编码", show = true),
            edit = @Edit(title = "箱次编码", show = false)
    )
    private String boxedCode;

    @FabosJsonField(
            views = @View(title = "箱号", show = true),
            edit = @Edit(title = "箱号", show = false)
    )
    private String boxNo;

    @FabosJsonField(
            views = @View(title = "数量", rowEdit = true, width = "100px"),
            edit = @Edit(title = "数量", notNull = true, tips = "物料数量",
                    numberType = @NumberType(min = 0, max = 9999999, precision = 2))
    )
    private Double quantity;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "单位", column = "unitChnName"),
            edit = @Edit(title = "单位",
                    type = EditType.REFERENCE_TABLE,
                    show = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "unitChnName",
                            type = ReferenceTableType.SelectShowTypeMTO.LIST, groupField = "unitType")
            )
    )
    private MeasureUnit accountUnit;

    @FabosJsonField(
            views = @View(title = "仓库名称"),
            edit = @Edit(title = "仓库名称",
                    inputType = @InputType(length = 20))
    )
    private String warehouseName;

    @FabosJsonField(
            views = @View(title = "仓库", show = false),
            edit = @Edit(title = "仓库", show = false)
    )
    private String warehouseId;

    @FabosJsonField(
            views = @View(title = "仓库类型"),
            edit = @Edit(title = "仓库类型", notNull = true, type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = WarehouseTypeEnum.class))
    )
    private String warehouseType;

    @FabosJsonField(
            views = @View(title = "库区名称"),
            edit = @Edit(title = "库区名称",
                    inputType = @InputType(length = 20))
    )
    private String blockName;

    @FabosJsonField(
            views = @View(title = "库区", show = false),
            edit = @Edit(title = "库区", show = false)
    )
    private String blockId;

    @FabosJsonField(
            views = @View(title = "货位名称"),
            edit = @Edit(title = "货位名称",
                    inputType = @InputType(length = 20))
    )
    private String shelfName;

    @FabosJsonField(
            views = @View(title = "货位", show = false),
            edit = @Edit(title = "货位", show = false)
    )
    private String shelfId;

    @FabosJsonField(
            views = @View(title = "托管单位id", show = false),
            edit = @Edit(title = "托管单位id", show = false)
    )
    private String orgId;

    @FabosJsonField(
            views = @View(title = "托管单位"),
            edit = @Edit(title = "托管单位")
    )
    private String orgName;

    @FabosJsonField(
            views = {
                    @View(title = "期初库存自动装箱明细", show = false, column = "materialName")
            },
            edit = @Edit(title = "期初库存自动装箱明细", type = EditType.REFERENCE_TABLE, show = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "materialName"))
    )
    @ManyToOne
    @JsonIgnoreProperties("openingInventoryAutoBoxedDetailList")
    private OpeningInventory openingInventory;
}
