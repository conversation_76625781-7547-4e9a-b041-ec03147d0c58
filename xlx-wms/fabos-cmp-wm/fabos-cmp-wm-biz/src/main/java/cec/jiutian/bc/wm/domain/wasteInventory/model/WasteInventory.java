package cec.jiutian.bc.wm.domain.wasteInventory.model;

import cec.jiutian.bc.wm.domain.wasteInventory.proxy.WasteInventoryProxy;
import cec.jiutian.bc.wm.enumeration.WasteInventoryStateEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Table(name = "bwi_waste_inventory")
@FabosJson(
        name = "废旧物资台账",
        orderBy = "WasteInventory.createTime desc",
        dataProxy = WasteInventoryProxy.class,
        power = @Power(add = false, edit = false, delete = false)
)
@Entity
@Getter
@Setter
@TemplateType(type = "usual")
public class WasteInventory extends MetaModel {
    @FabosJsonField(
            views = @View(title = "单据编号", show = false),
            edit = @Edit(title = "单据编号", search = @Search(vague = true), show = false)
    )
    private String orderNumber;

    @FabosJsonField(
            views = @View(title = "物资名称"),
            edit = @Edit(title = "物资名称", search = @Search(vague = true))
    )
    private String name;

    @FabosJsonField(
            views = @View(title = "物料数据主键id", show = false),
            edit = @Edit(title = "物料数据主键id", show = false)
    )
    private String materialId;

    @FabosJsonField(
            views = @View(title = "物料编码"),
            edit = @Edit(title = "物料编码", search = @Search(vague = true))
    )
    private String materialCode;

    @FabosJsonField(
            views = @View(title = "品号"),
            edit = @Edit(title = "品号")
    )
    private String articleNumber;

    @FabosJsonField(
            views = @View(title = "规格"),
            edit = @Edit(title = "规格")
    )
    private String materialSpecification;

    @FabosJsonField(
            views = @View(title = "单位"),
            edit = @Edit(title = "单位")
    )
    private String measureUnit;

    @FabosJsonField(
            views = @View(title = "总重量"),
            edit = @Edit(title = "总重量", inputType = @InputType(length = 20), numberType = @NumberType(min = 0, precision = 3))
    )
    private Double totalWeight;

    @FabosJsonField(
            views = @View(title = "台账总量"),
            edit = @Edit(title = "台账总量",
                    inputType = @InputType(length = 20), numberType = @NumberType(min = 0, precision = 3))
    )
    private Double accountingUnitQuantity;

    // 主要是待出库状态的锁定数量
    @FabosJsonField(
            views = @View(title = "待出库量"),
            edit = @Edit(title = "待出库量",
                    inputType = @InputType(length = 20), numberType = @NumberType(min = 0, precision = 3))
    )
    private Double lockQuantity;

/*    @FabosJsonField(
            views = @View(title = "已出库量"),
            edit = @Edit(title = "已出库量",
                    inputType = @InputType(length = 20),numberType = @NumberType(min = 0, precision = 3))
    )
    private Double handleLockQuantity;*/

    @FabosJsonField(
            views = @View(title = "可用数量"),
            edit = @Edit(title = "可用数量", search = @Search(vague = true),
                    inputType = @InputType(length = 20), numberType = @NumberType(min = 0, max = ********, precision = 3))
    )
    private Double availableQuantity;

    @FabosJsonField(
            views = @View(title = "当前状态"),
            edit = @Edit(title = "当前状态", type = EditType.CHOICE, search = @Search(vague = false), notNull = true,
                    choiceType = @ChoiceType(fetchHandler = WasteInventoryStateEnum.class))
    )
    private String currentState;
}
