package cec.jiutian.bc.outbound.domain.stockOutApply.proxy;

import cec.jiutian.bc.outbound.domain.stockOutApply.model.OtherStockOutApply;
import cec.jiutian.bc.outbound.enumeration.RestStockOutTypeEnum;
import cec.jiutian.bc.outbound.port.enumeration.StockOutApplyProcessStateEnum;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.data.jpa.JpaCrud;
import cec.jiutian.view.fun.DataProxy;
import cn.hutool.core.date.DateUtil;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;

@Component
public class OtherStockOutApplyProxy implements DataProxy<OtherStockOutApply> {

    @Resource
    private JpaCrud jpaCrud;

    @Override
    public void beforeAdd(OtherStockOutApply request) {
        if(request != null) {
            if(RestStockOutTypeEnum.Enum.SaleDeliveryStockOut.name().equals(request.getOtherStockOutCode())) {
                throw new FabosJsonApiErrorTip("不能手动创建销售发货出库类型的申请单, 这种类型的单据是销售发货出库的时候自动创建的, 请检查!");
            }
            if (CollectionUtils.isEmpty(request.getDetails())) {
                throw new FabosJsonApiErrorTip("申请明细不能为空!");
            }
            request.setCurrentState(OrderCurrentStateEnum.Enum.EXECUTE.name());
            request.setApplyDate(DateUtil.beginOfDay(DateUtil.date()));
            request.setProcessState(StockOutApplyProcessStateEnum.Enum.waiting_outbound.name());
            handleDetailAdd(request);
        }
    }

    private void handleDetailAdd(OtherStockOutApply request) {
        if (!CollectionUtils.isEmpty(request.getDetails())) {
            if(Objects.isNull(request.getGeneralCode())){
                throw new FabosJsonApiErrorTip("请先生成单号！");
            }
            AtomicInteger index = new AtomicInteger(1);
            request.getDetails().forEach(d -> {
                d.setOtherStockOutApplyDetailNumber(request.getGeneralCode() + "-" + String.format("%03d", index.get()));
                d.setSequenceNumber(index.get());
                d.setCreateBy(request.getCreateBy());
                d.setCreateTime(LocalDateTime.now());
                d.setUpdateBy(request.getUpdateBy());
                d.setUpdateTime(LocalDateTime.now());
                index.getAndIncrement();
            });
        }
    }

    @Override
    public void beforeUpdate(OtherStockOutApply request) {
        if(request != null) {
            if(RestStockOutTypeEnum.Enum.SaleDeliveryStockOut.name().equals(request.getOtherStockOutCode())) {
                throw new FabosJsonApiErrorTip("不能手动创建销售发货出库类型的申请单, 这种类型的单据是销售发货出库的时候自动创建的, 请检查!");
            }
        }
    }

    @Override
    public void beforeDelete(OtherStockOutApply request) {

    }
}
