package cec.jiutian.bc.outbound.domain.tailInventory.service;

import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.outbound.domain.tailInventory.model.TailInventory;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.data.jpa.JpaCrud;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @description:
 */
@Service
@Slf4j
@Transactional
public class TailInventoryService {
    @Resource
    private JpaCrud jpaCrud;
    @Resource
    private FabosJsonDao fabosJsonDao;
    @Resource
    private NamingRuleService namingRuleService;

    public void insetBatch(List<TailInventory> TailInventoryList) {
        if (CollectionUtils.isNotEmpty(TailInventoryList)) {
            jpaCrud.insertBatch(TailInventoryList);
        }
    }


}
