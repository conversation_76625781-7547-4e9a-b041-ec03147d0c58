package cec.jiutian.bc.outbound.domain.cuttingDelivery.handler;

import cec.jiutian.bc.outbound.domain.cuttingDelivery.model.CuttingDelivery;
import cec.jiutian.bc.outbound.domain.cuttingDelivery.model.CuttingDeliveryDetail;
import cec.jiutian.bc.outbound.domain.cuttingOrder.model.CuttingOrder;
import cec.jiutian.bc.outbound.domain.cuttingOrder.model.CuttingOrderDetail;
import cec.jiutian.bc.outbound.domain.cuttingOrder.model.CuttingOrderDetailLot;
import cec.jiutian.data.jpa.JpaCrud;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class CuttingDeliveryDetailHandler implements DependFiled.DynamicHandler<CuttingDelivery> {
    @Resource
    private JpaCrud jpaCrud;

    /**
     * 装载数据 -->下料配送明细中
     *
     * @param cuttingDelivery （从前端）传入表单内的所有值
     * @return 下料计划明细Map
     */
    @Override
    public Map<String, Object> handle(CuttingDelivery cuttingDelivery) {
        HashMap<String, Object> resultMap = new HashMap<>();
        // 获取下料出库单
        CuttingOrder cuttingOrder = cuttingDelivery.getCuttingOrder();
        if (cuttingOrder != null) {
            List<CuttingDeliveryDetail> cuttingDeliveryDetails = new ArrayList<>();
            CuttingOrder cuttingOrderById = jpaCrud.getById(CuttingOrder.class, cuttingOrder.getId());
            // 获取下料出库单明细
            List<CuttingOrderDetail> cuttingOrderDetails = cuttingOrderById.getCuttingOrderDetails();
            // 装载下料出库单明细 --> 下料配送明细
            for (CuttingOrderDetail cuttingOrderDetail : cuttingOrderDetails) {
                List<CuttingOrderDetailLot> codDetailLots = cuttingOrderDetail.getCuttingOrderDetailLots();
                if (!CollectionUtils.isEmpty(codDetailLots)) {
                    for (CuttingOrderDetailLot codDetailLot : codDetailLots) {
                        CuttingDeliveryDetail detail = new CuttingDeliveryDetail();
                        // 物料主键id
                        detail.setMaterialId(cuttingOrderDetail.getMaterialId());
                        // 批次数据主键
                        detail.setInventoryId(codDetailLot.getInventoryId());
                        // 物料编号
                        detail.setMaterialCode(cuttingOrderDetail.getMaterialCode());
                        // 添加类别和子类别
                        detail.setStockType(cuttingOrderDetail.getStockType());
                        detail.setStockSubType(cuttingOrderDetail.getStockSubType());
                        // 物料名称
                        detail.setMaterialName(cuttingOrderDetail.getMaterialName());
                        // 规格
                        detail.setMaterialSpecification(cuttingOrderDetail.getMaterialSpecification());
                        // 牌号
                        detail.setBrandCode(cuttingOrderDetail.getBrandCode());
                        // 单位
                        detail.setAccountUnit(cuttingOrderDetail.getAccountUnit());
                        // 下料尺寸
                        detail.setCuttingSize(cuttingOrderDetail.getCuttingSize());
                        // 下料数
                        detail.setCuttingQuantity(cuttingOrderDetail.getCuttingQuantity());
                        // 长度
                        detail.setLength(cuttingOrderDetail.getLength());
                        // 实发数量
                        detail.setActualQuantity(cuttingOrderDetail.getActualQuantity());
                        // 本厂批号
                        detail.setLotSerialId(codDetailLot.getLotSerialId());
                        // 原厂批号
                        detail.setSupplierLotSerialId(codDetailLot.getSupplierLotSerialId());
                        // 重量
                        detail.setQuantity(codDetailLot.getQuantity());
                        // 供应商
                        detail.setSupplierName(codDetailLot.getSupplierName());
                        // 合格证号
                        detail.setCertificateNumber(codDetailLot.getCertificateNumber());
                        // 原厂合格证号
                        detail.setSupplierCertificateNumber(codDetailLot.getSupplierCertificateNumber());
                        // 单价
                        detail.setPrice(codDetailLot.getPrice());
                        // 库存批次
                        detail.setInventoryLotId(codDetailLot.getInventoryLotId());
                        // 仓库id和名称
                        detail.setWarehouseId(codDetailLot.getWarehouseId());
                        detail.setWarehouseName(codDetailLot.getWarehouseName());
                        // 库区id和名称
                        detail.setBlockId(codDetailLot.getBlockId());
                        detail.setBlockName(codDetailLot.getBlockName());
                        // 货位id和名称
                        detail.setShelfId(codDetailLot.getShelfId());
                        detail.setShelfName(codDetailLot.getShelfName());
                        // 入队
                        cuttingDeliveryDetails.add(detail);
                    }
                }
            }
            resultMap.put("cuttingDeliveryDetails", cuttingDeliveryDetails);
        }
        return resultMap;
    }

}
