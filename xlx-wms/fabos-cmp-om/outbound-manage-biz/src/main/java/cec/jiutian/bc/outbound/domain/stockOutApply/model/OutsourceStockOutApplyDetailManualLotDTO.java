package cec.jiutian.bc.outbound.domain.stockOutApply.model;

import cec.jiutian.bc.generalModeler.enumeration.StockTypeEnum;
import cec.jiutian.bc.outbound.util.OmMaterialByCodeTableFormHandler;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Power;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Table(name = "bwi_outsource_lot_detail")
@FabosJson(
        name = "外包批次明细"
)

@Entity
@Getter
@Setter
public class OutsourceStockOutApplyDetailManualLotDTO extends MetaModel {

    @FabosJsonField(
            views = @View(title = "发货批号", width = "300px", type = ViewType.text),
            edit = @Edit(title = "发货批号", notNull = true,
                    inputType = @InputType(length = 40))
/*            ,dynamicField = @DynamicField(
                    dependFiled = @DependFiled(buttonName = "生成", changeBy = {"id11"},
                            dynamicHandler = NamingRuleManualGenerateHandler.class)
            )*/
    )
    private String deliveryBatchNo;

    @FabosJsonField(
            views = @View(title = "顺序号"),
            edit = @Edit(title = "顺序号", show = false)
    )
    private Integer sequenceNumber;

    @FabosJsonField(
            views = @View(title = "型号"),
            edit = @Edit(title = "型号", notNull = false,
                    type = EditType.CHOICE
                    , search = @Search(vague = true),
                    choiceType = @ChoiceType(fetchHandler = StockTypeEnum.class)
            )
    )
    private String stockType;


    @ManyToOne
    @FabosJsonField(
            views = @View(title = "内包物料", column = "generalCode", show = false),
            edit = @Edit(title = "内包物料", type = EditType.REFERENCE_TABLE, show = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "outsourceStockOutApplyMaterialDetailNumber")
            )
    )
    private OutsourceStockOutApplyMaterialAutoDetail outsourceStockOutApplyMaterialAutoDetail;

    @FabosJsonField(
            views = @View(title = "申请数量"),
            edit = @Edit(title = "申请数量", notNull = true, numberType = @NumberType(min = 0, precision = 3))
    )
    private Double requestQuantity;

    @FabosJsonField(
            views = @View(title = "剩余数量"),
            edit = @Edit(title = "剩余数量", notNull = true, numberType = @NumberType(min = 0, precision = 3))
    )
    private Double restQuantity;

    @FabosJsonField(
            views = @View(title = "本厂批号"),
            edit = @Edit(title = "本厂批号", readonly = @Readonly)
    )
    private String serialLotId;

    @FabosJsonField(
            views = @View(title = "物料批次id", show = false),
            edit = @Edit(title = "物料批次id", show = false)
    )
    private String inventoryId;

    @FabosJsonField(
            views = @View(title = "物料主键id", show = false),
            edit = @Edit(title = "物料主键id", show = false)
    )
    private String materialId;

    @FabosJsonField(
            views = @View(title = "物料编码"),
            edit = @Edit(title = "物料编码", readonly = @Readonly())
    )
    private String materialCode;

    @FabosJsonField(
            views = @View(title = "物料名称", type = ViewType.TABLE_FORM, tableFormParamsHandler = OmMaterialByCodeTableFormHandler.class),
            edit = @Edit(title = "物料名称", readonly = @Readonly)
    )
    private String materialName;
    @FabosJsonField(
            views = @View(title = "型号"),
            edit = @Edit(title = "型号", readonly = @Readonly)
    )
    private String materialSpecification;

    @FabosJsonField(
            views = @View(title = "单位"),
            edit = @Edit(title = "单位", readonly = @Readonly)
    )
    private String measureUnit;


    @FabosJsonField(
            views = @View(title = "来料批号", show = false),
            edit = @Edit(title = "来料批号", readonly = @Readonly)
    )
    private String originLotId;

    @FabosJsonField(
            views = @View(title = "仓库名称"),
            edit = @Edit(title = "仓库名称",
                    inputType = @InputType(length = 20))
    )
    private String warehouseName;
    @FabosJsonField(
            views = @View(title = "仓库", show = false),
            edit = @Edit(title = "仓库", show = false)
    )
    private String warehouseId;
    @FabosJsonField(
            views = @View(title = "库区名称"),
            edit = @Edit(title = "库区名称",
                    inputType = @InputType(length = 20))
    )
    private String blockName;
    @FabosJsonField(
            views = @View(title = "库区", show = false),
            edit = @Edit(title = "库区", show = false)
    )
    private String blockId;
    @FabosJsonField(
            views = @View(title = "货位名称"),
            edit = @Edit(title = "货位名称",
                    inputType = @InputType(length = 20))
    )
    private String shelfName;
    @FabosJsonField(
            views = @View(title = "货位", show = false),
            edit = @Edit(title = "货位", show = false)
    )
    private String shelfId;
    @FabosJsonField(
            views = @View(title = "制造厂商"),
            edit = @Edit(title = "制造厂商", readonly = @Readonly)
    )
    private String manufacturer;

    @FabosJsonField(
            views = @View(title = "供应商"),
            edit = @Edit(title = "供应商", readonly = @Readonly())
    )
    private String supplier;


    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "生产日期", type = ViewType.DATE),
            edit = @Edit(title = "生产日期", readonly = @Readonly)
    )
    private Date manufactureDate;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "有效日期", type = ViewType.DATE),
            edit = @Edit(title = "有效日期", readonly = @Readonly)
    )
    private Date expireDate;


}
