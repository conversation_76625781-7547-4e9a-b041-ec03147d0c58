package cec.jiutian.bc.outbound.domain.stockOutApply.model;

import cec.jiutian.bc.outbound.domain.stockOutApply.handler.SupplierReturnApplyReferenceAddHandler;
import cec.jiutian.bc.outbound.domain.stockOutApply.handler.SupplierReturnApplyReleaseHandler;
import cec.jiutian.bc.outbound.domain.stockOutApply.proxy.SupplierReturnApplyProxy;
import cec.jiutian.bc.outbound.enumeration.OmNamingRuleCodeEnum;
import cec.jiutian.bc.outbound.port.dto.OrgOmMTO;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.ReferenceAddType;
import cec.jiutian.view.ReferenceTreeType;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.AttachmentType;
import cec.jiutian.view.field.edit.DateType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.RowBaseOperation;
import cec.jiutian.view.type.RowOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;


@FabosJson(
        name = "供应商退货申请",
        orderBy = "SupplierReturnApply.createTime desc",
        dataProxy = SupplierReturnApplyProxy.class,
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "currentState!='EDIT'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "currentState!='EDIT'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                )
        }
        ,rowOperation = {
        @RowOperation(
                title = "发布",
                code = "SupplierReturnApply@RELEASE",
                mode = RowOperation.Mode.SINGLE,
                operationHandler = SupplierReturnApplyReleaseHandler.class,
                ifExpr = "currentState!='EDIT'",
                ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
                callHint = "是否确定操作？",
                show = @ExprBool(
                        exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                        params = "StockRelocation@RELEASE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                )
        )
}
)
@Table(name = "bwi_supplier_return_apply")
@Entity
@Getter
@Setter
@TemplateType(type = "multiTable")
public class SupplierReturnApply extends ApplyStateModel {


    @Override
    public String getNamingCode() {
        return OmNamingRuleCodeEnum.SupplierReturnApplyNumber.name();
    }

    @FabosJsonField(
            views = @View(title = "申请类型", show = false),
            edit = @Edit(title = "申请类型", show = false)
    )
    private String applyType;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "申请日期", type = ViewType.DATE),
            edit = @Edit(title = "申请日期", show = false, search = @Search(vague = true),
                    dateType = @DateType(type = DateType.Type.DATE))
    )
    private Date applyDate;

    @FabosJsonField(
            views = @View(title = "备注"),
            edit = @Edit(title = "备注", type = EditType.TEXTAREA, placeHolder = "请输入")
    )
    private String remark;




    @FabosJsonField(
            views = @View(title = "附件"),
            edit = @Edit(title = "附件", type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(maxLimit = 10))
    )
    private String attachment;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "bwi_supplier_return_apply_id")
    @OrderBy
    @FabosJsonField(
            edit = @Edit(title = "供应商退货申请明细", type = EditType.TAB_REFER_ADD),
            views = @View(title = "供应商退货申请明细", type = ViewType.TABLE_VIEW, extraPK = "boxedDetailId",column = "materialCode"),
            referenceAddType = @ReferenceAddType(referenceClass = "InventoryAutoBoxedDetailMTO",
//                    filter = "stockOutId = '${stockOut.id}'",
                    editable = {"requestQuantity"},
                    referenceAddHandler = SupplierReturnApplyReferenceAddHandler.class)
    )
    private List<SupplierReturnApplyDetail> details;

}
