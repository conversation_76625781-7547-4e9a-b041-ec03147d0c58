package cec.jiutian.bc.outbound.port.dto;

import cec.jiutian.bc.generalModeler.enumeration.ProductEnum;
import cec.jiutian.bc.generalModeler.enumeration.StockTypeEnum;
import cec.jiutian.bc.generalModeler.enumeration.YesOrNoEnum;
import cec.jiutian.bc.outbound.enumeration.OmInventoryManagementTypeEnum;
import cec.jiutian.bc.outbound.port.enumeration.OmInventoryCurrentStateEnum;
import cec.jiutian.bc.outbound.port.enumeration.OmLockTypeEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DateType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date ：2024/11/12 14:12
 * @description：
 */
@Table(name = "bwi_inventory")
@FabosJson(
        name = "库存台账",
        orderBy = "InventoryMTO.createTime desc",
        power = @Power(edit = false, delete = false)
)
@Entity
@Getter
@Setter
public class InventoryMTO extends MetaModel {
    @FabosJsonField(
            views = @View(title = "库存批次"),
            edit = @Edit(title = "库存批次", notNull = true,
                    search = @Search(vague = true),
                    inputType = @InputType(length = 20))
    )
    private String inventoryLotId;
    @FabosJsonField(
            views = @View(title = "本厂批号"),
            edit = @Edit(title = "本厂批号", notNull = true,
                    search = @Search(vague = true),
                    inputType = @InputType(length = 20))
    )
    //本厂批号号可重复  如果需要关联到一条唯一记录请使用库存批次
    private String lotSerialId;
    @FabosJsonField(
            views = @View(title = "原炉/原厂批号"),
            edit = @Edit(title = "原炉/原厂批号", notNull = true,
                    search = @Search(vague = true),
                    inputType = @InputType(length = 20))
    )
    private String supplierLotSerialId;

    @FabosJsonField(
            views = @View(title = "供应商id", show = false),
            edit = @Edit(title = "供应商id", show = false)
    )
    private String supplierId;
    @FabosJsonField(
            views = @View(title = "供应商"),
            edit = @Edit(title = "供应商")
    )
    private String supplierName;
    @FabosJsonField(
            views = @View(title = "物资",show = false),
            edit = @Edit(title = "物资", search = @Search(vague = true))
    )
    private String materialId;
    @FabosJsonField(
            views = @View(title = "物料编号"),
            edit = @Edit(title = "物料编号", search = @Search(vague = true))

    )
    private String materialCode;
    @FabosJsonField(
            views = @View(title = "物料名称"),
            edit = @Edit(title = "物料名称", search = @Search(vague = true))

    )
    private String materialName;

    @FabosJsonField(
            views = @View(title = "规格"),
            edit = @Edit(title = "规格")
    )
    private String materialSpecification;
    @FabosJsonField(
            views = @View(title = "单位"),
            edit = @Edit(title = "单位")
    )
    private String accountingUnit;
    @FabosJsonField(
            views = @View(title = "品号"),
            edit = @Edit(title = "品号")
    )
    private String productNo;
    @FabosJsonField(
            views = @View(title = "类别"),
            edit = @Edit(title = "类别", type = EditType.CHOICE, search = @Search(vague = true), notNull = true,
                    choiceType = @ChoiceType(fetchHandler = StockTypeEnum.class))
    )
    private String stockType;
    @FabosJsonField(
            views = @View(title = "子类别"),
            edit = @Edit(title = "子类别", type = EditType.CHOICE, search = @Search(vague = true), notNull = true,
                    choiceType = @ChoiceType(fetchHandler = ProductEnum.class))
    )
    private String stockSubType;
    @FabosJsonField(
            views = @View(title = "合格证号"),
            edit = @Edit(title = "合格证号",
                    notNull = true,
                    inputType = @InputType(length = 20))
    )
    private String certificateNumber;
    @FabosJsonField(
            views = @View(title = "原厂合格证号"),
            edit = @Edit(title = "原厂合格证号",
                    notNull = true,
                    inputType = @InputType(length = 20))
    )
    private String supplierCertificateNumber;
    @FabosJsonField(
            views = @View(title = "生产单位"),
            edit = @Edit(title = "生产单位")
    )
    private String production;
    @FabosJsonField(
            views = @View(title = "技术要求"),
            edit = @Edit(title = "技术要求")
    )
    private String technicalRequirements;
    @FabosJsonField(
            views = @View(title = "单价"),
            edit = @Edit(title = "单价")
    )
    private Double price;
    @FabosJsonField(
            views = @View(title = "已复验次数"),
            edit = @Edit(title = "已复验次数")
    )
    private Integer retestsCount;
    @FabosJsonField(
            views = @View(title = "可复验次数"),
            edit = @Edit(title = "可复验次数")
    )
    private Integer allowRetestsCount;
    @FabosJsonField(
            views = @View(title = "保管员"),
            edit = @Edit(title = "保管员")
    )
    private String storekeeper;
    @FabosJsonField(
            views = @View(title = "保管员id", show = false),
            edit = @Edit(title = "保管员id", show = false)
    )
    private String storekeeperId;
    @FabosJsonField(
            views = @View(title = "本厂批数量"),
            edit = @Edit(title = "本厂批数量", notNull = true,
                    inputType = @InputType(length = 20), numberType = @NumberType(min = 0))
    )
    //本厂批数量 生成库存台账的时候一次性写入  后续的所有业务流转该数量都不会改变  比如拆批，拆批过后该字段数量也不能改变
    private Double lotAllQuantity;
    @FabosJsonField(
            views = @View(title = "台账总量"),
            edit = @Edit(title = "台账总量", notNull = true,
                    inputType = @InputType(length = 20), numberType = @NumberType(min = 0))
    )
    //
    private Double accountingUnitQuantity;
    @FabosJsonField(
            views = @View(title = "待出库量"),
            edit = @Edit(title = "待出库量",
                    inputType = @InputType(length = 20), numberType = @NumberType(min = 0))
    )
    //主要是 待出库状态 的锁定数量
    private Double lockQuantity;
    @FabosJsonField(
            views = @View(title = "待处理量"),
            edit = @Edit(title = "待处理量",
                    inputType = @InputType(length = 20), numberType = @NumberType(min = 0))
    )
    //主要是  库内待处理 的锁定数量
    private Double handleLockQuantity;
    @FabosJsonField(
            views = @View(title = "可用数量"),
            edit = @Edit(title = "可用数量", search = @Search(vague = true),
                    inputType = @InputType(length = 20), numberType = @NumberType(min = 0))
    )
    //accountingUnitQuantity - lockQuantity-handleLockQuantity
    private Double availableQuantity;
    @FabosJsonField(
            views = @View(title = "锁定类型"),
            edit = @Edit(title = "锁定类型", type = EditType.CHOICE, search = @Search, notNull = true,
                    choiceType = @ChoiceType(fetchHandler = OmLockTypeEnum.class))
    )
    private String lockType;
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "生产日期", type = ViewType.DATE),
            edit = @Edit(title = "生产日期", notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE))
    )
    private Date manufactureDate;
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "使用期限", type = ViewType.DATE),
            edit = @Edit(title = "使用期限", notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE))
    )
    private Date useDeadlineDate;
    @FabosJsonField(
            views = @View(title = "用途"),
            edit = @Edit(title = "用途")
    )
    //多个逗号分割
    private String purpose;
    @FabosJsonField(
            views = @View(title = "仓库名称"),
            edit = @Edit(title = "仓库名称",
                    inputType = @InputType(length = 20))
    )
    private String warehouseName;
    @FabosJsonField(
            views = @View(title = "仓库", show = false),
            edit = @Edit(title = "仓库", show = false)
    )
    private String warehouseId;
    @FabosJsonField(
            views = @View(title = "库区名称"),
            edit = @Edit(title = "库区名称",
                    inputType = @InputType(length = 20))
    )
    private String blockName;
    @FabosJsonField(
            views = @View(title = "库区", show = false),
            edit = @Edit(title = "库区", show = false)
    )
    private String blockId;
    @FabosJsonField(
            views = @View(title = "货位名称"),
            edit = @Edit(title = "货位名称",
                    inputType = @InputType(length = 20))
    )
    private String shelfName;
    @FabosJsonField(
            views = @View(title = "货位", show = false),
            edit = @Edit(title = "货位", show = false)
    )
    private String shelfId;
    @FabosJsonField(
            views = @View(title = "当前状态"),
            edit = @Edit(title = "当前状态", type = EditType.CHOICE, notNull = true, search = @Search,
                    choiceType = @ChoiceType(fetchHandler = OmInventoryCurrentStateEnum.class))
    )
    private String currentState;
    @FabosJsonField(
            views = @View(title = "存货管理类型"),
            edit = @Edit(title = "存货管理类型", type = EditType.CHOICE, notNull = true,
                    choiceType = @ChoiceType(fetchHandler = OmInventoryManagementTypeEnum.class))
    )
    private String inventoryManagementType;
    @FabosJsonField(
            views = @View(title = "生产退回标记"),
            edit = @Edit(title = "生产退回标记", type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = YesOrNoEnum.class))
    )
    private String productionReturnFlag;
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "入库日期", type = ViewType.DATE),
            edit = @Edit(title = "入库日期", notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE))
    )
    private Date stockInDate;
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "出库日期", type = ViewType.DATE),
            edit = @Edit(title = "出库日期",
                    dateType = @DateType(type = DateType.Type.DATE))
    )
    private Date stockOutDate;


}

