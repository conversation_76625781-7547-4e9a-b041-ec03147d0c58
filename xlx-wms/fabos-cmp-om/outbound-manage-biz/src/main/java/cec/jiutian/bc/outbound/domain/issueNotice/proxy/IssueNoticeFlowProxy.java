package cec.jiutian.bc.outbound.domain.issueNotice.proxy;

import cec.jiutian.bc.flow.enums.ExamineStatusEnum;
import cec.jiutian.bc.outbound.domain.issueNotice.model.IssueNotice;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.FlowProxy;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/1/3
 * @description TODO
 */
@Component
public class IssueNoticeFlowProxy extends FlowProxy {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public void beforeSubmit(Object entity) {
        if (entity instanceof IssueNotice issueNotice) {
            if (CollectionUtils.isEmpty(issueNotice.getDetailList())) {
                throw new FabosJsonApiErrorTip("未选择明细数据，不能发起审批");
            }
            issueNotice.setCurrentState(OrderCurrentStateEnum.Enum.EXECUTE.name());
            fabosJsonDao.mergeAndFlush(issueNotice);
        }
    }

    @Override
    public void onEvent(Object event, Object entity) {
        if (entity instanceof IssueNotice issueNotice) {
            if (issueNotice.getExamineStatus().equals(ExamineStatusEnum.AUDITED.getCode())) {
                issueNotice.setCurrentState(OrderCurrentStateEnum.Enum.COMPLETE.name());
            } else if (issueNotice.getExamineStatus().equals(ExamineStatusEnum.REJECTED.getCode())) {
                issueNotice.setCurrentState(OrderCurrentStateEnum.Enum.END.name());
            }
            fabosJsonDao.mergeAndFlush(issueNotice);
        }
    }
}
