package cec.jiutian.bc.outbound.domain.otherStockOut.service;

import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.outbound.domain.otherStockOut.model.OtherStockOut;
import cec.jiutian.bc.outbound.enumeration.OmNamingRuleCodeEnum;
import cec.jiutian.bc.outbound.port.client.InventoryClient;
import cec.jiutian.common.util.StringUtils;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.data.jpa.JpaCrud;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 */
@Service
@Transactional
public class OtherStockOutService {
    @Resource
    private NamingRuleService namingRuleService;
    @Resource
    private JpaCrud jpaCrud;
    @Resource
    private InventoryClient inventoryClient;

    /**
     * 提供给上游的出库单子   在这里保存出库单直接是完成状态  并且直接调整库存台账状态
     *
     * @param otherStockOut
     * @return
     */
    public List<String> insert(OtherStockOut otherStockOut) {
        if (otherStockOut == null) {
            throw new FabosJsonApiErrorTip("参数异常");
        }
        if (StringUtils.isBlank(otherStockOut.getSourceOrderNo())) {
            throw new FabosJsonApiErrorTip("来源单据号不能为空");
        }
        if (StringUtils.isBlank(otherStockOut.getOtherStockOutType())) {
            throw new FabosJsonApiErrorTip("出库类型不能为空");
        }
        if (CollectionUtils.isEmpty(otherStockOut.getOtherStockOutDetails())) {
            throw new FabosJsonApiErrorTip("无出库明细");
        }
        List<String> inventoryIds = new ArrayList<>();
        otherStockOut.setGeneralCode(namingRuleService.getNameCode(OmNamingRuleCodeEnum.OtherStockOutCode.name(), 1, null).get(0));
        otherStockOut.getOtherStockOutDetails().forEach(d -> {
            if (StringUtils.isBlank(d.getInventoryId())) {
                throw new FabosJsonApiErrorTip("出库批次id不能为空");
            }
            if (d.getStockOutQuantity() == null || d.getStockOutQuantity() <= 0) {
                throw new FabosJsonApiErrorTip("批次" + d.getInventoryId() + "出库数量异常");
            }
        });
        jpaCrud.insert(otherStockOut);
        //需要现有出库单。在处理批次信息。因为批次履历需要出库单的id  从业务上来说也应该是现有出库单，库内再执行批次操作。
        otherStockOut.getOtherStockOutDetails().forEach(d -> {
            Object o = inventoryClient.stockOutSplit(d.getInventoryId(), d.getStockOutQuantity(), otherStockOut.getOtherStockOutType(), otherStockOut.getGeneralCode());
            inventoryIds.add(o.toString());
        });
        return inventoryIds;
    }

}
