package cec.jiutian.bc.outbound.domain.consumeOrder.model;

import cec.jiutian.bc.generalModeler.enumeration.ProductEnum;
import cec.jiutian.bc.generalModeler.enumeration.StockTypeEnum;
import cec.jiutian.bc.outbound.domain.consumeOrder.handler.OrderQuantityDynamicHandler;
import cec.jiutian.bc.outbound.domain.consumeOrder.handler.OrderReferenceAddHandler;
import cec.jiutian.bc.outbound.util.OmMaterialByCodeTableFormHandler;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.ReferenceAddType;
import cec.jiutian.view.SubTableField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OrderBy;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @description:
 */
@FabosJson(
        name = "明细",
        orderBy = "ConsumeOrderDetail.createTime desc"
)
@Table(name = "bwi_consume_order_detail",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"orderDetailNumber"})
        }
)
@Entity
@Getter
@Setter
public class ConsumeOrderDetail extends MetaModel {
    @FabosJsonField(
            views = @View(title = "单据编号", show = false),
            edit = @Edit(title = "单据编号", show = false, search = @Search(vague = true),
                    readonly = @Readonly(add = false), inputType = @InputType(length = 40))
    )
    private String orderDetailNumber;

    @FabosJsonField(
            views = {
                    @View(title = "出库单号", column = "generalCode", show = false)
            },
            edit = @Edit(title = "出库单号", type = EditType.REFERENCE_TABLE, show = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode")
            )
    )
    @ManyToOne
    @JsonIgnoreProperties("consumeOrderDetails")
    private ConsumeOrder consumeOrder;
    @FabosJsonField(
            views = @View(title = "物资数据主键", show = false),
            edit = @Edit(title = "物资数据主键", show = false)
    )
    private String materialId;

    @FabosJsonField(
            views = @View(title = "物资名称", type = ViewType.TABLE_FORM, tableFormParamsHandler = OmMaterialByCodeTableFormHandler.class),
            edit = @Edit(title = "物资名称", readonly = @Readonly)

    )
    private String materialName;
    @FabosJsonField(
            views = @View(title = "物资编号"),
            edit = @Edit(title = "物资编号", readonly = @Readonly)
    )
    private String materialCode;
    @FabosJsonField(
            views = @View(title = "规格"),
            edit = @Edit(title = "规格", readonly = @Readonly)
    )
    private String materialSpecification;

    @FabosJsonField(
            views = @View(title = "牌号"),
            edit = @Edit(title = "牌号", readonly = @Readonly)
    )
    private String brandCode;

    @FabosJsonField(
            views = @View(title = "物资类别"),
            edit = @Edit(title = "物资类别", notNull = false,
                    type = EditType.CHOICE
                    , search = @Search(vague = true),
                    choiceType = @ChoiceType(fetchHandler = StockTypeEnum.class)
            )
    )
    @SubTableField
    private String stockType;
    @FabosJsonField(
            views = @View(title = "物资子类别"),
            edit = @Edit(title = "物资子类别", notNull = false, type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ProductEnum.class),
                    readonly = @Readonly(add = true, edit = true),
                    search = @Search(vague = true))
    )
    private String stockSubType;
    @FabosJsonField(
            views = @View(title = "单位"),
            edit = @Edit(title = "单位", readonly = @Readonly)
    )
    private String accountUnit;
    @FabosJsonField(
            views = @View(title = "申请数量", rowEdit = true, width = "100px"),
            edit = @Edit(title = "申请数量", readonly = @Readonly,
                    numberType = @NumberType(min = 0, max = 9999999, precision = 3))
    )
    private Double quantity;

    @FabosJsonField(
            views = @View(title = "实发数量"),
            edit = @Edit(title = "实发数量", readonly = @Readonly, notNull = true),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "consumeOrderDetailLots", dynamicHandler = OrderQuantityDynamicHandler.class))
    )
    private Double actualQuantity;

    @FabosJsonField(
            views = @View(title = "备注", rowEdit = true, width = "150px"),
            edit = @Edit(title = "备注")
    )
    private String remark;
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = false)
    @JoinColumn(name = "consume_order_detail_id")
    @OrderBy
    @FabosJsonField(
            edit = @Edit(title = "批次信息", type = EditType.TAB_REFER_ADD),
            referenceAddType = @ReferenceAddType(referenceClass = "InventoryMTO", queryCondition = "{\"currentState\":\"normal\", \"materialCode\": \"${materialCode}\", \"lockType\": \"NO_LOCK\", \"availableQuantity\": \"1,\"}", editable = {"quantity"},
                    referenceAddHandler = OrderReferenceAddHandler.class),
            views = @View(title = "批次信息", type = ViewType.TABLE_VIEW, extraPK = "inventoryId")
    )
    private List<ConsumeOrderDetailLot> consumeOrderDetailLots;
}
