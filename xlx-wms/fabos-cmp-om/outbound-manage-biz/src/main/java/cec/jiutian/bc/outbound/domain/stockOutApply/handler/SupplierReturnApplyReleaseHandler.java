package cec.jiutian.bc.outbound.domain.stockOutApply.handler;

import cec.jiutian.bc.library.enumeration.OrderCurrentStateEnum;
import cec.jiutian.bc.outbound.domain.stockOutApply.model.SupplierReturnApply;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Component
public class SupplierReturnApplyReleaseHandler implements OperationHandler<SupplierReturnApply, Void> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    @Transactional
    public String exec(List<SupplierReturnApply> returnApplyList, Void modelObject, String[] param) {
        if(CollectionUtils.isEmpty(returnApplyList)){
            return "msg.success('操作成功!')";
        }
        SupplierReturnApply returnApply = returnApplyList.get(0);
        checkReturnApply(returnApply);
        returnApply.setCurrentState(OrderCurrentStateEnum.Enum.EXECUTE.name());
        fabosJsonDao.update(returnApply);
        return "msg.success('操作成功!')";
    }
    /**
     * <p><p>
     * 1: 为开立状态
     * 2：详情不为空
     */
    public void checkReturnApply(SupplierReturnApply returnApply){
        if(!OrderCurrentStateEnum.Enum.EDIT.name().equals(returnApply.getCurrentState())) {
            throw new FabosJsonApiErrorTip("单据状态不为开立状态, 不能进行发布操作, 请检查!");
        }
        if(CollectionUtils.isEmpty(returnApply.getDetails())) {
            throw new FabosJsonApiErrorTip("退货申请明细不能为空!");
        }
    }

}
