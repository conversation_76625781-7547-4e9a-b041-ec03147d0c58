package cec.jiutian.bc.outbound.domain.issueNotice.model;

import cec.jiutian.bc.flow.domain.model.entity.ExamineModel;
import cec.jiutian.bc.outbound.domain.issueNotice.handler.IssueNoticeCodeGenerateHandler;
import cec.jiutian.bc.outbound.domain.issueNotice.handler.IssueNoticeDynamicHandler;
import cec.jiutian.bc.outbound.domain.issueNotice.handler.IssueNoticeReferenceAddHandler;
import cec.jiutian.bc.outbound.domain.issueNotice.proxy.IssueNoticeFlowProxy;
import cec.jiutian.bc.outbound.domain.issueNotice.proxy.IssueNoticeProxy;
import cec.jiutian.bc.outbound.enumeration.IssueTypeEnum;
import cec.jiutian.bc.outbound.port.dto.PurchasePlanMTO;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.ReferenceAddType;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowBaseOperation;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OrderBy;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/2
 * @description TODO
 */
@Table(name = "bwo_issue_notice")
@FabosJson(
        name = "发料通知",
        orderBy = "IssueNotice.createTime desc",
        dataProxy = IssueNoticeProxy.class,
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "currentState != 'EDIT'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "currentState != 'EDIT'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "examine",
                        ifExpr = "examineStatus != '0'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "examineDetails",
                        ifExpr = "currentState == 'EDIT'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                )
        },
        flowProxy = {IssueNoticeFlowProxy.class},
        flowCode = "IssueNotice",
        power = @Power(examine = true, examineDetails = true)
)
@Entity
@Getter
@Setter
@TemplateType(type = "multiTable")
public class IssueNotice extends ExamineModel {

    @FabosJsonField(
            views = @View(title = "通知单号"),
            edit = @Edit(title = "通知单号", notNull = true),
            dynamicField = @DynamicField(
                    dependFiled = @DependFiled(buttonName = "生成", changeBy = {"id"},
                            dynamicHandler = IssueNoticeCodeGenerateHandler.class))
    )
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "通知单名称"),
            edit = @Edit(title = "通知单名称", search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    private String noticeName;

    @FabosJsonField(
            views = @View(title = "采购计划号", column = "generalCode"),
            edit = @Edit(title = "采购计划",
                    readonly = @Readonly(add = false),
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    filter = @Filter(value = "PurchasePlanMTO.examineStatus = '1'"),
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode")
            )
    )
    @ManyToOne
    private PurchasePlanMTO purchasePlanMTO;

    @FabosJsonField(
            views = @View(title = "发料类型"),
            edit = @Edit(title = "发料类型", type = EditType.CHOICE, notNull = true,
                    search = @Search(vague = true),
                    choiceType = @ChoiceType(fetchHandler = IssueTypeEnum.class))
    )
    private String issueType;

    @FabosJsonField(
            views = @View(title = "当前状态"),
            edit = @Edit(title = "当前状态", type = EditType.CHOICE, show = false,
                    search = @Search(),
                    choiceType = @ChoiceType(
                            fetchHandler = OrderCurrentStateEnum.class
                    )
            )
    )
    private String currentState;

    @FabosJsonField(
            views = @View(title = "备注"),
            edit = @Edit(title = "备注",
                    type = EditType.TEXTAREA)
    )
    private String remark;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "issue_notice_id")
    @OrderBy
    @FabosJsonField(
            edit = @Edit(title = "发料通知明细", type = EditType.TAB_REFER_ADD),
            referenceAddType = @ReferenceAddType(referenceClass = "PurchasePlanDetailMTO",
                    filter = "purchasePlan.id = '${purchasePlanMTO.id}'",
                    editable = {"quantity", "securityLevel", "remark"},
                    referenceAddHandler = IssueNoticeReferenceAddHandler.class),
            views = @View(title = "发料通知明细", type = ViewType.TABLE_VIEW, extraPK = "extraModelId"),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "purchasePlanMTO", dynamicHandler = IssueNoticeDynamicHandler.class))
    )
    private List<IssueNoticeDetail> detailList;
}
