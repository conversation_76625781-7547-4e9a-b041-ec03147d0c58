package cec.jiutian.bc.outbound.domain.refundStockOut.proxy;

import cec.jiutian.bc.flow.enums.ExamineStatusEnum;
import cec.jiutian.bc.generalModeler.enumeration.StockTypeEnum;
import cec.jiutian.bc.outbound.domain.refundStockOut.model.RefundStockOut;
import cec.jiutian.bc.outbound.domain.refundStockOut.model.RefundStockOutDetail;
import cec.jiutian.bc.outbound.enumeration.OrderCurrentStateEnum;
import cec.jiutian.bc.outbound.port.client.InventoryClient;
import cec.jiutian.bc.outbound.service.OutBoundClient;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.data.jpa.JpaCrud;
import cec.jiutian.view.fun.FlowProxy;
import cn.hutool.core.util.StrUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class RefundStockOutFlowProxy extends FlowProxy {
    @Resource
    private JpaCrud jpaCrud;
    @Resource
    private InventoryClient inventoryClient;
    @Resource
    private OutBoundClient outBoundClient;
    @Resource
    private RefundStockOutProxy refundStockOutProxy;

    public void onEvent(Object event, Object date) {
        if (date instanceof RefundStockOut refundStockOut) {
            if (refundStockOut.getExamineStatus().equals(ExamineStatusEnum.AUDITED.getCode())) {
                refundStockOut.setCurrentState(OrderCurrentStateEnum.Enum.COMPLETE.name());
                //通过才处理批次数据
                handleInventoryByFlow(refundStockOut);
                if (CollectionUtils.isNotEmpty(refundStockOut.getRefundStockOutDetails())) {
                    refundStockOut.getRefundStockOutDetails().forEach(refundStockOutDetail -> {
                        //调用「返厂台账」创建接口，生成返厂台账。
                        if (StrUtil.equals(StockTypeEnum.Enum.MATERIAL.name(), refundStockOutDetail.getStockType())) {
                            outBoundClient.createReturnInventory(refundStockOutDetail);
                        }
                    });

                }
            } else if (refundStockOut.getExamineStatus().equals(ExamineStatusEnum.AUDITING.getCode())) {
                refundStockOut.setCurrentState(OrderCurrentStateEnum.Enum.EXECUTE.name());
            } else if (refundStockOut.getExamineStatus().equals(ExamineStatusEnum.REJECTED.getCode())) {
                refundStockOut.setCurrentState(OrderCurrentStateEnum.Enum.END.name());
                refundStockOutProxy.unLock(refundStockOut);
            }
            jpaCrud.update(refundStockOut);
        }
        log.info("退货出库审批回调成功");

    }

    /**
     * 审核通过调用
     *
     * @param refundStockOut
     */
    private void handleInventoryByFlow(RefundStockOut refundStockOut) {
        List<RefundStockOutDetail> detailList = new ArrayList<>();
        //通过才处理批次数据
        if (CollectionUtils.isNotEmpty(refundStockOut.getRefundStockOutDetails())) {
            refundStockOut.getRefundStockOutDetails().forEach(d -> {
                if (!Boolean.parseBoolean(inventoryClient.lock(d.getInventoryId(), refundStockOut.getGeneralCode(), "RefundStockOut").toString())) {
                    //没锁住 需要直接处理
                    detailList.add(d);
                }
            });
        }
        if (CollectionUtils.isNotEmpty(detailList)) {
            handleInventory(refundStockOut, detailList);
        }
    }

    /**
     * 审核通过处理台账
     *
     * @param refundStockOut
     * @param detailList
     */
    private void handleInventory(RefundStockOut refundStockOut, List<RefundStockOutDetail> detailList) {
        if (CollectionUtils.isNotEmpty(detailList)) {
            detailList.forEach(refundStockOutDetail -> {
                inventoryClient.refundStockOutSplit(refundStockOutDetail.getInventoryId(), refundStockOutDetail.getRefundStockOutQuantity(), refundStockOut.getGeneralCode());
            });
        }
    }

    /**
     * 批次解锁调用
     *
     * @param generalCode
     * @param inventoryId
     */
    public void inventoryUnLock(String generalCode, String inventoryId) {
        List<RefundStockOutDetail> detailList = new ArrayList<>();
        RefundStockOut refundStockOut = new RefundStockOut();
        refundStockOut.setGeneralCode(generalCode);
        RefundStockOut selectOne = jpaCrud.selectOne(refundStockOut);
        if (selectOne != null) {
            List<RefundStockOutDetail> details = selectOne.getRefundStockOutDetails();
            if (CollectionUtils.isNotEmpty(details)) {
                details.forEach(d -> {
                    if (d.getInventoryId().equals(inventoryId)) {
                        detailList.add(d);
                    }
                });
            }
        }
        if (CollectionUtils.isNotEmpty(detailList)) {
            handleInventory(selectOne, detailList);
        }
    }

    public void beforeSubmit(Object entity) {
        if (entity instanceof RefundStockOut refundStockOut) {
            List<RefundStockOutDetail> detailList = refundStockOut.getRefundStockOutDetails();
            if (CollectionUtils.isEmpty(detailList)) {
                throw new ServiceException("缺失明细信息，无法提交审核");
            }
            refundStockOut.setCurrentState(OrderCurrentStateEnum.Enum.EXECUTE.name());
        }
    }
}
