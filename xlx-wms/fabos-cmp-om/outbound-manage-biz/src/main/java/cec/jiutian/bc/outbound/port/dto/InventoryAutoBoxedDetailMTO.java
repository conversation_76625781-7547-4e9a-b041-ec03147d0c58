package cec.jiutian.bc.outbound.port.dto;

import cec.jiutian.bc.generalModeler.domain.measureUnit.model.MeasureUnit;
import cec.jiutian.bc.generalModeler.enumeration.StockTypeEnum;
import cec.jiutian.bc.generalModeler.enumeration.WarehouseTypeEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.QueryModel;
import cec.jiutian.view.*;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Power;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.Formula;

import java.time.LocalDateTime;
import java.util.Date;

@FabosJson(name = "库存台账自动装箱明细MTO")
@Data
@QueryModel(hql = "select " +
        "new map (t.id as id,t.id as boxedDetailId," +
        " t.materialId as materialId," +
        " w.type as warehouseType," +
        " d.warehouseName as warehouseName," +
        " d.warehouseId as warehouseId," +
        " d.blockName as blockName," +
        " d.blockId as blockId," +
        " d.shelfName as shelfName," +
        " d.shelfId as shelfId," +
        " d.supplierName as supplierName," +
        " d.manufactureDate as manufactureDate," +
        " t.materialCode as materialCode," +
        " t.materialName as materialName," +
        " t.materialSpecification as materialSpecification," +
        " t.inventoryLotId as inventoryLotId," +
        " t.lotSerialId as lotSerialId," +
        " d.stockType as stockType," +
        " t.boxedCode as boxedCode," +
        " t.boxNo as boxNo," +
        " t.quantity as quantity," +
        " d.accountingUnit as accountingUnit,"+
        " d.id as inventoryId)"+
        " from InventoryAutoBoxedDetail t join t.inventory d left join Warehouse w on d.warehouseId = w.id" +
        " order by t.createTime desc"
)
public class InventoryAutoBoxedDetailMTO {

    @Id
    @FabosJsonField(
            views = @View(title = "主键id", show = false),
            edit = @Edit(title = "主键id", show = false),
            customHqlField = "t.id"
    )
    private String id;

    //库存明细子表id = id  但extraPK用id会报错
    @FabosJsonField(
            views = @View(title = "库存明细子表id", show = false),
            edit = @Edit(title = "库存明细子表id", show = false)
    )
    private String boxedDetailId;

    @FabosJsonField(
            views = @View(title = "物料主键id", show = false),
            edit = @Edit(title = "物料主键id", show = false)
    )
    private String materialId;
    @FabosJsonField(
            views = @View(title = "仓库类型"),
            edit = @Edit(title = "仓库类型", notNull = true, type = EditType.CHOICE,
                    search = @Search(notNull = true),
                    choiceType = @ChoiceType(fetchHandler = WarehouseTypeEnum.class))
    )
    private String warehouseType;
    @FabosJsonField(
            views = @View(title = "仓库名称"),
            edit = @Edit(title = "仓库名称",
                    inputType = @InputType(length = 20))
    )
    private String warehouseName;
    @FabosJsonField(
            views = @View(title = "仓库Id", show = false),
            edit = @Edit(title = "仓库Id", show = false)
    )
    private String warehouseId;
    @FabosJsonField(
            views = @View(title = "库区名称", show = false),
            edit = @Edit(title = "库区名称", show = false,
                    inputType = @InputType(length = 20))
    )
    private String blockName;
    @FabosJsonField(
            views = @View(title = "库区", show = false),
            edit = @Edit(title = "库区", show = false)
    )
    private String blockId;
    @FabosJsonField(
            views = @View(title = "货位名称", show = false),
            edit = @Edit(title = "货位名称", show = false,
                    inputType = @InputType(length = 20))
    )
    private String shelfName;
    @FabosJsonField(
            views = @View(title = "货位", show = false),
            edit = @Edit(title = "货位", show = false)
    )
    private String shelfId;
    @FabosJsonField(
            views = @View(title = "供应商",show = false),
            edit = @Edit(title = "供应商",show = false)
    )
    private String supplierName;
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "生产日期", type = ViewType.DATE, show = false),
            edit = @Edit(title = "生产日期", show = false,
                    dateType = @DateType(type = DateType.Type.DATE))
    )
    private Date manufactureDate;
    @FabosJsonField(
            views = @View(title = "物料编码", show = true),
            edit = @Edit(title = "物料编码", show = false)
    )
    private String materialCode;

    @FabosJsonField(
            views = @View(title = "物料名称", show = true),
            edit = @Edit(title = "物料名称", readonly = @Readonly(add = false, edit = false), show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "arrivalRegistrationMaterialLotDTO", beFilledBy = "materialName"))
    )
    private String materialName;

    @FabosJsonField(
            views = @View(title = "型号规格", show = true),
            edit = @Edit(title = "型号规格", show = false)
    )
    private String materialSpecification;

    @FabosJsonField(
            views = @View(title = "来料批号"),
            edit = @Edit(title = "来料批号", notNull = true,
                    search = @Search(vague = true),
                    inputType = @InputType(length = 20))
    )
    private String inventoryLotId;

    @FabosJsonField(
            views = @View(title = "本厂批号"),
            edit = @Edit(title = "本厂批号", notNull = true,
                    search = @Search(vague = true),
                    inputType = @InputType(length = 20))
    )
    private String lotSerialId;

    @FabosJsonField(
            views = @View(title = "型号"),
            edit = @Edit(title = "型号", notNull = false,
                    type = EditType.CHOICE
                    , search = @Search(vague = true),
                    choiceType = @ChoiceType(fetchHandler = StockTypeEnum.class)
            )
    )
    private String stockType;
    /*
        箱次编码= 批次号+箱号
     */
    @FabosJsonField(
            views = @View(title = "箱次编码", show = true),
            edit = @Edit(title = "箱次编码", show = false)
    )
    private String boxedCode;

    @FabosJsonField(
            views = @View(title = "箱号", show = true),
            edit = @Edit(title = "箱号", show = false)
    )
    private String boxNo;

    @FabosJsonField(
            views = @View(title = "数量", rowEdit = true, width = "100px"),
            edit = @Edit(title = "数量", notNull = true, tips = "物料数量",
                    numberType = @NumberType(min = 0, max = 9999999, precision = 2))
    )
    private Double quantity;

    /*@ManyToOne
    @FabosJsonField(
            views = @View(title = "单位", column = "unitChnName"),
            edit = @Edit(title = "单位",
                    type = EditType.REFERENCE_TABLE,
                    show = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "unitChnName",
                            type = ReferenceTableType.SelectShowTypeMTO.LIST, groupField = "unitType")
            )
    )
    private MeasureUnit accountUnit;*/
    @FabosJsonField(
            views = @View(title = "单位"),
            edit = @Edit(title = "单位")
    )
    private String accountingUnit;
    @FabosJsonField(
            views = @View(title = "库存台账id", show = false),
            edit = @Edit(title = "库存台账id", show = false)
    )
    private String inventoryId;
}

