package cec.jiutian.bc.outbound.domain.stockOut.model;

import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleModel;
import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleNoExamineModel;
import cec.jiutian.bc.outbound.domain.stockOut.handler.ReturnStockOutCompleteHandler;
import cec.jiutian.bc.outbound.domain.stockOut.handler.ReturnStockOutDetailHandler;
import cec.jiutian.bc.outbound.domain.stockOut.proxy.ReturnStockOutProxy;
import cec.jiutian.bc.outbound.domain.stockOut.handler.ReturnStockOutReleaseHandler;
import cec.jiutian.bc.outbound.domain.stockOutApply.model.SupplierReturnApply;
import cec.jiutian.bc.outbound.enumeration.OmNamingRuleCodeEnum;
import cec.jiutian.bc.outbound.enumeration.StockOutCategoryEnum;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.ReferenceGenerateType;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowBaseOperation;
import cec.jiutian.view.type.RowOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@FabosJson(
        name = "退货出库",
        orderBy = "ReturnStockOut.createTime desc",
        dataProxy = ReturnStockOutProxy.class,
        filter = @Filter(value = "ReturnStockOut.type = 'Return'"),
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "currentState!='EDIT'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "currentState!='EDIT'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                )
        },
        rowOperation = {
                @RowOperation(
                        title = "发布",
                        code = "ReturnStockOut@RELEASE",
                        mode = RowOperation.Mode.SINGLE,
                        operationHandler = ReturnStockOutReleaseHandler.class,
                        ifExpr = "currentState!='EDIT'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
                        callHint = "是否确定操作？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "ReturnStockOut@RELEASE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
                @RowOperation(
                        title = "完成",
                        code = "ReturnStockOut@COMPLETE",
                        mode = RowOperation.Mode.SINGLE,
                        operationHandler = ReturnStockOutCompleteHandler.class,
                        ifExpr = "currentState!='EXECUTE'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
                        callHint = "是否确定操作？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "ReturnStockOut@COMPLETE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                )
        },
        power = @Power(export=false,print=false)
)
@Table(name = "stock_out")
@Entity
@Getter
@Setter
@TemplateType(type = "multiTable")
public class ReturnStockOut extends NamingRuleNoExamineModel {

    @Override
    public String getNamingCode() {
        return OmNamingRuleCodeEnum.StockOutNumber.name();
    }

    @FabosJsonField(
            views = @View(title = "类型"),
            edit = @Edit(title = "类型", readonly = @Readonly,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = StockOutCategoryEnum.class), defaultVal = "Return")
    )
    private String type;

    @FabosJsonField(
            views = @View(title = "申请单号", column = "generalCode"),
            edit = @Edit(title = "申请单号",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    readonly = @Readonly(add = false),
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode"),
                    filter = @Filter(value = "SupplierReturnApply.processState = 'waiting_outbound' and SupplierReturnApply.currentState = 'EXECUTE'"),
                    allowAddMultipleRows = false
            )
    )
    @ManyToOne
    @JoinColumn(name = "supplier_return_apply_id")
    private SupplierReturnApply supplierReturnApply;


    @FabosJsonField(
            views = @View(title = "当前状态"),
            edit = @Edit(title = "当前状态", show = false, type = EditType.CHOICE,
                    search = @Search,
                    choiceType = @ChoiceType(fetchHandler = OrderCurrentStateEnum.class))
    )
    private String currentState;

    @FabosJsonField(
            views = @View(title = "备注"),
            edit = @Edit(title = "备注",
                    inputType = @InputType(length = 200))
    )
    private String remark;


    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.EAGER)
    @JoinColumn(name = "stock_out_id")
    @FabosJsonField(
            views = @View(title = "退货出库明细", type = ViewType.TABLE_VIEW),
            edit = @Edit(title = "退货出库明细", type = EditType.TAB_REFERENCE_GENERATE),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "supplierReturnApply",
                    dynamicHandler = ReturnStockOutDetailHandler.class)),
            referenceGenerateType = @ReferenceGenerateType(editable = {"remark"})
    )
    private List<ReturnStockOutDetail> returnStockOutDetailList;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "申请日期", type = ViewType.DATE),
            edit = @Edit(title = "申请日期", show = false, search = @Search(vague = true),
                    dateType = @DateType(type = DateType.Type.DATE))
    )
    private Date applyDate;

    @FabosJsonField(
            views = @View(title = "来源单号", show = false),
            edit = @Edit(title = "来源单号", show = false)
    )
    private String sourceNumber;
}
