package cec.jiutian.bc.outbound.domain.consumeRequest.handler;

import cec.jiutian.bc.generalModeler.domain.material.model.StockView;
import cec.jiutian.bc.generalModeler.domain.measureUnit.model.MeasureUnit;
import cec.jiutian.bc.outbound.domain.consumeRequest.model.ConsumeRequest;
import cec.jiutian.bc.outbound.domain.consumeRequest.model.ConsumeRequestDetail;
import cec.jiutian.bc.outbound.domain.materialControlPlan.model.MaterialControlPlan;
import cec.jiutian.bc.outbound.domain.materialControlPlan.model.MaterialControlPlanDetail;
import cec.jiutian.bc.outbound.enumeration.OmConsumeType;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.data.jpa.JpaCrud;
import cec.jiutian.view.ReferenceAddType;
import jakarta.annotation.Resource;
import jakarta.persistence.EntityManager;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date ：2024/11/18 17:35
 * @description：
 */
@Component
public class RequestReferenceAddHandler implements ReferenceAddType.ReferenceAddHandler<ConsumeRequest, StockView> {

    @Resource
    private JpaCrud jpaCrud;
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public Map<String, Object> handle(ConsumeRequest consumeRequest, List<StockView> stockViewList) {
        Map<String, Object> result = new HashMap<>();
        if (CollectionUtils.isEmpty(stockViewList)) {
            return result;
        }
        List<ConsumeRequestDetail> consumeRequestDetails = stockViewList.stream()
                .map(stockView -> createConsumeRequestDetail(consumeRequest,
                        stockView,
                        OmConsumeType.Enum.ProductionConsume.name().equals(consumeRequest.getConsumeType()) ?
                                getMaterialControlPlanDetails(consumeRequest) : null))
                .collect(Collectors.toList());
        result.put("consumeRequestDetails", consumeRequestDetails);
        return result;
    }

    private List<MaterialControlPlanDetail> getMaterialControlPlanDetails(ConsumeRequest consumeRequest) {
        MaterialControlPlan materialControlPlan = consumeRequest.getMaterialControlPlan();
        EntityManager entityManager = fabosJsonDao.getEntityManager();
        entityManager.clear();
        MaterialControlPlan materialControlPlanDb = entityManager.find(MaterialControlPlan.class, materialControlPlan.getId());
        return Optional.ofNullable(materialControlPlanDb.getMaterialControlPlanDetails())
                .orElse(Collections.emptyList());
    }

    private ConsumeRequestDetail createConsumeRequestDetail(ConsumeRequest consumeRequest, StockView stockView,
                                                            List<MaterialControlPlanDetail> materialControlPlanDetails) {
        ConsumeRequestDetail detail = new ConsumeRequestDetail();
        detail.setMaterialId(stockView.getId());
        detail.setMaterialName(stockView.getName());
        detail.setMaterialCode(stockView.getCode());
        detail.setBrandCode(stockView.getBrandCode());
        detail.setMaterialSpecification(stockView.getSpecification());
        detail.setStockSubType(stockView.getStockSubType());
        detail.setStockType(stockView.getStockType());

        StockView stockViewEntity = jpaCrud.getById(StockView.class, stockView.getId());
        detail.setAccountUnit(Optional.ofNullable(stockViewEntity.getAccountUnit())
                .map(MeasureUnit::getUnitChnName)
                .orElse(""));

        if (OmConsumeType.Enum.ProductionConsume.name().equals(consumeRequest.getConsumeType())) {
            materialControlPlanDetails.stream()
                    .filter(planDetail -> planDetail.getMaterialId().equals(stockView.getId()))
                    .findFirst()
                    .ifPresent(planDetail -> {
                        detail.setMaterialControlQuantity(planDetail.getRequiredQuantity());
                        detail.setMaxConsumeQuantity(planDetail.getRequiredQuantity() - planDetail.getConsumeQuantity());
                    });
        }
        return detail;
    }
}
