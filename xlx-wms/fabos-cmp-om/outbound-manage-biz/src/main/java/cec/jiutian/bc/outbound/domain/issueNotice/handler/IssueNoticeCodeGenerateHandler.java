package cec.jiutian.bc.outbound.domain.issueNotice.handler;

import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.outbound.domain.issueNotice.model.IssueNotice;
import cec.jiutian.bc.outbound.enumeration.OmNamingRuleCodeEnum;
import cec.jiutian.view.DependFiled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/1/2
 * @description TODO
 */
@Component
public class IssueNoticeCodeGenerateHandler implements DependFiled.DynamicHandler<IssueNotice> {

    @Resource
    private NamingRuleService namingRuleService;

    @Override
    public Map<String, Object> handle(IssueNotice issueNotice) {
        Map<String, Object> map = new HashMap<>();
        map.put("generalCode", String.valueOf(namingRuleService.getNameCode(OmNamingRuleCodeEnum.IssueNotice.name(), 1, null).get(0)));
        return map;
    }
}
