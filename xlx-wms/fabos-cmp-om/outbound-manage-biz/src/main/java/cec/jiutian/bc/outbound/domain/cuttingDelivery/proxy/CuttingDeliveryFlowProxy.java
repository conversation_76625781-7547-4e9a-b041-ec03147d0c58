package cec.jiutian.bc.outbound.domain.cuttingDelivery.proxy;

import cec.jiutian.bc.flow.enums.ExamineStatusEnum;
import cec.jiutian.bc.outbound.domain.cuttingDelivery.model.CuttingDelivery;
import cec.jiutian.bc.outbound.domain.cuttingDelivery.model.CuttingDeliveryDetail;
import cec.jiutian.bc.outbound.enumeration.OrderCurrentStateEnum;
import cec.jiutian.bc.outbound.port.client.InventoryClient;
import cec.jiutian.bc.outbound.service.OutBoundClient;
import cec.jiutian.bc.outbound.service.OutBoundService;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.common.util.StringUtils;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.data.jpa.JpaCrud;
import cec.jiutian.view.fun.FlowProxy;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
@Transactional
public class CuttingDeliveryFlowProxy extends FlowProxy {
    @Resource
    private JpaCrud jpaCrud;
    @Resource
    private OutBoundService outBoundService;
    @Resource
    private OutBoundClient outBoundClient;
    @Resource
    private FabosJsonDao fabosJsonDao;
    @Resource
    private InventoryClient inventoryClient;

    @Override
    public void onEvent(Object event, Object entity) {
        if (entity instanceof CuttingDelivery cuttingDelivery) {
            String examineStatus = cuttingDelivery.getExamineStatus();
            // 如果是审批通过, 则设置该单状态为"执行中", 并且根据尾料重量判断是否需要创建尾料入库单
            if (ExamineStatusEnum.AUDITED.getCode().equals(examineStatus)) {
                cuttingDelivery.setCurrentState(OrderCurrentStateEnum.Enum.EXECUTE.name());
                // 调用创建尾料入库单服务进行创建尾料入库单
                outBoundService.createTailMaterialStockInByCuttingDelivery(cuttingDelivery);
                // 处理下料配送单中的退料数量: 进行库存台账恢复、创建其他入库单
                this.dealBackQuantity(cuttingDelivery);
            }
            // 如果是审批中, 则设置该单状态为"执行中"
            if (ExamineStatusEnum.AUDITING.getCode().equals(examineStatus)) {
                cuttingDelivery.setCurrentState(OrderCurrentStateEnum.Enum.EXECUTE.name());
            }
            // 如果审批驳回, 则设置该单状态为"中止"
            if (cuttingDelivery.getExamineStatus().equals(ExamineStatusEnum.REJECTED.getCode())) {
                cuttingDelivery.setCurrentState(OrderCurrentStateEnum.Enum.END.name());
            }
            jpaCrud.update(cuttingDelivery);
        }
        log.info("下料配送审批回调成功");
    }

    /**
     * 处理下料配送单中的退料数量
     *
     * @param cuttingDelivery 下料配送单
     *                        1.过滤出退回重量大于0且不为空的所有子单
     *                        2.遍历子单列表, 进行库存台账的数量还原操作
     *                        3.调用OutBoundClient服务, 调用rpc创建其他入库单
     * <AUTHOR>
     * @date 2024/12/19 9:54
     */
    private void dealBackQuantity(CuttingDelivery cuttingDelivery) {
        // 1.过滤出退回重量大于0的所有子单
        List<CuttingDeliveryDetail> filterDetailList
                = cuttingDelivery.getCuttingDeliveryDetails().stream()
                .filter(detail -> detail.getBackQuantity() != null && detail.getBackQuantity() > 0)
                .collect(Collectors.toList());
        // 2.调用OutBoundClient服务, 调用rpc创建其他入库单
        Object otherStockInNumberObj = outBoundClient.createOtherStockInByCuttingDelivery("DeliverBack", filterDetailList);
        // 3.遍历子单列表, 进行库存台账的数量还原操作
        filterDetailList.forEach(d -> {
            if (StringUtils.isNotBlank(d.getInventoryId())) {
                // 解锁数量: 根据库存台账id查询出对应的台账信息, 把退还数量加到“台账总量”和“可用数量”上去
                inventoryClient.stockInForDeliverBack(d.getInventoryId(), d.getBackQuantity(), otherStockInNumberObj.toString());
            }
        });

    }

    @Override
    public void beforeSubmit(Object entity) {
        if (entity instanceof CuttingDelivery cuttingDelivery) {
            List<CuttingDeliveryDetail> detailList = cuttingDelivery.getCuttingDeliveryDetails();
            if (CollectionUtils.isEmpty(detailList)) {
                throw new ServiceException("下料配送缺失明细信息，无法提交审核");
            }
        }
    }

    @Override
    public void afterSubmit(Object entity) {
        if (entity instanceof CuttingDelivery cuttingDelivery) {
            cuttingDelivery.setCurrentState(OrderCurrentStateEnum.Enum.EXECUTE.name());
            jpaCrud.update(cuttingDelivery);
        }
    }
}
