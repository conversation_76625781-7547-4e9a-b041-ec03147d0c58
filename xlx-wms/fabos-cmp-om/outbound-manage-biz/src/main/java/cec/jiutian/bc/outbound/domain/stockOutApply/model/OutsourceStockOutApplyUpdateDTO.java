package cec.jiutian.bc.outbound.domain.stockOutApply.model;

import cec.jiutian.bc.outbound.domain.stockOutApply.handler.OutsourceStockOutApplyUpdateDTOReferenceAddHandler;
import cec.jiutian.bc.outbound.enumeration.LotGenTypeEnum;
import cec.jiutian.bc.outbound.enumeration.OmNamingRuleCodeEnum;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.ReferenceAddType;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.AttachmentType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DateType;
import cec.jiutian.view.field.edit.Search;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;
/*
外包出库申请-编辑
 */

@FabosJson(
        name = "外包出库申请",
        orderBy = "createTime desc"
)
@Table(name = "bwi_outsource_stockout_apply")
@Entity
@Getter
@Setter
@TemplateType(type = "multiTable")
public class OutsourceStockOutApplyUpdateDTO extends ApplyStateModel {


    @Override
    public String getNamingCode() {
        return OmNamingRuleCodeEnum.OutsourceStockOutApplyNumber.name();
    }

    @FabosJsonField(
            views = @View(title = "申请类型", show = false),
            edit = @Edit(title = "申请类型", show = false)
    )
    private String applyType;
    @FabosJsonField(
            views = @View(title = "发货批号生成方式"),
            edit = @Edit(title = "发货批号生成方式", readonly = @Readonly(),type = EditType.CHOICE, search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = LotGenTypeEnum.class))
    )
    private String lotGenType;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "申请日期", type = ViewType.DATE),
            edit = @Edit(title = "申请日期", show = false, search = @Search(vague = true),
                    dateType = @DateType(type = DateType.Type.DATE))
    )
    private Date applyDate;

    @FabosJsonField(
            views = @View(title = "备注"),
            edit = @Edit(title = "备注", type = EditType.TEXTAREA, placeHolder = "请输入")
    )
    private String remark;


    @FabosJsonField(
            views = @View(title = "附件"),
            edit = @Edit(title = "附件", type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(maxLimit = 10))
    )
    private String attachment;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "bwi_outsource_stockout_id")
    @OrderBy
    @FabosJsonField(
            edit = @Edit(title = "内包成品批次", type = EditType.TAB_REFER_ADD),
            views = @View(title = "内包成品批次", type = ViewType.TABLE_VIEW, extraPK = "inventoryId", column = "lotSerialId"),
            referenceAddType = @ReferenceAddType(referenceClass = "InventoryMTO",
//                    filter = "stockOutId = '${stockOut.id}'",
                    editable = {"requestQuantity"},
                    referenceAddHandler = OutsourceStockOutApplyUpdateDTOReferenceAddHandler.class)
    )
    private List<OutsourceStockOutApplyDetail> details;
}
