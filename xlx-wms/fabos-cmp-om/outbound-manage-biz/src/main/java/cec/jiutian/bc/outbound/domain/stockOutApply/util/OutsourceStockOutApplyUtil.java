package cec.jiutian.bc.outbound.domain.stockOutApply.util;


import cec.jiutian.bc.outbound.domain.stockOutApply.model.OutsourceStockOutApply;
import cec.jiutian.bc.outbound.domain.stockOutApply.model.OutsourceStockOutApplyDetail;
import cec.jiutian.bc.outbound.domain.stockOutApply.model.OutsourceStockOutApplyMaterialAutoDetail;
import cec.jiutian.bc.outbound.domain.stockOutApply.model.OutsourceStockOutApplyUpdateDTO;
import cec.jiutian.common.util.BeanUtils;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

public class OutsourceStockOutApplyUtil {
    /*
1:将相同物料的内包批次合并
 */
    public static void handleMaterialDetailAdd(OutsourceStockOutApply request) {
        List<OutsourceStockOutApplyDetail> details = request.getDetails();
        if (CollectionUtils.isEmpty(details)) {
            return;
        }
        AtomicInteger index = new AtomicInteger(1);
        List<OutsourceStockOutApplyMaterialAutoDetail> materialDetails = new ArrayList<>();
        Map<String, List<OutsourceStockOutApplyDetail>> materialCodeMap = details.stream().collect(Collectors.groupingBy(OutsourceStockOutApplyDetail::getMaterialCode));
        materialCodeMap.forEach((materialCode, tmp) -> {
            OutsourceStockOutApplyMaterialAutoDetail outsourceStockOutApplyMaterialAutoDetail = new OutsourceStockOutApplyMaterialAutoDetail();
            // 对tmp中需求数量求和
            double sumRequestQuantity = tmp.stream().mapToDouble(OutsourceStockOutApplyDetail::getRequestQuantity).sum();
            // 对tmp中的lotSerialId合并成以逗号隔开的字符串
            String serialLotId = tmp.stream().map(OutsourceStockOutApplyDetail::getSerialLotId).collect(Collectors.joining(","));
            String inventoryId = tmp.stream().map(OutsourceStockOutApplyDetail::getInventoryId).collect(Collectors.joining(","));
            BeanUtils.copyNotNullProperties(tmp.get(0), outsourceStockOutApplyMaterialAutoDetail);
            outsourceStockOutApplyMaterialAutoDetail.setId(null);
            outsourceStockOutApplyMaterialAutoDetail.setRequestQuantity(sumRequestQuantity);
            outsourceStockOutApplyMaterialAutoDetail.setSerialLotId(serialLotId);
            outsourceStockOutApplyMaterialAutoDetail.setInventoryId(inventoryId);
            outsourceStockOutApplyMaterialAutoDetail.setOutsourceStockOutApplyMaterialDetailNumber(request.getGeneralCode() + "-" + String.format("%03d", index.get()));
            outsourceStockOutApplyMaterialAutoDetail.setSequenceNumber(index.get());
            index.getAndIncrement();
            materialDetails.add(outsourceStockOutApplyMaterialAutoDetail);
        });
        request.setMaterialDetails(materialDetails);
    }

    public static void handleMaterialDetailDForUpdate(OutsourceStockOutApply request, OutsourceStockOutApplyUpdateDTO lotDTO) {
        List<OutsourceStockOutApplyDetail> details = lotDTO.getDetails();
        if (CollectionUtils.isEmpty(details)) {
            return;
        }
        AtomicInteger index = new AtomicInteger(1);
        List<OutsourceStockOutApplyMaterialAutoDetail> materialDetails = new ArrayList<>();
        Map<String, List<OutsourceStockOutApplyDetail>> materialCodeMap = details.stream().collect(Collectors.groupingBy(OutsourceStockOutApplyDetail::getMaterialCode));
        materialCodeMap.forEach((materialCode, tmp) -> {
            OutsourceStockOutApplyMaterialAutoDetail outsourceStockOutApplyMaterialAutoDetail = new OutsourceStockOutApplyMaterialAutoDetail();
            // 对tmp中需求数量求和
            double sumRequestQuantity = tmp.stream().mapToDouble(OutsourceStockOutApplyDetail::getRequestQuantity).sum();
            // 对tmp中的lotSerialId合并成以逗号隔开的字符串
            String serialLotId = tmp.stream().map(OutsourceStockOutApplyDetail::getSerialLotId).collect(Collectors.joining(","));
            String inventoryId = tmp.stream().map(OutsourceStockOutApplyDetail::getInventoryId).collect(Collectors.joining(","));
            BeanUtils.copyNotNullProperties(tmp.get(0), outsourceStockOutApplyMaterialAutoDetail);
            outsourceStockOutApplyMaterialAutoDetail.setId(null);
            outsourceStockOutApplyMaterialAutoDetail.setRequestQuantity(sumRequestQuantity);
            outsourceStockOutApplyMaterialAutoDetail.setSerialLotId(serialLotId);
            outsourceStockOutApplyMaterialAutoDetail.setInventoryId(inventoryId);
            outsourceStockOutApplyMaterialAutoDetail.setOutsourceStockOutApplyMaterialDetailNumber(request.getGeneralCode() + "-" + String.format("%03d", index.get()));
            outsourceStockOutApplyMaterialAutoDetail.setSequenceNumber(index.get());
            index.getAndIncrement();
            materialDetails.add(outsourceStockOutApplyMaterialAutoDetail);
        });
        request.getMaterialDetails().clear();
        request.getMaterialDetails().addAll(materialDetails);
        request.getDetails().clear();
        request.getDetails().addAll(details);
//        OutsourceStockOutApplyDetail applyDetail = new OutsourceStockOutApplyDetail();
//        applyDetail.setSequenceNumber(1);
//        request.getDetails().addAll( Arrays.asList(applyDetail));
    }


    public static int getMaxSequenceNumber(List<OutsourceStockOutApplyDetail> details) {
        if (CollectionUtils.isEmpty(details)) {
            return 0;
        }
        return details.stream().mapToInt(OutsourceStockOutApplyDetail::getSequenceNumber).max().getAsInt();
    }
}
