package cec.jiutian.bc.outbound.domain.tailMaterialStockIn.proxy;

import cec.jiutian.bc.outbound.domain.tailMaterialStockIn.model.TailMaterialStockIn;
import cec.jiutian.bc.outbound.enumeration.OmOrderProcessStateEnum;
import cec.jiutian.bc.outbound.enumeration.OrderCurrentStateEnum;
import cec.jiutian.view.fun.FlowProxy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class TailMaterialStockInFlowProxy extends FlowProxy {
    // 日志常量定义
    /*private static final String LOG_TAIL_MATERIAL_STOCK_IN_TYPE_MISMATCH = "回调事件中的数据类型不匹配，期望类型为TailMaterialStockIn";
    private static final String LOG_UNKNOWN_EXAMINE_STATUS = "未知的审批状态：{}";
    @Resource
    private JpaCrud jpaCrud;
    @Resource
    private OutBoundService outBoundService;*/

    public void onEvent(Object event, Object dateObj) {
        /*if (!(dateObj instanceof TailMaterialStockIn tailMaterialStockIn)) {
            log.warn(LOG_TAIL_MATERIAL_STOCK_IN_TYPE_MISMATCH);
            return;
        }

        String examineStatus = tailMaterialStockIn.getExamineStatus();
        OrderCurrentStateEnum.Enum currentState = null;
        OmOrderProcessStateEnum.Enum processState = null;
        boolean isAudited = ExamineStatusEnum.AUDITED.getCode().equals(examineStatus);
        boolean isAuditing = ExamineStatusEnum.AUDITING.getCode().equals(examineStatus);
        boolean isRejected = ExamineStatusEnum.REJECTED.getCode().equals(examineStatus);

        if (isAudited) {
            currentState = OrderCurrentStateEnum.Enum.COMPLETE;
            processState = OmOrderProcessStateEnum.Enum.StockIn;
            // 处理下料配送信息，剩余重量=剩余重量-入库重量
            updateCuttingDelivery(tailMaterialStockIn);
            // 调用尾料台账接口，完成入库
            createTailMaterialStockIn(tailMaterialStockIn);
        } else if (isAuditing) {
            currentState = OrderCurrentStateEnum.Enum.EXECUTE;
            processState = OmOrderProcessStateEnum.Enum.StockInIng;
        } else if (isRejected) {
            currentState = OrderCurrentStateEnum.Enum.END;
            processState = OmOrderProcessStateEnum.Enum.AwaitStockIn;
        } else {
            log.warn(LOG_UNKNOWN_EXAMINE_STATUS, examineStatus);
            return;
        }
        // 更新状态
        updateTailMaterialState(tailMaterialStockIn, currentState, processState);*/
    }


    // 假设的处理下料配送信息的方法
    private void updateCuttingDelivery(TailMaterialStockIn tailMaterialStockIn) {
        /*outBoundService.updateRestQuantity(tailMaterialStockIn);*/
    }

    /**
     * 更新尾料入库状态
     */
    private void updateTailMaterialState(TailMaterialStockIn tailMaterialStockIn,
                                         OrderCurrentStateEnum.Enum currentState,
                                         OmOrderProcessStateEnum.Enum processState) {
      /*  tailMaterialStockIn.setCurrentState(currentState.name());
        tailMaterialStockIn.setProcessState(processState.name());
        jpaCrud.update(tailMaterialStockIn);
        log.info("Updated tailMaterialStockIn with currentState: {}, processState: {}", currentState.name(), processState.name());*/
    }


    /**
     * 创建尾料入库单对应的尾料库存台账
     *
     * @param tailMaterialStockIn 尾料入库单实体
     */
    private void createTailMaterialStockIn(TailMaterialStockIn tailMaterialStockIn) {
        /*List<TailMaterialStockInDetail> tailMaterialStockInDetails = tailMaterialStockIn.getTailMaterialStockInDetails();
        if (CollectionUtils.isNotEmpty(tailMaterialStockInDetails)) {
            for (TailMaterialStockInDetail tailMaterialStockInDetail : tailMaterialStockInDetails) {
                TailInventory tailInventory = new TailInventory();
                BeanUtil.copyProperties(tailMaterialStockInDetail, tailInventory,"id");
                tailInventory.setReceiveOrg(tailMaterialStockIn.getReceiveOrg());
                outBoundService.createTailInventory(tailInventory);
            }
        }*/
    }

    @Override
    public void beforeSubmit(Object entity) {
       /* if (entity instanceof TailMaterialStockIn tailMaterialStockIn) {
            List<TailMaterialStockInDetail> detailList = tailMaterialStockIn.getTailMaterialStockInDetails();
            if (CollectionUtils.isEmpty(detailList)) {
                throw new ServiceException("缺失明细信息，无法提交审核");
            }
            tailMaterialStockIn.setCurrentState(OrderCurrentStateEnum.Enum.EXECUTE.name());
        }*/
    }

    @Override
    public void afterSubmit(Object entity) {
        /*if (entity instanceof TailMaterialStockIn tailMaterialStockIn) {
            tailMaterialStockIn.setCurrentState(OrderCurrentStateEnum.Enum.EXECUTE.name());
            tailMaterialStockIn.setProcessState(OmOrderProcessStateEnum.Enum.StockInIng.name());
            jpaCrud.update(tailMaterialStockIn);
        }*/
    }

}
