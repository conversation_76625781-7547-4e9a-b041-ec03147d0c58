package cec.jiutian.bc.outbound.domain.stockOut.handler;

import cec.jiutian.bc.generalModeler.enumeration.StockTypeEnum;
import cec.jiutian.bc.outbound.domain.stockOut.model.ProductionPickingStockOut;
import cec.jiutian.bc.outbound.domain.stockOut.model.ProductionPickingStockOutDetail;
import cec.jiutian.common.util.BeanUtils;
import cec.jiutian.common.util.StringUtils;
import cec.jiutian.data.jpa.JpaCrud;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import cec.jiutian.bc.outbound.domain.stockOutApply.model.ProductMaterialApply;
import cec.jiutian.bc.outbound.domain.stockOutApply.model.ProductMaterialApplyDetail;

@Component
public class ProductionPickingStockOutDetailHandler implements DependFiled.DynamicHandler<ProductionPickingStockOut> {
    @Resource
    private JpaCrud jpaCrud;

    @Override
    public Map<String, Object> handle(ProductionPickingStockOut productionPickingStockOut) {
        Map<String, Object> result = new HashMap<>();
        List<ProductionPickingStockOutDetail> list = new ArrayList<>();
        if (productionPickingStockOut.getProductMaterialApply() != null) {
            ProductMaterialApply productMaterialApply = jpaCrud.getById(ProductMaterialApply.class,
                    productionPickingStockOut.getProductMaterialApply().getId());
            if (productMaterialApply != null) {
                if (CollectionUtils.isNotEmpty(productMaterialApply.getDetails())) {
                    for (ProductMaterialApplyDetail productMaterialApplyDetail : productMaterialApply.getDetails()) {
                        ProductionPickingStockOutDetail productionPickingStockOutDetail = new ProductionPickingStockOutDetail();
                        BeanUtils.copyProperties(productMaterialApplyDetail, productionPickingStockOutDetail);
                        productionPickingStockOutDetail.setId(null);
                        productionPickingStockOutDetail.setMaterialCategory(productMaterialApplyDetail.getStockType());
                        if (StringUtils.equals(productMaterialApplyDetail.getStockType(), StockTypeEnum.Enum.MATERIAL.name())) {
                            productionPickingStockOutDetail.setMaterialCategoryName(StockTypeEnum.Enum.MATERIAL.getTitle());
                        } else if (StringUtils.equals(productMaterialApplyDetail.getStockType(), StockTypeEnum.Enum.PRODUCT.name())) {
                            productionPickingStockOutDetail.setMaterialCategoryName(StockTypeEnum.Enum.PRODUCT.getTitle());
                        }
                        productionPickingStockOutDetail.setMaterialLotIdentifier(productMaterialApplyDetail.getOriginLotId());
                        productionPickingStockOutDetail.setFactoryLotIdentifier(productMaterialApplyDetail.getSerialLotId());
                        productionPickingStockOutDetail.setUnit(productMaterialApplyDetail.getMeasureUnit());
                        productionPickingStockOutDetail.setBoxedCode(productMaterialApplyDetail.getBoxedCode());
                        productionPickingStockOutDetail.setBoxNo(productMaterialApplyDetail.getBoxNo());
                        productionPickingStockOutDetail.setBoxedDetailId(productMaterialApplyDetail.getBoxedDetailId());
                        productionPickingStockOutDetail.setQuantity(productMaterialApplyDetail.getRequestQuantity());
                        list.add(productionPickingStockOutDetail);
                    }
                    result.put("productionPickingStockOutDetailList", list);
                }
            }
        }
        return result;
    }
}
