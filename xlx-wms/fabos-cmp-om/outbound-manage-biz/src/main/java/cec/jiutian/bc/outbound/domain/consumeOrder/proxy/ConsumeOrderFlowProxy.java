package cec.jiutian.bc.outbound.domain.consumeOrder.proxy;

import cec.jiutian.bc.flow.enums.ExamineStatusEnum;
import cec.jiutian.bc.outbound.domain.consumeOrder.model.ConsumeOrder;
import cec.jiutian.bc.outbound.domain.consumeOrder.model.ConsumeOrderDetail;
import cec.jiutian.bc.outbound.domain.consumeOrder.model.ConsumeOrderDetailLot;
import cec.jiutian.bc.outbound.enumeration.OrderCurrentStateEnum;
import cec.jiutian.bc.outbound.port.client.InventoryClient;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.data.jpa.JpaCrud;
import cec.jiutian.view.fun.FlowProxy;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 通用规则c
 */
@Component
@Slf4j
@Transactional
public class ConsumeOrderFlowProxy extends FlowProxy {
    @Resource
    private JpaCrud jpaCrud;
    @Resource
    private InventoryClient inventoryClient;
    @Resource
    private ConsumeOrderProxy consumeOrderProxy;

    public void onEvent(Object event, Object data) {
        if (data instanceof ConsumeOrder consumeOrder) {
            if (consumeOrder.getExamineStatus().equals(ExamineStatusEnum.AUDITED.getCode())) {
                consumeOrder.setCurrentState(OrderCurrentStateEnum.Enum.COMPLETE.name());
                //审核通过处理台账
                handleInventoryByFlow(consumeOrder);
            } else if (consumeOrder.getExamineStatus().equals(ExamineStatusEnum.REJECTED.getCode())) {
                consumeOrder.setCurrentState(OrderCurrentStateEnum.Enum.END.name());
                //驳回就终止了  这里需要解锁库存  但是领用申请不会修改状态。到这里相当于是终止了。所以该出库单关联申请单也失效。和删除出库单逻辑不一样
                consumeOrderProxy.afterDelete(consumeOrder);
                //consumeOrderProxy.unLock(consumeOrder);
            }
            jpaCrud.update(consumeOrder);
        }
        log.info("领用出库审批回调成功");
    }

    /**
     * 审核通过调用
     *
     * @param consumeOrder
     */
    private void handleInventoryByFlow(ConsumeOrder consumeOrder) {
        List<ConsumeOrderDetailLot> consumeOrderDetailLots = new ArrayList<>();
        //通过才处理批次数据
        List<ConsumeOrderDetail> consumeOrderDetails = consumeOrder.getConsumeOrderDetails();
        if (CollectionUtils.isNotEmpty(consumeOrderDetails)) {
            consumeOrderDetails.forEach(d -> {
                //这里必须做一次查询操作  否则存在多个物料，并且都有批次信息的时候。会有懒加载的问题（目前没有更好的方案，或者就是直接将懒加载改为即时记载）
                ConsumeOrderDetail orderDetail = jpaCrud.getById(ConsumeOrderDetail.class, d.getId());
                if (CollectionUtils.isNotEmpty(orderDetail.getConsumeOrderDetailLots())) {
                    orderDetail.getConsumeOrderDetailLots().forEach(lot -> {

                        if (!Boolean.parseBoolean(inventoryClient.lock(lot.getInventoryId(), consumeOrder.getGeneralCode(), "ConsumeOrder").toString())) {
                            //没锁住 需要直接处理
                            consumeOrderDetailLots.add(lot);
                        }
                    });
                }
            });
        }
        if (CollectionUtils.isNotEmpty(consumeOrderDetailLots)) {
            handleInventory(consumeOrder, consumeOrderDetailLots);
        }
    }

    /**
     * 审核通过处理台账
     *
     * @param consumeOrder
     * @param consumeOrderDetailLots
     */
    private void handleInventory(ConsumeOrder consumeOrder, List<ConsumeOrderDetailLot> consumeOrderDetailLots) {
        if (CollectionUtils.isNotEmpty(consumeOrderDetailLots)) {
            consumeOrderDetailLots.forEach(lot -> {
                Object o = inventoryClient.stockOutSplit(lot.getInventoryId(), lot.getQuantity(), consumeOrder.getConsumeRequest().getConsumeType(), consumeOrder.getGeneralCode());
            });
        }
    }

    /**
     * 批次解锁调用
     *
     * @param generalCode
     * @param inventoryId
     */
    public void inventoryUnLock(String generalCode, String inventoryId) {
        List<ConsumeOrderDetailLot> consumeOrderDetailLots = new ArrayList<>();
        ConsumeOrder consumeOrder = new ConsumeOrder();
        consumeOrder.setGeneralCode(generalCode);
        ConsumeOrder selectOne = jpaCrud.selectOne(consumeOrder);
        if (selectOne != null) {
            List<ConsumeOrderDetail> consumeOrderDetails = selectOne.getConsumeOrderDetails();
            if (CollectionUtils.isNotEmpty(consumeOrderDetails)) {
                consumeOrderDetails.forEach(d -> {
                    //这里必须做一次查询操作  否则存在多个物料，并且都有批次信息的时候。会有懒加载的问题（目前没有更好的方案，或者就是直接将懒加载改为即时记载）
                    ConsumeOrderDetail orderDetail = jpaCrud.getById(ConsumeOrderDetail.class, d.getId());
                    if (CollectionUtils.isNotEmpty(orderDetail.getConsumeOrderDetailLots())) {
                        orderDetail.getConsumeOrderDetailLots().forEach(lot -> {
                            if (lot.getInventoryId().equals(inventoryId)) {
                                consumeOrderDetailLots.add(lot);
                            }
                        });
                    }
                });
            }
        }
        if (CollectionUtils.isNotEmpty(consumeOrderDetailLots)) {
            handleInventory(selectOne, consumeOrderDetailLots);
        }
    }

    @Override
    public void beforeSubmit(Object entity) {
        if (entity instanceof ConsumeOrder consumeOrder) {
            List<ConsumeOrderDetail> detailList = consumeOrder.getConsumeOrderDetails();
            if (CollectionUtils.isEmpty(detailList)) {
                throw new ServiceException("缺失明细信息，无法提交审核");
            } else {
                //如果存在明细  还需要校验明细里面的批次信息
                detailList.forEach(d -> {
                    if (CollectionUtils.isEmpty(d.getConsumeOrderDetailLots())) {
                        throw new ServiceException("明细缺失出库批次信息，无法提交审核");
                    } else {
                        //实发数量不为空或者0
                        d.getConsumeOrderDetailLots().forEach(lot -> {
                            if (lot.getQuantity() == null || lot.getQuantity() == 0) {
                                throw new ServiceException("明细实发数量不能为空，无法提交审核");
                            }
                        });
                    }
                });
            }
            consumeOrder.setCurrentState(OrderCurrentStateEnum.Enum.EXECUTE.name());
        }
    }
}
