package cec.jiutian.bc.outbound.domain.stockOut.handler;

import cec.jiutian.bc.outbound.domain.stockOut.model.InspectStockOut;
import cec.jiutian.bc.outbound.domain.stockOut.model.OutsourceStockOut;
import cec.jiutian.bc.outbound.domain.stockOut.model.OutsourceStockOutDetail;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.data.jpa.JpaCrud;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class OutsourceStockOutReleaseHandler implements OperationHandler<OutsourceStockOut, Void> {
    @Resource
    private JpaCrud jpaCrud;


    @Override
    public String exec(List<OutsourceStockOut> outsourceStockOutList, Void modelObject, String[] param) {
        if(CollectionUtils.isNotEmpty(outsourceStockOutList)) {
            OutsourceStockOut outsourceStockOut = outsourceStockOutList.get(0);
            outsourceStockOut.setCurrentState(OrderCurrentStateEnum.Enum.EXECUTE.name());
            jpaCrud.update(outsourceStockOut);
            //TODO 与立库系统对接
        }
        return "msg.success('操作成功')";
    }
}