package cec.jiutian.bc.outbound.domain.cuttingDelivery.model;

import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleModel;
import cec.jiutian.bc.generalModeler.enumeration.StockTypeEnum;
import cec.jiutian.bc.outbound.domain.cuttingDelivery.handler.CuttingDeliveryDetailHandler;
import cec.jiutian.bc.outbound.domain.cuttingDelivery.proxy.CuttingDeliveryFlowProxy;
import cec.jiutian.bc.outbound.domain.cuttingDelivery.proxy.CuttingDeliveryProxy;
import cec.jiutian.bc.outbound.domain.cuttingOrder.model.CuttingOrder;
import cec.jiutian.bc.outbound.enumeration.OmNamingRuleCodeEnum;
import cec.jiutian.bc.outbound.enumeration.OrderCurrentStateEnum;
import cec.jiutian.bc.outbound.util.OmMaterialByCodeTableFormHandler;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.ReferenceGenerateType;
import cec.jiutian.view.SubTableField;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowBaseOperation;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OrderBy;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@FabosJson(
        name = "毛坯件配送",
        orderBy = "CuttingDelivery.createTime desc",
        dataProxy = CuttingDeliveryProxy.class,
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "currentState != 'EDIT'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "currentState != 'EDIT'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "examine",
                        ifExpr = "examineStatus != '0'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "examineDetails",
                        ifExpr = "examineStatus == '0'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                )
        },
        // TODO 暂时不进行尾料入库的快捷操作
        /*rowOperation = {
                @RowOperation(
                        code = "CuttingDelivery@WASTEDISPOSAL",
                        operationHandler = WasteDisposalOperationHanler.class,
                        mode = RowOperation.Mode.SINGLE,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = WasteDisposalOperationView.class,
                        title = "剩料处理",
                        show = @ExprBool(
                                value = true,
                                params = "CuttingDelivery@WASTEDISPOSAL"
                        ),
                        // TODO 按钮暂时置灰
                        ifExpr = "currentState != 'TEST'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                )
        },*/
        flowProxy = {CuttingDeliveryFlowProxy.class},
        flowCode = "CuttingDelivery",
        power = @Power(examine = true, examineDetails = true)
)
@Table(name = "bwi_cutting_delivery",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Getter
@Setter
@TemplateType(type = "multiTable")
public class CuttingDelivery extends NamingRuleModel {
    @Override
    public String getNamingCode() {
        return OmNamingRuleCodeEnum.CuttingDeliveryCode.name();
    }

    @FabosJsonField(
            views = @View(title = "单据名称"),
            edit = @Edit(title = "单据名称", search = @Search(vague = true))
    )
    private String orderName;

    // TODO OneToOne解析有问题, 后续修改为OneToOne
    @ManyToOne
    @JoinColumn(name = "cutting_order_id")
    @FabosJsonField(
            views = @View(title = "下料出库单", column = "generalCode"),
            edit = @Edit(title = "下料出库单",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode"),
                    filter = @Filter(value = "CuttingOrder.currentState = 'COMPLETE' and CuttingOrder.examineStatus = '1'" +
                            "and CuttingOrder.orderDeliveryId is null"),
                    allowAddMultipleRows = false
            )
    )
    private CuttingOrder cuttingOrder;

    @FabosJsonField(
            views = @View(title = "零部件编号"),
            edit = @Edit(title = "零部件编号", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "cuttingOrder", beFilledBy = "partCode")
            )
    )
    private String partCode;

    // 物料/产品主键id, 用于View注解钟的TABLE_FORM展示
    @FabosJsonField(
            views = @View(title = "物料主键id", show = false),
            edit = @Edit(title = "物料主键id", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "cuttingOrder", beFilledBy = "materialId"))
    )
    private String materialId;

    // 物资类别, 用于View注解钟的TABLE_FORM展示
    @FabosJsonField(
            views = @View(title = "物资类别", show = false),
            edit = @Edit(title = "物资类别", show = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = StockTypeEnum.class)
            )
    )
    @SubTableField
    private String stockType;

    @FabosJsonField(
            views = @View(title = "零部件名称", tableFormParamsHandler = OmMaterialByCodeTableFormHandler.class, type = ViewType.TABLE_FORM),
            edit = @Edit(title = "零部件名称", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "cuttingOrder", beFilledBy = "partName"))
    )
    private String partName;

    // 从零部件回显保存, 目前暂时使用物料里面的牌号, 这一块还没完全确定
    @FabosJsonField(
            views = @View(title = "零部件图号"),
            edit = @Edit(title = "零部件图号", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "cuttingOrder", beFilledBy = "partBrandCode"))
    )
    private String partBrandCode;

    @FabosJsonField(
            views = @View(title = "领用部门"),
            edit = @Edit(title = "领用部门", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "cuttingOrder", beFilledBy = "receiveOrg")
            )
    )
    private String receiveOrg;

    @FabosJsonField(
            views = @View(title = "当前状态"),
            edit = @Edit(title = "当前状态", type = EditType.CHOICE, search = @Search, show = false,
                    choiceType = @ChoiceType(fetchHandler = OrderCurrentStateEnum.class))
    )
    private String currentState;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "cutting_delivery_detail_id")
    @OrderBy
    @FabosJsonField(
            edit = @Edit(title = "下料配送明细", type = EditType.TAB_REFERENCE_GENERATE),
            views = @View(title = "下料配送明细", type = ViewType.TABLE_VIEW),
            dynamicField = @DynamicField(dependFiled = @DependFiled(
                    changeBy = "cuttingOrder", dynamicHandler = CuttingDeliveryDetailHandler.class)),
            referenceGenerateType = @ReferenceGenerateType(editable = {"realCuttingQuantity", "partQuantity",
                    "tailQuantity", "backQuantity", "remark"})
    )
    private List<CuttingDeliveryDetail> cuttingDeliveryDetails;
}
