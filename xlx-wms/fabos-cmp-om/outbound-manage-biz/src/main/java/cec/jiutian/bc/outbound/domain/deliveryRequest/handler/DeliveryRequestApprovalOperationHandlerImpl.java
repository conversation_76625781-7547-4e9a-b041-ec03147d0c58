package cec.jiutian.bc.outbound.domain.deliveryRequest.handler;

import cec.jiutian.bc.outbound.domain.deliveryRequest.model.DeliveryRequest;
import cec.jiutian.bc.outbound.enumeration.OrderCurrentStateEnum;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.data.jpa.JpaCrud;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Component
public class DeliveryRequestApprovalOperationHandlerImpl implements OperationHandler<DeliveryRequest, Void> {
    @Resource
    private JpaCrud jpaCrud;

    @Override
    public Void fabosJsonFormValue(List<DeliveryRequest> data, Void fabosJsonForm, String[] param) {
        return OperationHandler.super.fabosJsonFormValue(data, fabosJsonForm, param);
    }

    @Override
    public String exec(List<DeliveryRequest> data, Void modelObject, String[] param) {
        if (!CollectionUtils.isEmpty(data)) {
            data.forEach(d -> {
                String currentState = OrderCurrentStateEnum.Enum.EDIT.name();
                if (!currentState.equals(d.getCurrentState())) {
                    throw new ServiceException("非开立状态不允许提交审批");
                }
                d.setCurrentState(OrderCurrentStateEnum.Enum.EXECUTE.name());
            });
            jpaCrud.updateBatchById(data);
        }
        return null;
    }
}
