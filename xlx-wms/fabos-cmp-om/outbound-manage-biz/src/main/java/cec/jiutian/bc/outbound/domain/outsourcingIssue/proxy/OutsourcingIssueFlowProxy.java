package cec.jiutian.bc.outbound.domain.outsourcingIssue.proxy;

import cec.jiutian.bc.flow.enums.ExamineStatusEnum;
import cec.jiutian.bc.outbound.domain.issueNotice.model.IssueNoticeDetail;
import cec.jiutian.bc.outbound.domain.outsourcingIssue.model.OutsourcingIssue;
import cec.jiutian.bc.outbound.domain.outsourcingIssue.model.OutsourcingIssueDetail;
import cec.jiutian.bc.outbound.port.client.InventoryClient;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.FlowProxy;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/1/3
 * @description TODO
 */
@Component
public class OutsourcingIssueFlowProxy extends FlowProxy {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private OutsourcingIssueProxy outsourcingIssueProxy;

    @Resource
    private InventoryClient inventoryClient;

    @Override
    public void beforeSubmit(Object entity) {
        if (entity instanceof OutsourcingIssue outsourcingIssue) {
            if (CollectionUtils.isEmpty(outsourcingIssue.getDetailList())) {
                throw new FabosJsonApiErrorTip("未选择明细数据，不能发起审批");
            } else {
                outsourcingIssue.getDetailList().forEach(detail -> {
                    if (CollectionUtils.isEmpty(detail.getLotDetailList())) {
                        throw new FabosJsonApiErrorTip("未选择发料物资批次数据，不能发起审批");
                    }
                });
                outsourcingIssue.setCurrentState(OrderCurrentStateEnum.Enum.EXECUTE.name());
                fabosJsonDao.mergeAndFlush(outsourcingIssue);
            }
        }
    }

    @Override
    public void onEvent(Object event, Object entity) {
        if (entity instanceof OutsourcingIssue outsourcingIssue) {
            if (outsourcingIssue.getExamineStatus().equals(ExamineStatusEnum.AUDITED.getCode())) {
                outsourcingIssue.setCurrentState(OrderCurrentStateEnum.Enum.COMPLETE.name());
                if (CollectionUtils.isNotEmpty(outsourcingIssue.getDetailList())) {
                    outsourcingIssue.getDetailList().forEach(d -> {
                        // 再查询一次子模型避免懒加载问题
                        OutsourcingIssueDetail issueDetail = fabosJsonDao.findById(OutsourcingIssueDetail.class, d.getId());
                        if (CollectionUtils.isNotEmpty(issueDetail.getLotDetailList())) {
                            // 扣减出库数量更新库存批次
                            issueDetail.getLotDetailList().forEach(lotDetail -> {
                                Object o = inventoryClient.stockOutSplit(lotDetail.getInventoryId(), lotDetail.getIssueLotQuantity(), "OutsourcingIssue", outsourcingIssue.getGeneralCode());
                            });
                        }

                        // 修改发料通知已发数量
                        if (StringUtils.isNotEmpty(d.getExtraModelId())) {
                            IssueNoticeDetail issueNoticeDetail = fabosJsonDao.findById(IssueNoticeDetail.class, d.getExtraModelId());
                            issueNoticeDetail.setIssuedQuantity((issueNoticeDetail.getIssuedQuantity() == null ? 0.0 : issueNoticeDetail.getIssuedQuantity())
                                    + d.getIssueQuantity());
                            if (Objects.equals(issueNoticeDetail.getQuantity(), issueNoticeDetail.getIssuedQuantity())) {
                                issueNoticeDetail.setIssueState(true);
                            }
                            fabosJsonDao.mergeAndFlush(issueNoticeDetail);
                        }
                    });
                }
            } else if (outsourcingIssue.getExamineStatus().equals(ExamineStatusEnum.REJECTED.getCode())) {
                outsourcingIssue.setCurrentState(OrderCurrentStateEnum.Enum.END.name());
                outsourcingIssueProxy.unlockLot(outsourcingIssue);
            }
            fabosJsonDao.mergeAndFlush(outsourcingIssue);
        }
    }
}
