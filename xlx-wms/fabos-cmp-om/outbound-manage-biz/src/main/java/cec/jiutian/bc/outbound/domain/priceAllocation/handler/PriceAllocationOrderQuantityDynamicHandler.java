package cec.jiutian.bc.outbound.domain.priceAllocation.handler;

import cec.jiutian.bc.outbound.domain.priceAllocation.model.PriceAllocation;
import cec.jiutian.bc.outbound.domain.priceAllocation.model.PriceAllocationDetail;
import cec.jiutian.view.DependFiled;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @description:
 */
@Component
public class PriceAllocationOrderQuantityDynamicHandler implements DependFiled.DynamicHandler<PriceAllocation> {
    @Override
    public Map<String, Object> handle(PriceAllocation priceAllocation) {
        Map<String, Object> result = new HashMap<>();

        if (priceAllocation != null) {
            List<PriceAllocationDetail> priceAllocationDetails = priceAllocation.getPriceAllocationDetails();

            // 如果价格分配详情为空，则跳过处理
            if (CollectionUtils.isNotEmpty(priceAllocationDetails)) {
                BigDecimal totalQuantity = BigDecimal.ZERO;
                BigDecimal totalPrice = BigDecimal.ZERO;

                // 获取 inviteAllocationQuantity，数量是详情的大小
                double inviteAllocationQuantity = priceAllocation.getInviteAllocationQuantity();

                // 核拨数量不能超过请拨数量
                for (PriceAllocationDetail detail : priceAllocationDetails) {
                    // 使用 null-safe 的方式处理数量和价格，防止 null
                    BigDecimal verifyAllocationQuantity = Optional.ofNullable(detail.getVerifyAllocationQuantity())
                            .map(BigDecimal::valueOf)
                            .orElse(BigDecimal.ZERO);
                    BigDecimal price = Optional.ofNullable(detail.getPrice())
                            .map(BigDecimal::valueOf)
                            .orElse(BigDecimal.ZERO);
                    // 累加 verifyAllocationQuantity
                    totalQuantity = totalQuantity.add(verifyAllocationQuantity);
                    // 累加 totalPrice
                    totalPrice = totalPrice.add(verifyAllocationQuantity.multiply(price));
                    // 检查 totalQuantity 是否大于 inviteAllocationQuantity
/*                    if (totalQuantity.compareTo(BigDecimal.valueOf(inviteAllocationQuantity)) > 0) {
                        // 如果超出，抛出异常
                        throw new FabosJsonApiErrorTip("核拨数量总和不能大于请拨数量！");
                    }*/

                }

                // 保留两位小数
                result.put("verifyAllocationQuantityCount", totalQuantity.setScale(2, RoundingMode.HALF_UP));
                result.put("verifyAllocationPrice", totalPrice.setScale(2, RoundingMode.HALF_UP));
            }
        }

        return result;
    }


}
