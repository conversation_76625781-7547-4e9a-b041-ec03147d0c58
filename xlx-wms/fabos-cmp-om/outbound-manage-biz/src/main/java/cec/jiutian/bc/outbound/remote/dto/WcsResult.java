package cec.jiutian.bc.outbound.remote.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class WcsResult {

    /**
     * 200成功,非200失败
     */
    private Integer code;
    /**
     * 类型
     */
    private String type;
    /**
     * 报错信息
     */
    private String message;

    public static WcsResult success() {
        return new WcsResult(200, "success", "");
    }

    public static WcsResult failure() {
        return new WcsResult(400, "error", "系统处理异常");
    }
}
