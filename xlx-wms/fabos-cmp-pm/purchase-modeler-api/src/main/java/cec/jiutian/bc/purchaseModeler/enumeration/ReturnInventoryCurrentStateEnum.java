package cec.jiutian.bc.purchaseModeler.enumeration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/20
 * @description TODO
 */
public class ReturnInventoryCurrentStateEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum value : Enum.values()) {
            list.add(new VLModel(value.name(), value.getTitle()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        WaitReturn("待返厂"),
        Returning("返厂中"),
        Returned("已返厂"),
        AwaitStockIn("待入库"),
        StockInIng("入库中"),
        StockIn("已入库");

        private final String title;

    }
}
