package cec.jiutian.bc.purchase.remote.rpc;

import cec.jiutian.bc.purchase.domain.receivePlan.model.ToSupplierInspectMaterialLot;
import cec.jiutian.bc.purchase.domain.receivePlan.port.dto.ReceivePlanDTO;
import cec.jiutian.bc.purchase.domain.receivePlan.port.dto.ReceivePlanDetailDTO;
import cec.jiutian.bc.purchase.domain.receivePlan.port.dto.ReceivePlanQueryDTO;
import cec.jiutian.bc.purchase.domain.receivePlan.service.ReceivePlanService;
import cec.jiutian.data.jpa.JpaCrud;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：2024/11/18 9:28
 * @description：
 */
@Component
public class ReceivePlanRpc {

    @Resource
    private JpaCrud jpaCrud;

    @Resource
    private ReceivePlanService receivePlanService;

    public String updateIqcQuantity(String code, String params) {
        if (StringUtils.isNotEmpty(code) && StringUtils.isNotEmpty(params)) {
            JSONObject jsonObject = JSONObject.parseObject(params);
            if (jsonObject != null) {
                if (StringUtils.isNotEmpty(jsonObject.getString("materialCode")) &&
                        StringUtils.isNotEmpty(jsonObject.getString("serialLotId"))) {
                    ToSupplierInspectMaterialLot condition = new ToSupplierInspectMaterialLot();
                    condition.setSerialLotCode(jsonObject.getString("serialLotId"));
                    ToSupplierInspectMaterialLot data = jpaCrud.selectOne(condition);
                    data.setPlanInjectedQuantity(Double.parseDouble(jsonObject.getString("qualifiedQuantity")));
                    receivePlanService.updateIqcQuantity(data);
                }
            }
        }
        return "success";
    }

    public String queryMaterialLot(String param) {
        ReceivePlanQueryDTO queryDTO = new ReceivePlanQueryDTO();
        queryDTO.setPlanDetailId(param);
        List<ToSupplierInspectMaterialLot> lotList = receivePlanService.queryMaterialLot(queryDTO);
        if (CollectionUtils.isNotEmpty(lotList)) {
            Map<String, Object> result = new HashMap<>();
            result.put("planDetailId", param);
            lotList.forEach(l -> {
                Map<String, Object> map = new HashMap<>();
                map.put("planDetailId", l.getToSupplierInspectMaterial().getId());
                map.put("serialLotId", l.getSerialLotCode());
                map.put("originLotId", l.getSourceLotCode());
                map.put("lotQuantity", l.getLotQuantity());
                result.put("lotData", map);
            });
            return JSONObject.toJSONString(result);
        }
        return null;
    }

    public void updateArrivalQuantity(String param) {
        if (StringUtils.isNotEmpty(param)) {
            JSONObject jsonObject = JSONObject.parseObject(param);
            JSONArray planDetailList = jsonObject.getJSONArray("planDetailList");
            if (CollectionUtils.isNotEmpty(planDetailList)) {
                for (int i = 0; i < planDetailList.size(); i++) {
                    JSONObject detailJson = planDetailList.getJSONObject(i);
                    ReceivePlanDetailDTO detailDTO = new ReceivePlanDetailDTO();
                    detailDTO.setReceivePlanDetailId(detailJson.getString("receivePlanDetailId"));
                    detailDTO.setChangeType(detailJson.getString("type"));
                    detailDTO.setArrivalQuantity(detailJson.getString("arrivalQuantity"));
                    receivePlanService.updateArrivalQuantity(detailDTO);
                }
            }
        }
    }

    public void updateReceivePlanState(String param) {
        if (StringUtils.isNotEmpty(param)) {
            JSONObject jsonObject = JSONObject.parseObject(param);
            if (jsonObject != null) {
                ReceivePlanDTO receivePlanDTO = new ReceivePlanDTO();
                receivePlanDTO.setReceivePlanId(jsonObject.getString("receivePlanId"));
                receivePlanDTO.setPlanState(jsonObject.getString("receivePlanState"));
                receivePlanDTO.setReceivePlanCode(jsonObject.getString("receivePlanCode"));
                receivePlanDTO.setLotSerialCode(jsonObject.getString("lotSerialCode"));
                receivePlanService.updateReceivePlanState(receivePlanDTO);
            }
        }
    }

    public void updateReceivePlanCurrentState(String param) {
        if (StringUtils.isNotEmpty(param)) {
            JSONObject jsonObject = JSONObject.parseObject(param);
            if (jsonObject != null) {
                ReceivePlanDTO receivePlanDTO = new ReceivePlanDTO();
                receivePlanDTO.setReceivePlanId(jsonObject.getString("receivePlanId"));
                receivePlanDTO.setPlanCurrentState(jsonObject.getString("receivePlanCurrentState"));
                receivePlanService.updateReceivePlanCurrentState(receivePlanDTO);
            }
        }
    }

    public void updateReturnQuantity(String param) {
        if (StringUtils.isNotEmpty(param)) {
            JSONObject jsonObject = JSONObject.parseObject(param);
            if (jsonObject != null) {
                ReceivePlanDTO receivePlanDTO = new ReceivePlanDTO();
                receivePlanDTO.setReceivePlanId(jsonObject.getString("receivePlanId"));
                receivePlanDTO.setPlanState(jsonObject.getString("receivePlanState"));
                receivePlanDTO.setReceivePlanCode(jsonObject.getString("receivePlanCode"));
                receivePlanDTO.setLotSerialCode(jsonObject.getString("lotSerialCode"));
                receivePlanDTO.setReturnQuantity(jsonObject.getString("returnQuantity"));
                receivePlanService.updateReturnQuantity(receivePlanDTO);
            }
        }
    }
}
