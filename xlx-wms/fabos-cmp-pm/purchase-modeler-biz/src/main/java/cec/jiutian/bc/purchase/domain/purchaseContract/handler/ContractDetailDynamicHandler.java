package cec.jiutian.bc.purchase.domain.purchaseContract.handler;

import cec.jiutian.bc.purchase.domain.purchaseContract.model.PurchaseContract;
import cec.jiutian.bc.purchase.domain.purchaseContract.model.PurchaseContractDetail;
import cec.jiutian.bc.purchase.domain.purchaseRequest.model.PurchaseRequest;
import cec.jiutian.data.jpa.JpaCrud;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：2024/11/14 18:13
 * @description：
 */
@Component
public class ContractDetailDynamicHandler implements DependFiled.DynamicHandler<PurchaseContract> {

    @Resource
    private JpaCrud jpaCrud;

    @Override
    public Map<String, Object> handle(PurchaseContract purchaseContract) {
        Map<String, Object> result = new HashMap<>();
        List<PurchaseContractDetail> list = new ArrayList<>();
        // 若"订货申请"不为空则执行操作
        if (purchaseContract.getPurchaseRequest() != null) {
            // 获取订货申请对象
            PurchaseRequest purchaseRequest = jpaCrud.getById(PurchaseRequest.class,
                    purchaseContract.getPurchaseRequest().getId());
            if (purchaseRequest != null) {
                if (CollectionUtils.isNotEmpty(purchaseRequest.getDetailList())) {
                    // 遍历订货申请明细
                    purchaseRequest.getDetailList().forEach(d -> {
                        PurchaseContractDetail contractDetail = new PurchaseContractDetail();
                        contractDetail.setPurchaseRequestDetailCode(d.getPurchaseRequestDetailCode());
                        // 品号, 暂用物料编号作为品号
                        contractDetail.setMaterialCategory(d.getMaterialCode());
                        // 技术要求
                        contractDetail.setStandardNumber(d.getStandardNumber());
                        // 用途
                        contractDetail.setMaterialProduct(d.getMaterialProduct());
                        // 物料名称
                        contractDetail.setMaterialName(d.getMaterialName());
                        // 物料编码
                        contractDetail.setMaterialCode(d.getMaterialCode());
                        // 规格
                        contractDetail.setMaterialSpecification(d.getMaterialSpecification());
                        // 牌号
                        contractDetail.setBrandCode(d.getBrandCode());
                        // 数量
                        contractDetail.setQuantity(d.getApprovedQuantity());
                        // 单位
                        contractDetail.setMeasureUnit(d.getMeasureUnit());
                        // 设置类别和子类别
                        contractDetail.setStockType(d.getStockType());
                        contractDetail.setStockSubType(d.getStockSubType());
                        // 物料主键id
                        contractDetail.setMaterialId(d.getPurchasePlanDetail().getMaterial().getId());
                        list.add(contractDetail);
                    });
                    result.put("purchaseContractDetailList", list);
                }
            }
        }
        return result;
    }
}
