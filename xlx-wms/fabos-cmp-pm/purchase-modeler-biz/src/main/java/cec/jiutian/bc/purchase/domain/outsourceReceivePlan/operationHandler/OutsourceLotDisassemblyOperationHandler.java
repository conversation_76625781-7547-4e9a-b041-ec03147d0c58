package cec.jiutian.bc.purchase.domain.outsourceReceivePlan.operationHandler;

import cec.jiutian.bc.purchase.domain.outsourceReceivePlan.model.OutsourcePlanToSupplierInspectMaterialLot;
import cec.jiutian.bc.purchase.domain.outsourceReceivePlan.model.OutsourceReceivePlan;
import cec.jiutian.bc.purchase.domain.outsourceReceivePlan.model.OutsourceReceivePlanDetail;
import cec.jiutian.bc.purchase.domain.outsourceReceivePlan.operationModel.OutsourceReceivePlanDetailDisassemblyModel;
import cec.jiutian.bc.purchase.domain.outsourceReceivePlan.operationModel.OutsourceReceivePlanDisassemblyModel;
import cec.jiutian.bc.purchaseModeler.enumeration.PlanProcessStateEnum;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.core.frame.module.MetaUser;
import cec.jiutian.data.jpa.JpaCrud;
import cec.jiutian.view.fun.OperationHandler;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <AUTHOR>
 * @date 2024/12/3
 * @description TODO
 */
@Component
public class OutsourceLotDisassemblyOperationHandler implements OperationHandler<OutsourceReceivePlan, OutsourceReceivePlanDisassemblyModel> {

    @Resource
    private JpaCrud jpaCrud;

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<OutsourceReceivePlan> data, OutsourceReceivePlanDisassemblyModel modelObject, String[] param) {
        OutsourceReceivePlan receivePlan = data.get(0);
        AtomicReference<Double> allReceiveQuantity = new AtomicReference<>(0.0);
        AtomicReference<Double> allLotQuantity = new AtomicReference<>(0.0);
        if (CollectionUtils.isNotEmpty(receivePlan.getOutsourceReceivePlanDetailList()) && CollectionUtils.isNotEmpty(modelObject.getReceivePlanDetails())) {
            receivePlan.getOutsourceReceivePlanDetailList().forEach(d -> {
                allReceiveQuantity.set(allReceiveQuantity.get() + d.getReceiveQuantity());
                OutsourceReceivePlanDetailDisassemblyModel disassemblyDetail = modelObject.getReceivePlanDetails().stream().filter(m -> m.getId().equals(d.getId())).findFirst().get();
                if (CollectionUtils.isNotEmpty(disassemblyDetail.getMaterialLotList())) {
                    double totalLotQuantity = disassemblyDetail.getMaterialLotList().stream().mapToDouble(OutsourcePlanToSupplierInspectMaterialLot::getLotQuantity).sum();
                    if (totalLotQuantity > d.getReceiveQuantity()) {
                        throw new FabosJsonApiErrorTip("拆批总数量不能大于收货数量，请确认");
                    }
                    allLotQuantity.set(allLotQuantity.get() + totalLotQuantity);
                    disassemblyDetail.getMaterialLotList().forEach(l -> {
                        l.setToSupplierInspectMaterial(d);
                    });
                    d.setMaterialLotList(disassemblyDetail.getMaterialLotList());
                }
            });
        }
        if (allLotQuantity.get() > 0 && Objects.equals(allLotQuantity.get(), allReceiveQuantity.get())) {
            receivePlan.setProcessState(PlanProcessStateEnum.Enum.UnApproval.name());
        }
        jpaCrud.update(receivePlan);
        return "alert(操作成功)";
    }

    @Override
    public OutsourceReceivePlanDisassemblyModel fabosJsonFormValue(List<OutsourceReceivePlan> data, OutsourceReceivePlanDisassemblyModel fabosJsonForm, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            OutsourceReceivePlan receivePlan = data.get(0);
            fabosJsonForm.setId(receivePlan.getId());
            fabosJsonForm.setGeneralCode(receivePlan.getGeneralCode());
            fabosJsonForm.setReceiveName(receivePlan.getReceiveName());
            fabosJsonForm.setSupplier(receivePlan.getSupplier());
            fabosJsonForm.setPreReceivedDate(receivePlan.getPreReceivedDate());
            fabosJsonForm.setReceivePerson(receivePlan.getReceivePerson());
            fabosJsonForm.setReceivePerson(fabosJsonDao.findById(MetaUser.class, fabosJsonDao.findById(OutsourceReceivePlan.class, receivePlan.getId()).getReceivePerson().getId()));
            fabosJsonForm.setReceiveAddress(receivePlan.getReceiveAddress());
            fabosJsonForm.setContactPerson(receivePlan.getContactPerson());
            fabosJsonForm.setContactTel(receivePlan.getContactTel());
            fabosJsonForm.setReceivePlanDocument(receivePlan.getReceivePlanDocument());
            fabosJsonForm.setRemark(receivePlan.getRemark());
            List<OutsourceReceivePlanDetailDisassemblyModel> disassemblyModels = new ArrayList<>();
            List<OutsourceReceivePlanDetail> receivePlanDetails = fabosJsonDao.findById(OutsourceReceivePlan.class, receivePlan.getId()).getOutsourceReceivePlanDetailList();
            if (CollectionUtils.isNotEmpty(receivePlanDetails)) {
                receivePlanDetails.forEach(d -> {
                    OutsourceReceivePlanDetailDisassemblyModel detailDisassemblyModel = new OutsourceReceivePlanDetailDisassemblyModel();
                    detailDisassemblyModel.setId(d.getId());
                    detailDisassemblyModel.setReceivePlanDetailCode(d.getReceivePlanDetailCode());
                    detailDisassemblyModel.setMaterialCode(d.getMaterialCode());
                    detailDisassemblyModel.setMaterialName(d.getMaterialName());
                    detailDisassemblyModel.setReceiveQuantity(d.getReceiveQuantity());
                    detailDisassemblyModel.setMaterialLotList(d.getMaterialLotList());
                    disassemblyModels.add(detailDisassemblyModel);
                });
            }
            fabosJsonForm.setReceivePlanDetails(disassemblyModels);
        }
        return fabosJsonForm;
    }
}
