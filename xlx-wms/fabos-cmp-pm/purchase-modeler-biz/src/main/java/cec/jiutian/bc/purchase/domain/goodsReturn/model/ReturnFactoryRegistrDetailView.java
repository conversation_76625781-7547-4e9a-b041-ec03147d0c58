package cec.jiutian.bc.purchase.domain.goodsReturn.model;

import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.DateType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@FabosJson(
        name = "回厂登记明细"
)
@Table(name = "bwi_goods_return_detail",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"orderNumber"})
        }
)
@Entity
@Getter
@Setter
public class ReturnFactoryRegistrDetailView extends MetaModel {
    @FabosJsonField(
            views = @View(title = "单据编号"),
            edit = @Edit(title = "单据编号", search = @Search(vague = true),
                    readonly = @Readonly(add = false), inputType = @InputType(length = 40))
    )
    private String orderNumber;

    @FabosJsonField(
            views = @View(title = "返厂批次"),
            edit = @Edit(title = "返厂批次", notNull = true,
                    search = @Search(vague = true),
                    inputType = @InputType(length = 20))
    )
    // 返厂批次不可重复
    private String returnLotSerialId;

    @FabosJsonField(
            views = @View(title = "本厂批号"),
            edit = @Edit(title = "本厂批号", notNull = true,
                    search = @Search(vague = true),
                    inputType = @InputType(length = 20))
    )
    private String lotSerialId;

    @FabosJsonField(
            views = @View(title = "原炉/原厂批号"),
            edit = @Edit(title = "原炉/原厂批号", notNull = true,
                    search = @Search(vague = true),
                    inputType = @InputType(length = 20))
    )
    private String supplierLotSerialId;

    @FabosJsonField(
            views = @View(title = "供应商id", show = false),
            edit = @Edit(title = "供应商id", show = false)
    )
    private String supplierId;

    @FabosJsonField(
            views = @View(title = "供应商"),
            edit = @Edit(title = "供应商", readonly = @Readonly)
    )
    private String supplierName;

    @FabosJsonField(
            views = @View(title = "物料名称"),
            edit = @Edit(title = "物料名称", search = @Search, readonly = @Readonly)
    )
    private String materialName;

    @FabosJsonField(
            views = @View(title = "规格"),
            edit = @Edit(title = "规格", readonly = @Readonly)
    )
    private String materialSpecification;

    @FabosJsonField(
            views = @View(title = "单位"),
            edit = @Edit(title = "单位", readonly = @Readonly)
    )
    private String measureUnit;

    @FabosJsonField(
            views = @View(title = "品号"),
            edit = @Edit(title = "品号", readonly = @Readonly)
    )
    private String productNo;

    @FabosJsonField(
            views = @View(title = "返厂数量"),
            edit = @Edit(title = "返厂数量", readonly = @Readonly,
                    inputType = @InputType(length = 20), numberType = @NumberType(min = 0))
    )
    private Double returnQuantity;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "回厂日期", type = ViewType.DATE, show = false),
            edit = @Edit(title = "回厂日期",
                    dateType = @DateType(type = DateType.Type.DATE), show = false)
    )
    private Date backDate;

    @FabosJsonField(
            views = @View(title = "回厂数量"),
            edit = @Edit(title = "回厂数量", search = @Search(vague = true), notNull = true,
                    inputType = @InputType(length = 20), numberType = @NumberType(min = 0))
    )
    private Double backFactoryQuantity;

    @FabosJsonField(
            views = @View(title = "回厂登记", column = "generalCode", show = false),
            edit = @Edit(title = "回厂登记", type = EditType.REFERENCE_TABLE, show = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode")
            )
    )
    @ManyToOne
    @JsonIgnoreProperties("returnFactoryRegistrDetailViews")
    private ReturnFactoryRegistrView returnFactoryRegistrView;
}
