package cec.jiutian.bc.purchase.util;

import cec.jiutian.view.fun.TableFormParamsHandler;
import org.springframework.stereotype.Component;

import java.util.HashMap;

/**
 * TableForm物料处理类：用于view注解
 *
 * <AUTHOR>
 * @date 2025/1/13 15:18
 */
@Component
public class PmMaterialByIdFormHandler implements TableFormParamsHandler {
    @Override
    public TableFormParams handle() {
        return new TableFormParams("stockType", new HashMap<>() {{
            put("MATERIAL", new DependCondition("Material", "materialId"));
            put("PRODUCT", new DependCondition("Product", "materialId"));
        }});
    }
}