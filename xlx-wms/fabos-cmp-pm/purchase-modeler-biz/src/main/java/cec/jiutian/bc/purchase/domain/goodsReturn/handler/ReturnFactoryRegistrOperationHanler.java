package cec.jiutian.bc.purchase.domain.goodsReturn.handler;

import cec.jiutian.bc.purchase.domain.goodsReturn.model.GoodsReturn;
import cec.jiutian.bc.purchase.domain.goodsReturn.model.GoodsReturnDetail;
import cec.jiutian.bc.purchase.domain.goodsReturn.model.ReturnFactoryRegistrDetailView;
import cec.jiutian.bc.purchase.domain.goodsReturn.model.ReturnFactoryRegistrView;
import cec.jiutian.bc.purchase.domain.returnInventory.service.ReturnInventoryService;
import cec.jiutian.bc.purchase.enumeration.GoodsReturnStateEnum;
import cec.jiutian.bc.purchaseModeler.enumeration.ReturnInventoryCurrentStateEnum;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.common.util.BeanUtils;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.data.jpa.JpaCrud;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.date.DateTime;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class ReturnFactoryRegistrOperationHanler implements OperationHandler<GoodsReturn, ReturnFactoryRegistrView> {
    @Resource
    private ReturnInventoryService returnInventoryService;
    @Resource
    private FabosJsonDao fabosJsonDao;
    @Resource
    private JpaCrud jpaCrud;

    @Override
    public ReturnFactoryRegistrView fabosJsonFormValue(List<GoodsReturn> data, ReturnFactoryRegistrView fabosJsonForm, String[] param) {
        // 获取行数据
        List<GoodsReturnDetail> detailList = data.get(0).getGoodsReturnDetails();
        ArrayList<ReturnFactoryRegistrDetailView> returnFactoryRegistrDetailViews = new ArrayList<>();
        for (GoodsReturnDetail detail : detailList) {
            ReturnFactoryRegistrDetailView detailView = new ReturnFactoryRegistrDetailView();
            BeanUtils.copyProperties(detail, detailView);
            returnFactoryRegistrDetailViews.add(detailView);
        }
        BeanUtils.copyProperties(data.get(0), fabosJsonForm);
        fabosJsonForm.setReturnFactoryRegistrDetailViews(returnFactoryRegistrDetailViews);
        return fabosJsonForm;
    }

    @Override
    public String exec(List<GoodsReturn> data, ReturnFactoryRegistrView modelObject, String[] param) {
        // 修改当前主单据状态为 --> 已回厂
        GoodsReturn goodsReturn = data.get(0);
        goodsReturn.setProcessState(GoodsReturnStateEnum.Enum.Returned.name());
        // 则设置该单状态为"完成"
        goodsReturn.setCurrentState(OrderCurrentStateEnum.Enum.COMPLETE.name());
        goodsReturn.setAttachment(modelObject.getAttachment());
        jpaCrud.update(goodsReturn);
        List<ReturnFactoryRegistrDetailView> rfRegistrDetailList = modelObject.getReturnFactoryRegistrDetailViews();
        List<GoodsReturnDetail> goodsReturnDetails = goodsReturn.getGoodsReturnDetails();
        for (GoodsReturnDetail goodsReturnDetail : goodsReturnDetails) {
            // 修改关联的返厂台账的状态 --> 待入库
            if (StringUtils.isNotBlank(goodsReturnDetail.getReturnInventoryId())) {
                returnInventoryService.updateReturnInventoryState(goodsReturnDetail.getReturnInventoryId(),
                        ReturnInventoryCurrentStateEnum.Enum.AwaitStockIn.name(), null);
            }
            // 内部遍历返厂明细，赋值回厂数量
            for (ReturnFactoryRegistrDetailView detailView : rfRegistrDetailList) {
                if (detailView.getId().equals(goodsReturnDetail.getId())) {
                    goodsReturnDetail.setBackFactoryQuantity(detailView.getBackFactoryQuantity());
                    goodsReturnDetail.setBackDate(DateTime.now());
                }
            }
            jpaCrud.update(goodsReturnDetail);
        }
        return "success";
    }
}
