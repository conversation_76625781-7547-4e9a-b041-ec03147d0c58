package cec.jiutian.bc.purchase.domain.receivePlan.port.client;

import cec.jiutian.bc.purchaseModeler.enumeration.InspectTypeEnum;
import cec.jiutian.functionexecutor.consumer.DubboGenericAPI;
import cec.jiutian.functionexecutor.dto.InvokeDTO;
import com.alibaba.fastjson2.JSONObject;
import io.seata.spring.annotation.GlobalTransactional;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：2024/11/18 15:05
 * @description：
 */
@Component
public class InvokeIqcRpc {

    @Resource
    private DubboGenericAPI dubboGenericAPI;

    @GlobalTransactional
    public Object planToIqc(Map<String, Object> map) {
        InvokeDTO invokeDTO = new InvokeDTO();
        Object[] args = new Object[2];
        args[0] = InspectTypeEnum.Enum.ToSupplier.name();
        args[1] = JSONObject.toJSONString(map);
        invokeDTO.setArgs(args);
        invokeDTO.setGroup("fabos-cmp-ag");
        String[] argsType = {"java.lang.String", "java.lang.String"};
        invokeDTO.setArgsType(argsType);
        invokeDTO.setBizName("fabos-cmp-ag");
        invokeDTO.setClassFullName("cec.jiutian.bc.ag.remote.rpc.ArrivalGoodsRpc");
        invokeDTO.setMethodName("createIqc");
        invokeDTO.setModuleVersion("3.2.0");
        return dubboGenericAPI.doInvoke(invokeDTO);
    }

    @GlobalTransactional
    public Object adjustIqcQuantity(Map<String, Object> map) {
        InvokeDTO invokeDTO = new InvokeDTO();
        Object[] args = new Object[1];
        Map<String, Object> result = new HashMap<>();
        args[0] = JSONObject.toJSONString(map);
        invokeDTO.setArgs(args);
        invokeDTO.setGroup("fabos-cmp-ag");
        String[] argsType = {"java.lang.String"};
        invokeDTO.setArgsType(argsType);
        invokeDTO.setBizName("fabos-cmp-ag");
        invokeDTO.setClassFullName("cec.jiutian.bc.ag.remote.rpc.ArrivalGoodsRpc");
        invokeDTO.setMethodName("adjustIqcQuantity");
        invokeDTO.setModuleVersion("3.2.0");
        return dubboGenericAPI.doInvoke(invokeDTO);
    }
}
