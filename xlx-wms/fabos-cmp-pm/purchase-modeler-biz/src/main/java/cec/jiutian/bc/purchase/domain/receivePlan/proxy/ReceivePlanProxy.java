package cec.jiutian.bc.purchase.domain.receivePlan.proxy;

import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.purchase.domain.purchaseContract.model.PurchaseContractDetail;
import cec.jiutian.bc.purchase.domain.purchaseRequest.model.PurchaseRequestDetail;
import cec.jiutian.bc.purchase.domain.receivePlan.model.ReceivePlan;
import cec.jiutian.bc.purchaseModeler.enumeration.PlanBindingStatusEnum;
import cec.jiutian.bc.purchaseModeler.enumeration.PlanProcessStateEnum;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.core.frame.module.MetaUser;
import cec.jiutian.data.jpa.JpaCrud;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import jakarta.persistence.EntityManager;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @description:
 */
@Component
public class ReceivePlanProxy implements DataProxy<ReceivePlan> {

    @Resource
    private JpaCrud jpaCrud;

    @Resource
    private FabosJsonDao fabosJsonDao;
    @Resource
    private NamingRuleService namingRuleService;

    @Override
    public void beforeAdd(ReceivePlan receivePlan) {
        receivePlan.setCurrentState(OrderCurrentStateEnum.Enum.EDIT.name());
        receivePlan.setProcessState(PlanProcessStateEnum.Enum.Edit.name());
        setDetailData(receivePlan);
    }

    @Override
    public void beforeUpdate(ReceivePlan receivePlan) {
        if (!receivePlan.getCurrentState().equals(OrderCurrentStateEnum.Enum.EDIT.name())) {
            throw new ServiceException("非开立状态不允许编辑");
        }
        setDetailData(receivePlan);
    }

    @Override
    public void beforeDelete(ReceivePlan entity) {
        if (!entity.getCurrentState().equals(OrderCurrentStateEnum.Enum.EDIT.name())) {
            throw new ServiceException("非开立状态不允许删除");
        }
    }

    @Override
    public void afterDelete(ReceivePlan receivePlan) {
        if (!CollectionUtils.isEmpty(receivePlan.getReceivePlanDetails())) {
            receivePlan.getReceivePlanDetails().forEach(d -> {
                if (d.getPurchaseRequestDetail() != null) {
                    PurchaseRequestDetail requestDetail = jpaCrud.getById(PurchaseRequestDetail.class, d.getPurchaseRequestDetail().getId());
                    requestDetail.setBindingStatus(PlanBindingStatusEnum.Enum.unbind.name());
                    jpaCrud.update(requestDetail);
                }
                if (d.getPurchaseContractDetail() != null) {
                    PurchaseContractDetail contractDetail = jpaCrud.getById(PurchaseContractDetail.class, d.getPurchaseContractDetail().getId());
                    contractDetail.setBindingStatus(PlanBindingStatusEnum.Enum.unbind.name());
                    jpaCrud.update(contractDetail);
                }
            });
        }
    }

    @Override
    public void editBehavior(ReceivePlan receivePlan) {
        receivePlan.setReceivePerson(fabosJsonDao.findById(MetaUser.class, fabosJsonDao.findById(ReceivePlan.class, receivePlan.getId()).getReceivePerson().getId()));
        receivePlan.setReceivePlanDetails(fabosJsonDao.findById(ReceivePlan.class, receivePlan.getId()).getReceivePlanDetails());
    }

    private void setDetailData(ReceivePlan receivePlan) {
        if (!CollectionUtils.isEmpty(receivePlan.getReceivePlanDetails())) {
            EntityManager entityManager = fabosJsonDao.getEntityManager();
            entityManager.clear();
            ReceivePlan receivePlanDb = entityManager.find(ReceivePlan.class, receivePlan.getId());
            int indexNow = receivePlanDb.getReceivePlanDetails().size();
            AtomicInteger index = new AtomicInteger(indexNow + 1);
            receivePlan.getReceivePlanDetails().forEach(d -> {
                if (d.getReceiveQuantity() > d.getRemainingReceiveQuantity()) {
                    throw new FabosJsonApiErrorTip("收货数量不能大于可收货数量，请确认！");
                }
                if (d.getRemainingReceiveQuantity() == 0) {
                    PurchaseRequestDetail requestDetail = jpaCrud.getById(PurchaseRequestDetail.class, d.getPurchaseRequestDetail().getId());
                    if (requestDetail != null) {
                        requestDetail.setBindingStatus(PlanBindingStatusEnum.Enum.complete.name());
                        jpaCrud.update(requestDetail);
                    }
                    PurchaseContractDetail contractDetail = jpaCrud.getById(PurchaseContractDetail.class, d.getPurchaseContractDetail().getId());
                    if (contractDetail != null) {
                        contractDetail.setBindingStatus(PlanBindingStatusEnum.Enum.complete.name());
                        jpaCrud.update(contractDetail);
                    }
                }

                d.setReceivePlan(receivePlan);
                if (CollectionUtils.isNotEmpty(d.getMaterialLotList())) {
                    d.getMaterialLotList().forEach(l -> {
                        l.setToSupplierInspectMaterial(d);
                    });
                }
                d.setReceivePlanDetailCode(receivePlan.getGeneralCode() + "-" + String.format("%03d", index.get()));
                d.setCreateBy(receivePlan.getCreateBy());
                d.setCreateTime(LocalDateTime.now());
                d.setUpdateBy(receivePlan.getUpdateBy());
                d.setUpdateTime(LocalDateTime.now());
                index.getAndIncrement();
            });
        }
    }
}
