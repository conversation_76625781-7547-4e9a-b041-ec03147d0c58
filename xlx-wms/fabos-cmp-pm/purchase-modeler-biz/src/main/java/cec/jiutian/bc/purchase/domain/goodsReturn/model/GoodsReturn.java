package cec.jiutian.bc.purchase.domain.goodsReturn.model;

import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleModel;
import cec.jiutian.bc.generalModeler.domain.supplier.model.Supplier;
import cec.jiutian.bc.purchase.domain.goodsReturn.handler.GoodsReturnDetailDynamicHandler;
import cec.jiutian.bc.purchase.domain.goodsReturn.handler.ReturnFactoryRegistrOperationHanler;
import cec.jiutian.bc.purchase.domain.goodsReturn.proxy.GoodsReturnFlowProxy;
import cec.jiutian.bc.purchase.domain.goodsReturn.proxy.GoodsReturnProxy;
import cec.jiutian.bc.purchase.enumeration.GoodsReturnStateEnum;
import cec.jiutian.bc.purchase.enumeration.GoodsReturnTypeEnum;
import cec.jiutian.bc.purchaseModeler.enumeration.NamingRuleCodeEnum;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.ReferenceAddType;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.AttachmentType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DependFieldDisplay;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowBaseOperation;
import cec.jiutian.view.type.RowOperation;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OrderBy;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@FabosJson(
        name = "外购物资返厂",
        orderBy = "GoodsReturn.createTime desc",
        dataProxy = GoodsReturnProxy.class,
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "currentState != 'EDIT'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "currentState != 'EDIT'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "examine",
                        ifExpr = "examineStatus != '0'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "examineDetails",
                        ifExpr = "currentState == 'EDIT'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                )
        },
        rowOperation = {
                @RowOperation(
                        code = "GoodsReturn@RETURNFACTORYDISPOSAL",
                        operationHandler = ReturnFactoryRegistrOperationHanler.class,
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = ReturnFactoryRegistrView.class,
                        title = "回厂登记",
                        show = @ExprBool(
                                value = true,
                                params = "GoodsReturn@RETURNFACTORYDISPOSAL"
                        ),
                        ifExpr = "selectedItems[0].processState != 'Returning' || selectedItems[0].goodsReturnType != 'Repair'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                )
        },
        flowProxy = {GoodsReturnFlowProxy.class},
        flowCode = "GoodsReturn",
        power = @Power(examine = true, examineDetails = true)
)
@Table(name = "bwi_goods_return",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Getter
@Setter
@TemplateType(type = "multiTable")
public class GoodsReturn extends NamingRuleModel {
    @Override
    public String getNamingCode() {
        return NamingRuleCodeEnum.GoodsReturnCode.name();
    }

    @FabosJsonField(
            views = @View(title = "单据名称"),
            edit = @Edit(title = "单据名称", search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    private String orderName;

    @FabosJsonField(
            views = @View(title = "返厂类型"),
            edit = @Edit(title = "返厂类型", type = EditType.CHOICE,
                    notNull = true,
                    search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = GoodsReturnTypeEnum.class)
            )
    )
    private String goodsReturnType;

    @FabosJsonField(
            views = @View(title = "返厂原因"),
            edit = @Edit(title = "返厂原因", notNull = true)
    )
    private String returnReason;

    @FabosJsonField(
            views = @View(title = "返厂金额（单位：元）", type = ViewType.NUMBER),
            edit = @Edit(title = "返厂金额（单位：元）",
                    numberType = @NumberType(min = 0, max = 999999999, precision = 2))
    )
    private Double returnPrice;

    @FabosJsonField(
            views = @View(title = "处理状态"),
            edit = @Edit(title = "处理状态", type = EditType.CHOICE, show = false,
                    search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = GoodsReturnStateEnum.class)
            )
    )
    private String processState;

    @FabosJsonField(
            views = @View(title = "当前状态"),
            edit = @Edit(title = "当前状态", type = EditType.CHOICE, show = false,
                    search = @Search(),
                    choiceType = @ChoiceType(
                            fetchHandler = OrderCurrentStateEnum.class
                    )
            )
    )
    private String currentState;

    @FabosJsonField(
            views = @View(title = "供应商", column = "supplierName", type = ViewType.TABLE_FORM),
            edit = @Edit(title = "供应商",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "supplierName")
            )
    )
    @JoinColumn(name = "supplier_id")
    @ManyToOne
    private Supplier supplier;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "goods_return_detail_id")
    @OrderBy
    @FabosJsonField(
            edit = @Edit(title = "返厂明细", type = EditType.TAB_REFER_ADD,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "supplier != null")),
            views = @View(title = "返厂明细", type = ViewType.TABLE_VIEW, column = "goodsReturnDetails",
                    extraPK = "returnInventoryId"),
            referenceAddType = @ReferenceAddType(referenceClass = "ReturnInventory",
                    queryCondition = "{\"currentState\":[\"WaitReturn\"], \"supplierId\":\"${supplier.id}\"}",
                    referenceAddHandler = GoodsReturnDetailDynamicHandler.class)
    )
    private List<GoodsReturnDetail> goodsReturnDetails;

    @FabosJsonField(
            views = @View(title = "附件显示条件", show = false),
            edit = @Edit(title = "附件显示条件", show = false, defaultVal = "0")
    )
    private String attachmentShow;

    @FabosJsonField(
            views = @View(title = "回厂检验附件"),
            edit = @Edit(title = "回厂检验附件", type = EditType.ATTACHMENT, show = false,
                    attachmentType = @AttachmentType(maxLimit = 10))
    )
    private String attachment;

    @FabosJsonField(
            views = @View(title = "备注"),
            edit = @Edit(title = "备注", type = EditType.TEXTAREA)
    )
    private String remark;
}
