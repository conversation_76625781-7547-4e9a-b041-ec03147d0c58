package cec.jiutian.bc.purchase.domain.returnExchange.handler;

import cec.jiutian.bc.purchase.domain.returnExchange.model.RefundExchange;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.data.jpa.JpaCrud;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：2024/11/7 14:53
 * @description：
 */
@Component
public class RefundExchangeApprovalOperationHandlerImpl implements OperationHandler<RefundExchange, Void> {

    @Resource
    private JpaCrud jpaCrud;

    @Override
    public String exec(List<RefundExchange> data, Void modelObject, String[] param) {
        if (!CollectionUtils.isEmpty(data)) {
            data.forEach(d -> {
                if (!d.getExecuteState().equals(OrderCurrentStateEnum.Enum.EDIT.name())) {
                    throw new ServiceException("非开立状态不允许提交审批");
                }
                d.setExecuteState(OrderCurrentStateEnum.Enum.EXECUTE.name());
            });
            jpaCrud.updateBatchById(data);
        }
        return null;
    }
}
