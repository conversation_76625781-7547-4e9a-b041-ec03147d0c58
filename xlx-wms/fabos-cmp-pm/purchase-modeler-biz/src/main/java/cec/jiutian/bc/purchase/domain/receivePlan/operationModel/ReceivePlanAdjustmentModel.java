package cec.jiutian.bc.purchase.domain.receivePlan.operationModel;

import cec.jiutian.bc.generalModeler.domain.supplier.model.Supplier;
import cec.jiutian.bc.purchaseModeler.enumeration.InspectTypeEnum;
import cec.jiutian.bc.purchaseModeler.enumeration.PlanProcessStateEnum;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.frame.module.MetaUser;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.AttachmentType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DateType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.CascadeType;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OrderBy;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/6
 * @description TODO
 */
@FabosJson(
        name = "收货计划紧急调整处理模型"
)
@Getter
@Setter
public class ReceivePlanAdjustmentModel {
    @Id
    @FabosJsonField(
            edit = @Edit(title = "", show = false)
    )
    private String id;

    @FabosJsonField(
            views = @View(title = "计划编号"),
            edit = @Edit(title = "计划编号", notNull = true, search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "计划名称"),
            edit = @Edit(title = "计划名称", search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    private String receiveName;

    @FabosJsonField(
            views = @View(title = "创建依据", show = false),
            edit = @Edit(title = "创建依据", show = false)
    )
    private String createBasis;

    @FabosJsonField(
            views = @View(title = "供应商", column = "supplierName"),
            edit = @Edit(title = "供应商",
                    readonly = @Readonly(add = false),
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "supplierName")
                    //filter = @Filter(value = "Supplier.status = '1'")
            )
    )
    @ManyToOne
    private Supplier supplier;

    @FabosJsonField(
            views = @View(title = "检验方式"),
            edit = @Edit(title = "检验方式", type = EditType.CHOICE, search = @Search(vague = true),
                    choiceType = @ChoiceType(fetchHandler = InspectTypeEnum.class))
    )
    private String inspectType;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "预收货日期", type = ViewType.DATE),
            edit = @Edit(title = "预收货日期", notNull = true,
                    type = EditType.DATE, dateType = @DateType(type = DateType.Type.DATE, pickerMode = DateType.PickerMode.ALL))
    )
    private Date preReceivedDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @FabosJsonField(
            views = @View(title = "收货人", column = "name"),
            edit = @Edit(title = "收货人",
                    filter = @Filter(value = "MetaUser.state = 'Y'"),
                    notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name", type = ReferenceTableType.SelectShowTypeMTO.DROPDOWN_TABLE))
    )
    private MetaUser receivePerson;

    @FabosJsonField(
            views = @View(title = "收货地址"),
            edit = @Edit(title = "收货地址", notNull = true)
    )
    private String receiveAddress;
    @FabosJsonField(
            views = @View(title = "联系人"),
            edit = @Edit(title = "联系人", notNull = true)
    )
    private String contactPerson;
    @FabosJsonField(
            views = @View(title = "联系人电话"),
            edit = @Edit(title = "联系人电话", notNull = true, inputType = @InputType(length = 20, regex = "^1[0-9]\\d{9}$"))
    )
    private String contactTel;

    @FabosJsonField(
            views = @View(title = "附件"),
            edit = @Edit(title = "附件", type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType)
    )
    private String receivePlanDocument;

    @FabosJsonField(
            views = @View(title = "备注"),
            edit = @Edit(title = "备注",
                    type = EditType.TEXTAREA)
    )
    private String remark;

    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumn(name = "receive_plan_id")
    @OrderBy
    @FabosJsonField(
            edit = @Edit(title = "计划明细", type = EditType.TAB_TABLE_ADD, readonly = @Readonly(add = false)),
            views = @View(title = "计划明细", type = ViewType.TABLE_VIEW, extraPK = "extraModelId")
    )
    private List<ReceivePlanDetailAdjustmentModel> receivePlanDetails;

    @FabosJsonField(
            views = @View(title = "当前状态"),
            edit = @Edit(title = "当前状态", type = EditType.CHOICE, show = false,
                    search = @Search(),
                    choiceType = @ChoiceType(
                            fetchHandler = OrderCurrentStateEnum.class
                    )
            )
    )
    private String currentState;

    @FabosJsonField(
            views = @View(title = "处理状态"),
            edit = @Edit(title = "处理状态", type = EditType.CHOICE, show = false,
                    search = @Search(),
                    choiceType = @ChoiceType(
                            fetchHandler = PlanProcessStateEnum.class
                    )
            )
    )
    private String processState;
}
