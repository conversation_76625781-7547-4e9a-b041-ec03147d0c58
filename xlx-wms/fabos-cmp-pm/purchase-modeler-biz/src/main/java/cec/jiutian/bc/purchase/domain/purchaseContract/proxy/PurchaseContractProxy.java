package cec.jiutian.bc.purchase.domain.purchaseContract.proxy;

import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.generalModeler.util.StringUtils;
import cec.jiutian.bc.purchase.domain.purchaseContract.model.PurchaseContract;
import cec.jiutian.bc.purchase.domain.purchaseContract.model.PurchaseContractDetail;
import cec.jiutian.bc.purchase.domain.purchaseRequest.model.PurchaseRequest;
import cec.jiutian.bc.purchase.domain.purchaseRequest.model.PurchaseRequestDetail;
import cec.jiutian.bc.purchaseModeler.enumeration.OrderSourceEnum;
import cec.jiutian.bc.purchaseModeler.enumeration.PlanBindingStatusEnum;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.data.jpa.JpaCrud;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date ：2024/11/5 18:00
 * @description：
 */
@Component
public class PurchaseContractProxy implements DataProxy<PurchaseContract> {

    @Resource
    private PurchaseContractDetailProxy detailProxy;
    @Resource
    private NamingRuleService namingRuleService;

    @Resource
    private JpaCrud jpaCrud;

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public void beforeAdd(PurchaseContract purchaseContract) {
        PurchaseContract condition = new PurchaseContract();
        condition.setPurchaseRequest(purchaseContract.getPurchaseRequest());
        List<PurchaseContract> contracts = jpaCrud.select(condition);
        if (CollectionUtils.isNotEmpty(contracts)) {
            throw new FabosJsonApiErrorTip("该采购申请已创建采购合同，请确认");
        }
        if (purchaseContract.getPurchaseRequest() != null) {
            PurchaseRequest purchaseRequest = jpaCrud.getById(PurchaseRequest.class, purchaseContract.getPurchaseRequest().getId());
            if (CollectionUtils.isNotEmpty(purchaseRequest.getDetailList())) {
                purchaseRequest.getDetailList().forEach(d -> {
                    d.setSignedContract("Y");
                });
                jpaCrud.updateBatchById(purchaseRequest.getDetailList());
            }
        }
        purchaseContract.setCurrentState(OrderCurrentStateEnum.Enum.EDIT.name());
        // 设置单据来源, 非同步类型的, 一律为自建
        String SYNCHRONIZED_SOURCE = OrderSourceEnum.Enum.SYNCHRONIZED.name();
        if (!SYNCHRONIZED_SOURCE.equals(purchaseContract.getSource())) {
            purchaseContract.setSource(OrderSourceEnum.Enum.SELF_BUILD.name());
        }
        this.handlerDetail(purchaseContract);
        updateBindingStatusByRequest(purchaseContract);
    }

    @Override
    public void beforeUpdate(PurchaseContract purchaseContract) {
        if (!purchaseContract.getCurrentState().equals(OrderCurrentStateEnum.Enum.EDIT.name())) {
            throw new ServiceException("非开立状态不允许编辑");
        }
        this.handlerDetail(purchaseContract);
    }

    @Override
    public void afterUpdate(PurchaseContract purchaseContract) {
        String sql = "delete from bwi_purchase_contract_detail where PURCHASE_CONTRACT_ID is null";
        fabosJsonDao.getJdbcTemplate().execute(sql);
        if (CollectionUtils.isNotEmpty(purchaseContract.getPurchaseContractDetailList())) {
            PurchaseRequest purchaseRequest = jpaCrud.getById(PurchaseRequest.class, purchaseContract.getPurchaseRequest().getId());
            if (CollectionUtils.isNotEmpty(purchaseRequest.getDetailList())) {
                purchaseRequest.getDetailList().forEach(d -> {
                    if (!"Y".equals(d.getSignedContract())) {
                        d.setSignedContract("Y");
                    }
                });
                jpaCrud.updateBatchById(purchaseRequest.getDetailList());
            }
            updateBindingStatusByRequest(purchaseContract);
            purchaseContract.getPurchaseContractDetailList().forEach(d -> fabosJsonDao.mergeAndFlush(d));
            //jpaCrud.update(purchaseContract.getPurchaseContractDetailList());
        }
    }

    private void updateBindingStatusByRequest(PurchaseContract purchaseContract) {
        if (CollectionUtils.isNotEmpty(purchaseContract.getPurchaseContractDetailList())) {
            purchaseContract.getPurchaseContractDetailList().forEach(d -> {
                if (StringUtils.isNotEmpty(d.getPurchaseRequestDetailCode())) {
                    PurchaseRequestDetail condition = new PurchaseRequestDetail();
                    condition.setPurchaseRequestDetailCode(d.getPurchaseRequestDetailCode());
                    PurchaseRequestDetail requestDetail = jpaCrud.selectOne(condition);
                    d.setBindingStatus(requestDetail.getBindingStatus());
                    if (!PlanBindingStatusEnum.Enum.unbind.name().equals(requestDetail.getBindingStatus())) {
                        d.setRequestBindingStatus("Y");
                    }
                }
            });
        }
    }

    /**
     * 处理明细数据
     *
     * @param purchaseContract 物资采购合同
     * <AUTHOR>
     * @date 2024/12/4 16:56
     */
    private void handlerDetail(PurchaseContract purchaseContract) {
        List<PurchaseContractDetail> detailList = purchaseContract.getPurchaseContractDetailList();
        if (CollectionUtils.isNotEmpty(detailList)) {
            String generalCode = purchaseContract.getGeneralCode();
            AtomicInteger index = new AtomicInteger(1);
            detailList.forEach(d -> {
                if (StringUtils.isEmpty(d.getId())) {
                    d.setBindingStatus(PlanBindingStatusEnum.Enum.unbind.name());
                }
                // 计算小计金额
                double totalPrice = this.calculateTotalPrice(d.getQuantity(), d.getPrice());
                d.setTotalPrice(totalPrice);
                // 设置合同明细编号
                d.setPurchaseContractDetailCode(generalCode + "-" + String.format("%03d", index.get()));
                index.getAndIncrement();
            });
        }
    }

    /**
     * 计算小计
     *
     * @param quantity 数量
     * @param price    单价
     * @return 小计
     * <AUTHOR>
     * @date 2024/12/4 16:04
     */
    private double calculateTotalPrice(Double quantity, Double price) {
        if (price == null || price == 0.0) {
            return 0.0;
        }
        BigDecimal tempQuantity = BigDecimal.valueOf(quantity);
        BigDecimal priceBD = BigDecimal.valueOf(price);
        // 计算结果保留2位小数
        return tempQuantity.multiply(priceBD).setScale(2, RoundingMode.HALF_UP).doubleValue();
    }

    @Override
    public void beforeDelete(PurchaseContract entity) {
        if (!entity.getCurrentState().equals(OrderCurrentStateEnum.Enum.EDIT.name())) {
            throw new ServiceException("非开立状态不允许删除");
        }
    }

}
