package cec.jiutian.bc.purchase.domain.purchasePlan.proxy;

import cec.jiutian.bc.flow.enums.ExamineStatusEnum;
import cec.jiutian.bc.purchase.domain.purchasePlan.model.PurchasePlan;
import cec.jiutian.bc.purchase.domain.purchasePlan.model.PurchasePlanDetail;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.data.jpa.JpaCrud;
import cec.jiutian.view.fun.FlowProxy;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class PurchasePlanFlowProxy extends FlowProxy {
    @Resource
    private JpaCrud jpaCrud;

    @Override
    public void onEvent(Object event, Object entity) {
        if (entity instanceof PurchasePlan purchasePlan) {
            String examineStatus = purchasePlan.getExamineStatus();
            // 如果审批通过, 则设置该单状态为"执行中"
            if (ExamineStatusEnum.AUDITED.getCode().equals(examineStatus)
                    || ExamineStatusEnum.AUDITING.getCode().equals(examineStatus)) {
                purchasePlan.setCurrentState(OrderCurrentStateEnum.Enum.EXECUTE.name());
                // 如果审批驳回, 则设置该单状态为"中止"
            } else if (ExamineStatusEnum.REJECTED.getCode().equals(examineStatus)) {
                purchasePlan.setCurrentState(OrderCurrentStateEnum.Enum.END.name());
            }
            jpaCrud.update(purchasePlan);
        }
        log.info("采购计划审批回调成功");
    }

    @Override
    public void beforeSubmit(Object entity) {
        if (entity instanceof PurchasePlan purchasePlan) {
            List<PurchasePlanDetail> detailList = purchasePlan.getPurchasePlanDetails();
            if (CollectionUtils.isEmpty(detailList)) {
                throw new ServiceException("采购计划缺失明细信息，无法提交审核");
            }
        }
    }

    @Override
    public void afterSubmit(Object entity) {
        if (entity instanceof PurchasePlan purchasePlan) {
            purchasePlan.setCurrentState(OrderCurrentStateEnum.Enum.EXECUTE.name());
            jpaCrud.update(purchasePlan);
        }
    }
}
