package cec.jiutian.bc.purchase.domain.receivePlan.handler;

import cec.jiutian.bc.flow.enums.ExamineStatusEnum;
import cec.jiutian.bc.purchase.domain.purchaseContract.model.PurchaseContractDetail;
import cec.jiutian.bc.purchase.domain.purchaseRequest.model.PurchaseRequestDetail;
import cec.jiutian.bc.purchase.domain.receivePlan.model.ReceivePlanDetail;
import cec.jiutian.bc.purchase.domain.receivePlan.model.ReceivePlanDetailWithContract;
import cec.jiutian.bc.purchase.domain.receivePlan.model.ReceivePlanWithContract;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.data.jpa.JpaCrud;
import cec.jiutian.view.ReferenceAddType;
import jakarta.persistence.TypedQuery;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/12/2
 * @description TODO
 */
@Component
public class CreateByContractReferenceAddHandler implements ReferenceAddType.ReferenceAddHandler<ReceivePlanWithContract, PurchaseContractDetail> {

    @Resource
    private JpaCrud jpaCrud;

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public Map<String, Object> handle(ReceivePlanWithContract receivePlanWithContract, List<PurchaseContractDetail> purchaseContractDetails) {
        Map<String, Object> result = new HashMap<>();
        List<ReceivePlanDetailWithContract> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(purchaseContractDetails)) {
            purchaseContractDetails.forEach(d -> {
                PurchaseContractDetail contractDetail = jpaCrud.getById(PurchaseContractDetail.class, d.getId());
                PurchaseRequestDetail requestCondition = new PurchaseRequestDetail();
                requestCondition.setPurchaseRequestDetailCode(contractDetail.getPurchaseRequestDetailCode());
                PurchaseRequestDetail purchaseRequestDetail = jpaCrud.selectOne(requestCondition);

                String hql = "from ReceivePlanDetail mt where mt.receivePlan.createBasis = :createBasis and mt.receivePlan.examineStatus != :examineStatus and mt.purchaseRequestDetail.id = :purchaseRequestDetailId";
                TypedQuery<ReceivePlanDetail> query = fabosJsonDao.getEntityManager().createQuery(hql, ReceivePlanDetail.class);
                query.setParameter("createBasis", "request");
                query.setParameter("purchaseRequestDetailId", purchaseRequestDetail.getId());
                query.setParameter("examineStatus", ExamineStatusEnum.REJECTED.getCode());
                List<ReceivePlanDetail> requestPlanDetails = query.getResultList();
                double totalReceiveRequestQuantity = 0.0;
                if (CollectionUtils.isNotEmpty(requestPlanDetails)) {
                    totalReceiveRequestQuantity = requestPlanDetails.stream().mapToDouble(req -> req.getReceiveQuantity() == null ? 0.0 : req.getReceiveQuantity()).sum() -
                            requestPlanDetails.stream().mapToDouble(ret -> ret.getReturnQuantity() == null ? 0.0 : ret.getReturnQuantity()).sum();
                }

                String contractHql = "from ReceivePlanDetail mt where mt.receivePlan.examineStatus != :examineStatus and mt.purchaseContractDetail.id = :purchaseContractDetailId";
                TypedQuery<ReceivePlanDetail> contractQuery = fabosJsonDao.getEntityManager().createQuery(contractHql, ReceivePlanDetail.class);
                contractQuery.setParameter("purchaseContractDetailId", d.getId());
                contractQuery.setParameter("examineStatus", ExamineStatusEnum.REJECTED.getCode());
                List<ReceivePlanDetail> existPlanDetails = contractQuery.getResultList();
                Double totalReceiveQuantity = (existPlanDetails.stream().mapToDouble(con -> con.getReceiveQuantity() == null ? 0.0 : con.getReceiveQuantity()).sum() -
                        existPlanDetails.stream().mapToDouble(ret -> ret.getReturnQuantity() == null ? 0.0 : ret.getReturnQuantity()).sum())
                        + totalReceiveRequestQuantity;

                ReceivePlanDetailWithContract planDetail = new ReceivePlanDetailWithContract();
                planDetail.setExtraModelId(d.getId());
                planDetail.setPurchaseRequestCode(purchaseRequestDetail.getPurchaseRequest().getGeneralCode());
                planDetail.setPurchaseRequestDetail(purchaseRequestDetail);
                planDetail.setPurchaseContractCode(contractDetail.getPurchaseContract().getGeneralCode());
                planDetail.setPurchaseContractDetail(contractDetail);
                planDetail.setMaterialCode(d.getMaterialCode());
                planDetail.setMaterialName(d.getMaterialName());
                planDetail.setMaterialSpecification(d.getMaterialSpecification());
                planDetail.setArticleNumber(d.getMaterialCode());
                planDetail.setWl09("test");
                planDetail.setPrice(d.getPrice());
                planDetail.setPurpose(d.getMaterialProduct());
                planDetail.setRequestQuantity(purchaseRequestDetail.getApprovedQuantity());
                planDetail.setContractQuantity(d.getQuantity());
                planDetail.setRemainingReceiveQuantity(Math.max((d.getQuantity() - totalReceiveQuantity), 0.0));
                planDetail.setMeasureUnit(d.getMeasureUnit());
                planDetail.setPurpose(d.getMaterialProduct());
                // 设置类别和子类别
                planDetail.setStockType(d.getStockType());
                planDetail.setStockSubType(d.getStockSubType());
                // 设置物料主键id
                planDetail.setMaterialId(d.getMaterialId());
                list.add(planDetail);
            });
            result.put("receivePlanWithContractDetails", list);
        }
        return result;
    }
}
