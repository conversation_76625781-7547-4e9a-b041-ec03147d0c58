package cec.jiutian.bc.ag.domain.productRefund.handler;

import cec.jiutian.bc.ag.domain.productRefund.model.ProductCheckDetail;
import cec.jiutian.view.DependFiled;
import org.springframework.stereotype.Component;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025年03月12日 14:44
 */
@Component
public class MaterialChangeProductCheckDetailHandler implements DependFiled.DynamicHandler<ProductCheckDetail> {
    @Override
    public Map<String, Object> handle(ProductCheckDetail productCheckDetail) {
        Map<String, Object> result = new HashMap<>();
        if(productCheckDetail != null && productCheckDetail.getMaterial() != null) {
            result.put("materialCode", productCheckDetail.getMaterial().getMaterialCode());
            result.put("materialName", productCheckDetail.getMaterial().getName());
            result.put("materialSpecification", productCheckDetail.getMaterial().getMaterialSpecification());
            result.put("measureUnit", productCheckDetail.getMaterial().getAccountUnit());
        }
        return result;
    }
}
