package cec.jiutian.bc.ag.domain.testNotification.handler;

import cec.jiutian.bc.ag.domain.overDullOrDullInventoryWarning.OverDullOrDullInventoryWarning;
import cec.jiutian.bc.ag.domain.testNotification.model.TestNotification;
import cec.jiutian.bc.ag.domain.testNotification.model.TestNotificationDetail;
import cec.jiutian.bc.generalModeler.domain.measureUnit.model.MeasureUnit;
import cec.jiutian.bc.generalModeler.enumeration.YesOrNoEnum;
import cec.jiutian.common.util.ListUtils;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.ReferenceAddType;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025年03月12日 14:44
 */
@Component
public class OverDullOrDullInventoryWarningChangeInTestNotificationHandler implements ReferenceAddType.ReferenceAddHandler<TestNotification, OverDullOrDullInventoryWarning> {

    @Resource
    FabosJsonDao fabosJsonDao;

    @Override
    public Map<String, Object> handle(TestNotification testNotification, List<OverDullOrDullInventoryWarning> overDullOrDullInventoryWarningList) {
        Map<String, Object> result = new HashMap<>();
        if(CollectionUtils.isNotEmpty(overDullOrDullInventoryWarningList)) {
            AtomicInteger index = new AtomicInteger(1);
            List<TestNotificationDetail> testNotificationDetailList = overDullOrDullInventoryWarningList.stream().map(item -> {
                MeasureUnit measureUnit = null;
                if(StringUtils.isNotEmpty(item.getAccountingUnit())) {
                    measureUnit = fabosJsonDao.findById(MeasureUnit.class, item.getAccountingUnit());
                }
                TestNotificationDetail testNotificationDetail = TestNotificationDetail.builder()
                        .materialSpecification(item.getMaterialSpecification())
                        .materialName(item.getMaterialName())
                        .materialCode(item.getMaterialCode())
                        .storeCode(item.getMaterialCode())
                        .materialSpecification(item.getMaterialSpecification())
                        .quantity(item.getAvailableQuantity())
                        .arrivalQuantity(item.getAvailableQuantity())
                        .factoryLotIdentifier(item.getLotSerialId())
                        .accountUnit(measureUnit)
                        .coaFlag(YesOrNoEnum.Enum.N.name())
                        .sampleFlag(YesOrNoEnum.Enum.N.name())
                        .build();
                List<TestNotificationDetail> tmpList = fabosJsonDao.select(testNotificationDetail);
                ListUtils.forEach(tmpList, tmp -> {
                    if(tmp.getTestNotification() != null && YesOrNoEnum.Enum.N.name().equals(tmp.getTestNotification().getSynFlag())) {
                        throw new FabosJsonApiErrorTip(String.format("第%d条库存预警已经存在于未同步的检验通知单了, 不能重复创建, 请检查!", index.get()));
                    }
                });
                index.getAndIncrement();
                return testNotificationDetail;
            }).collect(Collectors.toList());
            result.put("testNotificationDetailList", testNotificationDetailList);
        }
        return result;
    }
}
