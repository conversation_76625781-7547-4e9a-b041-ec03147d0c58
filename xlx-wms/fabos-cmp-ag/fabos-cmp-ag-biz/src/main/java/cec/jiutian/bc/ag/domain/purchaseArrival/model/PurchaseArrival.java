package cec.jiutian.bc.ag.domain.purchaseArrival.model;

import cec.jiutian.bc.ag.domain.purchaseArrival.handler.PurchaseArrivalReleaseHandler;
import cec.jiutian.bc.ag.domain.purchaseArrival.proxy.PurchaseArrivalProxy;
import cec.jiutian.bc.ag.enumeration.NamingRuleCodeEnum;
import cec.jiutian.bc.ag.enumeration.PurchaseArrivalProcessStateEnum;
import cec.jiutian.bc.ag.enumeration.PurchaseTypeEnum;
import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleNoExamineModel;
import cec.jiutian.bc.generalModeler.domain.supplier.model.Supplier;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DateType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowBaseOperation;
import cec.jiutian.view.type.RowOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年03月03日 14:47
 */
@FabosJson(
        name = "采购到货",
        orderBy = "PurchaseArrival.createTime desc",
        power = @Power(importable = false, export = false, add = true, print = true),
        dataProxy = PurchaseArrivalProxy.class,
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "currentState!='EDIT'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "currentState!='EDIT'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                )
        },
        rowOperation = {
                @RowOperation(
                        title = "发布",
                        code = "PurchaseArrival@RELEASE",
                        mode = RowOperation.Mode.SINGLE,
                        operationHandler = PurchaseArrivalReleaseHandler.class,
                        ifExpr = "currentState!='EDIT'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
                        callHint = "是否确定操作？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "PurchaseArrivalReleaseHandler@RELEASE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                )
        }
)
@Table(name = "bwi_purchase_arrival",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"id"}),
                @UniqueConstraint(columnNames = {"arrivalRegistrationNumber"})
        }
)
@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TemplateType(type = "multiTable")
public class PurchaseArrival extends NamingRuleNoExamineModel {

    @Override
    public String getNamingCode() {
        return NamingRuleCodeEnum.PurchaseArrival.name();
    }

    @FabosJsonField(
            views = @View(title = "单据状态"),
            edit = @Edit(title = "单据状态", type = EditType.CHOICE, show = false,
                    search = @Search(),
                    choiceType = @ChoiceType(
                            fetchHandler = OrderCurrentStateEnum.class
                    )
            )
    )
    private String currentState;

    @FabosJsonField(
            views = @View(title = "单据处理状态"),
            edit = @Edit(title = "单据处理状态", type = EditType.CHOICE, show = false,
                    search = @Search(),
                    choiceType = @ChoiceType(
                            fetchHandler = PurchaseArrivalProcessStateEnum.class
                    )
            )
    )
    private String processState;

    @FabosJsonField(
            views = @View(title = "采购类型"),
            edit = @Edit(title = "采购类型", type = EditType.CHOICE, show = false,
                    search = @Search(),
                    choiceType = @ChoiceType(
                            fetchHandler = PurchaseTypeEnum.class
                    )
            )
    )
    private String purchaseType;

    @FabosJsonField(
            views = @View(title = "供应商", column = "supplierName", type = ViewType.TABLE_FORM),
            edit = @Edit(title = "供应商",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "supplierName"),
                    filter = @Filter(value = "Supplier.status = '1'"),
                    allowAddMultipleRows = false
            )
    )
    @ManyToOne
    @JoinColumn(name = "supplier_id")
    private Supplier supplier;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "到货日期", type = ViewType.DATE),
            edit = @Edit(title = "到货日期", notNull = true, search = @Search(vague = true),
                    type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE))
    )
    private Date arrivalDate;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.EAGER)
    @JoinColumn(name = "purchase_arrival_id")
    @FabosJsonField(
            views = @View(title = "采购到货明细", type = ViewType.TABLE_VIEW, show = true),
            edit = @Edit(title = "采购到货明细", type = EditType.TAB_TABLE_ADD)
    )
    private List<PurchaseArrivalDetail> purchaseArrivalDetailList;
}
