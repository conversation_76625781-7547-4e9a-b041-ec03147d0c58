package cec.jiutian.bc.ag.domain.iqc.model;

import cec.jiutian.bc.ag.domain.iqc.proxy.OutSourceArrivalFactoryIqcProxy;
import cec.jiutian.bc.ag.enumeration.MaterialSourceTypeEnum;
import cec.jiutian.bc.ag.port.dto.OutSourceArrivalRegistrationMaterialLotDTO;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.config.Comment;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

@FabosJson(
        name = "到厂请验",
        dataProxy = OutSourceArrivalFactoryIqcProxy.class,
        orderBy = "OutSourceArrivalFactoryIqc.createTime desc"

)
@Table(name = "bwi_iqc_request",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"number"})
        }
)
@Entity
@Getter
@Setter
public class OutSourceArrivalFactoryIqc extends MetaModel {

    @FabosJsonField(
            views = @View(title = "请验单号"),
            edit = @Edit(title = "请验单号", show = false
            )
    )
    private String number;

    @FabosJsonField(
            views = @View(title = "检验方式"),
            edit = @Edit(title = "检验方式", show = false)
    )
    private String inspectType;

    @FabosJsonField(
            views = @View(title = "物料来源"),
            edit = @Edit(title = "检验来源", show = false, readonly = @Readonly, type = EditType.CHOICE, search = @Search(vague = true),
                    choiceType = @ChoiceType(fetchHandler = MaterialSourceTypeEnum.class))
    )
    private String materialSourceType;

    @FabosJsonField(
            views = @View(title = "到厂检验物料", column = "materialCode"),
            edit = @Edit(title = "到厂检验物料",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "materialCode")
            )
    )
    @ManyToOne
    @Transient
    private OutSourceArrivalRegistrationMaterialLotDTO arrivalRegistrationMaterialLotDTO;

    @FabosJsonField(
            views = @View(title = "到厂单号"),
            edit = @Edit(title = "到厂单号", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "arrivalRegistrationMaterialLotDTO", beFilledBy = "arrivalRegistrationNumber"))

    )
    private String arrivalRegistrationNumber;

    @FabosJsonField(
            views = @View(title = "物料名称"),
            edit = @Edit(title = "物料名称", readonly = @Readonly()),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "arrivalRegistrationMaterialLotDTO", beFilledBy = "materialName"))

    )
    private String materialName;

    @FabosJsonField(
            views = @View(title = "物料编码"),
            edit = @Edit(title = "物料编码", readonly = @Readonly()),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "arrivalRegistrationMaterialLotDTO", beFilledBy = "materialCode"))
    )
    private String materialCode;

    @FabosJsonField(
            views = @View(title = "品号"),
            edit = @Edit(title = "品号", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "arrivalRegistrationMaterialLotDTO", beFilledBy = "articleNumber"))
    )
    private String articleNumber;

    @FabosJsonField(
            views = @View(title = "规格"),
            edit = @Edit(title = "规格", readonly = @Readonly()),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "arrivalRegistrationMaterialLotDTO", beFilledBy = "materialSpecification"))
    )
    private String materialSpecification;

    @FabosJsonField(
            views = @View(title = "请验数量"),
            edit = @Edit(title = "请验数量", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "arrivalRegistrationMaterialLotDTO", beFilledBy = "lotQuantity"))
    )
    private Double quantity;

    @FabosJsonField(
            views = @View(title = "已收货数量"),
            edit = @Edit(title = "已收货数量", show = false)
    )
    private Double receivedQuantity;

    @FabosJsonField(
            views = @View(title = "单位"),
            edit = @Edit(title = "单位", readonly = @Readonly()),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "arrivalRegistrationMaterialLotDTO", beFilledBy = "measureUnit"))
    )
    private String measureUnit;

    @FabosJsonField(
            views = @View(title = "供应商"),
            edit = @Edit(title = "供应商", readonly = @Readonly()),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "arrivalRegistrationMaterialLotDTO", beFilledBy = "supplierName"))
    )
    @Comment("供应商")
    private String supplier;

    @FabosJsonField(
            views = @View(title = "本厂批号"),
            edit = @Edit(title = "本厂批号", readonly = @Readonly()),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "arrivalRegistrationMaterialLotDTO", beFilledBy = "serialLotId"))
    )
    private String serialLotId;

    @FabosJsonField(
            views = @View(title = "原炉/原厂批号"),
            edit = @Edit(title = "原炉/原厂批号", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "arrivalRegistrationMaterialLotDTO", beFilledBy = "originLotId"))
    )
    private String originLotId;

    @FabosJsonField(
            views = @View(title = "原厂合格证"),
            edit = @Edit(title = "原厂合格证", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "arrivalRegistrationMaterialLotDTO", beFilledBy = "originCertificate"))
    )
    private String originCertificate;


    @FabosJsonField(
            views = @View(title = "备注"),
            edit = @Edit(title = "备注", type = EditType.TEXTAREA)
    )
    private String remark;

    @FabosJsonField(
            views = @View(title = "单据状态"),
            edit = @Edit(title = "单据状态", show = false
            )
    )
    private String currentState;

}
