package cec.jiutian.bc.ag.enumeration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：2024/11/7 14:09
 * @description：
 */
public class InspectTypeEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum data : Enum.values()) {
            list.add(new VLModel(data.name(), data.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        ToSupplier("下厂检验"),
        ArriveFactory("进厂检验"),
        TransferCertificate("转合格证"),
        SiteConfirm("现场确认");

        private final String value;

    }
}
