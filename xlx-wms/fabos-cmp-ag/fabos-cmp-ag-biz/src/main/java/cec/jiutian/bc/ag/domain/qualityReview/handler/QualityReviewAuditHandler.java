package cec.jiutian.bc.ag.domain.qualityReview.handler;

import cec.jiutian.bc.ag.domain.qualityReview.model.QualityReview;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.data.jpa.JpaCrud;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Component
public class QualityReviewAuditHandler implements OperationHandler<QualityReview, Void> {

    @Resource
    private JpaCrud jpaCrud;

    @Override
    public String exec(List<QualityReview> data, Void modelObject, String[] param) {
        if (!CollectionUtils.isEmpty(data)) {
            data.forEach(d -> {
                if (!d.getCurrentState().equals(OrderCurrentStateEnum.Enum.EDIT.name())) {
                    throw new ServiceException("非开立状态不允许提交审批");
                }
                d.setCurrentState(OrderCurrentStateEnum.Enum.EXECUTE.name());
            });
            jpaCrud.updateBatchById(data);
        }
        return null;
    }
}
