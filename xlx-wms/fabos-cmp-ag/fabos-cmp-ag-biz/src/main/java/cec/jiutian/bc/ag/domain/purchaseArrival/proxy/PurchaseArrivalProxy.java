package cec.jiutian.bc.ag.domain.purchaseArrival.proxy;

import cec.jiutian.bc.ag.domain.purchaseArrival.model.PurchaseArrival;
import cec.jiutian.bc.ag.domain.purchaseArrival.model.ThirdPurchaseArrivalAutoBoxedDetail;
import cec.jiutian.bc.ag.enumeration.PurchaseTypeEnum;
import cec.jiutian.bc.ag.util.BoxCalculator;
import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.common.util.ListUtils;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import java.math.BigDecimal;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025年03月05日 16:47
 */
@Component
public class PurchaseArrivalProxy implements DataProxy<PurchaseArrival> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private NamingRuleService namingRuleService;

    @Override
    public void beforeAdd(PurchaseArrival purchaseArrival) {
        if(purchaseArrival != null) {
            PurchaseArrival condition = PurchaseArrival.builder()
                    .supplier(purchaseArrival.getSupplier())
                    .build();
            condition.setGeneralCode(purchaseArrival.getGeneralCode());
            PurchaseArrival purchaseArrivalTmp = fabosJsonDao.selectOne(condition);
            if(purchaseArrivalTmp != null) {
                throw new FabosJsonApiErrorTip("同样的单据已经建立了, 请不要重复添加!");
            }
            purchaseArrival.setCurrentState(OrderCurrentStateEnum.Enum.EDIT.name());
            purchaseArrival.setPurchaseType(PurchaseTypeEnum.Enum.LowUsageConsumables.name());
            if(CollectionUtils.isNotEmpty(purchaseArrival.getPurchaseArrivalDetailList())) {
                Set<String> stringSet = purchaseArrival.getPurchaseArrivalDetailList().stream().map(item -> item.getMaterialCode()).collect(Collectors.toSet());
                if(stringSet.size() < purchaseArrival.getPurchaseArrivalDetailList().size() ) {
                    throw new FabosJsonApiErrorTip("添加了同样的采购到货明细, 请检查!");
                }
            }
            ListUtils.forEach(purchaseArrival.getPurchaseArrivalDetailList(), pa -> {
                    ListUtils.forEach(pa.getThirdPurchaseArrivalDetailList(), tpa -> {
                        tpa.setMaterialId(pa.getMaterial() != null ? pa.getMaterial().getId() : "");
                        tpa.setMaterialCode(pa.getMaterialCode());
                        tpa.setMaterialName(pa.getMaterialName());
                        tpa.setMaterialSpecification(pa.getMaterialSpecification());
                        tpa.setMaterialCategory(pa.getMaterialCategory());
                        tpa.setAccountUnit(pa.getAccountUnit());
                        tpa.setIncomingBatchNo(pa.getIncomingBatchNo());
                        if(tpa.getBoxCapacity() != null) {
                            BoxCalculator.BoxResult result = BoxCalculator.calculateBoxes(BigDecimal.valueOf(tpa.getQuantity()), BigDecimal.valueOf(tpa.getBoxCapacity()));
                            tpa.setBoxCount(result.getBoxCount());
                            AtomicInteger index = new AtomicInteger(0);
                            tpa.setThirdPurchaseArrivalAutoBoxedDetailList(result.getAllocations().stream().map(item -> {
                                index.getAndIncrement();
                                return ThirdPurchaseArrivalAutoBoxedDetail.builder()
                                        .materialId(tpa.getMaterialId())
                                        .materialCode(tpa.getMaterialCode())
                                        .materialName(tpa.getMaterialName())
                                        .materialSpecification(tpa.getMaterialSpecification())
                                        .materialCategory(tpa.getMaterialCategory())
                                        .inventoryLotId(tpa.getIncomingBatchNo())
                                        .lotSerialId(tpa.getFactoryLotIdentifier())
                                        .boxedCode(tpa.getFactoryLotIdentifier() + "-" + String.format("%d", index.get()))
                                        .boxNo(String.valueOf(index.get()))
                                        .quantity(item.doubleValue())
                                        .accountUnit(tpa.getAccountUnit())
                                        .thirdPurchaseArrivalDetail(tpa)
                                        .build();
                            }).collect(Collectors.toList()));
                        }
                    });
            });
        }
    }

    @Override
    public void beforeUpdate(PurchaseArrival purchaseArrival) {
        if(purchaseArrival != null) {
            purchaseArrival.setCurrentState(OrderCurrentStateEnum.Enum.EDIT.name());
            purchaseArrival.setPurchaseType(PurchaseTypeEnum.Enum.LowUsageConsumables.name());
            if(CollectionUtils.isNotEmpty(purchaseArrival.getPurchaseArrivalDetailList())) {
                Set<String> stringSet = purchaseArrival.getPurchaseArrivalDetailList().stream().map(item -> item.getMaterialCode()).collect(Collectors.toSet());
                if(stringSet.size() < purchaseArrival.getPurchaseArrivalDetailList().size() ) {
                    throw new FabosJsonApiErrorTip("添加了同样的采购到货明细, 请检查!");
                }
            }
            ListUtils.forEach(purchaseArrival.getPurchaseArrivalDetailList(), pa -> {
                ListUtils.forEach(pa.getThirdPurchaseArrivalDetailList(), tpa -> {
                    tpa.setMaterialId(pa.getMaterial() != null ? pa.getMaterial().getId() : "");
                    tpa.setMaterialCode(pa.getMaterialCode());
                    tpa.setMaterialName(pa.getMaterialName());
                    tpa.setMaterialSpecification(pa.getMaterialSpecification());
                    tpa.setMaterialCategory(pa.getMaterialCategory());
                    tpa.setAccountUnit(pa.getAccountUnit());
                    tpa.setIncomingBatchNo(pa.getIncomingBatchNo());
                    if(tpa.getBoxCapacity() == null || tpa.getBoxCapacity().doubleValue() <= 0) {
                        tpa.setBoxCapacity(tpa.getQuantity());
                    }
                    if(CollectionUtils.isNotEmpty(tpa.getThirdPurchaseArrivalAutoBoxedDetailList())) {
                        BoxCalculator.BoxResult result = BoxCalculator.calculateBoxes(BigDecimal.valueOf(tpa.getQuantity()), BigDecimal.valueOf(tpa.getBoxCapacity()));
                        tpa.setBoxCount(result.getBoxCount());
                        AtomicInteger index = new AtomicInteger(0);
                        tpa.setThirdPurchaseArrivalAutoBoxedDetailList(result.getAllocations().stream().map(item -> {
                            index.getAndIncrement();
                            return ThirdPurchaseArrivalAutoBoxedDetail.builder()
                                    .materialId(tpa.getMaterialId())
                                    .materialCode(tpa.getMaterialCode())
                                    .materialName(tpa.getMaterialName())
                                    .materialSpecification(tpa.getMaterialSpecification())
                                    .materialCategory(tpa.getMaterialCategory())
                                    .inventoryLotId(tpa.getIncomingBatchNo())
                                    .lotSerialId(tpa.getFactoryLotIdentifier())
                                    .boxedCode(tpa.getFactoryLotIdentifier() + "-" + String.format("%d", index.get()))
                                    .boxNo(String.valueOf(index.get()))
                                    .quantity(item.doubleValue())
                                    .accountUnit(tpa.getAccountUnit())
                                    .thirdPurchaseArrivalDetail(tpa)
                                    .build();
                        }).collect(Collectors.toList()));
                    }
                });
            });
        }
    }
}
