package cec.jiutian.bc.ag.domain.outsourceArrivalRegistration.model;

import cec.jiutian.bc.ag.domain.outsourceArrivalRegistration.handler.OutsourceArrivalRegistrationAdjustHandler;
import cec.jiutian.bc.ag.domain.outsourceArrivalRegistration.proxy.OutsourceArrivalRegistrationFlowProxy;
import cec.jiutian.bc.ag.domain.outsourceArrivalRegistration.proxy.OutsourceArrivalRegistrationProxy;
import cec.jiutian.bc.ag.enumeration.InspectTypeEnum;
import cec.jiutian.bc.ag.enumeration.ProcessStateEnum;
import cec.jiutian.bc.ag.port.dto.OutsourceReceivePlanMTO;
import cec.jiutian.bc.flow.domain.model.entity.ExamineModel;
import cec.jiutian.bc.generalModeler.domain.supplier.model.Supplier;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DateType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowBaseOperation;
import cec.jiutian.view.type.RowOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OrderBy;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * 外包到货登记
 *
 * <AUTHOR>
 * @date 2025/1/9 9:59
 */
@FabosJson(
        name = "外采服务到货登记",
        orderBy = "OutsourceArrivalRegistration.createTime desc",
        dataProxy = OutsourceArrivalRegistrationProxy.class,
        power = @Power(add = false, edit = false, examine = true, examineDetails = true),
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "examine",
                        ifExpr = "examineStatus =='3' || examineStatus =='1'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "examineDetails",
                        ifExpr = "examineStatus =='0'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
//                @RowBaseOperation(
//                        code = "edit",
//                        ifExpr = "currentState!='开立'",
//                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
//                ),
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "currentState!='EDIT'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
        },
        rowOperation = {
                @RowOperation(
                        title = "下厂检验到货登记",
                        code = "OutsourceArrivalRegistration@TOSUPPLIERCREATE",
                        mode = RowOperation.Mode.BUTTON,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.ADD,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = OutsourceArrivalRegistrationToSupplier.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "OutsourceArrivalRegistration@TOSUPPLIERCREATE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
                @RowOperation(
                        title = "进厂检验到货登记",
                        code = "OutsourceArrivalRegistration@ARRIVALFACTORYCREATE",
                        mode = RowOperation.Mode.BUTTON,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.ADD,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = OutsourceArrivalRegistrationFactory.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "OutsourceArrivalRegistration@ARRIVALFACTORYCREATE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
                @RowOperation(
                        title = "编辑",
                        code = "OutsourceArrivalRegistration@TOSUPPLIERUPDATE",
                        mode = RowOperation.Mode.SINGLE,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.UPDATE,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = OutsourceArrivalRegistrationToSupplier.class,
                        ifExpr = "inspectType !='ToSupplier' || currentState!='EDIT'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "OutsourceArrivalRegistration@TOSUPPLIERUPDATE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
                @RowOperation(
                        title = "编辑",
                        code = "OutsourceArrivalRegistration@ARRIVALFACTORYUPDATE",
                        mode = RowOperation.Mode.SINGLE,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.UPDATE,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = OutsourceArrivalRegistrationFactory.class,
                        ifExpr = "inspectType =='ToSupplier' || currentState!='EDIT'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "OutsourceArrivalRegistration@ARRIVALFACTORYUPDATE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
                @RowOperation(
                        title = "紧急调整",
                        code = "OutsourceArrivalRegistration@ADJUST",
                        mode = RowOperation.Mode.SINGLE,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = OutsourceArrivalRegistrationFactory.class,
                        operationHandler = OutsourceArrivalRegistrationAdjustHandler.class,
                        ifExpr = "inspectType !='ArriveFactory' || examineStatus !='1'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "OutsourceArrivalRegistration@ADJUST"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
        },
        flowProxy = {OutsourceArrivalRegistrationFlowProxy.class},
        flowCode = "OutsourceArrivalRegistration"
)
@Table(name = "bwi_outsource_arrival_registration",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"oid", "arrivalRegistrationNumber"})
        }
)
@Entity
@Getter
@Setter
@TemplateType(type = "multiTable")
public class OutsourceArrivalRegistration extends ExamineModel {

    @FabosJsonField(
            views = @View(title = "到货单号"),
            edit = @Edit(title = "到货单号", search = @Search(vague = true), show = false)
    )
    private String arrivalRegistrationNumber;

    @FabosJsonField(
            views = @View(title = "外采服务收货计划", column = "generalCode"),
            edit = @Edit(title = "外采服务收货计划",
                    readonly = @Readonly(add = false),
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode")
            )
    )
    @ManyToOne
    private OutsourceReceivePlanMTO outsourceReceivePlanMTO;

    @FabosJsonField(
            views = @View(title = "检验方式"),
            edit = @Edit(title = "检验方式", readonly = @Readonly, type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = InspectTypeEnum.class))
    )
    private String inspectType;

    @FabosJsonField(
            views = @View(title = "供应商", column = "supplierName", type = ViewType.TABLE_FORM),
            edit = @Edit(title = "供应商",
                    readonly = @Readonly,
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "supplierName")
            )
    )
    @ManyToOne
    private Supplier supplier;

    @FabosJsonField(
            views = @View(title = "货车吨位"),
            edit = @Edit(title = "货车吨位", notNull = true, numberType = @NumberType(min = 0))
    )
    private Double truckTonnage;

    @FabosJsonField(
            views = @View(title = "车牌号"),
            edit = @Edit(title = "车牌号", notNull = true, inputType = @InputType(length = 20))
    )
    private String carNumber;

    @FabosJsonField(
            views = @View(title = "货物包装"),
            edit = @Edit(title = "货物包装", notNull = true, inputType = @InputType(length = 20))
    )
    private String goodsPackaging;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "到货日期", type = ViewType.DATE),
            edit = @Edit(title = "到货日期", notNull = true, search = @Search(vague = true),
                    type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE))
    )
    private Date arrivalDate;

    @FabosJsonField(
            views = @View(title = "当前状态"),
            edit = @Edit(title = "当前状态", type = EditType.CHOICE, show = false,
                    search = @Search(),
                    choiceType = @ChoiceType(
                            fetchHandler = OrderCurrentStateEnum.class
                    )
            )
    )
    private String currentState;

    @FabosJsonField(
            views = @View(title = "处理状态"),
            edit = @Edit(title = "处理状态", type = EditType.CHOICE, show = false,
                    search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = ProcessStateEnum.class)
            )
    )
    private String processState;

    @FabosJsonField(
            views = @View(title = "备注"),
            edit = @Edit(title = "备注", type = EditType.TEXTAREA)
    )
    private String remark;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "outsource_arrival_registration_id")
    @OrderBy
    @FabosJsonField(
            edit = @Edit(title = "到货明细", type = EditType.TAB_TABLE_ADD),
            views = @View(title = "到货明细", type = ViewType.TABLE_VIEW)
    )
    private List<OutsourceArrivalRegistrationDetail> details;

}
