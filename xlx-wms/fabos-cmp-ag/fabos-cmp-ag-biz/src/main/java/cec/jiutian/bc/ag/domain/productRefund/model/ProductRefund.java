package cec.jiutian.bc.ag.domain.productRefund.model;

import cec.jiutian.bc.ag.domain.productRefund.handler.ProductRefundCheckFeedbackHandler;
import cec.jiutian.bc.ag.domain.productRefund.handler.ProductRefundCheckHandler;
import cec.jiutian.bc.ag.domain.productRefund.handler.ProductRefundReleaseHandler;
import cec.jiutian.bc.ag.domain.productRefund.proxy.ProductRefundProxy;
import cec.jiutian.bc.ag.enumeration.PurchaseArrivalProcessStateEnum;
import cec.jiutian.bc.ag.enumeration.ReviewCommentEnum;
import cec.jiutian.bc.generalModeler.domain.client.model.Client;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowBaseOperation;
import cec.jiutian.view.type.RowOperation;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年03月03日 14:47
 */
@FabosJson(
        name = "产品退货",
        orderBy = "ProductRefund.createTime desc",
        power = @Power(importable = false, export = false, add = true, print = true),
        dataProxy = ProductRefundProxy.class,
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "add",
                        ifExpr = "1 != 1",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.HIDE
                ),
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "checkResult == 'PASS'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "1 == 1",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.HIDE
                )
        },
        rowOperation = {
                @RowOperation(
                        title = "验收",
                        code = "ProductRefund@CHECK",
                        mode = RowOperation.Mode.SINGLE,
                        operationHandler = ProductRefundCheckHandler.class,
                        ifExpr = "currentState != 'EDIT'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        callHint = "是否确定操作？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "ProductRefundCheckHandler@CHECK"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
                @RowOperation(
                        title = "验收反馈",
                        code = "ProductRefund@CHECKFEEDBACK",
                        mode = RowOperation.Mode.SINGLE,
                        operationHandler = ProductRefundCheckFeedbackHandler.class,
                        ifExpr = "checkResult == null || checkResult == ''",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        callHint = "是否确定操作？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "ProductRefundCheckFeedbackHandler@CHECKFEEDBACK"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
                @RowOperation(
                        title = "发布",
                        code = "ProductRefund@RELEASE",
                        mode = RowOperation.Mode.SINGLE,
                        operationHandler = ProductRefundReleaseHandler.class,
                        ifExpr = "checkResult != 'PASS' || currentState != 'EDIT'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        callHint = "是否确定操作？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "ProductRefundReleaseHandler@RELEASE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                )
        }
)
@Table(name = "bwi_product_refund",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"id", "productRefundNumber"})
        }
)
@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TemplateType(type = "multiTable")
public class ProductRefund extends MetaModel {

    @FabosJsonField(
            views = @View(title = "退货单号"),
            edit = @Edit(title = "退货单号",
                    readonly = @Readonly(add = false, edit = false), notNull = true
            )
    )
    private String productRefundNumber;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "客户名称", column = "clntNm", type = ViewType.TABLE_FORM),
            edit = @Edit(title = "客户名称", type = EditType.REFERENCE_TABLE, allowAddMultipleRows = false,
                    filter = @Filter(value = "Client.vldFlg = 'Y'"),
                    search = @Search(), placeHolder = "请选择", notNull = true, referenceTableType = @ReferenceTableType(id = "id", label = "clntNm"))
    )
    private Client customerName;

    @FabosJsonField(
            views = @View(title = "退货原因"),
            edit = @Edit(title = "退货原因", readonly = @Readonly(add = false, edit = false))
    )
    private String refundReason;

    @FabosJsonField(
            views = @View(title = "单据状态"),
            edit = @Edit(title = "单据状态", type = EditType.CHOICE, show = false,
                    search = @Search(),
                    choiceType = @ChoiceType(
                            fetchHandler = OrderCurrentStateEnum.class
                    )
            )
    )
    private String currentState;

    @FabosJsonField(
            views = @View(title = "单据处理状态"),
            edit = @Edit(title = "单据处理状态", type = EditType.CHOICE, show = false,
                    search = @Search(),
                    choiceType = @ChoiceType(
                            fetchHandler = PurchaseArrivalProcessStateEnum.class
                    )
            )
    )
    private String processState;

    @FabosJsonField(
            views = @View(title = "验收结果"),
            edit = @Edit(title = "验收结果", type = EditType.CHOICE, show = false,
                    search = @Search(),
                    choiceType = @ChoiceType(
                            fetchHandler = ReviewCommentEnum.class
                    )
            )
    )
    private String checkResult;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.EAGER)
    @JoinColumn(name = "product_refund_id")
    @FabosJsonField(
            views = @View(title = "产品退货详情", type = ViewType.TABLE_VIEW, show = true),
            edit = @Edit(title = "产品退货详情", type = EditType.TAB_TABLE_ADD, show = true)
    )
    private List<ProductRefundDetail> productRefundDetailList;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.EAGER)
    @JoinColumn(name = "product_refund_id")
    @FabosJsonField(
            views = @View(title = "产品验收详情", type = ViewType.TABLE_VIEW, show = true),
            edit = @Edit(title = "产品验收详情", type = EditType.TAB_TABLE_ADD, show = true)
    )
    private List<ProductCheckDetail> productCheckDetailList;
}
