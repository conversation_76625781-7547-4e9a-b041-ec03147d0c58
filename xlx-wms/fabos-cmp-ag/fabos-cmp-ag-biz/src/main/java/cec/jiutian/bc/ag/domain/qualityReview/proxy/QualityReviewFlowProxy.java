package cec.jiutian.bc.ag.domain.qualityReview.proxy;

import cec.jiutian.bc.ag.domain.qualityReview.model.QualityReview;
import cec.jiutian.bc.ag.service.ArrivalGoodsService;
import cec.jiutian.bc.flow.enums.ExamineStatusEnum;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.data.jpa.JpaCrud;
import cec.jiutian.view.fun.FlowProxy;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class QualityReviewFlowProxy extends FlowProxy {

    @Resource
    private JpaCrud jpaCrud;

    @Resource
    private ArrivalGoodsService arrivalGoodsService;

    @Override
    public void onEvent(Object event, Object entity) {
        if (entity instanceof QualityReview qualityReview) {
            if (qualityReview.getExamineStatus().equals(ExamineStatusEnum.AUDITED.getCode())) {
                // 审批通过 1.1 审理单当前状态为 完成
                qualityReview.setCurrentState(OrderCurrentStateEnum.Enum.COMPLETE.name());
                // 1.2 更新请验单的 检验结果为合格、合格证状态为待开；
                arrivalGoodsService.updateIqcByReview(qualityReview.getIqc().getId(), ExamineStatusEnum.AUDITED.getCode());

            } else if (qualityReview.getExamineStatus().equals(ExamineStatusEnum.REJECTED.getCode())) {
                // 2.不通过则 2.1 更新审理单当前状态为终止
                qualityReview.setCurrentState(OrderCurrentStateEnum.Enum.END.name());
                // 2.2 更新请验单的 合格证状态为待开
                arrivalGoodsService.updateIqcByReview(qualityReview.getIqc().getId(), ExamineStatusEnum.REJECTED.getCode());
            }
            jpaCrud.update(qualityReview);

        }
        log.info("质量审理审批回调成功");
    }

    @Override
    public void afterSubmit(Object entity) {
        if (entity instanceof QualityReview qualityReview) {
            qualityReview.setCurrentState(OrderCurrentStateEnum.Enum.EXECUTE.name());
            jpaCrud.update(qualityReview);
        }
    }

}
