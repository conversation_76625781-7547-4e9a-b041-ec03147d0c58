package cec.jiutian.bc.ag.remote.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025年03月13日 9:49
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InventoryAutoBoxedQueryDTO {

    //页号(必传)
    private Integer page;

    //每页大小(必传)
    private Integer perPage;

    private Condition condition;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Condition {

        //页号(必传)
        private Integer page;

        //每页大小(必传)
        private Integer perPage;

        //物料编码
        private String materialCode;

        //物料名称
        private String materialName;

        //来料批号
        private String inventoryLotId;

        //本厂批号
        private String lotSerialId;

        //箱次编码
        private String boxedCode;
    }
}
