package cec.jiutian.bc.ag.domain.urgentRelease.proxy;

import cec.jiutian.bc.ag.domain.urgentRelease.model.UrgentRelease;
import cec.jiutian.bc.ag.domain.urgentRelease.model.UrgentReleaseDetail;
import cec.jiutian.bc.library.domain.inventory.model.Inventory;
import cec.jiutian.bc.library.enumeration.InventoryCurrentStateEnum;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.common.util.ListUtils;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2025年03月07日 9:32
 */
@Component
public class UrgentReleaseProxy implements DataProxy<UrgentRelease> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public void beforeAdd(UrgentRelease urgentRelease) {
        if(urgentRelease != null) {
            urgentRelease.setCurrentState(OrderCurrentStateEnum.Enum.EDIT.name());
            if(CollectionUtils.isNotEmpty(urgentRelease.getUrgentReleaseDetailList())) {
                checkInventoryCurrentState(urgentRelease.getUrgentReleaseDetailList());
                urgentRelease.setUrgentReleaseDetailList(ListUtils.distinctListByKey(urgentRelease.getUrgentReleaseDetailList(), UrgentReleaseDetail::getInventoryId));
            }
        }
    }

    @Override
    public void beforeUpdate(UrgentRelease urgentRelease) {
        if(urgentRelease != null) {
            if(CollectionUtils.isNotEmpty(urgentRelease.getUrgentReleaseDetailList())) {
                checkInventoryCurrentState(urgentRelease.getUrgentReleaseDetailList());
                urgentRelease.setUrgentReleaseDetailList(ListUtils.distinctListByKey(urgentRelease.getUrgentReleaseDetailList(), UrgentReleaseDetail::getInventoryId));
            }
        }
    }

    private void checkInventoryCurrentState(List<UrgentReleaseDetail> urgentReleaseDetailList) {
        AtomicInteger index = new AtomicInteger(1);
        ListUtils.forEach(urgentReleaseDetailList, item -> {
            Inventory inventory = fabosJsonDao.findById(Inventory.class, item.getInventoryId());
            if(inventory == null) {
                throw new FabosJsonApiErrorTip("库存台账记录不存在, 请检查!");
            }
            if(!InventoryCurrentStateEnum.Enum.waitInspect.name().equals(inventory.getCurrentState())) {
                throw new FabosJsonApiErrorTip(String.format("第%d条库存台账记录不是待检验状态, 不能创建紧急放行单, 请检查!", index.get()));
            }
            index.getAndIncrement();
        });
    }
}
