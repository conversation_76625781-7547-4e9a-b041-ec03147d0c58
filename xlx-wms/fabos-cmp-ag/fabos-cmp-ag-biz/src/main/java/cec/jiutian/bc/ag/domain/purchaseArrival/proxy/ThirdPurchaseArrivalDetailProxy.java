package cec.jiutian.bc.ag.domain.purchaseArrival.proxy;

import cec.jiutian.bc.ag.domain.purchaseArrival.model.ThirdPurchaseArrivalDetail;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025年03月05日 16:47
 */
@Component
public class ThirdPurchaseArrivalDetailProxy implements DataProxy<ThirdPurchaseArrivalDetail> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public void beforeAdd(ThirdPurchaseArrivalDetail thirdPurchaseArrivalDetail) {
        if(thirdPurchaseArrivalDetail != null && thirdPurchaseArrivalDetail.getPurchaseArrivalDetail() != null) {
            thirdPurchaseArrivalDetail.setMaterialId(thirdPurchaseArrivalDetail.getPurchaseArrivalDetail().getMaterial() != null ? thirdPurchaseArrivalDetail.getPurchaseArrivalDetail().getMaterial().getId() : "");
            thirdPurchaseArrivalDetail.setMaterialCode(thirdPurchaseArrivalDetail.getPurchaseArrivalDetail().getMaterialCode());
            thirdPurchaseArrivalDetail.setMaterialName(thirdPurchaseArrivalDetail.getPurchaseArrivalDetail().getMaterialName());
            thirdPurchaseArrivalDetail.setMaterialSpecification(thirdPurchaseArrivalDetail.getPurchaseArrivalDetail().getMaterialSpecification());
            thirdPurchaseArrivalDetail.setMaterialCategory(thirdPurchaseArrivalDetail.getPurchaseArrivalDetail().getMaterialCategory());
            thirdPurchaseArrivalDetail.setAccountUnit(thirdPurchaseArrivalDetail.getPurchaseArrivalDetail().getAccountUnit());
            thirdPurchaseArrivalDetail.setIncomingBatchNo(thirdPurchaseArrivalDetail.getPurchaseArrivalDetail().getIncomingBatchNo());
        }
    }

    @Override
    public void beforeUpdate(ThirdPurchaseArrivalDetail thirdPurchaseArrivalDetail) {
        if(thirdPurchaseArrivalDetail != null && thirdPurchaseArrivalDetail.getPurchaseArrivalDetail() != null) {
            thirdPurchaseArrivalDetail.setMaterialId(thirdPurchaseArrivalDetail.getPurchaseArrivalDetail().getMaterial() != null ? thirdPurchaseArrivalDetail.getPurchaseArrivalDetail().getMaterial().getId() : "");
            thirdPurchaseArrivalDetail.setMaterialCode(thirdPurchaseArrivalDetail.getPurchaseArrivalDetail().getMaterialCode());
            thirdPurchaseArrivalDetail.setMaterialName(thirdPurchaseArrivalDetail.getPurchaseArrivalDetail().getMaterialName());
            thirdPurchaseArrivalDetail.setMaterialSpecification(thirdPurchaseArrivalDetail.getPurchaseArrivalDetail().getMaterialSpecification());
            thirdPurchaseArrivalDetail.setMaterialCategory(thirdPurchaseArrivalDetail.getPurchaseArrivalDetail().getMaterialCategory());
            thirdPurchaseArrivalDetail.setAccountUnit(thirdPurchaseArrivalDetail.getPurchaseArrivalDetail().getAccountUnit());
            thirdPurchaseArrivalDetail.setIncomingBatchNo(thirdPurchaseArrivalDetail.getPurchaseArrivalDetail().getIncomingBatchNo());
        }
    }
}
