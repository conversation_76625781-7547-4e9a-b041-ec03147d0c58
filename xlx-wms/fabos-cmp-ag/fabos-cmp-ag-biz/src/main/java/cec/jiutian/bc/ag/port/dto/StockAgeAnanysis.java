package cec.jiutian.bc.ag.port.dto;

import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.QueryModel;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.View;
import cec.jiutian.view.type.Power;

/**
 * <AUTHOR>
 * @date 2025年03月31日 15:29
 */
@FabosJson(
        name = "库龄分析",
        power = @Power(add = false, viewDetails = false, edit = false, delete = false)
)
@QueryModel(hql = "select new map(bi.inventoryLotId as inventoryLotId, bi.lotSerialId as lotSerialId," +
        "bi.materialCode as materialCode, bi.materialName as materialName, bi.materialSpecification as materialSpecification," +
        "bi.price as price, bi.lotAllQuantity as lotAllQuantity, bi.accountingUnitQuantity as accountingUnitQuantity, bi.availableQuantity as availableQuantity, " +
        "CASE \n" +
        "        WHEN extract(year from age(now(), bi.createTime)) > 0 THEN\n" +
        "            CONCAT(\n" +
        "                extract(year from age(now(), bi.createTime)), ' 年 ',\n" +
        "                extract(month from age(now(), bi.createTime)), ' 月 ',\n" +
        "                extract(day from age(now(), bi.createTime)), ' 天 ',\n" +
        "                extract(hour from age(now(), bi.createTime)), ' 时 ',\n" +
        "                extract(minute from age(now(), bi.createTime)), ' 分 ',\n" +
        "                ROUND(extract(second from age(now(), bi.createTime))), ' 秒'\n" +
        "            )\n" +
        "        WHEN extract(month from age(now(), bi.createTime)) > 0 THEN\n" +
        "            CONCAT(\n" +
        "                extract(month from age(now(), bi.createTime)), ' 月 ',\n" +
        "                extract(day from age(now(), bi.createTime)), ' 天 ',\n" +
        "                extract(hour from age(now(), bi.createTime)), ' 时 ',\n" +
        "                extract(minute from age(now(), bi.createTime)), ' 分 ',\n" +
        "                ROUND(extract(second from age(now(), bi.createTime))), ' 秒'\n" +
        "            )\n" +
        "        WHEN extract(day from age(now(), bi.createTime)) > 0 THEN\n" +
        "            CONCAT(\n" +
        "                extract(day from age(now(), bi.createTime)), ' 天 ',\n" +
        "                extract(hour from age(now(), bi.createTime)), ' 时 ',\n" +
        "                extract(minute from age(now(), bi.createTime)), ' 分 ',\n" +
        "                ROUND(extract(second from age(now(), bi.createTime))), ' 秒'\n" +
        "            )\n" +
        "        WHEN extract(hour from age(now(), bi.createTime)) > 0 THEN\n" +
        "            CONCAT(\n" +
        "                extract(hour from age(now(), bi.createTime)), ' 时 ',\n" +
        "                extract(minute from age(now(), bi.createTime)), ' 分 ',\n" +
        "                ROUND(extract(second from age(now(), bi.createTime))), ' 秒'\n" +
        "            )\n" +
        "        WHEN extract(minute from age(now(), bi.createTime)) > 0 THEN\n" +
        "            CONCAT(\n" +
        "                extract(minute from age(now(), bi.createTime)), ' 分 ',\n" +
        "                ROUND(extract(second from age(now(), bi.createTime))), ' 秒'\n" +
        "            )\n" +
        "        ELSE\n" +
        "            CONCAT(ROUND(extract(second from age(now(), bi.createTime))), ' 秒')\n" +
        "    END as stockAge" +
        ")" +
        "from Inventory bi order by bi.createTime asc", where = "bi.availableQuantity > 0")
public class StockAgeAnanysis extends MetaModel {

    @FabosJsonField(
            views = @View(title = "库存批次"),
            edit = @Edit(title = "库存批次")

    )
    private String inventoryLotId;

    @FabosJsonField(
            views = @View(title = "本厂批号"),
            edit = @Edit(title = "本厂批号")

    )
    private String lotSerialId;

    @FabosJsonField(
            views = @View(title = "物料编号"),
            edit = @Edit(title = "物料编号")

    )
    private String materialCode;

    @FabosJsonField(
            views = @View(title = "物料名称"),
            edit = @Edit(title = "物料名称")

    )
    private String materialName;

    @FabosJsonField(
            views = @View(title = "规格"),
            edit = @Edit(title = "规格")

    )
    private String materialSpecification;

    @FabosJsonField(
            views = @View(title = "单价"),
            edit = @Edit(title = "单价")

    )
    private Double price;

    @FabosJsonField(
            views = @View(title = "本厂批数量"),
            edit = @Edit(title = "本厂批数量")

    )
    private Double lotAllQuantity;

    @FabosJsonField(
            views = @View(title = "台账总量"),
            edit = @Edit(title = "台账总量")

    )
    private Double accountingUnitQuantity;

    @FabosJsonField(
            views = @View(title = "可用数量"),
            edit = @Edit(title = "可用数量")

    )
    private Double availableQuantity;

    @FabosJsonField(
            views = @View(title = "库龄"),
            edit = @Edit(title = "库龄")

    )
    private String stockAge;

}
