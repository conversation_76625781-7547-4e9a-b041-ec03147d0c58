package cec.jiutian.bc.ag.domain.productRefund.model;

import cec.jiutian.bc.ag.domain.productRefund.handler.MaterialChangeProductCheckDetailHandler;
import cec.jiutian.bc.generalModeler.domain.material.model.Material;
import cec.jiutian.bc.generalModeler.domain.measureUnit.model.MeasureUnit;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025年03月04日 9:56
 */
@Table(name = "bwi_product_check_detail")
@FabosJson(
        name = "产品验收详情",
        orderBy = "ProductCheckDetail.createTime desc",
        power = @Power(importable = false, add = true, edit = false)
)
@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TemplateType(type = "usual")
public class ProductCheckDetail extends MetaModel {

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "物料主数据", column = "name", type = ViewType.TABLE_FORM),
            edit = @Edit(title = "物料主数据",
                    filter = @Filter(value = "Material.currentStatus = 'Y'"),
                    notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"))
    )
    private Material material;

    @FabosJsonField(
            views = @View(title = "存货编码", show = true),
            edit = @Edit(title = "存货编码", show = true, notNull = true),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "material",
                    dynamicHandler = MaterialChangeProductCheckDetailHandler.class))
    )
    private String materialCode;

    @FabosJsonField(
            views = @View(title = "物料名称"),
            edit = @Edit(title = "物料名称", readonly = @Readonly(add = false, edit = false), notNull = true)
    )
    private String materialName;

    @FabosJsonField(
            views = @View(title = "型号规格", show = true),
            edit = @Edit(title = "型号规格", show = true, notNull = true)
    )
    private String materialSpecification;

    @FabosJsonField(
            views = @View(title = "退货批号"),
            edit = @Edit(title = "退货批号", notNull = true)
    )
    private String productRefundNumber;

    @FabosJsonField(
            views = @View(title = "退货数量", rowEdit = true, width = "100px"),
            edit = @Edit(title = "退货数量", notNull = true, tips = "退货数量",
                    numberType = @NumberType(min = 0, max = 9999999, precision = 3))
    )
    private Double refundQuantity;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "单位", column = "unitChnName", type = ViewType.TABLE_FORM),
            edit = @Edit(title = "单位",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "unitChnName",
                            /*type = ReferenceTableType.SelectShowTypeMTO.LIST, */groupField = "unitType")
            )
    )
    private MeasureUnit measureUnit;

    @FabosJsonField(
            views = {
                    @View(title = "产品退货单号", column = "productRefundNumber")
            },
            edit = @Edit(title = "产品退货单号", type = EditType.REFERENCE_TABLE, show = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "productRefundNumber"))
    )
    @ManyToOne
    @JsonIgnoreProperties("productCheckDetailList")
    private ProductRefund productRefund;
}
