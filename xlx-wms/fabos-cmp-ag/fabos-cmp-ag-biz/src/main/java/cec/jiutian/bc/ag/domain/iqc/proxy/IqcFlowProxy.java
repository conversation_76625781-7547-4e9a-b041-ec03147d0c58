package cec.jiutian.bc.ag.domain.iqc.proxy;

import cec.jiutian.bc.ag.domain.iqc.model.Iqc;
import cec.jiutian.bc.ag.port.client.ArrivalGoodsClient;
import cec.jiutian.data.jpa.JpaCrud;
import cec.jiutian.view.fun.FlowProxy;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class IqcFlowProxy extends FlowProxy {

    @Resource
    private ArrivalGoodsClient arrivalGoodsClient;

    @Resource
    private JpaCrud jpaCrud;

    @Override
    public void onEvent(Object event, Object entity) {
//        if (entity instanceof Iqc iqc) {
//            if (iqc.getExamineStatus().equals(ExamineStatusEnum.REJECTED.getCode())) {
//                iqc.setCurrentState(IqcCurrentStateEnum.Enum.EDIT.name());
//                jpaCrud.update(iqc);
//
//            } else if (iqc.getExamineStatus().equals(ExamineStatusEnum.AUDITED.getCode())) {
//                if (InspectTypeEnum.Enum.ToSupplier.name().equals(iqc.getInspectType()) && MaterialSourceTypeEnum.Enum.PurchaseMaterial.name().equals(iqc.getMaterialSourceType())) {
//                    arrivalGoodsClient.updateReceivePlanState(iqc.getReceivePlanNumber(), "UnArrival");
//                }
//            }
//        }

    }

    @Override
    public void beforeSubmit(Object entity) {
        if (entity instanceof Iqc iqc) {

        }
    }

}
