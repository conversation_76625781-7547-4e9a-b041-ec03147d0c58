package cec.jiutian.bc.si.domain.stockInTempApply.handler;

import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.si.domain.stockInTempApply.model.StockInTempApplyAddForMaterial;
import cec.jiutian.bc.si.enumeration.NamingRuleCodeEnum;
import cec.jiutian.view.DependFiled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/12/3
 * @description TODO
 */
@Component
public class StockInTempApplyForMaterialCodeGenerateHandler implements DependFiled.DynamicHandler<StockInTempApplyAddForMaterial> {

    @Resource
    private NamingRuleService namingRuleService;

    @Override
    public Map<String, Object> handle(StockInTempApplyAddForMaterial stockInTempApplyAddForMaterial) {
        Map<String, Object> map = new HashMap<>();
        map.put("generalCode", String.valueOf(namingRuleService.getNameCode(NamingRuleCodeEnum.StockInApplyNumber.name(), 1, null).get(0)));
        return map;
    }
}
