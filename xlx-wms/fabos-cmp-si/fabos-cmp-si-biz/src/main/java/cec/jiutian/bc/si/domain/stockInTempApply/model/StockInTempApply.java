package cec.jiutian.bc.si.domain.stockInTempApply.model;

import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleModel;
import cec.jiutian.bc.si.domain.stockInTempApply.handler.StockInTempApplyReferenceAddHandler;
import cec.jiutian.bc.si.domain.stockInTempApply.proxy.StockInTempApplyFlowProxy;
import cec.jiutian.bc.si.domain.stockInTempApply.proxy.StockInTempApplyProxy;
import cec.jiutian.bc.si.enumeration.NamingRuleCodeEnum;
import cec.jiutian.bc.si.enumeration.OrderApplyProcessStateEnum;
import cec.jiutian.bc.si.port.dto.OrgMTO;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.ReferenceAddType;
import cec.jiutian.view.ReferenceTreeType;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.AttachmentType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DateType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowBaseOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OrderBy;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;


@FabosJson(
        name = "暂存入库申请",
        orderBy = "StockInTempApply.createTime desc",
        dataProxy = {StockInTempApplyProxy.class},
        filter = @Filter(value = "StockInTempApply.applyType = 'Temp'"),
        rowOperation = {
//                @RowOperation(
//                        title = "暂存物料",
//                        code = "StockInTempApply@TEMP_MATERIAL_CREATE",
//                        operationHandler = StockInTempApplyForMaterialCreateOperationHandler.class,
//                        mode = RowOperation.Mode.BUTTON,
//                        type = RowOperation.Type.POPUP,
//                        submitMethod = RowOperation.SubmitMethod.ADD,
//                        popupType = RowOperation.PopupType.FORM,
//                        fabosJsonClass = StockInTempApplyAddForMaterial.class,
//                        show = @ExprBool(
//                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
//                                params = "StockInTempApply@TEMP_MATERIAL_CREATE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
//                        )
//                ),
//                @RowOperation(
//                        title = "暂存零部件",
//                        code = "StockInTempApply@TEMP_PRODUCT_CREATE",
//                        operationHandler = StockInTempApplyForProductCreateOperationHandler.class,
//                        mode = RowOperation.Mode.BUTTON,
//                        type = RowOperation.Type.POPUP,
//                        submitMethod = RowOperation.SubmitMethod.ADD,
//                        popupType = RowOperation.PopupType.FORM,
//                        fabosJsonClass = StockInTempApplyAddForProduct.class,
//                        show = @ExprBool(
//                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
//                                params = "StockInTempApply@TEMP_PRODUCT_CREATE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
//                        )
//                ),
        },
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "currentState!='EDIT'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "currentState!='EDIT'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "examine",
                        ifExpr = "examineStatus =='3' || examineStatus =='1'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "examineDetails",
                        ifExpr = "examineStatus =='0'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                )
        },
        flowProxy = StockInTempApplyFlowProxy.class,
        flowCode = "StockInTempApply",
        power = @Power(examine = true, examineDetails = true)
)
@Table(name = "bwi_stock_in_apply")
@Entity
@Getter
@Setter
@TemplateType(type = "multiTable")
public class StockInTempApply extends NamingRuleModel {
    @Override
    public String getNamingCode() {
        return NamingRuleCodeEnum.StockInApplyNumber.name();
    }

    @FabosJsonField(
            views = @View(title = "申请类型", show = false),
            edit = @Edit(title = "申请类型", show = false)
    )
    private String applyType;

/*    @FabosJsonField(
            views = @View(title = "入库对象",show = false),
            edit = @Edit(title = "入库对象", type = EditType.CHOICE,readonly = @Readonly(),
                    search = @Search(),
                    choiceType = @ChoiceType(
                            fetchHandler = StockInObjTypeEnum.class
                    )
            )
    )
    private String stockInObj;*/

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "暂存单位", column = "name", type = ViewType.TABLE_FORM),
            edit = @Edit(title = "暂存单位", type = EditType.REFERENCE_TABLE, allowAddMultipleRows = false,
                    referenceTreeType = @ReferenceTreeType(pid = "parentOrg.id"), search = @Search(), placeHolder = "请输入", notNull = true)
    )
    private OrgMTO unit;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "申请部门", column = "name", type = ViewType.TABLE_FORM),
            edit = @Edit(title = "申请部门", type = EditType.REFERENCE_TABLE, allowAddMultipleRows = false,
                    referenceTreeType = @ReferenceTreeType(pid = "parentOrg.id"), search = @Search(), placeHolder = "请输入", notNull = true)
    )
    private OrgMTO org;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "申请日期", type = ViewType.DATE),
            edit = @Edit(title = "申请日期", show = false, search = @Search(vague = true),
                    dateType = @DateType(type = DateType.Type.DATE))
    )
    private Date applyDate;

    @FabosJsonField(
            views = @View(title = "附件"),
            edit = @Edit(title = "附件", type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(maxLimit = 10))
    )
    private String attachment;


    @FabosJsonField(
            views = @View(title = "备注"),
            edit = @Edit(title = "备注", type = EditType.TEXTAREA)
    )
    private String remark;

    @FabosJsonField(
            views = @View(title = "处理状态"),
            edit = @Edit(title = "处理状态", type = EditType.CHOICE, show = false,
                    search = @Search(),
                    choiceType = @ChoiceType(
                            fetchHandler = OrderApplyProcessStateEnum.class
                    )
            )
    )
    private String processState;

    @FabosJsonField(
            views = @View(title = "当前状态"),
            edit = @Edit(title = "当前状态", type = EditType.CHOICE, show = false,
                    search = @Search(),
                    choiceType = @ChoiceType(
                            fetchHandler = OrderCurrentStateEnum.class
                    )
            )
    )
    private String currentState;


    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = false)
    @JoinColumn(name = "stock_in_apply_id")
    @OrderBy
    @FabosJsonField(
            edit = @Edit(title = "暂存物品", type = EditType.TAB_REFER_ADD),
            referenceAddType = @ReferenceAddType(referenceClass = "StockView",
                    editable = {"manufacturer", "lotNumber", "quantity", "manufactureDate", "expireDate", "certificateNumber"},
                    referenceAddHandler = StockInTempApplyReferenceAddHandler.class),
            views = @View(title = "暂存物品", type = ViewType.TABLE_VIEW, extraPK = "materialId")
    )
    private List<StockInTempApplyDetail> details;

}
