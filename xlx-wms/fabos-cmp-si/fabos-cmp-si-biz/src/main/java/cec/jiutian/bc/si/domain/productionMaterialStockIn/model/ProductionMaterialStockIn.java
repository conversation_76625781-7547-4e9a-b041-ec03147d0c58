package cec.jiutian.bc.si.domain.productionMaterialStockIn.model;

import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleNoExamineModel;
import cec.jiutian.bc.generalModeler.domain.warehouseArea.model.Warehouse;
import cec.jiutian.bc.si.domain.productionMaterialStockIn.handler.ProductionMaterialStockInCompleteHandler;
import cec.jiutian.bc.si.domain.productionMaterialStockIn.handler.ProductionMaterialStockInDetailHandler;
import cec.jiutian.bc.si.domain.productionMaterialStockIn.handler.ProductionMaterialStockInReleaseHandler;
import cec.jiutian.bc.si.domain.productionMaterialStockIn.proxy.ProductionMaterialStockInProxy;
import cec.jiutian.bc.si.domain.stockInRequest.model.MaterialReturnRequest;
import cec.jiutian.bc.si.enumeration.NamingRuleCodeEnum;
import cec.jiutian.bc.si.enumeration.StockInCategoryEnum;
import cec.jiutian.bc.si.enumeration.WarehouseCategoryEnum;
import cec.jiutian.bc.si.port.dto.OrgMTO;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.ReferenceGenerateType;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowBaseOperation;
import cec.jiutian.view.type.RowOperation;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@FabosJson(
        name = "生产退料入库",
        orderBy = "ProductionMaterialStockIn.createTime desc",
        dataProxy = ProductionMaterialStockInProxy.class,
        filter = @Filter(value = "ProductionMaterialStockIn.type = 'ProductionMaterial'"),
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "currentState!='EDIT'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "currentState!='EDIT'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                )
        },
        rowOperation = {
                @RowOperation(
                        title = "发布",
                        code = "ProductionMaterialStockIn@RELEASE",
                        mode = RowOperation.Mode.SINGLE,
                        operationHandler = ProductionMaterialStockInReleaseHandler.class,
                        ifExpr = "currentState!='EDIT'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
                        callHint = "是否确定操作？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "ProductionMaterialStockIn@RELEASE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
                @RowOperation(
                        title = "完成",
                        code = "ProductionMaterialStockIn@COMPLETE",
                        mode = RowOperation.Mode.SINGLE,
                        operationHandler = ProductionMaterialStockInCompleteHandler.class,
                        ifExpr = "currentState!='EXECUTE' || warehouseCategory!='Flat'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
                        callHint = "是否确定操作？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "ProductionMaterialStockIn@COMPLETE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                )
        },
        power = @Power(export=false,print=false)
)
@Table(name = "purchase_stock_in")
@Entity
@Getter
@Setter
@TemplateType(type = "multiTable")
public class ProductionMaterialStockIn extends NamingRuleNoExamineModel {

    @Override
    public String getNamingCode() {
        return NamingRuleCodeEnum.StockInNumber.name();
    }

    @FabosJsonField(
            views = @View(title = "类型"),
            edit = @Edit(title = "类型", readonly = @Readonly,
                    search = @Search, type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = StockInCategoryEnum.class), defaultVal = "ProductionMaterial")
    )
    private String type;

    @FabosJsonField(
            views = @View(title = "仓库类型"),
            edit = @Edit(title = "仓库类型", notNull = true,
                    search = @Search, type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = WarehouseCategoryEnum.class))
    )
    private String warehouseCategory;

    @FabosJsonField(
            views = @View(title = "仓库", column = "name"),
            edit = @Edit(title = "仓库",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    filter = @Filter(value = "Warehouse.lockState = 'Normal'"),
                    allowAddMultipleRows = false,
                    queryCondition = "{\"type\": \"${warehouseCategory}\"}"
            )
    )
    @ManyToOne
    @JoinColumn(name = "warehouse_id")
    private Warehouse warehouse;

    @FabosJsonField(
            views = @View(title = "申请单号", column = "generalCode"),
            edit = @Edit(title = "申请单号",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    readonly = @Readonly(add = false),
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode"),
                    filter = @Filter(value = "MaterialReturnRequest.processState = 'AwaitStockIn' and MaterialReturnRequest.currentState = 'EXECUTE'"),
                    allowAddMultipleRows = false
            )
    )
    @ManyToOne
    @JoinColumn(name = "material_return_request_id")
    private MaterialReturnRequest materialReturnRequest;

    @FabosJsonField(
            views = @View(title = "申请部门", show = false),
            edit = @Edit(title = "申请部门", readonly = @Readonly,
                    notNull = true)
            ,
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "materialReturnRequest", beFilledBy = "org_name"))
    )
    @Transient
    private String orgName;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "申请部门", column = "name", type = ViewType.TABLE_FORM),
            edit = @Edit(title = "申请部门", type = EditType.REFERENCE_TABLE, allowAddMultipleRows = false, show = false,
                    search = @Search(), placeHolder = "请选择", notNull = true)
            ,
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "materialReturnRequest", beFilledBy = "org"))
    )
    private OrgMTO org;

    @FabosJsonField(
            views = @View(title = "当前状态"),
            edit = @Edit(title = "当前状态", show = false, type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = OrderCurrentStateEnum.class))
    )
    private String currentState;


    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.EAGER)
    @JoinColumn(name = "purchase_stock_in_id")
    @FabosJsonField(
            views = @View(title = "生产退料入库明细", type = ViewType.TABLE_VIEW),
            edit = @Edit(title = "生产退料入库明细", type = EditType.TAB_REFERENCE_GENERATE, queryConditionChild = "{\"warehouseBlock.warehouse\": \"${warehouse}\"}"),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = {"materialReturnRequest","warehouse"},
                    dynamicHandler = ProductionMaterialStockInDetailHandler.class)),
            referenceGenerateType = @ReferenceGenerateType(editable = {"remark", "warehouseBlock", "warehouseShelf"})
    )
    private List<ProductionMaterialStockInDetail> productionMaterialStockInDetailList;

    @FabosJsonField(
            views = @View(title = "来源单号", show = false),
            edit = @Edit(title = "来源单号", show = false)
    )
    private String originCode;
}
