package cec.jiutian.bc.si.domain.sampleReturnStockIn.handler;

import cec.jiutian.bc.si.domain.sampleReturnStockIn.model.SampleReturnStockIn;
import cec.jiutian.bc.si.domain.sampleReturnStockIn.model.SampleReturnStockInDetail;
import cec.jiutian.bc.si.enumeration.WcsWarehouseEnum;
import cec.jiutian.bc.si.remote.dto.StockInRequestDTO;
import cec.jiutian.bc.si.remote.dto.StockInRequestDetailDTO;
import cec.jiutian.bc.si.remote.dto.WcsResultDTO;
import cec.jiutian.bc.si.remote.useWcs.WcsStockInFeignClient;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.common.util.StringUtils;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.data.jpa.JpaCrud;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date: 2025/3/10
 */
@Component
public class SampleReturnStockInReleaseHandler implements OperationHandler<SampleReturnStockIn, Void> {
    @Resource
    private JpaCrud jpaCrud;

    @Resource
    private WcsStockInFeignClient wcsStockInFeignClient;

    @Override
    public String exec(List<SampleReturnStockIn> sampleReturnStockInList, Void modelObject, String[] param) {
        if(CollectionUtils.isNotEmpty(sampleReturnStockInList)) {
            SampleReturnStockIn sampleReturnStockIn = sampleReturnStockInList.get(0);
            sampleReturnStockIn.setCurrentState(OrderCurrentStateEnum.Enum.EXECUTE.name());
            jpaCrud.update(sampleReturnStockIn);
            //与立库系统对接
            createStockInRequest(sampleReturnStockIn);
        }
        return "msg.success('操作成功')";
    }

    private boolean createStockInRequest(SampleReturnStockIn sampleReturnStockIn){
        StockInRequestDTO paramsDTO=new StockInRequestDTO();
        paramsDTO.setErpInId(sampleReturnStockIn.getId());
        paramsDTO.setStockInNo(sampleReturnStockIn.getGeneralCode());
        paramsDTO.setStockInTypeName("生产入库单");
        paramsDTO.setBillSource("WMS");
        paramsDTO.setCreateByName(sampleReturnStockIn.getCreateBy());
        paramsDTO.setStatus("A");
        paramsDTO.setCreateDate(Date.from(sampleReturnStockIn.getCreateTime().atZone(ZoneId.systemDefault()).toInstant()));
        List<SampleReturnStockInDetail> stockInDetailList=sampleReturnStockIn.getSampleReturnStockInDetailList();
        if(CollectionUtils.isEmpty(stockInDetailList)){
            throw new FabosJsonApiErrorTip("详情单不能为空");
        }
        Map<String,Double> stockInMap = stockInDetailList.stream().collect(Collectors.groupingBy(SampleReturnStockInDetail::getFactoryLotIdentifier,Collectors.summingDouble(SampleReturnStockInDetail::getQuantity)));
        List<StockInRequestDetailDTO> detailDTOS= stockInDetailList.stream().map(it->{
            StockInRequestDetailDTO detailsDTO=new StockInRequestDetailDTO();
            detailsDTO.setErpInDetailId(it.getId());
            detailsDTO.setBatch(it.getFactoryLotIdentifier());
            detailsDTO.setMaterialNo(it.getMaterialCode());
            detailsDTO.setMaterialName(it.getMaterialName());
            if(it.getWarehouse()!=null){
                detailsDTO.setLgort(WcsWarehouseEnum.getWarehouseIdByCode(it.getWarehouse().getCode()));
            }
            detailsDTO.setUnit(it.getUnit());
            detailsDTO.setBoxNo(StringUtils.isBlank(it.getBoxNo())?null:Integer.valueOf(it.getBoxNo()));
            detailsDTO.setPlanInQty(new BigDecimal(stockInMap.get(it.getFactoryLotIdentifier())));
            detailsDTO.setBoxQty(new BigDecimal(it.getQuantity()));
            return detailsDTO;
        }).collect(Collectors.toList());
        paramsDTO.setLines(detailDTOS);
        WcsResultDTO resultDTO= wcsStockInFeignClient.createStockInRequest(paramsDTO);
        if(resultDTO.getCode()==null||resultDTO.getCode().intValue()!=200){
            throw new FabosJsonApiErrorTip("调用WCS失败!message:"+resultDTO.getMessage());
        }
        return true;
    }
}
