package cec.jiutian.bc.si.domain.stockInFinishApply.handler;

import cec.jiutian.bc.generalModeler.domain.material.model.FinalProductDTM;
import cec.jiutian.bc.generalModeler.domain.material.model.Material;
import cec.jiutian.bc.generalModeler.domain.measureUnit.model.MeasureUnit;
import cec.jiutian.bc.si.domain.stockInFinishApply.model.StockInFinishApplyAddForMaterial;
import cec.jiutian.bc.si.domain.stockInFinishApply.model.StockInFinishApplyDetail;
import cec.jiutian.data.jpa.JpaCrud;
import cec.jiutian.view.ReferenceAddType;
import cn.hutool.core.util.ObjectUtil;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date ：2024/11/18 17:35
 * @description：
 */
@Component
public class StockInFinishApplyReferenceAddMaterialHandler implements ReferenceAddType.ReferenceAddHandler<StockInFinishApplyAddForMaterial, Material> {

    @Resource
    private JpaCrud jpaCrud;

    @Override
    public Map<String, Object> handle(StockInFinishApplyAddForMaterial stockInFinishApply, List<Material> materialList) {
        Map<String, Object> result = new HashMap<>();
        AtomicInteger index = new AtomicInteger(1);
        List<StockInFinishApplyDetail> details = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(materialList)) {
            materialList.forEach(c -> {
                StockInFinishApplyDetail stockInFinishApplyDetail = new StockInFinishApplyDetail();
                stockInFinishApplyDetail.setStockInApplyDetailNumber(stockInFinishApply.getGeneralCode() + "-" + String.format("%03d", index.get()));
                //stockInFinishApplyDetail.setMaterialOrProductId(c.getId());
                stockInFinishApplyDetail.setMaterialCode(c.getMaterialCode());
                stockInFinishApplyDetail.setMaterialName(c.getName());
                stockInFinishApplyDetail.setProductNo(c.getWl16());
                stockInFinishApplyDetail.setMaterialSpecification(c.getMaterialSpecification());
                Material material = new Material();
                material.setMaterialCode(c.getMaterialCode());
                List<Material> select = jpaCrud.select(material);
                if (CollectionUtils.isNotEmpty(select)) {
                    MeasureUnit accountUnit = select.get(0).getAccountUnit();
                    stockInFinishApplyDetail.setUnitChnName(ObjectUtil.isNotNull(accountUnit) ? accountUnit.getUnitChnName() : "");
                    List<FinalProductDTM> useForProductlist = select.get(0).getUseForProductlist();
                    stockInFinishApplyDetail.setUseForProductlist(useForProductlist);
                }
                stockInFinishApplyDetail.setManufacturer(c.getWl27());
                stockInFinishApplyDetail.setStandardNumber(c.getWl09());
                index.getAndIncrement();
                details.add(stockInFinishApplyDetail);
            });
            result.put("details", details);
        }
        return result;
    }
}
