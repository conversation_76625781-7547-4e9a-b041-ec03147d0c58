package cec.jiutian.bc.si.domain.stockInTempApply.proxy;

import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.si.domain.stockInTempApply.model.StockInTempApply;
import cec.jiutian.bc.si.enumeration.OrderApplyProcessStateEnum;
import cec.jiutian.bc.si.enumeration.StockInTypeEnum;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.data.jpa.JpaCrud;
import cec.jiutian.view.fun.DataProxy;
import cn.hutool.core.date.DateUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.concurrent.atomic.AtomicInteger;

@Component
public class StockInTempApplyProxy implements DataProxy<StockInTempApply> {

    @Resource
    private JpaCrud jpaCrud;

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private NamingRuleService namingRuleService;

    @Override
    public void beforeAdd(StockInTempApply stockInTempApply) {
        //stockInTempApply.setGeneralCode(namingRuleService.getNameCode(NamingRuleCodeEnum.StockInApplyNumber.name(), 1, null).get(0));
        stockInTempApply.setApplyType(StockInTypeEnum.Enum.Temp.name());
        stockInTempApply.setCurrentState(OrderCurrentStateEnum.Enum.EDIT.name());
        stockInTempApply.setProcessState(OrderApplyProcessStateEnum.Enum.AwaitStockIn.name());
        stockInTempApply.setApplyDate(DateUtil.beginOfDay(DateUtil.date()));
        handleDetailAdd(stockInTempApply);
    }

    private void handleDetailAdd(StockInTempApply stockInTempApply) {
        if (!CollectionUtils.isEmpty(stockInTempApply.getDetails())) {
            AtomicInteger index = new AtomicInteger(1);
            stockInTempApply.getDetails().forEach(d -> {
                d.setStockInApplyDetailNumber(stockInTempApply.getGeneralCode() + "-" + String.format("%03d", index.get()));
                d.setSequenceNumber(index.get());
                d.setCreateBy(stockInTempApply.getCreateBy());
                d.setCreateTime(LocalDateTime.now());
                d.setUpdateBy(stockInTempApply.getUpdateBy());
                d.setUpdateTime(LocalDateTime.now());
                index.getAndIncrement();
            });
        }
    }

    @Override
    public void afterAdd(StockInTempApply stockInTempApply) {


    }

    @Override
    public void beforeUpdate(StockInTempApply stockInTempApply) {
        // 校验
        if (!stockInTempApply.getCurrentState().equals(OrderCurrentStateEnum.Enum.EDIT.name())) {
            throw new FabosJsonApiErrorTip("非开立状态不允许编辑");
        }
    }

    @Override
    public void beforeDelete(StockInTempApply stockInTempApply) {
        // 校验
        if (!stockInTempApply.getCurrentState().equals(OrderCurrentStateEnum.Enum.EDIT.name())) {
            throw new FabosJsonApiErrorTip("非开立状态不允许删除");
        }
    }

    @Override
    public void afterDelete(StockInTempApply stockInTempApply) {

    }
}
