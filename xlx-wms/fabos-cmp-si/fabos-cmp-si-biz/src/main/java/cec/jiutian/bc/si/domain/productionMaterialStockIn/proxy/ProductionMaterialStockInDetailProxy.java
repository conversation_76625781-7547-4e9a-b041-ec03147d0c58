package cec.jiutian.bc.si.domain.productionMaterialStockIn.proxy;

import cec.jiutian.bc.si.domain.productionMaterialStockIn.model.ProductionMaterialStockInDetail;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.data.jpa.JpaCrud;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date: 2025/3/7
 */
@Component
public class ProductionMaterialStockInDetailProxy implements DataProxy<ProductionMaterialStockInDetail> {
    @Resource
    private JpaCrud jpaCrud;

    @Resource
    private FabosJsonDao fabosJsonDao;
}
