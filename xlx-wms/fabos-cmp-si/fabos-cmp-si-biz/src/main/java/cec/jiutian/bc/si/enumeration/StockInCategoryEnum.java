package cec.jiutian.bc.si.enumeration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date: 2025/3/3
 */
public class StockInCategoryEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum data : Enum.values()) {
            list.add(new VLModel(data.name(), data.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        Purchase("采购入库"),
        Product("成品入库"),
        ProductionMaterial("生产退料"),
        SampleReturn("样品归还"),
        ShipLot("发货批次"),
        Allocate("调拨入库"),
        InventoryProfit("盘盈入库"),
        Sample("样品入库"),
        Return("产品退货入库"),
        Scrap("报废入库")
        ;

        private final String value;

    }
}
