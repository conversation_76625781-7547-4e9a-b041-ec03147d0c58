package cec.jiutian.bc.si.domain.productStockIn.handler;

import cec.jiutian.bc.generalModeler.domain.material.model.Material;
import cec.jiutian.bc.generalModeler.domain.measureUnit.model.MeasureUnit;
import cec.jiutian.bc.library.domain.inventory.model.Inventory;
import cec.jiutian.bc.library.domain.inventory.model.InventoryAutoBoxedDetail;
import cec.jiutian.bc.library.enumeration.InventoryCurrentStateEnum;
import cec.jiutian.bc.si.domain.productStockIn.model.ProductStockIn;
import cec.jiutian.bc.si.domain.productStockIn.port.client.OrderFeignClient;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.common.util.BeanUtils;
import cec.jiutian.common.util.StringUtils;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@Component
public class ProductStockInCompleteHandler implements OperationHandler<ProductStockIn, Void> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private OrderFeignClient orderFeignClient;

    /*
     * 成品入库完成
     * 1：库存批次添加
     * 2：更新MES中的 生产工单、生产计划产出量
     */
    @Override
    @Transactional
    public String exec(List<ProductStockIn> productStockInList, Void modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(productStockInList)) {
            ProductStockIn productStockIn = productStockInList.get(0);
            stockInComplete(productStockIn);
        }
        return "msg.success('操作成功')";
    }

    public boolean stockInComplete(ProductStockIn productStockIn){
        productStockIn.setCurrentState(OrderCurrentStateEnum.Enum.COMPLETE.name());
        Inventory inventory = new Inventory();
        BeanUtils.copyProperties(productStockIn, inventory);
        inventory.setLotSerialId(productStockIn.getProductBatch().getSerialNumber());
        inventory.setWarehouseId(productStockIn.getWarehouse().getId());
        inventory.setWarehouseName(productStockIn.getWarehouse().getName());
        if (null != productStockIn.getWarehouseBlock()) {
            inventory.setBlockId(productStockIn.getWarehouseBlock().getId());
            inventory.setBlockName(productStockIn.getWarehouseBlock().getName());
        }
        if (null != productStockIn.getWarehouseShelf()) {
            inventory.setShelfId(productStockIn.getWarehouseShelf().getId());
            inventory.setShelfName(productStockIn.getWarehouseShelf().getName());
        }
        inventory.setId(null);
        inventory.setAccountingUnitQuantity(productStockIn.getQuantity());
        inventory.setLotAllQuantity(productStockIn.getQuantity());
        inventory.setAvailableQuantity(productStockIn.getQuantity());
        inventory.setAccountingUnit(productStockIn.getUnit());
        inventory.setCurrentState(InventoryCurrentStateEnum.Enum.waitInspect.name());
        inventory.setStockType(productStockIn.getMaterialCategory());
        List<InventoryAutoBoxedDetail> inventoryAutoBoxedDetailList=new ArrayList<>();
        InventoryAutoBoxedDetail autoBoxedDetail=new InventoryAutoBoxedDetail();
        AtomicInteger index = new AtomicInteger(1);
        autoBoxedDetail.setMaterialCode(productStockIn.getMaterialCode());
        autoBoxedDetail.setMaterialName(productStockIn.getMaterialName());
        autoBoxedDetail.setMaterialSpecification(productStockIn.getMaterialSpecification());
        if(StringUtils.isNotEmpty(productStockIn.getMaterialCode())){
            Material materialQuery=new Material();
            materialQuery.setMaterialCode(productStockIn.getMaterialCode());
            Material material= fabosJsonDao.selectOne(materialQuery);
            autoBoxedDetail.setMaterialCategory(material==null?null:material.getMaterialCategory());
            autoBoxedDetail.setMaterialId(material==null?null:material.getId());
        }
        autoBoxedDetail.setWarehouseId(productStockIn.getWarehouse()==null?null:productStockIn.getWarehouse().getId());
        autoBoxedDetail.setWarehouseName(productStockIn.getWarehouse()==null?null:productStockIn.getWarehouse().getName());
        autoBoxedDetail.setShelfId(productStockIn.getWarehouseShelf()==null?null:productStockIn.getWarehouseShelf().getId());
        autoBoxedDetail.setShelfName(productStockIn.getWarehouseShelf()==null?null:productStockIn.getWarehouseShelf().getName());
        autoBoxedDetail.setBlockId(productStockIn.getWarehouseBlock()==null?null:productStockIn.getWarehouseBlock().getId());
        autoBoxedDetail.setBlockName(productStockIn.getWarehouseBlock()==null?null:productStockIn.getWarehouseBlock().getName());
        autoBoxedDetail.setLotSerialId(productStockIn.getProductBatch().getSerialNumber());
        autoBoxedDetail.setBoxedCode(autoBoxedDetail.getLotSerialId() + "-" + String.format("%d", index.get()));
        autoBoxedDetail.setBoxNo(String.valueOf(index.get()));
        autoBoxedDetail.setQuantity(productStockIn.getQuantity());
        productStockIn.setBoxedCode(autoBoxedDetail.getBoxedCode());
        productStockIn.setBoxNo(autoBoxedDetail.getBoxNo());
        if(StringUtils.isNotEmpty(inventory.getAccountingUnit())){
            MeasureUnit unitQuery=new MeasureUnit();
            unitQuery.setUnitChnName(inventory.getAccountingUnit());
            List<MeasureUnit> unitList= fabosJsonDao.select(unitQuery);
            if(CollectionUtils.isNotEmpty(unitList)){
                autoBoxedDetail.setAccountUnit(unitList.get(0));
            }
        }
        inventoryAutoBoxedDetailList.add(autoBoxedDetail);
        inventory.setInventoryAutoBoxedDetailList(inventoryAutoBoxedDetailList);
        fabosJsonDao.insert(inventory);
        List<Map<String, Object>> mapList = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();
        if(StringUtils.isBlank(productStockIn.getProductionOrder())){
            throw new FabosJsonApiErrorTip("生产工单为空");
        }
        map.put("productionOrderNumber", productStockIn.getProductionOrder());
        map.put("addQuantity", productStockIn.getQuantity());
        mapList.add(map);
        orderFeignClient.updateProductionPlanAndOrderQuantity(mapList);
        fabosJsonDao.update(productStockIn);
        return true;
    }
}
