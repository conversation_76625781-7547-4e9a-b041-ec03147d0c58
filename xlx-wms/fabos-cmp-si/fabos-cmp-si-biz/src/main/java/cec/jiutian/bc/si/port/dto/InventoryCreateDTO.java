package cec.jiutian.bc.si.port.dto;

import cec.jiutian.view.config.Comment;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class InventoryCreateDTO {

    @Comment("物料名称")
    private String materialName;

    @Comment("物料编码")
    private String materialCode;

    @Comment("规格")
    private String materialSpecification;

    @Comment("用途")
    private String useWay;

    @Comment("单位")
    private String measureUnit;

    @Comment("原炉/原厂批号")
    private String originLotId;

    @Comment("原厂合格证")
    private String originCertificate;

    @Comment("本厂批号")
    private String serialLotId;

    @Comment("本厂合格证")
    private String newCertificate;

    @Comment("供应商")
    private String supplier;

    @Comment("生产日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private Date manufactureDate;

    @Comment("有效期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private Date expireDate;

    @Comment("入库数量")
    private Double stockInQuantity;

    @Comment("库房ID")
    private String warehouseId;

    @Comment("库区ID")
    private String warehouseBlockId;

    @Comment("货位ID")
    private String warehouseShelfId;

}
