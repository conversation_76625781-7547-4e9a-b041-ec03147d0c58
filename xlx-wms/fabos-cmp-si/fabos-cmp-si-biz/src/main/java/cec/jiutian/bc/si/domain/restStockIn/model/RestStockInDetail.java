package cec.jiutian.bc.si.domain.restStockIn.model;

import cec.jiutian.bc.generalModeler.domain.warehouseArea.model.Warehouse;
import cec.jiutian.bc.generalModeler.domain.warehouseArea.model.WarehouseBlock;
import cec.jiutian.bc.generalModeler.domain.warehouseArea.model.WarehouseShelf;
import cec.jiutian.bc.generalModeler.enumeration.YesOrNoEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Table(name = "purchase_stock_in_detail")
@FabosJson(
        name = "其他入库详情",
        orderBy = "RestStockInDetail.createTime desc"
)
@Entity
@Getter
@Setter
public class RestStockInDetail extends MetaModel {

    @FabosJsonField(
            views = @View(title = "入库明细编号"),
            edit = @Edit(title = "采购入库明细编号", show = false,
                    readonly = @Readonly(add = false),
                    search = @Search(vague = true),
                    inputType = @InputType(length = 20))
    )
    private String purchaseStockInDetailCode;

    @FabosJsonField(
            views = {
                    @View(title = "其他入库编号", column = "generalCode")
            },
            edit = @Edit(title = "其他入库编号", type = EditType.REFERENCE_TABLE, show = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode"))
    )
    @ManyToOne
    @JsonIgnoreProperties("restStockInDetailList")
    @JoinColumn(name = "purchase_stock_in_id")
    private RestStockIn restStockIn;

    @FabosJsonField(
            views = @View(title = "来源单明细编号",show = false)
    )
    private String originDetailCode;

    @FabosJsonField(
            views = @View(title = "物料主键id", show = false),
            edit = @Edit(title = "物料主键id", show = false)
    )
    private String materialId;

    @FabosJsonField(
            views = @View(title = "物料编码", show = false),
            edit = @Edit(title = "物料编码", show = false)
    )
    private String materialCode;

    @FabosJsonField(
            views = @View(title = "物料名称"),
            edit = @Edit(title = "物料名称", readonly = @Readonly)
    )
    private String materialName;

    @FabosJsonField(
            views = @View(title = "种类"),
            edit = @Edit(title = "种类", readonly = @Readonly)
    )
    private String materialCategory;

    @FabosJsonField(
            views = @View(title = "规格"),
            edit = @Edit(title = "规格", readonly = @Readonly)
    )
    private String materialSpecification;

    @FabosJsonField(
            views = @View(title = "来料批号"),
            edit = @Edit(title = "来料批号", readonly = @Readonly)
    )
    private String materialLotIdentifier;

    @FabosJsonField(
            views = @View(title = "本厂批号"),
            edit = @Edit(title = "本厂批号", readonly = @Readonly)
    )
    private String factoryLotIdentifier;

    @FabosJsonField(
            views = @View(title = "入库数量"),
            edit = @Edit(title = "入库数量", notNull = true, readonly = @Readonly,
                    numberType = @NumberType(min = 0, max = 9999999, precision = 3))
    )
    private Double quantity;

    @FabosJsonField(
            views = @View(title = "单位"),
            edit = @Edit(title = "单位", readonly = @Readonly)
    )
    private String unit;

    @FabosJsonField(
            views = @View(title = "仓库", show = false),
            edit = @Edit(title = "仓库", show = false)
    )
    @ManyToOne
    @JoinColumn(name = "warehouse_id")
    private Warehouse warehouse;

    @FabosJsonField(
            views = @View(title = "库区", column = "name", rowEdit = true),
            edit = @Edit(title = "库区",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    filter = @Filter(value = "WarehouseBlock.lockState = 'Normal'"),
                    allowAddMultipleRows = false,
                    queryCondition = "{\"warehouse.id\": \"${warehouse.id}\"}"
            )
    )
    @ManyToOne
    @JoinColumn(name = "warehouse_block_id")
    private WarehouseBlock warehouseBlock;

    @FabosJsonField(
            views = @View(title = "库位", column = "name", rowEdit = true),
            edit = @Edit(title = "库位",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    filter = @Filter(value = "WarehouseShelf.lockState = 'Normal'"),
                    allowAddMultipleRows = false,
                    queryCondition = "{\"warehouseBlock.id\": \"${warehouseBlock.id}\"}"
            )
    )
    @ManyToOne
    @JoinColumn(name = "warehouse_shelf_id")
    private WarehouseShelf warehouseShelf;

    @FabosJsonField(
            views = @View(title = "备注"),
            edit = @Edit(title = "备注",
                    inputType = @InputType(length = 200))
    )
    private String remark;

    @FabosJsonField(
            views = @View(title = "是否样品"),
            edit = @Edit(title = "是否样品", readonly = @Readonly, type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = YesOrNoEnum.class))
    )
    private String sampleFlag;

    @FabosJsonField(
            views = @View(title = "是否入库",show = false),
            edit = @Edit(title = "是否入库", show = false)
    )
    private String stockInFlag;
}
