package cec.jiutian.bc.si.domain.scrapStockIn.handler;

import cec.jiutian.bc.si.domain.productStockIn.model.ProductStockIn;
import cec.jiutian.bc.si.domain.scrapStockIn.model.ScrapStockIn;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.data.jpa.JpaCrud;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class ScrapStockInReleaseHandler implements OperationHandler<ScrapStockIn,Void> {
    @Resource
    private FabosJsonDao fabosJsonDao;


    @Override
    public String exec(List<ScrapStockIn> scrapStockInList, Void modelObject, String[] param) {
        if(CollectionUtils.isNotEmpty(scrapStockInList)) {
            ScrapStockIn scrapStockIn = scrapStockInList.get(0);
            scrapStockIn.setCurrentState(OrderCurrentStateEnum.Enum.EXECUTE.name());
            fabosJsonDao.update(scrapStockIn);
        }
        return "msg.success('操作成功')";
    }
}
