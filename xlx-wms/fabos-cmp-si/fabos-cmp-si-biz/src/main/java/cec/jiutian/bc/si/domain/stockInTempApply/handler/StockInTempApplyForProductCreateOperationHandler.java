package cec.jiutian.bc.si.domain.stockInTempApply.handler;

import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.si.domain.stockInTempApply.model.StockInTempApply;
import cec.jiutian.bc.si.domain.stockInTempApply.model.StockInTempApplyAddForProduct;
import cec.jiutian.data.jpa.JpaCrud;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class StockInTempApplyForProductCreateOperationHandler implements OperationHandler<StockInTempApply, StockInTempApplyAddForProduct> {

    @Resource
    private JpaCrud jpaCrud;

    @Resource
    private NamingRuleService namingRuleService;

    @Override
    public String exec(List<StockInTempApply> data, StockInTempApplyAddForProduct modelObject, String[] param) {
        return null;
    }

    @Override
    public StockInTempApplyAddForProduct fabosJsonFormValue(List<StockInTempApply> data, StockInTempApplyAddForProduct fabosJsonForm, String[] param) {
/*        fabosJsonForm.setNumber(namingRuleService.getNameCode(NamingRuleCodeEnum.IqcNumber.name(), 1, null).get(0));
        fabosJsonForm.setCurrentState(IqcCurrentStateEnum.Enum.EDIT.name());
        fabosJsonForm.setStockInObj(StockInObjTypeEnum.Enum.Product.name());*/
        jpaCrud.insert(fabosJsonForm);
        return fabosJsonForm;
    }

}
