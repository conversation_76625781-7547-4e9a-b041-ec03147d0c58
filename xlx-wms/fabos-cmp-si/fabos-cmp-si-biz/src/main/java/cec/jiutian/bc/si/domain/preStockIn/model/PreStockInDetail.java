package cec.jiutian.bc.si.domain.preStockIn.model;

import cec.jiutian.bc.generalModeler.domain.warehouseArea.model.Warehouse;
import cec.jiutian.bc.generalModeler.domain.warehouseArea.model.WarehouseBlock;
import cec.jiutian.bc.generalModeler.domain.warehouseArea.model.WarehouseShelf;
import cec.jiutian.bc.generalModeler.enumeration.ProductEnum;
import cec.jiutian.bc.generalModeler.enumeration.StockTypeEnum;
import cec.jiutian.bc.si.domain.preStockIn.handler.PreStockInDetailAmountDynamicHandler;
import cec.jiutian.bc.si.domain.preStockIn.proxy.PreStockInDetailProxy;
import cec.jiutian.bc.si.util.SiMaterialOrProductFormHandler;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Table(name = "bwi_pre_stock_in_detail")
@FabosJson(
        name = "预入库明细",
        dataProxy = PreStockInDetailProxy.class
)
@Entity
@Getter
@Setter
public class PreStockInDetail extends MetaModel {

    @FabosJsonField(
            views = @View(title = "入库明细编号"),
            edit = @Edit(title = "入库明细编号", show = false)
    )
    private String stockInDetailNumber;

    @FabosJsonField(
            views = @View(title = "顺序号"),
            edit = @Edit(title = "顺序号", show = false)
    )
    private Integer sequenceNumber;

    @FabosJsonField(
            views = @View(title = "类别"),
            edit = @Edit(title = "类别", notNull = false,
                    type = EditType.CHOICE
                    , search = @Search(vague = true),
                    choiceType = @ChoiceType(fetchHandler = StockTypeEnum.class)
            )
    )
    private String stockType;

    @FabosJsonField(
            views = @View(title = "子类别"),
            edit = @Edit(title = "子类别", notNull = false, type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ProductEnum.class),
                    readonly = @Readonly(add = true, edit = true),
                    search = @Search(vague = true))
    )
    private String stockSubType;

    @FabosJsonField(
            views = @View(title = "库存批次台账主键id", show = false),
            edit = @Edit(title = "库存批次台账主键id", show = false)
    )
    private String inventoryId;

    @ManyToOne
    @FabosJsonField(
            views = {
                    @View(title = "入库单号", column = "generalCode")
            },
            edit = @Edit(title = "入库单号", type = EditType.REFERENCE_TABLE, show = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode")
            )
    )
    @JsonIgnoreProperties("details")
    private PreStockIn preStockIn;

    @FabosJsonField(
            views = @View(title = "物料主键id", show = false),
            edit = @Edit(title = "物料主键id", show = false)
    )
    private String materialId;

    @FabosJsonField(
            views = @View(title = "物料名称", type = ViewType.TABLE_FORM, tableFormParamsHandler = SiMaterialOrProductFormHandler.class),
            edit = @Edit(title = "物料名称", readonly = @Readonly)
    )
    private String materialName;

    @FabosJsonField(
            views = @View(title = "物料编码"),
            edit = @Edit(title = "物料编码", readonly = @Readonly)
    )
    private String materialCode;

    @FabosJsonField(
            views = @View(title = "规格"),
            edit = @Edit(title = "规格", readonly = @Readonly)
    )
    private String materialSpecification;

    @FabosJsonField(
            views = @View(title = "品号"),
            edit = @Edit(title = "品号", readonly = @Readonly)
    )
    private String articleNumber;

    @FabosJsonField(
            views = @View(title = "用途"),
            edit = @Edit(title = "用途", readonly = @Readonly)
    )
    private String purpose;

    @FabosJsonField(
            views = @View(title = "单位"),
            edit = @Edit(title = "单位", readonly = @Readonly)
    )
    private String measureUnit;

    @FabosJsonField(
            views = @View(title = "原炉/原厂批号"),
            edit = @Edit(title = "原炉/原厂批号", readonly = @Readonly)
    )
    private String originLotId;


    @FabosJsonField(
            views = @View(title = "本厂批号"),
            edit = @Edit(title = "本厂批号", readonly = @Readonly)
    )
    private String serialLotId;
//
//    @FabosJsonField(
//            views = @View(title = "合格证"),
//            edit = @Edit(title = "合格证", readonly = @Readonly)
//    )
//    private String newCertificate;

    @FabosJsonField(
            views = @View(title = "供应商"),
            edit = @Edit(title = "供应商", readonly = @Readonly())
    )
    private String supplier;

    @FabosJsonField(
            views = @View(title = "生产单位"),
            edit = @Edit(title = "生产单位", readonly = @Readonly)
    )
    private String manufacturer;

    // todo （采购类型）物资采购合同、采购计划号

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "生产日期", type = ViewType.DATE),
            edit = @Edit(title = "生产日期", notNull = true, type = EditType.DATE, readonly = @Readonly)
    )
    private Date manufactureDate;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "有效日期", type = ViewType.DATE),
            edit = @Edit(title = "有效日期", notNull = true, type = EditType.DATE, readonly = @Readonly)
    )
    private Date expireDate;

    @FabosJsonField(
            views = @View(title = "申请数量"),
            edit = @Edit(title = "申请数量")
    )
    private Double requestQuantity;

    @FabosJsonField(
            views = @View(title = "入库数量"),
            edit = @Edit(title = "入库数量", notNull = true,
                    defaultVal = "requestQuantity",
                    numberType = @NumberType(min = 0))
    )
    private Double stockInQuantity;

    @FabosJsonField(
            views = @View(title = "计划单价"),
            edit = @Edit(title = "计划单价", notNull = true,
                    numberType = @NumberType(min = 0))
    )
    private Double unitPrice;

    @FabosJsonField(
            views = @View(title = "入库金额"),
            edit = @Edit(title = "入库金额", notNull = true, readonly = @Readonly,
                    numberType = @NumberType(min = 0)),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = {"stockInQuantity", "unitPrice"}, dynamicHandler = PreStockInDetailAmountDynamicHandler.class))
    )
    private Double amount;

    @FabosJsonField(
            views = @View(title = "存放库房", column = "name"),
            edit = @Edit(title = "存放库房", notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    queryCondition = "{\"lockState\": \"Normal\"}"
            )
    )
    @ManyToOne
    private Warehouse warehouse;

    @FabosJsonField(
            views = @View(title = "存放库区", column = "name"),
            edit = @Edit(title = "存放库区", notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    queryCondition = "{\"lockState\": \"Normal\", \"warehouse.id\": \"${warehouse.id}\"}"
            )
    )
    @ManyToOne
    private WarehouseBlock warehouseBlock;

    @FabosJsonField(
            views = @View(title = "存放货位", column = "name"),
            edit = @Edit(title = "存放货位", notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    queryCondition = "{\"lockState\": \"Normal\", \"fullFlag\": \"N\",\"warehouseBlock.id\": \"${warehouseBlock.id}\"}"
            )
    )
    @ManyToOne
    private WarehouseShelf warehouseShelf;

    @FabosJsonField(
            views = @View(title = "备注"),
            edit = @Edit(title = "备注", type = EditType.TEXTAREA)
    )
    private String remark;

}
