package cec.jiutian.bc.si.domain.preStockIn.model;

import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleModel;
import cec.jiutian.bc.si.domain.preStockIn.handler.PreStockInDetailDynamicHandler;
import cec.jiutian.bc.si.domain.preStockIn.proxy.PreStockInFlowProxy;
import cec.jiutian.bc.si.domain.preStockIn.proxy.PreStockInProxy;
import cec.jiutian.bc.si.enumeration.NamingRuleCodeEnum;
import cec.jiutian.bc.si.enumeration.StockInTypeEnum;
import cec.jiutian.bc.si.port.dto.IqcMTO;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.ReferenceGenerateType;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowBaseOperation;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OrderBy;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@FabosJson(
        name = "紧急预入库",
        orderBy = "PreStockIn.createTime desc",
        dataProxy = PreStockInProxy.class,
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "currentState!='EDIT'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "currentState!='EDIT'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "examine",
                        ifExpr = "examineStatus =='3' || examineStatus =='1'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "examineDetails",
                        ifExpr = "examineStatus =='0'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
        },
        flowProxy = PreStockInFlowProxy.class,
        flowCode = "PreStockIn",
        power = @Power(examine = true, examineDetails = true)
)
@Table(name = "bwi_pre_stock_in",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Getter
@Setter
@TemplateType(type = "multiTable")
public class PreStockIn extends NamingRuleModel {

    @Override
    public String getNamingCode() {
        return NamingRuleCodeEnum.StockInNumber.name();
    }

    @FabosJsonField(
            views = @View(title = "类型"),
            edit = @Edit(title = "类型", show = false, readonly = @Readonly(), type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = StockInTypeEnum.class))
    )
    private String type;

    @FabosJsonField(
            views = @View(title = "请验单号", column = "number"),
            edit = @Edit(title = "请验单号",
                    readonly = @Readonly(add = false),
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "number"),
                    filter = @Filter(value = "IqcMTO.inspectType = 'ArriveFactory' and (IqcMTO.certificateType = '' or IqcMTO.certificateType is null) and (IqcMTO.processState = '' or IqcMTO.processState is null) and (IqcMTO.currentState = 'INSPECTING' or IqcMTO.currentState = 'FILLING')")
            )
    )
    @ManyToOne
    private IqcMTO iqc;

    @FabosJsonField(
            views = @View(title = "当前状态"),
            edit = @Edit(title = "当前状态", type = EditType.CHOICE, show = false,
                    search = @Search(),
                    choiceType = @ChoiceType(
                            fetchHandler = OrderCurrentStateEnum.class
                    )
            )
    )
    private String currentState;

    @FabosJsonField(
            views = @View(title = "备注"),
            edit = @Edit(title = "备注", type = EditType.TEXTAREA)
    )
    private String remark;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "pre_stock_in_id")
    @OrderBy
    @FabosJsonField(
            edit = @Edit(title = "入库明细", type = EditType.TAB_REFERENCE_GENERATE),
            views = @View(title = "入库明细", type = ViewType.TABLE_VIEW),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = {"iqc"}, dynamicHandler = PreStockInDetailDynamicHandler.class)),
            referenceGenerateType = @ReferenceGenerateType(editable = {"stockInQuantity", "unitPrice", "amount", "warehouse", "warehouseBlock", "warehouseShelf", "remark"})
    )
    private List<PreStockInDetail> details;

}
