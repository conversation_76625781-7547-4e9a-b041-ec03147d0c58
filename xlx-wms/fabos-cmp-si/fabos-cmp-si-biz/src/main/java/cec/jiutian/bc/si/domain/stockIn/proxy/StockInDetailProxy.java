package cec.jiutian.bc.si.domain.stockIn.proxy;

import cec.jiutian.bc.si.domain.stockIn.model.StockInDetail;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.data.jpa.JpaCrud;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
public class StockInDetailProxy implements DataProxy<StockInDetail> {

    @Resource
    private JpaCrud jpaCrud;

    @Resource
    private FabosJsonDao fabosJsonDao;

}
