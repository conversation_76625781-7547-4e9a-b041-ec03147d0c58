package cec.jiutian.bc.generalModeler.enumeration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import java.util.ArrayList;
import java.util.List;

public class WarehouseTypeEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum value : Enum.values()) {
            list.add(new VLModel(value.name(), value.getTitle()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        Flat("平库"),
        Stand("立库");

        private final String title;

    }

    public static String getValueByKey(String key) {
        for (Enum e : Enum.values()) {
            if (e.name().equalsIgnoreCase(key)) {
                return e.getTitle();
            }
        }
        return null;
    }
}
