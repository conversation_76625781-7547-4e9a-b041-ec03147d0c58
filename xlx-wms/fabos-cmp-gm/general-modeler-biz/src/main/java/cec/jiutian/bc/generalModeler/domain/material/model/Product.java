package cec.jiutian.bc.generalModeler.domain.material.model;

import cec.jiutian.bc.generalModeler.domain.material.proxy.ProductDataProxy;
import cec.jiutian.bc.generalModeler.domain.measureUnit.model.MeasureUnit;
import cec.jiutian.bc.generalModeler.enumeration.IncomingMaterialsLevelEnum;
import cec.jiutian.bc.generalModeler.enumeration.ProductEnum;
import cec.jiutian.bc.generalModeler.handler.ProductAddHandler;
import cec.jiutian.core.data.annotation.LinkTable;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.frame.module.MetaUser;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.InputGroup;
import cec.jiutian.view.ReferenceTreeType;
import cec.jiutian.view.SubTableField;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.config.Comment;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DependFieldDisplay;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.field.edit.TabTableReferType;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.LinkTree;
import cec.jiutian.view.type.RowBaseOperation;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@FabosJson(
        name = "产品/零部件主数据",
        orderBy = "Product.createTime desc",
        dataProxy = ProductDataProxy.class,
        linkTree = @LinkTree(field = "productCategory"),
        rowBaseOperation = {
                @RowBaseOperation(code = "add",
                        needInitFormValue = true,
                        operationHandler = ProductAddHandler.class
                )}
)
@Table(name = "product",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"productCode"})
        }
)
@Entity
@Getter
@Setter
@TemplateType(type = "treeTable")
@LinkTable
public class Product extends MetaModel {
    @FabosJsonField(
            views = @View(title = "产品/零部件代码"),
            edit = @Edit(title = "产品/零部件代码",
                    readonly = @Readonly(add = false, edit = true),
                    notNull = true,
                    search = @Search(vague = true))
    )
    @SubTableField
    private String productCode;

    @FabosJsonField(
            views = @View(title = "类别"),
            edit = @Edit(title = "类别", notNull = false, type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ProductEnum.class),
                    readonly = @Readonly(add = false, edit = true),
                    search = @Search(vague = true), show = false)
    )
    private String productMainCategory;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "分类代码", column = "name"),
            edit = @Edit(title = "分类代码", readonly = @Readonly(add = false, edit = true), notNull = true,
                    type = EditType.REFERENCE_TREE,
                    search = @Search(),
                    referenceTreeType = @ReferenceTreeType(pid = "parentCategory.id")
//                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "productMainCategory != ''"),
//                    queryCondition = "{\"mainCategory\":\"${productMainCategory}\"}"
            )

    )
    @SubTableField
    private ProductCategory productCategory;

    @FabosJsonField(
            views = @View(title = "别名"),
            edit = @Edit(title = "别名", notNull = true)
    )
    @Comment("对外部展示名称（非密）")
    @SubTableField
    private String name;

    @FabosJsonField(
            views = @View(title = "内部名称"),
            edit = @Edit(title = "内部名称", notNull = false, search = @Search(vague = true))
    )
    @SubTableField
    private String internalName;

    @FabosJsonField(
            views = @View(title = "定型名称"),
            edit = @Edit(title = "定型名称", notNull = false)
    )
    @SubTableField
    private String officialName;

    @FabosJsonField(
            views = @View(title = "产品/零部件代号（军方）"),
            edit = @Edit(title = "产品/零部件代号（军方）", notNull = false)
    )
    private String productCodeA;

    @FabosJsonField(
            views = @View(title = "产品/零部件代号（集团公司）"),
            edit = @Edit(title = "产品/零部件代号（集团公司）", notNull = false)
    )
    private String productCodeB;

    @FabosJsonField(
            views = @View(title = "产品/零部代号（设计单位）"),
            edit = @Edit(title = "产品/零部代号（设计单位）", notNull = false)
    )
    private String productCodeC;

    @FabosJsonField(
            views = @View(title = "内部代号"),
            edit = @Edit(title = "内部代号", notNull = false, search = @Search(vague = true))
    )
    @SubTableField
    private String productCodeD;

    @FabosJsonField(
            views = @View(title = "设计单位名称"),
            edit = @Edit(title = "设计单位名称", notNull = false)
    )
    @SubTableField
    private String designCompanyName;

    @FabosJsonField(
            views = @View(title = "设计单位代码"),
            edit = @Edit(title = "设计单位代码", notNull = false)
    )
    @SubTableField
    private String designCompanyCode;

    @FabosJsonField(
            views = @View(title = "图号"),
            edit = @Edit(title = "图号", notNull = false, search = @Search(vague = true))
    )
    private String pictureNumber;

    @FabosJsonField(
            views = @View(title = "型号"),
            edit = @Edit(title = "型号", notNull = false)
    )
    @SubTableField
    private String modelNumber;

    @FabosJsonField(
            views = @View(title = "规格"),
            edit = @Edit(title = "规格", notNull = false)
    )
    @SubTableField
    private String specNumber;

    @FabosJsonField(
            views = @View(title = "技术标准号/技术要求"),
            edit = @Edit(title = "技术标准号/技术要求", notNull = false)
    )
    private String standardNumber;

    @FabosJsonField(
            views = @View(title = "阶段信息"),
            edit = @Edit(title = "阶段信息", notNull = false, type = EditType.TEXTAREA)
    )
    private String phaseInfo;

    @FabosJsonField(
            views = @View(title = "特殊信息"),
            edit = @Edit(title = "特殊信息", notNull = false, type = EditType.TEXTAREA)
    )
    private String otherInfo;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "单位", column = "unitChnName"),
            edit = @Edit(title = "单位",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "unitChnName",
                            type = ReferenceTableType.SelectShowTypeMTO.LIST, groupField = "unitType"))
    )
    private MeasureUnit measureUnit;

    @FabosJsonField(
            views = @View(title = "项目代号"),
            edit = @Edit(title = "项目代号", notNull = true)
    )
    private String projectNumber;

    @FabosJsonField(
            views = @View(title = "最大复验次数"),
            edit = @Edit(title = "最大复验次数", notNull = false,
                    inputGroup = @InputGroup(postfix = "次"))
    )
    private Integer maxReinspectTimes;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "storeKeeper_id")
    @FabosJsonField(
            views = @View(title = "保管员", column = "name", type = ViewType.text),
            edit = @Edit(title = "保管员",
                    filter = @Filter(value = "MetaUser.state = 'Y'"),
                    notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"), search = @Search())
    )
    private MetaUser storeKeeper;

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
            name = "e_semiproduct_usefor_product", // 中间表表名，如下为中间表的定义
            joinColumns = @JoinColumn(name = "target_id", referencedColumnName = "id"),
            inverseJoinColumns = @JoinColumn(name = "source_id", referencedColumnName = "id"))
    @FabosJsonField(
            views = @View(title = "用途", type = ViewType.TABLE_VIEW, export = false),
            edit = @Edit(title = "用途",
                    type = EditType.TAB_TABLE_REFER,
                    notNull = false,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "productMainCategory == 'SEMIPRODUCT'"),
                    search = @Search(),
                    tabTableReferType = @TabTableReferType(type = TabTableReferType.SelectShowTypeMTM.LIST,
                            groupField = "productCategory_name",
                            label = "productCodeD"
                    )
            )
    )
    private List<FinalProductDTM> useForProductlist;

    @FabosJsonField(
            views = @View(title = "来料物资分级"),
            edit = @Edit(show = false, title = "来料物资分级", type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = IncomingMaterialsLevelEnum.class))
    )
    private String incomingMaterialsLevel;
}
