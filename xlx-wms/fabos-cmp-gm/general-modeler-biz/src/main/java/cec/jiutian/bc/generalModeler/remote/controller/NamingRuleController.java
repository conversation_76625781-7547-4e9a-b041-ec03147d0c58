package cec.jiutian.bc.generalModeler.remote.controller;

import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequiredArgsConstructor
public class NamingRuleController {

    @Resource
    private FabosJsonDao FabosJsonDao;

    @Resource
    private NamingRuleService namingRuleService;

    //
    @PostMapping({"/namingRule/generateCode"})
//    @FabosJsonRouter(authIndex = 2, verifyType = FabosJsonRouter.VerifyType.FabosJson)
    public String generateCode(@RequestBody Map<String, Object> map) {
        Map<String, String> variablMap = convertJsonToMap(map.get("variables").toString());
        return namingRuleService.getNameCode(map.get("ruleCode").toString(), Integer.parseInt(map.get("num").toString()), variablMap).toString();
    }

    public static Map<String, String> convertJsonToMap(String json) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.readValue(json, Map.class);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
}