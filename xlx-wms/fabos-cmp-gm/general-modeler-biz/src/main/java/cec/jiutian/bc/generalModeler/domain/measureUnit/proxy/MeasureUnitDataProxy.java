package cec.jiutian.bc.generalModeler.domain.measureUnit.proxy;

import cec.jiutian.bc.generalModeler.domain.measureUnit.model.MeasureUnit;
import cec.jiutian.bc.generalModeler.util.StringUtils;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.data.jpa.JpaCrud;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import jakarta.persistence.TypedQuery;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：2024/10/24 17:00
 * @description：
 */
@Component
public class MeasureUnitDataProxy implements DataProxy<MeasureUnit> {
    @Resource
    private JpaCrud jpaCrud;

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public void beforeAdd(MeasureUnit measureUnit) {
        checkNameRepeat(measureUnit);
    }

    @Override
    public void beforeUpdate(MeasureUnit measureUnit) {
        checkNameRepeat(measureUnit);
    }

    private void checkNameRepeat(MeasureUnit measureUnit) {
        String hql = "from MeasureUnit mu where mu.unitChnName = :unitChnName " +
                "union from MeasureUnit mu1 where mu1.unitEngName = :unitEngName";
        TypedQuery<MeasureUnit> query = fabosJsonDao.getEntityManager().createQuery(hql, MeasureUnit.class);
        query.setParameter("unitChnName", measureUnit.getUnitChnName());
        query.setParameter("unitEngName", measureUnit.getUnitEngName());
        List<MeasureUnit> measureUnitList = query.getResultList();
        if (StringUtils.isNotEmpty(measureUnit.getId())) {
            if (CollectionUtils.isNotEmpty(measureUnitList) && measureUnitList.size() > 1) {
                throw new FabosJsonApiErrorTip("该计量单位名称已存在");
            }
        } else if (CollectionUtils.isNotEmpty(measureUnitList)) {
            throw new FabosJsonApiErrorTip("该计量单位名称已存在");
        }
    }
}
