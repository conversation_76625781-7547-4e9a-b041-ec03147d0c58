package cec.jiutian.bc.generalModeler.domain.client.model;

import cec.jiutian.bc.generalModeler.enumeration.YesOrNoEnum;
import cec.jiutian.core.frame.constant.RegexConst;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/*
 * 依赖的视图创建语句
 create or replace
    view mo_clnt_view as
    select
        c.id,
        c.crte_usr as create_by,
        c.clnt_nm,
        c.clnt_cd,
        c.clnt_shrt_nm,
        c.phn_nb,
        c.eml_tx,
        c.vld_flg,
        c.cntct_prsn_nm,
        c.crte_tm as create_time,
        '' as oid,
        c.lst_evnt_usr as update_by,
        c.lst_evnt_tm as update_time
    from
        mo_clnt c;
 */
@Table(name = "mo_clnt_view")
@FabosJson(
        name = "客户管理",
        power = @Power(add = false, edit = false, delete = false, importable = false, export = true, print = false, examine = false, examineDetails = false, viewDetails = false)
)
@Entity
@Builder
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class Client extends MetaModel {

    @FabosJsonField(
            views = @View(title = "客户名称"),
            edit = @Edit(title = "客户名称",
                    notNull = true, search = @Search(vague = true),
                    inputType = @InputType(length = 20))
    )
    private String clntNm;

    @FabosJsonField(
            views = @View(title = "客户代码"),
            edit = @Edit(title = "客户代码",
                    readonly = @Readonly(add = false),
                    notNull = true, search = @Search(vague = true),
                    inputType = @InputType(length = 20))
    )
    private String clntCd;

    @FabosJsonField(
            views = @View(title = "客户简称"),
            edit = @Edit(title = "客户简称",
                    notNull = true, search = @Search(vague = true),
                    inputType = @InputType(length = 20))
    )
    private String clntShrtNm;

    @FabosJsonField(
            views = @View(title = "联系人"),
            edit = @Edit(title = "联系人",
                    inputType = @InputType(length = 20))
    )
    private String cntctPrsnNm;

    @FabosJsonField(
            views = @View(title = "电话"),
            edit = @Edit(title = "电话", notNull = true,
                    inputType = @InputType(length = 20, regex = RegexConst.PHONE_EXT_REGEX))
    )
    private String phnNb;

    @FabosJsonField(
            views = @View(title = "电子邮箱"),
            edit = @Edit(title = "电子邮箱", notNull = true,
                    inputType = @InputType(regex = RegexConst.EMAIL_REGEX))
    )
    private String emlTx;

    @FabosJsonField(
            views = @View(title = "是否生效"),
            edit = @Edit(title = "是否生效",
                    notNull = true,
                    defaultVal = "N",
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(
                            fetchHandler = YesOrNoEnum.class
                    ))
    )
    private String vldFlg;
}
