package cec.jiutian.bc.generalModeler.domain.warehouseArea.proxy;


import cec.jiutian.bc.generalModeler.domain.warehouseArea.model.WarehouseShelf;
import cec.jiutian.bc.generalModeler.enumeration.WarehouseStateEnum;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Root;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class WarehouseShelfProxy implements DataProxy<WarehouseShelf> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public void afterAdd(WarehouseShelf warehouseShelf) {

    }

    @Override
    public void beforeAdd(WarehouseShelf warehouseShelf) {
        //唯一性验证友好提示
        CriteriaBuilder criteriaBuilder = fabosJsonDao.getEntityManager().getCriteriaBuilder();
        CriteriaQuery<WarehouseShelf> criteriaQuery = criteriaBuilder.createQuery(WarehouseShelf.class);
        Root<WarehouseShelf> root = criteriaQuery.from(WarehouseShelf.class);
        criteriaQuery.where(
                criteriaBuilder.or(
                        criteriaBuilder.equal(root.get("code"), warehouseShelf.getCode()),
                        criteriaBuilder.equal(root.get("name"), warehouseShelf.getName())
                )
        );
        List<WarehouseShelf> resultList = fabosJsonDao.getEntityManager().createQuery(criteriaQuery).getResultList();
        if (!resultList.isEmpty()) {
            throw new FabosJsonApiErrorTip("编码或者名称已存在");
        }
        warehouseShelf.setLockState(WarehouseStateEnum.Enum.Normal.name());
    }

    @Override
    public void beforeDelete(WarehouseShelf warehouseShelf) {
        if (warehouseShelf.getLockState().equals(WarehouseStateEnum.Enum.Locked.name())) {
            throw new FabosJsonApiErrorTip("已锁定货位不可删除");
        }
        // todo 校验库存是否存在
    }

}
