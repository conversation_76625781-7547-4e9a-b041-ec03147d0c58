package cec.jiutian.bc.generalModeler.domain.measureUnit.proxy;

import cec.jiutian.bc.generalModeler.domain.measureUnit.model.MeasureUnit;
import cec.jiutian.bc.generalModeler.domain.measureUnit.model.UnitConversionRule;
import cec.jiutian.bc.generalModeler.enumeration.CalculateFormulaENum;
import cec.jiutian.bc.generalModeler.enumeration.ConversionFormulaTemplateEnum;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.data.jpa.JpaCrud;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date ：2024/10/24 17:17
 * @description：
 */
@Component
public class UnitConversionRuleDataProxy implements DataProxy<UnitConversionRule> {

    @Resource
    private JpaCrud jpaCrud;

    @Override
    public void beforeAdd(UnitConversionRule unitConversionRule) {
        if (unitConversionRule.getSourceMeasureUnit().getId()
                .equals(unitConversionRule.getTargetMeasureUnit().getId())) {
            throw new FabosJsonApiErrorTip("主计量单位与辅计量单位不可相同，请确认！");
        }

        UnitConversionRule condition = new UnitConversionRule();
        condition.setTargetMeasureUnit(unitConversionRule.getTargetMeasureUnit());
        condition.setSourceMeasureUnit(unitConversionRule.getSourceMeasureUnit());
        UnitConversionRule data = jpaCrud.selectOne(condition);
        if (data != null) {
            throw new FabosJsonApiErrorTip("换算规则已存在，请确认！");
        }

        String formulaTemplateName = unitConversionRule.getFormulaTemplate();
        CalculateFormulaENum.Enum.valueOf(formulaTemplateName);
        unitConversionRule.setCalculateFormula(CalculateFormulaENum.Enum.valueOf(formulaTemplateName).getValue());

        String formula = ConversionFormulaTemplateEnum.Enum.valueOf(unitConversionRule.getFormulaTemplate()).getValue();
        unitConversionRule.setFormula(formula);

        String calculateFormula = unitConversionRule.getCalculateFormula();
        calculateFormula = calculateFormula.replace("A", unitConversionRule.getCoefficient());
        if (StringUtils.isNotEmpty(unitConversionRule.getConstantTerm())) {
            calculateFormula = calculateFormula.replace("B", unitConversionRule.getConstantTerm());
        }
        unitConversionRule.setCalculateFormula(calculateFormula);

        MeasureUnit sourceUnit = jpaCrud.getById(MeasureUnit.class, unitConversionRule.getSourceMeasureUnit().getId());
        MeasureUnit targetUnit = jpaCrud.getById(MeasureUnit.class, unitConversionRule.getTargetMeasureUnit().getId());
        unitConversionRule.setTargetMeasureUnitName(targetUnit.getUnitChnName() + "(" + sourceUnit.getUnitChnName() + ")");
        unitConversionRule.setMainUnitType(sourceUnit.getUnitType());
        unitConversionRule.setAuxiliaryUnitType(targetUnit.getUnitType());
    }

    @Override
    public void beforeUpdate(UnitConversionRule unitConversionRule) {
        if (unitConversionRule.getSourceMeasureUnit().getId()
                .equals(unitConversionRule.getTargetMeasureUnit().getId())) {
            throw new FabosJsonApiErrorTip("主计量单位与辅计量单位不可相同，请确认！");
        }

        String formula = ConversionFormulaTemplateEnum.Enum.valueOf(unitConversionRule.getFormulaTemplate()).getValue();
        formula = formula.replace("A", unitConversionRule.getCoefficient());
        if (StringUtils.isNotEmpty(unitConversionRule.getConstantTerm())) {
            formula = formula.replace("B", unitConversionRule.getConstantTerm());
        }
        unitConversionRule.setFormula(formula);
    }

}
