//package cec.jiutian.bc.modeler.domain.warehouse.model;
//
//import cec.jiutian.core.view.fabosJson.FabosJson;
//import cec.jiutian.core.view.fabosJson.InheritStrategy;
//import jakarta.persistence.*;
//import lombok.Getter;
//import lombok.Setter;
//import lombok.experimental.Accessors;
//
//@FabosJson(name = "仓库"
//)
//@Setter
//@Getter
//@Entity
//@Accessors(chain = true)
//@Table(name = "iot_warehouse_block_detail")
//@InheritStrategy(
//
//        excludeParentRowBaseOperationCode = {},
//        dataProxyFlag = true,
//        excludeParentFields = {}
//)
//public class WmsWarehouseBlockDetail extends WmsWarehouseBlock {
//
//}
