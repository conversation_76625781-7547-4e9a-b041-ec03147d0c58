model-driven,modelClass,menuType,sort,menuIcon,menuName,menuValue,parentMenu,status
FALSE,,usual,13,fa fa-cogs,主数据管理,$GeneralModeler,,
FALSE,,usual,101,fa fa-cogs,区域管理,$AreaGeneralModeler,$GeneralModeler,
TRUE,Warehouse,usual,10101,,,,$AreaGeneralModeler,
TRUE,WarehouseBlock,usual,10102,,,,$AreaGeneralModeler,
TRUE,WarehouseShelf,usual,10103,,,,$AreaGeneralModeler,
FALSE,,usual,102,fa fa-cogs,物资管理,$MaterialsGeneralModeler,$GeneralModeler,
TRUE,ProductCategory,multiTable,10201,,,,$MaterialsGeneralModeler,
TRUE,Product,multiTable,10202,,,,$MaterialsGeneralModeler,
TRUE,MaterialCategory,treeForm,10203,,,,$MaterialsGeneralModeler,
TRUE,Material,multiTable,10204,,,,$MaterialsGeneralModeler,
TRUE,MaterialAttributeTemplate,usual,10,,,,$MaterialsGeneralModeler,2
TRUE,MaterialModelField,usual,30,,,,$MaterialsGeneralModeler,2
TRUE,StockAlarmSafe,usual,10200,,,,$MaterialsGeneralModeler,
TRUE,StockAlarm,form,10401,,,,$MaterialsGeneralModeler,
FALSE,,usual,103,fa fa-cogs,往来单位,$DealingsGeneralModeler,$GeneralModeler,
TRUE,Client,usual,10301,,,,$DealingsGeneralModeler,
TRUE,Supplier,usual,10302,,,,$DealingsGeneralModeler,
FALSE,,usual,104,fa fa-cogs,单位管理,$PmSystemConfigModeler,$GeneralModeler,
TRUE,MeasureUnit,usual,10402,,,,$PmSystemConfigModeler,
TRUE,UnitConversionRule,usual,10403,,,,$PmSystemConfigModeler,
TRUE,NamingRule,usual,10405,,,,$PmSystemConfigModeler,
TRUE,NamingRuleParameter,usual,80,,,,$PmSystemConfigModeler,2
TRUE,NamingRuleParameterSrValue,usual,90,,,,$PmSystemConfigModeler,2
