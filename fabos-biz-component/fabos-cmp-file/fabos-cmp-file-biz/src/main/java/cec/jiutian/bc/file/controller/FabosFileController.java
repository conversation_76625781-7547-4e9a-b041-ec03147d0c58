package cec.jiutian.bc.file.controller;

import cec.jiutian.bc.file.service.FileService;
import cec.jiutian.core.frame.constant.FabosJsonRestPath;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.core.prop.FabosJsonProp;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;

import static cec.jiutian.api.file.utils.FileUtil.FS_SEP;

/**
 * <AUTHOR>
@Slf4j
@RestController
@RequestMapping(FabosJsonRestPath.FABOS_FILE)
@RequiredArgsConstructor
public class FabosFileController {

    @Resource
    private final FabosJsonDao fabosJsonDao;
    @Resource
    private final FileService fileService;
    private final FabosJsonProp fabosJsonProp;

//    @SneakyThrows
//    @PostMapping("/upload/{fabosJson}/{field}")
//    @Transactional
//    public BaseApi upload(@PathVariable("fabosJson") String fabosJsonName, @PathVariable("field") String fieldName, @RequestParam("file") MultipartFile file) {
//        //生成存储路径
//        FabosJsonModel fabosJsonModel = FabosJsonCoreService.getFabosJson(fabosJsonName);
////        FabosJsonPowerUtil.powerLegal(fabosJsonModel, powerObject -> powerObject.isEdit() || powerObject.isAdd());
//        Edit edit = fabosJsonModel.getFabosJsonFieldMap().get(fieldName).getFabosJsonField().edit();
//
//        FabosJsonFile fabosJsonFile = new FabosJsonFile(file);
//        String relativePath = File.separator + DateUtil.getFormatDate(new Date(), DateUtil.DATE);
//        String modifiedFileName = FileUtil.processFileRenaming(fabosJsonFile)
//                .replace(edit.attachmentType().fileSeparator(), "");
//        switch (edit.type()) {
//            case ATTACHMENT:
//                AttachmentType attachmentType = edit.attachmentType();
//                //校验扩展名
//                if (attachmentType.fileTypes().length > 0) {
//                    String extensionName = fabosJsonFile.getFilenameExtension();
//                    if (Stream.of(attachmentType.fileTypes()).noneMatch(type -> type.contains(extensionName))) {
//                        return BaseApi.errorApi(FabosI18nTranslate.$translate("fabosJson.upload_error.file_format") + ": " + extensionName);
//                    }
//                }
//                if (!"".equals(attachmentType.path())) {
//                    relativePath = attachmentType.path() + relativePath;
//                }
//                //校验文件大小
//                if (attachmentType.size() > 0 && file.getSize() / 1024 > attachmentType.size()) {
//                    return BaseApi.errorApi(FabosI18nTranslate.$translate("fabosJson.upload_error.size") + ": " + attachmentType.size() + "KB");
//                }
//                switch (edit.attachmentType().type()) {
//                    case IMAGE:
//                        AttachmentType.ImageType imageType = edit.attachmentType().imageType();
//                        // 通过MultipartFile得到InputStream，从而得到BufferedImage
//                        BufferedImage bufferedImage = ImageIO.read(file.getInputStream());
//                        if (bufferedImage == null) {
//                            return BaseApi.errorApi(FabosI18nTranslate.$translate("fabosJson.upload_error.not_image"));
//                        }
//                        int width = bufferedImage.getWidth();
//                        int height = bufferedImage.getHeight();
//                        if (imageType.minWidth() > width || imageType.maxWidth() < width) {
//                            return BaseApi.errorApi(FabosI18nTranslate.$translate("fabosJson.upload_error.image_width") + String.format("[%s,%s]", imageType.minWidth(), imageType.maxWidth()));
//                        }
//                        if (imageType.minHeight() > height || imageType.maxHeight() < height) {
//                            return BaseApi.errorApi(FabosI18nTranslate.$translate("fabosJson.upload_error.image_height") + String.format("[%s,%s]", imageType.minWidth(), imageType.maxWidth()));
//                        }
//                        break;
//                    case BASE:
//                        break;
//                }
//                break;
//            case HTML_EDITOR:
//                HtmlEditorType htmlEditorType = edit.htmlEditorType();
//                if (!"".equals(htmlEditorType.path())) {
//                    relativePath = htmlEditorType.path() + relativePath;
//                }
//                break;
//        }
//        try {
//            fileService.createFile(file, relativePath, modifiedFileName, fabosJsonFile);
//            return BaseApi.successApi(modifiedFileName);
//        } catch (Exception e) {
//            log.error("fabosJson upload error", e);
//            return BaseApi.errorApi(FabosI18nTranslate.$translate("fabosJson.upload_error") + " " + e.getMessage());
//        }
//    }
//
//    @PostMapping("/uploads/{fabosJson}/{field}")
//    @Transactional
//    public BaseApi uploads(@PathVariable("fabosJson") String fabosJsonName, @PathVariable("field") String fieldName, @RequestParam("file") MultipartFile[] files) {
//        List<String> paths = new ArrayList<>();
//        for (MultipartFile file : files) {
//            BaseApi fabosJsonApiModel = upload(fabosJsonName, fieldName, file);
//            paths.add(fabosJsonApiModel.getMsg());
//        }
//        return BaseApi.successApi(String.join(",", paths));
//    }


//@PostMapping("/upload-html-editor/{fabosJson}/{field}")
//public Map<String, Object> uploadHtmlEditorImage(@PathVariable("fabosJson") String fabosJsonName,
//                                                 @PathVariable("field") String fieldName,
//                                                 @RequestParam("upload") MultipartFile file) throws ClassNotFoundException {
//    BaseApi fabosJsonApiModel = upload(fabosJsonName, fieldName, file);
//    Map<String, Object> map = new HashMap<>(2);
//    if (fabosJsonApiModel.getStatus() == BaseApi.Status.SUCCESS.getValue()) {
//        //{"uploaded":"true", "url":"image-path..."}
//        AttachmentProxy attachmentProxy = FabosJsonUtil.findAttachmentProxy();
//        if (null != attachmentProxy) {
//            map.put("url", attachmentProxy.fileDomain() + fabosJsonApiModel.getData());
//        } else {
//            map.put("url", FabosJsonRestPath.FABOS_ATTACHMENT + fabosJsonApiModel.getData());
//        }
//        map.put("uploaded", true);
//    } else {
//        map.put("uploaded", false);
//        throw new FabosJsonWebApiRuntimeException(fabosJsonApiModel.getMsg());
//    }
//    return map;
//}
//
//
//    @RequestMapping("/upload-ueditor/{fabosJson}/{field}")
//    public void uploadUEditorImage(@PathVariable("fabosJson") String fabosJsonName,
//                                   @PathVariable("field") String fieldName,
//                                   @RequestParam(value = "callback", required = false) String callback,
//                                   @RequestParam(value = "file", required = false) MultipartFile file,
//                                   HttpServletResponse response) throws IOException, ClassNotFoundException {
//        if (null == file) {
//            @Cleanup InputStream stream = FabosFileController.class.getClassLoader().getResourceAsStream("ueditor.json");
//            String json = StreamUtils.copyToString(stream, Charset.forName(StandardCharsets.UTF_8.name()));
//            if (null == callback) {
//                response.getOutputStream().write(json.getBytes(StandardCharsets.UTF_8));
//            } else {
//                response.getOutputStream().write((callback + "(" + json + ")").getBytes(StandardCharsets.UTF_8));
//            }
//        } else {
//            Map<String, Object> map = uploadHtmlEditorImage(fabosJsonName, fieldName, file);
//            Boolean status = (Boolean) map.get("uploaded");
//            map.put("state", status ? "SUCCESS" : "ERROR");
//            response.getOutputStream().write(new Gson().toJson(map).getBytes(StandardCharsets.UTF_8));
//        }
//    }

    public static final String DOWNLOAD_PATH = "/download-attachment";
    public static final String DOWNLOAD_PATH_NO_AUTH = "/public" + DOWNLOAD_PATH;


    @GetMapping(value = FabosJsonRestPath.DOWNLOAD_PATH + "/**", produces = {MediaType.APPLICATION_OCTET_STREAM_VALUE})
    public void downloadAttachment(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String incomingFileName = request.getServletPath().replace(FabosJsonRestPath.FABOS_FILE + FabosJsonRestPath.DOWNLOAD_PATH + FS_SEP, "");
        fileService.download(incomingFileName, response);
    }

    /**
     * 此下载接口用作下载文件，但无需校验权限
     */
    @GetMapping(value = FabosJsonRestPath.DOWNLOAD_PATH_NO_AUTH + "/**", produces = {MediaType.APPLICATION_OCTET_STREAM_VALUE})
    public void publicDownloadAttachment(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String incomingFileName = request.getServletPath().replace(FabosJsonRestPath.FABOS_FILE + FabosJsonRestPath.DOWNLOAD_PATH_NO_AUTH + FS_SEP, "");
        fileService.download(incomingFileName, response);
    }



}
