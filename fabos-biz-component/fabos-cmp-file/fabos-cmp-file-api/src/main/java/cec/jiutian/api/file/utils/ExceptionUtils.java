package cec.jiutian.api.file.utils;

import cec.jiutian.common.exception.MesErrorCodeException;

/**
 * <AUTHOR>
 * @time 2025-05-13 16:21
 */

public class ExceptionUtils {
    public static void throwException(String errorCode, String... msgs) {
        MesErrorCodeException exception = new MesErrorCodeException(errorCode);
        for (String msg : msgs) {
            exception.addMsgItem(msg);
        }
        throw exception;
    }

    public static void throwException(Exception exp, String errorCode, String... msgs) {
        MesErrorCodeException exception = new MesErrorCodeException(exp, errorCode);
        for (String msg : msgs) {
            exception.addMsgItem(msg);
        }
        throw exception;
    }
}

