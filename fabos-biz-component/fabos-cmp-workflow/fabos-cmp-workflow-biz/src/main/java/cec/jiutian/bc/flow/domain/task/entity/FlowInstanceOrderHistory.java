package cec.jiutian.bc.flow.domain.task.entity;


import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.view.config.Comment;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Entity
@Table(name = "fr_flow_instance_order_history",indexes = {@Index(columnList = "businessKey,businessCategory")})
@Getter
@Setter
public class FlowInstanceOrderHistory extends MetaModel {

    /**
     * 流程定义Id。
     */
    @Comment("流程名称")
    @Column(name = "process_definition_id")
    private String processDefinitionId;

    /**
     * 流程名称。
     */
    @Comment("流程名称")
    @Column(name = "process_definition_name")
    private String processDefinitionName;

    /**
     * 流程唯一编码。
     */
    @Comment("流程编码")
    @Column(name = "process_definition_key")
    private String processDefinitionKey;

    /**
     * 模型名称。
     */
    @Comment("模型名称")
    @Column(name = "model_name")
    private String modelName;

    /**
     * 流程版本。
     */
    @Comment("流程版本")
    @Column(name = "process_definition_version")
    private Integer processDefinitionVersion;

    /**
     * 流程实例id
     */
    @Comment("流程")
    @Column(name = "process_instance_id")
    private String processInstanceId;

    /**
     * 业务主键值。
     */
    @Comment("业务主键值")
    @Column(name = "business_key")
    private String businessKey;

    /**
     * 业务类别
     */
    @Comment("业务类别,业务组件发起流程时填写")
    @Column(name = "business_category")
    private String businessCategory;


    /**
     * 模块名称
     */
    @Comment("模块名称,业务组件发起流程时填写")
    @Column(name = "module_mame")
    private String moduleName;

    /**
     * 模块版本
     */
    @Comment("模块版本,业务组件发起流程时填写")
    @Column(name = "module_version")
    private String moduleVersion;

    /**
     *  发起人用户Account
     */
    @Comment("发起人用户Account")
    @Column(name = "start_user_account")
    private String startUserAccount;

    /**
     *  发起人用户名字
     */
    @Comment("发起人用户名字")
    @Column(name = "start_user_name")
    private String startUserName;

    /**
     * 开始时间
     */
    @Column(name = "start_date")
    private LocalDateTime startDate;

    /**
     * 完成时间
     */
    @Column(name = "end_date")
    private LocalDateTime endDate;

    /**
     * 流程实例状态
     */
    @Comment("流程实例状态")
    @Column(name = "status")
    private Integer status;


    /**
     *  表单数据
     */
    @Comment("表单数据")
    @Column(name = "form_data",columnDefinition ="TEXT")
    private String formData;

}
