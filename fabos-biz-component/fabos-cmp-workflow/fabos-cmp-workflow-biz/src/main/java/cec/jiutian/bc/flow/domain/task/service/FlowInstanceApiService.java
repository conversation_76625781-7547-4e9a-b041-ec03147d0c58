package cec.jiutian.bc.flow.domain.task.service;

import cec.jiutian.bc.flow.domain.model.entity.FlowDefinition;
import cec.jiutian.bc.flow.domain.model.vo.FlowTaskVO;
import cec.jiutian.bc.flow.domain.model.vo.ProcessInstanceInfoVO;
import cec.jiutian.bc.flow.domain.task.entity.FlowInstanceOrder;
import cec.jiutian.bc.flow.domain.task.entity.FlowTaskCommentRecord;
import cec.jiutian.bc.flow.domain.task.entity.FlowTaskExt;
import cec.jiutian.bc.flow.domain.task.entity.FlowTaskUrgingRecord;
import cec.jiutian.bc.flow.dto.FlowTaskCommentDTO;
import cec.jiutian.bc.flow.enums.FlowApprovalType;
import cec.jiutian.bc.flow.inbound.local.service.query.FlowTaskQuery;
import cec.jiutian.bc.flow.inbound.message.FlowStartInstanceDTO;
import cec.jiutian.bc.flow.outbound.message.TaskInfoVO;
import cec.jiutian.bc.flow.outbound.port.client.WfUserClient;
import cec.jiutian.bc.infra.enums.FlowConstant;
import cec.jiutian.bc.infra.enums.FlowInstanceStatus;
import cec.jiutian.bc.infra.enums.FlowTaskMultiSignAssign;
import cec.jiutian.bc.infra.enums.FlowTaskOperation;
import cec.jiutian.bc.infra.enums.FlowTaskStatus;
import cec.jiutian.bc.infra.utils.MessageUtil;
import cec.jiutian.bc.urm.dto.MetaUserinfo;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.common.util.BeanUtils;
import cec.jiutian.common.util.StringUtils;
import cec.jiutian.core.frame.constant.RegexConst;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.core.frame.module.Page;
import cec.jiutian.core.frame.service.FabosJsonCoreService;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.text.StringEscapeUtils;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.bpmn.model.EndEvent;
import org.flowable.bpmn.model.FlowElement;
import org.flowable.bpmn.model.FlowNode;
import org.flowable.bpmn.model.Gateway;
import org.flowable.bpmn.model.Process;
import org.flowable.bpmn.model.SequenceFlow;
import org.flowable.bpmn.model.ServiceTask;
import org.flowable.bpmn.model.StartEvent;
import org.flowable.bpmn.model.SubProcess;
import org.flowable.bpmn.model.UserTask;
import org.flowable.common.engine.impl.de.odysseus.el.ExpressionFactoryImpl;
import org.flowable.common.engine.impl.de.odysseus.el.util.SimpleContext;
import org.flowable.common.engine.impl.identity.Authentication;
import org.flowable.common.engine.impl.javax.el.ExpressionFactory;
import org.flowable.common.engine.impl.javax.el.ValueExpression;
import org.flowable.engine.HistoryService;
import org.flowable.engine.ManagementService;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.history.HistoricActivityInstance;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.history.HistoricProcessInstanceQuery;
import org.flowable.engine.impl.bpmn.behavior.ParallelMultiInstanceBehavior;
import org.flowable.engine.impl.bpmn.behavior.SequentialMultiInstanceBehavior;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.runtime.ActivityInstance;
import org.flowable.engine.runtime.ChangeActivityStateBuilder;
import org.flowable.engine.runtime.Execution;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.identitylink.api.IdentityLink;
import org.flowable.image.impl.DefaultProcessDiagramGenerator;
import org.flowable.task.api.DelegationState;
import org.flowable.task.api.Task;
import org.flowable.task.api.TaskInfo;
import org.flowable.task.api.TaskQuery;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.flowable.task.api.history.HistoricTaskInstanceQuery;
import org.flowable.variable.api.history.HistoricVariableInstance;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.Stack;
import java.util.UUID;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Slf4j
@Service
public class FlowInstanceApiService {

    @Resource
    private RuntimeService runtimeService;
    @Resource
    private RepositoryService repositoryService;
    @Resource
    private TaskService taskService;
    @Resource
    private HistoryService historyService;
    @Resource
    private ManagementService managementService;
    @Resource
    private FlowTaskExtService flowTaskExtService;

    @Resource
    private FlowTaskCommentService flowTaskCommentService;
    @Resource
    private FlowInstanceOrderService flowInstanceOrderService;
    @Resource
    private FlowCopyService flowCopyService;
    @Resource
    private WfUserClient wfUserClient;

    @Resource
    private MessageUtil messageUtil;
    @Resource
    private FlowTaskUrgingRecordService flowTaskUrgingRecordService;

    @Resource
    private FabosJsonDao fabosJsonDao;

    /**
     * 启动流程实例，如果当前登录用户为第一个用户任务的指派者，或者Assginee为流程启动人变量时，
     * 则自动完成第一个用户任务。
     *
     * @param processDefinitionId 流程定义Id。
     * @param startInstanceDTO    流程实例
     * @return 新启动的流程实例。
     */
    @Transactional(rollbackFor = Exception.class)
    public ProcessInstance startAndTakeFirst(String processDefinitionId, FlowStartInstanceDTO startInstanceDTO) {
        String loginName = UserContext.getPhoneNumber();
        FlowTaskCommentDTO flowTaskCommentDTO = startInstanceDTO.getFlowTaskComment();
        JSONObject formData = startInstanceDTO.getFormData();
        JSONObject copyData = startInstanceDTO.getCopyConfig();
        Authentication.setAuthenticatedUserId(loginName);
        // 设置流程变量。
        JSONObject variableData = this.initAndGetProcessInstanceVariables(processDefinitionId);
        if (MapUtil.isNotEmpty(startInstanceDTO.getCopyConfig())) {
            variableData.put(FlowConstant.COPY_DATA_KEY, startInstanceDTO.getCopyConfig());
            variableData.putAll(formData);
        }
        // 设置流程自定义变量
        FlowDefinition flowDefinition = fabosJsonDao.queryEntity(FlowDefinition.class, "processDefinitionId=:processDefinitionId",
                Map.of("processDefinitionId", processDefinitionId));
        if (flowDefinition != null) {
            flowDefinition.getVariableList().forEach(variable -> {
                if (variable.getModelFieldFlag()) {
                    // 查询业务数据，获取变量值
                    JSONObject modelData = JSONObject.from(fabosJsonDao.getById(FabosJsonCoreService.getFabosJson(flowDefinition.getModelName()).getClazz(), startInstanceDTO.getBusinessKey()));
                    variableData.put(variable.getVariableName(), modelData.get(variable.getVariableName()));
                }

            });
        }
        ProcessInstance instance = runtimeService.startProcessInstanceById(processDefinitionId, startInstanceDTO.getBusinessKey(), variableData);
        // 获取流程启动后的第一个任务。
        Task task = taskService.createTaskQuery().processInstanceId(instance.getId()).active().singleResult();
        if (StrUtil.equalsAny(task.getAssignee(), loginName, FlowConstant.START_USER_ACCOUNT_VAR)) {
            FlowTaskCommentRecord flowTaskComment = new FlowTaskCommentRecord();
            BeanUtils.copyNotEmptyProperties(flowTaskCommentDTO, flowTaskComment);
            // 按照规则，调用该方法的用户，就是第一个任务的assignee，因此默认会自动执行complete。
            flowTaskComment.fillWith(task);
            flowTaskComment.setComments("提交审批");
            this.completeTask(task, flowTaskComment, variableData, false);
        } else {
            if (copyData != null) {
                flowCopyService.save(task, instance, copyData);
            }
        }
        return instance;
    }

    /**
     * 查询我的发起
     *
     * @param taskQuery
     * @return
     */
    public Page<ProcessInstanceInfoVO> getMyStartTaskList(FlowTaskQuery taskQuery) {
        String userAccount = UserContext.getPhoneNumber();
        if (StringUtils.isBlank(userAccount)) {
            return new Page<>();
        }
        HistoricProcessInstanceQuery query = historyService.createHistoricProcessInstanceQuery()
                .startedBy(userAccount)
                .orderByProcessInstanceStartTime().desc();
        if (StringUtils.isNotBlank(taskQuery.getProcessDefinitionName())) {
            query.processDefinitionName(taskQuery.getProcessDefinitionName());
        }
        if (StringUtils.isNotBlank(taskQuery.getProcessDefinitionKey())) {
            query.processDefinitionKey(taskQuery.getProcessDefinitionKey());
        }
        if (StringUtils.isNotBlank(taskQuery.getBusinessKey())) {
            query.processInstanceBusinessKey(taskQuery.getBusinessKey());
        }
        if (taskQuery.getStartDate() != null) {
            query.startedAfter(Date.from(taskQuery.getStartDate().atZone(ZoneId.systemDefault()).toInstant()));
        }
        if (taskQuery.getEndDate() != null) {
            query.startedBefore(Date.from(taskQuery.getEndDate().atZone(ZoneId.systemDefault()).toInstant()));
        }
        if (StringUtils.isNotBlank(taskQuery.getStartUserAccount())) {
            query.startedBy(taskQuery.getStartUserAccount());
        }
        List<HistoricProcessInstance> instanceList = query.listPage((taskQuery.getPage() - 1) * taskQuery.getPerPage(), taskQuery.getPerPage());
        List<ProcessInstanceInfoVO> instanceInfoList = convertInstanceInfoList(instanceList);
        return new Page(instanceInfoList, taskQuery.getPage(), taskQuery.getPerPage(), query.count());

    }

    /**
     * 查询我的处理
     *
     * @param taskQuery
     * @return
     */
    public Page<ProcessInstanceInfoVO> getMyHandleTaskList(FlowTaskQuery taskQuery) {
        String userAccount = UserContext.getPhoneNumber();
        if (StringUtils.isBlank(userAccount)) {
            return new Page<>();
        }
        HistoricTaskInstanceQuery query = historyService.createHistoricTaskInstanceQuery()
                .includeProcessVariables()
                .finished()
                .taskAssignee(userAccount)
                .orderByHistoricTaskInstanceEndTime()
                .desc();
        if (StringUtils.isNotBlank(taskQuery.getProcessDefinitionName())) {
            query.processDefinitionNameLike(taskQuery.getProcessDefinitionName());
        }
        if (StringUtils.isNotBlank(taskQuery.getProcessDefinitionKey())) {
            query.processDefinitionKeyLike(taskQuery.getProcessDefinitionKey());
        }
        if (StringUtils.isNotBlank(taskQuery.getBusinessKey())) {
            query.processInstanceBusinessKey(taskQuery.getBusinessKey());
        }
        if (taskQuery.getStartDate() != null) {
            query.taskCreatedAfter(Date.from(taskQuery.getStartDate().atZone(ZoneId.systemDefault()).toInstant()));
        }
        if (taskQuery.getEndDate() != null) {
            query.taskCreatedBefore(Date.from(taskQuery.getEndDate().atZone(ZoneId.systemDefault()).toInstant()));
        }
        List<HistoricTaskInstance> instanceList = query.listPage(((taskQuery.getPage() - 1) * taskQuery.getPerPage()), taskQuery.getPerPage());
        List<ProcessInstanceInfoVO> instanceInfoList = convertTaskInstance(instanceList);
        return new Page(instanceInfoList, taskQuery.getPage(), taskQuery.getPerPage(), query.count());
    }

    /**
     * 查询我的待办
     *
     * @param query
     * @return
     */
    public Page<FlowTaskVO> getMyTodoTaskList(FlowTaskQuery query) {
        String userAccount = UserContext.getPhoneNumber();
        if (StringUtils.isBlank(userAccount)) {
            return new Page<>();
        }
        TaskQuery taskQuery = taskService.createTaskQuery()
                .active()
                .includeProcessVariables()
                .orderByTaskCreateTime().desc();
        if (StringUtils.isNotBlank(query.getProcessDefinitionName())) {
            taskQuery.processDefinitionNameLike(query.getProcessDefinitionName());
        }
        if (StringUtils.isNotBlank(query.getProcessDefinitionKey())) {
            taskQuery.processDefinitionKeyLike(query.getProcessDefinitionKey());
        }
        if (query.getStartDate() != null) {
            taskQuery.taskCreatedAfter(Date.from(query.getStartDate().atZone(ZoneId.systemDefault()).toInstant()));
        }
        if (StringUtils.isNotBlank(query.getBusinessKey())) {
            taskQuery.processInstanceBusinessKey(query.getBusinessKey());
        }
        if (query.getEndDate() != null) {
            taskQuery.taskCreatedBefore(Date.from(query.getEndDate().atZone(ZoneId.systemDefault()).toInstant()));
        }
        this.buildCandidateCondition(taskQuery, userAccount, query.getTaskId());
        List<Task> taskList = taskQuery.listPage(query.getPerPage() * (query.getPage() - 1), query.getPerPage());
        Set<String> processInstanceIdSet = taskList.stream().map(it -> it.getProcessInstanceId()).collect(Collectors.toSet());
        List<FlowInstanceOrder> flowInstanceOrderList =
                flowInstanceOrderService.getOrderByProcessInstanceIds(new ArrayList<>(processInstanceIdSet));
        List<FlowTaskVO> taskListVo = new ArrayList<>();
        taskList.forEach(task -> {
            FlowTaskVO flowTask = new FlowTaskVO();
            flowTask.setTaskId(task.getId());
            flowTask.setTaskDefinitionKey(task.getTaskDefinitionKey());
            flowTask.setStartTime(LocalDateTime.ofInstant(task.getCreateTime().toInstant(), ZoneId.systemDefault()));
            flowTask.setParentTaskId(task.getParentTaskId());
            flowTask.setProcessDefinitionId(task.getProcessDefinitionId());
            flowTask.setTaskName(task.getName());
            flowTask.setFormKey(task.getFormKey());
            flowTask.setProcessInstanceId(task.getProcessInstanceId());
            flowTask.setUserAccount(task.getAssignee());
            FlowInstanceOrder flowInstanceOrder = flowInstanceOrderList.stream()
                    .filter(iol -> iol.getProcessInstanceId().equals(task.getProcessInstanceId())).findFirst().orElse(null);
            if (flowInstanceOrder != null) {
                flowTask.setBusinessKey(flowInstanceOrder.getBusinessKey());
                flowTask.setStartUserId(flowInstanceOrder.getStartUserAccount());
                flowTask.setStartUserName(flowInstanceOrder.getStartUserName());
                flowTask.setProcessDefinitionName(flowInstanceOrder.getProcessDefinitionName());
                flowTask.setProcessDefinitionKey(flowInstanceOrder.getProcessDefinitionKey());
                flowTask.setProcessDefinitionVersion(flowInstanceOrder.getProcessDefinitionVersion());
            }
            taskListVo.add(flowTask);
        });
        return new Page(taskListVo, query.getPage(), query.getPerPage(), taskQuery.count());
    }


    /**
     * 根据流程实例查询所有任务
     *
     * @return
     */
    public List<FlowTaskVO> getTaskListByInstanceId(String processInstanceId) {
        //根据流程实例ID查询实例的具体信息(先获得节点列表),注意先后排序
        List<HistoricTaskInstance> taskList = historyService.createHistoricTaskInstanceQuery().processInstanceId(processInstanceId).orderByTaskCreateTime().asc().list();
        if (CollectionUtils.isEmpty(taskList)) {
            return new ArrayList<>();
        }
        List<FlowTaskVO> taskVOList = new ArrayList<>();
        taskList.forEach(task -> {
            FlowTaskVO flowTask = new FlowTaskVO();
            // 当前流程信息
            flowTask.setTaskId(task.getId());
            flowTask.setParentTaskId(task.getParentTaskId());
            flowTask.setTaskDefinitionKey(task.getTaskDefinitionKey());
            flowTask.setProcessDefinitionId(task.getProcessDefinitionId());
            flowTask.setTaskName(task.getName());
            // 流程发起人信息
            flowTask.setProcessInstanceId(task.getProcessInstanceId());
            if (StringUtils.isNotBlank(task.getAssignee())) {
                MetaUserinfo userinfo = wfUserClient.getMetaUserByPhoneNumber(task.getAssignee());
                if (userinfo != null) {
                    flowTask.setUserName(userinfo.getName());
                }
                flowTask.setUserAccount(task.getAssignee());
            }
            taskVOList.add(flowTask);
        });
        return taskVOList;
    }

    private List<ProcessInstanceInfoVO> convertTaskInstance(List<HistoricTaskInstance> instanceTaskList) {
        if (CollectionUtils.isEmpty(instanceTaskList)) {
            return new ArrayList<>();
        }
        Set<String> processInstanceIdSet = instanceTaskList.stream().map(it -> it.getProcessInstanceId()).collect(Collectors.toSet());
        List<FlowInstanceOrder> flowInstanceOrderList = flowInstanceOrderService.getOrderByProcessInstanceIds(new ArrayList<>(processInstanceIdSet));
        ArrayList<ProcessInstanceInfoVO> processInstanceList = new ArrayList<>();
        instanceTaskList.forEach(taskInstance -> {
            ProcessInstanceInfoVO infoVO = new ProcessInstanceInfoVO();
            infoVO.setProcessDefinitionId(taskInstance.getProcessDefinitionId());
            infoVO.setProcessInstanceId(taskInstance.getProcessInstanceId());
            infoVO.setProcessInstanceStartTime(LocalDateTime.ofInstant(taskInstance.getStartTime().toInstant(), ZoneId.systemDefault()));
            FlowInstanceOrder flowInstanceOrder = flowInstanceOrderList.stream().filter(iol -> iol.getProcessInstanceId().equals(taskInstance.getProcessInstanceId())).findFirst().orElse(null);
            if (flowInstanceOrder != null) {
                infoVO.setFormData(flowInstanceOrder.getFormData());
                infoVO.setModelName(flowInstanceOrder.getModuleName());
                infoVO.setProcessDefinitionName(flowInstanceOrder.getProcessDefinitionName());
                infoVO.setProcessDefinitionKey(flowInstanceOrder.getProcessDefinitionKey());
                infoVO.setStartUserAccount(flowInstanceOrder.getStartUserAccount());
                infoVO.setStartUserName(flowInstanceOrder.getStartUserName());
                infoVO.setStatus(flowInstanceOrder.getStatus());
                infoVO.setProcessInstanceEndTime(flowInstanceOrder.getEndDate());
                infoVO.setStartUserName(flowInstanceOrder.getStartUserName());
                infoVO.setBusinessKey(flowInstanceOrder.getBusinessKey());
            }
            infoVO.setStatusName(FlowInstanceStatus.getName(infoVO.getStatus()));
            processInstanceList.add(infoVO);
        });
        return processInstanceList;

    }

    private List<ProcessInstanceInfoVO> convertInstanceInfoList(List<HistoricProcessInstance> instanceInfoList) {
        if (CollectionUtils.isEmpty(instanceInfoList)) {
            return new ArrayList<>();
        }
        List<String> processInstanceIdList = instanceInfoList.stream().map(it -> it.getId()).collect(Collectors.toList());
        List<FlowInstanceOrder> flowInstanceOrderList = flowInstanceOrderService.getOrderByProcessInstanceIds(processInstanceIdList);
        ArrayList<ProcessInstanceInfoVO> processInstanceList = new ArrayList<>();
        instanceInfoList.forEach(instance -> {
            ProcessInstanceInfoVO infoVO = new ProcessInstanceInfoVO();
            infoVO.setProcessDefinitionId(instance.getProcessDefinitionId());
            infoVO.setProcessDefinitionName(instance.getProcessDefinitionName());
            infoVO.setProcessDefinitionKey(instance.getProcessDefinitionKey());
            infoVO.setStartUserAccount(instance.getStartUserId());
            infoVO.setProcessInstanceId(instance.getId());
            infoVO.setProcessInstanceStartTime(LocalDateTime.ofInstant(instance.getStartTime().toInstant(), ZoneId.systemDefault()));
            FlowInstanceOrder flowInstanceOrder = flowInstanceOrderList.stream().filter(iol -> iol.getProcessInstanceId().equals(instance.getId())).findFirst().orElse(null);
            if (flowInstanceOrder != null) {
                infoVO.setStatus(flowInstanceOrder.getStatus());
                infoVO.setProcessInstanceEndTime(flowInstanceOrder.getEndDate());
                infoVO.setStartUserName(flowInstanceOrder.getStartUserName());
                infoVO.setModelName(flowInstanceOrder.getModelName());
                infoVO.setFormData(flowInstanceOrder.getFormData());
            }
            infoVO.setBusinessKey(instance.getBusinessKey());
            infoVO.setStatusName(FlowInstanceStatus.getName(infoVO.getStatus()));
            List<Task> runTaskList = taskService.createTaskQuery().processInstanceId(instance.getId()).active().list();
            if (!CollectionUtils.isEmpty(runTaskList)) {
                Set<String> userNames = new HashSet<>();
                runTaskList.forEach(it -> {
                    if (StringUtils.isNotBlank(it.getAssignee())) {
                        userNames.add(it.getAssignee());
                    } else {
                        List<IdentityLink> Idlist = taskService.getIdentityLinksForTask(it.getId());
                        Idlist.forEach(is -> {
                            userNames.add(is.getUserId());
                        });
                    }
                });
                List<MetaUserinfo> userinfoList = wfUserClient.getMetaUserByPhoneNumbers(userNames.stream().toList());
                if (!CollectionUtils.isEmpty(userinfoList)) {
                    infoVO.setCurrentAssignees(StrUtil.join(",", userinfoList.stream().map(it -> it.getName()).collect(Collectors.toList())));
                }
            }
            //根据流程实例ID查询实例的具体信息(先获得节点列表),注意先后排序
            List<HistoricTaskInstance> tasks = historyService.createHistoricTaskInstanceQuery().processInstanceId(instance.getId()).orderByTaskCreateTime().asc().list();
            ArrayList<FlowTaskVO> taskList = new ArrayList<>();
            tasks.forEach(task -> {
                FlowTaskVO taskInfo = new FlowTaskVO();
                taskInfo.setUserAccount(task.getAssignee());
                taskInfo.setTaskId(task.getId());
                taskInfo.setFormKey(task.getFormKey());
                taskInfo.setTaskDefinitionKey(task.getTaskDefinitionKey());
                taskInfo.setParentTaskId(task.getParentTaskId());
                taskInfo.setProcessDefinitionId(task.getProcessDefinitionId());
                taskInfo.setStartTime(LocalDateTime.ofInstant(task.getCreateTime().toInstant(), ZoneId.systemDefault()));
                taskInfo.setStatus(task.getEndTime() == null ? FlowTaskStatus.APPROVING : FlowTaskStatus.APPROVED);
                if (task.getEndTime() != null) {
                    taskInfo.setEndTime(LocalDateTime.ofInstant(task.getEndTime().toInstant(), ZoneId.systemDefault()));
                }
                taskList.add(taskInfo);
            });
            infoVO.setTaskList(taskList);
            processInstanceList.add(infoVO);
        });
        return processInstanceList;
    }

    /**
     * 初始化并返回流程实例的变量Map。
     *
     * @param processDefinitionId 流程定义Id。
     * @return 初始化后的流程实例变量Map。
     */
    public JSONObject initAndGetProcessInstanceVariables(String processDefinitionId) {
        String loginName = UserContext.getPhoneNumber();
        // 设置流程固定变量。
        JSONObject variableMap = new JSONObject();
        variableMap.put(FlowConstant.PROC_INSTANCE_INITIATOR_VAR, loginName);
        variableMap.put(FlowConstant.PROC_INSTANCE_START_USER_ACCOUNT_VAR, loginName);
        variableMap.put(FlowConstant.PROC_INSTANCE_START_USER_NAME_VAR, UserContext.getUserName());
        variableMap.put(FlowConstant.PROC_INSTANCE_START_TIME, LocalDateTime.now());
        List<FlowTaskExt> flowTaskExtList = flowTaskExtService.getByProcessDefinitionId(processDefinitionId);
        this.buildCopyData(flowTaskExtList, variableMap);
        this.buildMultiInstanceAssigness(flowTaskExtList, variableMap);
        return variableMap;
    }


    /**
     * 催办,对流程当前节点人发出催办提醒
     *
     * @return
     */
    public boolean urging(String processInstanceId, String flowDefName) {
        List<Task> taskList = taskService.createTaskQuery().processInstanceId(processInstanceId).active().list();
        for (Task task : taskList) {
            String taskId = task.getId();
            TaskInfoVO taskInfo = StringUtils.isBlank(task.getFormKey()) ? null : JSON.parseObject(task.getFormKey(), TaskInfoVO.class);
            String messageType = taskInfo == null ? "" : taskInfo.getMessageType();
            String userId = UserContext.getPhoneNumber();
            String userName = UserContext.getUserName();
            HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                    .processInstanceId(processInstanceId)
                    .singleResult();
            String businessKey = historicProcessInstance.getBusinessKey();
            //获取任务接收人,如果没有配置则获取候选组用户
            String receiver = task.getAssignee();
            Set<String> userList = new HashSet<>();
            if (StringUtils.isBlank(receiver)) {
                List<IdentityLink> idList = taskService.getIdentityLinksForTask(taskId);
                if (!CollectionUtils.isEmpty(idList)) {
                    List<String> userNameList = idList.stream().map(it -> it.getUserId()).collect(Collectors.toList());
                    userList.addAll(userNameList);
                }
            } else {
                userList.add(receiver);
            }
            userList.forEach(user -> {
                MetaUserinfo toUser = wfUserClient.getMetaUserByPhoneNumber(user);
                if (toUser != null) {
                    messageUtil.sendMessage(toUser.getPhoneNumber(), toUser.getEmailAddress(), FlowConstant.FLOW_URGING
                            , messageUtil.buildMsgContent(businessKey, userName, flowDefName, FlowConstant.FLOW_URGING_CONTENT,
                                    "审批中", messageType), messageType);
                }
            });
            flowTaskUrgingRecordService.saveOrUpdate(new FlowTaskUrgingRecord(processInstanceId, messageType, userId, userName, StrUtil.join(",", userList), taskId, task.getTaskDefinitionKey(), task.getName()));
        }
        return true;
    }

    /**
     * 完成任务，同时提交审批数据。
     *
     * @param task                  工作流任务对象。
     * @param flowTaskCommentRecord 审批对象。
     * @param taskVariableData      流程任务的变量数据。
     */
    public void completeTask(Task task, FlowTaskCommentRecord flowTaskCommentRecord, JSONObject taskVariableData, boolean isSubmit) {
        JSONObject passCopyData = null;
        if (taskVariableData != null) {
            passCopyData = (JSONObject) taskVariableData.remove(FlowConstant.COPY_DATA_KEY);
        }
        if (flowTaskCommentRecord != null) {
            // 这里处理多实例会签逻辑。
            if (flowTaskCommentRecord.getApprovalType().equals(FlowApprovalType.MULTI_SIGN)) {
                String loginAccount = UserContext.getPhoneNumber();
                String loginName = UserContext.getUserName();
                if (taskVariableData == null) {
                    taskVariableData = new JSONObject();
                }
                String assigneeList = taskVariableData.getString(FlowConstant.MULTI_ASSIGNEE_LIST_VAR);
                if (StringUtils.isBlank(assigneeList)) {
                    FlowTaskExt flowTaskExt = flowTaskExtService.getByProcessDefinitionIdAndTaskKey(
                            task.getProcessDefinitionId(), task.getTaskDefinitionKey());
                    assigneeList = this.buildMutiSignAssigneeList(flowTaskExt.getOperationListJson());
                    if (assigneeList != null) {
                        taskVariableData.put(FlowConstant.MULTI_ASSIGNEE_LIST_VAR, StringUtils.split(assigneeList, ','));
                        taskVariableData.put(FlowConstant.PROC_INSTANCE_START_USER_ACCOUNT_VAR, loginAccount);
                        taskVariableData.put(FlowConstant.PROC_INSTANCE_START_USER_NAME_VAR, loginName);
                    }
                }
                Assert.isTrue(StringUtils.isNotBlank(assigneeList));
                taskVariableData.put(FlowConstant.MULTI_AGREE_COUNT_VAR, 0);
                taskVariableData.put(FlowConstant.MULTI_REFUSE_COUNT_VAR, 0);
                taskVariableData.put(FlowConstant.MULTI_ABSTAIN_COUNT_VAR, 0);
                taskVariableData.put(FlowConstant.MULTI_SIGN_NUM_OF_INSTANCES_VAR, 0);
                taskVariableData.put(FlowConstant.MULTI_SIGN_START_TASK_VAR, task.getId());
                String comment = String.format("用户 [%s] 会签 [%s]。", loginName, assigneeList);
                flowTaskCommentRecord.setComments(comment);
            }
            // 处理转办。
            if (FlowApprovalType.TRANSFER.equals(flowTaskCommentRecord.getApprovalType())) {
                taskService.setAssignee(task.getId(), flowTaskCommentRecord.getDelegateAssginee());
                flowTaskCommentRecord.fillWith(task);
                flowTaskCommentService.save(flowTaskCommentRecord);
                return;
            }
            if (taskVariableData == null) {
                taskVariableData = new JSONObject();
            }
            this.handleMultiInstanceApprovalType(
                    task.getExecutionId(), flowTaskCommentRecord.getApprovalType(), taskVariableData);
            taskVariableData.put(FlowConstant.OPERATION_TYPE_VAR, flowTaskCommentRecord.getApprovalType());
            flowTaskCommentRecord.fillWith(task);
            // 获取表单模型编码
            FlowTaskExt flowTaskExt = flowTaskExtService.getByProcessDefinitionIdAndTaskKey(
                    task.getProcessDefinitionId(), task.getTaskDefinitionKey());
            if (flowTaskExt != null && flowTaskExt.getFormModel() != null) {
                flowTaskCommentRecord.setFormModelCode(flowTaskExt.getFormModelCode());
                // 获取表单模型提交的表单数据
                if (taskVariableData.containsKey(flowTaskExt.getFormModelVariable())) {
                    flowTaskCommentRecord.setFormModelData(taskVariableData.get(flowTaskExt.getFormModelVariable()).toString());
                }
            }
            flowTaskCommentService.save(flowTaskCommentRecord);
        }
        // 判断当前完成执行的任务，是否存在抄送设置。
        Object copyData = runtimeService.getVariable(
                task.getProcessInstanceId(), FlowConstant.COPY_DATA_MAP_PREFIX + task.getTaskDefinitionKey());
        if (copyData != null || passCopyData != null) {
            HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                    .processInstanceId(task.getProcessInstanceId()).singleResult();
            JSONObject copyDataJson = this.mergeCopyData(copyData, passCopyData);
            flowCopyService.save(task, historicProcessInstance, copyDataJson);
        }
        if (isSubmit) {
            runtimeService.setVariables(task.getExecutionId(), taskVariableData);
        }
        if (isSubmit) {
            runtimeService.setVariables(task.getExecutionId(), taskVariableData);
        }
        if (DelegationState.PENDING.equals(task.getDelegationState())) {
            taskService.resolveTask(task.getId(), taskVariableData);
        } else {
            taskService.complete(task.getId(), taskVariableData);
        }

    }

    private JSONObject mergeCopyData(Object copyData, JSONObject passCopyData) {
        // passCopyData是传阅数据，copyData是抄送数据。
        JSONObject resultCopyDataJson = passCopyData;
        if (resultCopyDataJson == null) {
            resultCopyDataJson = JSON.parseObject(copyData.toString());
        } else if (copyData != null) {
            JSONObject copyDataJson = JSON.parseObject(copyData.toString());
            for (Map.Entry<String, Object> entry : copyDataJson.entrySet()) {
                String value = resultCopyDataJson.getString(entry.getKey());
                if (value == null) {
                    resultCopyDataJson.put(entry.getKey(), entry.getValue());
                } else {
                    List<String> list1 = StrUtil.split(value, ",");
                    List<String> list2 = StrUtil.split(entry.getValue().toString(), ",");
                    Set<String> valueSet = new HashSet<>(list1);
                    valueSet.addAll(list2);
                    resultCopyDataJson.put(entry.getKey(), StrUtil.join(",", valueSet));
                }
            }
        }
        return resultCopyDataJson;
    }


    private String buildMutiSignAssigneeList(String operationListJson) {
        FlowTaskMultiSignAssign multiSignAssignee = null;
        List<FlowTaskOperation> taskOperationList = JSONArray.parseArray(operationListJson, FlowTaskOperation.class);
        for (FlowTaskOperation taskOperation : taskOperationList) {
            if ("multi_sign".equals(taskOperation.getType())) {
                multiSignAssignee = taskOperation.getMultiSignAssignee();
                break;
            }
        }
        Assert.notNull(multiSignAssignee);
        if (FlowTaskMultiSignAssign.ASSIGN_TYPE_USER.equals(multiSignAssignee.getAssigneeType())) {
            return multiSignAssignee.getAssigneeList();
        }
        Set<String> usernameSet = null;
        Set<String> idSet = CollUtil.newHashSet(StrUtil.split(multiSignAssignee.getAssigneeList(), ","));
        switch (multiSignAssignee.getAssigneeType()) {
            case FlowTaskMultiSignAssign.ASSIGN_TYPE_ROLE:
                usernameSet = wfUserClient.getUserIdByRoleIds(idSet);
                break;
            default:
                break;
        }
        return CollUtil.isEmpty(usernameSet) ? null : CollUtil.join(usernameSet, ",");
    }


    private void buildCopyData(List<FlowTaskExt> flowTaskExtList, Map<String, Object> variableMap) {
        for (FlowTaskExt flowTaskExt : flowTaskExtList) {
            if (StringUtils.isBlank(flowTaskExt.getCopyListJson())) {
                continue;
            }
            List<JSONObject> copyDataList = JSON.parseArray(flowTaskExt.getCopyListJson(), JSONObject.class);
            Map<String, Object> copyDataMap = new HashMap<>(copyDataList.size());
            for (JSONObject copyData : copyDataList) {
                String type = copyData.getString("type");
                String id = copyData.getString("id");
                copyDataMap.put(type, id == null ? "" : id);
            }
            variableMap.put(FlowConstant.COPY_DATA_MAP_PREFIX + flowTaskExt.getTaskId(), JSON.toJSONString(copyDataMap));
        }
    }

    /**
     * 构建多实例的指派人
     *
     * @param flowTaskExtList
     * @param variableMap
     */
    private void buildMultiInstanceAssigness(List<FlowTaskExt> flowTaskExtList, Map<String, Object> variableMap) {
        for (FlowTaskExt flowTaskExt : flowTaskExtList) {
            if (flowTaskExt.getMultiInstanceVariable() == null || flowTaskExt.getMultiInstanceAssigneeGroups() == null || flowTaskExt.getMultiInstanceAssigneeGroups().equals("")) {
                continue;
            }
            List<String> applyList = JSON.parseArray(flowTaskExt.getMultiInstanceAssigneeGroups(), String.class);
            variableMap.put(flowTaskExt.getMultiInstanceVariable(), applyList);//
        }
    }


    /**
     * 通过流程定义编号查询流程实例
     *
     * @param processDefinitionId
     * @return
     */
    public List<HistoricProcessInstance> getProcessInstanceByDefinitionId(String processDefinitionId) {
        return historyService.createHistoricProcessInstanceQuery().processDefinitionId(processDefinitionId).list();
    }

    /**
     * 获取第一个任务节点
     *
     * @param processInstanceId
     * @return
     */
    public Task getTaskByInstanceId(String processInstanceId) {
        // 获取流程启动后的第一个任务。
        Task task = taskService.createTaskQuery().processInstanceId(processInstanceId).active().singleResult();
        return task;
    }

    /**
     * 判断当前登录用户是否为流程实例中的用户任务的指派人。或是候选人之一。
     *
     * @param task 流程实例中的用户任务。
     * @return 是返回true，否则false。
     */
    public boolean isAssigneeOrCandidate(TaskInfo task) {
        String loginName = UserContext.getPhoneNumber();
        // assingee中记录的可能为部门、角色ID，不能使用以下两行进行盘点
//        if (StringUtils.isNotBlank(task.getAssignee())) {
//            return StrUtil.equals(loginName, task.getAssignee());
//        }
        TaskQuery query = taskService.createTaskQuery();
        this.buildCandidateCondition(query, loginName, task.getTaskDefinitionId());
        return query.active().count() != 0;
    }

    private void buildCandidateCondition(TaskQuery query, String loginName, String taskId) {
        Set<String> groupIdSet = new HashSet<>();
        // NOTE: 需要注意的是，部门Id、部门岗位Id，或者其他类型的分组Id，他们之间一定不能重复。
        String orgId = UserContext.get().getOrgId();
        if (StringUtils.isNotBlank(orgId)) {
            groupIdSet.add(orgId);
        }
        List<String> roleIds = UserContext.get().getRoleIds();
        if (roleIds != null) {
            groupIdSet.addAll(roleIds);
        }
        // 当审批对象为角色、部门时，assginee中记录的是角色、部门的Id。
        // 以下三个or，分别处理：1. candidatGroups是否包含当前登录用户所属角色或部门的ID; 2. assignee中是否为当前登录用户所属角色或部门的ID; 3. assignee或candidateAssignee中是否包含当前登录用户的ID。
        if (CollUtil.isNotEmpty(groupIdSet)) {
            query.or().taskCandidateGroupIn(groupIdSet).taskAssigneeIds(groupIdSet).taskCandidateOrAssigned(loginName).endOr();
        } else {
            query.taskCandidateOrAssigned(loginName);
        }
    }

    private void handleMultiInstanceApprovalType(String executionId, String approvalType, JSONObject taskVariableData) {
        if (StrUtil.isBlank(approvalType)) {
            return;
        }
        if (StrUtil.equalsAny(approvalType,
                FlowApprovalType.MULTI_AGREE,
                FlowApprovalType.MULTI_REFUSE,
                FlowApprovalType.MULTI_ABSTAIN)) {
            Map<String, Object> variables = runtimeService.getVariables(executionId);
            Integer agreeCount = (Integer) variables.get(FlowConstant.MULTI_AGREE_COUNT_VAR);
            Integer refuseCount = (Integer) variables.get(FlowConstant.MULTI_REFUSE_COUNT_VAR);
            Integer abstainCount = (Integer) variables.get(FlowConstant.MULTI_ABSTAIN_COUNT_VAR);
            Integer nrOfInstances = (Integer) variables.get(FlowConstant.NUMBER_OF_INSTANCES_VAR);
            taskVariableData.put(FlowConstant.MULTI_AGREE_COUNT_VAR, agreeCount);
            taskVariableData.put(FlowConstant.MULTI_REFUSE_COUNT_VAR, refuseCount);
            taskVariableData.put(FlowConstant.MULTI_ABSTAIN_COUNT_VAR, abstainCount);
            taskVariableData.put(FlowConstant.MULTI_SIGN_NUM_OF_INSTANCES_VAR, nrOfInstances);
            switch (approvalType) {
                case FlowApprovalType.MULTI_AGREE:
                    if (agreeCount == null) {
                        agreeCount = 0;
                    }
                    taskVariableData.put(FlowConstant.MULTI_AGREE_COUNT_VAR, agreeCount + 1);
                    break;
                case FlowApprovalType.MULTI_REFUSE:
                    if (refuseCount == null) {
                        refuseCount = 0;
                    }
                    taskVariableData.put(FlowConstant.MULTI_REFUSE_COUNT_VAR, refuseCount + 1);
                    break;
                case FlowApprovalType.MULTI_ABSTAIN:
                    if (abstainCount == null) {
                        abstainCount = 0;
                    }
                    taskVariableData.put(FlowConstant.MULTI_ABSTAIN_COUNT_VAR, abstainCount + 1);
                    break;
                default:
                    break;
            }
        }
    }

    /**
     * 判断指定的流程实例Id是否存在。
     *
     * @param processInstanceId 流程实例Id。
     * @return 存在返回true，否则false。
     */
    public boolean existActiveProcessInstance(String processInstanceId) {
        return runtimeService.createProcessInstanceQuery()
                .processInstanceId(processInstanceId).active().count() != 0;
    }

    /**
     * 获取指定的流程实例对象。
     *
     * @param processInstanceId 流程实例Id。
     * @return 流程实例对象。
     */
    public ProcessInstance getProcessInstance(String processInstanceId) {
        return runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
    }


    /**
     * 获取指定流程实例和任务Id的当前活动任务。
     *
     * @param processInstanceId 流程实例Id。
     * @param taskId            流程任务Id。
     * @return 当前流程实例的活动任务。
     */
    public Task getProcessInstanceActiveTask(String processInstanceId, String taskId) {
        TaskQuery query = taskService.createTaskQuery().processInstanceId(processInstanceId);
        if (StringUtils.isNotBlank(taskId)) {
            query.taskId(taskId);
        }
        return query.active().singleResult();
    }

    public void updateTaskAssignee(String taskId, String newAssignee) {
        taskService.setAssignee(taskId, newAssignee);
    }

    /**
     * 获取指定流程实例的当前活动任务列表。
     *
     * @param processInstanceId 流程实例Id。
     * @return 当前流程实例的活动任务。
     */
    public List<Task> getProcessInstanceActiveTaskList(String processInstanceId) {
        return taskService.createTaskQuery().processInstanceId(processInstanceId).list();
    }


    private List<SequenceFlow> getElementIncomingFlows(FlowElement source) {
        List<SequenceFlow> sequenceFlows = null;
        if (source instanceof org.flowable.bpmn.model.Task) {
            sequenceFlows = ((org.flowable.bpmn.model.Task) source).getIncomingFlows();
        } else if (source instanceof Gateway) {
            sequenceFlows = ((Gateway) source).getIncomingFlows();
        } else if (source instanceof SubProcess) {
            sequenceFlows = ((SubProcess) source).getIncomingFlows();
        } else if (source instanceof StartEvent) {
            sequenceFlows = ((StartEvent) source).getIncomingFlows();
        } else if (source instanceof EndEvent) {
            sequenceFlows = ((EndEvent) source).getIncomingFlows();
        }
        return sequenceFlows;
    }


    private List<SequenceFlow> getElementOutgoingFlows(FlowElement source) {
        List<SequenceFlow> sequenceFlows = null;
        if (source instanceof org.flowable.bpmn.model.Task) {
            sequenceFlows = ((org.flowable.bpmn.model.Task) source).getOutgoingFlows();
        } else if (source instanceof Gateway) {
            sequenceFlows = ((Gateway) source).getOutgoingFlows();
        } else if (source instanceof SubProcess) {
            sequenceFlows = ((SubProcess) source).getOutgoingFlows();
        } else if (source instanceof StartEvent) {
            sequenceFlows = ((StartEvent) source).getOutgoingFlows();
        } else if (source instanceof EndEvent) {
            sequenceFlows = ((EndEvent) source).getOutgoingFlows();
        }
        return sequenceFlows;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean stopProcessInstance(String processInstanceId, String stopReason) {
        List<Task> taskList = taskService.createTaskQuery().processInstanceId(processInstanceId).active().list();
        if (CollUtil.isEmpty(taskList)) {
            throw new FabosJsonApiErrorTip("数据验证失败，当前流程尚未开始或已经结束！");
        }
        for (Task task : taskList) {
            String currActivityId = task.getTaskDefinitionKey();
            BpmnModel bpmnModel = repositoryService.getBpmnModel(task.getProcessDefinitionId());
            FlowNode currFlow = (FlowNode) bpmnModel.getMainProcess().getFlowElement(currActivityId);
            if (currFlow == null) {
                List<SubProcess> subProcessList =
                        bpmnModel.getMainProcess().findFlowElementsOfType(SubProcess.class);
                for (SubProcess subProcess : subProcessList) {
                    FlowElement flowElement = subProcess.getFlowElement(currActivityId);
                    if (flowElement != null) {
                        currFlow = (FlowNode) flowElement;
                        break;
                    }
                }
            }
            EndEvent endEvent = bpmnModel.getMainProcess()
                    .findFlowElementsOfType(EndEvent.class, false).get(0);
            if (!(currFlow.getParentContainer().equals(endEvent.getParentContainer()))) {
                throw new FabosJsonApiErrorTip("数据验证失败，不能从子流程直接中止！");
            }
            // 保存原有的输出方向。
            List<SequenceFlow> oriSequenceFlows = Lists.newArrayList();
            oriSequenceFlows.addAll(currFlow.getOutgoingFlows());
            // 清空原有方向。
            currFlow.getOutgoingFlows().clear();
            // 建立新方向。
            SequenceFlow newSequenceFlow = new SequenceFlow();
            String uuid = UUID.randomUUID().toString().replace("-", "");
            newSequenceFlow.setId(uuid);
            newSequenceFlow.setSourceFlowElement(currFlow);
            newSequenceFlow.setTargetFlowElement(endEvent);
            currFlow.setOutgoingFlows(CollUtil.newArrayList(newSequenceFlow));
            // 完成任务并跳转到新方向。
            taskService.complete(task.getId());
            FlowTaskCommentRecord taskComment = new FlowTaskCommentRecord(task);
            taskComment.setApprovalType(FlowApprovalType.STOP);
            taskComment.setComments(stopReason);
            taskComment.setUserId(UserContext.getPhoneNumber());
            taskComment.setUserName(UserContext.getUserName());
            flowTaskCommentService.save(taskComment);
            // 回复原有输出方向。
            currFlow.setOutgoingFlows(oriSequenceFlows);
        }
        return true;
    }

    public void deleteProcessInstance(String processInstanceId, String deleteReason) {
        runtimeService.deleteProcessInstance(processInstanceId, deleteReason);
        historyService.deleteHistoricProcessInstance(processInstanceId);
    }

    /**
     * 判断当前登录用户是否为流程实例中的用户任务的指派人。或是候选人之一，如果是候选人则拾取该任务并成为指派人。
     * 如果都不是，就会返回具体的错误信息。
     *
     * @param task 流程实例中的用户任务。
     * @return 调用结果。
     */
    public Boolean verifyAssigneeOrCandidateAndClaim(Task task) {
        // 校验当前用户是否是该待办任务的候选人或指派人
        if (!this.isAssigneeOrCandidate(task)) {
            throw new FabosJsonApiErrorTip("数据验证失败，当前用户不是该待办任务的候选人或指派人，请刷新后重试！");
        }
        // 如果当前任务没有指派人
        if (task.getAssignee() == null) {
            String loginName = UserContext.getPhoneNumber();
            // 作为候选人主动拾取任务。
            taskService.claim(task.getId(), loginName);
        }
        return true;
    }

    /**
     * 获取指定的历史任务实例。
     *
     * @param processInstanceId 流程实例Id。
     * @param taskId            任务Id。
     * @return 历史任务实例。
     */
    public HistoricTaskInstance getHistoricTaskInstance(String processInstanceId, String taskId) {
        return historyService.createHistoricTaskInstanceQuery()
                .processInstanceId(processInstanceId).taskId(taskId).singleResult();
    }

    /**
     * 获取任务的指定本地变量。
     *
     * @param taskId       任务Id。
     * @param variableName 变量名。
     * @return 变量值。
     */
    public Object getTaskVariable(String taskId, String variableName) {
        return taskService.getVariable(taskId, variableName);
    }

    /**
     * 加签
     */
    public void addSign(String taskId, String newAssignees) {
        String[] assigneeArray = newAssignees.split(",");
        String currentOwnerPhone = UserContext.getPhoneNumber();
        for (String assignee : assigneeArray) {
            if (assignee != null && !assignee.trim().isEmpty()) {
                //电话号码正则校验
                if (!RegexConst.isPhoneNum(assignee)) {
                    throw new ServiceException("电话号码格式有误：" + assignee);
                }
                taskService.setOwner(taskId, currentOwnerPhone);
                taskService.delegateTask(taskId, assignee.trim());
            } else {
                log.warn("Invalid assignee found: {}", assignee);
            }
        }
    }


    /**
     * 根据任务id获取变量和表单数据
     *
     * @param taskId
     * @return
     */
    public Map<String, Object> getFlowVariablesByTaskId(String taskId) {
        HistoricTaskInstance historicTaskInstance = historyService.createHistoricTaskInstanceQuery().includeProcessVariables().finished().taskId(taskId).singleResult();
        if (Objects.nonNull(historicTaskInstance)) {
            return historicTaskInstance.getProcessVariables();
        } else {
            return taskService.getVariables(taskId);
        }
    }


    /**
     * 根据流程实例ID获取变量和表单数据
     *
     * @param processInstanceId
     * @return
     */
    public Map<String, Object> getFlowVariablesById(String processInstanceId) {
        List<HistoricVariableInstance> historicVariableInstanceList = historyService.createHistoricVariableInstanceQuery()
                .processInstanceId(processInstanceId)
                .list();
        Map<String, Object> map = new HashMap<>();
        if (CollectionUtils.isEmpty(historicVariableInstanceList)) {
            return map;
        }
        historicVariableInstanceList.stream().sorted(Comparator.comparing(HistoricVariableInstance::getCreateTime)).forEach(it -> {
            map.put(it.getVariableName(), it.getValue());
        });
        return map;
    }


    private Set<String> findChildProcessAllDirtyRoad(
            FlowElement source, Set<String> hasSequenceFlow, Set<String> dirtyRoads) {
        hasSequenceFlow = hasSequenceFlow == null ? new HashSet<>() : hasSequenceFlow;
        dirtyRoads = dirtyRoads == null ? new HashSet<>() : dirtyRoads;
        // 根据类型，获取出口连线
        List<SequenceFlow> sequenceFlows = getElementOutgoingFlows(source);
        if (sequenceFlows != null) {
            // 循环找到目标元素
            for (SequenceFlow sequenceFlow : sequenceFlows) {
                // 如果发现连线重复，说明循环了，跳过这个循环
                if (hasSequenceFlow.contains(sequenceFlow.getId())) {
                    continue;
                }
                // 添加已经走过的连线
                hasSequenceFlow.add(sequenceFlow.getId());
                // 添加脏路线
                dirtyRoads.add(sequenceFlow.getTargetFlowElement().getId());
                // 如果节点为子流程节点情况，则从节点中的第一个节点开始获取
                if (sequenceFlow.getTargetFlowElement() instanceof SubProcess) {
                    dirtyRoads = findChildProcessAllDirtyRoad(
                            (FlowElement) (((SubProcess) sequenceFlow.getTargetFlowElement()).getFlowElements().toArray()[0]), hasSequenceFlow, dirtyRoads);
                }
                // 继续迭代
                // 注意：已经经过的节点与连线都应该用浅拷贝出来的对象
                // 比如分支：a->b->c与a->d->c，走完a->b->c后走另一个路线是，已经经过的节点应该不包含a->b->c路线的数据
                dirtyRoads = findChildProcessAllDirtyRoad(
                        sequenceFlow.getTargetFlowElement(), new HashSet<>(hasSequenceFlow), dirtyRoads);
            }
        }
        return dirtyRoads;
    }

    private Boolean dirtyTargetInChildProcess(
            FlowElement source, Set<String> hasSequenceFlow, List<String> targets, Boolean inChildProcess) {
        hasSequenceFlow = hasSequenceFlow == null ? new HashSet<>() : hasSequenceFlow;
        inChildProcess = inChildProcess == null ? false : inChildProcess;
        // 根据类型，获取出口连线
        List<SequenceFlow> sequenceFlows = getElementOutgoingFlows(source);
        if (sequenceFlows != null && !inChildProcess) {
            // 循环找到目标元素
            for (SequenceFlow sequenceFlow : sequenceFlows) {
                // 如果发现连线重复，说明循环了，跳过这个循环
                if (hasSequenceFlow.contains(sequenceFlow.getId())) {
                    continue;
                }
                // 添加已经走过的连线
                hasSequenceFlow.add(sequenceFlow.getId());
                // 如果发现目标点在子流程上存在，说明只到子流程为止
                if (targets.contains(sequenceFlow.getTargetFlowElement().getId())) {
                    inChildProcess = true;
                    break;
                }
                // 如果节点为子流程节点情况，则从节点中的第一个节点开始获取
                if (sequenceFlow.getTargetFlowElement() instanceof SubProcess) {
                    inChildProcess = dirtyTargetInChildProcess((FlowElement) (((SubProcess) sequenceFlow.getTargetFlowElement()).getFlowElements().toArray()[0]), hasSequenceFlow, targets, inChildProcess);
                }
                // 继续迭代
                // 注意：已经经过的节点与连线都应该用浅拷贝出来的对象
                // 比如分支：a->b->c与a->d->c，走完a->b->c后走另一个路线是，已经经过的节点应该不包含a->b->c路线的数据
                inChildProcess = dirtyTargetInChildProcess(sequenceFlow.getTargetFlowElement(), new HashSet<>(hasSequenceFlow), targets, inChildProcess);
            }
        }
        return inChildProcess;
    }


    /**
     * 验证并获取流程的实时任务信息。
     *
     * @param task 流程引擎的任务对象。
     * @return 任务信息对象。
     */
    public TaskInfoVO verifyAndGetRuntimeTaskInfo(Task task) {
        if (task == null) {
            throw new FabosJsonApiErrorTip("数据验证失败，指定的任务Id，请刷新后重试！");
        }
        if (!isAssigneeOrCandidate(task)) {
            throw new FabosJsonApiErrorTip("数据验证失败，当前用户不是指派人也不是候选人之一！");
        }
        if (task.isSuspended()) {
            throw new FabosJsonApiErrorTip("任务处于挂起状态!");
        }
        if (StringUtils.isBlank(task.getFormKey())) {
            return null;
        }
        TaskInfoVO taskInfo = JSON.parseObject(StringEscapeUtils.unescapeHtml4(task.getFormKey()), TaskInfoVO.class);
        taskInfo.setTaskKey(task.getTaskDefinitionKey());
        return taskInfo;
    }

    public Process getBpmnModel(String processDefinitionId) {
        return repositoryService.getBpmnModel(processDefinitionId).getProcesses().get(0);
    }

    /**
     * 历史节点数据清洗，清洗掉又回滚导致的脏数据
     *
     * @param allElements              全部节点信息
     * @param historicTaskInstanceList 历史任务实例信息，数据采用开始时间升序
     * @return
     */
    public List<String> historicTaskInstanceClean(Collection<FlowElement> allElements, List<HistoricTaskInstance> historicTaskInstanceList) {
        // 会签节点收集
        List<String> multiTask = new ArrayList<>();
        allElements.forEach(flowElement -> {
            if (flowElement instanceof UserTask) {
                // 如果该节点的行为为会签行为，说明该节点为会签节点
                if (((UserTask) flowElement).getBehavior() instanceof ParallelMultiInstanceBehavior || ((UserTask) flowElement).getBehavior() instanceof SequentialMultiInstanceBehavior) {
                    multiTask.add(flowElement.getId());
                }
            }
        });
        // 循环放入栈，栈 LIFO：后进先出
        Stack<HistoricTaskInstance> stack = new Stack<>();
        historicTaskInstanceList.forEach(stack::push);
        // 清洗后的历史任务实例
        List<String> lastHistoricTaskInstanceList = new ArrayList<>();
        // 网关存在可能只走了部分分支情况，且还存在跳转废弃数据以及其他分支数据的干扰，因此需要对历史节点数据进行清洗
        // 临时用户任务 key
        StringBuilder userTaskKey = null;
        // 临时被删掉的任务 key，存在并行情况
        List<String> deleteKeyList = new ArrayList<>();
        // 临时脏数据线路
        List<Set<String>> dirtyDataLineList = new ArrayList<>();
        // 由某个点跳到会签点,此时出现多个会签实例对应 1 个跳转情况，需要把这些连续脏数据都找到
        // 会签特殊处理下标
        int multiIndex = -1;
        // 会签特殊处理 key
        StringBuilder multiKey = null;
        // 会签特殊处理操作标识
        boolean multiOpera = false;
        while (!stack.empty()) {
            // 从这里开始 userTaskKey 都还是上个栈的 key
            // 是否是脏数据线路上的点
            final boolean[] isDirtyData = {false};
            for (Set<String> oldDirtyDataLine : dirtyDataLineList) {
                if (oldDirtyDataLine.contains(stack.peek().getTaskDefinitionKey())) {
                    isDirtyData[0] = true;
                }
            }
            // 删除原因不为空，说明从这条数据开始回跳或者回退的
            // MI_END：会签完成后，其他未签到节点的删除原因，不在处理范围内
            if (stack.peek().getDeleteReason() != null && !"MI_END".equals(stack.peek().getDeleteReason())) {
                // 可以理解为脏线路起点
                String dirtyPoint = "";
                if (stack.peek().getDeleteReason().contains("Change activity to ")) {
                    dirtyPoint = stack.peek().getDeleteReason().replace("Change activity to ", "");
                }
                // 会签回退删除原因有点不同
                if (stack.peek().getDeleteReason().contains("Change parent activity to ")) {
                    dirtyPoint = stack.peek().getDeleteReason().replace("Change parent activity to ", "");
                }
                FlowElement dirtyTask = null;
                // 获取变更节点的对应的入口处连线
                // 如果是网关并行回退情况，会变成两条脏数据路线，效果一样
                for (FlowElement flowElement : allElements) {
                    if (flowElement.getId().equals(stack.peek().getTaskDefinitionKey())) {
                        dirtyTask = flowElement;
                    }
                }
                // 获取脏数据线路
                Set<String> dirtyDataLine = iteratorFindDirtyRoads(dirtyTask, null, null, Arrays.asList(dirtyPoint.split(",")), null);
                // 自己本身也是脏线路上的点，加进去
                dirtyDataLine.add(stack.peek().getTaskDefinitionKey());
                log.info(stack.peek().getTaskDefinitionKey() + "点脏路线集合：" + dirtyDataLine);
                // 是全新的需要添加的脏线路
                boolean isNewDirtyData = true;
                for (int i = 0; i < dirtyDataLineList.size(); i++) {
                    // 如果发现他的上个节点在脏线路内，说明这个点可能是并行的节点，或者连续驳回
                    // 这时，都以之前的脏线路节点为标准，只需合并脏线路即可，也就是路线补全
                    if (dirtyDataLineList.get(i).contains(userTaskKey.toString())) {
                        isNewDirtyData = false;
                        dirtyDataLineList.get(i).addAll(dirtyDataLine);
                    }
                }
                // 已确定时全新的脏线路
                if (isNewDirtyData) {
                    // deleteKey 单一路线驳回到并行，这种同时生成多个新实例记录情况，这时 deleteKey 其实是由多个值组成
                    // 按照逻辑，回退后立刻生成的实例记录就是回退的记录
                    // 至于驳回所生成的 Key，直接从删除原因中获取，因为存在驳回到并行的情况
                    deleteKeyList.add(dirtyPoint + ",");
                    dirtyDataLineList.add(dirtyDataLine);
                }
                // 添加后，现在这个点变成脏线路上的点了
                isDirtyData[0] = true;
            }
            // 如果不是脏线路上的点，说明是有效数据，添加历史实例 Key
            if (!isDirtyData[0]) {
                lastHistoricTaskInstanceList.add(stack.peek().getTaskDefinitionKey());
            }
            // 校验脏线路是否结束
            for (int i = 0; i < deleteKeyList.size(); i++) {
                // 如果发现脏数据属于会签，记录下下标与对应 Key，以备后续比对，会签脏数据范畴开始
                if (multiKey == null && multiTask.contains(stack.peek().getTaskDefinitionKey())
                        && deleteKeyList.get(i).contains(stack.peek().getTaskDefinitionKey())) {
                    multiIndex = i;
                    multiKey = new StringBuilder(stack.peek().getTaskDefinitionKey());
                }
                // 会签脏数据处理，节点退回会签清空
                // 如果在会签脏数据范畴中发现 Key改变，说明会签脏数据在上个节点就结束了，可以把会签脏数据删掉
                if (multiKey != null && !multiKey.toString().equals(stack.peek().getTaskDefinitionKey())) {
                    deleteKeyList.set(multiIndex, deleteKeyList.get(multiIndex).replace(stack.peek().getTaskDefinitionKey() + ",", ""));
                    multiKey = null;
                    // 结束进行下校验删除
                    multiOpera = true;
                }
                // 其他脏数据处理
                // 发现该路线最后一条脏数据，说明这条脏数据线路处理完了，删除脏数据信息
                // 脏数据产生的新实例中是否包含这条数据
                if (multiKey == null && deleteKeyList.get(i).contains(stack.peek().getTaskDefinitionKey())) {
                    // 删除匹配到的部分
                    deleteKeyList.set(i, deleteKeyList.get(i).replace(stack.peek().getTaskDefinitionKey() + ",", ""));
                }
                // 如果每组中的元素都以匹配过，说明脏数据结束
                if ("".equals(deleteKeyList.get(i))) {
                    // 同时删除脏数据
                    deleteKeyList.remove(i);
                    dirtyDataLineList.remove(i);
                    break;
                }
            }
            // 会签数据处理需要在循环外处理，否则可能导致溢出
            // 会签的数据肯定是之前放进去的所以理论上不会溢出，但还是校验下
            if (multiOpera && deleteKeyList.size() > multiIndex && "".equals(deleteKeyList.get(multiIndex))) {
                // 同时删除脏数据
                deleteKeyList.remove(multiIndex);
                dirtyDataLineList.remove(multiIndex);
                multiIndex = -1;
                multiOpera = false;
            }
            // pop() 方法与 peek() 方法不同，在返回值的同时，会把值从栈中移除
            // 保存新的 userTaskKey 在下个循环中使用
            userTaskKey = new StringBuilder(stack.pop().getTaskDefinitionKey());
        }
        log.info("清洗后的历史节点数据：" + lastHistoricTaskInstanceList);
        return lastHistoricTaskInstanceList;
    }


    /**
     * 从后向前寻路，获取所有脏线路上的点
     *
     * @param source          起始节点
     * @param passRoads       已经经过的点集合
     * @param hasSequenceFlow 已经经过的连线的 ID，用于判断线路是否重复
     * @param targets         目标脏线路终点
     * @param dirtyRoads      确定为脏数据的点，因为不需要重复，因此使用 set 存储
     * @return
     */
    public Set<String> iteratorFindDirtyRoads(FlowElement source, List<String> passRoads, Set<String> hasSequenceFlow, List<String> targets, Set<String> dirtyRoads) {
        passRoads = passRoads == null ? new ArrayList<>() : passRoads;
        dirtyRoads = dirtyRoads == null ? new HashSet<>() : dirtyRoads;
        hasSequenceFlow = hasSequenceFlow == null ? new HashSet<>() : hasSequenceFlow;
        // 如果该节点为开始节点，且存在上级子节点，则顺着上级子节点继续迭代
        if (source instanceof StartEvent && source.getSubProcess() != null) {
            dirtyRoads = iteratorFindDirtyRoads(source.getSubProcess(), passRoads, hasSequenceFlow, targets, dirtyRoads);
        }
        // 根据类型，获取入口连线
        List<SequenceFlow> sequenceFlows = getElementIncomingFlows(source);
        if (sequenceFlows != null) {
            // 循环找到目标元素
            for (SequenceFlow sequenceFlow : sequenceFlows) {
                // 如果发现连线重复，说明循环了，跳过这个循环
                if (hasSequenceFlow.contains(sequenceFlow.getId())) {
                    continue;
                }
                // 添加已经走过的连线
                hasSequenceFlow.add(sequenceFlow.getId());
                // 新增经过的路线
                passRoads.add(sequenceFlow.getSourceFlowElement().getId());
                // 如果此点为目标点，确定经过的路线为脏线路，添加点到脏线路中，然后找下个连线
                if (targets.contains(sequenceFlow.getSourceFlowElement().getId())) {
                    dirtyRoads.addAll(passRoads);
                    continue;
                }
                // 如果该节点为开始节点，且存在上级子节点，则顺着上级子节点继续迭代
                if (sequenceFlow.getSourceFlowElement() instanceof SubProcess) {
                    dirtyRoads = findChildProcessAllDirtyRoad((StartEvent) ((SubProcess) sequenceFlow.getSourceFlowElement()).getFlowElements().toArray()[0], null, dirtyRoads);
                    // 是否存在子流程上，true 是，false 否
                    Boolean isInChildProcess = dirtyTargetInChildProcess((StartEvent) ((SubProcess) sequenceFlow.getSourceFlowElement()).getFlowElements().toArray()[0], null, targets, null);
                    if (isInChildProcess) {
                        // 已在子流程上找到，该路线结束
                        continue;
                    }
                }
                // 继续迭代
                dirtyRoads = iteratorFindDirtyRoads(sequenceFlow.getSourceFlowElement(), passRoads, hasSequenceFlow, targets, dirtyRoads);
            }
        }
        return dirtyRoads;
    }

    /**
     * 根据正在运行的任务节点，迭代获取子级任务节点列表，向后找
     *
     * @param source          起始节点(退回节点)
     * @param runTaskKeyList  正在运行的任务 Key，用于校验任务节点是否是正在运行的节点
     * @param hasSequenceFlow 已经经过的连线的 ID，用于判断线路是否重复
     * @param userTaskList    需要撤回的用户任务列表
     * @return
     */
    public List<UserTask> iteratorFindChildUserTasks(FlowElement source, List<String> runTaskKeyList, Set<String> hasSequenceFlow, List<UserTask> userTaskList) {
        hasSequenceFlow = hasSequenceFlow == null ? new HashSet<>() : hasSequenceFlow;
        userTaskList = userTaskList == null ? new ArrayList<>() : userTaskList;
        // 如果该节点为开始节点，且存在上级子节点，则顺着上级子节点继续迭代
        if (source instanceof EndEvent && source.getSubProcess() != null) {
            userTaskList = iteratorFindChildUserTasks(source.getSubProcess(), runTaskKeyList, hasSequenceFlow, userTaskList);
        }
        // 根据类型，获取出口连线
        List<SequenceFlow> sequenceFlows = getElementOutgoingFlows(source);
        if (sequenceFlows != null) {
            // 循环找到目标元素
            for (SequenceFlow sequenceFlow : sequenceFlows) {
                // 如果发现连线重复，说明循环了，跳过这个循环
                if (hasSequenceFlow.contains(sequenceFlow.getId())) {
                    continue;
                }
                // 添加已经走过的连线
                hasSequenceFlow.add(sequenceFlow.getId());
                // 如果为用户任务类型，且任务节点的 Key 正在运行的任务中存在，添加
                if (sequenceFlow.getTargetFlowElement() instanceof UserTask && runTaskKeyList.contains((sequenceFlow.getTargetFlowElement()).getId())) {
                    userTaskList.add((UserTask) sequenceFlow.getTargetFlowElement());
                    continue;
                }
                // 如果节点为子流程节点情况，则从节点中的第一个节点开始获取
                if (sequenceFlow.getTargetFlowElement() instanceof SubProcess) {
                    List<UserTask> childUserTaskList = iteratorFindChildUserTasks((FlowElement) (((SubProcess) sequenceFlow.getTargetFlowElement()).getFlowElements().toArray()[0]), runTaskKeyList, hasSequenceFlow, null);
                    // 如果找到节点，则说明该线路找到节点，不继续向下找，反之继续
                    if (childUserTaskList != null && childUserTaskList.size() > 0) {
                        userTaskList.addAll(childUserTaskList);
                        continue;
                    }
                }
                // 继续迭代
                userTaskList = iteratorFindChildUserTasks(sequenceFlow.getTargetFlowElement(), runTaskKeyList, hasSequenceFlow, userTaskList);
            }
        }
        return userTaskList;
    }

    /**
     * 添加意见
     *
     * @param taskId
     * @param processInstanceId
     * @param comment
     */
    public void addComment(String taskId, String processInstanceId, String comment) {
        //1:正常意见,3:驳回意见
        taskService.addComment(taskId, processInstanceId, "3", comment);
    }


    private UserTask setExclusiveGateway(FlowElement targetFlow, Map proInsMap) {
        List<SequenceFlow> targetFlows = ((Gateway) targetFlow).getOutgoingFlows();
        for (SequenceFlow sequenceFlow : targetFlows) {
            try {
                if (checkFormDataByRuleEl(sequenceFlow.getConditionExpression(), proInsMap)) {
                    //目标节点信息
                    FlowElement targetFlowElement = sequenceFlow.getTargetFlowElement();
                    if (targetFlowElement instanceof UserTask) {
                        return (UserTask) targetFlowElement;
                    } else if (targetFlowElement instanceof EndEvent) {
                        return null;
                    } else if (targetFlowElement instanceof ServiceTask) {
                        return (UserTask) targetFlowElement;//不一定对 按照UserTask 来
                    } else if (targetFlowElement instanceof Gateway) {
                        //递归寻找
                        setExclusiveGateway(targetFlowElement, proInsMap);
                    } else if (targetFlowElement instanceof SubProcess) {
                        return null;//目前不管
                    } else {
                        return null;
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return null;
    }


    private Boolean checkFormDataByRuleEl(String el, Map<String, Object> formData) {
        ExpressionFactory factory = new ExpressionFactoryImpl();
        SimpleContext context = new SimpleContext();
        for (Object k : formData.keySet()) {
            if (formData.get(k) != null) {
                context.setVariable(k.toString(), factory.createValueExpression(formData.get(k), formData.get(k).getClass()));
            }
        }
        ValueExpression e = factory.createValueExpression(context, el, Boolean.class);
        return (Boolean) e.getValue(context);
    }


    /**
     * 流程记录
     */
    public InputStream processInstanceImage(String processInstanceId) throws IOException {
        HistoricProcessInstance hiProcInst =
                Optional.ofNullable(historyService.createHistoricProcessInstanceQuery().processInstanceId(processInstanceId).singleResult())
                        .orElseThrow(() -> new IOException("流程实例没有找到"));
        List<String> highLightedActivities = taskService.createTaskQuery().processInstanceId(processInstanceId).list().stream()
                .map(task -> runtimeService.createExecutionQuery().executionId(task.getExecutionId()).singleResult().getActivityId()).collect(Collectors.toList());
        List<String> flowIds;
        if (highLightedActivities.size() > 0) {
            flowIds = runtimeService.createActivityInstanceQuery()
                    .orderByActivityInstanceStartTime().asc()
                    .orderByActivityInstanceEndTime().asc()
                    .processInstanceId(processInstanceId).list().stream()
                    .filter(hiActInst -> "sequenceFlow".equals(hiActInst.getActivityType()))
                    .map(ActivityInstance::getActivityId)
                    .collect(Collectors.toCollection(CopyOnWriteArrayList::new));
        } else {
            flowIds = historyService.createHistoricActivityInstanceQuery().orderByHistoricActivityInstanceStartTime().asc()
                    .orderByHistoricActivityInstanceEndTime().asc()
                    .processInstanceId(processInstanceId).list().stream()
                    .filter(hiActInst -> "sequenceFlow".equals(hiActInst.getActivityType()))
                    .map(HistoricActivityInstance::getActivityId)
                    .collect(Collectors.toCollection(CopyOnWriteArrayList::new));
        }
        List<String> highLightedFlows = new CopyOnWriteArrayList<>();
        flowIds.forEach(id -> {
            if (highLightedFlows.contains(id)) {
                int index = highLightedFlows.indexOf(id);
                highLightedFlows.removeIf(id1 -> highLightedFlows.indexOf(id1) > index);
            } else {
                highLightedFlows.add(id);
            }
        });
        return new DefaultProcessDiagramGenerator().generateDiagram(
                repositoryService.getBpmnModel(hiProcInst.getProcessDefinitionId()),
                "PNG",
                highLightedActivities,
                highLightedFlows,
                "宋体",
                "宋体",
                "宋体",
                null,
                1.0,
                true);
    }

    /**
     * 回退到用户任务节点。如果没有指定，则回退到上一个任务。如果指定则回退到指定节点
     *
     * @param task      当前活动任务。
     * @param targetKey 指定回退到的任务标识。如果为null，则回退到上一个任务。
     * @param forReject true表示驳回，false为撤回。
     * @param reason    驳回或者撤销的原因。
     * @return 回退结果。
     */
    @Transactional
    public boolean backToRuntimeTask(Task task, String targetKey, boolean forReject, String reason) {
        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().processDefinitionId(task.getProcessDefinitionId()).singleResult();
        Collection<FlowElement> allElements = this.getProcessAllElements(processDefinition.getId());
        FlowElement source = null;
        // 获取跳转的节点元素
        FlowElement target = null;
        for (FlowElement flowElement : allElements) {
            if (flowElement.getId().equals(task.getTaskDefinitionKey())) {
                source = flowElement;
                if (StringUtils.isBlank(targetKey)) {
                    break;
                }
            }
            if (StringUtils.isNotBlank(targetKey)) {
                if (flowElement.getId().equals(targetKey)) {
                    target = flowElement;
                }
            }
        }
        if (StringUtils.isNotBlank(targetKey) && target == null) {
            throw new FabosJsonApiErrorTip("数据验证失败，被驳回的指定目标节点不存在！");
        }
        UserTask oneUserTask = null;
        List<String> targetIds = null;
        if (target == null) {
            List<UserTask> parentUserTaskList =
                    this.getParentUserTaskList(source, null, null);
            if (CollUtil.isEmpty(parentUserTaskList)) {
                throw new FabosJsonApiErrorTip("数据验证失败，当前节点为初始任务节点，不能驳回！");
            }
            // 获取活动ID, 即节点Key
            Set<String> parentUserTaskKeySet = new HashSet<>();
            parentUserTaskList.forEach(item -> parentUserTaskKeySet.add(item.getId()));
            List<HistoricActivityInstance> historicActivityIdList = historyService.createHistoricActivityInstanceQuery()
                    .processInstanceId(task.getProcessInstanceId()).orderByHistoricActivityInstanceStartTime().asc().list();
            // 数据清洗，将回滚导致的脏数据清洗掉
            List<String> lastHistoricTaskInstanceList =
                    this.cleanHistoricTaskInstance(allElements, historicActivityIdList);
            // 此时历史任务实例为倒序，获取最后走的节点
            targetIds = new ArrayList<>();
            // 循环结束标识，遇到当前目标节点的次数
            int number = 0;
            StringBuilder parentHistoricTaskKey = new StringBuilder();
            for (String historicTaskInstanceKey : lastHistoricTaskInstanceList) {
                // 当会签时候会出现特殊的，连续都是同一个节点历史数据的情况，这种时候跳过
                if (parentHistoricTaskKey.toString().equals(historicTaskInstanceKey)) {
                    continue;
                }
                parentHistoricTaskKey = new StringBuilder(historicTaskInstanceKey);
                if (historicTaskInstanceKey.equals(task.getTaskDefinitionKey())) {
                    number++;
                }
                if (number == 2) {
                    break;
                }
                // 如果当前历史节点，属于父级的节点，说明最后一次经过了这个点，需要退回这个点
                if (parentUserTaskKeySet.contains(historicTaskInstanceKey)) {
                    targetIds.add(historicTaskInstanceKey);
                }
            }
            // 目的获取所有需要被跳转的节点 currentIds
            // 取其中一个父级任务，因为后续要么存在公共网关，要么就是串行公共线路
            oneUserTask = parentUserTaskList.get(0);
        }
        // 获取所有正常进行的执行任务的活动节点ID，这些任务不能直接使用，需要找出其中需要撤回的任务
        List<Execution> runExecutionList =
                runtimeService.createExecutionQuery().processInstanceId(task.getProcessInstanceId()).list();
        List<String> runActivityIdList = runExecutionList.stream()
                .filter(c -> StringUtils.isNotBlank(c.getActivityId()))
                .map(Execution::getActivityId).collect(Collectors.toList());
        // 需驳回任务列表
        List<String> currentIds = new ArrayList<>();
        // 通过父级网关的出口连线，结合 runExecutionList 比对，获取需要撤回的任务
        List<FlowElement> currentFlowElementList = this.getChildUserTaskList(
                target != null ? target : oneUserTask, runActivityIdList, null, null);
        currentFlowElementList.forEach(item -> currentIds.add(item.getId()));
        if (target == null) {
            // 规定：并行网关之前节点必须需存在唯一用户任务节点，如果出现多个任务节点，则并行网关节点默认为结束节点，原因为不考虑多对多情况
            if (targetIds.size() > 1 && currentIds.size() > 1) {
                throw new FabosJsonApiErrorTip("数据验证失败，任务出现多对多情况，无法撤回！");
            }
        }

        // 删除需驳回任务相关的定时器任务
        deleteTimerJobsForRejectedTasks(task);
        AtomicReference<List<HistoricActivityInstance>> tmp = new AtomicReference<>();
        // 用于下面新增网关删除信息时使用
        String targetTmp = StringUtils.isNotBlank(targetKey) ? targetKey : String.join(",", targetIds);
        // currentIds 为活动ID列表
        // currentExecutionIds 为执行任务ID列表
        // 需要通过执行任务ID来设置驳回信息，活动ID不行
        currentIds.forEach(currentId -> runExecutionList.forEach(runExecution -> {
            if (StringUtils.isNotBlank(runExecution.getActivityId()) && currentId.equals(runExecution.getActivityId())) {
                // 查询当前节点的执行任务的历史数据
                tmp.set(historyService.createHistoricActivityInstanceQuery()
                        .processInstanceId(task.getProcessInstanceId())
                        .executionId(runExecution.getId())
                        .activityId(runExecution.getActivityId()).list());
                // 如果这个列表的数据只有 1 条数据
                // 网关肯定只有一条，且为包容网关或并行网关
                // 这里的操作目的是为了给网关在扭转前提前加上删除信息，结构与普通节点的删除信息一样，目的是为了知道这个网关也是有经过跳转的
                if (tmp.get() != null && tmp.get().size() == 1 && StringUtils.isNotBlank(tmp.get().get(0).getActivityType())
                        && ("parallelGateway".equals(tmp.get().get(0).getActivityType()) || "inclusiveGateway".equals(tmp.get().get(0).getActivityType()))) {
                    // singleResult 能够执行更新操作
                    // 利用 流程实例ID + 执行任务ID + 活动节点ID 来指定唯一数据，保证数据正确
                    historyService.createNativeHistoricActivityInstanceQuery().sql(
                            "UPDATE ACT_HI_ACTINST SET DELETE_REASON_ = 'Change activity to " + targetTmp + "'  WHERE PROC_INST_ID_='" + task.getProcessInstanceId() + "' AND EXECUTION_ID_='" + runExecution.getId() + "' AND ACT_ID_='" + runExecution.getActivityId() + "'").singleResult();
                }
            }
        }));
        try {
            UserContext.CurrentUser currentUser = UserContext.get();
            if (StringUtils.isNotBlank(targetKey)) {
                runtimeService.createChangeActivityStateBuilder()
                        .processInstanceId(task.getProcessInstanceId())
                        .moveActivityIdsToSingleActivityId(currentIds, targetKey).changeState();
            } else {
                // 如果父级任务多于 1 个，说明当前节点不是并行节点，原因为不考虑多对多情况
                if (targetIds.size() > 1) {
                    // 1 对 多任务跳转，currentIds 当前节点(1)，targetIds 跳转到的节点(多)
                    ChangeActivityStateBuilder builder = runtimeService.createChangeActivityStateBuilder()
                            .processInstanceId(task.getProcessInstanceId())
                            .moveSingleActivityIdToActivityIds(currentIds.get(0), targetIds);
                    for (String targetId : targetIds) {
                        FlowTaskCommentRecord taskComment = flowTaskCommentService.getLatestFlowTaskComment(task.getProcessInstanceId(), targetId);
                        // 如果驳回后的目标任务包含指定人，则直接通过变量回抄，如果没有则自动忽略该变量，不会给流程带来任何影响。
                        String submitLoginName = taskComment.getUserId();
                        if (StringUtils.isNotBlank(submitLoginName)) {
                            builder.localVariable(targetId, FlowConstant.TASK_APPOINTED_ASSIGNEE_VAR, submitLoginName);
                        }
                    }
                    builder.changeState();
                }
                // 如果父级任务只有一个，因此当前任务可能为网关中的任务
                if (targetIds.size() == 1) {
                    // 1 对 1 或 多 对 1 情况，currentIds 当前要跳转的节点列表(1或多)，targetIds.get(0) 跳转到的节点(1)
                    // 如果驳回后的目标任务包含指定人，则直接通过变量回抄，如果没有则自动忽略该变量，不会给流程带来任何影响。
                    ChangeActivityStateBuilder builder = runtimeService.createChangeActivityStateBuilder()
                            .processInstanceId(task.getProcessInstanceId())
                            .moveActivityIdsToSingleActivityId(currentIds, targetIds.get(0));
                    FlowTaskCommentRecord taskComment =
                            flowTaskCommentService.getLatestFlowTaskComment(task.getProcessInstanceId(), targetIds.get(0));
                    String submitLoginName = taskComment.getUserId();
                    if (StringUtils.isNotBlank(submitLoginName)) {
                        builder.localVariable(targetIds.get(0), FlowConstant.TASK_APPOINTED_ASSIGNEE_VAR, submitLoginName);
                    }
                    builder.changeState();
                }
            }
            FlowTaskCommentRecord comment = new FlowTaskCommentRecord();
            comment.setTaskId(task.getId());
            comment.setTaskKey(task.getTaskDefinitionKey());
            comment.setTaskName(task.getName());
            comment.setApprovalType(forReject ? FlowApprovalType.REJECT : FlowApprovalType.REVOKE);
            comment.setProcessInstanceId(task.getProcessInstanceId());
            comment.setUserId(currentUser.getPhoneNumber());
            comment.setUserName(currentUser.getUserName());
            comment.setAuto(false);
            comment.setComments(reason);
            flowTaskCommentService.save(comment);
        } catch (Exception e) {
            log.error("Failed to execute moveSingleActivityIdToActivityIds", e);
            throw new FabosJsonApiErrorTip(e.getMessage());
        }
        return true;
    }

    private Collection<FlowElement> getProcessAllElements(String processDefinitionId) {
        Process process = repositoryService.getBpmnModel(processDefinitionId).getProcesses().get(0);
        return this.getAllElements(process.getFlowElements(), null);
    }

    private Collection<FlowElement> getAllElements(Collection<FlowElement> flowElements, Collection<FlowElement> allElements) {
        allElements = allElements == null ? new ArrayList<>() : allElements;
        for (FlowElement flowElement : flowElements) {
            allElements.add(flowElement);
            if (flowElement instanceof SubProcess) {
                allElements = getAllElements(((SubProcess) flowElement).getFlowElements(), allElements);
            }
        }
        return allElements;
    }

    private List<UserTask> getParentUserTaskList(
            FlowElement source, Set<String> hasSequenceFlow, List<UserTask> userTaskList) {
        userTaskList = userTaskList == null ? new ArrayList<>() : userTaskList;
        hasSequenceFlow = hasSequenceFlow == null ? new HashSet<>() : hasSequenceFlow;
        // 如果该节点为开始节点，且存在上级子节点，则顺着上级子节点继续迭代
        if (source instanceof StartEvent && source.getSubProcess() != null) {
            userTaskList = getParentUserTaskList(source.getSubProcess(), hasSequenceFlow, userTaskList);
        }
        List<SequenceFlow> sequenceFlows = getElementIncomingFlows(source);
        if (sequenceFlows != null) {
            // 循环找到目标元素
            for (SequenceFlow sequenceFlow : sequenceFlows) {
                // 如果发现连线重复，说明循环了，跳过这个循环
                if (hasSequenceFlow.contains(sequenceFlow.getId())) {
                    continue;
                }
                // 添加已经走过的连线
                hasSequenceFlow.add(sequenceFlow.getId());
                // 类型为用户节点，则新增父级节点
                if (sequenceFlow.getSourceFlowElement() instanceof UserTask) {
                    userTaskList.add((UserTask) sequenceFlow.getSourceFlowElement());
                    continue;
                }
                // 类型为子流程，则添加子流程开始节点出口处相连的节点
                if (sequenceFlow.getSourceFlowElement() instanceof SubProcess) {
                    // 获取子流程用户任务节点
                    List<UserTask> childUserTaskList = findChildProcessUserTasks(
                            (StartEvent) ((SubProcess) sequenceFlow.getSourceFlowElement()).getFlowElements().toArray()[0], null, null);
                    // 如果找到节点，则说明该线路找到节点，不继续向下找，反之继续
                    if (childUserTaskList != null && childUserTaskList.size() > 0) {
                        userTaskList.addAll(childUserTaskList);
                        continue;
                    }
                }
                // 网关场景的继续迭代
                // 注意：已经经过的节点与连线都应该用浅拷贝出来的对象
                // 比如分支：a->b->c与a->d->c，走完a->b->c后走另一个路线是，已经经过的节点应该不包含a->b->c路线的数据
                userTaskList = getParentUserTaskList(
                        sequenceFlow.getSourceFlowElement(), new HashSet<>(hasSequenceFlow), userTaskList);
            }
        }
        return userTaskList;
    }

    private List<FlowElement> getChildUserTaskList(
            FlowElement source, List<String> runActiveIdList, Set<String> hasSequenceFlow, List<FlowElement> flowElementList) {
        hasSequenceFlow = hasSequenceFlow == null ? new HashSet<>() : hasSequenceFlow;
        flowElementList = flowElementList == null ? new ArrayList<>() : flowElementList;
        // 如果该节点为开始节点，且存在上级子节点，则顺着上级子节点继续迭代
        if (source instanceof EndEvent && source.getSubProcess() != null) {
            flowElementList = getChildUserTaskList(
                    source.getSubProcess(), runActiveIdList, hasSequenceFlow, flowElementList);
        }
        // 根据类型，获取出口连线
        List<SequenceFlow> sequenceFlows = getElementOutgoingFlows(source);
        if (sequenceFlows != null) {
            // 循环找到目标元素
            for (SequenceFlow sequenceFlow : sequenceFlows) {
                // 如果发现连线重复，说明循环了，跳过这个循环
                if (hasSequenceFlow.contains(sequenceFlow.getId())) {
                    continue;
                }
                // 添加已经走过的连线
                hasSequenceFlow.add(sequenceFlow.getId());
                // 如果为用户任务类型，或者为网关
                // 活动节点ID 在运行的任务中存在，添加
                FlowElement targetElement = sequenceFlow.getTargetFlowElement();
                if ((targetElement instanceof UserTask || targetElement instanceof Gateway)
                        && runActiveIdList.contains(targetElement.getId())) {
                    flowElementList.add(sequenceFlow.getTargetFlowElement());
                    continue;
                }
                // 如果节点为子流程节点情况，则从节点中的第一个节点开始获取
                if (sequenceFlow.getTargetFlowElement() instanceof SubProcess) {
                    List<FlowElement> childUserTaskList = getChildUserTaskList(
                            (FlowElement) (((SubProcess) sequenceFlow.getTargetFlowElement()).getFlowElements().toArray()[0]), runActiveIdList, hasSequenceFlow, null);
                    // 如果找到节点，则说明该线路找到节点，不继续向下找，反之继续
                    if (childUserTaskList != null && childUserTaskList.size() > 0) {
                        flowElementList.addAll(childUserTaskList);
                        continue;
                    }
                }
                // 继续迭代
                // 注意：已经经过的节点与连线都应该用浅拷贝出来的对象
                // 比如分支：a->b->c与a->d->c，走完a->b->c后走另一个路线是，已经经过的节点应该不包含a->b->c路线的数据
                flowElementList = getChildUserTaskList(
                        sequenceFlow.getTargetFlowElement(), runActiveIdList, new HashSet<>(hasSequenceFlow), flowElementList);
            }
        }
        return flowElementList;
    }

    private List<UserTask> findChildProcessUserTasks(FlowElement source, Set<String> hasSequenceFlow, List<UserTask> userTaskList) {
        hasSequenceFlow = hasSequenceFlow == null ? new HashSet<>() : hasSequenceFlow;
        userTaskList = userTaskList == null ? new ArrayList<>() : userTaskList;
        // 根据类型，获取出口连线
        List<SequenceFlow> sequenceFlows = getElementOutgoingFlows(source);
        if (sequenceFlows != null) {
            // 循环找到目标元素
            for (SequenceFlow sequenceFlow : sequenceFlows) {
                // 如果发现连线重复，说明循环了，跳过这个循环
                if (hasSequenceFlow.contains(sequenceFlow.getId())) {
                    continue;
                }
                // 添加已经走过的连线
                hasSequenceFlow.add(sequenceFlow.getId());
                // 如果为用户任务类型，且任务节点的 Key 正在运行的任务中存在，添加
                if (sequenceFlow.getTargetFlowElement() instanceof UserTask) {
                    userTaskList.add((UserTask) sequenceFlow.getTargetFlowElement());
                    continue;
                }
                // 如果节点为子流程节点情况，则从节点中的第一个节点开始获取
                if (sequenceFlow.getTargetFlowElement() instanceof SubProcess) {
                    List<UserTask> childUserTaskList = findChildProcessUserTasks((FlowElement) (((SubProcess) sequenceFlow.getTargetFlowElement()).getFlowElements().toArray()[0]), hasSequenceFlow, null);
                    // 如果找到节点，则说明该线路找到节点，不继续向下找，反之继续
                    if (childUserTaskList != null && childUserTaskList.size() > 0) {
                        userTaskList.addAll(childUserTaskList);
                        continue;
                    }
                }
                // 继续迭代
                // 注意：已经经过的节点与连线都应该用浅拷贝出来的对象
                // 比如分支：a->b->c与a->d->c，走完a->b->c后走另一个路线是，已经经过的节点应该不包含a->b->c路线的数据
                userTaskList = findChildProcessUserTasks(sequenceFlow.getTargetFlowElement(), new HashSet<>(hasSequenceFlow), userTaskList);
            }
        }
        return userTaskList;
    }

    private List<String> cleanHistoricTaskInstance(
            Collection<FlowElement> allElements, List<HistoricActivityInstance> historicActivityList) {
        // 会签节点收集
        List<String> multiTask = new ArrayList<>();
        allElements.forEach(flowElement -> {
            if (flowElement instanceof UserTask) {
                // 如果该节点的行为为会签行为，说明该节点为会签节点
                if (((UserTask) flowElement).getBehavior() instanceof ParallelMultiInstanceBehavior
                        || ((UserTask) flowElement).getBehavior() instanceof SequentialMultiInstanceBehavior) {
                    multiTask.add(flowElement.getId());
                }
            }
        });
        // 循环放入栈，栈 LIFO：后进先出
        Stack<HistoricActivityInstance> stack = new Stack<>();
        historicActivityList.forEach(stack::push);
        // 清洗后的历史任务实例
        List<String> lastHistoricTaskInstanceList = new ArrayList<>();
        // 网关存在可能只走了部分分支情况，且还存在跳转废弃数据以及其他分支数据的干扰，因此需要对历史节点数据进行清洗
        // 临时用户任务 key
        StringBuilder userTaskKey = null;
        // 临时被删掉的任务 key，存在并行情况
        List<String> deleteKeyList = new ArrayList<>();
        // 临时脏数据线路
        List<Set<String>> dirtyDataLineList = new ArrayList<>();
        // 由某个点跳到会签点,此时出现多个会签实例对应 1 个跳转情况，需要把这些连续脏数据都找到
        // 会签特殊处理下标
        int multiIndex = -1;
        // 会签特殊处理 key
        StringBuilder multiKey = null;
        // 会签特殊处理操作标识
        boolean multiOpera = false;
        while (!stack.empty()) {
            // 从这里开始 userTaskKey 都还是上个栈的 key
            // 是否是脏数据线路上的点
            final boolean[] isDirtyData = {false};
            for (Set<String> oldDirtyDataLine : dirtyDataLineList) {
                if (oldDirtyDataLine.contains(stack.peek().getActivityId())) {
                    isDirtyData[0] = true;
                }
            }
            // 删除原因不为空，说明从这条数据开始回跳或者回退的
            // MI_END：会签完成后，其他未签到节点的删除原因，不在处理范围内
            if (stack.peek().getDeleteReason() != null && !"MI_END".equals(stack.peek().getDeleteReason())) {
                // 可以理解为脏线路起点
                String dirtyPoint = "";
                if (stack.peek().getDeleteReason().contains("Change activity to ")) {
                    dirtyPoint = stack.peek().getDeleteReason().replace("Change activity to ", "");
                }
                // 会签回退删除原因有点不同
                if (stack.peek().getDeleteReason().contains("Change parent activity to ")) {
                    dirtyPoint = stack.peek().getDeleteReason().replace("Change parent activity to ", "");
                }
                FlowElement dirtyTask = null;
                // 获取变更节点的对应的入口处连线
                // 如果是网关并行回退情况，会变成两条脏数据路线，效果一样
                for (FlowElement flowElement : allElements) {
                    if (flowElement.getId().equals(stack.peek().getActivityId())) {
                        dirtyTask = flowElement;
                    }
                }
                // 获取脏数据线路
                Set<String> dirtyDataLine = iteratorFindDirtyRoads(
                        dirtyTask, null, null, StrUtil.split(dirtyPoint, ','), null);
                // 自己本身也是脏线路上的点，加进去
                dirtyDataLine.add(stack.peek().getActivityId());
                log.info(stack.peek().getActivityId() + "点脏路线集合：" + dirtyDataLine);
                // 是全新的需要添加的脏线路
                boolean isNewDirtyData = true;
                for (Set<String> strings : dirtyDataLineList) {
                    // 如果发现他的上个节点在脏线路内，说明这个点可能是并行的节点，或者连续驳回
                    // 这时，都以之前的脏线路节点为标准，只需合并脏线路即可，也就是路线补全
                    if (strings.contains(userTaskKey.toString())) {
                        isNewDirtyData = false;
                        strings.addAll(dirtyDataLine);
                    }
                }
                // 已确定时全新的脏线路
                if (isNewDirtyData) {
                    // deleteKey 单一路线驳回到并行，这种同时生成多个新实例记录情况，这时 deleteKey 其实是由多个值组成
                    // 按照逻辑，回退后立刻生成的实例记录就是回退的记录
                    // 至于驳回所生成的 Key，直接从删除原因中获取，因为存在驳回到并行的情况
                    deleteKeyList.add(dirtyPoint + ",");
                    dirtyDataLineList.add(dirtyDataLine);
                }
                // 添加后，现在这个点变成脏线路上的点了
                isDirtyData[0] = true;
            }
            // 如果不是脏线路上的点，说明是有效数据，添加历史实例 Key
            if (!isDirtyData[0]) {
                lastHistoricTaskInstanceList.add(stack.peek().getActivityId());
            }
            // 校验脏线路是否结束
            for (int i = 0; i < deleteKeyList.size(); i++) {
                // 如果发现脏数据属于会签，记录下下标与对应 Key，以备后续比对，会签脏数据范畴开始
                if (multiKey == null && multiTask.contains(stack.peek().getActivityId())
                        && deleteKeyList.get(i).contains(stack.peek().getActivityId())) {
                    multiIndex = i;
                    multiKey = new StringBuilder(stack.peek().getActivityId());
                }
                // 会签脏数据处理，节点退回会签清空
                // 如果在会签脏数据范畴中发现 Key改变，说明会签脏数据在上个节点就结束了，可以把会签脏数据删掉
                if (multiKey != null && !multiKey.toString().equals(stack.peek().getActivityId())) {
                    deleteKeyList.set(multiIndex, deleteKeyList.get(multiIndex).replace(stack.peek().getActivityId() + ",", ""));
                    multiKey = null;
                    // 结束进行下校验删除
                    multiOpera = true;
                }
                // 其他脏数据处理
                // 发现该路线最后一条脏数据，说明这条脏数据线路处理完了，删除脏数据信息
                // 脏数据产生的新实例中是否包含这条数据
                if (multiKey == null && deleteKeyList.get(i).contains(stack.peek().getActivityId())) {
                    // 删除匹配到的部分
                    deleteKeyList.set(i, deleteKeyList.get(i).replace(stack.peek().getActivityId() + ",", ""));
                }
                // 如果每组中的元素都以匹配过，说明脏数据结束
                if ("".equals(deleteKeyList.get(i))) {
                    // 同时删除脏数据
                    deleteKeyList.remove(i);
                    dirtyDataLineList.remove(i);
                    break;
                }
            }
            // 会签数据处理需要在循环外处理，否则可能导致溢出
            // 会签的数据肯定是之前放进去的所以理论上不会溢出，但还是校验下
            if (multiOpera && deleteKeyList.size() > multiIndex && "".equals(deleteKeyList.get(multiIndex))) {
                // 同时删除脏数据
                deleteKeyList.remove(multiIndex);
                dirtyDataLineList.remove(multiIndex);
                multiIndex = -1;
                multiOpera = false;
            }
            // pop() 方法与 peek() 方法不同，在返回值的同时，会把值从栈中移除
            // 保存新的 userTaskKey 在下个循环中使用
            userTaskKey = new StringBuilder(stack.pop().getActivityId());
        }
        log.info("清洗后的历史节点数据：" + lastHistoricTaskInstanceList);
        return lastHistoricTaskInstanceList;
    }

    /**
     * 判断是否为用户组用户
     *
     * @param taskId
     * @param groupId
     * @return
     */
    public boolean isCandidateUser(String taskId, String groupId) {
        if (StringUtils.isBlank(groupId)) {
            return false;
        }
        List<IdentityLink> idList = taskService.getIdentityLinksForTask(taskId);
        if (!CollectionUtils.isEmpty(idList)) {
            for (IdentityLink identityLink : idList) {
                if (groupId.equals(identityLink.getGroupId())) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 转移组用户
     *
     * @param taskId
     * @param oldAssignees
     * @param newAssignees
     */
    public void transferCandidateUser(String taskId, String oldAssignees, String newAssignees) {
        addCandidateUser(taskId, newAssignees);
        deleteCandidateUser(taskId, oldAssignees);
    }

    /**
     * 添加组用户
     *
     * @param taskId
     * @param newAssignees
     */
    public void addCandidateUser(String taskId, String newAssignees) {
        taskService.addCandidateUser(taskId, newAssignees);
    }


    /**
     * 删除组用户
     *
     * @param taskId
     * @param assignees
     */
    public void deleteCandidateUser(String taskId, String assignees) {
        taskService.deleteCandidateUser(taskId, assignees);
    }

    /**
     * 删除需驳回任务相关的定时器任务
     *
     * @param runExecutionList 正在运行的执行任务列表
     * @param currentIds       需要驳回的活动ID列表
     */
    private void deleteTimerJobsForRejectedTasks(Task task) {
        try {
            // 删除所有定时器任务
            managementService.createTimerJobQuery()
                    .processInstanceId(task.getProcessInstanceId())
                    .list()
                    .forEach(timerJob -> {
                        try {
                            managementService.deleteTimerJob(timerJob.getId());
                            log.info("通过流程实例删除定时器任务成功: timerJobId={}, processInstanceId={}",
                                    timerJob.getId(), task.getProcessInstanceId());
                        } catch (Exception e) {
                            log.warn("通过流程实例删除定时器任务失败: timerJobId={}, processInstanceId={}, error={}",
                                    timerJob.getId(), task.getProcessInstanceId(), e.getMessage());
                        }
                    });

        } catch (Exception e) {
            log.error("删除驳回任务相关的定时器任务时发生异常", e);
            // 这里不抛出异常，避免影响主要的驳回流程
        }
    }
}
