package cec.jiutian.bc.infra.config;

import cec.jiutian.bc.infra.handler.FlowTimeoutHandler;
import org.flowable.engine.impl.bpmn.parser.factory.DefaultActivityBehaviorFactory;
import org.flowable.spring.SpringProcessEngineConfiguration;
import org.flowable.spring.boot.EngineConfigurationConfigurer;
import org.flowable.spring.job.service.SpringAsyncExecutor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 工作流超时配置
 */
@Configuration
public class FlowTimeoutConfiguration implements EngineConfigurationConfigurer<SpringProcessEngineConfiguration> {
    @Override
    public void configure(SpringProcessEngineConfiguration engineConfiguration) {
        engineConfiguration.setActivityBehaviorFactory(customActivityBehaviorFactory());
        engineConfiguration.setAsyncExecutorActivate(true);
        engineConfiguration.setAsyncExecutor(springAsyncExecutor());
        engineConfiguration.addCustomJobHandler(timeoutHandler());
    }
    @Bean
    public DefaultActivityBehaviorFactory customActivityBehaviorFactory() {
        return new DefaultActivityBehaviorFactory();
    }

    @Bean
    public SpringAsyncExecutor springAsyncExecutor() {
        return new SpringAsyncExecutor();
    }

    @Bean
    public FlowTimeoutHandler timeoutHandler() {
        return new FlowTimeoutHandler();
    }
}
