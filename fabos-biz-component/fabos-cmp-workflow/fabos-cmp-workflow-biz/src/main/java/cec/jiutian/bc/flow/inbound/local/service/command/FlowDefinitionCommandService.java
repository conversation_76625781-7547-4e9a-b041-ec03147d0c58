package cec.jiutian.bc.flow.inbound.local.service.command;

import cec.jiutian.bc.flow.inbound.local.service.query.FlowDefinitionQuery;
import cec.jiutian.bc.flow.inbound.message.FlowDefinitionDTO;
import cec.jiutian.bc.flow.inbound.message.IdOperateDTO;
import cec.jiutian.bc.flow.outbound.message.TaskInfoVO;
import cec.jiutian.bc.infra.enums.FlowDefinitionStatus;
import cec.jiutian.bc.infra.enums.FlowTaskType;
import cec.jiutian.common.util.StringUtils;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.bc.flow.domain.model.entity.FlowDefinition;
import cec.jiutian.bc.flow.domain.model.service.FlowDefinitionApiService;
import cec.jiutian.bc.flow.domain.model.service.FlowDefinitionService;
import cec.jiutian.bc.flow.domain.task.entity.FlowTaskExt;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.flowable.bpmn.converter.BpmnXMLConverter;
import org.flowable.bpmn.model.*;
import org.flowable.bpmn.model.Process;
import org.springframework.stereotype.Service;
import org.springframework.util.StreamUtils;
import javax.xml.stream.XMLStreamException;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Collection;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class FlowDefinitionCommandService {

    @Resource
    private FlowDefinitionService flowDefinitionService;
    @Resource
    private FlowDefinitionApiService flowDefinitionApiService;

    public Boolean addModel(FlowDefinitionDTO createDTO){
       return flowDefinitionService.add(createDTO);
    }

    public Object getMainFlowDefinitionList(FlowDefinitionQuery query){
        query.setMainVersion(true);
        return flowDefinitionService.getFlowDefinitionList(query);
    }

    public Object getFlowDefinitionById(FlowDefinitionQuery query){
        return flowDefinitionService.getFlowDefinitionById(query.getId());
    }


    public Object getFlowByProcessDefinitionId(String definitionId){
        return flowDefinitionService.getFlowByProcessDefinitionId(definitionId);
    }

    public boolean effective(IdOperateDTO idDTO){
        FlowDefinition flowDefinition=flowDefinitionService.getDefinitionById(idDTO.getId());
        if(flowDefinition==null){
            throw new FabosJsonApiErrorTip("主键ID不存在!");
        }
        if(!FlowDefinitionStatus.INVALID.equals(flowDefinition.getStatus())){
            throw new FabosJsonApiErrorTip("只能生效被失效的流程定义!");
        }
       return flowDefinitionService.updateStatusById(idDTO.getId(),FlowDefinitionStatus.ACTIVE);
    }

    public boolean disable(IdOperateDTO idDTO){
        FlowDefinition flowDefinition=flowDefinitionService.getDefinitionById(idDTO.getId());
        if(flowDefinition==null){
            throw new FabosJsonApiErrorTip("主键ID不存在!");
        }
        if(!FlowDefinitionStatus.ACTIVE.equals(flowDefinition.getStatus())){
            throw new FabosJsonApiErrorTip("只能失效被生效的流程定义!");
        }
       return flowDefinitionService.updateStatusById(idDTO.getId(),FlowDefinitionStatus.INVALID);
    }


    public Object getWaitApproveTemplateList(FlowDefinitionQuery query){
        query.setStatus(FlowDefinitionStatus.REVIEW);
        return flowDefinitionService.getFlowDefinitionList(query);
    }

    public Object getFlowDefinitionListByKey(FlowDefinitionQuery query){
        if(StringUtils.isBlank(query.getProcessDefinitionKey())){
            throw new FabosJsonApiErrorTip("processDefinitionKey不能为空!");
        }
        return flowDefinitionService.getFlowDefinitionList(query);
    }

    public Boolean updateDefinition(FlowDefinitionDTO updateDTO){
        return flowDefinitionService.update(updateDTO);
    }

    public Boolean deleteDefinition(IdOperateDTO idDTO){
        return flowDefinitionService.delete(idDTO);
    }

    /**
     * 切换版本
     * @param idDTO
     * @return
     */
    public boolean switchVersion(IdOperateDTO idDTO){
        try {
            return flowDefinitionService.switchVersion(idDTO);
        } catch (XMLStreamException e) {
            throw new FabosJsonApiErrorTip(e.getMessage());
        }
    }

    public Boolean approve(IdOperateDTO idDTO){
        if(UserContext.get()==null){
            throw new FabosJsonApiErrorTip("当前用户未登录，请登录后再试！");
        }
        FlowDefinition flowDefinition = flowDefinitionService.getFlowDefinitionById(idDTO.getId());
        if(flowDefinition==null){
            throw new FabosJsonApiErrorTip("流程模型Id不存在！");
        }
        try {
            List<FlowTaskExt> flowTaskExtList = flowDefinitionService.buildTaskExtList(flowDefinition);
          return flowDefinitionService.approve(flowDefinition,flowTaskExtList);
        }catch (Exception e){
            log.error("e={}",e);
            throw new FabosJsonApiErrorTip(e.getMessage());
        }
    }

    public Boolean reject(IdOperateDTO idDTO){
        return flowDefinitionService.reject(idDTO);
    }

    private TaskInfoVO verifyAndGetInitialTaskInfo(FlowDefinition flowDefinition) {
        if(UserContext.get()==null){
            throw new FabosJsonApiErrorTip("当前用户未登录，请登录后再试！");
        }
        BpmnModel bpmnModel = flowDefinitionApiService.convertToBpmnModel(flowDefinition.getBpmnXml());
        Process process = bpmnModel.getMainProcess();
        if (process == null) {
            throw new FabosJsonApiErrorTip("数据验证失败，当前流程标识 [" + flowDefinition.getProcessDefinitionKey() + "] 关联的流程模型并不存在！");
        }
        if (flowDefinition.getFlowForm() == null||StringUtils.isBlank(flowDefinition.getFlowForm().getFormJson())) {
            throw new FabosJsonApiErrorTip("数据验证失败，当前流程表单不能为空！");
        }
        if (StringUtils.isBlank(flowDefinition.getFlowForm().getFormName())) {
            throw new FabosJsonApiErrorTip("数据验证失败，当前流程表单名称不能为空!");
        }
        Collection<FlowElement> elementList = process.getFlowElements();
        FlowElement startEvent = null;
        FlowElement firstTask = null;
        // 这里我们只定位流程模型中的第二个节点。
        for (FlowElement flowElement : elementList) {
            if (flowElement instanceof StartEvent) {
                startEvent = flowElement;
                break;
            }
        }
        if (startEvent == null) {
            throw new FabosJsonApiErrorTip("数据验证失败，当前流程图没有包含 [开始事件] 节点，请修改流程图！");
        }
        for (FlowElement flowElement : elementList) {
            if (flowElement instanceof SequenceFlow) {
                SequenceFlow sequenceFlow = (SequenceFlow) flowElement;
                if (sequenceFlow.getSourceFlowElement().equals(startEvent)) {
                    firstTask = sequenceFlow.getTargetFlowElement();
                    break;
                }
            }
        }
        if (firstTask == null) {
            throw new FabosJsonApiErrorTip("数据验证失败，当前流程图没有包含 [开始事件] 节点没有任何连线，请修改流程图！");
        }
        TaskInfoVO taskInfoVO;
        if (firstTask instanceof UserTask) {
            UserTask userTask = (UserTask) firstTask;
            String formKey = userTask.getFormKey();
            if (StrUtil.isNotBlank(formKey)) {
                taskInfoVO = JSON.parseObject(formKey, TaskInfoVO.class);
            } else {
                taskInfoVO = new TaskInfoVO();
            }
            taskInfoVO.setAssignee(userTask.getAssignee());
            taskInfoVO.setTaskKey(userTask.getId());
            taskInfoVO.setTaskType(FlowTaskType.USER_TYPE);
            Map<String, List<ExtensionElement>> extensionMap = userTask.getExtensionElements();
            if (MapUtil.isNotEmpty(extensionMap)) {
                taskInfoVO.setOperationList(flowDefinitionService.buildOperationListExtensionElement(extensionMap));
                taskInfoVO.setVariableList(flowDefinitionService.buildVariableListExtensionElement(extensionMap));
            }
        } else {
            taskInfoVO = new TaskInfoVO();
            taskInfoVO.setTaskType(FlowTaskType.OTHER_TYPE);
        }
        return taskInfoVO;
    }

    /**
     * 提交审批
     * @param idDTO
     * @return
     */
    public Boolean release(IdOperateDTO idDTO){
        FlowDefinition flowDefinition = flowDefinitionService.getFlowDefinitionById(idDTO.getId());
        if(flowDefinition==null){
            throw new FabosJsonApiErrorTip("流程模型Id不存在！");
        }
        TaskInfoVO taskInfoVO = this.verifyAndGetInitialTaskInfo(flowDefinition);
        String taskInfo = taskInfoVO == null ? null : JSON.toJSONString(taskInfoVO);
        return flowDefinitionService.release(flowDefinition,taskInfo);

    }

    /**
     * 获取指定流程定义的流程图。
     *
     * @param processDefinitionId 流程定义Id
     * @return 流程图。
     */
    public String getProcessBpmnByDefinitionId(String processDefinitionId) {
        BpmnXMLConverter converter = new BpmnXMLConverter();
        try {
            BpmnModel bpmnModel = flowDefinitionApiService.getBpmnModelByDefinitionId(processDefinitionId);
            byte[] xmlBytes = converter.convertToXML(bpmnModel);
            InputStream in = new ByteArrayInputStream(xmlBytes);
            return StreamUtils.copyToString(in, StandardCharsets.UTF_8);
        } catch (IOException e) {
            throw new FabosJsonApiErrorTip(e.getMessage());
        }
    }

    /**
     * 验证并获取流程对象
     * @param processDefinitionKey
     * @return
     */
    public FlowDefinition verifyAndGetFlowDefinition(String processDefinitionKey){
        FlowDefinition flowDefinition=flowDefinitionService.getMainFlowDefinitionByKey(processDefinitionKey);
        if(flowDefinition==null){
            throw new FabosJsonApiErrorTip("数据验证失败，该流程并不存在!");
        }
        if(!FlowDefinitionStatus.ACTIVE.equals(flowDefinition.getStatus())){
            throw new FabosJsonApiErrorTip("数据验证失败，该流程没有生效!");
        }
        return flowDefinition;
    }

    public FlowDefinition getNewFlowDefinitionByKey(String processDefinitionKey){
        return flowDefinitionService.getNewFlowDefinitionByKey(processDefinitionKey);
    }


}
