package cec.jiutian.bc.flow.domain.task.service;

import cec.jiutian.bc.flow.domain.model.entity.FlowDefinition;
import cec.jiutian.bc.flow.domain.task.entity.FlowInstanceOrder;
import cec.jiutian.bc.flow.domain.task.entity.FlowInstanceOrderHistory;
import cec.jiutian.bc.flow.inbound.local.service.query.FlowTaskQuery;
import cec.jiutian.bc.flow.inbound.message.FlowStartInstanceDTO;
import cec.jiutian.bc.flow.outbound.adapter.repository.FlowInstanceOrderRepository;
import cec.jiutian.bc.infra.enums.FlowInstanceStatus;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.common.util.StringUtils;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.data.jpa.JpaCrud;
import com.alibaba.fastjson2.JSONObject;
import jakarta.annotation.Resource;
import jakarta.persistence.criteria.Predicate;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.runtime.ProcessInstance;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 流程实例工单
 */
@Slf4j
@Service
public class FlowInstanceOrderService {

    @Resource
    private JpaCrud jpaCrud;

    @Resource
    private FlowInstanceOrderRepository flowInstanceOrderRepository;

    /**
     * 保存流程实例工单
     * @param instance
     * @return
     */
    public boolean save(ProcessInstance instance, FlowDefinition flowDefinition, FlowStartInstanceDTO startInstanceDTO){
        FlowInstanceOrder flowInstanceOrder = new FlowInstanceOrder();
        flowInstanceOrder.setModelName(flowDefinition.getModelName());
        flowInstanceOrder.setProcessDefinitionKey(flowDefinition.getProcessDefinitionKey());
        flowInstanceOrder.setProcessDefinitionName(flowDefinition.getProcessDefinitionName());
        flowInstanceOrder.setProcessDefinitionId(flowDefinition.getProcessDefinitionId());
        flowInstanceOrder.setProcessDefinitionVersion(flowDefinition.getProcessDefinitionVersion());
        flowInstanceOrder.setProcessInstanceId(instance.getId());
        flowInstanceOrder.setFormData(startInstanceDTO.getFormData().toJSONString());
        flowInstanceOrder.setBusinessKey(startInstanceDTO.getBusinessKey());
        flowInstanceOrder.setBusinessCategory(startInstanceDTO.getBusinessCategory());
        if(startInstanceDTO.getCallback()!=null){
            flowInstanceOrder.setModuleName(startInstanceDTO.getCallback().getModuleName());
            flowInstanceOrder.setModuleVersion(startInstanceDTO.getCallback().getModuleVersion());
        }
        flowInstanceOrder.setStatus(FlowInstanceStatus.SUBMITTED);
        flowInstanceOrder.setStartUserName(UserContext.getUserName());
        flowInstanceOrder.setStartUserAccount(UserContext.getPhoneNumber());
        flowInstanceOrder.setStartDate(LocalDateTime.now());
        flowInstanceOrder.setCreateBy(UserContext.getAccount());
        flowInstanceOrder.setUpdateBy(UserContext.getAccount());
        flowInstanceOrder.setCreateTime(LocalDateTime.now());
        flowInstanceOrder.setUpdateTime(LocalDateTime.now());
        flowInstanceOrder.setOid(UserContext.getTenantId());
        return jpaCrud.insertAndFlush(flowInstanceOrder)>0;
    }

    /**
     * 删除指定数据。
     *
     * @param orderId 主键Id。
     * @return 成功返回true，否则false。
     */
    public boolean remove(String orderId){
        return jpaCrud.deleteById(FlowInstanceOrder.class,orderId)>0;
    }

    /**
     * 根据实例Id把流程标记为已删除
     *
     * @param processInstanceId 流程实例Id。
     * @return 成功返回true，否则false。
     */
    @Transactional(rollbackFor = Exception.class)
    public void removeByProcessInstanceId(String processInstanceId) {
        updateFlowStatusByProcessInstanceId(processInstanceId,FlowInstanceStatus.DELETE);
    }

    /**
     * 查询被删除的流程单
     * @param query
     * @return
     */
    public Page<FlowInstanceOrder> getFlowInstanceOrder(FlowTaskQuery query){
        Specification<FlowInstanceOrder> specification = (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            if(query.getStatus()!=null){
                predicates.add(criteriaBuilder.equal(root.get("status"), query.getStatus()));
            }
            if(StringUtils.isNotBlank(query.getProcessDefinitionName())){
                predicates.add(criteriaBuilder.like(root.get("processDefinitionName"), "%" + query.getProcessDefinitionName() + "%"));
            }
            if(StringUtils.isNotBlank(query.getProcessDefinitionKey())){
                predicates.add(criteriaBuilder.like(root.get("processDefinitionKey"), "%" +query.getProcessDefinitionKey()+ "%"));
            }
            if(StringUtils.isNotBlank(query.getUserAccount())){
                predicates.add(criteriaBuilder.equal(root.get("startUserName"), query.getUserAccount()));
            }
            return criteriaBuilder.and(predicates.toArray(new Predicate[predicates.size()]));
        };
        Pageable pageable = PageRequest.of((query.getPage()-1), query.getPerPage(), Sort.by(Sort.Direction.DESC,"createTime").descending());
        return flowInstanceOrderRepository.findAll(specification,pageable);
    }

    public FlowInstanceOrder getOrderByProcessInstanceId(String processInstanceId){
        FlowInstanceOrder order = new FlowInstanceOrder();
        order.setProcessInstanceId(processInstanceId);
        return jpaCrud.selectOne(order);
    }

    public FlowInstanceOrder getOrderByBusinessKey(String businessKey,String businessCategory){
        FlowInstanceOrder order = new FlowInstanceOrder();
        order.setBusinessKey(businessKey);
        order.setBusinessCategory(businessCategory);
        return jpaCrud.selectOne(order);
    }



    public List<FlowInstanceOrder> getOrderByProcessInstanceIds(List<String> processInstanceIds){
        return flowInstanceOrderRepository.findByProcessInstanceIdIn(processInstanceIds);
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean updateFlowStatusByProcessInstanceId(String processInstanceId, int flowStatus){
        FlowInstanceOrder instanceOrder = getOrderByProcessInstanceId(processInstanceId);
        if(instanceOrder==null){
            throw new FabosJsonApiErrorTip("该流程实例单id不存在");
        }
        if (FlowInstanceStatus.FINISHED == flowStatus) {
            instanceOrder.setEndDate(LocalDateTime.now());
        }
        instanceOrder.setUpdateTime(LocalDateTime.now());
        instanceOrder.setUpdateBy(UserContext.getPhoneNumber());
        instanceOrder.setStatus(flowStatus);
        return jpaCrud.update(instanceOrder)>0;
    }

    public boolean deleteByProcessInstanceId(String processInstanceId){
        FlowInstanceOrder instanceOrder = getOrderByProcessInstanceId(processInstanceId);
        if(instanceOrder==null){
            throw new FabosJsonApiErrorTip("该流程实例单id不存在");
        }
        FlowInstanceOrderHistory flowInstanceOrderHistory = new FlowInstanceOrderHistory();
        BeanUtils.copyProperties(instanceOrder, flowInstanceOrderHistory);
        flowInstanceOrderHistory.setId(null);
        flowInstanceOrderHistory.setCreateBy(UserContext.getPhoneNumber());
        flowInstanceOrderHistory.setCreateTime(LocalDateTime.now());
        flowInstanceOrderHistory.setUpdateBy(UserContext.getPhoneNumber());
        flowInstanceOrderHistory.setUpdateTime(LocalDateTime.now());
        jpaCrud.insert(flowInstanceOrderHistory);
        return jpaCrud.delete(instanceOrder)>0;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean updateFlowStatusByProcessInstanceId(String processInstanceId, int flowStatus,JSONObject formData){
        FlowInstanceOrder instanceOrder = getOrderByProcessInstanceId(processInstanceId);
        if(instanceOrder==null){
            throw new FabosJsonApiErrorTip("该流程实例单id不存在");
        }
        if (FlowInstanceStatus.FINISHED != flowStatus) {
            instanceOrder.setUpdateTime(LocalDateTime.now());
            instanceOrder.setUpdateBy(UserContext.getAccount());
        }else {
            instanceOrder.setEndDate(LocalDateTime.now());
        }
        if(formData!=null&&!formData.isEmpty()){
//            instanceOrder.setFormData(formData.toString());
            // 修复：表单数据更新时，原有数据丢失的问题
            JSONObject originalFormData = JSONObject.parseObject(instanceOrder.getFormData());
            if (originalFormData == null) {
                instanceOrder.setFormData(formData.toString());
            } else {
                originalFormData.putAll(formData);
                instanceOrder.setFormData(originalFormData.toString());
            }
        }
        instanceOrder.setStatus(flowStatus);
        return jpaCrud.update(instanceOrder)>0;
    }

    public Boolean updateFlowStatus(FlowInstanceOrder instanceOrder, int flowStatus){
        if(instanceOrder==null){
            throw new FabosJsonApiErrorTip("该流程实例单id不存在");
        }
        if (FlowInstanceStatus.FINISHED == flowStatus) {
            instanceOrder.setEndDate(LocalDateTime.now());
        }
        instanceOrder.setUpdateTime(LocalDateTime.now());
        instanceOrder.setUpdateBy(UserContext.getAccount());
        instanceOrder.setStatus(flowStatus);
        return jpaCrud.update(instanceOrder)>0;
    }
}
