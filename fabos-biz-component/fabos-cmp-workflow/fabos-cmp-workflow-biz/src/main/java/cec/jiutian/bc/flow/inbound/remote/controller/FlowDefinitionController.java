package cec.jiutian.bc.flow.inbound.remote.controller;

import cec.jiutian.bc.flow.inbound.local.service.command.FlowDefinitionCommandService;
import cec.jiutian.bc.flow.inbound.local.service.query.FlowDefinitionQuery;
import cec.jiutian.bc.flow.inbound.message.FlowDefinitionDTO;
import cec.jiutian.bc.flow.inbound.message.IdOperateDTO;
import cec.jiutian.bc.flow.outbound.message.TaskInfoVO;
import cec.jiutian.bc.infra.enums.FlowConstant;
import cec.jiutian.common.util.BeanUtils;
import cec.jiutian.common.util.StringUtils;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.bc.flow.domain.model.entity.FlowDefinition;
import cec.jiutian.core.view.fabosJson.view.FabosJsonApiModel;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/fabos-cmp-workflow")
public class FlowDefinitionController {

    private final FlowDefinitionCommandService flowDefinitionCommandService;

    public FlowDefinitionController(FlowDefinitionCommandService flowDefinitionCommandService) {
        this.flowDefinitionCommandService = flowDefinitionCommandService;
    }

    /**
     * 获取主流程列表
     * @param query
     * @return
     */
    @PostMapping("/getFlowTemplateList")
    public FabosJsonApiModel getMainFlowDefinitionList(@RequestBody FlowDefinitionQuery query){
        Object object =  flowDefinitionCommandService.getMainFlowDefinitionList(query);
        return FabosJsonApiModel.successApi(object);
    }

    /**
     * 获取主流程
     * @param query
     * @return
     */
    @PostMapping("/getFlowDefinitionById")
    public FabosJsonApiModel getFlowDefinitionById(@RequestBody FlowDefinitionQuery query){
        if(StringUtils.isBlank(query.getId())){
            FabosJsonApiModel.errorApi("id不能为空!");
        }
        Object object = flowDefinitionCommandService.getFlowDefinitionById(query);
        return FabosJsonApiModel.successApi(object);
    }


    /**
     * 根据流程定义id获取流程定义信息
     * @param processDefinitionId
     * @return
     */
    @PostMapping("/getFlowByProcessDefinitionId")
    public FabosJsonApiModel getFlowDefinitionByDefinitionId(@RequestParam String processDefinitionId){
        Object object = flowDefinitionCommandService.getFlowByProcessDefinitionId(processDefinitionId);
        return FabosJsonApiModel.successApi(object);
    }

    /**
     * 查询待审批的流程列表
     * @param query
     * @return
     */
    @PostMapping("/getWaitApproveTemplateList")
    public FabosJsonApiModel getWaitApproveTemplateList(@RequestBody FlowDefinitionQuery query){
        Object object =  flowDefinitionCommandService.getWaitApproveTemplateList(query);
        return FabosJsonApiModel.successApi(object);
    }

    /**
     * 通过编码获取流程模板列表
     * @param query
     * @return
     */
    @PostMapping("/getFlowModelListByDefinitionKey")
    public FabosJsonApiModel getFlowDefinitionListByDefinitionKey(@RequestBody FlowDefinitionQuery query){
        Object object = flowDefinitionCommandService.getFlowDefinitionListByKey(query);
        return FabosJsonApiModel.successApi(object);
    }

    /**
     * 创建模板
     * @param createDTO
     * @return
     */
    @PostMapping("/workflow/template/create")
    public FabosJsonApiModel addModel(@Valid @RequestBody FlowDefinitionDTO createDTO){
        Object object = flowDefinitionCommandService.addModel(createDTO);
        return FabosJsonApiModel.successApi(object);
    }

    /**
     * 修改模板
     * @param updateDTO
     * @return
     */
    @PostMapping("/workflow/template/update")
    public FabosJsonApiModel updateModel(@Valid @RequestBody FlowDefinitionDTO updateDTO){
        Object object = flowDefinitionCommandService.updateDefinition(updateDTO);
        return FabosJsonApiModel.successApi(object);
    }

    /**
     * 删除模板
     * @param idDTO
     * @return
     */
    @PostMapping("/workflow/template/delete")
    public FabosJsonApiModel deleteModel(@Valid @RequestBody IdOperateDTO idDTO){
        Object object = flowDefinitionCommandService.deleteDefinition(idDTO);
        return FabosJsonApiModel.successApi(object);
    }


    /**
     * 失效模板
     * @param idDTO
     * @return
     */
    @PostMapping("/workflow/template/disable")
    public FabosJsonApiModel disable(@Valid @RequestBody IdOperateDTO idDTO){
        Object object =  flowDefinitionCommandService.disable(idDTO);
        return FabosJsonApiModel.successApi(object);
    }


    /**
     * 生效模板
     * @param idDTO
     * @return
     */
    @PostMapping("/workflow/template/effective")
    public FabosJsonApiModel effective(@Valid @RequestBody IdOperateDTO idDTO){
        Object object =  flowDefinitionCommandService.effective(idDTO);
        return FabosJsonApiModel.successApi(object);
    }

    /**
     * 审批定义
     * @param idDTO
     * @return
     */
    @PostMapping("/workflow/template/approve")
    public FabosJsonApiModel approve(@Valid @RequestBody IdOperateDTO idDTO){
        Object object = flowDefinitionCommandService.approve(idDTO);
        return FabosJsonApiModel.successApi(object);
    }

    /**
     * 拒绝提交的审批流定义
     * @param idDTO
     * @return
     */
    @PostMapping("/workflow/template/reject")
    public FabosJsonApiModel reject(@Valid @RequestBody IdOperateDTO idDTO){
        Object object = flowDefinitionCommandService.reject(idDTO);
        return FabosJsonApiModel.successApi(object);
    }


    /**
     * 提交审批
     * @param idDTO
     * @return
     */
    @PostMapping("/workflow/template/release")
    public FabosJsonApiModel publish(@Valid @RequestBody IdOperateDTO idDTO){
        Object object = flowDefinitionCommandService.release(idDTO);
        return FabosJsonApiModel.successApi(object);
    }

    /**
     * 切换模板
     * @param idDTO
     * @return
     */
    @PostMapping("/workflow/template/switchVersion")
    public FabosJsonApiModel switchVersion(@Valid @RequestBody IdOperateDTO idDTO){
        Object object = flowDefinitionCommandService.switchVersion(idDTO);
        return FabosJsonApiModel.successApi(object);
    }

    /**
     * 根据流程定义ID获取流程图
     * @param processDefinitionId
     */
    @PostMapping("/getProcessBpmnByDefinitionId")
    public FabosJsonApiModel getProcessBpmnByDefinitionId(@RequestParam String processDefinitionId){
        Object object = flowDefinitionCommandService.getProcessBpmnByDefinitionId(processDefinitionId);
        return FabosJsonApiModel.successApi(object);
    }

    /**
     * 通过流程唯一标识对应的表单
     * @param processDefinitionKey
     * @return
     */
    @PostMapping("/getFlowForm")
    public FabosJsonApiModel getFlowForm(@RequestParam String processDefinitionKey){
        FlowDefinition flowDefinition = flowDefinitionCommandService.verifyAndGetFlowDefinition(processDefinitionKey);
        String initTaskInfo = flowDefinition.getInitTaskInfo();
        TaskInfoVO taskInfoVO = StrUtil.isBlank(initTaskInfo) ? null : JSON.parseObject(initTaskInfo, TaskInfoVO.class);
        if (taskInfoVO != null) {
            String loginName = UserContext.getPhoneNumber();
            taskInfoVO.setAssignedMe(StrUtil.equalsAny(taskInfoVO.getAssignee(), loginName, FlowConstant.START_USER_ACCOUNT_VAR));
        } else {
            taskInfoVO=new TaskInfoVO();
        }
        BeanUtils.copyNotEmptyProperties(flowDefinition,taskInfoVO);
        BeanUtils.copyNotEmptyProperties(flowDefinition.getFlowForm(),taskInfoVO);
        return FabosJsonApiModel.successApi(taskInfoVO);
    }

    /**
     * 通过流程唯一标识获取最新的流程定义
     * @param processDefinitionKey
     * @return
     */
    @PostMapping("/getNewFlowDefinitionByKey")
    public Object getNewFlowDefinitionByKey(@RequestParam String processDefinitionKey){
        Object object = flowDefinitionCommandService.getNewFlowDefinitionByKey(processDefinitionKey);
        return FabosJsonApiModel.successApi(object);
    }

}
