package cec.jiutian.bc.flow.inbound.message;


import cec.jiutian.view.config.Comment;
import lombok.Data;
import java.io.Serializable;

@Data
public class FlowFormDTO implements Serializable {

    /**
     * 表单名字。
     */
    @Comment("表单名字")
    private String formName;

    /**
     * 表单类型。
     */
    @Comment("表单类型")
    private String category;

    /**
     * 表单JSON。
     */
    @Comment("表单JSON")
    private String formJson;
}
