package cec.jiutian.bc.ecs.domain.messagerecord.entity;

import cec.jiutian.bc.ecs.dto.MessageRecordCreateDTO;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.TransferModel;
import cec.jiutian.view.FabosJsonI18n;
import cec.jiutian.view.type.Power;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description:
 */
@Entity
@Table(name = "ecs_message_record")
@Getter
@Setter
@FabosJson(name = "告警推送记录",
        orderBy = "MessageRecord.createTime desc",
        power = @Power(add = false, edit = false, delete = false, export = false, importable = false, viewDetails = true))
@FabosJsonI18n
@TransferModel(baseModel = "MessageRecordSuper")
public class MessageRecord extends MessageRecordSuper {


    public void initMessageRecord(MessageRecordCreateDTO createDTO) {
        this.setTitle(createDTO.getTitle());
        this.setContent(createDTO.getContent());
        this.setDispatchUser(createDTO.getDispatchUser());
        this.setDispatchWay(createDTO.getDispatchWay());
        this.setCreateTime(LocalDateTime.now());
    }
//
//    public void initByMessage(Message message) {
//        this.setCreateBy(message.getCreateBy());
//        this.setOid(message.getOid());
//        this.setTitle(message.getTitle());
//        this.setMessageId(message.getId());
//        this.setOriginalSystem(null);
//        this.setMessageGroupId(message.getMessageGroup().getId());
//
//    }
}
