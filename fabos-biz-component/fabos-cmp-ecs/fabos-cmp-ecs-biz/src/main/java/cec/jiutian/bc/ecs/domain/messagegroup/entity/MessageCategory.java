package cec.jiutian.bc.ecs.domain.messagegroup.entity;

import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.Search;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "ecs_message_category",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"code"})
        }
)
@FabosJson(
        name = "告警分类",
        orderBy = "MessageCategory.createTime desc"
)
@Getter
@Setter
public class MessageCategory extends MetaModel {

    @FabosJsonField(
            views = @View(title = "分类代码"),
            edit = @Edit(title = "分类代码", search = @Search(vague = true), readonly = @Readonly(add = false, edit = true), notNull = true)
    )
    private String code;

    @FabosJsonField(
            views = @View(title = "分类名称"),
            edit = @Edit(title = "分类名称", search = @Search(vague = true), notNull = true, readonly = @Readonly(add = false, edit = true))
    )
    private String name;

    @FabosJsonField(
            edit = @Edit(title = "备注", notNull = false,
                    type = EditType.TEXTAREA, inputType = @InputType(length = 400), formColumns = 3)
    )
    private String desciption;


}
