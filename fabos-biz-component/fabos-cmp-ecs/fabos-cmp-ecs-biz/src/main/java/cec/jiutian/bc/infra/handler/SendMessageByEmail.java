package cec.jiutian.bc.infra.handler;

import cec.jiutian.bc.ecs.domain.messagerecord.entity.MessageRecord;
import cec.jiutian.bc.ecs.inbound.local.enums.DispatchWayEnum;
import cec.jiutian.bc.infra.util.EmailTemplate;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.core.frame.module.MetaUser;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：2023/5/22 17:45
 * @description：
 */
@Slf4j
@Component(DispatchWayEnum.EMAIL_CODE)
public class SendMessageByEmail implements MessageHandler {
    private static String aesKey;

    private static EmailTemplate emailTemplate;

    @Value("${aes.key}")
    public void setAesKey(String aesKey) {
        SendMessageByEmail.aesKey = aesKey;
    }

    @Resource
    public void setEmailTemplate(EmailTemplate emailTemplate) {
        SendMessageByEmail.emailTemplate = emailTemplate;
    }

    @Override
    public void sendMessage(MessageRecord createDTO) {
        MetaUser user = createDTO.getDispatchMetaUser();
        // 根据接收消息用户查询用户列表
            String html = createDTO.getContent();
            if (user.getEmailAddress() == null) {
                throw new ServiceException("推送人员" + user.getAccount() + "的邮箱账号不存在");
            }
            emailTemplate.sendHtmlMail(user.getEmailAddress(), "", html);

    }

    /**
     * 批量发送邮件
     * @param emails
     * @param title
     * @param content
     */
    public void sendMailBatch(List<String> emails, String title, String content) {
        if (CollectionUtils.isEmpty(emails)) {
            return;
        }
        for (String email : emails) {
            //邮箱格式校验
            if (!isEmail(email)) {
                log.error("邮箱格式错误：{}",email);
                continue;
            }
            emailTemplate.sendHtmlMail(email, title, content);
        }
    }

    private static final String emailRegex = "^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\\.[a-zA-Z0-9_-]+)+$";
    private boolean isEmail(String email) {
        if (email == null || email.isEmpty()) {
            return false;
        }
        return email.matches(emailRegex);
    }



}
