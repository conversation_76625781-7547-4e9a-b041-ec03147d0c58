package cec.jiutian.bc.infra.port.client;

import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.service.FabosJsonSessionService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class WeixinClient {


    @Value("${ecs.weixin.sCorpID}")
    private String sCorpID;

    @Value("${ecs.weixin.corpsecret}")
    private String corpsecret;

    @Value("${ecs.weixin.getTokenUrl}")
    private String getTokenUrl;

    @Value("${ecs.weixin.agentid}")
    private String agentID;

    @Value("${ecs.weixin.sendMessageUrl}")
    private String sendMessageUrl;

    @Value("${ecs.weixin.createGroupUrl:https://qyapi.weixin.qq.com/cgi-bin/appchat/create}")
    private String createGroupUrl;

    @Value("${ecs.weixin.updateGroupUrl:https://qyapi.weixin.qq.com/cgi-bin/appchat/update}")
    private String updateGroupUrl;

    @Value("${ecs.weixin.sendGroupMessageUrl:https://qyapi.weixin.qq.com/cgi-bin/appchat/send}")
    private String sendGroupMessageUrl;

    private String weixinAccessTokenKey = "weixinAccessToken";

    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final CloseableHttpClient httpClient = HttpClients.createDefault();

    private final FabosJsonSessionService sessionService;

    public WeixinClient(FabosJsonSessionService sessionService) {
        this.sessionService = sessionService;
    }

    /**
     * 获取企业微信access_token
     */
    public String weixinGetAccessToken() {

        String cachedToken = sessionService.getAsString(weixinAccessTokenKey);
        if (cachedToken != null) {
            return cachedToken;
        }

        String url = getTokenUrl + "?corpid=" + sCorpID + "&corpsecret=" + corpsecret;
        HttpGet httpGet = new HttpGet(url);

        try (CloseableHttpResponse response = httpClient.execute(httpGet)) {
            if (response.getStatusLine().getStatusCode() == 200) {
                String responseBody = EntityUtils.toString(response.getEntity());
                JsonNode jsonNode = objectMapper.readTree(responseBody);
                cachedToken = jsonNode.get("access_token").asText();
                int expiresIn = jsonNode.get("expires_in").asInt() - 300; // 提前5分钟过期
                sessionService.put(weixinAccessTokenKey, cachedToken, expiresIn, TimeUnit.SECONDS);
                return cachedToken;
            } else {
                throw new ServiceException("微信接口返回错误：" + response.getStatusLine());
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 向微信个人发送文本消息
     *
     * @param toUsers 接收者列表
     * @param message 消息内容
     * @return 错误信息，如果没有错误则返回null
     */
    public void weixinSendTextMessage(List<String> toUsers, String message) throws Exception {
        Map<String, Object> textContent = new HashMap<>();
        textContent.put("content", message);

        Map<String, Object> body = new HashMap<>();
        body.put("touser", String.join("|", toUsers));
        body.put("toparty", "");
        body.put("totag", "");
        body.put("msgtype", "text");
        body.put("agentid", agentID);
        body.put("text", textContent);
        body.put("safe", 0);
        body.put("enable_id_trans", 0);
        body.put("enable_duplicate_check", 0);
        body.put("duplicate_check_interval", 1800);

        String url = sendMessageUrl + "?access_token=" + weixinGetAccessToken();
        HttpPost httpPost = new HttpPost(url);
        httpPost.addHeader("Content-Type", "application/json");
        httpPost.setEntity(new StringEntity(new ObjectMapper().writeValueAsString(body), "UTF-8"));

        try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
            if (response.getStatusLine().getStatusCode() == 200) {
                String responseBody = EntityUtils.toString(response.getEntity());
                JsonNode jsonNode = objectMapper.readTree(responseBody);
                int errcode = jsonNode.get("errcode").asInt();
                String errmsg = jsonNode.get("errmsg").asText();
                log.info("发送微信 - errcode: {}, errmsg: {}", errcode, errmsg);
                if (errcode != 0) {
                    throw new FabosJsonApiErrorTip("微信接口返回错误：" + errmsg);
                }
            } else {
                throw new ServiceException("微信接口返回错误：" + response.getStatusLine());
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }

    /**
     * 向微信群组发送文本消息
     *
     * @param toUsers 接收者列表
     * @param message 消息内容
     * @return 错误信息，如果没有错误则返回null
     */
    public void weixinSendGroupTextMessage(String chatId, String message) throws Exception {
        Map<String, Object> textContent = new HashMap<>();
        textContent.put("content", message);

        Map<String, Object> body = new HashMap<>();
        body.put("chatid", chatId);
        body.put("msgtype", "text");
        body.put("text", textContent);
        body.put("safe", 0);

        String url = sendGroupMessageUrl + "?access_token=" + weixinGetAccessToken();
        HttpPost httpPost = new HttpPost(url);
        httpPost.addHeader("Content-Type", "application/json");
        httpPost.setEntity(new StringEntity(new ObjectMapper().writeValueAsString(body), "UTF-8"));

        try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
            if (response.getStatusLine().getStatusCode() == 200) {
                String responseBody = EntityUtils.toString(response.getEntity());
                JsonNode jsonNode = objectMapper.readTree(responseBody);
                int errcode = jsonNode.get("errcode").asInt();
                String errmsg = jsonNode.get("errmsg").asText();
                log.info("发送微信 - errcode: {}, errmsg: {}", errcode, errmsg);
                if (errcode != 0) {
                    throw new FabosJsonApiErrorTip("微信接口返回错误：" + errmsg);
                }
            } else {
                throw new ServiceException("微信接口返回错误：" + response.getStatusLine());
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }

    /**
     * 创建微信群组
     * 返回创建的微信群组ID
     */
    public String weixinCreateGroup(String groupName, String managerId, String memberIds) throws JsonProcessingException {

        Map<String, Object> body = new HashMap<>();
        body.put("name", groupName);
        body.put("owner", managerId);
        if (memberIds != null) {
            body.put("userlist", memberIds.split(","));
        }

        String url = createGroupUrl + "?access_token=" + weixinGetAccessToken();
        HttpPost httpPost = new HttpPost(url);
        httpPost.addHeader("Content-Type", "application/json");
        httpPost.setEntity(new StringEntity(new ObjectMapper().writeValueAsString(body), "UTF-8"));

        try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
            if (response.getStatusLine().getStatusCode() == 200) {
                String responseBody = EntityUtils.toString(response.getEntity());
                JsonNode jsonNode = objectMapper.readTree(responseBody);
                int errcode = jsonNode.get("errcode").asInt();
                String errmsg = jsonNode.get("errmsg").asText();
                log.info("创建微信群组 - errcode: {}, errmsg: {}", errcode, errmsg);
                if (errcode != 0) {
                    throw new ServiceException("微信接口返回错误：" + errmsg);
                } else {
                    String chatId = jsonNode.get("chatid").asText();
                    return chatId;
                }
            } else {
                throw new ServiceException("微信接口返回错误：" + response.getStatusLine());
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 修改微信群组
     */
    public void weixinUpdateGroup(String chatId, String groupName, String manager, List<String> add_memberIds, List<String> del_memberIds) throws JsonProcessingException {

        Map<String, Object> body = new HashMap<>();
        body.put("chatid", chatId);
        body.put("name", groupName);
        body.put("owner", manager);
        body.put("add_user_list", add_memberIds);
        body.put("del_user_list", del_memberIds);

        String url = updateGroupUrl + "?access_token=" + weixinGetAccessToken();
        HttpPost httpPost = new HttpPost(url);
        httpPost.addHeader("Content-Type", "application/json");
        httpPost.setEntity(new StringEntity(new ObjectMapper().writeValueAsString(body), "UTF-8"));

        try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
            if (response.getStatusLine().getStatusCode() == 200) {
                String responseBody = EntityUtils.toString(response.getEntity());
                JsonNode jsonNode = objectMapper.readTree(responseBody);
                int errcode = jsonNode.get("errcode").asInt();
                String errmsg = jsonNode.get("errmsg").asText();
                log.info("修改微信群组 - errcode: {}, errmsg: {}", errcode, errmsg);
                if (errcode != 0) {
                    throw new ServiceException("微信接口返回错误：" + errmsg);
                }
            } else {
                throw new ServiceException("微信接口返回错误：" + response.getStatusLine());
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
