package cec.jiutian.bc.ecs.domain.messagerecord.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * DTO for {@link MessageRecord}
 */
public record MessageRecordDto(String id, LocalDateTime createTime, LocalDateTime updateTime, String title,
                               String dispatchWay, Boolean immediateAlarmFlag, LocalDateTime dispatchTime,
                               String content, String status, String isProcess, String processCompleteFlag,
                               String processDetail, LocalDateTime processTime) implements Serializable {
}