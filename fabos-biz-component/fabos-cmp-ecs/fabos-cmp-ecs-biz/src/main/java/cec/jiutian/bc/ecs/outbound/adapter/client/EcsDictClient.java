package cec.jiutian.bc.ecs.outbound.adapter.client;

import cec.jiutian.bc.urm.dto.MetaDictItem;
import cec.jiutian.bc.urm.provider.IDictProvider;
import cec.jiutian.core.data.factory.SpringBeanUtils;
import com.alipay.sofa.koupleless.common.api.SpringServiceFinder;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class EcsDictClient {

    private Object lock = new Object();
    private static volatile IDictProvider iDictProvider;

    public List<MetaDictItem> getDictItems(String code) {
        return acquireUserProvider().getDictItems(code);
    }

    private IDictProvider acquireUserProvider(){
        if (iDictProvider != null) {
            return iDictProvider;
        }
        synchronized(lock){
            if (iDictProvider!=null){
                return iDictProvider;
            }
            iDictProvider = SpringBeanUtils.getBean(IDictProvider.class);
            if (iDictProvider == null) {
                iDictProvider = SpringServiceFinder.getModuleService("fabos-cmp-urm", "3.2.2-SNAPSHOT", IDictProvider.class);
            }
        }
        return iDictProvider;
    }
}
