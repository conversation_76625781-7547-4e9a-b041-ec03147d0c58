package cec.jiutian.bc.infra.business.definition;

import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2022/3/3
 */
@RestController
public class BusinessDefinition {
    public static class MessageGroupOperation {
        public static final String MESSAGE_GROUP_CREATE = "MESSAGE_GROUP_CREATE";
        public static final String MESSAGE_GROUP_UPDATE = "MESSAGE_GROUP_UPDATE";
        public static final String MESSAGE_GROUP_DELETE = "MESSAGE_GROUP_DELETE";
    }

    public static class TemplateOperation {
        public static final String TEMPLATE_CREATE = "TEMPLATE_CREATE";
        public static final String TEMPLATE_UPDATE = "TEMPLATE_UPDATE";
        public static final String TEMPLATE_DELETE = "TEMPLATE_DELETE";
    }

    public static class TemplateAttributeOperation {
        public static final String TEMPLATE_ATTRIBUTE_CREATE = "TEMPLATE_ATTRIBUTE_CREATE";
        public static final String TEMPLATE_ATTRIBUTE_UPDATE = "TEMPLATE_ATTRIBUTE_UPDATE";
        public static final String TEMPLATE_ATTRIBUTE_DELETE = "TEMPLATE_ATTRIBUTE_DELETE";
    }

    public static class MessageStatus {
        public static final String DISPATCHED = "已派送";
        public static final String RECEIVED = "已接收";
        public static final String TIMEOUT_CLOSE = "超时关闭";
        public static final String PROCESSED = "已处理";
    }

    public static class MessageOpType {
        public static final String RECEIVE = "receive";
        public static final String HANDLE = "handle";
    }
}
