package cec.jiutian.bc.ecs.service;

import cec.jiutian.bc.ecs.domain.message.service.MessageService;
import cec.jiutian.bc.ecs.domain.messagerecord.service.MessageRecordService;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component
public class MessageProcessService {

    private final MessageService messageService;
    private final MessageRecordService messageRecordService;

    public MessageProcessService(MessageService messageService, MessageRecordService messageRecordService) {
        this.messageService = messageService;
        this.messageRecordService = messageRecordService;
    }

    @Transactional
    public void messageProcessComplete(String messageId, String processDetail) {
        // 检查消息是否允许关闭,并关闭消息
        messageService.closeMessage(messageId, processDetail);
        // 关闭消息下的所有发送记录
        messageRecordService.closeMessageRecord(messageId, processDetail);
    }
}
