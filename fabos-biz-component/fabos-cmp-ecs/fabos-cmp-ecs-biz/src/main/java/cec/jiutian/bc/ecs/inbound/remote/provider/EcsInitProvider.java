package cec.jiutian.bc.ecs.inbound.remote.provider;

import cec.jiutian.component.utils.ReadInitDictDataFromCSVUtil;
import cec.jiutian.component.utils.ReadInitMenuDataFromCSVUtil;
import cec.jiutian.bc.urm.dto.InitDataDTO;
import cec.jiutian.bc.urm.dto.MetaDict;
import cec.jiutian.bc.urm.dto.MetaMenu;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component("fabos-cmp-ecs")
public class EcsInitProvider implements InitDataDTO.IInitDataProvider {
    @Override
    public List<MetaMenu> initData(InitDataDTO dto) {
        List<MetaMenu> menus = new ArrayList<>();
        Optional.ofNullable(ReadInitMenuDataFromCSVUtil.convertCsvToList(dto.getComponentName())).ifPresent(e -> menus.addAll(e));
        return menus;
    }

    @Override
    public List<MetaDict> initDict(InitDataDTO dto) {
        List<MetaDict> metaDicts = new ArrayList<>();
        Optional.ofNullable(ReadInitDictDataFromCSVUtil.convertCsvToList(dto)).ifPresent(e -> metaDicts.addAll(e));
        return metaDicts;
    }
}
