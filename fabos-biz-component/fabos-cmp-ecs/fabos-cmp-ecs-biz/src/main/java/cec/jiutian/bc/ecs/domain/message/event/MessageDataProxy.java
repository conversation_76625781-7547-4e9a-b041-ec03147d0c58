package cec.jiutian.bc.ecs.domain.message.event;

import cec.jiutian.bc.ecs.domain.message.entity.Message;
import cec.jiutian.bc.ecs.domain.message.entity.MessageProcessFormDetail;
import cec.jiutian.bc.ecs.domain.message.service.MessageService;
import cec.jiutian.bc.ecs.domain.messagegroup.entity.MessageGroup;
import cec.jiutian.core.frame.constant.YesOrNoStatus;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.core.view.fabosJson.FabosJson.BackgroundColor;
import cec.jiutian.view.fun.DataProxy;
import com.alibaba.fastjson2.JSONObject;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description:
 */
@Component
public class MessageDataProxy implements DataProxy<Message> {
    private final MessageService messageService;

    private final FabosJsonDao fabosJsonDao;

    public MessageDataProxy(MessageService messageService, FabosJsonDao fabosJsonDao) {
        this.messageService = messageService;
        this.fabosJsonDao = fabosJsonDao;
    }

    @Override
    public void beforeAdd(Message message) {
        if (message.getCurrentUpNumber() == null) {
            message.setCurrentUpNumber(0);
        }
        message.setCurrentUpNumber(1);
        message.setManualCreateFlag(true);
        message.setReceivedTime(LocalDateTime.now());
        MessageGroup messageGroup = fabosJsonDao.findById(MessageGroup.class, message.getMessageGroup().getId());
        try {
            // 生成表单处理详情
            List<MessageProcessFormDetail> messageProcessFormDetails = messageGroup.getMessageProcessFormConfigs()
                    .stream()
                    .map(config -> {
                        MessageProcessFormDetail detail = new MessageProcessFormDetail();
//                        detail.setMessageId(message.getId());
                        detail.setFormId(config.getId());
                        detail.setFormName(config.getName());
                        if (message.getFormUrlParameters() != null) {
                            String urlParameters = JSONObject.parseObject(message.getFormUrlParameters())
                                    .entrySet()
                                    .stream()
                                    .map(entry -> entry.getKey() + "=" + entry.getValue())
                                    .collect(Collectors.joining("&"));
                            detail.setFormUrl(config.getFormUrl() + "?" + urlParameters);
                        }
                        detail.setProcessCompletedFlag(false);
                        return detail;
                    })
                    .collect(Collectors.toList());
            message.setMessageProcessFormConfigs(messageProcessFormDetails);
        } catch (Exception e) {
            throw new FabosJsonApiErrorTip("表单参数处理异常:" + e);
        }
        // 判断是否需要升级，是则记录下次升级时间
        if (message.getIsProcess() != null && message.getUpNumber() != null && message.getIsProcess().equals("Y") && message.getUpNumber() > 1) {
            message.setNextUpgradeTime(LocalDateTime.now().plusMinutes(message.getMessageGroup().getSecondIntervalTime()));
        }
        if (YesOrNoStatus.YES.getValue().equals(message.getIsProcess())) {
            message.setProcessCompleteFlag(YesOrNoStatus.NO.getValue());
        }

    }

    @Override
    public void afterAdd(Message message) {
        // 新增消息后，发送消息
        messageService.sendMessage(message);
    }

    @Override
    public void afterFetch(Collection<Map<String, Object>> list) {
        DataProxy.super.afterFetch(list);
        // 设置消息的颜色标志, 规则为，消息需要处理，则展示颜色，且颜色根据消息级别来设置
        list.forEach(map -> {
            String groupLevel = map.getOrDefault("groupLevel", "").toString();
            Object isProcess = map.getOrDefault("isProcess", "");
            Object processCompleteFlag = map.getOrDefault("processCompleteFlag", "N");
            if (isProcess != null && processCompleteFlag != null && isProcess.toString().equals(YesOrNoStatus.YES.getValue()) && processCompleteFlag.toString().equals(YesOrNoStatus.NO.getValue())) {
                if ("LEVEL1".equals(groupLevel)) {
                    map.put("colorFlag", BackgroundColor.BLUE.getValue());
                } else if ("LEVEL2".equals(groupLevel)) {
                    map.put("colorFlag", BackgroundColor.YELLOW.getValue());
                } else if ("LEVEL3".equals(groupLevel)) {
                    map.put("colorFlag", BackgroundColor.RED.getValue());
                }
            }

        });
    }
}
