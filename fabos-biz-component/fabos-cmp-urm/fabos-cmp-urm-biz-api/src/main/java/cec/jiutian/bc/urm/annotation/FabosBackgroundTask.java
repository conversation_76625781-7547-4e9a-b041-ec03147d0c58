package cec.jiutian.bc.urm.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;


@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD})
@Documented
public @interface FabosBackgroundTask {

    /**
     * 给定当前调用链的持续时间，默认为30分钟，若为非正数则无超时时间（慎用）
     *
     * @return 持续时间，秒
     */
    int timeout() default 30 * 60;

}
