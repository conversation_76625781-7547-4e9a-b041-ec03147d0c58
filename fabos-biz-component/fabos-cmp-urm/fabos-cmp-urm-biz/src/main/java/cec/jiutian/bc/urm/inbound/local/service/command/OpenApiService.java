package cec.jiutian.bc.urm.inbound.local.service.command;

import cec.jiutian.bc.urm.domain.openApi.entity.FabosJsonOpenApi;
import cec.jiutian.bc.urm.domain.openApi.entity.FabosJsonOpenApiResources;
import cec.jiutian.bc.urm.domain.user.service.UserService;
import cec.jiutian.common.constant.SessionKey;
import cec.jiutian.core.frame.constant.FabosJsonRestPath;
import cec.jiutian.core.frame.exception.FabosJsonWebApiRuntimeException;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.core.service.FabosJsonSessionService;
import cec.jiutian.core.view.fabosJson.util.DateUtil;
import com.alibaba.fastjson2.JSON;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @time 2025-01-20 14:07
 */

@Service
@Slf4j
@AllArgsConstructor
public class OpenApiService {
    private final UserService userService;
    private final FabosJsonDao fabosJsonDao;
    private final FabosJsonSessionService sessionService;

    public void kickOutToken(FabosJsonOpenApi FabosJsonOpenApi) {
        Optional.ofNullable(FabosJsonOpenApi.getCurrentToken()).ifPresent(it -> userService.logoutToken(FabosJsonOpenApi.getCurrentToken()));
    }

    public Map<String, String> requestNewToken(String appid, String secret) {
        FabosJsonOpenApi fabosJsonOpenApi = fabosJsonDao.lambdaQuery(FabosJsonOpenApi.class).eq(FabosJsonOpenApi::getAppid, appid).one();
        if (fabosJsonOpenApi == null || !secret.equals(fabosJsonOpenApi.getSecret())) {
            throw new FabosJsonWebApiRuntimeException("AppId或者Secret不正确，请检查");
        }
        String expire = this.requestAndSetToken(fabosJsonOpenApi);
        return fabosJsonOpenApi.transformToMap(expire);
    }

    public String requestAndSetToken(FabosJsonOpenApi fabosJsonOpenApi) {
        if (!fabosJsonOpenApi.getStatus()) {
            throw new FabosJsonWebApiRuntimeException("无法生成token，因为此应用[" + fabosJsonOpenApi.getName() + "]已被锁定");
        }
        String token = RandomStringUtils.randomAlphanumeric(24).toUpperCase();
        createToken(token, fabosJsonOpenApi.getAvailableResources(), fabosJsonOpenApi.getExpire() * 60 * 60);
        if (null != fabosJsonOpenApi.getCurrentToken()) {
            log.info("由于应用程序[{}]重新生成token，旧的token已被移除{}", fabosJsonOpenApi.getName(), fabosJsonOpenApi.getCurrentToken());
            userService.logoutToken(fabosJsonOpenApi.getCurrentToken());
        }
        String result = null;
        if (fabosJsonOpenApi.getExpire() > 0) {
            LocalDateTime expire = LocalDateTime.now().plusMinutes(fabosJsonOpenApi.getExpire());
            result = DateUtil.getFormatDate(DateUtil.transformToDate(expire), DateUtil.DATE_TIME);
        }
        fabosJsonOpenApi.setCurrentToken(token);
        return result;
    }

    public Integer getExpireTimeSeconds(String token) {
        return Math.toIntExact(sessionService.getExpire(SessionKey.OPEN_API_TOKENS + token));
    }

    public void createToken(String token, List<FabosJsonOpenApiResources> availableResources, Integer expire) {
        // 这里expire单位要是秒
        List<String> resources = availableResources.stream().map(fabosJsonOpenApiResources -> {
            String uri = fabosJsonOpenApiResources.getName();
            if (!uri.startsWith(FabosJsonRestPath.FABOS_OPEN_API)) {
                uri = FabosJsonRestPath.FABOS_OPEN_API + uri;
            }
            return uri;
        }).toList();
        userService.createOpenApiToken(token, JSON.toJSONString(resources), expire);
    }

    public String getFormattedOnlineTokenExpireTime(String token) {
        Integer ttl = getExpireTimeSeconds(token);
        if (ttl < 0) {
            return String.valueOf(ttl);
        } else {
            LocalDateTime expire = LocalDateTime.now().plusSeconds(ttl);
            return DateUtil.getFormatDate(DateUtil.transformToDate(expire), DateUtil.DATE_TIME);
        }
    }
}
