package cec.jiutian.bc.urm.domain.dictionary.handler;

import cec.jiutian.bc.urm.domain.dictionary.service.DictionaryService;
import cec.jiutian.bc.urm.dto.MetaDictItem;
import cec.jiutian.core.frame.cache.FabosJsonCache;
import cec.jiutian.core.frame.cache.FabosJsonCacheLRU;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.core.frame.util.FabosJsonAssert;
import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class DictChoiceFetchHandler implements ChoiceFetchHandler {
    @Resource
    private FabosJsonDao fabosJsonDao;
    @Resource
    private DictionaryService dictionaryService;

    private final FabosJsonCache<List<VLModel>> dictCache = new FabosJsonCacheLRU<>(500);

    public DictChoiceFetchHandler() {
    }

    /**
     * 从枚举表获取枚举，附带缓存
     *
     * @param params 首参数为需要查询的字典编码（code），第二参数为缓存时间，单位为毫秒，默认为30秒，最大长度为2，否则会截取前2位
     * @return 获取到的枚举，如果没有，则返回空List
     */
    public List<VLModel> fetch(String... params) {

        FabosJsonAssert.notNull(params, String.format("[%s]类的fetch方法接收到的参数中，首参数需为枚举类型，第二参数（可选）应为缓存过期时间", DictChoiceFetchHandler.class.getSimpleName()));
        String key = DictChoiceFetchHandler.class.getName() + ":" + params[0];
        long ttl = params.length >= 2 ? Long.parseLong(params[1]) : 30000L;

        List<MetaDictItem> dictCode = dictionaryService.getDictItems(params[0]);
        if (CollectionUtils.isEmpty(dictCode)) {
            return new ArrayList<>();
        }

        return this.dictCache.getAndSet(key, ttl, () -> dictCode.stream()
                .map((item) -> new VLModel(item.getCode(), item.getName()))
                .toList());
    }

    @Override
    public boolean isDataBaseQuery() {
        return true;
    }
}
