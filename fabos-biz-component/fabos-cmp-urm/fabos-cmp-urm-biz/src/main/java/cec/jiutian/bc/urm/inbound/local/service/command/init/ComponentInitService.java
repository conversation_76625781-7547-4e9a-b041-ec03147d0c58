package cec.jiutian.bc.urm.inbound.local.service.command.init;

import cec.jiutian.bc.urm.domain.menu.entity.Menu;
import cec.jiutian.bc.urm.domain.systemInit.entity.SystemInit;
import cec.jiutian.bc.urm.dto.InitDataDTO;
import cec.jiutian.bc.urm.dto.MetaDict;
import cec.jiutian.bc.urm.dto.MetaMenu;
import cec.jiutian.bc.urm.inbound.local.config.SystemConfig;
import cec.jiutian.bc.urm.inbound.local.service.command.InitDataCommandService;
import cec.jiutian.core.data.factory.SpringBeanUtils;
import cec.jiutian.core.frame.module.FabModule;
import cec.jiutian.data.jpa.JpaCrud;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 初始化菜单
 */
@Service
@Slf4j
public class ComponentInitService{

    @Resource
    private SystemConfig systemConfig;

    @Resource
    private InitDataCommandService initDataCommandService;
    @Resource
    private JpaCrud jpaCrud;


    /**
     * 整个组件初始化。会校验SystemInit是否存在组件初始化过的数据
     */
    public void initAllMenu(){
        log.info("开始初始化菜单");
        List<FabModule> fabModules = jpaCrud.select(new FabModule());
        if(CollectionUtils.isNotEmpty(fabModules)){
            List<String> componentsList = fabModules.stream().map(FabModule::getName).toList();
            componentsList.forEach(componentName -> {
                if(componentName!=null){
                    //组件初始化(菜单)
                    InitDataDTO dto = new InitDataDTO();
                    FabModule fabModule = new FabModule();
                    fabModule.setName(componentName);
                    fabModule.setVersion("3.2.2-SNAPSHOT");
                    dto.setComponentName(componentName);
                    dto.setOid("");//暂时无oid 不需要初始化oid入库
                    //初始化菜单
                    SystemInit systemInit = new SystemInit();
                    systemInit.setInitItem(componentName);
                    List<SystemInit> select = jpaCrud.select(systemInit);
                    if(CollectionUtils.isEmpty(select)){
                        //初始化菜单
                        menuInit(dto, fabModule);
                        systemInit.setInitTime(LocalDateTime.now());
                        jpaCrud.insert(systemInit);
                    }else {
                        log.info("组件【{}】菜单已初始化，本次跳过初始化流程",componentName);
                    }
                }

            });
        }
        //获取配置的组件初始化菜单
//        systemConfig.getComponentsList().forEach(componentName -> {
//            //组件初始化(菜单)
//            InitDataDTO dto = new InitDataDTO();
//            FabModule fabModule = new FabModule();
//            fabModule.setName(componentName);
//            fabModule.setVersion("3.2.2-SNAPSHOT");
//            dto.setComponentName(componentName);
//            dto.setOid("");//暂时无oid 不需要初始化oid入库
//            //初始化菜单
//            SystemInit systemInit = new SystemInit();
//            systemInit.setInitItem(componentName);
//            List<SystemInit> select = jpaCrud.select(systemInit);
//            if(CollectionUtils.isEmpty(select)){
//                //初始化菜单
//                menuInit(dto, fabModule);
//                systemInit.setInitTime(LocalDateTime.now());
//                jpaCrud.insert(systemInit);
//            }else {
//                log.info("组件【{}】菜单已初始化，本次跳过初始化流程",componentName);
//            }
//        });
    }

    /**
     * 手动触发菜单初始化逻辑。不会校验SystemInit是否存在组件初始化过的数据
     * @param componentName
     */
    public void initMenu(String componentName){
        InitDataDTO dto = new InitDataDTO();
        FabModule fabModule = new FabModule();
        fabModule.setName(componentName);
        fabModule.setVersion("3.2.2-SNAPSHOT");
        dto.setComponentName(componentName);
        dto.setOid("");//暂时无oid 不需要初始化oid入库
        menuInit(dto, fabModule);
    }

    /**
     * 通用的初始化菜单的逻辑  从之前多租户模式下的代码中抽离出来
     * @param dto
     * @param module
     * @return
     */
    public List<Menu> menuInit(InitDataDTO dto, FabModule module ) {
//        InitDataDTO.IInitDataProvider provider = SpringServiceFinder.getModuleService(module.getName(), module.getVersion(), InitDataDTO.IInitDataProvider.class);
        List<MetaMenu> menus = null;
        List<MetaDict> dicts = null;
//        try {
//            menus = provider.initData(dto);
//            dicts = provider.initDict(dto);
//        } catch (BizRuntimeException ex) {
//            //这里是sofa的逻辑 目前暂时没用
//        }

        try {
            InitDataDTO.IInitDataProvider provider = SpringBeanUtils.getBean(dto.getComponentName(), InitDataDTO.IInitDataProvider.class);
            menus = provider.initData(dto);
            dicts = provider.initDict(dto);
        }catch (Exception e){
            log.error("{}初始化菜单失败，无InitDataDTO.IInitDataProvider实现类",module.getName());
        }
        //特殊逻辑 超级管理员模式不需要初始化 三员日志 菜单  注释掉不需要了 现在没法初始化的时候没法判断是否是三员模式  需求文档也没有该需求
//        if(dto.getComponentName().equals("fabos-cmp-urm")&& RightManagementPolicyEnum.SUPER.getValue().equals(systemConfig.getSystemMode())){
//            menus.removeIf(menu -> "PermissionOperationLog".equals(menu.getValue()));
//        }
        initDataCommandService.saveMetaDictionary(dicts);
        List<Menu> updatedMenus = initDataCommandService.savaMetaMenus(menus);
        return updatedMenus;
    }
}
