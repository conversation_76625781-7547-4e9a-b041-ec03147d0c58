package cec.jiutian.bc.job.domain.job.service;

import cec.jiutian.core.view.fabosJson.util.FabosJsonSpringUtil;
import cec.jiutian.bc.job.domain.job.entity.JobInfo;
import cec.jiutian.bc.job.domain.log.entity.JobLog;
import cec.jiutian.bc.job.outbound.port.publisher.JobPublish;
import cec.jiutian.bc.job.provider.IJobProvider;
import cec.jiutian.core.prop.FabosJsonProp;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.quartz.Job;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.Objects;
import java.util.concurrent.TimeUnit;


@Slf4j
@Component
public class JobAction implements Job {

    public static final String JOB_KEY = "job:distributed-lock:";

    @Override
    public void execute(JobExecutionContext ctx) {
        JobDataMap jobDataMap = ctx.getJobDetail().getJobDataMap();
        JobInfo jobInfo = (JobInfo) jobDataMap.get(ctx.getJobDetail().getKey().getName());
        log.info("start execute job: " + jobInfo.getName() + ", " + jobInfo.getCron());
        trigger(jobInfo, (JavaMailSenderImpl) jobDataMap.get(JobService.MAIL_SENDER_KEY));
    }

    void trigger(JobInfo jobInfo, JavaMailSenderImpl javaMailSender) {
        if (FabosJsonSpringUtil.getBean(FabosJsonProp.class).isRedisSession()) {
            if (Boolean.FALSE.equals(FabosJsonSpringUtil.getBean(JobService.class).getStringRedisTemplate().opsForValue().setIfAbsent(JOB_KEY + jobInfo.getCode(), jobInfo.getCode(), 999, TimeUnit.MILLISECONDS))) {
                log.info("The {} task has been executed in other nodes", jobInfo.getName());
                return;
            }
        }
        JobLog jobInfoLog = new JobLog();
        String handler = jobInfo.getHandler();
        log.info("start execute job: " + jobInfo.getName() + ", handler: " + jobInfo.getHandler());
        if (StringUtils.isNotBlank(handler)) {
            jobInfoLog.setJobId(jobInfo.getId());
            jobInfoLog.setJobName(jobInfo.getName());
            jobInfoLog.setOid(jobInfo.getOid());
            jobInfoLog.setStartTime(LocalDateTime.now());
            try {
                String result = (String) FabosJsonSpringUtil.getBean(JobPublish.class).execute(jobInfo);
                jobInfoLog.setResultInfo(result);
                log.info("job result: " + result);
                jobInfoLog.setStatus(true);
            } catch (Exception e) {
                log.error(jobInfo.getName() + " job error", e);
                jobInfoLog.setStatus(false);
                String exceptionTraceStr = ExceptionUtils.getStackTrace(e);
                jobInfoLog.setErrorInfo(exceptionTraceStr);
                //失败通知
                if (StringUtils.isNotBlank(jobInfo.getNotifyEmails())) {
                    if (null == javaMailSender) {
                        log.warn("Sending mailbox not configured");
                    } else {
                        SimpleMailMessage message = new SimpleMailMessage();
                        message.setSubject(jobInfo.getName() + " job error ！！！");
                        message.setText(exceptionTraceStr);
                        message.setTo(jobInfo.getNotifyEmails().split("\\|"));
                        message.setFrom(Objects.requireNonNull(javaMailSender.getUsername()));
                        javaMailSender.send(message);
                    }
                }
            }
            jobInfoLog.setHandlerParam(jobInfo.getHandlerParam());
            jobInfoLog.setEndTime(LocalDateTime.now());
            if (null == jobInfo.getRecordLog() || jobInfo.getRecordLog()) {
                FabosJsonSpringUtil.getBean(JobService.class).saveJobLog(jobInfoLog);
            }
        }
    }
}
