package cec.jiutian.bc.infra.handler;

import cec.jiutian.core.frame.proxy.LambdaSee;
import cec.jiutian.core.frame.service.FabosJsonApplication;
import cec.jiutian.core.view.fabosJson.util.FabosJsonSpringUtil;
import cec.jiutian.meta.FabosJob;
import cec.jiutian.bc.job.domain.job.entity.JobInfo;
import cec.jiutian.bc.job.provider.IJobProvider;
import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.ChoiceTrigger;
import cec.jiutian.view.fun.VLModel;
import lombok.SneakyThrows;
import org.springframework.core.type.filter.AssignableTypeFilter;
import org.springframework.core.type.filter.TypeFilter;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class JobChoiceFetchHandler implements ChoiceFetchHandler, ChoiceTrigger {

    private static List<VLModel> loadedJobHandler;

    @Override
    public synchronized List<VLModel> fetch(String[] params) {
        if (null == loadedJobHandler) {
            loadedJobHandler = new ArrayList<>();
            FabosJsonSpringUtil.scannerPackage(FabosJsonApplication.getScanPackage(), new TypeFilter[]{new AssignableTypeFilter(IJobProvider.class)}, clazz -> {
                FabosJob fabosJob = clazz.getAnnotation(FabosJob.class);
                if (null == fabosJob) {
                    loadedJobHandler.add(new VLModel(clazz.getName(), ((IJobProvider) FabosJsonSpringUtil.getBean(clazz)).name(), clazz.getName()));
                } else {
                    loadedJobHandler.add(new VLModel(clazz.getName(), fabosJob.comment(), clazz.getName()));
                }
            });
        }
        return loadedJobHandler;
    }

    @Override
    @SneakyThrows
    public Map<String, Object> trigger(Object value, String[] params) {
        for (VLModel vl : loadedJobHandler) {
            if (vl.getValue().equals(value)) {
                Map<String, Object> map = new HashMap<>();
                IJobProvider jobHandler = FabosJsonSpringUtil.getBeanByPath(vl.getDesc(), IJobProvider.class);
                map.put(LambdaSee.field(JobInfo::getName), vl.getLabel());
                if (null != jobHandler.param()) {
                    map.put(LambdaSee.field(JobInfo::getHandlerParam), jobHandler.param());
                }
                if (null != jobHandler.cron()) {
                    map.put(LambdaSee.field(JobInfo::getCron), jobHandler.cron());
                }
                return map;
            }
        }
        return Collections.emptyMap();
    }
}
