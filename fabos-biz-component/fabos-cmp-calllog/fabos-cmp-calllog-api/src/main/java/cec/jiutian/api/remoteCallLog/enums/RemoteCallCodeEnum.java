package cec.jiutian.api.remoteCallLog.enums;


public enum RemoteCallCodeEnum {
    /**
     * 调用成功
     */
    SUCCESS("200", "调用成功"),

    FAILED("400", "调用失败"),

    UNAUTHORIZED("401", "未授权"),

    FORBIDDEN("403", "禁止访问"),

    NOT_FOUND("404", "未找到"),

    INTERNAL_SERVER_ERROR("500", "服务器内部错误"),

    /**
     * 未知状态
     */
    UNKNOWN("000", "未知状态");

    private final String code;
    private final String description;

    RemoteCallCodeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    @Override
    public String toString() {
        return String.format("Status Code: %s, Description: %s", code, description);
    }
}