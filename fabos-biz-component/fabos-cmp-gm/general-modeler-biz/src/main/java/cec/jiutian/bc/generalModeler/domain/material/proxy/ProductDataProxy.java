package cec.jiutian.bc.generalModeler.domain.material.proxy;

import cec.jiutian.bc.generalModeler.domain.material.model.Product;
import cec.jiutian.data.jpa.JpaCrud;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
public class ProductDataProxy implements DataProxy<Product> {

    @Resource
    private JpaCrud jpaCrud;


    @Override
    public void beforeAdd(Product product) {
        if (product.getProductCategory() != null) {
            product.setProductMainCategory(jpaCrud.getById(product.getProductCategory().getClass(), product.getProductCategory().getId()).getMainCategory());
        }
        DataProxy.super.beforeAdd(product);
    }

    @Override
    public void beforeUpdate(Product product) {
        if (product.getProductCategory() != null) {
            product.setProductMainCategory(product.getProductCategory().getMainCategory());
        }
        DataProxy.super.beforeUpdate(product);
    }
}
