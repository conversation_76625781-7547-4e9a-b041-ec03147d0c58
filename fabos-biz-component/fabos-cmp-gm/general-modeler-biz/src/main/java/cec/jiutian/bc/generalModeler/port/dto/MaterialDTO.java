package cec.jiutian.bc.generalModeler.port.dto;

import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.QueryModel;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.FabosJsonI18n;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import jakarta.persistence.Id;
import lombok.Data;

/**
 * <AUTHOR>
 * @description:
 */
@Data
@FabosJson(name = "允许存放的物料",
        //dataProxy = InventoryDTOProxy.class,
        orderBy = "Material.createTime desc",
        power = @Power(add = false, edit = false, delete = false)
//        , rowOperation = {@RowOperation(
//                code = "InventoryDTO@VIEW",
//                operationHandler = InventoryViewOperationHandler.class,
//                mode = RowOperation.Mode.SINGLE,
//                callHint = "点击会跳转到台账明细。该功能属于框架功能暂时未实现。暂时不测试",
//                title = "查看详情",
//                show = @ExprBool(
//                        exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
//                        params = "InventoryDTO@VIEW"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
//                )
//        )
//}
)
//SELECT
//	a.id,
//	a.MATERIAL_CODE,
//	b.WAREHOUSE_ID
//FROM
//	MATERIAL a
//INNER JOIN mos_warehouse_detail b on
//	a.MATERIAL_CATEGORY_ID = b.MATERIAL_CATEGORY_ID
@FabosJsonI18n
@QueryModel(hql = "select new map (" +
        "MaterialDTO.id as id, MaterialDTO.code as materialCode, MaterialDTO.name as materialName, MaterialDTO.specification as materialSpecification," +
        "wd.warehouseRelationId as warehouseRelationId, wd.name as materialCategory) " +
        "from StockView as MaterialDTO " +
        "join MaterialDTO.stockCategory as mc " +
        "join WarehouseDetail as wd on mc.id = wd.materialCategoryId")
public class MaterialDTO {
    @FabosJsonField(
            views = @View(title = "id", show = false),
            edit = @Edit(title = "id", show = false),
            customHqlField = "id"
    )
    @Id
    private String id;
    @FabosJsonField(
            views = @View(title = "物料名称"),
            edit = @Edit(title = "物料名称", search = @Search(vague = true)
            ),
            customHqlField = "MaterialDTO.name"

    )
    private String materialName;
    @FabosJsonField(
            views = @View(title = "物料编号"),
            edit = @Edit(title = "物料编号", search = @Search(vague = true)),
            customHqlField = "MaterialDTO.code"
    )
    private String materialCode;

    @FabosJsonField(
            views = @View(title = "物料规格"),
            edit = @Edit(title = "物料规格")
    )
    private String materialSpecification;

    @FabosJsonField(
            views = @View(title = "物料分类"),
            edit = @Edit(title = "物料分类")
    )
    private String materialCategory;

    @FabosJsonField(
            views = @View(title = "仓库Id", show = false),
            edit = @Edit(title = "仓库Id", show = false),
            customHqlField = "warehouseRelationId"
    )
    private String warehouseRelationId;


}
