package cec.jiutian.bc.generalModeler.domain.test.model;


import cec.jiutian.data.jpa.JpaCrud;
import cec.jiutian.view.ReferenceAddType;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Component
public class SplitModelp3ReferAddHandler implements ReferenceAddType.ReferenceAddHandler<SplitModelFull, SplitModelDto> {
    @Resource
    private JpaCrud jpaCrud;

    @Override
    public Map<String, Object> handle(SplitModelFull splitModelFull, List<SplitModelDto> splitModelDtoList) {
        Map<String, Object> result = new HashMap<>();
        List<SplitModelP3> splitModelP3List = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(splitModelDtoList)) {
            splitModelDtoList.forEach(c -> {
                SplitModelP3 splitModelP3 = new SplitModelP3();
                splitModelP3.setExtraId(c.getId());
                splitModelP3.setUserName(c.getUserName());
                splitModelP3.setRoleName(c.getRoleName());
                splitModelP3.setState(c.getState());
                splitModelP3List.add(splitModelP3);
            });
            result.put("splitModelP3List", splitModelP3List);
        }
        return result;
    }


}

