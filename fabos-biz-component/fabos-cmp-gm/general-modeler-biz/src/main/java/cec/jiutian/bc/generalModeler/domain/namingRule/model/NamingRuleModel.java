package cec.jiutian.bc.generalModeler.domain.namingRule.model;

import cec.jiutian.bc.flow.domain.model.entity.ExamineModel;
import cec.jiutian.bc.generalModeler.handler.NamingRuleGenerateHandler;
import cec.jiutian.core.frame.module.MetaDataProxy;
import cec.jiutian.core.view.fabosJson.PreDataProxy;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.config.Comment;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.Search;
import jakarta.persistence.Column;
import jakarta.persistence.MappedSuperclass;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

@Getter
@Setter
@MappedSuperclass
@PreDataProxy(MetaDataProxy.class)
public class NamingRuleModel extends ExamineModel {

    /**
     *
     */
    public String getNamingCode() {
        throw new RuntimeException("错误调用！");
    }

    public Map<String, String> getParameters() {
        return null;
    }

    @Comment("业务单据编码编号")
    @Column(unique = true)
    @FabosJsonField(
            views = @View(title = "单号", index = -9),
            edit = @Edit(title = "单号", readonly = @Readonly(add = false, edit = true),
                    notNull = true, search = @Search(vague = true), inputType = @InputType(length = 40))
            ,
            dynamicField = @DynamicField(
                    dependFiled = @DependFiled(buttonName = "生成", changeBy = {"id"},
                            dynamicHandler = NamingRuleGenerateHandler.class)
            )
    )
    private String generalCode;

}