package cec.jiutian.bc.generalModeler.domain.supplier.model;

import cec.jiutian.bc.generalModeler.domain.supplier.handler.SupplierInValidateOperationHandler;
import cec.jiutian.bc.generalModeler.domain.supplier.handler.SupplierValidateOperationHandler;
import cec.jiutian.bc.generalModeler.domain.supplier.proxy.SupplierDataProxy;
import cec.jiutian.bc.urm.domain.dictionary.handler.DictChoiceFetchHandler;
import cec.jiutian.core.frame.constant.RegexConst;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DateType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.RowOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date ：2024/10/31 16:12
 * @description：
 */
@Table(name = "supplier")
@FabosJson(
        name = "供应商管理",
        dataProxy = SupplierDataProxy.class,
        rowOperation = {
                @RowOperation(
                        title = "生效",
                        code = "Supplier@EFFECTIVE",
                        operationHandler = SupplierValidateOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "是否确定操作？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "Supplier@EFFECTIVE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "status == '1'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE
                ),
                @RowOperation(
                        title = "失效",
                        code = "Supplier@INVALID",
                        operationHandler = SupplierInValidateOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "是否确定操作？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "Supplier@INVALID"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "status == '2'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE
                )
        }
)
@Entity
@Getter
@Setter
public class Supplier extends MetaModel {

    @FabosJsonField(
            views = @View(title = "供应商编码"),
            edit = @Edit(title = "供应商编码",
                    notNull = true, search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    private String supplierCode;

    @FabosJsonField(
            views = @View(title = "供应商外部代号"),
            edit = @Edit(title = "供应商外部代号", notNull = true, search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    private String supplierExternalCode;

    @FabosJsonField(
            views = @View(title = "供应商全称"),
            edit = @Edit(title = "供应商全称", notNull = true, search = @Search(vague = true),
                    inputType = @InputType(length = 20))
    )
    private String supplierName;

    @FabosJsonField(
            views = @View(title = "统一社会信用代码"),
            edit = @Edit(title = "统一社会信用代码", notNull = true, search = @Search(vague = true),
                    inputType = @InputType(length = 60))
    )
    private String socialCreditCode;

    @FabosJsonField(
            views = @View(title = "经济类型代码"),
            edit = @Edit(title = "经济类型代码",
                    inputType = @InputType(length = 60))
    )
    private String economicTypeCode;

    @FabosJsonField(
            views = @View(title = "注册国家/地区"),
            edit = @Edit(title = "注册国家/地区",
                    inputType = @InputType(length = 60))
    )
    private String registeredRegion;

    @FabosJsonField(
            views = @View(title = "法人姓名"),
            edit = @Edit(title = "法人姓名",
                    inputType = @InputType(length = 60))
    )
    private String legalPersonName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "成立日期"),
            edit = @Edit(title = "成立日期", notNull = true, type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE, pickerMode = DateType.PickerMode.ALL))
    )
    private Date establishDate;

    @FabosJsonField(
            views = @View(title = "单位状态"),
            edit = @Edit(title = "单位状态", show = false, type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = DictChoiceFetchHandler.class,
                            fetchHandlerParams = "WLDW028"))
    )
    private String status;

    @FabosJsonField(
            views = @View(title = "注册地址"),
            edit = @Edit(title = "注册地址",
                    inputType = @InputType(length = 60))
    )
    private String registeredAddress;

    @FabosJsonField(
            views = @View(title = "注册地区代码"),
            edit = @Edit(title = "注册地区代码",
                    inputType = @InputType(length = 60))
    )
    private String registrationAreaCode;

    @FabosJsonField(
            views = @View(title = "注册地区邮编"),
            edit = @Edit(title = "注册地区邮编",
                    inputType = @InputType(length = 60))
    )
    private String registrationAreaPostalCode;

    @FabosJsonField(
            views = @View(title = "单位网址"),
            edit = @Edit(title = "单位网址",
                    inputType = @InputType(length = 60))
    )
    private String website;

    @FabosJsonField(
            views = @View(title = "联系人姓名"),
            edit = @Edit(title = "联系人姓名",
                    inputType = @InputType(length = 60))
    )
    private String contactName;

    @FabosJsonField(
            views = @View(title = "联系电话"),
            edit = @Edit(title = "联系电话", notNull = true,
                    inputType = @InputType(length = 20, regex = "^1[0-9]\\d{9}$"))
    )
    private String phoneNumber;

    @FabosJsonField(
            views = @View(title = "电子邮箱"),
            edit = @Edit(title = "电子邮箱",
                    inputType = @InputType(regex = RegexConst.EMAIL_REGEX))
    )
    private String email;

    @FabosJsonField(
            views = @View(title = "开户银行名称"),
            edit = @Edit(title = "开户银行名称",
                    inputType = @InputType(length = 60))
    )
    private String bankName;

    @FabosJsonField(
            views = @View(title = "开户银行账号"),
            edit = @Edit(title = "开户银行账号",
                    inputType = @InputType(length = 60))
    )
    private String bankAccount;

    @FabosJsonField(
            views = @View(title = "所在行业"),
            edit = @Edit(title = "所在行业",
                    inputType = @InputType(length = 60))
    )
    private String industry;

    @FabosJsonField(
            views = @View(title = "注册资金（单位：元）"),
            edit = @Edit(title = "注册资金（单位：元）",
                    numberType = @NumberType(min = 0, max = *********, precision = 2))
    )
    private Double registeredCapital;

    @FabosJsonField(
            views = @View(title = "实缴资本（单位：元）"),
            edit = @Edit(title = "实缴资本（单位：元）",
                    numberType = @NumberType(min = 0, max = *********, precision = 2))
    )
    private Double actualCapital;

    @FabosJsonField(
            views = @View(title = "经营范围"),
            edit = @Edit(title = "经营范围",
                    inputType = @InputType(length = 1000))
    )
    private String businessScope;

    @FabosJsonField(
            views = @View(title = "单位性质"),
            edit = @Edit(title = "单位性质",
                    inputType = @InputType(length = 60))
    )
    private String unitNature;

    @FabosJsonField(
            views = @View(title = "单位考核评级"),
            edit = @Edit(title = "单位考核评级",
                    inputType = @InputType(length = 60))
    )
    private String unitAssessmentRate;

    @FabosJsonField(
            views = @View(title = "单位类型"),
            edit = @Edit(title = "单位类型",
                    inputType = @InputType(length = 60))
    )
    private String unitType;

    @FabosJsonField(
            views = @View(title = "单位规模"),
            edit = @Edit(title = "单位规模",
                    inputType = @InputType(length = 60))
    )
    private String unitScale;

    @FabosJsonField(
            views = @View(title = "单位级别"),
            edit = @Edit(title = "单位级别",
                    inputType = @InputType(length = 60))
    )
    private String unitLevel;

    @FabosJsonField(
            views = @View(title = "描述"),
            edit = @Edit(title = "描述",
                    inputType = @InputType(length = 200))
    )
    private String description;
}
