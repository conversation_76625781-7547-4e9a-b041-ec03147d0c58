app:
  id: xlx-ehs
nacos:
  addr: 172.16.200.101:8848
spring:
  jpa:
    generate-ddl: true
    hibernate:
      ddl-auto: update
  main:
    allow-bean-definition-overriding: true
  profiles:
    active: dev
  application:
    name: ${app.id}
  cloud:
    nacos:
      server-addr: ${nacos.addr}
      username: xlx
      password: xlx0228
      discovery:
        enabled: true
        namespace: fabos-xlx
        group: ${spring.profiles.active}
      config:
        shared-configs:
          - data-id: common
            group: ${spring.profiles.active}
            refresh: true
        enabled: true
        namespace: fabos-xlx
        group: ${spring.profiles.active}
        prefix: ${app.id}

logging:
  level:
    com:
      alibaba:
        cloud: debug