package cec.jiutian.bc.inspection.domain.task.handler;

import cec.jiutian.bc.inspection.domain.task.model.EhsInsTask;
import cec.jiutian.bc.inspection.domain.task.model.EhsInsTaskSkip;
import cec.jiutian.bc.inspection.enumeration.EhsInsTaskStateEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class EhsInsTaskSkipHandler implements OperationHandler<EhsInsTask, EhsInsTaskSkip> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<EhsInsTask> data, EhsInsTaskSkip modelObject, String[] param) {
        EhsInsTask ehsInsTaskDb = fabosJsonDao.getById(EhsInsTask.class, modelObject.getId());
        ehsInsTaskDb.setIsSkip("Y");
        ehsInsTaskDb.setSkipReason(modelObject.getSkipReason());
        // 设置单据为已关闭
        ehsInsTaskDb.setCurrentState(EhsInsTaskStateEnum.Enum.CLOSED.name());
        // 设置提交状态为2
        ehsInsTaskDb.setIsSubmit("2");
        fabosJsonDao.update(ehsInsTaskDb);
        return null;
    }

    @Override
    public EhsInsTaskSkip fabosJsonFormValue(List<EhsInsTask> data, EhsInsTaskSkip fabosJsonForm, String[] param) {
        BeanUtils.copyProperties(data.get(0), fabosJsonForm);
        return fabosJsonForm;
    }
}
