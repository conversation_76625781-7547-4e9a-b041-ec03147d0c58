package cec.jiutian.bc.inspection.domain.plan.handler;

import cec.jiutian.bc.inspection.domain.plan.model.EhsInsPlan;
import cec.jiutian.bc.inspection.domain.plan.model.EhsInsPlanDetail;
import cec.jiutian.bc.inspection.domain.scheme.model.EhsInsScheme;
import cec.jiutian.bc.inspection.domain.scheme.model.EhsInsSchemeDetail;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：2024/11/14 18:13
 * @description：
 */
@Component
public class EhsInsPlanDetailDynamicHandler implements DependFiled.DynamicHandler<EhsInsPlan> {

    @Resource
    private FabosJsonDao fabosJsonDao;
    @Override
    public Map<String, Object> handle(EhsInsPlan plan) {
        Map<String, Object> result = new HashMap<>();
        List<EhsInsPlanDetail> ehsInsPlanDetails = new ArrayList<>();
        EhsInsScheme ehsInsScheme = fabosJsonDao.findById(EhsInsScheme.class, plan.getEhsInsScheme().getId());
        if (ehsInsScheme!=null&&CollectionUtils.isNotEmpty(ehsInsScheme.getEhsInsSchemeDetails())) {
            for (EhsInsSchemeDetail schemeDetail : ehsInsScheme.getEhsInsSchemeDetails()) {
                EhsInsPlanDetail detail = new EhsInsPlanDetail();
                detail.setEhsInsItemId(schemeDetail.getEhsInsItemId());
                detail.setName(schemeDetail.getName());
                detail.setContent(schemeDetail.getContent());
                detail.setType(schemeDetail.getType());
                detail.setResultRecord(schemeDetail.getResultRecord());
                detail.setAbnormalValue(schemeDetail.getAbnormalValue());
                detail.setNormalValue(schemeDetail.getNormalValue());
                detail.setUpperValue(schemeDetail.getUpperValue());
                detail.setLowerValue(schemeDetail.getLowerValue());
                detail.setStandardValue(schemeDetail.getStandardValue());
                detail.setIsTakePhoto(schemeDetail.getIsTakePhoto());
                ehsInsPlanDetails.add(detail);
            }
        }
        result.put("ehsInsPlanDetails", ehsInsPlanDetails);
        return result;
    }
}
