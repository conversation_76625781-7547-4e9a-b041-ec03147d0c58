package cec.jiutian.bc.inspection.remote.controller;

import cec.jiutian.bc.inspection.domain.plan.service.EhsInsPlanService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description:
 */
@RequestMapping("/device/ins/plan")
@RestController
public class EhsInsPlanController {
    @Resource
    private EhsInsPlanService deviceInsPlanService;


    @GetMapping("/generate")
    public void generateStocktakingOrder(){
        deviceInsPlanService.generateDeviceInsTask();
    }
}
