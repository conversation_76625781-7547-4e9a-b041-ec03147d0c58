package cec.jiutian.bc.inspection.domain.task.proxy;

import cec.jiutian.bc.inspection.domain.task.model.MyEhsInsTask;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.view.fun.DataProxy;
import cec.jiutian.view.query.Condition;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class MyEhsInsTaskProxy implements DataProxy<MyEhsInsTask> {
    @Value("${system.manage.super-account}")
    private String superUserAccount;

    /**
     * 查询前过滤：只查询出 我领取的、分配给我的任务
     * @param conditions
     * @return
     */
    @Override
    public String beforeFetch(List<Condition> conditions) {
        if (UserContext.getAccount().equals(superUserAccount)) {
            return "";
        }
        String queryHql = "MyEhsInsTask.insPerson.id = \'" + UserContext.getUserId() + "\'";
        return queryHql;
    }
}
