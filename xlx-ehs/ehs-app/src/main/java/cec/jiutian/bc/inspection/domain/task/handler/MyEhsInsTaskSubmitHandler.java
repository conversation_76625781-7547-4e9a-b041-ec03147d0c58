package cec.jiutian.bc.inspection.domain.task.handler;

import cec.jiutian.bc.inspection.domain.task.model.EhsInsTaskDetail;
import cec.jiutian.bc.inspection.domain.task.model.MyEhsInsTask;
import cec.jiutian.bc.inspection.enumeration.EhsInsIsAbnormalEnum;
import cec.jiutian.bc.inspection.enumeration.EhsInsTaskStateEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Component
public class MyEhsInsTaskSubmitHandler implements OperationHandler<MyEhsInsTask, Void> {
    @Resource
    private FabosJsonDao fabosJsonDao;
    // 隐患
    private static final String DANGER = EhsInsIsAbnormalEnum.Enum.DANGER.name();

    @Transactional
    @Override
    public String exec(List<MyEhsInsTask> data, Void modelObject, String[] param) {
        // 提交后修改状态为已完成
        MyEhsInsTask myEhsInsTask = data.get(0);
        // 设置提交状态为2 已经提交, 不可再提交
        myEhsInsTask.setIsSubmit("2");
        myEhsInsTask.setCurrentState(EhsInsTaskStateEnum.Enum.COMPLETE.name());
        // 设置完成时间
        myEhsInsTask.setFinishTime(new Date());
        fabosJsonDao.update(myEhsInsTask);
        return null;
    }
}
