package cec.jiutian.bc.inspection.domain.question.handler;

import cec.jiutian.bc.inspection.domain.question.model.EhsQuestion;
import cec.jiutian.bc.inspection.domain.question.model.EhsQuestionCorrection;
import cec.jiutian.bc.inspection.enumeration.EhsQuestionCurrentStatusEnum;
import cec.jiutian.bc.inspection.enumeration.ProgressEnum;
import cec.jiutian.bc.inspection.enumeration.VerifyResultEnum;
import cec.jiutian.common.util.StringUtils;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/28
 * @description
 */
@Component
public class EhsInsQuestionVERIFYOperationHandler implements OperationHandler<EhsQuestion,Void> {
    @Resource
    private FabosJsonDao fabosJsonDao;
    @Override
    @Transactional
    public String exec(List<EhsQuestion> data, Void modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            EhsQuestion question = data.get(0);
            //判断明细是否都已经验证  如果存在未验证的  不允许提交
            question.getEhsQuestionCorrections().forEach(d->{
                if(StringUtils.isBlank(d.getVerifyResult())){
                    throw new FabosJsonApiErrorTip("提交失败：整改措施全部验证完毕才能提交");
                }
            });
            //如果全部验证通过  问题清单改为完成    如果存在一个未通过  状态改为 执行中
            String currentStatus = EhsQuestionCurrentStatusEnum.Enum.FINISH.name();
            for (EhsQuestionCorrection d: question.getEhsQuestionCorrections()){
                if (VerifyResultEnum.Enum.FAILED.name().equals(d.getVerifyResult())) {
                    currentStatus = EhsQuestionCurrentStatusEnum.Enum.RUNNING.name();
                    break;
                }
            }
            question.setCurrentStatus(currentStatus);
            //如果主单据是执行中 则修改整改措施为 已执行 只需要在我们整改任务里面 重新提交数据
            //这里如果改为 执行中  整改措施完成相关数据和验证相关的数据  都不要清除  直接回显 @禹修
            if(currentStatus.equals(EhsQuestionCurrentStatusEnum.Enum.RUNNING.name())){
                question.getEhsQuestionCorrections().forEach(d->{
                    d.setProgress(ProgressEnum.Enum.RUN_SUBMIT.name());
                });
            }
            if(currentStatus.equals(EhsQuestionCurrentStatusEnum.Enum.FINISH.name())){
                question.getEhsQuestionCorrections().forEach(d->{
                    d.setProgress(ProgressEnum.Enum.FINISH.name());
                });
            }
            fabosJsonDao.mergeAndFlush(question);
        }
        return "msg.success('操作成功')";
    }

}
