package cec.jiutian.bc.inspection.domain.question.handler;

import cec.jiutian.bc.inspection.domain.question.model.EhsMyQuestion;
import cec.jiutian.bc.inspection.domain.question.model.EhsQuestion;
import cec.jiutian.bc.inspection.domain.question.model.EhsQuestionCorrection;
import cec.jiutian.bc.inspection.enumeration.EhsQuestionCurrentStatusEnum;
import cec.jiutian.bc.inspection.enumeration.ProgressEnum;
import cec.jiutian.bc.inspection.enumeration.ProgressRUNEnum;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/28
 * @description
 */
@Component
public class EhsMyQuestionSubmitOperationHandler implements OperationHandler<EhsMyQuestion,Void> {
    @Resource
    private FabosJsonDao fabosJsonDao;
    @Override
    @Transactional
    public String exec(List<EhsMyQuestion> data, Void modelObject, String[] param) {
        //这里有个问题  data传过来的参数  子表会包括所有的数据。但是在我的整改任务中   子表一般只处理 子表责任人是当前登录人的数据。因此需要通过过滤来处理
        //子表过滤过后的主表模型不能用于更新  更新过后会把过滤的数据从数据库层面去掉
        if (CollectionUtils.isNotEmpty(data)) {
            EhsMyQuestion question = data.get(0);
            List<EhsQuestionCorrection> updateDetailList = new ArrayList<>();
            for(EhsQuestionCorrection d: question.getEhsQuestionCorrections()){
                if(!d.getMetaUser().getId().equals(UserContext.getUserId())){
                    //不是自己的任务不需要参与校验
                    continue;
                }
                if(!ProgressRUNEnum.Enum.RUN_SUBMIT.name().equals(d.getProgress())){
                    throw new FabosJsonApiErrorTip("提交失败：需要完成所有整改任务才可提交");
                }
                //保存自己的整改任务  如果校验通过需要更新这部分数据的状态
                updateDetailList.add(d);
            }
            updateDetailList.forEach(d->{
                d.setProgress(ProgressEnum.Enum.VERIFIED.name());
                fabosJsonDao.mergeAndFlush(d);
            });
            //需要判断  如果所有的明细都是待验证  那么整个单据需要调整状态为：待验证
            EhsQuestion ehsQuestion = fabosJsonDao.findById(EhsQuestion.class, question.getId());
            if(checkCurrentStatus(ehsQuestion)){
                ehsQuestion.setCurrentStatus(EhsQuestionCurrentStatusEnum.Enum.VERIFIED.name());
                fabosJsonDao.mergeAndFlush(ehsQuestion);
            }
        }
        return "msg.success('操作成功')";
    }

    /**
     * 明细的状态如果全部为 待验证  返回true  否则返回false
     * @param question
     * @return
     */
    private boolean checkCurrentStatus(EhsQuestion question) {
        for (EhsQuestionCorrection d : question.getEhsQuestionCorrections()){
            if(!ProgressEnum.Enum.VERIFIED.name().equals(d.getProgress())){
                return false;
            }
        }
        return true;
    }

}
