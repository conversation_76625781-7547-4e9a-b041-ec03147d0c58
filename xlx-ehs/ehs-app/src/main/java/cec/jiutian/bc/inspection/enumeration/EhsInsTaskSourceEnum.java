package cec.jiutian.bc.inspection.enumeration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 巡检任务-任务来源枚举
 * <AUTHOR>
 * @date 2025/4/7 17:19
 */
public class EhsInsTaskSourceEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum value : Enum.values()) {
            list.add(new VLModel(value.name(), value.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        EHS_INS_PLAN("计划任务"),
        SELF_BUILD("临时任务");

        private final String value;

    }
}
