package cec.jiutian.bc.inspection.domain.question.model;

import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleBaseModel;
import cec.jiutian.bc.inspection.domain.question.dto.EhsInsTaskDTO;
import cec.jiutian.bc.inspection.domain.question.proxy.EhsInsQuestionADDDataProxy;
import cec.jiutian.bc.inspection.enumeration.EhsNamingRuleCodeEnum;
import cec.jiutian.bc.inspection.enumeration.EhsQuestionCurrentStatusEnum;
import cec.jiutian.bc.inspection.enumeration.HiddenLevelEnum;
import cec.jiutian.bc.urm.domain.dictionary.handler.DictChoiceFetchHandler;
import cec.jiutian.bc.urm.domain.org.entity.Org;
import cec.jiutian.core.frame.module.MetaUser;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.FabosJsonI18n;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.AttachmentType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @description:
 */
@Entity
@Table(name = "ehs_ins_question")
@Getter
@Setter
@FabosJson(name = "问题清单",power = @Power(add = false,delete = false),
        orderBy = "EhsQuestionADD.createTime desc",
        dataProxy = EhsInsQuestionADDDataProxy.class
)
@FabosJsonI18n
public class EhsQuestionADD extends NamingRuleBaseModel {
    @Override
    public String getNamingCode() {
        return EhsNamingRuleCodeEnum.Enum.EHS_INS_QUESTION.name();
    }

    @Transient
    @FabosJsonField(
            views = @View(title = "巡检任务", show = false, column = "generalCode"),
            edit = @Edit(title = "巡检任务",
                    type = EditType.REFERENCE_TABLE,notNull = true,readonly = @Readonly(add = false),
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode")
            )
    )
    private EhsInsTaskDTO ehsInsTaskDTO;
    // 任务名称
    @FabosJsonField(
            views = @View(title = "任务名称"),
            edit = @Edit(title = "任务名称", search = @Search(vague = true),readonly = @Readonly,notNull = true),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "ehsInsTaskDTO", beFilledBy = "name"))

    )
    private String name;
    @FabosJsonField(
            views = @View(title = "任务编号"),
            edit = @Edit(title = "任务编号", show = false,readonly = @Readonly,notNull = true),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "ehsInsTaskDTO", beFilledBy = "generalCode"))
    )
    private String taskGeneralCode;

    @FabosJsonField(
            views = @View(title = "巡检任务明细ID",show = false),
            edit = @Edit(title = "巡检任务明细ID",show = false))
    private String taskDetailId;

    // 任务描述
    @FabosJsonField(
            views = @View(title = "任务描述", toolTip = true),
            edit = @Edit(title = "任务描述",readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "ehsInsTaskDTO", beFilledBy = "description"))

    )
    private String description;

    @FabosJsonField(
            views = @View(title = "巡检方案"),
            edit = @Edit(title = "巡检方案", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "ehsInsTaskDTO", beFilledBy = "ehsInsSchemeName"))

    )
    private String ehsInsSchemeName;
    @FabosJsonField(
            views = @View(title = "巡检项目"),
            edit = @Edit(title = "巡检项目",readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "ehsInsTaskDTO", beFilledBy = "ehsInsItemName"))

    )
    private String ehsInsItemName;

    @FabosJsonField(
            views = @View(title = "巡检内容", toolTip = true),
            edit = @Edit(title = "巡检内容", search = @Search(vague = true), readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "ehsInsTaskDTO", beFilledBy = "content"))

    )
    private String content;

    @FabosJsonField(
            views = @View(title = "巡检类型"),
            edit = @Edit(title = "巡检类型", type = EditType.CHOICE, readonly = @Readonly,
                    choiceType = @ChoiceType(fetchHandler = DictChoiceFetchHandler.class,
                            fetchHandlerParams = "EHS_INS_ITEM_TYPE"), search = @Search(vague = true)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "ehsInsTaskDTO", beFilledBy = "type"))

    )
    private String type;

    @FabosJsonField(
            views = @View(title = "车间名称"),
            edit = @Edit(title = "车间名称",
                    readonly = @Readonly
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "ehsInsTaskDTO", beFilledBy = "factoryAreaName"))

    )
    private String factoryAreaName;


    @FabosJsonField(
            views = @View(title = "产线名称"),
            edit = @Edit(title = "产线名称",
                    readonly = @Readonly
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "ehsInsTaskDTO", beFilledBy = "factoryLineName"))

    )
    private String factoryLineName;


    @FabosJsonField(
            views = @View(title = "工序名称"),
            edit = @Edit(title = "工序名称",
                    readonly = @Readonly
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "ehsInsTaskDTO", beFilledBy = "processName"))

    )
    private String processName;

    @FabosJsonField(
            views = @View(title = "巡检结果", toolTip = true),
            edit = @Edit(title = "巡检结果",readonly = @Readonly,type = EditType.TEXTAREA),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "ehsInsTaskDTO", beFilledBy = "insResult"))

    )
    private String insResult;

    //隐患内容
    @FabosJsonField(
            views = @View(title = "隐患内容"),
            edit = @Edit(title = "隐患内容",notNull = true)
    )
    private String contentHidden;
    //严重程度  轻微 一般 严重
    @FabosJsonField(
            views = @View(title = "严重程度"),
            edit = @Edit(title = "严重程度",type = EditType.CHOICE,notNull = true,
                    choiceType = @ChoiceType(fetchHandler = HiddenLevelEnum.class))
    )
    private String hiddenLevel;
    @ManyToOne
    @FabosJsonField(
            views = @View(title = "责任部门", column = "name"),
            edit = @Edit(title = "责任部门",
                    type = EditType.REFERENCE_TABLE,notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"))
    )
    private Org org;
    @ManyToOne
    @FabosJsonField(
            views = @View(title = "责任人", column = "name"),
            edit = @Edit(title = "责任人",notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"))
    )
    private MetaUser metaUser;
//    //隐患责任部门
//    @FabosJsonField(
//            views = @View(title = "隐患责任部门"),
//            edit = @Edit(title = "隐患责任部门")
//    )
//    private String hiddenDepartment;
//    @FabosJsonField(
//            views = @View(title = "隐患责任部门",show = false),
//            edit = @Edit(title = "隐患责任部门",show = false)
//    )
//    private String hiddenDepartmentId;
//    //责任人
//    @FabosJsonField(
//            views = @View(title = "责任人"),
//            edit = @Edit(title = "责任人")
//    )
//    private String responsiblePerson;
//    @FabosJsonField(
//            views = @View(title = "责任人",show = false),
//            edit = @Edit(title = "责任人",show = false)
//    )
//    private String responsiblePersonId;
    //措施纳期
    @FabosJsonField(
            views = @View(title = "措施纳期"),
            edit = @Edit(title = "措施纳期",notNull = true)
    )
    private Date measureDate;
    //问题重复次数
    @FabosJsonField(
            views = @View(title = "问题重复次数"),
            edit = @Edit(title = "问题重复次数",notNull = true)
    )
    private Integer repeatTimes;
    //问题附件
    @FabosJsonField(
            views = @View(title = "问题附件"),
            edit = @Edit(title = "问题附件",
                    type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(size = 5120, maxLimit = 10, type = AttachmentType.Type.BASE)
            )
    )
    private String attachment;

    //状态
    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",type = EditType.CHOICE,show = false,
                    choiceType = @ChoiceType(fetchHandler = EhsQuestionCurrentStatusEnum.class))
    )
    private String currentStatus;


}
